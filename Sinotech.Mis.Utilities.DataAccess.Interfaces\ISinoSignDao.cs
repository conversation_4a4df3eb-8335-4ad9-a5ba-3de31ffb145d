﻿using System.Data;

namespace Sinotech.Mis.Utilities.DataAccess.Interfaces
{
    /// <summary>
    /// SinoDAMS資料存取介面
    /// </summary>
    public interface ISinoSignDao
    {
        /// <summary>
        /// 取得員工角色
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>角色 DataTable</returns>
        public DataTable GetUserRoles(string empNo);

        /// <summary>
        /// 取得角色的所有員工
        /// </summary>
        /// <param name="role">角色</param>
        /// <returns>員工編號 DataTable</returns>
        public DataTable GetRoleUsers(string role);

        /// <summary>
        /// 取得角色的預設員工
        /// </summary>
        /// <param name="role">角色</param>
        /// <returns>員工編號 DataTable</returns>
        public DataTable GetRoleDefaultUser(string role);

        /// <summary>
        /// 取得代理人
        /// </summary>
        /// <param name="empNo"></param>
        /// <returns></returns>
        public DataTable GetDeputies(string empNo);

        /// <summary>
        /// 取得角色名稱資料表
        /// </summary>
        /// <returns></returns>
        public DataTable GetRoleGroupNames();

        /// <summary>
        /// 取得部門收發員工編號資料表
        /// </summary>
        /// <param name="deptNo">部門編號</param>
        /// <returns>部門收發員工編號 DataTable</returns>
        public DataTable GetDepartmentMailroom(int deptNo);
    }
}
