﻿using Sinotech.Mis.HR.Attendance.Common;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves
{
    /// <summary>
    /// 國外假
    /// </summary>
    /// <seealso cref="Leaves.LeaveBase" />
    [LeaveKind(LeaveKindEnum.ForeignLeave)]
    public class ForeignLeave : C1CardLeaveNotProvided
    {

        /// <summary>
        /// 建構子
        /// </summary>
        /// <param name="c1Card"></param>
        /// <param name="c1CardBo"></param>
        public ForeignLeave(C1Card c1Card, IC1CardBo c1CardBo) : base(c1Card, c1CardBo)
        {
        }
    }

}
