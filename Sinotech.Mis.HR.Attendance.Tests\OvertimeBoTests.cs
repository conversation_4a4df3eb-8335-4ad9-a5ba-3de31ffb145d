﻿using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.Tests
{
    [ExcludeFromCodeCoverage]
    public class OvertimeBoTests
    {

        private readonly OvertimeBo _overtimeBo;

        public OvertimeBoTests(OvertimeBo overtimeBo)
        {
            _overtimeBo = overtimeBo;
        }



        [Fact]
        public void CalculateDayOvertimeRateTest()
        {
            DayOvertime dayOvertime = new DayOvertime();
            dayOvertime.OvertimeDate = new DateTime(2022, 10, 10);

            OvertimeRecord overtimeRecordReserve2 = new OvertimeRecord();
            overtimeRecordReserve2.OvertimeType = OvertimeType.ReservedLeave;
            overtimeRecordReserve2.Hours = 1;
            overtimeRecordReserve2.StartTime = new DateTime(2022, 10, 10, 17, 15, 0);
            overtimeRecordReserve2.EndTime = new DateTime(2022, 10, 10, 19, 20, 0);
            overtimeRecordReserve2.ProjectNumber = "RP19553";

            OvertimeRecord overtimeRecordOvertime3 = new OvertimeRecord();
            overtimeRecordOvertime3.OvertimeType = OvertimeType.Overtime;
            overtimeRecordOvertime3.Hours = 3;
            overtimeRecordOvertime3.StartTime = new DateTime(2022, 10, 10, 9, 30, 0);
            overtimeRecordOvertime3.EndTime = new DateTime(2022, 10, 10, 12, 30, 0);
            overtimeRecordOvertime3.ProjectNumber = "RP19553";
            dayOvertime.OvertimeRecords.Add(overtimeRecordOvertime3);
            dayOvertime = _overtimeBo.CalculateDayOvertimeRate(dayOvertime);
            Assert.Equal(3, dayOvertime.TotalHours);
            Assert.Equal(0, dayOvertime.InOvertimeHours);
            Assert.Equal(3.0, dayOvertime.PaidHours);

            dayOvertime.OvertimeDate = new DateTime(2022, 10, 11);
            dayOvertime = _overtimeBo.CalculateDayOvertimeRate(dayOvertime);
            Assert.Equal(3, dayOvertime.TotalHours);
            Assert.Equal(4.333333333333333, dayOvertime.PaidHours);
            dayOvertime.OvertimeDate = new DateTime(2022, 10, 10);

            OvertimeRecord overtimeRecordOutside3 = new OvertimeRecord();
            overtimeRecordOutside3.OvertimeType = OvertimeType.OutsideOvertime;
            overtimeRecordOutside3.Hours = 2; //故意寫錯，看會不會更正
            overtimeRecordOutside3.ProjectNumber = "RP19553";
            overtimeRecordOutside3.StartTime = new DateTime(2022, 10, 10, 14, 0, 0);
            overtimeRecordOutside3.EndTime = new DateTime(2022, 10, 10, 17, 0, 0);

            dayOvertime = new DayOvertime();

            dayOvertime.OvertimeDate = new DateTime(2022, 10, 10);
            dayOvertime.OvertimeRecords.Add(overtimeRecordOvertime3);
            _overtimeBo.CalculateDayOvertimeRate(dayOvertime);

            Assert.Equal(3, dayOvertime.TotalHours);
            Assert.Equal(0, dayOvertime.InOvertimeHours);
            Assert.Equal(3.0, dayOvertime.PaidHours);
            dayOvertime.OvertimeRecords.Add(overtimeRecordOutside3);
            _overtimeBo.CalculateDayOvertimeRate(dayOvertime);
            Assert.Equal(6, dayOvertime.TotalHours);

            Assert.Equal(6.0, dayOvertime.PaidHours);
            dayOvertime.OvertimeRecords = new List<OvertimeRecord>();
            dayOvertime.OvertimeRecords.Add(overtimeRecordReserve2);
            _overtimeBo.CalculateDayOvertimeRate(dayOvertime);
            Assert.Equal(2, dayOvertime.TotalHours);
            Assert.Equal(0, dayOvertime.InOvertimeHours);
            Assert.Equal(2.0, dayOvertime.PaidHours);

            dayOvertime.OvertimeDate = new DateTime(2022, 11, 19); //星期六
            dayOvertime = _overtimeBo.CalculateDayOvertimeRate(dayOvertime);
            Assert.Equal(2, dayOvertime.TotalHours);
            Assert.Equal(2, dayOvertime.InOvertimeHours);
            Assert.Equal(2.6666666666666665, dayOvertime.PaidHours);
            overtimeRecordOutside3.StartTime = new DateTime(2022, 11, 19, 14, 0, 0);
            overtimeRecordOutside3.EndTime = new DateTime(2022, 11, 19, 17, 0, 0);
            dayOvertime.OvertimeRecords.Add(overtimeRecordOutside3);
            dayOvertime = _overtimeBo.CalculateDayOvertimeRate(dayOvertime);
            Assert.Equal(5, dayOvertime.TotalHours);
            Assert.Equal(5, dayOvertime.InOvertimeHours);
            Assert.Equal(7.666666666666667, dayOvertime.PaidHours);

            OvertimeRecord overtimeRecordOvertime2 = new OvertimeRecord();
            overtimeRecordOvertime2.OvertimeType = OvertimeType.Overtime;
            overtimeRecordOvertime2.Hours = 2;
            overtimeRecordOvertime2.StartTime = new DateTime(2022, 11, 19, 17, 0, 0);
            overtimeRecordOvertime2.EndTime = new DateTime(2022, 11, 19, 19, 0, 0);
            overtimeRecordOvertime2.ProjectNumber = "RP19555";
            dayOvertime.OvertimeRecords.Add(overtimeRecordOvertime2);
            _overtimeBo.CalculateDayOvertimeRate(dayOvertime);
            Assert.Equal(7, dayOvertime.TotalHours);
            Assert.Equal(7, dayOvertime.InOvertimeHours);
            Assert.Equal(11, dayOvertime.PaidHours);

            dayOvertime.OvertimeRecords.Add(overtimeRecordOvertime3);
            _overtimeBo.CalculateDayOvertimeRate(dayOvertime);
            Assert.Equal(10, dayOvertime.TotalHours);
            Assert.Equal(10, dayOvertime.InOvertimeHours);
            Assert.Equal(18, dayOvertime.PaidHours);

            dayOvertime.OvertimeDate = new DateTime(2022, 10, 10);
            dayOvertime.OvertimeRecords = new List<OvertimeRecord>
              {
                  overtimeRecordReserve2
              };
            _overtimeBo.CalculateDayOvertimeRate(dayOvertime);
            Assert.Equal(2, dayOvertime.TotalHours);
            Assert.Equal(2.0, dayOvertime.PaidHours);
            Assert.Equal(0, dayOvertime.InOvertimeHours);

            dayOvertime.OvertimeRecords.Add(overtimeRecordOvertime3);
            _overtimeBo.CalculateDayOvertimeRate(dayOvertime);
            Assert.Equal(5, dayOvertime.TotalHours);
            Assert.Equal(5.0, dayOvertime.PaidHours);
            Assert.Equal(0, dayOvertime.InOvertimeHours);
        }

        [Fact]
        public void DayOvertime2B1RateB1CardTest()
        {
            B1Card b1Card = new B1Card();
            b1Card.B1_WDate = new DateTime(2023, 1, 13);
            b1Card.CreatedTime = new DateTime(2023, 1, 13, 14, 15, 10);
            b1Card.AddSigners = "2268";
            b1Card.DateTypeId = (int)WorkdayType.WeekWorkday;
            b1Card.EmpNo = "0395";
            b1Card.Reason = "test";
            b1Card.FormUID = Guid.NewGuid();
            b1Card.SheetNo = "加111000002";
            B1CardDetail detail = new B1CardDetail();
            detail.Project = "RP19552";
            detail.Hour = 1;
            detail.B1_CODE = (int)OvertimeType.Overtime;
            detail.SerialNo = 1;
            detail.StartTime = new DateTime(2023, 1, 14, 8, 0, 0);
            detail.EndTime = new DateTime(2023, 1, 14, 9, 0, 0);
            b1Card.Details.Add(detail);
            B1CardDetail detail2 = new B1CardDetail();
            detail2.Project = "RP19553";
            detail2.Hour = 3;
            detail2.B1_CODE = (int)OvertimeType.ReservedLeave;
            detail2.SerialNo = 2;
            detail2.StartTime = new DateTime(2023, 1, 14, 9, 30, 0);
            detail2.EndTime = new DateTime(2023, 1, 14, 12, 30, 0);
            b1Card.Details.Add(detail2);

            B1CardDetail detail3 = new B1CardDetail();
            detail3.Project = "9652K";
            detail3.Hour = 2;
            detail3.B1_CODE = (int)OvertimeType.OutsideOvertime;
            detail3.SerialNo = 3;
            detail3.StartTime = new DateTime(2023, 1, 14, 13, 30, 0);
            detail3.EndTime = new DateTime(2023, 1, 14, 15, 30, 0);
            b1Card.Details.Add(detail3);
            DayOvertime dayOvertime = _overtimeBo.B1CardToDayOvertime(b1Card);

            Assert.Equal(6, dayOvertime.TotalHours);
            Assert.Equal(9.3333333333333339, dayOvertime.PaidHours);
            Assert.Equal(6, dayOvertime.InOvertimeHours);


            B1CardDetail detail4 = new B1CardDetail();
            detail4.Project = "TI06999";
            detail4.Hour = 4;
            detail4.B1_CODE = (int)OvertimeType.OutsideOvertime;
            detail4.SerialNo = 4;
            detail4.StartTime = new DateTime(2023, 1, 14, 15, 30, 0);
            detail4.EndTime = new DateTime(2023, 1, 14, 19, 30, 0);
            b1Card.Details.Add(detail4);
            dayOvertime = _overtimeBo.B1CardToDayOvertime(b1Card);


            /*
            DayOvertime dayOvertime = new DayOvertime();
            dayOvertime.OvertimeDate = new DateTime(2022, 12, 5);
            OvertimeRecord overtimeRecordOvertime1Hour = new OvertimeRecord();
            overtimeRecordOvertime1Hour.OvertimeType = OvertimeType.Overtime;
            overtimeRecordOvertime1Hour.StartTime = new DateTime(2022, 12, 5, 16, 30, 0);
            overtimeRecordOvertime1Hour.EndTime = new DateTime(2022, 12, 5, 17, 30, 0);
            overtimeRecordOvertime1Hour.ProjectNumber = "RP19552";
            dayOvertime.OvertimeRecords.Add(overtimeRecordOvertime1Hour);
            OvertimeRecord overtimeRecordReserve2Hours = new OvertimeRecord();
            overtimeRecordReserve2Hours.OvertimeType = OvertimeType.ReservedLeave;
            overtimeRecordReserve2Hours.ProjectNumber = "RP19553";
            overtimeRecordReserve2Hours.StartTime = new DateTime(2022, 12, 5, 17, 30, 0);
            overtimeRecordReserve2Hours.EndTime = new DateTime(2022, 12, 5, 19, 30, 0);
            dayOvertime.OvertimeRecords.Add(overtimeRecordReserve2Hours);
            dayOvertime = _overtimeBo.CalculateDayOvertimeRate(dayOvertime);
            */
            Assert.Equal(10, dayOvertime.TotalHours);
            Assert.Equal(18.0, dayOvertime.PaidHours);
            Assert.Equal(10, dayOvertime.InOvertimeHours);

            DataTable dtB1Rate;
            DataTable dtB1Rate_CompHol;
            (dtB1Rate, dtB1Rate_CompHol) = _overtimeBo.DayOvertimeToB1Rate(dayOvertime, b1Card);

            Assert.Equal(5, dtB1Rate.Rows.Count);
            Assert.Equal(3, dtB1Rate_CompHol.Rows.Count);

            int rate = (int)dtB1Rate.Rows[0]["B1_RATE"];
            Assert.Equal(2, rate);
            int hours = (int)dtB1Rate.Rows[0]["B1_HOURS"];
            Assert.Equal(1, hours);
            hours = (int)dtB1Rate.Rows[1]["B1_HOURS"];
            Assert.Equal(1, hours);
            hours = (int)dtB1Rate.Rows[2]["B1_HOURS"];
            Assert.Equal(1, hours);
            hours = (int)dtB1Rate.Rows[3]["B1_HOURS"];
            Assert.Equal(4, hours);
            hours = (int)dtB1Rate.Rows[4]["B1_HOURS"];
            Assert.Equal(3, hours);
            foreach (DataRow row in dtB1Rate.Rows)
            {
                string x = $"ProjNo={row["B1_PRJNO"]} B1_HOURS={row["B1_HOURS"]} B1_RATE={row["B1_RATE"]}";
                //Console.WriteLine(x);
            }
            bool isOvertime = (bool)dtB1Rate.Rows[0]["IsOvertime"];
            Assert.True(isOvertime);
            rate = (int)dtB1Rate.Rows[0]["B1_RATE"];
            Assert.Equal(2, rate);
            rate = (int)dtB1Rate.Rows[1]["B1_RATE"];
            Assert.Equal(2, rate);
            rate = (int)dtB1Rate.Rows[2]["B1_RATE"];
            Assert.Equal(3, rate);
            rate = (int)dtB1Rate.Rows[3]["B1_RATE"];
            Assert.Equal(3, rate);
            rate = (int)dtB1Rate.Rows[4]["B1_RATE"];
            Assert.Equal(4, rate);
            foreach (DataRow row in dtB1Rate_CompHol.Rows)
            {
                string x = $"B1_HOURS={row["B1_HOURS"]} B1_RATE={row["B1_RATE"]}";
                //Console.WriteLine(x);
            }
            rate = (int)dtB1Rate_CompHol.Rows[0]["B1_RATE"];
            Assert.Equal(3, rate);
            rate = (int)dtB1Rate_CompHol.Rows[1]["B1_RATE"];
            Assert.Equal(6, rate);
            rate = (int)dtB1Rate_CompHol.Rows[2]["B1_RATE"];
            Assert.Equal(6, rate);
            hours = (int)dtB1Rate.Rows[0]["B1_HOURS"];
            Assert.Equal(1, hours);
            hours = (int)dtB1Rate.Rows[1]["B1_HOURS"];
            Assert.Equal(1, hours);
            hours = (int)dtB1Rate.Rows[2]["B1_HOURS"];
            Assert.Equal(1, hours);
        }

        [Fact]
        public void DayOvertime2B1RateByB1CardDtoTest()
        {
            B1CardDto b1Card = new B1CardDto();
            b1Card.B1_PROJNO = "RP19553";
            b1Card.B1_EMPNO = "0349";
            b1Card.B1_YYMM = "11111";
            b1Card.B1_SDD = "19";
            b1Card.B1_EDD = "19";
            b1Card.B1_WDate = new DateTime(2022, 11, 19);

            DayOvertime overtime = new DayOvertime();
            overtime.OvertimeDate = b1Card.B1_WDate;
            OvertimeRecord overtimeRecordReserve2 = new OvertimeRecord();
            overtimeRecordReserve2.OvertimeType = OvertimeType.ReservedLeave;
            overtimeRecordReserve2.Hours = 2;
            overtimeRecordReserve2.Order = 1;
            overtimeRecordReserve2.StartTime = new DateTime(2022, 11, 19, 17, 0, 0);
            overtimeRecordReserve2.EndTime = new DateTime(2022, 11, 19, 19, 10, 0);
            overtimeRecordReserve2.ProjectNumber = "RP19555";
            overtime.OvertimeRecords.Add(overtimeRecordReserve2);
            OvertimeRecord overtimeRecordOvertime3 = new OvertimeRecord();
            overtimeRecordOvertime3.OvertimeType = OvertimeType.Overtime;
            overtimeRecordOvertime3.Hours = 3;
            overtimeRecordOvertime3.Order = 2;
            overtimeRecordOvertime3.StartTime = new DateTime(2022, 11, 19, 08, 15, 0);
            overtimeRecordOvertime3.EndTime = new DateTime(2022, 11, 19, 11, 20, 0);
            overtimeRecordOvertime3.ProjectNumber = "RP19553";
            overtime.OvertimeRecords.Add(overtimeRecordOvertime3);

            OvertimeRecord overtimeRecordReserve3 = new OvertimeRecord();
            overtimeRecordReserve3.OvertimeType = OvertimeType.ReservedLeave;
            overtimeRecordReserve3.Hours = 4;
            overtimeRecordReserve3.Order = 3;
            overtimeRecordReserve3.StartTime = new DateTime(2022, 11, 19, 8, 0, 0);
            overtimeRecordReserve3.EndTime = new DateTime(2022, 11, 19, 12, 0, 0);
            overtimeRecordReserve3.ProjectNumber = "RP19553";
            overtime.OvertimeRecords.Add(overtimeRecordReserve3);
            OvertimeRecord overtimeRecordReserve4 = new OvertimeRecord();
            overtimeRecordReserve4.OvertimeType = OvertimeType.ReservedLeave;
            overtimeRecordReserve4.Hours = 3;
            overtimeRecordReserve3.Order = 4;
            overtimeRecordReserve4.StartTime = new DateTime(2022, 11, 19, 12, 20, 0);
            overtimeRecordReserve4.EndTime = new DateTime(2022, 11, 19, 15, 20, 0);
            overtimeRecordReserve4.ProjectNumber = "RP19554";
            overtime.OvertimeRecords.Add(overtimeRecordReserve4);
            _overtimeBo.CalculateDayOvertimeRate(overtime);
            Assert.Equal(12, overtime.TotalHours);
            Assert.Equal(23.333333333333336, overtime.PaidHours);
            Assert.Equal(12, overtime.InOvertimeHours);
            DataTable dtB1Rate;
            DataTable dtB1RateComp;
            (dtB1Rate, dtB1RateComp) = _overtimeBo.DayOvertime2B1Rate(overtime, b1Card);
            Assert.NotEmpty(dtB1Rate.Rows);
            Assert.NotEmpty(dtB1RateComp.Rows);
            Assert.Equal(5, dtB1Rate.Rows.Count);
            Assert.Equal(9, dtB1RateComp.Rows.Count);
            string yyyMMdd = (string)dtB1Rate.Rows[0]["B1_YYMMDD"];
            Assert.Equal("1111119", yyyMMdd);

            int hours = (int)dtB1Rate.Rows[0]["B1_HOURS"];
            Assert.Equal(2, hours);
            hours = (int)dtB1Rate.Rows[1]["B1_HOURS"];
            Assert.Equal(1, hours);
            hours = (int)dtB1Rate.Rows[2]["B1_HOURS"];
            Assert.Equal(2, hours);
            hours = (int)dtB1Rate.Rows[3]["B1_HOURS"];
            Assert.Equal(4, hours);
            hours = (int)dtB1Rate.Rows[4]["B1_HOURS"];
            Assert.Equal(3, hours);

            b1Card = new B1CardDto();
            b1Card.B1_PROJNO = "RP19553";
            b1Card.B1_EMPNO = "0349";
            b1Card.B1_YYMM = "11202";
            b1Card.B1_SDD = "13";
            b1Card.B1_EDD = "13";
            b1Card.B1_WDate = new DateTime(2023, 2, 13, 20, 15, 23);
            overtime = new DayOvertime();
            overtime.OvertimeDate = b1Card.B1_WDate;
            OvertimeRecord overtimeRecord3 = new OvertimeRecord();
            overtimeRecord3.StartTime = new DateTime(2023, 2, 13, 17, 0, 0);
            overtimeRecord3.EndTime = new DateTime(2023, 2, 13, 20, 0, 0);
            overtimeRecord3.OvertimeType = OvertimeType.Overtime;
            overtimeRecord3.Order = 1;
            overtimeRecord3.Hours = 3;
            overtimeRecord3.ProjectNumber = "RP19553";
            overtime.OvertimeRecords.Add(overtimeRecordOvertime3);
            overtime = _overtimeBo.CalculateDayOvertimeRate(overtime);
            Assert.Equal(3, overtime.TotalHours);
            Assert.Equal(3, overtime.InOvertimeHours);
            Assert.Equal(4.333333333333333, overtime.PaidHours);
            (dtB1Rate, dtB1RateComp) = _overtimeBo.DayOvertime2B1Rate(overtime, b1Card);
            Assert.NotEmpty(dtB1Rate.Rows);
            Assert.Empty(dtB1RateComp.Rows);
            Assert.Equal(2, dtB1Rate.Rows.Count);
            hours = (int)dtB1Rate.Rows[0]["B1_HOURS"];
            Assert.Equal(2, hours);
            hours = (int)dtB1Rate.Rows[1]["B1_HOURS"];
            Assert.Equal(1, hours);
        }

        [Fact]
        public void GetMonthEmployeeOvertimeHoursTest()
        {
            DateTime date = new DateTime(2020, 1, 1);
            string empNo = "0395";
            int hours = _overtimeBo.GetMonthEmployeeOvertimeHours(date, empNo);
            Assert.Equal(0, hours);
        }

        [Theory]
        [InlineData(8, "TG24215", '1', "0989", "2024-08-31", 38.0 / 3)]
        [InlineData(8, "RP19553", '1', "0349", "2024-08-31", 38.0 / 3)]
        public void GetPaidHourTest(int hours, string project, char code, string empNo, DateTime date, double expected)
        {
            B1CardApp b1CardApp = new B1CardApp();
            b1CardApp.B1_Hour = hours;
            b1CardApp.B1_PrjNo = project;
            b1CardApp.B1_Code = code;
            b1CardApp.B1_EmpNo = empNo;
            b1CardApp.B1_Date = date;
            double actual = _overtimeBo.GetPaidHour(b1CardApp);
            Assert.Equal(expected, actual);
        }

    }
}
