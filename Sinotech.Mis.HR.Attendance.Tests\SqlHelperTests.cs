﻿using Microsoft.Extensions.Configuration;
using Sinotech.Mis.Extensions.Configuration;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.Helpers.Tests
{
    [ExcludeFromCodeCoverage]
    public class SqlHelperTests
    {
        private DataTable _dataTable;

        private IConfiguration _configuration;

        private class TestIdNameObject
        {
            public int Id { get; set; }
            public string Name { get; set; }
        }

        public SqlHelperTests()
        {
            //_configuration = configuration;
            _configuration = new ConfigurationBuilder().
                AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).Build();

            _dataTable = new DataTable();
            _dataTable.Columns.Add("Id", typeof(int));
            _dataTable.Columns.Add("Name", typeof(string));
            _dataTable.Rows.Add(1, "<PERSON>");
            _dataTable.Rows.Add(2, "<PERSON>");
        }

        [Fact]
        public void ConvertDataTable_DataRowArray_ReturnsList()
        {
            // Arrange
            DataRow[] dataRows = _dataTable.Select();

            // Act
            List<TestIdNameObject> result = SqlHelper.ConvertDataTable<TestIdNameObject>(dataRows);

            // Assert
            Assert.Equal(2, result.Count);
            Assert.Equal(1, result[0].Id);
            Assert.Equal("John", result[0].Name);
            Assert.Equal(2, result[1].Id);
            Assert.Equal("Mary", result[1].Name);
        }

        [Fact]
        public void ConvertDataTable_DataTable_ReturnsList()
        {
            // Act
            List<TestIdNameObject> result = SqlHelper.ConvertDataTable<TestIdNameObject>(_dataTable);

            // Assert
            Assert.Equal(2, result.Count);
            Assert.Equal(1, result[0].Id);
            Assert.Equal("John", result[0].Name);
            Assert.Equal(2, result[1].Id);
            Assert.Equal("Mary", result[1].Name);
        }


        [Fact]
        public void CreateListFromTable_GivenValidDataTable_ReturnsList()
        {
            // Act
            List<TestIdNameObject> result = SqlHelper.CreateListFromTable<TestIdNameObject>(_dataTable);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Equal(1, result[0].Id);
            Assert.Equal("John", result[0].Name);
            Assert.Equal(2, result[1].Id);
            Assert.Equal("Mary", result[1].Name);
        }

        [Fact]
        public void CreateItemFromRow_GivenValidDataRow_ReturnsItem()
        {
            // Arrange
            DataTable dataTable = new DataTable();
            dataTable.Columns.Add("Id", typeof(int));
            dataTable.Columns.Add("Name", typeof(string));
            dataTable.Rows.Add(1, "John");

            DataRow dataRow = dataTable.Rows[0];

            // Act
            TestIdNameObject result = SqlHelper.CreateItemFromRow<TestIdNameObject>(dataRow);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(1, result.Id);
            Assert.Equal("John", result.Name);
        }

        [Fact]
        public void DataTableToList_ReturnsCorrectList()
        {
            // Act
            List<TestIdNameObject> list = SqlHelper.DataTableToList<TestIdNameObject>(_dataTable);

            // Assert
            Assert.Equal(2, list.Count);
            Assert.Equal(1, list[0].Id);
            Assert.Equal("John", list[0].Name);
            Assert.Equal(2, list[1].Id);
            Assert.Equal("Mary", list[1].Name);
        }

        [Fact]
        public void ExecuteSqlCommand_ReturnsCorrectNumberOfAffectedRows()
        {
            string connectionString = _configuration.GetSecuredConnectionString("Workday");
            string sql = @"SELECT ShiftId into #tempShift FROM WorkShift;";
            // Act
            int affectedRows = SqlHelper.ExecuteSqlCommand(connectionString, sql);
            // Assert
            Assert.True(affectedRows >= 1);
        }

        [Fact]
        public void ExecuteSqlCommand_WithParameter_ReturnsCorrectNumberOfAffectedRows()
        {
            // Arrange
            string connectionString = _configuration.GetSecuredConnectionString("Workday");
            string sql = @"SELECT ShiftName into #tempShift FROM WorkShift;";
            SqlParameter[] parameters = new SqlParameter[2]
            {
                new SqlParameter("@ColumnName", "ShiftName") ,
                new SqlParameter("@TableName", "WorkShift")
            };

            // Act
            int affectedRows = SqlHelper.ExecuteSqlCommand(connectionString, sql, parameters);

            // Assert
            Assert.True(affectedRows >= 1);


            //parameters = new SqlParameter[3]
            //{
            //    new SqlParameter("@ColumnName", "ShiftName") ,
            //    new SqlParameter("@TableName", "WorkShift"),
            //    new SqlParameter("@ColumnValue", "正常班")
            //};
            //sql = @"UPDATE @TableName SET @ColumnName=@ColumnValue FROM @TableName WHERE @ColumnName=@ColumnValue;";
            //affectedRows = SqlHelper.ExecuteSqlCommand(connectionString, sql, parameters);
            //// Assert
            //Assert.Equal(-1, affectedRows);
        }


        [Fact]
        public void ExecuteNonQuery_ReturnsCorrectNumberOfAffectedRows()
        {
            // Arrange
            string connectionString = _configuration.GetSecuredConnectionString("Workday");
            SqlConnection connection = new SqlConnection(connectionString);
            connection.Open();
            SqlTransaction transaction = connection.BeginTransaction();

            string sql = @"SELECT ShiftId into #tempShift FROM WorkShift;";
            List<SqlParameter> parameters = new List<SqlParameter>()
            {
                new SqlParameter("@ColumnName", "ShiftId"),
                new SqlParameter("@TableName", "WorkShift")
            };

            // Act
            int affectedRows = SqlHelper.ExecuteNonQuery(connection, transaction, sql, parameters);
            transaction.Rollback();
            connection.Close();
            // Assert
            Assert.True(affectedRows >= 1);
        }

    }
}