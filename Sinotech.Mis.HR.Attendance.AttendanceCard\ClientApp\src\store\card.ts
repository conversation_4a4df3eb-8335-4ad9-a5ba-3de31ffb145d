import { defineStore } from 'pinia'
import type { CardStoreType, CardApiType, FormFlowType, FormFlowApiType, FlowApiType, AttachmentsApiType } from '../api/appType'

export const useCardStore = defineStore('card', {
  state: () => ({
    data: undefined as ((CardStoreType & FormFlowType) | undefined),
    queryDataTime: new Date()
  }),
  actions: {
    setData(cardData: CardApiType & FormFlowApiType) {
      this.data = {
        formID: cardData.FormID,
        formUID: cardData.FormUID,
        formNo: cardData.FormNo,
        empNo: cardData.EmpNo,
        empName: cardData.EmpName,
        deptNo: cardData.DeptNo,
        deptSName: cardData.DeptSName,
        formInfo: cardData.FormInfo,
        addedSigner: cardData.AddedSigner,
        createdEmpNo: cardData.CreatedEmpNo,
        createdName: cardData.CreatedName,
        createdTime: cardData.CreatedTime,
        card: cardData.Card,
        attachments: (cardData.Attachments === null) ? [] : cardData.Attachments.map((attachment: AttachmentsApiType) => {
          return {
            id: attachment.ID,
            formUID: attachment.FormUID,
            originalFileName: attachment.OriginalFileName
          }
        }),
        flows: (cardData.Flows.length > 0) ? cardData.Flows.map((flow: FlowApiType) => {
          return {
            flowUID: flow.FlowUID,
            step: flow.Step,
            flowName: flow.FlowName,
            recipientEmpNo: flow.RecipientEmpNo,
            recipientName: flow.RecipientName,
            approverName: flow.ApproverName,
            approveComments: flow.ApproveComments,
            isAgentApprove: flow.IsAgentApprove,
            approveTime: flow.ApproveTime,
            flowStatusName: flow.FlowStatusName
          }
        }) : [],
        currentStep: cardData.CurrentStep,
        formStatus: cardData.FormStatus,
        formStatusName: cardData.FormStatusName,
        updatedName: cardData.UpdatedName,
        updatedTime: cardData.UpdatedTime,
        endTime: cardData.EndTime
      }
    },
    setQueryDataTime() {
      this.queryDataTime = new Date()
    }
  }
})