﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.Common.Tests
{
    [ExcludeFromCodeCoverage]
    public class B1CardTests
    {
        [Fact]
        public void EasyClone_ShouldReturnClonedObject()
        {
            // Arrange
            var original = new B1Card
            {
                AddSigners = "Signer1",
                CreatedTime = DateTime.Now,
                FilledTime = DateTime.Now.AddHours(1),
                ApplicationType = "Type1",
                EmpNo = "12345",
                Reason = "Reason1",
                Details = new List<B1CardDetail>
                {
                    new B1CardDetail { B1_CODE = 1, ApplicationType = "加班" }
                }
            };

            // Act
            var clone = original.EasyClone();

            // Assert
            Assert.NotSame(original, clone);
            Assert.Equal(original.AddSigners, clone.AddSigners);
            Assert.Equal(original.CreatedTime, clone.CreatedTime);
            Assert.Equal(original.FilledTime, clone.FilledTime);
            Assert.Equal(original.ApplicationType, clone.ApplicationType);
            Assert.Equal(original.EmpNo, clone.EmpNo);
            Assert.Equal(original.Reason, clone.Reason);
            Assert.Equal(original.Details, clone.Details);
        }

        [Fact]
        public void EasyEquals_ShouldReturnTrueForIdenticalObjects()
        {
            DateTime now = DateTime.Now;
            // Arrange
            var card1 = new B1Card
            {
                AddSigners = "Signer1",
                CreatedTime = now,
                FilledTime = now.AddHours(1),
                ApplicationType = "加班",
                EmpNo = "12345",
                Reason = "Reason1",
                Details = new List<B1CardDetail>
                {
                    new B1CardDetail { B1_CODE = 1, ApplicationType = "加班" }
                }
            };

            var card2 = new B1Card
            {
                AddSigners = "Signer1",
                CreatedTime = now,
                FilledTime = now.AddHours(1),
                ApplicationType = "加班",
                EmpNo = "12345",
                Reason = "Reason1",
                Details = new List<B1CardDetail>
                {
                    new B1CardDetail { B1_CODE = 1, ApplicationType = "加班" }
                }
            };

            // Act
            var result = card1.EasyEquals(card2);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void EasyEquals_ShouldReturnFalseForDifferentObjects()
        {
            // Arrange
            var card1 = new B1Card
            {
                AddSigners = "Signer1",
                CreatedTime = DateTime.Now,
                FilledTime = DateTime.Now.AddHours(1),
                ApplicationType = "Type1",
                EmpNo = "12345",
                Reason = "Reason1",
                Details = new List<B1CardDetail>
                {
                    new B1CardDetail { B1_CODE = 1, ApplicationType = "加班" }
                }
            };

            var card2 = new B1Card
            {
                AddSigners = "Signer2",
                CreatedTime = DateTime.Now.AddDays(1),
                FilledTime = DateTime.Now.AddHours(2),
                ApplicationType = "Type2",
                EmpNo = "67890",
                Reason = "Reason2",
                Details = new List<B1CardDetail>
                {
                    new B1CardDetail { B1_CODE = 2, ApplicationType = "社外加班" }
                }
            };

            // Act
            var result = card1.EasyEquals(card2);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void EasyEquals_ShouldReturnFalseForNull()
        {
            // Arrange
            var card = new B1Card
            {
                AddSigners = "Signer1",
                CreatedTime = DateTime.Now,
                FilledTime = DateTime.Now.AddHours(1),
                ApplicationType = "Type1",
                EmpNo = "12345",
                Reason = "Reason1",
                Details = new List<B1CardDetail>
                {
                    new B1CardDetail { B1_CODE = 1, ApplicationType = "加班" }
                }
            };

            // Act
            var result = card.EasyEquals(null);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void SetApplicationType_ShouldSetCorrectApplicationType()
        {
            // Arrange
            var card = new B1Card
            {
                Details = new List<B1CardDetail>
                {
                    new B1CardDetail { B1_CODE = 1 },
                    new B1CardDetail { B1_CODE = 2 },
                    new B1CardDetail { B1_CODE = 3 },
                    new B1CardDetail { B1_CODE = 1 }
                }
            };

            // Act
            card.SetApplicationType();

            // Assert
            Assert.Equal("加班、社外加班、補休假", card.ApplicationType);
        }

        [Fact]
        public void SetApplicationType_ShouldHandleEmptyDetails()
        {
            // Arrange
            var card = new B1Card
            {
                Details = new List<B1CardDetail>()
            };

            // Act
            card.SetApplicationType();

            // Assert
            Assert.Equal(string.Empty, card.ApplicationType);
        }

        [Fact]
        public void SetApplicationType_ShouldHandleUnknownB1_CODE()
        {
            // Arrange
            var card = new B1Card
            {
                Details = new List<B1CardDetail>
                {
                    new B1CardDetail { B1_CODE = 99 }
                }
            };

            // Act
            card.SetApplicationType();

            // Assert
            Assert.Equal(string.Empty, card.ApplicationType);
        }
    }
}
