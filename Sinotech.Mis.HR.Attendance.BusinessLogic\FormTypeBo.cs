﻿using System.Collections.Generic;
using System.Data;
using Sinotech.Mis.HR.Attendance.Common;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic;

public class FormTypeBo: IFormTypeBo
{

    private readonly IAttendanceBo _attendanceBo;

    public FormTypeBo(IAttendanceBo attendanceBo)
    {
        _attendanceBo = attendanceBo;
    }

    /// <summary>
    /// Gets or sets the FormTypes
    /// </summary>
    /// <value>
    /// The FormTypes
    /// </value>
    private static List<FormType>? FormTypes { get; set; } = null;

    /// <summary>
    /// 由表單ID取得表單中文名
    /// </summary>
    /// <param name="formId"></param>
    /// <returns></returns>
    public string? GetFormName(string formId)
    {
        List<FormType> formTypes = GetFormTypes();
        string? formName = null;
        foreach (FormType formType in formTypes)
        {
            if (formType.FormID == formId)
            {
                formName = formType.FormName;
                break;
            }
        }
        return formName;
    }

    /// <summary>
    /// 取得所有 FormTypes
    /// </summary>
    /// <returns></returns>
    public List<FormType> GetFormTypes()
    {
        List<FormType> formTypes;
        if (FormTypes != null)
        {
            return FormTypes;
        }
        else
        {
            DataTable dtFormTypes = _attendanceBo.GetFormTypeDataTable();
            formTypes = new List<FormType>();
            foreach (DataRow dr in dtFormTypes.Rows)
            {
                FormType formType = new FormType
                {
                    FormID = (string)dr["FormID"],
                    FormName = (string)dr["FormName"],
                    FormSName = (string)dr["FormSName"]
                };
                formTypes.Add(formType);
            }

            FormTypes = formTypes;
        }
        return formTypes;
    }

}