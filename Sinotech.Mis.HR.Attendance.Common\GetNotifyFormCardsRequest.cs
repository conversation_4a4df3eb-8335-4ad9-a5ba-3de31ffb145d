﻿using System;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 取得特定時間區間的通知表單卡別資料之請求
    /// </summary>
    public class GetNotifyFormCardsRequest
    {
        /// <summary>
        /// 起始通知日期
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 結束通知日期
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 申請人員工編號
        /// </summary>
        public string EmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 申請人部門編號
        /// </summary>
        public int DeptNo { get; set; } = 0;

        /// <summary>
        /// 被通知者員工編號
        /// </summary>
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 表單狀態(複選)
        /// </summary>
        public int[] Status { get; set; } = [];

        /// <summary>
        /// 通知狀態(複選)
        /// </summary>
        public int[] IsRead { get; set; } = [];
    }
}
