﻿using Sinotech.Mis.Common;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.Tests
{
    [ExcludeFromCodeCoverage]
    public class B1CardCheckerTests
    {
        private readonly AttendanceBo _attendanceBo;
        private readonly EmployeeBo _employeeBo;

        public B1CardCheckerTests(AttendanceBo attendanceBo, EmployeeBo employeeBo)
        {
            _attendanceBo = attendanceBo;
            _employeeBo = employeeBo;
        }

        [Fact]
        public void IsOvertimeAllowedTest()
        {

            DateTime date = new DateTime(2023, 2, 20, 0, 0, 0, DateTimeKind.Local);
            DataTable dt = _employeeBo.GetEmployeeDataTable();
            B1CardParameters b1CardParameters = new B1CardParameters();
            b1CardParameters.OvertimeDate = date;
            b1CardParameters.IsFilledB1CardApp = true;
            B1Card b1Card = new B1Card();

            foreach (DataRow dr in dt.Rows)
            {
                string empNo = (string)dr["EmpNo"];
                b1Card.EmpNo = empNo;
                Employee employeeDetail = _employeeBo.GetEmployeeDetail(empNo);
                b1CardParameters.EmployeeDetail = employeeDetail;
                b1CardParameters.EmployeePosition = _attendanceBo.GetPositionType(empNo);
                b1CardParameters.B1Card = b1Card;
                var checker = new B1CardChecker(b1CardParameters);
                var b1CardResult = checker.IsOvertimeAllowed();
                if (empNo == "0395")
                {
                    Assert.Equal(0, b1CardResult.Code);
                    Assert.Equal(B1CardStatusEnum.Success, b1CardResult.Status);
                    b1CardResult = checker.CheckIfAppliedOvertimeWork();
                    Assert.Equal(0, b1CardResult.Code);
                }
                if (empNo == "0391")
                {
                    Assert.Equal(500, b1CardResult.Code);
                    Assert.Equal(B1CardStatusEnum.Error, b1CardResult.Status);
                }
                if (empNo == "2096")
                {
                    Assert.Equal(500, b1CardResult.Code);
                    Assert.Equal(B1CardStatusEnum.Error, b1CardResult.Status);
                }
                b1CardResult = checker.CheckOvertimeDate();
                Assert.Equal(0, b1CardResult.Code);
                b1CardResult = checker.CheckOvertimeSegment();
                Assert.Equal(0, b1CardResult.Code);

            }

        }

    }
}