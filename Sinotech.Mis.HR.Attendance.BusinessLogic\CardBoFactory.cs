﻿using Sinotech.Mis.HR.Attendance.Common;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic
{
    /// <summary>
    /// 卡 商業物件 工廠
    /// </summary>
    public class CardBoFactory: ICardBoFactory
    {
        private readonly IA1CardBo _a1CardBo;
        private readonly IB1CardBo _b1CardBo;
        private readonly IB1CardAppBo _b1CardAppBo;
        private readonly IC1CardBo _c1CardBo;

        /// <summary>
        /// 建構元
        /// </summary>
        /// <param name="a1CardBo">正常工作卡商業物件</param>
        /// <param name="b1CardBo">加班卡商業物件</param>
        /// <param name="b1CardAppBo">加班申請卡商業物件</param>
        /// <param name="c1CardBo">請假卡商業物件</param>
        public CardBoFactory(IA1CardBo a1CardBo, IB1CardBo b1CardBo, IB1CardAppBo b1CardAppBo, 
            IC1CardBo c1CardBo)
        {
            _a1CardBo = a1CardBo;
            _b1CardBo = b1CardBo;
            _b1CardAppBo = b1CardAppBo;
            _c1CardBo = c1CardBo;
        }

        /// <summary>
        /// 取得卡商業物件
        /// </summary>
        /// <param name="cardName"></param>
        /// <returns></returns>
        public ICardBaseBo? GetCardBo(string cardName)
        {
            ICardBaseBo? cardBo = null;
            switch (cardName)
            {
                case "A1Card":
                    cardBo = _a1CardBo;
                    break;
                case "B1Card":
                    cardBo = _b1CardBo;
                    break;
                case "B1CardApp":
                    cardBo = _b1CardAppBo;
                    break;
                case "C1Card":
                    cardBo = _c1CardBo;
                    break;
            }
            return cardBo;
        }
    }
}
