﻿namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 部門
    /// </summary>
    public class Department
    {
        /// <summary>
        /// 部門代號
        /// </summary>
        public int DeptNo { get; set; }

        /// <summary>
        /// 部門英文單碼簡稱
        /// </summary>
        public string DeptESSName { get; set; } = string.Empty;

        /// <summary>
        /// 部門英文簡稱
        /// </summary>
        public string DeptESName { get; set; } = string.Empty;

        /// <summary>
        /// 部門單碼簡稱
        /// </summary>
        public string DeptSSName { get; set; } = string.Empty;

        /// <summary>
        /// 部門中文簡稱
        /// </summary>
        public string DeptSName { get; set; } = string.Empty;

        /// <summary>
        /// 部門中文名稱
        /// </summary>
        public string DeptName { get; set; } = string.Empty;

        /// <summary>
        /// 部門英文名稱
        /// </summary>
        public string DeptEName { get; set; } = string.Empty;

        /// <summary>
        /// 狀態，現用為1，停用為0
        /// </summary>
        public int StNo { get; set; }
    }
}
