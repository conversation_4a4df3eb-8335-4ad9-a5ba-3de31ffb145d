﻿﻿using Sinotech.Mis.HR.Attendance.Common;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves
{
    /// <summary>
    /// 防疫隔離假類別
    /// </summary>
    [LeaveKind(LeaveKindEnum.QuarantineIsolationLeave)]
    public class QuarantineIsolationLeave : C1CardLeaveNotProvided
    {

        /// <summary>
        /// 建構子
        /// </summary>
        /// <param name="c1Card"></param>
        /// <param name="c1CardBo"></param>
        public QuarantineIsolationLeave(C1Card c1Card, IC1CardBo c1CardBo) : base(c1Card, c1CardBo)
        {
        }
    }
}
