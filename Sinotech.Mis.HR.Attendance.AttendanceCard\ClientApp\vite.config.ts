﻿/// <reference types="vitest" />
import serverOption from './serverOption'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig(({mode}) => {
  const network: string = 'intranet' // 社內為'intranet'，社外為'internet'

  return {
    plugins: [vue()],
    server: {
      ...serverOption,
      fs: {
        strict: false
      }
    },
    css: {
      devSourcemap: true
    },
    mode: mode + '.' + network,
    base: (mode === 'development') ? '/' : ((network === 'intranet') ? '/AttendanceCard/' : '/CardInternet/')
  }
})
