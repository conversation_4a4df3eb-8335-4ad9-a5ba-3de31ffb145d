﻿using Sinotech.Mis.HR.Attendance.Utilities;
using Microsoft.AspNetCore.Http;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Net;
using UAParser.Objects;
using Xunit;
#nullable enable
namespace Sinotech.Mis.HR.Attendance.Utilities.Tests
{
    [ExcludeFromCodeCoverage]
    public class CardUtilityTests
    {
        [Theory]
        [InlineData('日', 0, false)]
        [InlineData('一', 1, false)]
        [InlineData('二', 2, false)]
        [InlineData('三', 3, false)]
        [InlineData('四', 4, false)]
        [InlineData('五', 5, false)]
        [InlineData('六', 6, false)]
        [InlineData(' ', 7, true)]
        [InlineData(' ', -1, true)]
        public void RocWeekDayCharTest(char weekDay, int dayNumber, bool isSpace)
        {
            char rocWeekDayChar = CardUtility.RocWeekDayChar(dayNumber);
            Assert.Equal(weekDay, rocWeekDayChar);
            Assert.Equal(isSpace, char.IsWhiteSpace(rocWeekDayChar));
        }

        [Theory]
        [InlineData("日", 0, false)]
        [InlineData("一", 1, false)]
        [InlineData("二", 2, false)]
        [InlineData("三", 3, false)]
        [InlineData("四", 4, false)]
        [InlineData("五", 5, false)]
        [InlineData("六", 6, false)]
        [InlineData("", 7, true)]
        [InlineData("", -1, true)]
        public void RocWeekDayTest(string weekDay, int dayNumber, bool isEmpty)
        {
            string rocWeekDay = CardUtility.RocWeekDay(dayNumber);
            Assert.Equal(weekDay, rocWeekDay);
            Assert.Equal(isEmpty, string.IsNullOrWhiteSpace(rocWeekDay));
        }

        [Theory]
        [InlineData("0349", true, "")]
        [InlineData(@"secinc\0349", false, "0349")]
        [InlineData(@"SECINC\0349", false, "0349")]
        [InlineData(@"sinotech\0349", false, "0349")]
        [InlineData(@"SINOTECH\0349", false, "0349")]
        [InlineData(@"secltd\0349", true, "")]
        public void GetValidEmpNoTest(string logonUser, bool isEmpty, string expect)
        {
            string empNo = CardUtility.GetValidEmpNo(logonUser);
            Assert.Equal(isEmpty, string.IsNullOrEmpty(empNo));
            Assert.Equal(expect, empNo);
        }


        [Fact]
        public void GetLocalHostNameTest()
        {
            string? hostname = CardUtility.GetLocalHostName();
            Assert.NotNull(hostname);
        }

        [Fact]
        public void RocChineseYearTest()
        {
            DateTime date = new DateTime(2025, 3, 3, 0, 0, 0, DateTimeKind.Local);
            int rocYear = CardUtility.RocChineseYear(date);
            Assert.Equal(114, rocYear);
        }

        [Fact]
        public void RocChineseYearStringTest()
        {
            DateTime date = new DateTime(2025, 3, 3);
            string rocYear = CardUtility.RocChineseYearString(date);
            Assert.Equal("114", rocYear);
        }

        [Fact]
        public void RocChineseYearMonthStringTest()
        {
            DateTime date = new DateTime(2025, 3, 3);
            string rocYearMonth = CardUtility.RocChineseYearMonthString(date);
            Assert.Equal("114年3月", rocYearMonth);
        }

        [Fact]
        public void RocChineseDateStringTest()
        {
            DateTime date = new DateTime(2025, 3, 3);
            string rocDateString = CardUtility.RocChineseDateString(date);
            Assert.Equal("114/03/03", rocDateString);
        }

        [Fact]
        public void GetIPandHostnameTest()
        {
            HttpContext? httpContext = null;
            var (ip, hostname) = CardUtility.GetIP_Hostname(httpContext);
            Assert.Null(ip);
            Assert.Null(hostname);

        }

        [Theory]
        [InlineData(1990, 3, 5, "07903")]
        [InlineData(1991, 3, 5, "08003")]
        [InlineData(2000, 3, 5, "08903")]
        [InlineData(2010, 4, 5, "09904")]
        [InlineData(2011, 5, 5, "10005")]
        [InlineData(2022, 7, 5, "11107")]
        [InlineData(2023, 8, 8, "11208")]
        public void RocChineseYYYMMTest(int year, int month, int day, string rocYYYMM)
        {
            string yyymm = CardUtility.RocChineseYYYMM(new DateTime(year, month, day, 0, 0, 0, DateTimeKind.Local));
            Assert.Equal(rocYYYMM, yyymm);
        }

        [Theory]
        [InlineData("7877880088", "78778800880", 11)]
        [InlineData("0888880088", "08888800880", 11)]
        [InlineData("0888880088", "088888008800", 12)]
        [InlineData("00000000088", "00000000088", 11)]
        [InlineData(null, "00000", 5)]
        [InlineData("123", "12300", 5)]
        [InlineData("123", "123000", 6)]
        [InlineData("123456", "123456", 5)]
        public void TailFillZeroTest(string input, string expected, int digits)
        {
            string result = CardUtility.TailFillZero(input, digits);
            Assert.Equal(expected, result);
        }


        /// <summary>
        /// AES加密解密，由於加密字串變成不固定，所以需要加密解密同時測試
        /// </summary>
        /// <param name="cryptoKey"></param>
        /// <param name="source"></param>
        [Theory]
        [InlineData("Sinotech1156932687919198Sinotech1156932687919198Sinotech1156932687919198Sinotech1156932687919198", "This is a book.")]
        [InlineData("Sinotech1156932687919198Sinotech1156932687919198Sinotech1156932687919198Sinotech1156932687919198", "是不是這樣的夜晚，你才會這樣的想起我")]
        [InlineData("Sinotech1156932687919198Sinotech1156932687919198Sinotech1156932687919198Sinotech1156932687919198", "Where there is a will, there is a way.")]
        public void AesEncryptDecryptBase64Test(string cryptoKey, string source)
        {
            string encrypt = CardUtility.AesEncryptBase64(cryptoKey, source);
            string decrypt = CardUtility.AesDecryptBase64(cryptoKey, encrypt);
            Assert.Equal(source, decrypt);
        }

        [Fact]
        public void AesEncryptBase64Test_Invalid()
        {
            string cryptoKey = string.Empty;
            string source = "Not empty";
            Assert.Throws<ArgumentException>(() => CardUtility.AesEncryptBase64(cryptoKey, source));
            cryptoKey = "Not empty";
            source = string.Empty;
            Assert.Throws<ArgumentException>(() => CardUtility.AesEncryptBase64(cryptoKey, source));
        }

        [Fact]
        public void AesDecryptBase64Test_Invalid()
        {
            string cryptoKey = string.Empty;
            string source = "Not empty";
            Assert.Throws<ArgumentException>(() => CardUtility.AesDecryptBase64(cryptoKey, source));
            cryptoKey = "Not empty";
            source = string.Empty;
            Assert.Throws<ArgumentException>(() => CardUtility.AesDecryptBase64(cryptoKey, source));
        }


        [Theory]
        [InlineData(3.0, true)]
        [InlineData(1.1, false)]
        [InlineData(5.00001, false)]
        public void IsIntegerTest(double doubleValue, bool expect)
        {
            bool result = CardUtility.IsInteger(doubleValue);
            Assert.Equal(expect, result);
        }

        [Theory]
        [InlineData("PublicKey.xml", "PrivateKey.xml", "This is a book.")]
        [InlineData("PublicKey.xml", "PrivateKey.xml", "Where there is a will, there is a way.")]
        public void RsaEncryptDecryptTest(string publicKeyPath, string privateKeyPath, string plainText)
        {
            string encryptText = CardUtility.RsaEncrypt(publicKeyPath, plainText);
            string decryptText = CardUtility.RsaDecrypt(privateKeyPath, encryptText);
            Assert.Equal(plainText, decryptText);
        }

        [Fact]
        public void MaskNumberTest()
        {
            long unixTime = CardUtility.ConvertToUnixTimestamp(DateTime.Now);
            string masked = CardUtility.MaskNumber(unixTime);
            long result = CardUtility.UnmaskNumber(masked);
            Assert.Equal(unixTime, result);
        }

        [Fact]
        public void GetLocationTest()
        {
            string ip = "*************";
            string geoDbPath = @"d:\GeoIP\GeoLite2-City.mmdb";
            if (System.IO.File.Exists(geoDbPath))
            {
                IPAddress iPAddress = IPAddress.Parse(ip);
                var location = CardUtility.GetLocation(iPAddress, geoDbPath);
                Assert.NotNull(location);
                string country = $"{location.Country}";
                Assert.NotEmpty(country);
            }
            Assert.True(true);
        }

        [Theory]
        [InlineData("*************", "d:\\GeoIP\\GeoLite2-ASN.mmdb", false)]
        [InlineData("************", "d:\\GeoIP\\GeoLite2-ASN.mmdb", true)]
        public void GetAsnResponseTest(string ipAddress, string asnDbPath, bool isNull)
        {
            if (System.IO.File.Exists(asnDbPath))
            {
                var asn = CardUtility.GetAsnResponse(ipAddress, asnDbPath);
                string? asnName = null;
                string? asnSystemNumber = null;
                if (isNull)
                {
                    Assert.Null(asn);
                }
                else
                {
                    Assert.NotNull(asn);
                }
                if (asn != null)
                {
                    asnName = asn.AutonomousSystemOrganization;
                    asnSystemNumber = asn.AutonomousSystemNumber.ToString();
                }
                if (asn != null)
                {
                    string output = $"IP:{ipAddress} 網路供應商 ASN:{asnSystemNumber} 組織:{asnName}";
                    Console.WriteLine(output);
                }
                else
                {
                    Console.WriteLine($"IP:{ipAddress} 網路供應商 ASN:未知");
                }
            }
            Assert.True(true);
        }

        [Fact]
        public void GetClientInfoTest()
        {
            string userAgent = "Mozilla/5.0 (iPhone; CPU iPhone OS 5_1_1 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Version/5.1 Mobile/9B206 Safari/7534.48.3";
            var parser = UAParser.Parser.GetDefault();
            ClientInfo clientInfo = parser.Parse(userAgent);
            // 取得作業系統名稱與版本
            string osName = clientInfo.OS.Family;
            string osMajorVersion = clientInfo.OS.Major;
            string osVersion = $"{clientInfo.OS.Major}.{clientInfo.OS.Minor}" +
                $".{clientInfo.OS.Patch}.{clientInfo.OS.PatchMinor}";
            string browser = clientInfo.Browser.Family;
            string browserVersion = clientInfo.Browser.Version;
            string device = clientInfo.Device.ToString();
            string model = clientInfo.Device.Model.ToString();
            Assert.Equal("iOS", osName);
            Assert.Equal("5", osMajorVersion);
            Assert.Equal("5.1.1.", osVersion);
            Assert.Equal("Mobile Safari", browser);
            Assert.Equal("5.1", browserVersion);
            Assert.Equal("iPhone", device);
            Assert.Equal("iPhone", model);
        }

        [Fact]
        public void RsaDecryptByPrivateKeyTest()
        {
            // C#預設只能使用[私鑰]進行解密(想使用[私鑰加密]可使用第三方元件BouncyCastle來實現)
            string privateKey = File.ReadAllText("PrivateKey.xml");
            CardUtility.RsaDecryptByPrivateKey(privateKey, "This is a book.");
            Assert.True(true);
        }

        [Theory]
        [InlineData("This is a book.", "d98e0cdc5ac3d7a17d7299a4f571aa0a")]
        [InlineData("Where there is a will, there is a way.", "4c0f756c84cea218a037ef5c11211d98")]
        public void ComputeMD5HashTest(string input, string expected)
        {
            string result = CardUtility.ComputeMD5Hash(input);
            Assert.Equal(expected, result);
        }

        [Fact]
        public void CreateRSAKeyTest()
        {
            string privateKeyPath = "PrivateKey.xml";
            string publicKeyPath = "PublicKey.xml";
            CardUtility.CreateRSAKey(privateKeyPath, publicKeyPath);
            Assert.True(true);
        }

        [Fact]
        public void GetTypeByFileTest()
        {
            var assemblies = AppDomain.CurrentDomain.GetAssemblies();
            Assert.NotEmpty(assemblies);
            Assert.NotNull(assemblies[0]);
            string? fullName = assemblies[0].FullName;
            Assert.NotNull(fullName);
            string? dll = fullName.Split(',')[0];
            Assert.NotNull(dll);
            CardUtility.GetTypeByFile(dll, dll);
            Assert.True(true);
        }

        [Theory]
        [InlineData("", false)]
        [InlineData("test", false)]
        [InlineData("test@test", false)]
        [InlineData("<EMAIL>", true)]
        [InlineData("<EMAIL>", true)]
        [InlineData("<EMAIL>", true)]
        public void IsValidEmailTest(string email, bool expected)
        {
            bool actual = CardUtility.IsValidEmail(email);
            Assert.Equal(expected, actual);
        }

        [Fact]
        public void PostProcessUploadedFilesTest()
        {
            List<UploadedFile> files = new List<UploadedFile>();
            UploadedFile file = new UploadedFile();
            file.FileName = "test.txt";
            file.FilePathName = "Uploads\\test.txt";
            file.Size = 100;
            files.Add(file);
            string uploadFolder = "Uploads";
            CardUtility.PostProcessUploadedFiles(files, uploadFolder);
            Assert.True(true);
        }

        [Fact]
        public void GetLogonUserTest()
        {
            System.Security.Principal.IIdentity identity = new System.Security.Principal.GenericIdentity("test");
            string? result = CardUtility.GetLogonUser(identity);
            Assert.NotNull(result);
        }

        [Fact]
        public void GetTypeByAssemblyName_ValidAssemblyAndType_ReturnsType()
        {
            // Arrange
            string assemblyName = "System.Private.CoreLib";
            string typeName = "System.String";

            // Act
            Type? result = CardUtility.GetTypeByAssemblyName(assemblyName, typeName);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(typeName, result?.FullName);
        }

        [Fact]
        public void GetTypeByAssemblyName_InvalidAssembly_ThrowsException()
        {
            // Arrange
            string assemblyName = "Invalid.Assembly";
            string typeName = "System.String";

            // Act & Assert
            Assert.Throws<FileNotFoundException>(() => CardUtility.GetTypeByAssemblyName(assemblyName, typeName));
        }

        [Fact]
        public void GetTypeByFile_ValidDllAndType_ReturnsType()
        {
            // Arrange
            string dllName = "Sinotech.Mis.HR.Attendance.BusinessLogic.dll";
            string typeName = "Sinotech.Mis.HR.Attendance.BusinessLogic.FormBo";

            // Act
            Type? result = CardUtility.GetTypeByFile(dllName, typeName);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(typeName, result?.FullName);
        }
    }
}
