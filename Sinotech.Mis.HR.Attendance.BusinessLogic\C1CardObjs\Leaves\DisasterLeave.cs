﻿using Sinotech.Mis.HR.Attendance.Common;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves
{
    /// <summary>
    /// 防災假
    /// </summary>
    /// <seealso cref="Sinotech.Mis.HR.Attendance.C1CardObjs.C1CardBase" />
    [LeaveKind(LeaveKindEnum.DisasterLeave)]
    public class DisasterLeave : C1CardBypassAnyCheck
    {

        /// <summary>
        /// 建構子
        /// </summary>
        /// <param name="c1Card"></param>
        /// <param name="c1CardBo"></param>
        public DisasterLeave(C1Card c1Card, IC1CardBo c1CardBo) : base(c1Card, c1CardBo)
        {
        }
    }
}
