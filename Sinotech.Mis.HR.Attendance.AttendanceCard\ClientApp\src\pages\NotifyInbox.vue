<template>
  <h6 class="my-2 fs-2 text-center">
    <i class="bi bi-bell me-1" />
    <span>三卡通知</span>
  </h6>
  <div class="container px-0">
    <div class="text-end">
      <button
        type="button"
        :class="[
          'btn',
          ((cardDataIsAllRead === true) || (userStore.logonUserId !== userStore.userId)) ? 'btn-outline-secondary' : 'btn-primary'
        ]"
        :disabled="(cardDataIsAllRead === true) || (userStore.logonUserId !== userStore.userId)"
        @click="onMarkNotification"
      >
        全部已讀
      </button>
    </div>
    <DataTable
      :value="cardData"
      :paginator="true"
      :alwaysShowPaginator="false"
      :first="queryPage"
      :rows="rows"
      :loading="cardDataLoading"
      :sortField="sortField"
      :sortOrder="sortOrder"
      :rowClass="cardRead"
      class="border my-2"
    >
      <template #header>
        <div
          id="accordionDiv"
          class="accordion"
        >
          <div class="accordion-item">
            <h2
              id="heading"
              class="accordion-header"
            >
              <button
                class="accordion-button collapsed"
                type="button"
                data-bs-toggle="collapse"
                data-bs-target="#collapseDiv"
                aria-expanded="true"
                aria-controls="collapseDiv"
              >
                <span>查詢條件</span>
                <span class="ms-1">(預設查詢近一月通知紀錄)</span>
              </button>
            </h2>
            <div
              id="collapseDiv"
              class="accordion-collapse collapse"
              aria-labelledby="heading"
              data-bs-parent="#accordionDiv"
            >
              <div class="accordion-body py-1">
                <div class="row my-2">
                  <div class="col-3 col-md-2">
                    <span>部門</span>
                  </div>
                  <div class="col-6 col-md-4">
                    <VueSelect
                      label="deptSName"
                      :reduce="(e: DeptType) => e.deptNo"
                      :options="deptOptions"
                      :clearable="false"
                      :searchable="false"
                      :modelValue="dept"
                      @update:modelValue="onChangeDept"
                    />
                  </div>
                </div>
                <div class="row my-2">
                  <div class="col-3 col-md-2">
                    <span>申請人</span>
                  </div>
                  <div class="col-6 col-md-4">
                    <ChooseUser
                      :modelValue="{
                        userId: employee.userId,
                        userName: employee.userName,
                        deptNo: employee.deptNo,
                        deptSName: employee.deptSName
                      }"
                      :employeeData="dept.deptNo === -1 ? employeeData : employeeData.filter((employeeEle) => (employeeEle.deptNo === dept.deptNo))"
                      :filter="onSearchEmployeeData"
                      :clearable="employee.userId.length > 0"
                      @update:modelValue="onChangeEmployee"
                      @delete="onDeleteEmployee"
                    />
                  </div>
                </div>
                <div class="row my-2">
                  <div class="col-3 col-md-2">
                    <span>通知時間</span>
                  </div>
                  <div class="col-6 col-md-9">
                    <div class="row">
                      <div class="col-auto py-1">
                        <RocCalendarSelect
                          v-model="startNotifyTime"
                          :inputClass="{
                            'form-control': true
                          }"
                        />
                      </div>
                      <div class="col-auto d-flex align-items-center">
                        <span>至</span>
                      </div>
                      <div class="col-auto py-1">
                        <RocCalendarSelect
                          v-model="endNotifyTime"
                          :inputClass="{
                            'form-control': true
                          }"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div class="row my-2">
                  <div class="col-3 col-md-2">
                    <span>通知狀態</span>
                  </div>
                  <div class="col-6 col-md-4">
                    <div class="form-check">
                      <input
                        id="isRead1"
                        v-model="isRead"
                        class="form-check-input"
                        type="checkbox"
                        value="1"
                      >
                      <label
                        class="form-check-label"
                        for="isRead1"
                      >
                        <span>未讀</span>
                      </label>
                    </div>
                    <div class="form-check">
                      <input
                        id="isRead2"
                        v-model="isRead"
                        class="form-check-input"
                        type="checkbox"
                        value="2"
                      >
                      <label
                        class="form-check-label"
                        for="isRead2"
                      >
                        <span>已讀</span>
                      </label>
                    </div>
                  </div>
                </div>
                <div class="row my-2">
                  <div class="col-3 col-md-2">
                    <span>表單狀態</span>
                  </div>
                  <div class="col-6 col-md-4">
                    <div class="form-check">
                      <input
                        id="formStatus1"
                        v-model="formStatus"
                        class="form-check-input"
                        type="checkbox"
                        value="1"
                      >
                      <label
                        class="form-check-label"
                        for="formStatus1"
                      >
                        <span>進行中</span>
                      </label>
                    </div>
                    <div class="form-check">
                      <input
                        id="formStatus2"
                        v-model="formStatus"
                        class="form-check-input"
                        type="checkbox"
                        value="2"
                      >
                      <label
                        class="form-check-label"
                        for="formStatus2"
                      >
                        <span>同意</span>
                      </label>
                    </div>
                    <div class="form-check">
                      <input
                        id="formStatus3"
                        v-model="formStatus"
                        class="form-check-input"
                        type="checkbox"
                        value="3"
                      >
                      <label
                        class="form-check-label"
                        for="formStatus3"
                      >
                        <span>不同意</span>
                      </label>
                    </div>
                    <div class="form-check">
                      <input
                        id="formStatus4"
                        v-model="formStatus"
                        class="form-check-input"
                        type="checkbox"
                        value="4"
                      >
                      <label
                        class="form-check-label"
                        for="formStatus4"
                      >
                        <span>抽單</span>
                      </label>
                    </div>
                  </div>
                </div>
                <div class="row my-2">
                  <div class="col text-center">
                    <button
                      type="button"
                      class="btn btn-primary me-1 p-1 p-sm-2"
                      @click="onQueryData"
                    >
                      <span>查詢</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template #empty>
        <div class="text-center">
          <span>無</span>
        </div>
      </template>
      <template #paginatorfirstpagelinkicon>
        <i class="bi bi-chevron-double-left" />
      </template>
      <template #paginatorprevpagelinkicon>
        <i class="bi bi-chevron-left" />
      </template>
      <template #paginatornextpagelinkicon>
        <i class="bi bi-chevron-right" />
      </template>
      <template #paginatorlastpagelinkicon>
        <i class="bi bi-chevron-double-right" />
      </template>
      <Column
        field="formNo"
        header="卡號"
        :sortable="true"
      >
        <template #body="{ data, field }">
          <router-link
            :to="{
              name: data['formID'] + 'Notify',
              params: {
                formUID: data['formUID'],
                id: data['id']
              }
            }"
          >
            {{ data[field] }}
          </router-link>
        </template>
        <template #sorticon="sort">
          <span
            v-if="sort.sorted === false"
            class="bi bi-arrow-down-up ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === 1)"
            class="bi bi-sort-up-alt ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === -1)"
            class="bi bi-sort-down-alt ms-1"
          />
        </template>
      </Column>
      <Column
        field="formID"
        header="卡別"
        :sortable="true"
      >
        <template #body="{ data, field }">
          {{ data[field] ? formatFormID(data[field]) : '' }}
        </template>
        <template #sorticon="sort">
          <span
            v-if="sort.sorted === false"
            class="bi bi-arrow-down-up ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === 1)"
            class="bi bi-sort-up-alt ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === -1)"
            class="bi bi-sort-down-alt ms-1"
          />
        </template>
      </Column>
      <Column
        field="empName"
        header="申請人"
        :sortable="true"
      >
        <template #body="{ data }">
          <span class="me-1">
            {{ data['empNo'] ?? '' }}
          </span>
          <span>
            {{ data['empName'] ?? '' }}
          </span>
        </template>
        <template #sorticon="sort">
          <span
            v-if="sort.sorted === false"
            class="bi bi-arrow-down-up ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === 1)"
            class="bi bi-sort-up-alt ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === -1)"
            class="bi bi-sort-down-alt ms-1"
          />
        </template>
      </Column>
      <Column
        field="applicationType"
        header="申請別"
        :sortable="true"
      >
        <template #body="{ data, field }">
          <template v-if="data['formID'] !== 'A1Card'">
            {{ data[field] }}
          </template>
        </template>
        <template #sorticon="sort">
          <span
            v-if="sort.sorted === false"
            class="bi bi-arrow-down-up ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === 1)"
            class="bi bi-sort-up-alt ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === -1)"
            class="bi bi-sort-down-alt ms-1"
          />
        </template>
      </Column>
      <Column
        field="formInfo"
        header="申請資訊"
      >
        <template #sorticon="sort">
          <span
            v-if="sort.sorted === false"
            class="bi bi-arrow-down-up ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === 1)"
            class="bi bi-sort-up-alt ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === -1)"
            class="bi bi-sort-down-alt ms-1"
          />
        </template>
      </Column>
      <Column
        field="notifyTime"
        header="通知時間"
        :sortable="true"
      >
        <template #body="{ data, field }">
          {{ data[field] ? dateToRocString(new Date(data[field])) : '' }}
        </template>
        <template #sorticon="sort">
          <span
            v-if="sort.sorted === false"
            class="bi bi-arrow-down-up ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === 1)"
            class="bi bi-sort-up-alt ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === -1)"
            class="bi bi-sort-down-alt ms-1"
          />
        </template>
      </Column>
      <Column
        field="formStatus"
        header="表單狀態"
        :sortable="true"
      >
        <template #body="{ data, field }">
          {{ data[field] ? data['formStatusName'] : '' }}
        </template>
        <template #sorticon="sort">
          <span
            v-if="sort.sorted === false"
            class="bi bi-arrow-down-up ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === 1)"
            class="bi bi-sort-up-alt ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === -1)"
            class="bi bi-sort-down-alt ms-1"
          />
        </template>
      </Column>
      <Column
        field="isRead"
        :hidden="true"
      />
    </DataTable>

    <template v-if="queryDataTime !== null">
      <div class="text-black-50 text-end">
        <span>
          查詢時間：
        </span>
        <span class="fst-italic">
          {{ dateToRocString(queryDataTime) }}
        </span>
      </div>
    </template>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useAuthUserStore } from '../store/index'
import { useToast } from 'primevue/usetoast'
import { useDepartment } from '../composable/department'
import { useEmployeeData } from '../composable/employeeData'
import { useAbortController } from '../composable/abortController'
import { useNotifyInboxQueryStore } from '../store/notifyInboxQuery'
import { useNavMenuBadgeStore } from '../store/navMenuBadge'
import { onBeforeRouteLeave } from 'vue-router'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import { POST_Notifications_URL, POST_MARKDELIVEREDNOTIFICATIONS_URL } from '../api/appUrl'
import { dateToRocString, formatFormID, onSearchEmployeeData } from '../api/appFunction'
import { SYSTEM_ERROR_MESSAGE, DATERANGE_WARN_MESSAGE } from '../api/appConst'
import VueSelect from 'vue-select'
import ChooseUser from '../components/ChooseUser.vue'
import RocCalendarSelect from '../components/RocCalendarSelect.vue'
import type { NotifyInboxType, EmployeeStoreBaseType, DeptType } from '../api/appType'

const rows = 10

const toast = useToast()
const { employeeData, onSetEmployeeData } = useEmployeeData()
const { deptOptions, onGetDeptOptions } = useDepartment()
const userStore = useAuthUserStore()
const notifyInboxQueryStore = useNotifyInboxQueryStore()
const navMenuBadgeStore = useNavMenuBadgeStore()
const { abortController, fetchErrorHandler, abortListener } = useAbortController()

const queryDataTime = ref<Date | null>(null)
const cardData = ref<Array<NotifyInboxType>>([])
const cardDataLoading = ref<boolean>(false)

const cardDataIsAllRead = computed<boolean>(() => cardData.value.find((e: NotifyInboxType) => e.isRead === '1') === undefined)

const {
  queryPage,
  sortField,
  sortOrder,
  isRead,
  startNotifyTime,
  endNotifyTime,
  employee,
  dept,
  formStatus
} = storeToRefs(notifyInboxQueryStore)

const cardRead = (data: NotifyInboxType): string => {
  return (data.isRead === '1') ? 'fw-bold' : ''
}

const onChangeEmployee = (event: EmployeeStoreBaseType): void => {
  employee.value = {
    userId: event.userId,
    userName: event.userName,
    deptNo: event.deptNo,
    deptSName: event.deptSName
  }
}

const onDeleteEmployee = (): void => {
  employee.value = {
    userId: '',
    userName: '',
    deptNo: 0,
    deptSName: ''
  }
}

const onChangeDept = (event: number): void => {
  dept.value = deptOptions.value.find((e: DeptType) => e.deptNo === event) ?? dept.value
  if ((dept.value.deptNo !== -1) && (employee.value.deptNo !== dept.value.deptNo)) {
    onDeleteEmployee()
  }
}

const onQueryData = async (): Promise<void> => {
  if (startNotifyTime.value > endNotifyTime.value) {
    toast.add({
      severity: 'warn',
      summary: DATERANGE_WARN_MESSAGE,
      group: 'app'
    })
  } else {
    queryDataTime.value = new Date()
    cardDataLoading.value = true

    await fetch(POST_Notifications_URL, {
      method: 'POST',
      headers: {
        'content-type': 'application/json'
      },
      body: JSON.stringify({
        userId: userStore.userId,
        empNo: employee.value.userId,
        deptNo: (dept.value.deptNo === -1) ? 0 : dept.value.deptNo,
        startDate: startNotifyTime.value.toISOString(),
        endDate: endNotifyTime.value.toISOString(),
        status: formStatus.value,
        isRead: isRead.value
      }),
      signal: abortController.signal
    }).then((res: Response) => {
      if (!res.ok) {
        throw new Error(res.status.toString())
      }
      return res.json()
    }).then(res => {
      cardData.value = res.map((e: any) => {
        return {
          id: e.ID,
          formID: e.FormID,
          formUID: e.FormUID,
          formNo: e.FormNo,
          formInfo: e.FormInfo,
          formStatus: e.FormStatus,
          formStatusName: e.FormStatusName,
          applicationType: e.ApplicationType,
          empNo: e.EmpNo,
          empName: e.EmpName,
          deptNo: e.DeptNo,
          notifyTime: new Date(e.NotifyTime),
          viewTime: e.ViewTime,
          isRead: e.ViewTime ? '2' : '1'
        }
      })
    }).catch((err: Error): void => {
      console.error(err)
      fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
    }).finally((): void => {
      cardDataLoading.value = false
    })
  }
}

const onMarkNotification = (): void => {
  fetch(POST_MARKDELIVEREDNOTIFICATIONS_URL, {
    method: 'POST',
    headers: {
      'content-type': 'application/json'
    },
    body: JSON.stringify({
      userId: userStore.userId
    }),
    signal: abortController.signal
  }).then((res: Response) => {
    if (!res.ok) {
      throw new Error(res.status.toString())
    }
    return res.json()
  }).then(async (res: { result: boolean }): Promise<void> => {
    if (res.result === true) {
      await onQueryData()
      navMenuBadgeStore.setBadge(userStore.userId, abortController.signal)
    }
  }).catch((err: Error): void => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })
}

onBeforeRouteLeave(() => {
  toast.removeGroup('app')
})

onMounted(async (): Promise<void> => {
  abortListener()
  deptOptions.value.push({
    deptNo: -1,
    deptSName: '全社'
  })
  await onGetDeptOptions(abortController.signal).catch((err: Error) => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })

  if (dept.value.deptNo === 0) {
    dept.value = {
      deptNo: -1,
      deptSName: '全社'
    }
  }

  await onSetEmployeeData(dept.value.deptNo, abortController.signal).catch((err: Error) => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })

  await onQueryData()
})
</script>