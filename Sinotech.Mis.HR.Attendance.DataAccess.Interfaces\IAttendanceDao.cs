﻿using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;

namespace Sinotech.Mis.HR.Attendance.DataAccess.Interfaces
{
    /// <summary>
    /// 出勤資料存取介面
    /// </summary>
    public interface IAttendanceDao
    {

        /// <summary>
        /// 新增表單
        /// </summary>
        /// <param name="formID">表單名稱</param>
        /// <param name="chineseYear">ROC年度</param>
        /// <param name="dataSet">包含所有待新增資料表的 Data Set</param>
        /// <returns>(單號，錯誤訊息)</returns>
        public (string?,string) AddForm(string formID, int chineseYear, DataSet dataSet);

        /// <summary>
        /// 結案抽單
        /// </summary>
        /// <param name="withdraw">抽單物件</param>
        /// <returns></returns>
        public string ClosedWithdraw(Withdraw withdraw);

        /// <summary>
        /// 紀錄第一次讀取通知的時間。第二次後讀取通知不會更新時間，視為失敗
        /// </summary>
        /// <param name="id">通知編號</param>
        /// <returns>紀錄第一次讀取通知的結果是成功或失敗</returns>
        public bool DeliveredNotifications(int id);

        /// <summary>
        /// 取得流程狀態名稱列表
        /// </summary>
        /// <returns>流程狀態名稱DataTable</returns>
        public DataTable FormFlowStatusNames();

        /// <summary>
        /// 取得表單狀態名稱列表
        /// </summary>
        /// <returns>表單狀態名稱DataTable</returns>
        public DataTable FormStatusNames();

        /// <summary>
        /// 取得所有管理員名單
        /// </summary>
        /// <returns>所有管理員名單</returns>
        public DataTable GetAdministrators();

        /// <summary>
        /// 取得所有的通知
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>通知 DataTable</returns>
        public DataTable GetAllNotifications(string empNo);

        /// <summary>
        /// 取得全部已填表單，以填表日期為準
        /// </summary>
        /// <param name="startDate">最早填卡日期</param>
        /// <param name="endDate">最晚填卡日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns>已填表單</returns>
        public DataTable GetAllSentBox(DateTime startDate, DateTime endDate, int? status = null);

        /// <summary>
        /// 取得表單附件
        /// </summary>
        /// <param name="formUID">表單識別碼</param>
        /// <returns>表單附件DataTable</returns>
        public DataTable GetAttachments(Guid formUID);

        /// <summary>
        /// 讀取員工刷卡時間(依據查詢日期 )
        /// </summary>
        /// <param name="theDate">刷卡日期</param>
        /// <param name="empNo">員工編號</param>
        public DataTable GetDayInTime(DateTime theDate, string empNo);

        /// <summary>
        /// 取得部門已填表單
        /// </summary>
        /// <param name="deptNo">部門編號</param>
        /// <returns>已填表單</returns>
        public DataTable GetDepartmentSentBox(int deptNo);

        /// <summary>
        /// 取得部門已填表單，以填表日期為準
        /// </summary>
        /// <param name="deptNo">部門編號</param>
        /// <param name="startDate">最早填卡日期</param>
        /// <param name="endDate">最晚填卡日期</param>
        /// <returns>已填表單</returns>
        public DataTable GetDepartmentSentBox(int deptNo, DateTime startDate, DateTime endDate);

        /// <summary>
        /// 取得部門已填表單，以填表日期為主
        /// </summary>
        /// <param name="deptNo">部門員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>已填表單</returns>
        public DataTable GetDepartmentSentBox(int deptNo, DateTime startDate, DateTime endDate, string projNo);

        /// <summary>
        /// 取得部門已填表單，以內容日期為準
        /// </summary>
        /// <param name="deptNo">部門編號</param>
        /// <param name="startDate">最早日期</param>
        /// <param name="endDate">最晚日期</param>
        /// <returns>已填表單</returns>
        public DataTable GetDepartmentSentBoxByContentDate(int deptNo, DateTime startDate, DateTime endDate);

        /// <summary>
        /// 取得部門已填表單，以內容日期為準
        /// </summary>
        /// <param name="deptNo">部門編號</param>
        /// <param name="startDate">最早日期</param>
        /// <param name="endDate">最晚日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>已填表單</returns>
        public DataTable GetDepartmentSentBoxByContentDate(int deptNo, DateTime startDate, DateTime endDate, string projNo);

        /// <summary>
        /// 取得 部門  某月份已填表單 ，限部門登記桌或管理員
        /// </summary>
        /// <param name="deptNo">部門編號</param>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <returns>已填表單</returns>
        public DataTable GetDepartmentSentBoxYearMonth(int deptNo, int year, int month);

        /// <summary>
        /// 讀取員工指定月份(依據查詢日期 )休假統計時數，
        /// 包括年度總可用時數、年度已使用時數(簽核中＋已核可)、累至查詢月份已使用時數(簽核中＋已核可)、
        /// 查詢月份已使用時數(簽核中＋已核可)、年度已核可時數(已核可)、
        /// 累至查詢月份年度已核可時數(已核可)、查詢月份已核可時數(已核可)
        /// 提供
        /// 01：特別休息假
        /// 12：補休假
        /// 14：延休假
        /// </summary>
        /// <param name="date">查詢日期</param>
        /// <param name="employeeNumber">員工編號</param>
        public List<EmpLeaveInfo> GetEmpLeaveInfo(DateTime theDate, string employeeNumber);

        /// <summary>
        /// 讀取員工指定月份(依據查詢日期 )休假統計時數，
        /// 包括年度總可用時數、年度已使用時數(簽核中＋已核可)、累至查詢月份已使用時數(簽核中＋已核可)、
        /// 查詢月份已使用時數(簽核中＋已核可)、年度已核可時數(已核可)、
        /// 累至查詢月份年度已核可時數(已核可)、查詢月份已核可時數(已核可)
        /// 提供 01：特別休息假
        /// 04：事假
        /// 10：病假
        /// 12：補休假
        /// 14：延休假
        /// 16：生理假
        /// 19：家庭照顧假
        /// </summary>
        /// <param name="date">查詢日期</param>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="leaveNumber">假別代號</param>
        /// <param name="leaveSubNumber">假別細項代號</param>
        /// <returns></returns>
        public SqlParameter[] GetEmpLeaveInfo(DateTime theDate, string employeeNumber, int leaveNumber, int leaveSubNumber);

        /// <summary>
        /// 取得當年度1月至該月分之剩餘補休假時數
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empno">員工編號</param>
        /// <returns>剩餘補休假時數</returns>
        public int GetEmployeeCompensatoryLeaveHours(DateTime date, string empNo);

        /// <summary>
        /// 取得員工指定假別休假資料，包括年度總可用時數、年度已使用時數(簽核中＋已核可)、
        /// 累至查詢月份已使用時數(簽核中＋已核可)、查詢月份已使用時數(簽核中＋已核可)、
        /// 年度已核可時數(已核可)、累至查詢月份年度已核可時數(已核可)、
        /// 查詢月份已核可時數(已核可)
        /// 提供以下假別代碼查詢：
        /// 01：特別休息假
        /// 04：事假
        /// 10：病假
        /// 12：補休假(未開發)
        /// 14：延休假
        /// 16：生理假
        /// 19：家庭照顧假
        /// </summary>
        /// <param name="date">查詢日期.</param>
        /// <param name="empNo">員工編號.</param>
        /// <param name="leaveNumber">假別</param>
        /// <param name="leaveSubNumber">假別細項</param>
        public List<SqlParameter> GetEmployeeLeaveInfo(DateTime date, string empNo, LeaveKindEnum leaveNumber, int leaveSubNumber);

        /// <summary>
        /// 讀取員工指定日期已填報有效(簽核中與同意)且為加班上限範圍內之補休假換算加權後加班總時數
        /// </summary>
        /// <param name="theDate">查詢日期</param>
        /// <param name="empno">員工編號</param>
        public double GetEmployeeWeightedCompensatoryOvertimeHours(DateTime theDate, string empno);

        /// <summary>取得 Form</summary>
        /// <param name="formUID">表單識別碼</param>
        /// <returns>DataTable Form</returns>
        public DataTable GetForm(Guid formUID);

        /// <summary>取得 FormFLow</summary>
        /// <param name="flowUID">流程識別碼</param>
        /// <returns>DataTable FormFlow</returns>
        public DataTable GetFormFlow(Guid flowUID);

        /// <summary>取得 FormFLow</summary>
        /// <param name="formUID">表單識別碼</param>
        /// <returns>DataTable FormFlow</returns>
        public DataTable GetFormFlows(Guid formUID);

        /// <summary>
        /// 依日期區間取得所有表單 by 填報日期，限管理員使用
        /// </summary>
        /// <param name="startDate">最早填卡日期</param>
        /// <param name="endDate">最晚填卡日期</param>
        /// <returns>表單資料表</returns>
        public DataTable GetForms(DateTime startDate, DateTime endDate);

        /// <summary>
        /// 依日期區間取得所有表單 by 填報日期，限管理員使用
        /// </summary>
        /// <param name="startDate">開始日</param>
        /// <param name="endDate">結束日</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單資料表</returns>
        public DataTable GetForms(DateTime startDate, DateTime endDate, string projNo);

        /// <summary>
        /// 依日期區間取得所有表單 by 內容日期，限管理員使用
        /// </summary>
        /// <param name="startDate">最早填卡日期</param>
        /// <param name="endDate">最晚填卡日期</param>
        /// <returns>表單資料表</returns>
        public DataTable GetFormsByContentDate(DateTime startDate, DateTime endDate);

        /// <summary>
        /// 依日期區間取得所有表單 by 內容日期，限管理員使用
        /// </summary>
        /// <param name="startDate">開始日</param>
        /// <param name="endDate">結束日</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單資料表</returns>
        public DataTable GetFormsByContentDate(DateTime startDate, DateTime endDate, string projNo);
        /// <summary>
        /// Get Forms by FormStatus
        /// </summary>
        /// <param name="status">FormStatus</param>
        /// <returns>Forms</returns>
        public DataTable GetFormsByStatus(FormStatus status);

        /// <summary>
        /// Get Forms by FormStatus
        /// </summary>
        /// <param name="status">FormStatus</param>
        /// <param name="startDate">最早填卡日期</param>
        /// <param name="endDate">最晚填卡日期</param>
        /// <returns>Forms</returns>
        public DataTable GetFormsByStatus(FormStatus status, DateTime startDate, DateTime endDate);

        /// <summary>
        /// 取得FormType
        /// </summary>
        /// <returns></returns>
        public DataTable GetFormTypes();

        /// <summary>取得收件匣 DataTable</summary>
        /// <param name="roles">角色的List</param>
        /// <returns>收件匣 DataTable</returns>
        public DataTable GetInboxes(List<Role> roles);

        /// <summary>取得收件匣 DataTable</summary>
        /// <param name="role">角色</param>
        /// <returns>收件匣 DataTable</returns>
        public DataTable GetInboxes(string role);

        /// <summary>
        /// 讀取員工指定月份出勤時間
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns></returns>
        public DataTable GetMonthAttendance(DateTime date, string employeeNumber);

        /// <summary>
        /// 讀取員工指定月份(依據查詢日期 )休假統計時數
        /// </summary>
        /// <param name="theDate">查詢日期</param>
        /// <param name="employeeNumber">員工編號</param>
        public SqlParameter[] GetMonthEmployeeLeaves(DateTime theDate, string employeeNumber);

        /// <summary>
        /// 讀取員工指定月(依據查詢日期 ) 已填報有效(簽核中與同意)且為加班上限範圍內之加班總時數
        /// </summary>
        /// <param name="theDate">查詢日期</param>
        /// <param name="empno">員工編號</param>
        public int GetMonthEmployeeOvertimeHours(DateTime theDate, string empno);

        /// <summary>
        /// 讀取員工指定月(依據查詢日期 ) 已填報有效(簽核中與同意)且為加班上限範圍內之加班
        /// </summary>
        /// <param name="theDate">查詢日期</param>
        /// <param name="empno">員工編號</param>
        public DataTable GetMonthEmployeeOvertimeStatics(DateTime theDate, string empno);

        /// <summary>
        /// 讀取員工指定月(依據查詢日期 ) 已填報有效(簽核中與同意)且為加班上限範圍內之加權後加班總時數
        /// </summary>
        /// <param name="theDate">查詢日期</param>
        /// <param name="empno">員工編號</param>
        public double GetMonthEmployeeWeightedOvertimeHours(DateTime theDate, string empno);

        /// <summary>
        /// 讀取員工指定月份出勤時間字串
        /// </summary>
        /// <param name="date">查詢日期</param>
        /// <param name="userid">員工編號</param>
        /// <returns></returns>
        public DataTable GetMonthInTimeString(DateTime date, string userid);

        /// <summary>取得指定表單新單號 (最大表單單號+1)</summary>
        /// <param name="formID">表單編號</param>
        /// <param name="chineseYear">民國年</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public string GetNewFormNo(string formID, string chineseYear);

        /// <summary>
        /// 取得所有通知
        /// </summary>
        /// <returns>通知 DataTable</returns>
        public DataTable GetNotifications();

        /// <summary>
        /// 取得特定時間區間的通知表單卡別資料
        /// </summary>
        /// <param name="empNo">申請人員工編號</param>
        /// <param name="deptNo">申請人部門編號</param>
        /// <param name="roles">被通知者的角色</param>
        /// <param name="isRead">通知狀態，1代表未讀，2代表已讀</param>
        /// <param name="formStatus">表單狀態</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <returns>通知 DataTable</returns>
        public DataTable GetNotifyFormCards(string empNo, int deptNo, List<Role> roles, List<int> isRead, List<int> formStatus, DateTime startDate, DateTime endDate);

        /// <summary>
        /// 讀取員工指定季(依據查詢日期 ) 已填報有效(簽核中與同意)且為加班上限範圍內之加班總時數
        /// </summary>
        /// <param name="theDate">查詢日期</param>
        /// <param name="empno">員工編號</param>
        public int GetQuarterlyEmployeeOvertimeHours(DateTime theDate, string empno);

        /// <summary>取得 填表人/表單關係人 已填表單</summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns>寄件匣 DataTable</returns>
        public DataTable GetSentBox(string empNo, DateTime? startDate = null, DateTime? endDate = null, int? status = null);

        /// <summary>取得 填表人/表單關係人 已填表單</summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <param name="status">表單狀態</param>
        /// <returns>寄件匣 DataTable</returns>
        public DataTable GetSentBox(string empNo, DateTime startDate, DateTime endDate, string projNo, int? status = null);

        /// <summary>取得 填表人/表單關係人 已填表單，以內容日期為準</summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns>寄件匣 DataTable</returns>
        public DataTable GetSentBoxByContentDate(string empNo, DateTime startDate, DateTime endDate, int? status = null);

        /// <summary>取得 填表人/表單關係人 已填表單，以內容日期為準</summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <param name="status">表單狀態</param>
        /// <returns>寄件匣 DataTable</returns>
        public DataTable GetSentBoxByContentDate(string empNo, DateTime startDate, DateTime endDate, string projNo, int? status = null);

        /// <summary>
        /// 取得 填表人/表單關係人 某月份已填表單
        /// </summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <param name="status">表單狀態</param>
        /// <returns>已填表單</returns>
        public DataTable GetSentBoxYearMonth(string empNo, int year, int month, int? status = null);

        /// <summary>
        /// 取得 已簽核表單，包括本人簽核或代理人簽核，及預設本人角色(非代理)簽核
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="roles">角色</param>
        /// <returns>已簽核表單</returns>
        public DataTable GetSignedForms(string empNo, DateTime startDate, DateTime endDate, List<Role> roles);

        /// <summary>
        /// 取得 已簽核表單，包括本人簽核或代理人簽核，及預設本人角色(非代理)簽核，以申請內容日期為準
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="contentStartDate">申請內容啟始日期</param>
        /// <param name="contentEndDate">申請內容結束日期</param>
        /// <param name="roles">角色</param>
        /// <returns>已簽核表單</returns>
        public DataTable GetSignedFormsByContentDate(string empNo, DateTime startDate, DateTime endDate, DateTime contentStartDate, DateTime contentEndDate, List<Role> roles);

        /// <summary>
        /// 取得使用者前N筆簽核意見
        /// </summary>
        /// <param name="empNo"></param>
        /// <param name="count"></param>
        /// <returns>簽核意見</returns>
        public DataTable GetTopApproveComments(string empNo, int count);

        /// <summary>
        /// 得到 transaction.
        /// </summary>
        /// <param name="connection">out connection</param>
        /// <returns>transaction</returns>
        public SqlTransaction GetTransaction(out SqlConnection connection);

        /// <summary>
        /// 取得有效的加班卡
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>加班卡DataTable</returns>
        public DataTable GetValidB1Card(DateTime date, string empNo);


        /// <summary>
        /// 取得有效的加班申請卡，也就是 B1_Status 為1或2
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>加班申請卡DataTable</returns>
        public DataTable GetValidB1CardApp(DateTime date, string empNo);

        /// <summary>
        /// 取得指定月份的全年病假可休總時數
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empno">員工編號</param>
        /// <returns></returns>
        public int GetYearSickLeaveHours(DateTime date, string empNo);

        /// <summary>
        /// 是否為管理員
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        //public bool IsAdmin(string empNo);

        /// <summary>檢查是否有指定表單編號和通知編號的通知</summary>
        /// <param name="formUID">The form guid.</param>
        /// <param name="notifyId">The notify id.</param>
        /// <returns>是否有指定的通知</returns>
        public bool IsNotificationExist(Guid formUID, int notifyId);

        /// <summary>檢查是否有指定表單編號、通知編號和員工編號的通知</summary>
        /// <param name="roles">角色list</param>
        /// <param name="formUID">The form guid.</param>
        /// <param name="notifyId">The notify id.</param>
        /// <returns>是否有指定的通知</returns>
        public bool IsNotificationExist(List<Role> roles, Guid formUID, int notifyId);

        /// <summary>
        /// 判斷是不是可加班特殊員工
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="date">加班日</param>
        /// <returns>若為特殊員工，傳回true</returns>
        public (bool, double) IsSpecialStaff(string employeeNumber, DateTime date);

        /// <summary>
        /// 標註某員工及其對應之角色的所有未讀通知為已讀
        /// </summary>
        /// <param name="roles">員工角色</param>
        /// <returns></returns>
        public bool MarkDeliveredNotifications(List<Role> roles);

        /// <summary>
        /// 更新表單
        /// </summary>
        /// <param name="formID">表單名稱</param>
        /// <param name="dataSet">包含所有待更新資料表的 Data Set</param>
        /// <returns>成功為空值，失敗時為錯誤訊息 </returns>
        public string UpdateForm(string formID, DataSet dataSet);

    }
}
