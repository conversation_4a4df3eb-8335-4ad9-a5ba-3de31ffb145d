import { ref } from 'vue'
import { GET_B1CARDTYPES_URL } from '../api/appUrl'
import type { OvertimeCompensatoryType, OvertimeCompensatoryApiType } from '../api/appType'

/**
 * 加班類型
 * @returns 
 */
export function useOvertimeCompensatory() {
  const overtimeCompensatory = ref<Array<OvertimeCompensatoryType>>([])

  const onGetOvertimeCompensatory = async (signal: AbortSignal): Promise<void> => {
    const res: Response = await fetch(GET_B1CARDTYPES_URL, {
      method: 'GET',
      signal: signal
    })
    if (!res.ok) {
      throw new Error(res.status.toString())
    }

    const jsonData = await res.json()
    overtimeCompensatory.value = overtimeCompensatory.value.concat(
      jsonData.map((e: OvertimeCompensatoryApiType) => {
        return {
          id: e.Type,
          name: e.TypeName
        }
      })
    )
  }

  return { overtimeCompensatory, onGetOvertimeCompensatory }
}