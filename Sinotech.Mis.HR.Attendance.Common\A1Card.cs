﻿using System;
using System.Collections.Generic;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 正常工作卡
    /// </summary>
    public class A1Card : CardBase
    {

        /// <summary>
        /// Only for 比對，並非真正 EasyClone
        /// </summary>
        /// <returns></returns>
        public A1Card EasyClone()
        {
            A1Card card = new A1Card();
            card.A1_NN = A1_NN;
            card.A1_EMPNO = A1_EMPNO;
            card.CreatedTime = CreatedTime;
            card.FilledTime = FilledTime;
            card.A1_YYMM = A1_YYMM;
            card.Details = Details;
            return card;
        }

        /// <summary>
        /// 簡單比對，不嚴謹
        /// </summary>
        /// <param name="other"></param>
        /// <returns></returns>
        public bool EasyEquals(A1Card? other)
        {
            return other is not null &&
                   CreatedTime == other.CreatedTime &&
                   FilledTime == other.FilledTime &&
                   A1_EMPNO == other.A1_EMPNO &&
                   A1_YYMM == other.A1_YYMM &&
                   A1_NN == other.A1_NN &&
                   other.Details.Count == Details.Count;
        }

        /// <summary>
        /// 設定申請別/旬別名稱
        /// </summary>
        /// <returns></returns>
        public override void SetApplicationType()
        {
            string tenDays = "上旬";
            switch (A1_NN)
            {
                case '1':
                    tenDays = "上旬";
                    break;
                case '2':
                    tenDays = "中旬";
                    break;
                case '3':
                    tenDays = "下旬";
                    break;
            }
            ApplicationType = tenDays;
        }

#nullable enable

        /// <summary>
        /// YYYMMDD    結案核卡日期
        /// </summary>
        public DateTime? A1_ADate { get; set; }

#nullable enable
        /// <summary>
        /// YYYMMDD    結案核卡日期民國年月日
        /// </summary>
        public string? A1_AYYMMDD { get; set; }

        /// <summary>
        /// 員工編號
        /// </summary>
        public string A1_EMPNO { get; set; } = string.Empty;

        /// <summary>
        /// 旬卡別 1/2/3 代表 上/中/下
        /// </summary>
        public char A1_NN { get; set; } = '1';

        /// <summary>
        /// 正常卡表單單號
        /// </summary>
        /// <value>
        /// 來自 Form table 的 FormNo
        /// </value>
        public string A1_SHEETNO { get; set; } = string.Empty;

        /// <summary>
        /// 寫入資料來源
        /// </summary>
        /// <value>
        /// EasyFlow、Attendance (本系統)、SECINC (承辦人)
        /// </value>
        public string A1_SOURCE { get; set; } = "Attendance";

#nullable disable

        /// <summary>
        /// 簽核狀態 <br />
        /// </summary>
        /// <value>
        /// 1:未完成 2:同意 3:不同意 4:抽單
        /// </value>
        public int A1_STATUS { get; set; } = 1;

        /// <summary>
        /// 填卡日期
        /// </summary>
        public DateTime A1_WDate { get; set; }

        /// <summary>
        /// YYYMMDD    填卡日期民國年月日
        /// </summary>
        public string A1_WYYMMDD { get; set; } = string.Empty;

        /// <summary>
        /// 民國年月
        /// </summary>
        public string A1_YYMM { get; set; } = string.Empty;

        /// <summary>
        /// 正常工作卡細項
        /// </summary>
        public List<A1CardDetail> Details { get; set; } = new List<A1CardDetail>();

        /// <summary>
        /// 卡片類別名稱
        /// </summary>
        public override string Name { get; } = "A1Card";

    }

}
