﻿using Sinotech.Mis.HR.Attendance.DataAccess.Ado;
using Microsoft.Extensions.Configuration;
using Sinotech.Mis.Extensions.Configuration;
using Sinotech.Mis.HR.Attendance.BusinessLogic;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.DataAccess.Interfaces;
using Sinotech.Mis.HR.Attendance.Tests;
using Sinotech.Mis.Utilities.DataAccess.Ado;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using Xunit;
using static Antlr4.Runtime.Atn.SemanticContext;
using System.Linq;

namespace Sinotech.Mis.HR.Attendance.DataAccess.Ado.Tests
{
    [ExcludeFromCodeCoverage]
    public class AttendanceDaoTests
    {

        private readonly IAttendanceDao _attendanceDao;
        private readonly SinoSignBo _sinoSignBo;

        public AttendanceDaoTests(IAttendanceDao attendanceDao, SinoSignBo sinoSignBo)
        {
            _attendanceDao = attendanceDao;
            _sinoSignBo = sinoSignBo;
            IConfiguration configuration = new ConfigurationBuilder().
AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).Build();
            string ConnectionStringAttendance = configuration.GetSecuredConnectionString("Attendance");
            TestHelper.ClearData(ConnectionStringAttendance);
        }

        [Theory]
        [InlineData(1, false)]
        [InlineData(2, false)]
        [InlineData(3, false)]
        [InlineData(4, false)]
        [InlineData(5, false)]
        [InlineData(6, false)]
        [InlineData(7, false)]
        [InlineData(8, false)]
        [InlineData(9, false)]
        [InlineData(10, false)]
        [InlineData(11, false)]
        [InlineData(12, false)]
        [InlineData(13, false)]
        [InlineData(14, false)]
        [InlineData(15, false)]
        [InlineData(16, false)]
        [InlineData(17, false)]
        [InlineData(18, false)]
        [InlineData(19, false)]
        [InlineData(20, false)]
        public void DeliveredNotificationsTest(int id, bool expected)
        {
            bool result = _attendanceDao.DeliveredNotifications(id);
            Assert.Equal(expected, result);
        }

        [Theory]
        [InlineData("0178", true)]
        [InlineData("2195", true)]
        [InlineData("0281", true)]
        [InlineData("0390", true)]
        [InlineData("0217", true)]
        [InlineData("0270", true)]
        [InlineData("0273", true)]
        [InlineData("2320", true)]
        [InlineData("1055", true)]
        [InlineData("0931", true)]
        [InlineData("2037", true)]
        public void GetAllNotificationsTest(string empNo, bool isEmpty)
        {
            DataTable dataTable = _attendanceDao.GetAllNotifications(empNo);
            Assert.Equal(isEmpty, dataTable.Rows.Count == 0);
        }

        [Theory]
        [InlineData("2021-01-01", "2021-01-31", true)]
        [InlineData("2021-02-01", "2021-02-28", true)]
        [InlineData("2021-03-01", "2021-03-31", true)]
        [InlineData("2021-04-01", "2021-04-30", true)]
        [InlineData("2021-05-01", "2021-05-31", true)]
        [InlineData("2021-06-01", "2021-06-30", true)]
        [InlineData("2021-07-01", "2021-07-31", true)]
        [InlineData("2021-08-01", "2021-08-31", true)]
        [InlineData("2021-09-01", "2021-09-30", true)]
        [InlineData("2021-10-01", "2021-10-31", true)]
        [InlineData("2021-11-01", "2021-11-30", true)]
        [InlineData("2021-12-01", "2021-12-31", true)]
        [InlineData("2021-01-01", "2021-12-31", true)]
        public void GetAllSentBoxTest(DateTime startDate, DateTime endDate, bool isEmpty)
        {
            DataTable dataTable = _attendanceDao.GetAllSentBox(startDate, endDate);
            Assert.Equal(isEmpty, dataTable.Rows.Count == 0);
        }

        [Theory]
        [InlineData(1, "2021-01-01", "2021-01-31", false)]
        [InlineData(2, "2021-01-01", "2021-01-31", false)]
        [InlineData(3, "2021-01-01", "2021-01-31", false)]
        [InlineData(4, "2021-01-01", "2021-01-31", false)]
        [InlineData(5, "2021-01-01", "2021-01-31", false)]
        [InlineData(6, "2021-01-01", "2021-01-31", false)]
        [InlineData(7, "2021-01-01", "2021-01-31", false)]
        [InlineData(8, "2021-01-01", "2021-01-31", false)]
        [InlineData(9, "2021-01-01", "2021-01-31", false)]
        [InlineData(10, "2021-01-01", "2021-01-31", false)]
        [InlineData(11, "2021-01-01", "2021-01-31", false)]
        [InlineData(12, "2021-01-01", "2021-01-31", false)]
        [InlineData(13, "2021-01-01", "2021-01-31", false)]
        [InlineData(14, "2021-01-01", "2021-01-31", false)]
        [InlineData(15, "2021-01-01", "2021-01-31", false)]
        [InlineData(16, "2021-01-01", "2021-01-31", false)]
        [InlineData(17, "2021-01-01", "2021-01-31", false)]
        [InlineData(18, "2021-01-01", "2021-01-31", false)]
        [InlineData(19, "2021-01-01", "2021-01-31", false)]
        [InlineData(20, "2021-01-01", "2021-01-31", false)]
        public void GetDepartmentSentBox_Period_Test(int deptNo, DateTime startDate, DateTime endDate, bool hasData)
        {
            DataTable dataTable = _attendanceDao.GetDepartmentSentBox(deptNo, startDate, endDate);
            if (hasData)
            {
                Assert.NotEmpty(dataTable.Rows);
            }
            else
            {
                Assert.Empty(dataTable.Rows);
            }
        }

        [Theory]
        [InlineData(-1, "2021-01-01", "2021-01-31", false)]
        [InlineData(0, "2021-01-01", "2021-01-31", false)]
        [InlineData(1, "2021-01-01", "2021-01-31", false)]
        [InlineData(2, "2021-01-01", "2021-01-31", false)]
        [InlineData(3, "2021-01-01", "2021-01-31", false)]
        [InlineData(4, "2021-01-01", "2021-01-31", false)]
        [InlineData(5, "2021-01-01", "2021-01-31", false)]
        [InlineData(6, "2021-01-01", "2021-01-31", false)]
        [InlineData(7, "2021-01-01", "2021-01-31", false)]
        [InlineData(8, "2021-01-01", "2021-01-31", false)]
        [InlineData(9, "2021-01-01", "2021-01-31", false)]
        [InlineData(10, "2021-01-01", "2021-01-31", false)]
        [InlineData(11, "2021-01-01", "2021-01-31", false)]
        [InlineData(12, "2021-01-01", "2021-01-31", false)]
        [InlineData(13, "2021-01-01", "2021-01-31", false)]
        [InlineData(14, "2021-01-01", "2021-01-31", false)]
        [InlineData(15, "2021-01-01", "2021-01-31", false)]
        [InlineData(16, "2021-01-01", "2021-01-31", false)]
        [InlineData(17, "2021-01-01", "2021-01-31", false)]
        [InlineData(18, "2021-01-01", "2021-01-31", false)]
        [InlineData(19, "2021-01-01", "2021-01-31", false)]
        [InlineData(20, "2021-01-01", "2021-01-31", false)]
        public void GetDepartmentSentBoxByContentDateTest(int deptNo, DateTime startDate, DateTime endDate, bool hasData)
        {
            DataTable dataTable = _attendanceDao.GetDepartmentSentBoxByContentDate(deptNo, startDate, endDate);
            if (hasData)
            {
                Assert.NotEmpty(dataTable.Rows);
            }
            else
            {
                Assert.Empty(dataTable.Rows);
            }
        }

        [Theory]
        [InlineData(1, false)]
        [InlineData(2, false)]
        [InlineData(3, false)]
        [InlineData(4, false)]
        [InlineData(5, false)]
        [InlineData(6, false)]
        [InlineData(7, false)]
        [InlineData(8, false)]
        [InlineData(9, false)]
        [InlineData(10, false)]
        [InlineData(11, false)]
        [InlineData(12, false)]
        [InlineData(13, false)]
        [InlineData(14, false)]
        [InlineData(15, false)]
        [InlineData(16, false)]
        [InlineData(17, false)]
        [InlineData(18, false)]
        [InlineData(19, false)]
        [InlineData(20, false)]
        public void GetDepartmentSentBoxTest(int deptNo, bool hasData)
        {
            DataTable dataTable = _attendanceDao.GetDepartmentSentBox(deptNo);
            if (hasData)
            {
                Assert.NotEmpty(dataTable.Rows);
            }
            else
            {
                Assert.Empty(dataTable.Rows);
            }
        }

        [Theory]
        [InlineData(0, 2022, 1, false)]
        [InlineData(1, 2022, 1, false)]
        [InlineData(2, 2022, 1, false)]
        [InlineData(3, 2022, 1, false)]
        [InlineData(4, 2022, 1, false)]
        [InlineData(5, 2022, 1, false)]
        [InlineData(6, 2022, 1, false)]
        [InlineData(7, 2022, 1, false)]
        [InlineData(8, 2022, 1, false)]
        [InlineData(9, 2022, 1, false)]
        [InlineData(10, 2022, 1, false)]
        [InlineData(11, 2022, 1, false)]
        [InlineData(12, 2022, 1, false)]
        [InlineData(13, 2022, 1, false)]
        [InlineData(14, 2022, 1, false)]
        [InlineData(15, 2022, 1, false)]
        [InlineData(16, 2022, 1, false)]
        [InlineData(17, 2022, 1, false)]
        [InlineData(18, 2022, 1, false)]
        [InlineData(19, 2022, 1, false)]
        [InlineData(20, 2022, 1, false)]
        public void GetDepartmentSentBoxYearMonthTest(int deptNo, int year, int month, bool hasData)
        {
            DataTable dataTable = _attendanceDao.GetDepartmentSentBoxYearMonth(deptNo, year, month);
            if (hasData)
            {
                Assert.NotEmpty(dataTable.Rows);
            }
            else
            {
                Assert.Empty(dataTable.Rows);
            }
        }

        [Theory]
        [InlineData("2022-01-03", "0395", 0.0)]
        [InlineData("2022-01-04", "0395", 0.0)]
        [InlineData("2022-01-05", "0395", 0.0)]
        [InlineData("2022-01-06", "0395", 0.0)]
        [InlineData("2022-01-07", "0395", 0.0)]
        [InlineData("2022-01-08", "0395", 0.0)]
        [InlineData("2022-01-09", "0395", 0.0)]
        [InlineData("2022-01-10", "0395", 0.0)]
        [InlineData("2022-01-11", "0395", 0.0)]
        [InlineData("2022-01-12", "0395", 0.0)]
        [InlineData("2022-01-03", "0349", 0.0)]
        [InlineData("2022-01-04", "0349", 0.0)]
        [InlineData("2022-01-05", "0349", 0.0)]
        [InlineData("2022-01-06", "0349", 0.0)]
        [InlineData("2022-01-07", "0349", 0.0)]
        [InlineData("2022-01-08", "0349", 0.0)]
        [InlineData("2022-01-09", "0349", 0.0)]
        [InlineData("2022-01-10", "0349", 0.0)]
        [InlineData("2022-01-11", "0349", 0.0)]
        [InlineData("2022-01-12", "0349", 0.0)]
        public void GetEmployeeWeightedCompensatoryOvertimeHoursTest(DateTime theDate, string empNo, double expected)
        {
            double actual = _attendanceDao.GetEmployeeWeightedCompensatoryOvertimeHours(theDate, empNo);
            Assert.Equal(expected, actual);
        }

        [Theory]
        [InlineData("2021-01-01", "2021-01-31", true)]
        [InlineData("2021-02-01", "2021-02-28", true)]
        [InlineData("2021-03-01", "2021-03-31", true)]
        [InlineData("2021-04-01", "2021-04-30", true)]
        [InlineData("2021-05-01", "2021-05-31", true)]
        [InlineData("2021-06-01", "2021-06-30", true)]
        [InlineData("2021-07-01", "2021-07-31", true)]
        [InlineData("2021-08-01", "2021-08-31", true)]
        [InlineData("2021-09-01", "2021-09-30", true)]
        [InlineData("2021-10-01", "2021-10-31", true)]
        [InlineData("2021-11-01", "2021-11-30", true)]
        [InlineData("2021-12-01", "2021-12-31", true)]
        [InlineData("2021-01-01", "2021-12-31", true)]
        public void GetFormsByContentDateTest(DateTime startDate, DateTime endDate, bool isEmpty)
        {
            DataTable dt = _attendanceDao.GetFormsByContentDate(startDate, endDate);
            Assert.Equal(isEmpty, dt.Rows.Count == 0);
        }

        [Theory]
        [InlineData(FormStatus.Deny, 0)]
        [InlineData(FormStatus.Agree, 0)]
        [InlineData(FormStatus.Withdraw, 0)]
        [InlineData(FormStatus.Processing, 0)]
        public void GetFormsByStatusTest(FormStatus status, int expected)
        {
            DataTable dt = _attendanceDao.GetFormsByStatus(status);
            Assert.Equal(expected, dt.Rows.Count);
        }

        [Theory]
        [InlineData("A1Card", "110", "旬110000001")]
        [InlineData("B1CardApp", "110", "申110000001")]
        [InlineData("B1Card", "110", "加110000001")]
        [InlineData("C1Card", "110", "假110000001")]
        public void GetNewFormNoTest(string formId, string chineseYear, string expected)
        {
            string actual = _attendanceDao.GetNewFormNo(formId, chineseYear);
            Assert.Equal(expected, actual);
        }

        [Theory]
        [InlineData("0395", "0349", 0, new int[] { }, new int[] { }, "2022-01-03", "2022-01-12", true)]
        [InlineData("0395", "0349", 0, new int[] { }, new int[] { }, "2022-01-03", "2022-12-12", true)]
        [InlineData("0000", "0349", 0, new int[] { }, new int[] { }, "2022-01-03", "2022-01-12", true)]
        [InlineData("0349", "0395", 0, new int[] { }, new int[] { }, "2022-01-03", "2022-01-12", true)]
        [InlineData("0395", "0349", 0, new int[] { }, new int[] { }, "2022-02-01", "2022-03-01", true)]
        [InlineData("0395", "0349", 0, new int[] { }, new int[] { }, "2022-08-03", "2022-12-25", true)]
        public void GetNotifyFormCardsTest(string empNo, string notifier, int deptNo, int[] isRead, int[] formStatus, DateTime startDate, DateTime endDate, bool isEmpty)
        {
            List<Role> roles = _sinoSignBo.GetUserRoles(notifier);
            DataTable dt = _attendanceDao.GetNotifyFormCards(empNo, deptNo, roles, isRead.ToList<int>(), formStatus.ToList<int>(), startDate, endDate);
            Assert.Equal(isEmpty, dt.Rows.Count == 0);
        }

        [Theory]
        [InlineData(' ', 1.0)]
        [InlineData('0', 1.0)]
        [InlineData('1', 1.0)]
        [InlineData('2', 4 / 3.0)]
        [InlineData('3', 5.0 / 3)]
        [InlineData('4', 0.0)]
        [InlineData('5', 1.0)]
        [InlineData('6', 8.0 / 3)]
        [InlineData('7', 1.0)]

        public void GetRateFromB1HoursTest(char charRate, double expected)
        {
            double result = AttendanceDao.GetRateFromB1Hours(charRate);
            Assert.Equal(expected, result);
        }


        [Theory]
        [InlineData("0395", "2021-01-01", "2021-01-31", "RP19553", 0, true)]
        [InlineData("0395", "2021-02-01", "2021-02-28", "RP19553", 0, true)]
        [InlineData("0395", "2021-03-01", "2021-03-31", "RP19553", 0, true)]
        [InlineData("0395", "2021-04-01", "2021-04-30", "RP19553", 0, true)]
        [InlineData("0395", "2021-05-01", "2021-05-31", "RP19553", 0, true)]
        [InlineData("0395", "2021-06-01", "2021-06-30", "RP19553", 0, true)]
        [InlineData("0395", "2021-07-01", "2021-07-31", "RP19553", 0, true)]
        [InlineData("0395", "2021-08-01", "2021-08-31", "RP19553", 0, true)]
        [InlineData("0395", "2021-09-01", "2021-09-30", "RP19553", 0, true)]
        [InlineData("0395", "2021-10-01", "2021-10-31", "RP19553", 0, true)]
        [InlineData("0395", "2021-11-01", "2021-11-30", "RP19553", 0, true)]
        [InlineData("0395", "2021-12-01", "2021-12-31", "RP19553", 0, true)]
        [InlineData("0395", "2021-01-01", "2021-12-31", "RP19553", 0, true)]
        [InlineData("2268", "2021-01-01", "2021-01-31", "RP19553", 0, true)]
        [InlineData("2268", "2021-02-01", "2021-02-28", "RP19553", 0, true)]
        [InlineData("2268", "2021-03-01", "2021-03-31", "RP19553", 0, true)]
        [InlineData("2268", "2021-04-01", "2021-04-30", "RP19553", 0, true)]
        [InlineData("2268", "2021-05-01", "2021-05-31", "RP19553", 0, true)]
        [InlineData("2268", "2021-06-01", "2021-06-30", "RP19553", 0, true)]
        [InlineData("2268", "2021-07-01", "2021-07-31", "RP19553", 0, true)]
        [InlineData("2268", "2021-08-01", "2021-08-31", "RP19553", 0, true)]
        [InlineData("2268", "2021-09-01", "2021-09-30", "RP19553", 0, true)]
        [InlineData("2268", "2021-10-01", "2021-10-31", "RP19553", 0, true)]
        [InlineData("2268", "2021-11-01", "2021-11-30", "RP19553", 0, true)]
        [InlineData("2268", "2021-12-01", "2021-12-31", "RP19553", 0, true)]
        [InlineData("2268", "2021-01-01", "2021-12-31", "RP19553", 0, true)]
        public void GetSentBox_Project_Status_Test(
            string empNo, DateTime startDate, DateTime endDate,
            string projNo, int status, bool isEmpty)
        {
            DataTable dt = _attendanceDao.GetSentBox(empNo, startDate, endDate, projNo, status);
            Assert.Equal(isEmpty, dt.Rows.Count == 0);
        }

        [Theory]
        [InlineData("0395", "2021-01-01", "2021-01-31", "RP19553", true)]
        [InlineData("0395", "2021-02-01", "2021-02-28", "RP19553", true)]
        [InlineData("0395", "2021-03-01", "2021-03-31", "RP19553", true)]
        [InlineData("0395", "2021-04-01", "2021-04-30", "RP19553", true)]
        [InlineData("0395", "2021-05-01", "2021-05-31", "RP19553", true)]
        [InlineData("0395", "2021-06-01", "2021-06-30", "RP19553", true)]
        [InlineData("0395", "2021-07-01", "2021-07-31", "RP19553", true)]
        [InlineData("0395", "2021-08-01", "2021-08-31", "RP19553", true)]
        [InlineData("0395", "2021-09-01", "2021-09-30", "RP19553", true)]
        [InlineData("0395", "2021-10-01", "2021-10-31", "RP19553", true)]
        [InlineData("0395", "2021-11-01", "2021-11-30", "RP19553", true)]
        [InlineData("0395", "2021-12-01", "2021-12-31", "RP19553", true)]
        [InlineData("0395", "2021-01-01", "2021-12-31", "RP19553", true)]
        [InlineData("2268", "2021-01-01", "2021-01-31", "RP19553", true)]
        [InlineData("2268", "2021-02-01", "2021-02-28", "RP19553", true)]
        [InlineData("2268", "2021-03-01", "2021-03-31", "RP19553", true)]
        [InlineData("2268", "2021-04-01", "2021-04-30", "RP19553", true)]
        [InlineData("2268", "2021-05-01", "2021-05-31", "RP19553", true)]
        [InlineData("2268", "2021-06-01", "2021-06-30", "RP19553", true)]
        [InlineData("2268", "2021-07-01", "2021-07-31", "RP19553", true)]
        [InlineData("2268", "2021-08-01", "2021-08-31", "RP19553", true)]
        [InlineData("2268", "2021-09-01", "2021-09-30", "RP19553", true)]
        [InlineData("2268", "2021-10-01", "2021-10-31", "RP19553", true)]
        [InlineData("2268", "2021-11-01", "2021-11-30", "RP19553", true)]
        [InlineData("2268", "2021-12-01", "2021-12-31", "RP19553", true)]
        [InlineData("2268", "2021-01-01", "2021-12-31", "RP19553", true)]
        public void GetSentBox_Project_Test(string empNo, DateTime startDate, DateTime endDate, string projNo, bool isEmpty)
        {
            DataTable dt = _attendanceDao.GetSentBox(empNo, startDate, endDate, projNo);
            Assert.Equal(isEmpty, dt.Rows.Count == 0);
        }

        [Theory]
        [InlineData("0395", "2021-01-01", "2021-01-31", 0, true)]
        [InlineData("0395", "2021-02-01", "2021-02-28", 0, true)]
        [InlineData("0395", "2021-03-01", "2021-03-31", 0, true)]
        [InlineData("0395", "2021-04-01", "2021-04-30", 0, true)]
        [InlineData("0395", "2021-05-01", "2021-05-31", 0, true)]
        [InlineData("0395", "2021-06-01", "2021-06-30", 0, true)]
        [InlineData("0395", "2021-07-01", "2021-07-31", 0, true)]
        [InlineData("0395", "2021-08-01", "2021-08-31", 0, true)]
        [InlineData("0395", "2021-09-01", "2021-09-30", 0, true)]
        [InlineData("0395", "2021-10-01", "2021-10-31", 0, true)]
        [InlineData("0395", "2021-11-01", "2021-11-30", 0, true)]
        [InlineData("0395", "2021-12-01", "2021-12-31", 0, true)]
        [InlineData("0395", "2021-01-01", "2021-12-31", 0, true)]
        [InlineData("2268", "2021-01-01", "2021-01-31", 0, true)]
        [InlineData("2268", "2021-02-01", "2021-02-28", 0, true)]
        [InlineData("2268", "2021-03-01", "2021-03-31", 0, true)]
        [InlineData("2268", "2021-04-01", "2021-04-30", 0, true)]
        [InlineData("2268", "2021-05-01", "2021-05-31", 0, true)]
        [InlineData("2268", "2021-06-01", "2021-06-30", 0, true)]
        [InlineData("2268", "2021-07-01", "2021-07-31", 0, true)]
        [InlineData("2268", "2021-08-01", "2021-08-31", 0, true)]
        [InlineData("2268", "2021-09-01", "2021-09-30", 0, true)]
        [InlineData("2268", "2021-10-01", "2021-10-31", 0, true)]
        [InlineData("2268", "2021-11-01", "2021-11-30", 0, true)]
        [InlineData("2268", "2021-12-01", "2021-12-31", 0, true)]
        [InlineData("2268", "2021-01-01", "2021-12-31", 0, true)]
        public void GetSentBox_Status_Test(string empNo, DateTime startDate, DateTime endDate, int status, bool isEmpty)
        {
            DataTable dt = _attendanceDao.GetSentBox(empNo, startDate, endDate, status);
            Assert.Equal(isEmpty, dt.Rows.Count == 0);
        }

        [Theory]
        [InlineData("0395", "2021-01-01", "2021-01-31", "RP19553", true)]
        [InlineData("0395", "2021-02-01", "2021-02-28", "RP19553", true)]
        [InlineData("0395", "2021-03-01", "2021-03-31", "RP19553", true)]
        [InlineData("0395", "2021-04-01", "2021-04-30", "RP19553", true)]
        [InlineData("0395", "2021-05-01", "2021-05-31", "RP19553", true)]
        [InlineData("0395", "2021-06-01", "2021-06-30", "RP19553", true)]
        [InlineData("0395", "2021-07-01", "2021-07-31", "RP19553", true)]
        [InlineData("0395", "2021-08-01", "2021-08-31", "RP19553", true)]
        [InlineData("0395", "2021-09-01", "2021-09-30", "RP19553", true)]
        [InlineData("0395", "2021-10-01", "2021-10-31", "RP19553", true)]
        [InlineData("0395", "2021-11-01", "2021-11-30", "RP19553", true)]
        [InlineData("0395", "2021-12-01", "2021-12-31", "RP19553", true)]
        [InlineData("0395", "2021-01-01", "2021-12-31", "RP19553", true)]
        [InlineData("2268", "2021-01-01", "2021-01-31", "RP19553", true)]
        [InlineData("2268", "2021-02-01", "2021-02-28", "RP19553", true)]
        [InlineData("2268", "2021-03-01", "2021-03-31", "RP19553", true)]
        [InlineData("2268", "2021-04-01", "2021-04-30", "RP19553", true)]
        [InlineData("2268", "2021-05-01", "2021-05-31", "RP19553", true)]
        [InlineData("2268", "2021-06-01", "2021-06-30", "RP19553", true)]
        [InlineData("2268", "2021-07-01", "2021-07-31", "RP19553", true)]
        [InlineData("2268", "2021-08-01", "2021-08-31", "RP19553", true)]
        [InlineData("2268", "2021-09-01", "2021-09-30", "RP19553", true)]
        [InlineData("2268", "2021-10-01", "2021-10-31", "RP19553", true)]
        [InlineData("2268", "2021-11-01", "2021-11-30", "RP19553", true)]
        [InlineData("2268", "2021-12-01", "2021-12-31", "RP19553", true)]
        [InlineData("2268", "2021-01-01", "2021-12-31", "RP19553", true)]
        public void GetSentBoxByContentDate_PrjNoTest(string empNo, DateTime startDate, DateTime endDate, string projNo, bool isEmpty)
        {
            DataTable dt = _attendanceDao.GetSentBoxByContentDate(empNo, startDate, endDate, projNo);
            Assert.Equal(isEmpty, dt.Rows.Count == 0);
        }

        [Theory]
        [InlineData("0395", "2021-01-01", "2021-01-31", true)]
        [InlineData("0395", "2021-02-01", "2021-02-28", true)]
        [InlineData("0395", "2021-03-01", "2021-03-31", true)]
        [InlineData("0395", "2021-04-01", "2021-04-30", true)]
        [InlineData("0395", "2021-05-01", "2021-05-31", true)]
        [InlineData("0395", "2021-06-01", "2021-06-30", true)]
        [InlineData("0395", "2021-07-01", "2021-07-31", true)]
        [InlineData("0395", "2021-08-01", "2021-08-31", true)]
        [InlineData("0395", "2021-09-01", "2021-09-30", true)]
        [InlineData("0395", "2021-10-01", "2021-10-31", true)]
        [InlineData("0395", "2021-11-01", "2021-11-30", true)]
        [InlineData("0395", "2021-12-01", "2021-12-31", true)]
        [InlineData("0395", "2021-01-01", "2021-12-31", true)]
        [InlineData("2268", "2021-01-01", "2021-01-31", true)]
        [InlineData("2268", "2021-02-01", "2021-02-28", true)]
        [InlineData("2268", "2021-03-01", "2021-03-31", true)]
        [InlineData("2268", "2021-04-01", "2021-04-30", true)]
        [InlineData("2268", "2021-05-01", "2021-05-31", true)]
        [InlineData("2268", "2021-06-01", "2021-06-30", true)]
        [InlineData("2268", "2021-07-01", "2021-07-31", true)]
        [InlineData("2268", "2021-08-01", "2021-08-31", true)]
        [InlineData("2268", "2021-09-01", "2021-09-30", true)]
        [InlineData("2268", "2021-10-01", "2021-10-31", true)]
        [InlineData("2268", "2021-11-01", "2021-11-30", true)]
        [InlineData("2268", "2021-12-01", "2021-12-31", true)]
        [InlineData("2268", "2021-01-01", "2021-12-31", true)]
        public void GetSentBoxByContentDateTest(string empNo, DateTime startDate, DateTime endDate, bool isEmpty)
        {
            DataTable dt = _attendanceDao.GetSentBoxByContentDate(empNo, startDate, endDate);
            Assert.Equal(isEmpty, dt.Rows.Count == 0);
        }

        [Theory]
        [InlineData("0395", "2021-01-01", "2021-01-31", true)]
        [InlineData("0395", "2021-02-01", "2021-02-28", true)]
        [InlineData("0395", "2021-03-01", "2021-03-31", true)]
        [InlineData("0395", "2021-04-01", "2021-04-30", true)]
        [InlineData("0395", "2021-05-01", "2021-05-31", true)]
        [InlineData("0395", "2021-06-01", "2021-06-30", true)]
        [InlineData("0395", "2021-07-01", "2021-07-31", true)]
        [InlineData("0395", "2021-08-01", "2021-08-31", true)]
        [InlineData("0395", "2021-09-01", "2021-09-30", true)]
        [InlineData("0395", "2021-10-01", "2021-10-31", true)]
        [InlineData("0395", "2021-11-01", "2021-11-30", true)]
        [InlineData("0395", "2021-12-01", "2021-12-31", true)]
        [InlineData("0395", "2021-01-01", "2021-12-31", true)]
        [InlineData("2268", "2021-01-01", "2021-01-31", true)]
        [InlineData("2268", "2021-02-01", "2021-02-28", true)]
        [InlineData("2268", "2021-03-01", "2021-03-31", true)]
        [InlineData("2268", "2021-04-01", "2021-04-30", true)]
        [InlineData("2268", "2021-05-01", "2021-05-31", true)]
        [InlineData("2268", "2021-06-01", "2021-06-30", true)]
        [InlineData("2268", "2021-07-01", "2021-07-31", true)]
        [InlineData("2268", "2021-08-01", "2021-08-31", true)]
        [InlineData("2268", "2021-09-01", "2021-09-30", true)]
        [InlineData("2268", "2021-10-01", "2021-10-31", true)]
        [InlineData("2268", "2021-11-01", "2021-11-30", true)]
        [InlineData("2268", "2021-12-01", "2021-12-31", true)]
        [InlineData("2268", "2021-01-01", "2021-12-31", true)]
        public void GetSentBoxTest(string empNo, DateTime startDate, DateTime endDate, bool isEmpty)
        {
            DataTable dt = _attendanceDao.GetSentBox(empNo, startDate, endDate);
            Assert.Equal(isEmpty, dt.Rows.Count == 0);
        }

        [Theory]
        [InlineData("0395", 1, 0)]
        [InlineData("0395", 2, 0)]
        [InlineData("0395", 3, 0)]
        [InlineData("0395", 4, 0)]
        [InlineData("0395", 5, 0)]
        [InlineData("0395", 6, 0)]
        [InlineData("0395", 10, 0)]
        [InlineData("2268", 10, 0)]
        [InlineData("2295", 10, 0)]
        public void GetTopApproveCommentsTest(string empNo, int count, int expected)
        {
            DataTable dt = _attendanceDao.GetTopApproveComments(empNo, count);
            Assert.Equal(expected, dt.Rows.Count);
        }

        /// <summary>
        /// 測試累積加權倍率時數計算
        /// </summary>
        /// <param name="rate"></param>
        /// <param name="hours"></param>
        /// <param name="expected"></param>
        [Theory]
        [InlineData('2', 1, 1.3333333333333)]
        [InlineData('2', 2, 2.6666666666666)]
        [InlineData('2', 3, 4.0)]
        [InlineData('2', 4, 5.3333333333333)]
        [InlineData('2', 5, 6.6666666666666)]
        [InlineData('2', 22, 29.3333333333333)] // 若用 1.33計算則會變成29.26，誤差較大，故須用4.0/3
        [InlineData('2', 23, 30.6666666666666)] // 若用 1.33計算則會變成30.59，誤差較大，故須用4.0/3
        public void GetWeightedHoursFromDataTableTest(char rate, int hours, double expected)
        {
            double weightedHours = 0.0;
            DataTable dt = new DataTable();
            dt.Columns.Add("B1_RATE", typeof(string));
            dt.Columns.Add("B1_HOURS", typeof(byte));

            for (int i = 0; i < hours; i++)
            {
                DataRow dr = dt.NewRow();
                dr["B1_RATE"] = rate;
                dr["B1_HOURS"] = (byte)1;
                dt.Rows.Add(dr);
            }

            foreach (DataRow dr in dt.Rows)
            {
                char charRate = ((string)dr["B1_RATE"])[0];
                double dRate = AttendanceDao.GetRateFromB1Hours(charRate);
                int thisHours = (byte)dr["B1_HOURS"];
                weightedHours += dRate * thisHours;
            }

            double difference = Math.Abs(expected - weightedHours);
            Assert.True(difference < 0.0000001);
            hours = 0;
            weightedHours = AttendanceDao.GetWeightedHoursFromDataTable(hours, dt);
            difference = Math.Abs(expected - weightedHours);
            Assert.True(difference < 0.0000001);
        }

        [Theory]
        [InlineData("5fadb665-cc43-4fc3-8061-764c48885ee7", 1, false)]
        [InlineData("5fadb665-cc43-4fc3-8061-764c48885ee7", 2, false)]
        [InlineData("5fadb665-cc43-4fc3-8061-764c48885ee7", 3, false)]
        [InlineData("5fadb665-cc43-4fc3-8061-764c48885ee7", 4, false)]
        [InlineData("5fadb665-cc43-4fc3-8061-764c48885ee7", 5, false)]
        [InlineData("5fadb665-cc43-4fc3-8061-764c48885ee7", 6, false)]
        [InlineData("5fadb665-cc43-4fc3-8061-764c48885ee7", 7, false)]
        [InlineData("5fadb665-cc43-4fc3-8061-764c48885ee7", 8, false)]
        [InlineData("5fadb665-cc43-4fc3-8061-764c48885ee7", 9, false)]
        [InlineData("5fadb665-cc43-4fc3-8061-764c48885ee7", 10, false)]
        public void IsNotificationExistTest(Guid formUID, int notifyId, bool expected)
        {
            bool actual = _attendanceDao.IsNotificationExist(formUID, notifyId);
            Assert.Equal(expected, actual);
        }

        [Theory]
        [InlineData("0395", "5fadb665-cc43-4fc3-8061-764c48885ee7", 1, false)]
        [InlineData("0395", "5fadb665-cc43-4fc3-8061-764c48885ee7", 2, false)]
        [InlineData("0395", "5fadb665-cc43-4fc3-8061-764c48885ee7", 3, false)]
        [InlineData("0395", "5fadb665-cc43-4fc3-8061-764c48885ee7", 4, false)]
        [InlineData("0395", "5fadb665-cc43-4fc3-8061-764c48885ee7", 5, false)]
        [InlineData("0395", "5fadb665-cc43-4fc3-8061-764c48885ee7", 6, false)]
        [InlineData("0395", "5fadb665-cc43-4fc3-8061-764c48885ee7", 7, false)]
        [InlineData("0395", "5fadb665-cc43-4fc3-8061-764c48885ee7", 8, false)]
        [InlineData("0395", "5fadb665-cc43-4fc3-8061-764c48885ee7", 9, false)]
        [InlineData("0395", "5fadb665-cc43-4fc3-8061-764c48885ee7", 10, false)]
        public void IsNotificationExist_notifyId_Test(string empNo, Guid formUID, int notifyId, bool expected)
        {
            List<Role> roles = _sinoSignBo.GetUserRoles(empNo);
            bool actual = _attendanceDao.IsNotificationExist(roles, formUID, notifyId);
            Assert.Equal(expected, actual);
        }

        [Theory]
        [InlineData("0000", false)]
        [InlineData("0305", false)]
        [InlineData("0395", false)]
        [InlineData("0349", false)]
        [InlineData("2268", false)]
        [InlineData("2295", false)]
        public void MarkDeliveredNotificationsTest(string empNo, bool expected)
        {
            List<Role> roles = _sinoSignBo.GetUserRoles(empNo);
            bool actual = _attendanceDao.MarkDeliveredNotifications(roles);
            Assert.Equal(expected, actual);
        }

    }
}