﻿using System.Collections.Generic;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 只有卡的List
    /// </summary>
    public class CardsList
    {
        /// <summary>
        /// 卡
        /// </summary>
        public List<CardBase> Cards { get; set; } = new List<CardBase>();

        /// <summary>
        /// 表單編號  
        /// A1CARD：正常工作卡
        /// B1CARDAPP：加班申請卡
        /// B1CARD：加班卡
        /// C1CARD：請假卡
        /// </summary>
        public string FormID { get; set; } = string.Empty;

        /// <summary>
        /// 表單名稱
        /// </summary>
        public string FormName { get; set; } = string.Empty;
    }
}
