<template>
  <h6 class="my-2 fs-2 text-center">
    <i class="bi bi-cloud-sun me-1" />
    <span>{{ FORM_ID.C1Card }}</span>
  </h6>
  <div class="container px-0 text-center">
    <div class="border border-dark-subtle border-2 mx-2 mx-sm-0">
      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>申</span>
              <span class="mx-1">請</span>
              <span>人</span>
            </div>
          </div>
        </div>
        <div class="col-9 col-md-10 py-2 text-start">
          <div class="row">
            <div class="col col-sm-5 col-md-4 col-lg-3 pe-0 py-0">
              <ChooseUser
                :modelValue="{
                  userId: employee.userId,
                  userName: employee.userName,
                  deptNo: employee.deptNo,
                  deptSName: employee.deptSName
                }" 
                :employeeData="applyEmployeeData"
                :filter="onSearchEmployeeData"
                :clearable="false"
                :mode="'apply'"
                :disabled="submitted === true"
                @update:modelValue="onChangeEmployee"
              />
            </div>
            <div class="col-auto">
              <template v-if="userStore.userId !== employee?.userId">
                <span class="badge rounded-pill bg-warning">代填</span>
              </template>
            </div>
          </div>
        </div>
      </div>

      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>所屬部門</span>
            </div>
          </div>
        </div>
        <div class="col-9 col-md-10 py-2 d-flex align-items-center">
          {{ employee.deptSName }}
        </div>
      </div>

      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span class="mx-1">假</span>
              <span class="mx-1">別</span>
            </div>
          </div>
        </div>
        <div class="col-9 col-md-10 py-2 text-start">
          <template v-if="leaveKinds.length > 0">
            <div class="row">
              <div class="col-auto">
                <select
                  class="form-select"
                  :style="{ 'min-width': '5rem' }"
                  :value="currentLeaveKind?.number"
                  :disabled="submitted === true"
                  @input="onChangeLeaveKind"
                >
                  <template
                    v-for="(value, index) in leaveKinds"
                    :key="index"
                  >
                    <option :value="value.number">
                      {{ value.name }}
                    </option>
                  </template>
                </select>
              </div>
              
              <div
                v-if="currentLeaveKind?.leaveKindExplanation && currentLeaveKind?.leaveKindExplanation.length > 0"
                class="col-12 text-danger"
              >
                <small>{{ currentLeaveKind.leaveKindExplanation }}</small>
              </div>
            </div>
          </template>
        </div>
      </div>

      <div
        v-if="currentLeaveKind != undefined && currentLeaveKind?.detail.length > 0"
        class="row border-bottom border-dark-subtle mx-0"
      >
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>假別細項</span>
            </div>
          </div>
        </div>
        <div class="col-9 col-md-10 py-2">
          <template v-if="currentLeaveKind?.detail.length > 0">
            <select
              class="form-select"
              :style="{ 'min-width': '5rem' }"
              :value="currentLeaveKindDetail?.leaveSubNumber"
              :disabled="submitted === true"
              @input="onChangeLeaveKindDetail"
            >
              <template
                v-for="(value, index) in currentLeaveKind.detail"
                :key="index"
              >
                <option :value="value.leaveSubNumber">
                  {{ value.leaveSubName }}
                </option>
              </template>
            </select>
          </template>
        </div>
      </div>

      <template v-if="(currentLeaveKind?.isEvent === true) && (eventCardData.length > 0)">
        <div class="row border-bottom border-dark-subtle mx-0">
          <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card">
            <div class="d-table h-100 w-100">
              <div class="d-table-cell align-middle">
                <span>相關卡號</span>
              </div>
            </div>
          </div>
          <div class="col-9 col-md-10 py-2 text-start">
            <div class="form-check">
              <input
                id="eventCardExisted"
                type="radio"
                class="form-check-input"
                value="true"
                :checked="eventCardExisted === true"
                :disabled="(submitted === true) || (eventCardData.find((e: any) => e.remain > 0) === undefined)"
                @change="onChangeEventCardExisted"
              >
              <label
                class="form-check-label"
                for="eventCardExisted"
              >
                <span>有</span>
                <small class="text-secondary">(事件第二次起填報時請選取相關卡號)</small>
              </label>
            </div>
            <div class="form-check">
              <input
                id="eventCardInexisted"
                type="radio"
                class="form-check-input"
                value="false"
                :checked="eventCardExisted === false"
                :disabled="submitted === true"
                @change="onChangeEventCardExisted"
              >
              <label
                class="form-check-label"
                for="eventCardInexisted"
              >
                <span>無</span>
                <small class="text-secondary">(事件第一次填報時請直接輸入事件發生日)</small>
              </label>
            </div>

            <div class="table-responsive">
              <table class="table table-sm table-bordered text-center mb-0">
                <caption class="d-none">
                  <span>相關卡號</span>
                </caption>
                <thead class="table-light align-middle">
                  <tr>
                    <th :style="{ width: '10%' }" />
                    <th :style="{ width: '20%' }">
                      <span>請假卡號</span>
                    </th>
                    <th :style="{ width: '30%' }">
                      <span>事件發生日</span>
                    </th>
                    <th :style="{ width: '30%' }">
                      <span>請假期限</span>
                    </th>
                    <th :style="{ width: '10%' }">
                      <span>剩餘可休</span>
                    </th>
                  </tr>
                </thead>
                <tbody class="align-middle">
                  <tr
                    v-for="(cardData, cardDataIndex) in eventCardData"
                    :key="cardDataIndex"
                    :class="[
                      cardData.remain === 0 ? 'text-muted' : '',
                      (eventCardExisted === false || cardData.remain === 0) ? 'table-secondary' : ''
                    ]"
                  >
                    <td>
                      <input
                        :id="'cardData' + cardDataIndex"
                        :class="[
                          'form-check-input me-1'
                        ]"
                        type="radio"
                        :value="cardData.formUID"
                        :checked="cardData.formUID === eventCardUID"
                        :disabled="(submitted === true) || ((eventCardExisted === false) ? true : (cardData.remain === 0))"
                        @change="onChangeEventCardUID(cardDataIndex.toString(), cardData.formUID ?? '', cardData.formNo)"
                      >
                    </td>
                    <td>
                      <template v-if="!cardData.formUID">
                        {{ cardData.formNo }}
                      </template>
                      <template v-else>
                        <router-link
                          v-slot="{ href }"
                          :to="{
                            name: cardData.formID + 'Sent',
                            params: { formUID: cardData.formUID }
                          }"
                          custom
                        >
                          <a
                            class
                            role="button"
                            tabindex="0"
                            @keydown.enter="onKeydownEditLink(href)"
                            @keydown.space="onKeydownEditLink(href)"
                            @click="onClickEditLink(href)"
                          >
                            {{ cardData.formNo }}
                          </a>
                        </router-link>
                      </template>
                    </td>
                    <td>
                      <span
                        :class="[
                          eventCardExisted === false ? 'opacity-50' : ''
                        ]"
                      >
                        {{ cardData.date ? dateToRocString(cardData.date, false) : '' }}
                      </span>
                    </td>
                    <td>
                      <span
                        :class="[
                          eventCardExisted === false ? 'opacity-50' : ''
                        ]"
                      >
                        <template v-if="cardData.periodStart && cardData.periodEnd">
                          {{ dateToRocString(cardData.periodStart, false) + ' ~ ' + dateToRocString(cardData.periodEnd, false) }}
                        </template>
                      </span>
                    </td>
                    <td>
                      <span 
                        :class="[
                          eventCardExisted === false ? 'opacity-50' : ''
                        ]"
                      >
                        {{ cardData.remain + cardData.unitName }}
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
              <template v-if="eventCardUID === '' && eventCardExisted === true">
                <small class="text-secondary">請選取同一事件的請假卡號為相關卡號</small>
              </template>
            </div>
          </div>
        </div>
      </template>

      <template v-if="(currentLeaveKind?.isEvent === true) || (currentLeaveKind?.number === 7 || currentLeaveKind?.number === 9)">
        <div class="row border-bottom border-dark-subtle mx-0">
          <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card">
            <div class="d-table h-100 w-100">
              <div class="d-table-cell align-middle">
                <span>事件發生日</span>
              </div>
            </div>
          </div>
          <div class="col-9 col-md-10 py-2 text-start">
            <div class="row">
              <div class="col-auto">
                <RocCalendarSelect
                  :inputClass="{
                    'form-control': ((currentLeaveKind?.detail.length > 0 && currentLeaveKindDetail === null) || (eventCardExisted === true) || (submitted === true)),
                    'w-auto': true
                  }"
                  :modelValue="eventTime"
                  :disabled="(currentLeaveKind?.detail.length > 0 && currentLeaveKindDetail === null) || (eventCardExisted === true) || (submitted === true)"
                  @click="onClickDate(eventTime)"
                  @update:modelValue="onSelectEventTime"
                  @update:year-month="onChangeYearMonth"
                >
                  <template
                    v-if="calendarLoaded === true"
                    #date="{ date }"
                  >
                    <span :class="getCalendarDayClass(date, workdays)">
                      {{ (date as any).day }}
                    </span>
                  </template>
                </RocCalendarSelect>
              </div>
            </div>
            
            <div class="row">
              <div class="col-12 text-secondary">
                <template v-if="(currentLeaveKind?.detail.length > 0 && currentLeaveKindDetail === null)">
                  <small>請先輸入假別細項</small>
                </template>
                <template v-else-if="eventCardData.length === 0">
                  <small>請於事件第一次填報時填寫</small>
                </template>
              </div>
            </div>

            <small
              class="text-secondary"
            >
              {{ currentLeaveKind?.eventExplanation }}
            </small>
          </div>
        </div>
      </template>

      <template v-if="currentLeaveKind?.isEvent === true">
        <div class="row border-bottom border-dark-subtle mx-0">
          <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card">
            <div class="d-table h-100 w-100">
              <div class="d-table-cell align-middle">
                <span>請假期限</span>
              </div>
            </div>
          </div>
          <div class="col-9 col-md-10 py-2 text-start">
            <span>{{ (periodStart && periodEnd) ? (dateToRocString(periodStart, false) + ' ~ ' + dateToRocString(periodEnd, false)) : '' }}</span>
          </div>
        </div>
      </template>

      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <template v-if="currentLeaveKind?.onlyOneDay === false">
                <span>起始時間</span>
              </template>
              <template v-else>
                <span>請假日期</span>
              </template>
            </div>
          </div>
        </div>
        <div class="col-9 col-md-10 py-2 text-start">
          <div class="row">
            <div
              class="col-auto d-flex align-items-baseline mb-2 mb-md-0"
              :style="{ 'min-height': '2.5rem' }"
            >
              <RocCalendarSelect
                :inputClass="{
                  'form-control': true,
                  'w-auto': true
                }"
                :modelValue="startTime"
                :disabled="submitted === true"
                @click="onClickDate(startTime)"
                @update:modelValue="onSelectStartTime($event, startTime)"
                @update:year-month="onChangeYearMonth"
              >
                <template
                  v-if="calendarLoaded === true"
                  #date="{ date }"
                >
                  <span :class="getCalendarDayClass(date, workdays)">
                    {{ (date as any).day }}
                  </span>
                </template>
              </RocCalendarSelect>
            </div>
            <div
              v-if="(currentLeaveKind?.unit === 'H') || (currentLeaveKind?.unit === 'D' && currentLeaveKind?.minimumUnit === 'F')"
              class="col-auto d-flex align-items-center my-1 my-sm-0"
            >
              <div
                v-if="currentLeaveKind?.unit === 'H'"
                class="text-center"
              >
                <select
                  class="form-select d-inline-block w-auto"
                  :value="startTime.getHours()"
                  :disabled="submitted === true"
                  @input="onSelectHour($event, startTime)"
                >
                  <template
                    v-for="hour in 24"
                    :key="hour"
                  >
                    <option :value="hour - 1">
                      {{ (hour - 1).toString().padStart(2, '0') }}
                    </option>
                  </template>
                </select>

                <span class="mx-1">:</span>

                <select
                  class="form-select d-inline-block w-auto"
                  :value="startTime.getMinutes()"
                  :disabled="submitted === true"
                  @input="onSelectMinute($event, startTime)"
                >
                  <template
                    v-for="minute in 60"
                    :key="minute"
                  >
                    <option :value="minute - 1">
                      {{ (minute - 1).toString().padStart(2, '0') }}
                    </option>
                  </template>
                </select>
              </div>

              <div
                v-else-if="currentLeaveKind?.unit === 'D' && currentLeaveKind?.minimumUnit === 'F'"
                class="text-center"
              >
                <select
                  class="form-select d-inline-block w-auto"
                  :value="startTime.getHours()"
                  :disabled="submitted === true"
                  @input="onSelectHour($event, startTime)"
                >
                  <option :value="startTimeArrivalTime.getHours()">
                    {{ startTimeArrivalTime.getHours().toString().padStart(2, '0') }}
                  </option>
                  <option :value="startTimeMiddayBreakEnd.getHours()">
                    {{ startTimeMiddayBreakEnd.getHours().toString().padStart(2, '0') }}
                  </option>
                </select>

                <span class="mx-1">:</span>

                <select
                  class="form-select d-inline-block w-auto"
                  :value="0"
                  disabled
                >
                  <option :value="0">
                    {{ '0'.toString().padStart(2, '0') }}
                  </option>
                </select>
              </div>
            </div>
            <div class="col-12 col-md-auto text-primary d-flex align-items-center">
              <span>刷卡時間：</span>
              <span>{{ startDateClockTime.length > 0 ? startDateClockTime : '無' }}</span>
            </div>
          </div>
        </div>
      </div>

      <template v-if="currentLeaveKind?.onlyOneDay === false">
        <div class="row border-bottom border-dark-subtle mx-0">
          <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card">
            <div class="d-table h-100 w-100">
              <div class="d-table-cell align-middle">
                <span>截止時間</span>
              </div>
            </div>
          </div>
          <div class="col-9 col-md-10 py-2 text-start">
            <div class="row">
              <div
                class="col-auto d-flex align-items-baseline mb-2 mb-md-0"
                :style="{ 'min-height': '2.5rem' }"
              >
                <RocCalendarSelect
                  :inputClass="{
                    'form-control': true,
                    'w-auto': true
                  }"
                  :modelValue="endTime"
                  :disabled="(submitted === true) || (currentLeaveKind?.autoEndDate === true)"
                  @click="onClickDate(endTime)"
                  @update:modelValue="onSelectEndTime($event, endTime)"
                  @update:year-month="onChangeYearMonth"
                >
                  <template
                    v-if="calendarLoaded === true"
                    #date="{ date }"
                  >
                    <span :class="getCalendarDayClass(date, workdays)">
                      {{ (date as any).day }}
                    </span>
                  </template>
                </RocCalendarSelect>
              </div>
              <div
                v-if="(currentLeaveKind?.unit === 'H') || (currentLeaveKind?.unit === 'D' && currentLeaveKind?.minimumUnit === 'F')"
                class="col-auto d-flex align-items-center my-1 my-sm-0"
              >
                <div
                  v-if="currentLeaveKind?.unit === 'H'"
                  class="text-center"
                >
                  <select
                    class="form-select d-inline-block w-auto"
                    :value="endTime.getHours()"
                    :disabled="submitted === true"
                    @input="onSelectHour($event, endTime)"
                  >
                    <template
                      v-for="hour in 24"
                      :key="hour"
                    >
                      <option :value="hour - 1">
                        {{ (hour - 1).toString().padStart(2, '0') }}
                      </option>
                    </template>
                  </select>

                  <span class="mx-1">:</span>

                  <select
                    class="form-select d-inline-block w-auto"
                    :value="endTime.getMinutes()"
                    :disabled="submitted === true"
                    @input="onSelectMinute($event, endTime)"
                  >
                    <template
                      v-for="minute in 60"
                      :key="minute"
                    >
                      <option :value="minute - 1">
                        {{ (minute -1).toString().padStart(2, '0') }}
                      </option>
                    </template>
                  </select>
                </div>

                <div
                  v-else-if="currentLeaveKind?.unit === 'D' && currentLeaveKind?.minimumUnit === 'F'"
                  class="text-center"
                >
                  <select
                    class="form-select d-inline-block w-auto"
                    :value="endTime.getHours()"
                    :disabled="submitted === true"
                    @input="onSelectHour($event, endTime)"
                  >
                    <option :value="endTimeMiddayBreakStart.getHours()">
                      {{ endTimeMiddayBreakStart.getHours().toString().padStart(2, '0') }}
                    </option>
                    <option :value="endTimeDepartureTime.getHours()">
                      {{ endTimeDepartureTime.getHours().toString().padStart(2, '0') }}
                    </option>
                  </select>

                  <span class="mx-1">:</span>

                  <select
                    class="form-select d-inline-block w-auto"
                    :value="0"
                    disabled
                  >
                    <option :value="0">
                      {{ '0'.toString().padStart(2, '0') }}
                    </option>
                  </select>
                </div>
              </div>
              <div class="col-12 col-md-auto text-primary d-flex align-items-center">
                <span>刷卡時間：</span>
                <span>{{ endDateClockTime.length > 0 ? endDateClockTime : '無' }}</span>
              </div>
            </div>
            <div class="row">
              <div class="col-12 text-secondary">
                <small>
                  {{ currentLeaveKind?.endTimeExplanation }}
                </small>
              </div>
            </div>
          </div>
        </div>
      </template>

      <template v-if="currentLeaveKind?.needProject === true">
        <div class="row border-bottom border-dark-subtle mx-0">
          <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card">
            <div class="d-table h-100 w-100">
              <div class="d-table-cell align-middle">
                <span>填報計畫</span>
              </div>
            </div>
          </div>
          <div class="col py-2 text-start">
            <div class="row">
              <div :class="['col py-0', (shakeProject === true) ? 'shake' : '' ]">
                <ProjectSelect
                  :modelValue="project"
                  :projectData="projectData"
                  :placeholder="'請輸入或選取計畫'"
                  :clearable="true"
                  :disabled="submitted === true"
                  @update:modelValue="onSelectProject"
                  @delete="onDeleteProject"
                />
              </div>
              <div class="col-12 fw-normal text-secondary">
                <small>※ 計畫進度已達100%第3個月起不可填報</small>
              </div>
            </div>
          </div>
        </div>
      </template>

      <template v-if="currentLeaveKind?.needLocation === true">
        <div class="row border-bottom border-dark-subtle mx-0">
          <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card">
            <div class="d-table h-100 w-100">
              <div class="d-table-cell align-middle">
                <span>出差地點</span>
              </div>
            </div>
          </div>
          <div class="col-9 col-md-10 py-2 text-start">
            <div class="row">
              <div :class="['col py-0', (shakePlace === true) ? 'shake' : '' ]">
                <textarea
                  v-model="place"
                  class="form-control w-100"
                  :placeholder="'請填寫出差地點'"
                  :maxlength="PLCAE_TEXTAREA_MAX"
                  :disabled="submitted === true"
                />
                <small class="text-secondary">最大可輸入字數：{{ PLCAE_TEXTAREA_MAX }}</small>
              </div>
            </div>
          </div>
        </div>
      </template>

      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>請假事由</span>
            </div>
          </div>
        </div>
        <div class="col-9 col-md-10 py-2 text-start">
          <div class="row">
            <div
              :class="[
                'col py-0',
                (shakeReason === true) ? 'shake' : ''
              ]"
            >
              <textarea
                v-model="reason"
                class="form-control w-100"
                :placeholder="'請填寫請假事由'"
                :maxlength="REASON_TEXTAREA_MAX"
                :disabled="submitted === true"
                @focusout="onTextareaFocusout"
              />
              <small class="text-secondary">最大可輸入字數：{{ REASON_TEXTAREA_MAX }}</small>
            </div>
          </div>
        </div>
      </div>

      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>代</span>
              <span class="mx-1">理</span>
              <span>人</span>
            </div>
          </div>
        </div>
        <div class="col py-2 text-start">
          <div class="row">
            <div class="col col-sm-6 col-md-5 col-lg-4 pt-1">
              <ChooseColleague
                :modelValue="agent.userId ? {
                  userId: agent.userId,
                  userName: agent.userName
                } : null"
                :employeeData="employeeData.filter((employeeEle) => (employee.userId !== employeeEle.userId))"
                :employeeFilter="employee.userId"
                :placeholder="'請輸入或選取代理人'"
                :filter="onSearchEmployeeData"
                :clearable="false"
                :alwaysShowClearButton="false"
                :disabled="submitted === true"
                @update:modelValue="onChangeAgent"
              />
            </div>
            <div class="col-12 text-danger">
              <small>同仁於請假期間如有電子公文表單或三卡簽核作業代理需求，請至</small>
              <template v-if="(mode.split('.')[1] === 'intranet')">
                <button
                  type="button"
                  class="btn btn-link mx-1 p-0 align-top"
                  @click="onSetDeputy"
                >
                  <small>SinoDAMS系統</small>
                </button>
              </template>
              <template v-else>
                <small>SinoDAMS系統</small>
              </template>
              <small>設定個人或職務代理人及代理期間</small>
            </div>
          </div>
        </div>
      </div>

      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>加會人員</span>
            </div>
          </div>
        </div>
        <div :class="['col-sm-auto py-2', (signers.length > 0) ? 'col-2' : 'col-auto']">
          <button
            type="button"
            :class="[
              'btn me-1 p-2',
              submitted === true ? 'btn-outline-secondary' : 'btn-primary'
            ]"
            :disabled="submitted === true"
            @click="onAddSignerClick"
          >
            <span>新增加會人員</span>
          </button>
        </div>

        <div class="col py-2">
          <template
            v-for="(signer, index) in signers"
            :key="index"
          >
            <div class="row mb-2">
              <div class="col-auto col-md-1 text-md-center p-0">
                <span class="badge bg-secondary">
                  {{ index + 1 }}
                </span>
              </div>
              <div class="col col-sm-8 col-md-6 col-lg-4 pt-1">
                <ChooseColleague
                  :modelValue="signer.userId ? {
                    userId: signer.userId,
                    userName: signer.userName
                  } : null"
                  :customClass="(shakeSigner && signer.userId === null) ? 'shake' : ''"
                  :disabled="submitted === true"
                  :employeeData="employeeData.filter((employeeEle) => ((signers.findIndex((signerEle) => signerEle?.userId ? (signerEle.userId === employeeEle.userId) : false) === -1) && (employee.userId !== employeeEle.userId)))"
                  :employeeFilter="employee.userId"
                  :signerFilter="employeeData.filter((employeeEle) => ((signers.findIndex((signerEle) => signerEle?.userId ? (signerEle.userId === employeeEle.userId) : false) !== -1)))"
                  :placeholder="'請輸入或選取加會人員'"
                  :filter="onSearchEmployeeData"
                  :clearable="true"
                  :alwaysShowClearButton="true"
                  @update:modelValue="onChangeSigner($event, index)"
                  @delete="onDeleteSigner(index)"
                />
              </div>
            </div>
          </template>
        </div>
      </div>

      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>上傳附件</span>
            </div>
          </div>
        </div>
        <div class="col-9 col-md-10 py-2 text-start">
          <AttachedFile
            :disabled="submitted === true"
            @update:modelValue="onUploadFiles"
          >
            <template #message>
              <small class="text-danger align-middle">
                {{ currentLeaveKind?.attachmentExplanation }}
              </small>
            </template>
          </AttachedFile>
        </div>
      </div>

      <template v-if="userStore.userId === employee?.userId">
        <div class="row mx-0 text-start">
          <div class="col-12">
            <LeavesInfo
              :modelValue="{
                yearForStatic: startTime.getFullYear(),
                yearLeaveDateStatic: yearLeaveDateStatic,
                leavesData: {
                  annualLeaves: annualLeaves,
                  compensatoryLeaves: compensatoryLeaves,
                  extendedLeaves: extendedLeaves
                }
              }"
            />
          </div>

          <div class="col-12">
            <span>※ 時數說明：</span>
            <ol class="mb-1">
              <li>
                <span>已休時數：主管已同意及簽核中的請假時數總和。</span>
              </li>
              <li>
                <span>剩餘時數：可休時數扣除已休時數。</span>
              </li>
            </ol>
          </div>
        </div>
      </template>

      <div
        v-if="currentLeaveKind?.attachmentPageExplanation"
        class="alert alert-warning mb-0"
        role="alert"
      >
        {{ currentLeaveKind?.attachmentPageExplanation }}
      </div>

      <div
        v-if="currentLeaveKind?.pageExplanation"
        class="alert alert-warning mb-0"
        role="alert"
      >
        {{ currentLeaveKind?.pageExplanation }}
      </div>
    </div>
  </div>

  <div class="row mt-2 mx-0">
    <div class="col text-center">
      <button
        type="button"
        :class="[
          'btn mx-2',
          (submitted === true) ? 'btn-outline-secondary' : 'btn-primary'
        ]"
        :disabled="submitted === true"
        @click="onSubmit"
      >
        <span>送出</span>
      </button>
      <button
        type="button"
        :class="[
          'btn mx-2',
          (submitted === true ? 'btn-outline-secondary' : 'btn-secondary')
        ]"
        :disabled="submitted === true"
        @click="onResetForm"
      >
        <span>重填</span>
      </button>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'
import { useAuthUserStore } from '../store/index'
import { useMessageStore } from '../store/message'
import { useOvertimeData } from '../composable/overtimeData'
import { useWorkday } from '../composable/workdays'
import { useProject } from '../composable/project'
import { useYearLeaveData } from '../composable/yearLeaveData'
import { useEmployeeData } from '../composable/employeeData'
import { useAddSigner } from '../composable/signer'
import { useAbortController } from '../composable/abortController'
import ChooseUser from '../components/ChooseUser.vue'
import ChooseColleague from '../components/ChooseColleague.vue'
import ProjectSelect from '../components/ProjectSelect.vue'
import RocCalendarSelect from '../components/RocCalendarSelect.vue'
import AttachedFile from '../components/AttachedFile.vue'
import LeavesInfo from '../components/LeavesInfo.vue'
import {
  GET_LEAVEKINDS_URL,
  GET_WORKDAYSDATERANGE_URL,
  GET_DAYINTIME_URL,
  GET_CALCULATELEAVEDAYHOURS_URL,
  GET_CALCULATELEAVEENDDATE_URL,
  POST_EVENTRELATEDSHEET_URL,
  POST_LEAVEPERMITTEDPERIOD_URL,
  POST_CHECKC1CARD_URL,
  POST_SUBMITC1CARD_URL
} from '../api/appUrl'
import { SHAKE_RESIST_TIME, SYSTEM_ERROR_MESSAGE, PLCAE_TEXTAREA_MAX, REASON_TEXTAREA_MAX, SIGNERS_MAX, FORM_ID } from '../api/appConst'
import type { EmployeeStoreBaseType, SignerType, LeaveKindType, LeaveKindDetailType, ProjectType, MonthType, UploadedFileType, FormSubmitResponseApiType, C1CardSubmitType, LeaveKindApiType, LeaveKindDetailApiType } from '../api/appType'
import { getCalendarDayClass, dateToRocString, onSearchEmployeeData, onSetDeputy } from '../api/appFunction'
import { onBeforeRouteLeave } from 'vue-router'
import { routerExtend } from '../router'

const mode = import.meta.env.MODE
const userStore = useAuthUserStore()
const confirm = useConfirm()
const toast = useToast()
const { overtimeData, onSetOvertimeDate, onGetOvertimeData } = useOvertimeData()
const { workdays, calendarLoaded, onLoadCalendarData } = useWorkday()
const { projectData, onGetProjectsDateRange } = useProject()
const { yearLeaveDateStatic, annualLeaves, compensatoryLeaves, extendedLeaves, onGetYearLeaves } = useYearLeaveData()
const { employeeData, onSetEmployeeData } = useEmployeeData()
const { signers, signersString, onChangeSigner, onChangeSigners, onAddSigner, onDeleteSigner, onDeleteSigners, onCheckSigner } = useAddSigner()
const { abortController, fetchErrorHandler, abortListener } = useAbortController()

/** 前端開始填表時間 */
const filledTime = new Date()

/** 預設起始時間 */
let startTimeDefault = filledTime

/** 預設截止時間 */
let endTimeDefault = filledTime

const employee = ref<EmployeeStoreBaseType>({
  userId: userStore.userId,
  userName: userStore.userName,
  deptNo: userStore.deptNo,
  deptSName: userStore.deptSName
})

const applyEmployeeData = ref<Array<EmployeeStoreBaseType>>([])

const agent = ref<EmployeeStoreBaseType>({
  userId: '',
  userName: '',
  deptNo: 0,
  deptSName: ''
})

const project = ref<ProjectType | null>(null)

/** 起始時間 */
const startTime = ref<Date>(filledTime)

/** 截止時間 */
const endTime = ref<Date>(filledTime)

/** 起始時間當日的刷卡時間 */
const startDateClockTime = ref<string>('')

/** 截止時間當日的刷卡時間 */
const endDateClockTime = ref<string>('')

/** 事件發生日 */
const eventTime = ref<Date | null>(null)

/** 請假期限(開始) */
const periodStart = ref<Date | null>(null)

/** 請假期限(結束) */
const periodEnd = ref<Date | null>(null)

/** 剩餘可休 */
const remain = ref<number | null>(null)

/** 起始時間的工作日正常上班時間 */
const startTimeArrivalTime = ref<Date>(new Date())

/** 起始時間的工作日午休結束時間 */
const startTimeMiddayBreakEnd = ref<Date>(new Date())

/** 截止時間的工作日午休起始時間 */
const endTimeMiddayBreakStart = ref<Date>(new Date())

/** 截止時間的工作日正常下班時間 */
const endTimeDepartureTime = ref<Date>(new Date())

const leaveKinds = ref<Array<LeaveKindType>>([])
const currentLeaveKind = ref<LeaveKindType | null>(null)
const currentLeaveKindDetail = ref<LeaveKindDetailType | null>(null)
const place = ref<string>('')
const reason = ref<string>('')
const eventCardExisted = ref<boolean>(false)
const eventCardData = ref<Array<{
  formUID: string
  formID: string
  formNo: string
  date: Date
  periodStart: Date
  periodEnd: Date
  remain: number
  unit: string
  unitName: string
}>>([])
const eventCardUID = ref<string>('')
const eventCardFormNumber = ref<string>('')
const shakeReason = ref<boolean>(false)
const shakePlace = ref<boolean>(false)
const shakeProject = ref<boolean>(false)
const shakeSigner = ref<boolean>(false)
const uploadedFiles = ref<Array<UploadedFileType>>([])
const submitted = ref<boolean>(false)

/**
 * 切換申請人
 * @param event 單一名員工的資料
 */
const onChangeEmployee = async (event: EmployeeStoreBaseType): Promise<void> => {
  const tempEmployee = employee.value
  const tempAgent = agent.value
  const tempProject = project.value
  const tempStartTime = startTime.value
  const tempEndTime = endTime.value
  const tempStartDateClockTime = startDateClockTime.value
  const tempEndDateClockTime = endDateClockTime.value
  const tempEventTime = eventTime.value
  const tempPeriodStart = periodStart.value
  const tempPeriodEnd = periodEnd.value
  const tempRemain = remain.value
  const tempStartTimeArrivalTime = startTimeArrivalTime.value
  const tempStartTimeMiddayBreakEnd = startTimeMiddayBreakEnd.value
  const tempEndTimeMiddayBreakStart = endTimeMiddayBreakStart.value
  const tempEndTimeDepartureTime = endTimeDepartureTime.value
  const tempLeaveKind = leaveKinds.value
  const tempCurrentLeaveKind = currentLeaveKind.value
  const tempCurrentLeaveKindDetail = currentLeaveKindDetail.value
  const tempPlace = place.value
  const tempReason = reason.value
  const tempAddSigner = signers.value
  const tempEventCardData = eventCardData.value
  const tempEventCardUID = eventCardUID.value
  const tempEventCardFormNumber = eventCardFormNumber.value

  setDefaultForm({
    userId: event.userId,
    userName: event.userName,
    deptNo: event.deptNo,
    deptSName: event.deptSName
  })

  confirm.require({
    group: 'app',
    header: '提醒',
    message: '目前填報的表單資料將被清空，是否繼續？',
    acceptProps: {
      severity: 'primary'
    },
    rejectProps: {
      severity: 'secondary'
    },
    reject: (): void => {
      employee.value = tempEmployee
      agent.value = tempAgent
      project.value = tempProject
      startTime.value = tempStartTime
      endTime.value = tempEndTime
      startDateClockTime.value = tempStartDateClockTime
      endDateClockTime.value = tempEndDateClockTime
      eventTime.value = tempEventTime
      periodStart.value = tempPeriodStart
      periodEnd.value = tempPeriodEnd
      remain.value = tempRemain
      startTimeArrivalTime.value = tempStartTimeArrivalTime
      startTimeMiddayBreakEnd.value = tempStartTimeMiddayBreakEnd
      endTimeMiddayBreakStart.value = tempEndTimeMiddayBreakStart
      endTimeDepartureTime.value = tempEndTimeDepartureTime
      leaveKinds.value = tempLeaveKind
      currentLeaveKind.value = leaveKinds.value.find((e: LeaveKindType) => e.number === tempCurrentLeaveKind?.number) ?? leaveKinds.value[0]
      currentLeaveKindDetail.value = currentLeaveKind.value?.detail.find(((e: LeaveKindDetailType) => e.leaveSubNumber === tempCurrentLeaveKindDetail?.leaveSubNumber)) ?? null
      place.value = tempPlace
      reason.value = tempReason
      onChangeSigners(tempAddSigner)
      eventCardData.value = tempEventCardData
      eventCardUID.value = tempEventCardUID
      eventCardFormNumber.value = tempEventCardFormNumber
    }
  })
}

const onChangeAgent = (user: EmployeeStoreBaseType): void => {
  agent.value = {
    userId: user.userId,
    userName: user.userName,
    deptNo: user.deptNo,
    deptSName: user.deptSName
  }
}

const onSelectHour = (event: Event, date: Date): void => {
  date.setHours(parseInt((event.target as HTMLSelectElement).value, 10))
}

const onSelectMinute = (event: Event, date: Date): void => {
  date.setMinutes(parseInt((event.target as HTMLSelectElement).value, 10))
}

const onSelectStartTime = async (event: Date, originalTime: Date): Promise<void> => {
  try {
    startTime.value = new Date(event.getFullYear(), event.getMonth(), event.getDate(), originalTime.getHours(), originalTime.getMinutes())
    if (currentLeaveKind.value?.autoEndDate === true) {
      await onCalculateLeaveEndDate()
    }

    startDateClockTime.value = await onLoadClockTime(startTime.value)
  } catch (err: unknown) {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  }

  try {
    await onGetYearLeaves(startTime.value, employee.value.userId, abortController.signal)
  } catch (err: unknown) {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  }

  if (currentLeaveKind.value?.needProject === true) {
    await onGetProjectData()

    // 如果已填計畫不符合新取得的計畫資料，則清空已填計畫
    if (projectData.value.find((e: ProjectType) => e.id === project.value?.id) === undefined) {
      project.value = null
    }
  }
}

const onSelectEndTime = async (event: Date, originalTime: Date): Promise<void> => {
  endTime.value = new Date(event.getFullYear(), event.getMonth(), event.getDate(), originalTime.getHours(), originalTime.getMinutes())
  endDateClockTime.value = await onLoadClockTime(endTime.value)

  if (currentLeaveKind.value?.needProject === true) {
    await onGetProjectData()

    // 如果已填計畫不符合新取得的計畫資料，則清空已填計畫
    if (projectData.value.find((e: ProjectType) => e.id === project.value?.id) === undefined) {
      project.value = null
    }
  }
}

const onLoadClockTime  = async (time: Date): Promise<string> => {
  const params = new URLSearchParams({
    empNo: employee.value.userId,
    date: time.toISOString()
  })
  return await fetch(GET_DAYINTIME_URL + '?' + params, {
    method: 'GET',
    signal: abortController.signal
  }).then((res: Response) => {
    if (!res.ok) {
      throw new Error(res.status.toString())
    }
    return res.text()
  }).then(res => {
    return res
  }).catch((err: Error): string => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
    return ''
  })
}

const onSelectEventTime = (event: Date): void => {
  eventTime.value = event

  const params = new URLSearchParams({
    eventDate: eventTime.value.toISOString(),
    leaveNumber: currentLeaveKind.value ? currentLeaveKind.value.number.toString() : '0',
    subKindNumber: currentLeaveKindDetail.value ? currentLeaveKindDetail.value.leaveSubNumber.toString() : '0',
    empNo: employee.value.userId
  })
  fetch(POST_LEAVEPERMITTEDPERIOD_URL + '?' + params, {
    method: 'POST',
    signal: abortController.signal
  }).then((res: Response): Promise<any> => {
    if (!res.ok) {
      throw new Error(res.status.toString())
    }
    return res.json()
  }).then((res: any): void => {
    periodStart.value = new Date(res.ExpirationStartDate)
    periodEnd.value = new Date(res.ExpirationEndDate)
  }).catch((err: Error): void => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })
}

const onGetProjectData = async (): Promise<void> => {
  if (startTime.value !== null && endTime.value !== null) {
    try {
      await onGetProjectsDateRange(startTime.value, endTime.value, employee.value.deptNo, abortController.signal)
    } catch (err: unknown) {
      console.error(err)
      fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
    }
  }
}

const onChangeEventCardUID = (cardDataIndex: string, formUID: string, formNo: string): void => {
  if (eventCardUID.value === formUID) {
    eventCardUID.value = ''
    eventCardFormNumber.value = ''
    eventTime.value = null
    periodStart.value = null
    periodEnd.value = null
    remain.value = null
  } else {
    eventCardUID.value = formUID
    eventCardFormNumber.value = formNo
    eventTime.value = eventCardData.value[parseInt(cardDataIndex, 10)].date
    periodStart.value = eventCardData.value[parseInt(cardDataIndex, 10)].periodStart
    periodEnd.value = eventCardData.value[parseInt(cardDataIndex, 10)].periodEnd
    remain.value = eventCardData.value[parseInt(cardDataIndex, 10)].remain
  }
}

const onChangeLeaveKind = async (event: Event): Promise<void> => {
  currentLeaveKind.value = leaveKinds.value.find((e: LeaveKindType) => e.number === parseInt((event.target as HTMLSelectElement).value, 10)) ?? leaveKinds.value[0]
  currentLeaveKindDetail.value = null

  startTime.value = new Date(startTimeDefault)
  endTime.value = new Date(endTimeDefault)
  startDateClockTime.value = await onLoadClockTime(startTime.value)
  endDateClockTime.value = await onLoadClockTime(endTime.value)

  eventCardUID.value = ''
  eventCardFormNumber.value = ''
  eventTime.value = null
  periodStart.value = null
  periodEnd.value = null
  remain.value = null
  eventCardData.value = []

  if (currentLeaveKind.value?.unit === 'D') {
    try {
      onSetOvertimeDate(startTime.value)
      await onGetOvertimeData(employee.value.userId, abortController.signal)

      if (overtimeData.value !== null) {
        startTimeArrivalTime.value = new Date(overtimeData.value.dayDetail.ArrivalTime)
        startTimeMiddayBreakEnd.value = new Date(overtimeData.value.dayDetail.MiddayBreakEnd)
        endTimeMiddayBreakStart.value = new Date(overtimeData.value.dayDetail.MiddayBreakStart)
        endTimeDepartureTime.value = new Date(overtimeData.value.dayDetail.DepartureTime)
        startTime.value = startTimeArrivalTime.value
        endTime.value = endTimeDepartureTime.value
        startDateClockTime.value = await onLoadClockTime(startTime.value)
        endDateClockTime.value = await onLoadClockTime(endTime.value)
      }

      if (currentLeaveKind.value?.autoEndDate === true) {
        await onCalculateLeaveEndDate()
      }
    } catch (err: unknown) {
      console.error(err)
      fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
    }
  }

  if (currentLeaveKind.value?.switchLeavePrompt !== null) {
    toast.add({
      severity: 'info',
      summary: currentLeaveKind.value.switchLeavePrompt,
      group: 'app'
    })
  }

  if (currentLeaveKind.value?.needProject === true) {
    await onGetProjectData()

    // 如果已填計畫不符合新取得的計畫資料，則清空已填計畫
    if (projectData.value.find((e: ProjectType) => e.id === project.value?.id) === undefined) {
      project.value = null
    }
  }

  if (currentLeaveKind.value?.isEvent === true && currentLeaveKind.value?.detail.length === 0) {
    onGetEventRelatedSheets()
  }
}

const onChangeLeaveKindDetail = async (event: Event): Promise<void> => {
  currentLeaveKindDetail.value = currentLeaveKind.value?.detail.find(((e: LeaveKindDetailType) => e.leaveSubNumber === parseInt((event.target as HTMLSelectElement).value, 10))) ?? null

  startTime.value = new Date(startTimeDefault)
  endTime.value = new Date(endTimeDefault)
  startDateClockTime.value = await onLoadClockTime(startTime.value)
  endDateClockTime.value = await onLoadClockTime(endTime.value)

  eventCardUID.value = ''
  eventCardFormNumber.value = ''
  eventTime.value = null
  periodStart.value = null
  periodEnd.value = null
  remain.value = null
  eventCardData.value = []

  if (currentLeaveKind.value?.isEvent === true) {
    onGetEventRelatedSheets()
  }
  if (currentLeaveKind.value?.autoEndDate === true) {
    onCalculateLeaveEndDate()
  }
}

const onChangeEventCardExisted = (event: Event): void => {
  eventCardExisted.value = ((event.target as HTMLInputElement).value === 'true')

  if (eventCardExisted.value === true) {
    const found = eventCardData.value.find((e: any) => e.remain > 0)
    if (found) {
      eventCardUID.value = found.formUID
      eventCardFormNumber.value = found.formNo
      eventTime.value = new Date(found.date)
      periodStart.value = found.periodStart
      periodEnd.value = found.periodEnd
    } else {
      eventCardUID.value = ''
      eventCardFormNumber.value = ''
      eventTime.value = null
      periodStart.value = null
      periodEnd.value = null
      remain.value = null
    }
  } else {
    eventCardUID.value = ''
    eventCardFormNumber.value = ''
    eventTime.value = null
    periodStart.value = null
    periodEnd.value = null
    remain.value = null
  }
}

const onSelectProject = (projectData: ProjectType) => {
  project.value = projectData
}

const onDeleteProject = (): void => {
  project.value = null
}

const onAddSignerClick = (): void => {
  if (signers.value.find((e: SignerType) => e.userId === null)) {
    toast.add({
      severity: 'warn',
      summary: '請先輸入加會人員',
      group: 'app'
    })
  } else if (signers.value.length >= SIGNERS_MAX) {
    toast.add({
      severity: 'warn',
      summary: '僅開放最多加會' + SIGNERS_MAX + '名人員',
      group: 'app'
    })
  } else {
    onAddSigner()
  }
}

const onGetLeaveKinds = async (userId: string): Promise<void> => {
  const params = new URLSearchParams({
    empNo: userId
  })
  await fetch(GET_LEAVEKINDS_URL + '?' + params, {
    method: 'GET',
    signal: abortController.signal
  }).then((res: Response) => {
    if (!res.ok) {
      throw new Error(res.status.toString())
    }
    return res.json()
  }).then(res => {
    leaveKinds.value = res.filter((e: LeaveKindApiType) => e.DisplayOnFrontEnd === true).map((e: LeaveKindApiType) => {
      return {
        attachmentExplanation: e.AttachmentExplanation,
        attachmentPageExplanation: e.AttachmentPageExplanation,
        autoEndDate: e.AutoEndDate,
        certificateRequired: e.CertificateRequired,
        detail: e.Detail.map((detail: LeaveKindDetailApiType) => {
          return {
            displayOrder: detail.DisplayOrder,
            leaveSubName: detail.LeaveSubName,
            leaveSubNumber: detail.LeaveSubNumber,
            leaveSubType: detail.LeaveSubType,
            permitExtraLeave: detail.PermitExtraLeave,
            permittedApplyNumber: detail.PermittedApplyNumber,
            upperLimit: detail.UpperLimit
          }
        }),
        displayOrder: e.DisplayOrder,
        endTimeExplanation: e.EndTimeExplanation,
        eventExplanation: e.EventExplanation,
        isCalendarDay: e.IsCalendarDay,
        isEvent: e.IsEvent,
        minimumUnit: e.MinimumUnit,
        name: e.Name,
        needLocation: e.NeedLocation,
        needProject: e.NeedProject,
        number: e.Number,
        onlyOneDay: e.OnlyOneDay,
        pageExplanation: e.PageExplanation,
        switchLeavePrompt: e.SwitchLeavePrompt,
        leaveKindExplanation: e.LeaveKindExplanation,
        unit: e.Unit,
        upperLimit: e.UpperLimit
      }
    })
  }).catch((err: Error): void => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })
}

const onGetEventRelatedSheets = async (): Promise<void> => {
  const params = new URLSearchParams({
    empNo: employee.value.userId,
    leaveNumber: currentLeaveKind.value ? currentLeaveKind.value.number.toString() : '0',
    leaveSubNumber: currentLeaveKindDetail.value ? currentLeaveKindDetail.value.leaveSubNumber.toString() : '0'
  })
  await fetch(POST_EVENTRELATEDSHEET_URL + '?' + params, {
    method: 'POST',
    signal: abortController.signal
  }).then((res: Response): Promise<any> => {
    if (!res.ok) {
      throw new Error(res.status.toString())
    }
    return res.json()
  }).then((res: any): void => {
    eventCardData.value = res.map((e: any) => {
      let unitName = ''
      if (e.LeaveUnit === 'H') {
        unitName = '小時'
      } else if (e.LeaveUnit) {
        unitName = '天'
      }

      return {
        formUID: e.FormUID,
        formID: 'C1Card',
        formNo: e.RelationSheetNo,
        date: e.EventDate ? new Date(e.EventDate) : null,
        periodStart: e.DeadlineStartDate ? new Date(e.DeadlineStartDate) : null,
        periodEnd: e.DeadlineEndDate ? new Date(e.DeadlineEndDate) : null,
        remain: e.Remainder ?? '',
        unit: e.LeaveUnit ?? '',
        unitName: unitName
      }
    })

    const found = eventCardData.value.find((e: any) => e.remain > 0)
    if (found) {
      eventCardExisted.value = true
      eventCardUID.value = found.formUID
      eventCardFormNumber.value = found.formNo
      eventTime.value = new Date(found.date)
      periodStart.value = found.periodStart
      periodEnd.value = found.periodEnd
      remain.value = found.remain
    } else {
      eventCardExisted.value = false
      eventCardUID.value = ''
      eventCardFormNumber.value = ''
      eventTime.value = null
      periodStart.value = null
      periodEnd.value = null
      remain.value = null
    }
  }).catch((err: Error): void => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })
}

const onCalculateLeaveEndDate = async (): Promise<void> => {
  const params = new URLSearchParams({
    startDate: startTime.value.toISOString(),
    leaveNumber: currentLeaveKind.value ? currentLeaveKind.value.number.toString() : '0',
    subKindNumber: currentLeaveKindDetail.value ? currentLeaveKindDetail.value.leaveSubNumber.toString() : '0',
    shiftId: '1'
  })
  await fetch(GET_CALCULATELEAVEENDDATE_URL + '?' + params, {
    method: 'GET',
    signal: abortController.signal
  }).then((res: Response) => {
    if (!res.ok) {
      throw new Error(res.status.toString())
    }
    return res.json()
  }).then(res => {
    endTime.value = new Date(res)
  }).catch((err: Error): void => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })
}

const onClickDate = async (date: Date | null): Promise<void> => {
  if (date === null) {
    date = new Date()
  }
  const thisMonthFirstDate = new Date(date.getFullYear(), date.getMonth(), 1)
  const nextMonthFirstDate = new Date(date.getFullYear(), date.getMonth() + 1, 0)
  const loadStartTime = new Date(date.getFullYear(), date.getMonth(), 1 - thisMonthFirstDate.getDay())
  const loadEndTime = new Date(date.getFullYear(), date.getMonth() + 1, 6 - nextMonthFirstDate.getDay())

  try {
    await onLoadCalendarData(loadStartTime, loadEndTime, employee.value.userId, abortController.signal)
  } catch (err: unknown) {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  }
}

const onChangeYearMonth = async (event: MonthType): Promise<void> => {
  const thisMonthFirstDate = new Date(event.year, event.month, 1)
  const nextMonthFirstDate = new Date(event.year, event.month + 1, 0)
  const eventStartTime = new Date(event.year, event.month, 1 - thisMonthFirstDate.getDay())
  const eventEndTime = new Date(event.year, event.month + 1, 6 - nextMonthFirstDate.getDay())

  try {
    await onLoadCalendarData(eventStartTime, eventEndTime, employee.value.userId, abortController.signal)
  } catch (err: unknown) {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  }
}

const onUploadFiles = (event: Array<UploadedFileType>): void => {
  uploadedFiles.value = event
}

const onConfirmSubmit = (postData: C1CardSubmitType): void => {
  fetch(POST_SUBMITC1CARD_URL, {
    method: 'POST',
    headers: {
      'content-type': 'application/json'
    },
    body: JSON.stringify(postData),
    signal: abortController.signal
  }).then((res: Response): Promise<any> => {
    if (!res.ok) {
      throw new Error(res.status.toString())
    }
    return res.json()
  }).then((res: FormSubmitResponseApiType): void => {
    if (res.Status === 0) {
      const messageStore = useMessageStore()
      messageStore.setData('表單已送出')

      routerExtend.pushHandler('Message')
    } else {
      toast.add({
        severity: 'warn',
        summary: res.Message,
        group: 'app'
      })
    }
  }).catch((err: Error): void => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  }).finally((): void => {
    submitted.value = false
  })
}

const onSubmit = (): void => {
  if (onCheckSigner() === true) {
    shakeSigner.value = true
    toast.add({
      severity: 'warn',
      summary: '請輸入或選取加會人員',
      group: 'app'
    })
    setTimeout(() => {
      shakeSigner.value = false
    }, SHAKE_RESIST_TIME)
  } else if (startTime.value >= endTime.value) {
    toast.add({
      severity: 'warn',
      summary: '【請假截止日期時間】須大於【請假起始日期時間】',
      group: 'app'
    })
  } else {
    const sentTime: Date = new Date() // 前端按送出的時間

    const getParams = new URLSearchParams({
      startDate: startTime.value.toISOString(),
      endDate: endTime.value.toISOString(),
      leaveNumber: currentLeaveKind.value ? currentLeaveKind.value.number.toString() : '0'
    })

    const postData: C1CardSubmitType = {
      empNo: employee.value.userId,
      startTime: startTime.value,
      endTime: endTime.value,
      eventDate: eventTime.value,
      leaveKind: currentLeaveKind.value?.number ?? null,
      leaveDetailNumber: currentLeaveKindDetail.value?.leaveSubNumber ?? null,
      relatedFormNumber: eventCardFormNumber.value,
      reason: reason.value,
      deputy: agent.value.userId,
      projectNumber: project.value ? project.value.id : '',
      location: place.value,
      addSigners: signersString.value,
      createdTime: sentTime,
      filledTime: filledTime,
      uploadedFiles: uploadedFiles.value,
      confirmed: false
    }

    Promise.all([
      fetch(GET_CALCULATELEAVEDAYHOURS_URL + '?' + getParams, {
        method: 'GET',
        signal: abortController.signal
      }),
      fetch(POST_CHECKC1CARD_URL, {
        method: 'POST',
        headers: {
          'content-type': 'application/json'
        },
        body: JSON.stringify(postData),
        signal: abortController.signal
      })
    ]).then(async (checkDataRes: Array<Response>) => {
      if (checkDataRes[0].ok && checkDataRes[1].ok) {
        const timeRes = await checkDataRes[0].text()
        const calculateLeaveDayHoursRes: FormSubmitResponseApiType = await checkDataRes[1].json()
        if (calculateLeaveDayHoursRes.Status === 0) {
          let checkSubmitted: number = 0
          submitted.value = true
          confirm.require({
            group: 'app',
            header: '提醒',
            message: '您本次請【' + currentLeaveKind.value?.name + '】' + timeRes + '，是否確定請假？',
            acceptProps: {
              severity: 'primary'
            },
            rejectProps: {
              severity: 'secondary'
            },
            accept: (): void => {
              checkSubmitted++
              if (checkSubmitted === 1) {
                onConfirmSubmit(postData)
              }
            },
            reject: (): void => {
              submitted.value = false
            }
          })
        } else if (calculateLeaveDayHoursRes.Status === 9) {
          let checkSubmitted: number = 0
          submitted.value = true
          confirm.require({
            group: 'app',
            header: '提醒',
            message: calculateLeaveDayHoursRes.Message,
            defaultFocus: 'reject',
            acceptLabel: '確定',
            rejectLabel: '取消',
            acceptProps: {
              severity: 'secondary'
            },
            rejectProps: {
              severity: 'primary'
            },
            accept: (): void => {
              checkSubmitted++
              if (checkSubmitted === 1) {
                postData.confirmed = true

                onConfirmSubmit(postData)
              }
            },
            reject: (): void => {
              submitted.value = false
            }
          })
        } else {
          toast.add({
            severity: 'warn',
            summary: calculateLeaveDayHoursRes.Message,
            group: 'app'
          })
          submitted.value = false
        }
      } else {
        throw new Error(SYSTEM_ERROR_MESSAGE)
      }
    }).catch((err: Error) => {
      console.error(err)
      fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
    })
  }
}

const onLoadLeaveData = async (): Promise<void> => {
  await onGetLeaveKinds(employee.value.userId)

  if (leaveKinds.value.length > 0) {
    currentLeaveKind.value = leaveKinds.value[0]
    currentLeaveKindDetail.value = null
  }

  try {
    await onGetYearLeaves(new Date(), employee.value.userId, abortController.signal)
  } catch (err: unknown) {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  }
}

const setDefaultForm = async (user: EmployeeStoreBaseType): Promise<void> => {
  employee.value = {
    userId: user.userId,
    userName: user.userName,
    deptNo: user.deptNo,
    deptSName: user.deptSName
  }
  agent.value = {
    userId: '',
    userName: '',
    deptNo: 0,
    deptSName: ''
  }
  project.value = null

  startTime.value = new Date(startTimeDefault)
  endTime.value = new Date(endTimeDefault)
  startDateClockTime.value = await onLoadClockTime(startTime.value)
  endDateClockTime.value = await onLoadClockTime(endTime.value)
  eventTime.value = null
  periodStart.value = null
  periodEnd.value = null
  remain.value = null
  startTimeArrivalTime.value = new Date()
  startTimeMiddayBreakEnd.value = new Date()
  endTimeMiddayBreakStart.value = new Date()
  endTimeDepartureTime.value = new Date()
  place.value = ''
  reason.value = ''
  onDeleteSigners()
  eventCardData.value = []
  eventCardUID.value = ''
  eventCardFormNumber.value = ''

  onLoadLeaveData()

  await onSetEmployeeData(userStore.deptNo, abortController.signal).catch((err: Error): void => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })
  applyEmployeeData.value = employeeData.value

  onSetEmployeeData(employee.value.deptNo, abortController.signal).catch((err: Error): void => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })
}

const onResetForm = (): void => {
  submitted.value = true
  confirm.require({
    group: 'app',
    header: '提醒',
    message: '確認重置表單？',
    acceptProps: {
      severity: 'primary'
    },
    rejectProps: {
      severity: 'secondary'
    },
    accept: (): void => {
      toast.add({
        severity: 'success',
        summary: '表單已重置',
        group: 'app'
      })

      setDefaultForm({
        userId: userStore.userId,
        userName: userStore.userName,
        deptNo: userStore.deptNo,
        deptSName: userStore.deptSName
      })
      submitted.value = false
    },
    reject: (): void => {
      submitted.value = false
    }
  })
}

const onTextareaFocusout = (): void => {
  const temp = reason.value.trim()
  if (temp.length === 0) {
    reason.value = temp
  }
}

const onKeydownEditLink = (href: string): void => {
  window.open(href, '_blank')
}

const onClickEditLink = (href: string): void => {
  window.open(href, '_blank')
}

onBeforeRouteLeave(() => {
  toast.removeGroup('app')
})

onMounted(async (): Promise<void> => {
  abortListener()
  const params = new URLSearchParams({
    startDate: startTime.value.toISOString(),
    endDate: endTime.value.toISOString(),
    shiftId: '1'
  })
  try {
    const res: Response = await fetch(GET_WORKDAYSDATERANGE_URL + '?' + params, {
      method: 'GET',
      signal: abortController.signal
    })
    if (!res.ok) {
      throw new Error(res.status.toString())
    } else {
      const jsonData = await res.json()
      startTimeDefault = new Date(jsonData[0].ArrivalTime)
      endTimeDefault = new Date(jsonData[0].DepartureTime)
      startTime.value = new Date(startTimeDefault)
      endTime.value = new Date(endTimeDefault)
      startDateClockTime.value = await onLoadClockTime(startTime.value)
      endDateClockTime.value = await onLoadClockTime(endTime.value)
    }
  } catch (err: unknown) {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  }

  onLoadLeaveData()

  await onSetEmployeeData(userStore.deptNo, abortController.signal).catch((err: Error): void => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })
  applyEmployeeData.value = employeeData.value
})
</script>
<style lang="scss" scoped>
@use '../assets/scss/calendar';
</style>