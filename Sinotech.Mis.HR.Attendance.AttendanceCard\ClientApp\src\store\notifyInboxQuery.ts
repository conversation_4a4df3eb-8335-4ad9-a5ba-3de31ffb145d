import { defineStore } from 'pinia'

const now = new Date()
const startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 30)
const endTime = new Date(now.getFullYear(), now.getMonth(), now.getDate())

const defaultParameter = {
  isRead: ['1', '2'],
  startNotifyTime: startTime,
  endNotifyTime: endTime,
  employee: {
    userId: '',
    userName: '',
    deptNo: 0,
    deptSName: ''
  },
  dept: {
    deptNo: 0,
    deptSName: ''
  },
  formStatus: ['1', '2', '3', '4']
}

export const useNotifyInboxQueryStore = defineStore('notifyInboxQuery', {
  state: () => ({
    queryPage: 0,
    sortField: 'formNo',
    sortOrder: -1,
    ...defaultParameter
  }),
  actions: {
    setParameter(newParameter: any) {
      this.isRead = newParameter.isRead
      this.startNotifyTime = newParameter.startNotifyTime
      this.endNotifyTime = newParameter.endNotifyTime
      this.employee = newParameter.employee
      this.dept = newParameter.dept
      this.formStatus = newParameter.formStatus
    }
  }
})