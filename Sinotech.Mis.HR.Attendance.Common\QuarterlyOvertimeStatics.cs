﻿using Sinotech.Mis.HR.Attendance.Common;
using System.Collections.Generic;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic
{
    /// <summary>
    /// 季加班統計
    /// </summary>
    public class QuarterlyOvertimeStatics
    {
        /// <summary>
        /// 季名，例如："1-3月"、"7-9月"
        /// </summary>
        public string QuarterName { get; set; } = "";
        /// <summary>
        /// 逐月的統計資訊
        /// </summary>
        public List<MonthOvertimeStatics> Months = new List<MonthOvertimeStatics>();
        /// <summary>
        /// 本季已核可加班時數，只包括 InOvertime = true
        /// </summary>
        public int ApprovedHours { get; set; } = 0;
        /// <summary>
        /// 本季未核可加班時數，只包括 InOvertime = true
        /// </summary>
        public int UnderApprovalHours { get; set; } = 0;
        /// <summary>
        /// 本季加班時數，只包括 InOvertime = true ，等同於 ApprovedHours + UnderApprovalHours
        /// </summary>
        public int TotalHours { get; set; } = 0;
    }
}
