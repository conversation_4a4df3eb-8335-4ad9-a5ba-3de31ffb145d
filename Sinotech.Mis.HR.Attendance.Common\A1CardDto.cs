﻿using System;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 正常工作卡DTO，欄位順序不可更改
    /// </summary>
    public class A1CardDto
    {
        /// <summary>
        /// 流水號
        /// </summary>
        public int? ID { get; set; }

        /// <summary>
        /// 表單識別碼
        /// </summary>
        public Guid FormUID { get; set; }

        /// <summary>
        /// 員工編號
        /// </summary>
        public string A1_EMPNO { get; set; } = string.Empty;

        /// <summary>
        /// 民國年月
        /// </summary>
        public string A1_YYMM { get; set; } = string.Empty;

        /// <summary>
        /// 旬卡別 1/2/3 代表 上/中/下
        /// </summary>
        public char A1_NN { get; set; }

        /// <summary>
        /// 計畫編號
        /// </summary>
        public string A1_PROJNO { get; set; } = string.Empty;

        /// <summary>
        /// 每旬的1~11天時數
        /// </summary>
        public string A1_DDHH { get; set; } = string.Empty;

        /// <summary>
        /// 每旬每計畫工作總時數
        /// </summary>
        public int A1_HOUR { get; set; }

        /// <summary>
        /// YYYMMDD    填卡日期民國年月日
        /// </summary>
        public string A1_WYYMMDD { get; set; } = string.Empty;

#nullable enable
        /// <summary>
        /// YYYMMDD    結案核卡日期民國年月日
        /// </summary>
        public string? A1_AYYMMDD { get; set; }


#nullable disable

        /// <summary>
        /// 簽核狀態 <br />
        /// </summary>
        /// <value>
        /// 1:未完成 2:同意 3:不同意 4:抽單
        /// </value>
        public int A1_STATUS { get; set; }

        /// <summary>
        /// 正常卡表單單號
        /// </summary>
        /// <value>
        /// 來自 Form table 的 FormNo
        /// </value>
        public string A1_SHEETNO { get; set; }

        /// <summary>
        /// 正常卡表單序號
        /// </summary>
        /// <value>
        /// 從1開始編號
        /// </value>
        public string A1_SERIALNO { get; set; }

        /// <summary>
        /// 寫入資料來源
        /// </summary>
        /// <value>
        /// EasyFlow、Attendance (本系統)、SECINC (承辦人)
        /// </value>
        public string A1_SOURCE { get; set; }

        /// <summary>
        /// 填卡日期
        /// </summary>
        public DateTime A1_WDate { get; set; }
#nullable enable

        /// <summary>
        /// YYYMMDD    結案核卡日期
        /// </summary>
        public DateTime? A1_ADate { get; set; }

        /// <summary>
        /// 最後修改者員工編號
        /// </summary>
        public string? UpdatedEmpNo { get; set; }

        /// <summary>
        /// 最後修改者姓名
        /// </summary>
        public string? UpdatedName { get; set; }

        /// <summary>
        /// 最後修改日期時間
        /// </summary>
        public DateTime? UpdatedTime { get; set; }

        /// <summary>
        /// 最後修改來源IP 位址
        /// </summary>
        public string? UpdatedIP { get; set; }

        /// <summary>
        /// 最後修改來源電腦名稱
        /// </summary>
        public string? UpdatedHost { get; set; }

    }

}
