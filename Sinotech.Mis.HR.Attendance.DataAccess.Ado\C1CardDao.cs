﻿using Sinotech.Mis.Helpers;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.DataAccess.Interfaces;
using Sinotech.Mis.HR.Attendance.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;

namespace Sinotech.Mis.HR.Attendance.DataAccess.Ado
{
    public class C1CardDao : IC1CardDao
    {

        /// <summary>
        /// 連線字串
        /// </summary>
        private readonly string _connectionString;

        /// <summary>Initializes a new instance of the <see cref="C1CardDao" /> class.</summary>
        /// <param name="connectionString">The connection string.</param>
        public C1CardDao(string connectionString)
        {
            _connectionString = connectionString;
        }

        /// <summary>
        /// 取得年度員工生日福利資料表
        /// </summary>
        /// <param name="year">西元年</param>
        /// <returns></returns>
        public DataTable GetBirthdayWelfare(int year)
        {
            string sql = @"SELECT * FROM BirthdayWelfare WHERE ADYear=@Year;";
            SqlParameter parameter = new SqlParameter("@Year", SqlDbType.Int);
            parameter.Value = year;
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameter);
            return dt;
        }

        /// <summary>
        /// 取得年度員工生日福利資料表
        /// </summary>
        /// <param name="year">西元年</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public DataTable GetBirthdayWelfare(int year, string empNo)
        {
            List<SqlParameter> parameters = new List<SqlParameter>();

            SqlParameter parameterYear = new SqlParameter("@Year", SqlDbType.Int);
            parameterYear.Value = year;
            parameters.Add(parameterYear);

            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            parameterEmpNo.Value = empNo;
            parameters.Add(parameterEmpNo);

            string sql = @"SELECT * FROM BirthdayWelfare WHERE ADYear=@Year AND EmpNo=@EmpNo;";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            return dt;
        }

        /// <summary>
        /// 查詢特定假別特定同仁於特定事件發生日的所有假單，僅限 已同意與簽核中
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="leaveNumber">假別</param>
        /// <param name="leaveSubNumber">假別細項</param>
        /// <param name="eventDate">事件發生日</param>
        /// <returns></returns>
        public DataTable GetC1CardByEventDate(string empNo, int leaveNumber, int leaveSubNumber, DateTime eventDate)
        {
            string sql = "SELECT * FROM C1CARD WHERE C1_CODE=@LeaveNumber AND C1_CODE2=@LeaveSubNumber AND C1_EventDate = @EventDate AND C1_EMPNO = @EmpNo AND C1_STATUS in (1,2);";
            /*
             * 若要查 C1Card+Form
             SELECT C1CARD.ID, C1CARD.FormUID, C1CARD.C1_EMPNO, C1CARD.C1_YYMM, C1CARD.C1_SDD, C1CARD.C1_SHH, 
 C1CARD.C1_SMM, C1CARD.C1_EDD, C1CARD.C1_EHH, C1CARD.C1_EMM, C1CARD.C1_HOUR, C1CARD.C1_CODE, 
 C1CARD.C1_CODE2, C1CARD.C1_EventDate, C1CARD.C1_DeadlineStartDate, C1CARD.C1_DeadlineEndDate, 
 C1CARD.C1_RelationSheetNo, C1CARD.C1_LeaveMaximum, C1CARD.C1_LeaveUnit, C1CARD.C1_PrjNo, 
 C1CARD.C1_Location, C1CARD.C1_Reason, C1CARD.C1_Agent, C1CARD.C1_WYYMMDD, C1CARD.C1_AYYMMDD, 
 C1CARD.C1_STATUS, C1CARD.C1_OVER, C1CARD.C1_SHEETNO, C1CARD.C1_SERIALNO, C1CARD.C1_SOURCE, 
 C1CARD.C1_PREMON, C1CARD.C1_CHECK, C1CARD.ExpDate, C1CARD.C1_StartDate, C1CARD.C1_EndDate, 
 C1CARD.C1_WDate, C1CARD.C1_ADate, C1CARD.UpdatedEmpNo, C1CARD.UpdatedName, C1CARD.UpdatedTime, 
 C1CARD.UpdatedIP, C1CARD.UpdatedHost, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, 
 Form.EmpNo, Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.RankNo, 
 Form.RankName, Form.JobNo, Form.JobName, Form.CurrentStep, Form.TotalSteps, Form.FormStatus, Form.EndTime, 
 Form.StartTime, Form.AddedSigner, Form.CreatedHost, Form.CreatedIP, Form.FilledTime, Form.CreatedName, 
 Form.CreatedEmpNo, Form.CreatedTime, Form.ContentStartTime, Form.ContentEndTime, Form.ID AS FormIntID
 FROM C1CARD INNER JOIN
 Form ON C1CARD.FormUID = Form.FormUID
 WHERE (C1CARD.C1_EventDate = @EventDate) AND (C1CARD.C1_EMPNO = @EmpNo) AND (C1CARD.C1_STATUS IN (1, 2));
             */
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar)
            {
                Value = empNo
            };
            parameters.Add(parameterEmpNo);

            SqlParameter parameterLeaveNumber = new SqlParameter("@LeaveNumber", SqlDbType.Char, 2)
            {
                Value = leaveNumber.ToString("00")
            };
            parameters.Add(parameterLeaveNumber);

            SqlParameter parameterLeaveSubNumber = new SqlParameter("@LeaveSubNumber", SqlDbType.Char, 2)
            {
                Value = leaveSubNumber.ToString("00")
            };
            if (leaveSubNumber == 0) { parameterLeaveSubNumber.Value = "  "; }
            parameters.Add(parameterLeaveSubNumber);

            SqlParameter parameterEventDate = new SqlParameter("@EventDate", SqlDbType.DateTime);
            parameterEventDate.Value = eventDate;
            parameters.Add(parameterEventDate);
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            return dt;
        }

        /// <summary>
        /// 查詢特定假別特定同仁於特定假別的所有假單，僅限 已同意與簽核中
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="leaveNumber">假別</param>
        /// <param name="leaveSubNumber">假別細項</param>
        /// <returns></returns>
        public DataTable GetC1CardByLeaveKind(string empNo, LeaveKindEnum leaveNumber, int leaveSubNumber)
        {
            string sql = "SELECT * FROM C1CARD WHERE C1_CODE=@LeaveNumber AND C1_CODE2=@LeaveSubNumber AND C1_EMPNO = @EmpNo AND C1_STATUS in (1,2);";
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar)
            {
                Value = empNo
            };
            parameters.Add(parameterEmpNo);

            SqlParameter parameterLeaveNumber = new SqlParameter("@LeaveNumber", SqlDbType.Char, 2)
            {
                Value = ((int)leaveNumber).ToString("00")
            };
            parameters.Add(parameterLeaveNumber);

            SqlParameter parameterLeaveSubNumber = new SqlParameter("@LeaveSubNumber", SqlDbType.Char, 2);
            if (leaveSubNumber == 0)
            {
                parameterLeaveSubNumber.Value = "  ";
            }
            else
            {
                parameterLeaveSubNumber.Value = leaveSubNumber.ToString("00");
            }
            parameters.Add(parameterLeaveSubNumber);

            DataTable dt;
            dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            return dt;
        }

        /// <summary>
        /// 取得 表單關係人 某月份請假單
        /// </summary>
        /// <param name="empNo">表單關係人 員工編號</param>
        /// <param name="date">日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns>表單</returns>
        public DataTable GetC1CardMonth(string empNo, DateTime date, int? status = null)
        {
            List<SqlParameter> parameters = new List<SqlParameter>();
            string strSql = @"SELECT * FROM C1CARD WHERE C1_YYMM=@YYYMM AND C1_EMPNO=@EmpNo";

            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            parameterEmpNo.Value = empNo;
            parameters.Add(parameterEmpNo);

            string yyyMM = CardUtility.RocChineseYYYMM(date);
            SqlParameter parameterYYYMM = new SqlParameter("@YYYMM", SqlDbType.VarChar, 50);
            parameterYYYMM.Value = yyyMM;
            parameters.Add(parameterYYYMM);
            if (status != null)
            {
                strSql += @" AND C1_STATUS=@Status";
                SqlParameter parameterStatus = new SqlParameter("@Status", SqlDbType.Int);
                parameterStatus.Value = status;
                parameters.Add(parameterStatus);
            }
            strSql += " ORDER BY C1_YYMM,C1_SDD,C1_SHH;";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql, parameters);
            return dt;
        }

        /// <summary>
        /// 取得C1Cards
        /// </summary>
        /// <param name="formUID"></param>
        /// <returns></returns>
        public DataTable GetC1Cards(Guid formUID)
        {
            SqlParameter parameter = new SqlParameter("@FormUID", SqlDbType.UniqueIdentifier);
            parameter.Value = formUID;
            string sql = @"SELECT * FROM C1Card WHERE FormUID=@FormUID";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameter);
            return dt;
        }

        /// <summary>
        /// 依日期區間取得所有請假卡
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public DataTable GetC1Cards(DateTime startDate, DateTime endDate)
        {
            List<SqlParameter> parameters = new List<SqlParameter>();
            string sql = @"SELECT * FROM C1Card WHERE C1_WYYMMDD >= @StartDate AND C1_WYYMMDD <= @EndDate ORDER BY ID;";
            string strStartDate = CardUtility.RocChineseYYYMMDD(startDate);
            string strEndDate = CardUtility.RocChineseYYYMMDD(endDate);
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.Char, 7);
            parameterStartDate.Value = strStartDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.Char, 7);
            parameterEndDate.Value = strEndDate;
            parameters.Add(parameterEndDate);
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            return dt;
        }

        /// <summary>
        /// 取得某段時間內特定員工的請假卡，檢查重覆使用，故不包括頭尾
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public DataTable GetC1CardsBetween(string empNo, DateTime startDate, DateTime endDate)
        {
            string sql = @"SELECT * FROM C1Card  
                    WHERE ((C1_EndDate > @StartDate) AND (C1_StartDate < @EndDate) 
                    AND (C1_EMPNO = @EmpNo) );";
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            parameterEmpNo.Value = empNo;
            parameters.Add(parameterEmpNo);
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            return dt;
        }

        /// <summary>
        /// 依日期區間取得所有表單及請假單
        /// </summary>
        /// <param name="startDate">最早填單日期</param>
        /// <param name="endDate">最晚填單日期</param>
        /// <returns></returns>
        public DataTable GetC1CardsForms(DateTime startDate, DateTime endDate)
        {
            string sql = @"SELECT C1CARD.ID, C1CARD.FormUID, C1CARD.C1_EMPNO, C1CARD.C1_YYMM, C1CARD.C1_SDD, C1CARD.C1_SHH, 
 C1CARD.C1_SMM, C1CARD.C1_EDD, C1CARD.C1_EHH, C1CARD.C1_EMM, C1CARD.C1_HOUR, C1CARD.C1_CODE, 
 C1CARD.C1_CODE2, C1CARD.C1_EventDate, C1CARD.C1_DeadlineStartDate, C1CARD.C1_DeadlineEndDate, 
 C1CARD.C1_RelationSheetNo, C1CARD.C1_LeaveMaximum, C1CARD.C1_LeaveUnit, C1CARD.C1_PrjNo, 
 C1CARD.C1_Location, C1CARD.C1_Reason, C1CARD.C1_Agent, C1CARD.C1_WYYMMDD, C1CARD.C1_AYYMMDD, 
 C1CARD.C1_STATUS, C1CARD.C1_OVER, C1CARD.C1_SHEETNO, C1CARD.C1_SERIALNO, C1CARD.C1_SOURCE, 
 C1CARD.C1_PREMON, C1CARD.C1_CHECK, C1CARD.ExpDate, C1CARD.C1_StartDate, C1CARD.C1_EndDate, 
 C1CARD.C1_WDate, C1CARD.C1_ADate, C1CARD.UpdatedEmpNo, C1CARD.UpdatedName, C1CARD.UpdatedTime, 
 C1CARD.UpdatedIP, C1CARD.UpdatedHost, Form.ID AS FormIntID,Form.FormID, X.R1_NAME AS LeaveName, Form.FormNo, Form.FormSubject, Form.FormInfo, 
 Form.EmpNo, Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.RankNo, 
 Form.RankName, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime, Form.FormStatus, Form.TotalSteps, Form.CurrentStep,FormAttachment.EncodedFileName,
 FormAttachment.OriginalFileName,FormAttachment.FileDirectory,FormAttachment.ID AS AttachmentID,
 FormStatus.Name AS FormStatusName FROM C1CARD
 INNER JOIN (SELECT DISTINCT R1_NO, R1_NAME FROM RESTKIND ) AS X ON C1CARD.C1_CODE= X.R1_NO
 INNER JOIN Form ON C1CARD.FormUID = Form.FormUID
 INNER JOIN FormStatus ON C1CARD.C1_STATUS = FormStatus.ID AND Form.FormStatus = FormStatus.ID
 LEFT OUTER JOIN FormAttachment ON Form.FormUID = FormAttachment.FormUID
 WHERE (C1_WYYMMDD >= @StartDate AND C1_WYYMMDD <= @EndDate);";

            List<SqlParameter> parameters = new List<SqlParameter>();
            string strStartDate = CardUtility.RocChineseYYYMMDD(startDate);
            string strEndDate = CardUtility.RocChineseYYYMMDD(endDate);
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.Char, 7);
            parameterStartDate.Value = strStartDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.Char, 7);
            parameterEndDate.Value = strEndDate;
            parameters.Add(parameterEndDate);
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            return dt;
        }

        /// <summary>
        /// 依日期區間取得所有表單及請假卡
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="projNo"></param>
        /// <returns></returns>
        public DataTable GetC1CardsForms(DateTime startDate, DateTime endDate, string projNo)
        {
            string sql = @"SELECT C1CARD.ID, C1CARD.FormUID, C1CARD.C1_EMPNO, C1CARD.C1_YYMM, C1CARD.C1_SDD, C1CARD.C1_SHH, 
 C1CARD.C1_SMM, C1CARD.C1_EDD, C1CARD.C1_EHH, C1CARD.C1_EMM, C1CARD.C1_HOUR, C1CARD.C1_CODE, 
 C1CARD.C1_CODE2, C1CARD.C1_EventDate, C1CARD.C1_DeadlineStartDate, C1CARD.C1_DeadlineEndDate, 
 C1CARD.C1_RelationSheetNo, C1CARD.C1_LeaveMaximum, C1CARD.C1_LeaveUnit, C1CARD.C1_PrjNo, 
 C1CARD.C1_Location, C1CARD.C1_Reason, C1CARD.C1_Agent, C1CARD.C1_WYYMMDD, C1CARD.C1_AYYMMDD, 
 C1CARD.C1_STATUS, C1CARD.C1_OVER, C1CARD.C1_SHEETNO, C1CARD.C1_SERIALNO, C1CARD.C1_SOURCE, 
 C1CARD.C1_PREMON, C1CARD.C1_CHECK, C1CARD.ExpDate, C1CARD.C1_StartDate, C1CARD.C1_EndDate, 
 C1CARD.C1_WDate, C1CARD.C1_ADate, C1CARD.UpdatedEmpNo, C1CARD.UpdatedName, C1CARD.UpdatedTime, 
 C1CARD.UpdatedIP, C1CARD.UpdatedHost, Form.ID AS FormIntID,Form.FormID, X.R1_NAME AS LeaveName, Form.FormNo, Form.FormSubject, Form.FormInfo, 
 Form.EmpNo, Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.RankNo, 
 Form.RankName, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime, Form.FormStatus, Form.TotalSteps, Form.CurrentStep,FormAttachment.EncodedFileName,
 FormAttachment.OriginalFileName,FormAttachment.FileDirectory,FormAttachment.ID AS AttachmentID,
 FormStatus.Name AS FormStatusName FROM C1CARD
 INNER JOIN (SELECT DISTINCT R1_NO, R1_NAME FROM RESTKIND ) AS X ON C1CARD.C1_CODE= X.R1_NO
 INNER JOIN Form ON C1CARD.FormUID = Form.FormUID
 INNER JOIN FormStatus ON C1CARD.C1_STATUS = FormStatus.ID AND Form.FormStatus = FormStatus.ID
 LEFT OUTER JOIN FormAttachment ON Form.FormUID = FormAttachment.FormUID
 WHERE (Form.FormID='C1Card') AND C1CARD.C1_StartDate >= @StartDate AND C1CARD.C1_EndDate <= @EndDate AND C1CARD.C1_PrjNo like @ProjNo;";
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);
            SqlParameter parameterProjNo = new SqlParameter("@ProjNo", SqlDbType.VarChar, 10); // 為做 Like查詢要放寬
            parameterProjNo.Value = $"%{projNo}%";
            parameters.Add(parameterProjNo);
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            return dt;
        }

        /// <summary>取得某段時間內特定員工表單及卡，檢查重覆使用，故不包括頭尾</summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <returns></returns>
        public DataTable GetC1CardsFormsBetween(string empNo, DateTime startDate, DateTime endDate)
        {
            string sql = @"SELECT C1CARD.ID, C1CARD.FormUID, C1CARD.C1_EMPNO, C1CARD.C1_YYMM, C1CARD.C1_SDD, C1CARD.C1_SHH, 
 C1CARD.C1_SMM, C1CARD.C1_EDD, C1CARD.C1_EHH, C1CARD.C1_EMM, C1CARD.C1_HOUR, C1CARD.C1_CODE, 
 C1CARD.C1_CODE2, C1CARD.C1_EventDate, C1CARD.C1_DeadlineStartDate, C1CARD.C1_DeadlineEndDate, 
 C1CARD.C1_RelationSheetNo, C1CARD.C1_LeaveMaximum, C1CARD.C1_LeaveUnit, C1CARD.C1_PrjNo, 
 C1CARD.C1_Location, C1CARD.C1_Reason, C1CARD.C1_Agent, C1CARD.C1_WYYMMDD, C1CARD.C1_AYYMMDD, 
 C1CARD.C1_STATUS, C1CARD.C1_OVER, C1CARD.C1_SHEETNO, C1CARD.C1_SERIALNO, C1CARD.C1_SOURCE, 
 C1CARD.C1_PREMON, C1CARD.C1_CHECK, C1CARD.ExpDate, C1CARD.C1_StartDate, C1CARD.C1_EndDate, 
 C1CARD.C1_WDate, C1CARD.C1_ADate, C1CARD.UpdatedEmpNo, C1CARD.UpdatedName, C1CARD.UpdatedTime, 
 C1CARD.UpdatedIP, C1CARD.UpdatedHost, Form.FormID, X.R1_NAME AS LeaveName,Form.FormNo, Form.FormSubject, Form.FormInfo, 
 FormFlow.RecipientDeptNo,FormFlow.RecipientDeptSName,FormFlow.RecipientTeamID,
 FormFlow.RecipientTeamCName,FormFlow.ApproverEmpNo,FormFlow.ApproverName,FormFlow.ApproverDeptNo,
 FormFlow.IsNotification,FormFlow.FlowStatus,FormFlowStatus.Name AS FlowStatusName,
 FormFlow.IsAgentApprove,FormFlow.ApproveHost,FormFlow.ApproveIP,FormFlow.ApproveTime,
 FormFlow.ApproverTeamCName,FormFlow.ApproverTeamID,FormFlow.ApproverDeptSName,
 Form.FormUID AS FormFormUID,Form.FormID,Form.FormSubject,Form.FormNo,Form.FormInfo,Form.EmpNo,
 Form.EmpName,Form.DeptNo,Form.DeptSName,Form.TeamID,Form.TeamCName,Form.RankNo,Form.RankName,
 Form.JobNo,Form.JobName,Form.ContentStartTime,Form.ContentEndTime,Form.CreatedEmpNo,
 Form.CreatedName,Form.FilledTime,Form.CreatedTime,Form.CreatedIP,Form.CreatedHost,Form.AddedSigner,
 Form.StartTime,Form.EndTime,Form.FormStatus,Form.TotalSteps,Form.CurrentStep,
 Form.UpdatedTime AS FormUpdatedTime,Form.UpdatedIP AS FormUpdatedIP,
 Form.UpdatedHost AS FormUpdatedHost,Form.UpdatedName AS FormUpdatedName,
 Form.UpdatedEmpNo AS FormUpdatedEmpNo,Form.ID AS FormIntID,FormAttachment.EncodedFileName,
 FormAttachment.OriginalFileName,FormAttachment.FileDirectory,FormAttachment.ID AS AttachmentID,
 FormStatus.Name AS FormStatusName 
 FROM C1CARD INNER JOIN
 Form ON C1CARD.FormUID = Form.FormUID 
  INNER JOIN (SELECT DISTINCT R1_NO, R1_NAME FROM RESTKIND ) AS X ON C1CARD.C1_CODE= X.R1_NO
 INNER JOIN FormStatus ON C1CARD.C1_STATUS = FormStatus.ID AND Form.FormStatus = FormStatus.ID 
 INNER JOIN FormFlow ON Form.FormUID = FormFlow.FormUID 
 INNER JOIN FormFlowStatus ON FormFlow.FlowStatus = FormFlowStatus.ID 
 LEFT OUTER JOIN FormAttachment ON Form.FormUID = FormAttachment.FormUID
 WHERE ((C1CARD.C1_EndDate > @StartDate) AND (C1CARD.C1_StartDate < @EndDate) 
 AND (C1CARD.C1_EMPNO = @EmpNo) );";

            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            parameterEmpNo.Value = empNo;
            parameters.Add(parameterEmpNo);
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            return dt;
        }

        /// <summary>
        /// 取得同一事件的請假記錄（以事件的第一筆請假卡號查詢相關請假記錄）
        /// </summary>
        /// <param name="formNo">表單編號</param>
        /// <returns></returns>
        public DataTable GetEventRelatedRecord(string formNo)
        {
            string storedProcedure = "sp_GetEventRelationRecord";
            List<SqlParameter> parameters = new List<SqlParameter>();

            SqlParameter parameterFormNo = new SqlParameter("@SheetNo", SqlDbType.NVarChar, 10)
            {
                Value = formNo
            };
            parameters.Add(parameterFormNo);

            DataTable dt;

            dt = SqlHelper.GetDataTableByStoredProcedure(_connectionString, storedProcedure, parameters);

            return dt;
        }

        /// <summary>
        /// 取得指定員工事件假有效期限內的相關卡號資料表
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="leaveNumber">假別編號</param>
        /// <param name="leaveSubNumber">假別細項編號</param>
        /// <param name="startDate">請假起始日期時間</param>
        /// <param name="endDate">請假截止日期時間</param>
        /// <returns></returns>
        public DataTable GetEventRelatedSheets(string empNo, int leaveNumber, int leaveSubNumber, DateTime startDate, DateTime endDate)
        {
            string storedProcedure = "sp_GetEventRelationSheet";
            List<SqlParameter> parameters = new List<SqlParameter>();

            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar)
            {
                Value = empNo
            };
            parameters.Add(parameterEmpNo);

            SqlParameter parameterLeaveNumber = new SqlParameter("@LeaveNumber", SqlDbType.Char, 2)
            {
                Value = leaveNumber.ToString("00")
            };
            parameters.Add(parameterLeaveNumber);

            SqlParameter parameterLeaveSubNumber = new SqlParameter("@LeaveSubNumber", SqlDbType.Char, 2)
            {
                Value = leaveSubNumber.ToString("00")
            };
            if (leaveSubNumber == 0) { parameterLeaveSubNumber.Value = "  "; }
            parameters.Add(parameterLeaveSubNumber);

            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);

            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);

            DataTable dt = SqlHelper.GetDataTableByStoredProcedure(_connectionString, storedProcedure, parameters);
            return dt;
        }

        /// <summary>
        /// 取得所有假別
        /// </summary>
        public DataTable GetLeaveKinds()
        {
            // 目前排除休國外假、防疫隔離假、疫苗接種假
            string sqlStr = "SELECT * FROM RESTKIND WHERE Enabled=1 ORDER BY R1_DisplayOrder,R2_DisplayOrder;";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sqlStr);
            return dt;
        }

        /// <summary>
        /// 取得假別專有訊息
        /// </summary>
        /// <param name="leaveNumber">假別代碼</param>
        /// <returns>該假別專有訊息 Data Table</returns>
        public DataTable GetLeaveMessage(int leaveNumber)
        {
            string sql = @"SELECT * FROM [Message] WHERE MessageId like @MessageId;";
            SqlParameter parameter = new SqlParameter("@MessageId", SqlDbType.VarChar, 20); // 為做 Like查詢要放寬
            parameter.Value = $"3{leaveNumber.ToString("000")}0%";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameter);
            return dt;
        }

        /// <summary>
        /// 取得所有假別訊息
        /// </summary>
        public DataTable GetLeaveMessages()
        {
            string sql = @"SELECT * FROM [Message] WHERE MessageId like @MessageId;";
            SqlParameter parameter = new SqlParameter("@MessageId", SqlDbType.VarChar);
            parameter.Value = $"3___0%";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameter);
            return dt;
        }

        /// <summary>
        /// 取得相關於某單號的 C1CardDtos (C1Card為已核可或簽核中)
        /// </summary>
        /// <param name="formNumber">單號</param>
        /// <returns></returns>
        public DataTable GetRelatedCards(string formNumber)
        {
            string sql = @"SELECT * FROM C1CARD WHERE C1_RelationSheetNo=@FormNumber AND C1_STATUS in (1, 2);";
            SqlParameter parameterFormNumber = new SqlParameter("@FormNumber", SqlDbType.NVarChar, 11);
            parameterFormNumber.Value = formNumber;
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameterFormNumber);
            return dt;
        }

        /// <summary>取得某單號的 C1CardDtos，不含流程與附件等</summary>
        /// <param name="formNumber">相關單號</param>
        /// <returns>卡 Card 物件 List</returns>
        public DataTable GetCardsByFormNumber(string formNumber)
        {
            string sql = @"SELECT * FROM C1CARD WHERE C1_SHEETNO=@FormNumber AND C1_STATUS in (1, 2);";
            SqlParameter parameterFormNumber = new SqlParameter("@FormNumber", SqlDbType.NVarChar, 11);
            parameterFormNumber.Value = formNumber;
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameterFormNumber);
            return dt;
        }

        /// <summary>
        /// 取得 填表人/表單關係人 某月份已填請假單
        /// </summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="year">填表年</param>
        /// <param name="month">填表月</param>
        /// <param name="status">表單狀態</param>
        /// <returns>已填表單</returns>
        public DataTable GetSentC1CardYearMonth(string empNo, int year, int month, int? status = null)
        {
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            parameterEmpNo.Value = empNo;
            parameters.Add(parameterEmpNo);
            string strSql = @"SELECT Form.FormUID, FormFlow.Step, Form.CurrentStep, FormFlow.ID AS FormFlowID, FormFlow.FlowUID, FormFlow.RecipientEmpNo, 
 FormFlow.RecipientDeptNo, FormFlow.RecipientName, FormFlow.RecipientDeptSName, FormFlow.RecipientTeamID, 
 FormFlow.RecipientTeamCName, FormFlow.FlowName, FormFlow.ApproverEmpNo, FormFlow.ApproverName, 
 FormFlow.ApproverDeptNo, FormFlow.ApproverDeptSName, FormFlow.ApproverTeamID, 
 FormFlow.ApproverTeamCName, FormFlow.ApproveTime, FormFlow.ApproveIP, FormFlow.ApproveHost, 
 FormFlow.IsAgentApprove, FormFlow.FlowStatus, FormFlow.IsNotification, 
 FormFlowStatus.Name AS FlowStatusName, Form.FormID, X.R1_NAME AS LeaveName, Form.FormNo, Form.FormSubject, Form.FormInfo, 
 Form.EmpNo, Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.RankNo, 
 Form.RankName, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime, Form.FormStatus, Form.TotalSteps, Form.FormUID, 
 FormStatus.Name AS FormStatusName, FormFlow.ID AS FormIntID,
 FormAttachment.FileDirectory, FormAttachment.OriginalFileName, FormAttachment.EncodedFileName, FormAttachment.ID AS AttachmentID, 
 C1CARD.C1_EMPNO, C1CARD.C1_YYMM, C1CARD.C1_SDD, C1CARD.C1_SHH, C1CARD.C1_SMM, C1CARD.C1_EDD, 
 C1CARD.C1_EHH, C1CARD.C1_EMM, C1CARD.C1_HOUR, C1CARD.C1_CODE, C1CARD.C1_CODE2, 
 C1CARD.C1_EventDate, C1CARD.C1_DeadlineStartDate, C1CARD.C1_DeadlineEndDate, 
 C1CARD.C1_RelationSheetNo, C1CARD.C1_LeaveMaximum, C1CARD.C1_LeaveUnit, C1CARD.C1_Reason, 
 C1CARD.C1_WYYMMDD, C1CARD.C1_AYYMMDD, C1CARD.C1_STATUS, C1CARD.C1_OVER, C1CARD.C1_SHEETNO, 
 C1CARD.C1_SERIALNO, C1CARD.C1_SOURCE, C1CARD.C1_PREMON, C1CARD.ExpDate, C1CARD.C1_StartDate, 
 C1CARD.C1_EndDate, C1CARD.C1_CHECK, C1CARD.C1_WDate, C1CARD.C1_ADate, Form.ID AS FormIntID, 
 C1CARD.ID, Form.UpdatedEmpNo, Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, 
 Form.UpdatedHost FROM Form
 INNER JOIN FormStatus ON Form.FormStatus = FormStatus.ID
 INNER JOIN C1CARD ON FormStatus.ID = C1CARD.C1_STATUS
 INNER JOIN (SELECT DISTINCT R1_NO, R1_NAME FROM RESTKIND ) AS X ON C1CARD.C1_CODE= X.R1_NO
 INNER JOIN FormFlow ON Form.FormUID = FormFlow.FormUID
 INNER JOIN FormFlowStatus ON FormFlow.FlowStatus = FormFlowStatus.ID
 LEFT OUTER JOIN  FormAttachment ON Form.FormUID = FormAttachment.FormUID
 WHERE (Form.FormID='C1Card') AND ( ((Form.CreatedEmpNo = @EmpNo) AND (Form.FormInfo LIKE @FormInfo)) OR
 ((Form.FormInfo LIKE @FormInfo) AND (Form.EmpNo = @EmpNo)) )";

            int rocYear = CardUtility.RocChineseYear(year);
            string formInfo = $"{rocYear}_{month.ToString("00")}%";
            SqlParameter parameterFormInfo = new SqlParameter("@FormInfo", SqlDbType.VarChar, 52);
            parameterFormInfo.Value = formInfo;
            parameters.Add(parameterFormInfo);
            if (status != null)
            {
                strSql += @" AND Form.FormStatus=@Status";
                SqlParameter parameterStatus = new SqlParameter("@Status", SqlDbType.Int);
                parameterStatus.Value = status;
                parameters.Add(parameterStatus);
            }
            strSql += " ORDER BY FormInfo,Step;";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql, parameters);
            return dt;
        }
        #region

        /// <summary>
        /// 檢查個人休假資料表是否有該員工指定年月的資料
        /// </summary>
        /// <param name="empNumber">員工編號</param>
        /// <param name="applyDate">指定年月</param>
        /// <returns>若指定個人休假資料表存在則回傳 true</returns>
        public bool IfAnnualLeaveDataExists(string empNumber, DateTime applyDate)
        {
            string storedProcedure = "sp_ChkEmpRestExist";

            List<SqlParameter> parameters = new List<SqlParameter>();

            SqlParameter pEmpNumber = new SqlParameter("@EmpNo", SqlDbType.VarChar, 4);
            pEmpNumber.Value = empNumber;
            parameters.Add(pEmpNumber);

            SqlParameter pDate = new SqlParameter("@YYYMM", SqlDbType.VarChar, 5);
            pDate.Value = CardUtility.RocChineseYYYMM(applyDate);
            parameters.Add(pDate);

            SqlParameter pEmpRestTableKind = new SqlParameter("@EmpRestTableKind", SqlDbType.Int);
            pEmpRestTableKind.Value = 0;
            parameters.Add(pEmpRestTableKind);

            int returnValue = SqlHelper.GetStatusFromStoredProcedure(_connectionString, storedProcedure, parameters, out int _);
            return returnValue == 1;
        }

        #endregion

        #region GetAnnualLeaveRemainingHour / GetPostponedLeaveRemainingHour

        /// <summary>
        /// 檢查該員工於指定時間之剩餘年度延休假時數（扣除「已同意」及「簽核中」)
        /// </summary>
        /// <param name="empNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>該員工剩餘年休假時數</returns>
        public int GetAnnualLeaveRemainingHours(string employeeNumber, DateTime dt)
        {
            LeaveKindEnum leaveKind = LeaveKindEnum.AnnualLeave;
            //DateTime dt = new DateTime(year, 1, 1);

            return GetRemainingHours(employeeNumber, dt, leaveKind);
        }

        /// <summary>
        /// 檢查該員工剩餘年度延休假時數（扣除「已同意」及「簽核中」)
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="year">查詢年度，以西元年表示</param>
        /// <returns>該員工剩餘年度延休假時數</returns>
        public int GetPostponedLeaveRemainingHours(string employeeNumber, DateTime dt)
        {
            LeaveKindEnum leaveKind = LeaveKindEnum.PostponedLeave;
            //DateTime dt = new DateTime(year, 1, 1);

            return GetRemainingHours(employeeNumber, dt, leaveKind);
        }

        private int GetRemainingHours(string employeeNumber, DateTime dt, LeaveKindEnum leaveKind)
        {
            int subItem = 0;
            try
            {
                GetEmployeeLeaveInfo(employeeNumber, dt, leaveKind, subItem,
                        out int yearAvailableHours, out int yearUsedHours,
                        out int _, out int _,
                        out int _, out int _,
                        out int _);
                return yearAvailableHours - yearUsedHours;
            }
            catch (SqlException ex)
            {
                throw new DataException($"資料存取錯誤\nCode: {ex.ErrorCode}, Message: {ex.Message}");
            }
            catch (Exception ex)
            {
                throw new DataException($"資料存取錯誤\nMessage: {ex.Message}");
            }
        }

        #endregion

        #region GetCompensatoryLeaveRemainingHours

        /// <summary>
        /// 取得補修日(含時間)前可補休的時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="applyDateTime">指定的補休時間</param>
        /// <returns></returns>
        /// <returns></returns>
        public int GetCompensatoryLeaveRemainingHours(string employeeNumber, DateTime applyDateTime)
        {
            var storedProcedureName = "sp_GetCompensatoryLeaveRemainingHours";

            List<SqlParameter> parameters = new List<SqlParameter>();

            SqlParameter pEmpNumber = new SqlParameter("@EmpNo", SqlDbType.VarChar, 4);
            pEmpNumber.Value = employeeNumber;
            parameters.Add(pEmpNumber);

            SqlParameter pDate = new SqlParameter("@QDate", SqlDbType.DateTime, 5);
            pDate.Value = applyDateTime;
            parameters.Add(pDate);

            SqlParameter pRemainingHours = new SqlParameter("@RemainingHours", SqlDbType.Int);
            pRemainingHours.Direction = ParameterDirection.Output;
            parameters.Add(pRemainingHours);

            SqlHelper.GetStatusFromStoredProcedure(_connectionString, storedProcedureName, parameters,
                out int _);

            return pRemainingHours.Value as int? ?? default(int);
        }

        #endregion

        #region MenstrualLeave Related

        /// <summary>
        /// 是否指定月份已經申請生理假
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="year">查詢年度，以西元年表示</param>
        /// <param name="month">查詢月份</param>
        /// <returns>是否已申請</returns>
        public bool IsMenstrualLeaveAlreadyTaken(string employeeNumber, int year, int month)
        {
            LeaveKindEnum leaveKind = LeaveKindEnum.MenstrualLeave;
            DateTime dt = new DateTime(year, month, 1);
            int subItem = 0;

            try
            {
                GetEmployeeLeaveInfo(employeeNumber, dt, leaveKind, subItem,
                        out int _, out int _,
                        out int _, out int monthUsedHours,
                        out int _, out int _,
                        out int _);
                return monthUsedHours > 0;
            }
            catch (SqlException ex)
            {
                throw new DataException($"資料存取錯誤\nCode: {ex.ErrorCode}, Message: {ex.Message}");
            }
            catch (Exception ex)
            {
                throw new DataException($"資料存取錯誤\nMessage: {ex.Message}");
            }
        }

        /// <summary>
        /// 取得年度已休生理假時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>年度已休生理假時數</returns>
        public int GetMenstrualLeaveYearUsedHours(string employeeNumber, DateTime dt)
        {
            LeaveKindEnum leaveKind = LeaveKindEnum.MenstrualLeave;
            //DateTime dt = new DateTime(year, 1, 1);
            int subItem = 0;

            try
            {
                GetEmployeeLeaveInfo(employeeNumber, dt, leaveKind, subItem,
                        out int _, out int yearUsedHours,
                        out int _, out int _,
                        out int _, out int _,
                        out int _);
                return yearUsedHours;
            }
            catch (SqlException ex)
            {
                throw new DataException($"資料存取錯誤\nCode: {ex.ErrorCode}, Message: {ex.Message}");
            }
            catch (Exception ex)
            {
                throw new DataException($"資料存取錯誤\nMessage: {ex.Message}");
            }
        }

        #endregion

        #region SickLeave Related

        /// <summary>
        /// 取得年度病假統計時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <param name="yearAvailableHours">年度總可用時數</param>
        /// <param name="yearUsedHours">年度已使用時數(簽核中＋已核可)</param>
        /// <param name="yearApprovedHours">年度已核可時數</param>
        public void GetSickLeaveInfo(string employeeNumber, DateTime dt,
            out int yearAvailableHours, out int yearUsedHours, out int yearApprovedHours)
        {
            LeaveKindEnum leaveKind = LeaveKindEnum.SickLeave;
            //DateTime dt = new DateTime(year, 1, 1);
            int subItem = 0;

            GetEmployeeLeaveYearInfo(employeeNumber, leaveKind, dt, subItem,
                out yearAvailableHours, out yearUsedHours, out yearApprovedHours);
        }

        #endregion

        #region PersonalLeave Related

        /// <summary>
        /// 取得年度事假統計時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <param name="yearAvailableHours">年度總可用時數</param>
        /// <param name="yearUsedHours">年度已使用時數(簽核中＋已核可)</param>
        /// <param name="yearApprovedHours">年度已核可時數</param>
        public void GetPersonalLeaveYearInfo(string employeeNumber, DateTime dt,
            out int yearAvailableHours, out int yearUsedHours, out int yearApprovedHours)
        {
            LeaveKindEnum leaveKind = LeaveKindEnum.PersonalLeave;
            //DateTime dt = new DateTime(year, 1, 1);
            int subItem = 0;

            GetEmployeeLeaveYearInfo(employeeNumber, leaveKind, dt, subItem,
                out yearAvailableHours, out yearUsedHours, out yearApprovedHours);
        }

        #endregion

        #region FamilyCareLeave Related

        /// <summary>
        /// 取得年度家庭照顧假統計時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <param name="yearUsedHours">年度已使用時數(簽核中＋已核可)</param>
        /// <param name="yearApprovedHours">年度已核可時數</param>
        public void GetFamilyCareLeaveYearInfo(string employeeNumber, DateTime dt,
            out int yearAvailableHours, out int yearUsedHours, out int yearApprovedHours)
        {
            LeaveKindEnum leaveKind = LeaveKindEnum.FamilyCareLeave;
            //DateTime dt = new DateTime(year, 1, 1);
            int subItem = 0;

            GetEmployeeLeaveYearInfo(employeeNumber, leaveKind, dt, subItem,
                out yearAvailableHours, out yearUsedHours, out yearApprovedHours);
        }

        #endregion

        #region BirthdayLeave Related

        /// <summary>
        /// 取得員工生日假資料，包括查詢年度選擇的生日假方案、是否已經使用生日假B
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="year">查詢年度，以西元年表示</param>
        /// <param name="birthdayWelfare">查詢年度的生日假方案，0-查無資料，1-生日假，2-生日禮券</param>
        /// <param name="isUsed">查詢年度是否已經使用生日假</param>
        public void GetBirthdayLeaveInfo(string employeeNumber, int year, out int birthdayWelfare, out bool isUsed)
        {
            var storedProcedureName = "sp_GetEmpLeaveInfo_BirthdayLeave";
            var applyDateTime = new DateTime(year, 1, 1);

            List<SqlParameter> parameters = new List<SqlParameter>();

            SqlParameter pEmpNumber = new SqlParameter("@EmpNo", SqlDbType.VarChar, 4);
            pEmpNumber.Value = employeeNumber;
            parameters.Add(pEmpNumber);

            SqlParameter pDate = new SqlParameter("@QDate", SqlDbType.DateTime, 5);
            pDate.Value = applyDateTime;
            parameters.Add(pDate);

            SqlParameter pBirthdayWelfare = new SqlParameter("@BirthdayWelfare", SqlDbType.Int);
            pBirthdayWelfare.Direction = ParameterDirection.Output;
            parameters.Add(pBirthdayWelfare);

            SqlParameter pIsUsed = new SqlParameter("@IsUsed", SqlDbType.Int);
            pIsUsed.Direction = ParameterDirection.Output;
            parameters.Add(pIsUsed);

            SqlHelper.GetStatusFromStoredProcedure(_connectionString, storedProcedureName, parameters,
                out int _);

            birthdayWelfare = pBirthdayWelfare.Value as int? ?? default(int);
            isUsed = (pIsUsed.Value as int? ?? default(int)) == 1;
        }


        #endregion

        #region ParentalLeave Related

        /// <summary>
        /// 取得年度育嬰假統計時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <param name="yearAvailableHours">年度總可用時數</param>
        /// <param name="yearUsedHours">年度已使用時數(簽核中＋已核可)</param>
        /// <param name="yearApprovedHours">年度已核可時數</param>
        public void GetParentalLeaveYearInfo(string employeeNumber, DateTime dt,
            out int yearAvailableHours, out int yearUsedHours, out int yearApprovedHours)
        {
            //TODO: 待實作
            LeaveKindEnum leaveKind = LeaveKindEnum.ParentalLeave;
            int subItem = 0;

            GetEmployeeLeaveYearInfo(employeeNumber, leaveKind, dt, subItem,
                out yearAvailableHours, out yearUsedHours, out yearApprovedHours);
        }
        #endregion

        #region Tools

        private void GetEmployeeLeaveYearInfo(string employeeNumber, LeaveKindEnum leaveKind, DateTime dt, int subItem, out int yearAvailableHours, out int yearUsedHours, out int yearApprovedHours)
        {
            try
            {
                GetEmployeeLeaveInfo(employeeNumber, dt, leaveKind, subItem,
                        out yearAvailableHours, out yearUsedHours,
                        out int _, out int _,
                        out yearApprovedHours, out int _,
                        out int _);
                return;
            }
            catch (SqlException ex)
            {
                throw new DataException($"資料存取錯誤\nCode: {ex.ErrorCode}, Message: {ex.Message}");
            }
            catch (Exception ex)
            {
                throw new DataException($"資料存取錯誤\nMessage: {ex.Message}");
            }
        }


        private int GetEmployeeLeaveInfo(string employeeNumber, DateTime dt, LeaveKindEnum leaveKind, int subItem,
                    out int yearAvailableHours, out int yearUsedHours,
                    out int sumToMonthUsedHours, out int monthUsedHours,
                    out int yearApprovedHours, out int sumToMonthApprovedHours,
                    out int monthApprovedHours)
        {
            var storedProcedureName = "sp_GetEmpLeaveInfo";

            List<SqlParameter> parameters = new List<SqlParameter>();

            SqlParameter pEmpNumber = new SqlParameter("@EmpNo", SqlDbType.VarChar, 4);
            pEmpNumber.Value = employeeNumber;
            parameters.Add(pEmpNumber);

            SqlParameter pDate = new SqlParameter("@QDate", SqlDbType.DateTime, 5);
            pDate.Value = dt;
            parameters.Add(pDate);

            SqlParameter pLeaveNumber = new SqlParameter("@R1_NO", SqlDbType.VarChar, 2);
            pLeaveNumber.Value = $"{((int)leaveKind).ToString("00")}";
            parameters.Add(pLeaveNumber);

            SqlParameter pSubItem = new SqlParameter("@R2_NO", SqlDbType.VarChar, 2);
            pSubItem.Value = subItem == 0 ? string.Empty : $"{subItem.ToString("00")}";
            parameters.Add(pSubItem);

            SqlParameter pYearAvailableHours = new SqlParameter("@YearAvailableHours", SqlDbType.Int);
            pYearAvailableHours.Value = 1;
            pYearAvailableHours.Direction = ParameterDirection.Output;
            parameters.Add(pYearAvailableHours);

            SqlParameter pYearUsedHours = new SqlParameter("@YearUsedHours", SqlDbType.Int);
            pYearUsedHours.Direction = ParameterDirection.Output;
            parameters.Add(pYearUsedHours);

            SqlParameter pSumToMonthUsedHours = new SqlParameter("@SumToMonthUsedHours", SqlDbType.Int);
            pSumToMonthUsedHours.Direction = ParameterDirection.Output;
            parameters.Add(pSumToMonthUsedHours);

            SqlParameter pMonthUsedHours = new SqlParameter("@MonthUsedHours", SqlDbType.Int);
            pMonthUsedHours.Direction = ParameterDirection.Output;
            parameters.Add(pMonthUsedHours);

            SqlParameter pYearApprovedHours = new SqlParameter("@YearApprovedHours", SqlDbType.Int);
            pYearApprovedHours.Direction = ParameterDirection.Output;
            parameters.Add(pYearApprovedHours);

            SqlParameter pSumToMonthApprovedHours = new SqlParameter("@SumToMonthApprovedHours", SqlDbType.Int);
            pSumToMonthApprovedHours.Direction = ParameterDirection.Output;
            parameters.Add(pSumToMonthApprovedHours);

            SqlParameter pMonthApprovedHours = new SqlParameter("@MonthApprovedHours", SqlDbType.Int);
            pMonthApprovedHours.Direction = ParameterDirection.Output;
            parameters.Add(pMonthApprovedHours);

            int returnValue = SqlHelper.GetStatusFromStoredProcedure(_connectionString, storedProcedureName, parameters, out int affectedRecords);

            yearAvailableHours = pYearAvailableHours.Value as int? ?? default(int);
            yearUsedHours = pYearUsedHours.Value as int? ?? default(int);
            sumToMonthUsedHours = pSumToMonthUsedHours.Value as int? ?? default(int);
            monthUsedHours = pMonthUsedHours.Value as int? ?? default(int);
            yearApprovedHours = pYearApprovedHours.Value as int? ?? default(int);
            sumToMonthApprovedHours = pSumToMonthApprovedHours.Value as int? ?? default(int);
            monthApprovedHours = pMonthApprovedHours.Value as int? ?? default(int);

            return returnValue;
        }

        #endregion
    }
}
