﻿﻿using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Diagnostics.CodeAnalysis;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves
{
    /// <summary>
    /// 延休假類別
    /// </summary>
    [ExcludeFromCodeCoverage]
    [LeaveKind(LeaveKindEnum.PostponedLeave)]
    public class PostponedLeave : C1CardBase
    {
        #region CheckResult

        public const int CodeExceedQuota = 3014301;
        private readonly CardStatusEnum _statusExceedQuota = CardStatusEnum.Error;
        private readonly string _messagePatternExceedQuota = "您本次請假 {0} 小時，年度延休假剩餘 {1} 小時，不得超假";

        #endregion

        /// <summary>
        /// 延休假建構子
        /// </summary>
        /// <param name="c1Card"></param>
        /// <param name="c1CardBo"></param>
        public PostponedLeave(C1Card c1Card, IC1CardBo c1CardBo) : base(c1Card, c1CardBo)
        {
        }

        /// <summary>
        /// 檢查是否能請此假
        /// </summary>
        /// <returns></returns>
        internal override CardCheckResult CanTakeThisLeave()
        {
            // 是否已經建立休假資料
            if (!IfLeaveRecordExists())
            {
                return ResultLeaveRecordNotCreatedYet;
            }

            return ResultOk;
        }

        /// <summary>
        /// 檢查是否超假
        /// </summary>
        /// <returns></returns>
        internal override CardCheckResult CheckOverPermittedLeaveHours()
        {
            var postponedLeaveRemainingHours = GetPostponedLeaveRemainingHours();
            if (postponedLeaveRemainingHours < TotalHours)
            {
                return new CardCheckResult(CodeExceedQuota, _statusExceedQuota,
                    string.Format(_messagePatternExceedQuota, TotalHours, postponedLeaveRemainingHours));
            }
            return ResultOk;
        }

        /// <summary>
        /// 檢查必要欄位
        /// </summary>
        /// <returns></returns>
        internal override CardCheckResult CheckRequiredFields()
        {
            var result = base.CheckRequiredFields();
            if (result != ResultOk)
            {
                return result;
            }

            return ResultOk;
        }

        /// <summary>
        /// 計算最早可請假日期與最晚可請假日期
        /// </summary>
        /// <param name="date">請假日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public override (DateTime, DateTime) CalculateLeavePermittedPeriod(DateTime date, string empNo) => DefaultCalculateLeavePermittedPeriod(date, empNo);

        /// <summary>
        /// 此性別是否可請此假別
        /// </summary>
        /// <param name="gender"></param>
        /// <returns></returns>
        public static bool IsAllowForThisGender(Gender gender)
        {
            return true;
        }

    }

}
