﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Sinotech.Mis.HR.Attendance.Common
{
    public interface IA1CardBo : ICardBaseBo
    {

        /// <summary>加入正常工作卡</summary>
        /// <param name="creatorId">填表人 員工編號</param>
        /// <param name="a1Card">正常工作卡</param>
        /// <param name="ipAddress">IP Address</param>
        /// <param name="hostname">Host name</param>
        /// <returns>Form Guid</returns>
        public Task<(Guid?, string)> AddA1Card(string creatorId, A1Card a1Card, string ipAddress,
            string hostname);

        /// <summary>
        /// 檢查某員工是否已填某旬之有效（簽核中及同意）正常工作卡
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="chineseYearMonth">民國年月(yyymm)</param>
        /// <param name="tenDays">旬別(1:上旬、2:中旬、3:下旬)</param>
        /// <returns>若未填則傳回 {FormUID: null, FormNo: null}</returns>
        public FormFilled IsFilled(string empNo, string chineseYearMonth, char tenDays);

        /// <summary>
        /// 更新A1Card，限管理員使用，僅允許修改計畫時數
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="cards"></param>
        /// <param name="ipAddress"></param>
        /// <param name="hostname"></param>
        /// <returns></returns>
        public CardCheckResult UpdateA1CardProjectNo(string userId, List<A1CardForm> cards, string ipAddress, string hostname);

    }
}
