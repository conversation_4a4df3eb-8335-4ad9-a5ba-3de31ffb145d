<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>disable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>False</GenerateDocumentationFile>
  </PropertyGroup>

	<ItemGroup>
    	<ProjectReference Include="..\Sinotech.Mis.HR.Attendance.Common\Sinotech.Mis.HR.Attendance.Common.csproj" />
    	<PackageReference Include="MaxMind.GeoIP2" Version="5.2.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.Negotiate" Version="8.0.10" />
		<PackageReference Include="System.Security.Cryptography.Csp" Version="4.3.0" />
		<PackageReference Include="UAParser.Core" Version="4.0.4" />
  </ItemGroup>

</Project>
