﻿using Sinotech.Mis.Common;
using System;
using System.Collections.Generic;

namespace Sinotech.Mis.HR.Attendance.Common
{
    public interface IFormFlowBo
    {
        /// <summary>
        /// 執行長關卡
        /// </summary>
        /// <param name="form">表單</param>
        /// <returns>總關卡數</returns>
        int FlowAddCEO(Form form);

        /// <summary>
        /// 董事長關卡
        /// </summary>
        /// <param name="form">表單</param>
        /// <returns>總關卡數</returns>
        int FlowAddChairman(Form form);

        /// <summary>
        /// 流程加入部門登記桌，若無登記桌則跳過
        /// </summary>
        /// <param name="form">申請單</param>
        /// <param name="employee">員工詳細資料</param>
        /// <param name="isNotification">是否為通知，預設否</param>
        /// <returns>表單關卡總數</returns>
        int FlowAddDepartmentMailroom(Form form, Employee employee, bool isNotification = false);

        /// <summary>
        /// 副執行長關卡
        /// </summary>
        /// <param name="form">表單</param>
        /// <returns>總關卡數</returns>
        int FlowAddDeputyCEO(Form form);

        /// <summary>
        ///   <para>
        /// 流程加入部門主管</para>
        /// </summary>
        /// <param name="form">The forms.</param>
        /// <param name="employee">The employeeWatcher.</param>
        /// <returns>
        ///  總關卡數
        /// </returns>
        int FlowAddManager(Form form, Employee employee);

        /// <summary>
        /// 加入通知代理人流程
        /// </summary>
        /// <param name="form">The forms.</param>
        /// <param name="substitute">代理人</param>
        /// <returns>總關卡數</returns>
        int FlowAddNotifySubstitute(Form form, Employee substitute);

        /// <summary>流程加入加會人員</summary>
        /// <param name="form">The forms.</param>
        /// <param name="signers">The signers.</param>
        /// <returns>加會關卡數</returns>
        int FlowAddSigners(Form form, string[] signers);

        /// <summary>
        /// Deduplicate the flows. 刪除所有重覆流程，包括不連續者
        /// </summary>
        /// <param name="flows">The flows.</param>
        /// <returns></returns>
        List<FormFlow> FlowDedup(List<FormFlow> flows);

        /// <summary>
        /// Deduplicate the continuous flows. 刪除所有連續重覆流程
        /// </summary>
        /// <param name="flows">The flows.</param>
        /// <returns></returns>
        List<FormFlow> FlowDedupContinuous(List<FormFlow> flows);

        /// <summary>
        /// 產生 <see cref="FormFlow" />流程物件，在此不會修改原 form的 Flows
        /// </summary>
        /// <param name="form">表單</param>
        /// <param name="recipient">收件人</param>
        /// <param name="flowName">流程名稱</param>
        /// <param name="deptNo">部門編號</param>
        /// <param name="deptSName">部門簡稱</param>
        /// <param name="roleName">角色名稱</param>
        /// <param name="isNotification">是否為通知</param>
        /// <returns>
        /// FormFlow物件
        /// </returns>
        FormFlow GenerateFlowDto(Form form, string recipient, string flowName, int deptNo, string deptSName, string roleName, bool isNotification = false);

        /// <summary>
        /// 依照部門編號取得副執行長關卡資訊
        /// </summary>
        /// <param name="deptNo">部門編號</param>
        /// <returns>角色ID, 關卡名稱，角色名稱</returns>
        public (string roleId, string stageName, string roleName) GetDeputyCEOStageInfo(int deptNo);

        /// <summary>Gets the name of the flow status.</summary>
        /// <param name="flowStatus">The flow status.</param>
        /// <returns>Flow Status FormName</returns>
        string GetFlowStatusName(int flowStatus);

        /// <summary>
        /// 由FormUID取得流程
        /// </summary>
        /// <param name="formUID"></param>
        /// <param name="step"></param>
        /// <returns></returns>
        FormFlow? GetFormFlow(Guid formUID, int step);

        /// <summary>由FlowUID取得流程 FormFLow</summary>
        /// <param name="flowUID">The flow forms.</param>
        /// <returns>DataTable FormFlow</returns>
        FormFlow GetFormFlowByFlowUID(Guid flowUID);

        /// <summary>取得 FormFLow List</summary>
        /// <param name="formUID">The forms forms.</param>
        /// <returns>List of FormFlow</returns>
        List<FormFlow> GetFormFlows(Guid formUID);

    }
}