﻿using Sinotech.Mis.Helpers;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.Utilities.DataAccess.Interfaces;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Runtime.Caching;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic
{
    /// <summary>
    /// 工作日 商業物件
    /// </summary>
    public class WorkdayBo : IWorkdayBo
    {

        private static ObjectCache _cache = MemoryCache.Default;
        private readonly static object CacheLock = new object();
        private readonly IWorkdayDao _workdayDao;

        /// <summary>
        /// Initializes a new instance of the <see cref="WorkdayBo"/> class.
        /// </summary>
        /// <param name="workdayDao">The workday DAO.</param>
        public WorkdayBo(IWorkdayDao workdayDao)
        {
            _workdayDao = workdayDao;
        }

        /// <summary>
        /// 調整下班時間為 DepartureTime (正常班 17:00)
        /// </summary>
        /// <param name="workday"></param>
        /// <returns></returns>
        private static DateTime AdjustOffDutyTime(Workday workday)
        {
            DateTime dateTime = new DateTime(workday.WorkDate.Year, workday.WorkDate.Month, workday.WorkDate.Day, workday.DepartureTime.Hour, 0, 0, DateTimeKind.Local);
            return dateTime;
        }

        /// <summary>
        /// 調整上班時間由 ArrivalTime (正常班 08:00) 開始
        /// </summary>

        /// <param name="workday"></param>
        /// <returns></returns>
        private static DateTime AdjustOnDutyTime(Workday workday)
        {
            DateTime dateTime = new DateTime(workday.WorkDate.Year, workday.WorkDate.Month, workday.WorkDate.Day, workday.ArrivalTime.Hour, 0, 0, DateTimeKind.Local);
            return dateTime;
        }

        /// <summary>
        /// 從 List 中找出特定一筆 Workday
        /// </summary>
        /// <param name="workdays"></param>
        /// <param name="date"></param>
        /// <returns></returns>
        private static Workday? GetWorkdayFromList(List<Workday> workdays, DateTime date)
        {
            Workday? workday = null;
            var rows = from x in workdays
                       where x.WorkDate == date
                       select x;
            if (rows.Any())
            {
                workday = rows.First();
            }
            return workday;
        }

        /// <summary>
        /// Cache Policy
        /// </summary>
        private static CacheItemPolicy CachePolicy
        {
            get
            {
                CacheItemPolicy policy = new CacheItemPolicy();
                policy.AbsoluteExpiration = DateTimeOffset.Now.AddMinutes(2.0);
                return policy;
            }
        }

        /// <summary>
        /// 此處的 days 只允許 正整數
        /// </summary>
        /// <param name="startDate">開始日期</param>
        /// <param name="days">天數</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public DateTime AddWorkDays(DateTime startDate, int days, string empNo)
        {
            DateTime date = startDate;
            DateTime endDate = startDate.AddDays(days + 60); // 為避免太多假日，抓個Buffer 60天
            int countDays = 0;
            List<Workday> workdays = GetEmpWorkdaysDateRange(startDate, endDate, empNo);
            foreach (Workday workday in workdays)
            {
                bool isWorkday = IsWorkday(workday.DayType);

                if (isWorkday) // 工作日才算工時
                {
                    countDays++;
                }

                if (countDays == days)
                {
                    date = workday.WorkDate;
                    break;
                }
            }
            return date;
        }

        /// <summary>
        /// 此處的 days 只允許 正整數
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="days"></param>
        /// <param name="shiftId"></param>
        /// <returns></returns>
        public DateTime AddWorkDays(DateTime startDate, int days, int shiftId = 1)
        {
            DateTime date = startDate;
            DateTime endDate = startDate.AddDays(days + 60); // 為避免太多假日，抓個Buffer 60天
            int countDays = 0;
            // 以實際日曆天計算整天，要從隔一天開始算
            List<Workday> workdays = GetWorkdaysDateRange(startDate.AddDays(1), endDate, shiftId);
            foreach (Workday workday in workdays)
            {
                bool isWorkday = IsWorkday(workday.DayType);

                if (isWorkday) // 工作日才算工時
                {
                    countDays++;
                }

                if (countDays == days)
                {
                    date = workday.WorkDate;
                    break;
                }
            }
            return date;
        }

        /// <summary>查詢指定期間內之正常工時合計，不包含加班時數，
        /// 每日目前最多8小時，超過則以8小時計算 <br />
        /// 此處計算出若在此區間內請假需幾小時，區間自動排除午休與非上班時段<br />
        /// 求整數時要用  (int) Math.Ceiling(hours); 無條件進位<br />
        /// 此API不適用請假時離開時間與包含中午時段特例的時數計算
        /// </summary>
        /// <param name="startDate">起始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns>工時</returns>
        public double CalculateWorkingHoursDateDiff(DateTime startDate, DateTime endDate, int shiftId = 1)
        {
            double hours = 0.0;

            List<Workday> workdays = GetWorkdaysDateRange(startDate, endDate, shiftId);
            foreach (Workday workday in workdays)
            {
                bool isWorkday = IsWorkday(workday.DayType);
                double normalWorkHoursOfTheDay = workday.WorkHours; // 當日正常工時
                DateTime date = workday.WorkDate;
                DateTime arrivalTime = workday.ArrivalTime;
                DateTime flexibleArrivalBefore = workday.FlexibleArrivalBefore;
                DateTime departureTime = workday.DepartureTime;
                DateTime flexibleDepartureAfter = workday.FlexibleDepartureAfter;
                DateTime middayBreakStart = workday.MiddayBreakStart;
                DateTime middayBreakEnd = workday.MiddayBreakEnd;
                if (isWorkday) // 工作日才算工時
                {
                    if (date == startDate.Date && endDate.Date == startDate.Date) // 起迄同一天
                    {
                        if (startDate < flexibleArrivalBefore)
                        {
                            startDate = flexibleArrivalBefore;
                        }
                        else if (startDate > middayBreakStart && startDate < middayBreakEnd) // 若起介於午休時間
                        {
                            startDate = middayBreakEnd;
                        }

                        if (endDate > flexibleDepartureAfter)
                        {
                            endDate = flexibleDepartureAfter;
                        }
                        else if (endDate > middayBreakStart && endDate < middayBreakEnd) // 若迄介於午休時間
                        {
                            endDate = middayBreakStart;
                        }

                        double hoursDiff = (endDate - startDate).TotalHours;
                        hours = hoursDiff;

                        // 若起早於午休時間，迄晚於午休時間
                        if (startDate <= middayBreakStart && endDate >= middayBreakEnd)
                        {
                            hours -= (middayBreakEnd - middayBreakStart).TotalHours;
                        }
                        if (hours > normalWorkHoursOfTheDay) // 工作時數超過8小時
                        {
                            hours = normalWorkHoursOfTheDay;
                        }
                    }
                    else
                    {
                        double workingHours = 0.0; // 當日工時

                        if (date == startDate.Date)
                        {
                            if (startDate < flexibleArrivalBefore)
                            {
                                startDate = flexibleArrivalBefore;
                            }

                            if (startDate <= arrivalTime)
                            {
                                workingHours += normalWorkHoursOfTheDay;
                            }
                            else
                            {
                                double hoursDiff = (departureTime - startDate).TotalHours;
                                if (startDate <= middayBreakStart)
                                {
                                    hoursDiff -= 1.0;
                                }
                                workingHours += hoursDiff;

                            }
                        }
                        else if (date == endDate.Date)
                        {
                            if (endDate > departureTime)
                            {
                                endDate = departureTime;
                            }

                            double hoursDiff = (endDate - arrivalTime).TotalHours;
                            if (endDate >= arrivalTime)
                            {
                                hoursDiff -= 1.0;
                            }
                            workingHours += hoursDiff;
                        }
                        else
                        {
                            workingHours += normalWorkHoursOfTheDay;
                        }
                        if(workingHours > normalWorkHoursOfTheDay) // 工作時數超過8小時
                        {
                            workingHours = normalWorkHoursOfTheDay;
                        }
                        hours += workingHours;
                    }
                }
            }
            return hours;
        }

        /// <summary>
        /// 取得該日的類型
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="shiftId">班別ID</param>
        /// <returns></returns>
        public WorkdayType GetDayType(DateTime date, int shiftId = 1)
        {
            WorkdayType workdayType;
            string cacheName = $"DayType{date.ToString("yyyyMMdd")}{shiftId}";
            if (_cache.Contains(cacheName))
            {
                workdayType = (WorkdayType)_cache[cacheName];
            }
            else
            {
                workdayType = (WorkdayType)_workdayDao.GetDayType(date, shiftId);
                lock (CacheLock)
                {
                    _cache.Set(cacheName, workdayType, CachePolicy);
                }
            }
            return workdayType;
        }

        // 新版，適用於 非正常班別員工有個人行事曆
        /// <summary>查詢指定期間內員工之行事曆資料。</summary>
        /// <param name="startDate">起始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>List<Workday>(行事曆資料)</returns>
        public List<Workday> GetEmpWorkdaysDateRange(DateTime startDate, DateTime endDate, string empNo)
        {
            List<Workday> list = new List<Workday>();
            string cacheName = $"WorkdaysDateRange{startDate.ToString("yyyyMMdd")}-{endDate.ToString("yyyyMMdd")}-{empNo}";
            if (_cache.Contains(cacheName))
            {
                list = (List<Workday>)_cache[cacheName];
            }
            else
            {
                DataTable dtDefault = _workdayDao.GetWorkdaysDateRange(startDate, endDate);
                DataTable dt = _workdayDao.GetWorkdaysDateRange(startDate, endDate, empNo);
                if (dt.Rows.Count == 0) // 若沒有特殊班別時
                {
                    list = SqlHelper.ConvertDataTable<Workday>(dtDefault);
                }
                else
                {
                    if (dt.Rows.Count == dtDefault.Rows.Count) // 若筆數相同
                    {
                        list = SqlHelper.ConvertDataTable<Workday>(dt);
                    }
                    else // dt 的筆數比 default 少
                    {
                        List<Workday> listDefault = SqlHelper.ConvertDataTable<Workday>(dtDefault);
                        List<Workday> listUser = SqlHelper.ConvertDataTable<Workday>(dtDefault);
                        foreach (Workday workday in listDefault)
                        {
                            Workday? w = GetWorkdayFromList(listUser, workday.WorkDate);
                            if (w != null)
                            {
                                list.Add(w);
                            }
                            else
                            {
                                list.Add(workday);
                            }
                        }
                    }
                }

                lock (CacheLock)
                {
                    _cache.Set(cacheName, list, CachePolicy);
                }
            }
            return list;
        }

        /* // 以下適用每位員工相同班別時上班時間均相同，目前改為非正常班別員工有個人行事曆，故不適用
        /// <summary>查詢指定期間內之行事曆資料。</summary>
        /// <param name="startDate">起始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>List<Workday>(行事曆資料)</returns>
        public List<Workday> GetEmpWorkdaysDateRange(DateTime startDate, DateTime endDate, string empNo)
        {

            List<Workday> list = new List<Workday>();
            startDate = startDate.Date;
            endDate = endDate.Date;
            string cacheName = $"WorkdaysDateRange{startDate.ToString("yyyyMMdd")}-{endDate.ToString("yyyyMMdd")}-{empNo}";
            if (_cache.Contains(cacheName))
            {
                list = (List<Workday>)_cache[cacheName];
            }
            else
            {
                DataTable dtShifts = _workdayDao.GetShiftsDateRange(startDate, endDate, empNo);
                DateTime date = startDate;
                while (date <= endDate)
                {
                    int shiftId = 1;
                    List<EmpWorkShift> shifts = SqlHelper.ConvertDataTable<EmpWorkShift>(dtShifts);
                    var rows = from x in shifts
                               where x.WorkDate == date
                               select x;
                    if (rows.Any())
                    {
                        shiftId = rows.First().ShiftId;
                    }
                    Workday workday = GetWorkday(date, shiftId);
                    list.Add(workday);
                    date = date.AddDays(1);
                }

                lock (CacheLock)
                {
                    _cache.Set(cacheName, list, CachePolicy);
                }
            }
            return list;
        }
        */

        /// <summary>
        /// 某月的日曆天資料
        /// </summary>
        /// <param name="year">西元年</param>
        /// <param name="month">月</param>
        /// <param name="empNo">員工編號/param>
        /// <returns>List<Workday>(日曆天資料)</returns>
        public List<Workday> GetEmpWorkDaysInMonth(int year, int month, string empNo)
        {
            List<Workday> list;
            string cacheName = $"WorkDaysInMonth{year}-{month}-{empNo}";
            if (_cache.Contains(cacheName))
            {
                list = (List<Workday>)_cache[cacheName];
            }
            else
            {
                DateTime startDate = new DateTime(year, month, 1, 0, 0, 0, DateTimeKind.Local);
                DateTime endDate = startDate.AddMonths(1).AddDays(-1);
                list = GetEmpWorkdaysDateRange(startDate, endDate, empNo);
                lock (CacheLock)
                {
                    _cache.Set(cacheName, list, CachePolicy);
                }
            }
            return list;
        }

        /// <summary>取得某日期所屬旬的最後一天工作日之日曆天日期</summary>
        /// <param name="year">The year.</param>
        /// <param name="month">The month.</param>
        /// <param name="day">The day.</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns>最後一天工作日之日曆天日期</returns>
        public DateTime GetLastWorkDayInTenDays(int year, int month, int day, int shiftId = 1)
        {
            DateTime date;
            string cacheName = $"LastWorkDayInTenDays{year}/{month}/{day}-{shiftId}";
            if (_cache.Contains(cacheName))
            {
                date = (DateTime)_cache[cacheName];
            }
            else
            {
                date = _workdayDao.GetLastWorkDayInTenDays(year, month, day, shiftId);
                lock (CacheLock)
                {
                    _cache.Set(cacheName, date, CachePolicy);
                }
            }
            return date;
        }

        /// <summary>取得最多可加班時數</summary>
        /// <param name="date">日期</param>
        /// <param name="shiftId">班別ID，預設 1 正常班</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public int GetMaxOvertimeHours(DateTime date, int shiftId = 1)
        {
            int maxOvertimeHours;
            string cacheName = $"MaxOvertimeHours{date.ToString("yyyyMMdd")}-{shiftId}";
            if (_cache.Contains(cacheName))
            {
                maxOvertimeHours = (int)_cache[cacheName];
            }
            else
            {
                Workday workday = GetWorkday(date, shiftId);

                switch (workday.DayType)
                {
                    case WorkdayType.WeekWorkday: // 1 週間工作日
                    case WorkdayType.MakeUpWorkday: // 7 補班日
                        maxOvertimeHours = Convert.ToInt32(AttendanceParameters.MaxWorkHours - workday.WorkHours);
                        break;
                    case WorkdayType.SundayRegularHoliday: // 3 週日例假日
                    case WorkdayType.SundayHoliday: // 6 週日國定假日
                        maxOvertimeHours = 0;
                        break;
                    default:
                        maxOvertimeHours = Convert.ToInt32(AttendanceParameters.MaxWorkHours);
                        break;
                }
                lock (CacheLock)
                {
                    _cache.Set(cacheName, maxOvertimeHours, CachePolicy);
                }
            }
            return maxOvertimeHours;
        }

        /// <summary>取得最少可加班時數</summary>
        /// <param name="date">日期</param>
        /// <param name="shiftId">班別ID，預設 1 正常班</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public int GetMinOvertimeHours(DateTime date, int shiftId = 1)
        {
            int minOvertimeHours;
            string cacheName = $"MinOvertimeHours{date.ToString("yyyyMMdd")}-{shiftId}";
            if (_cache.Contains(cacheName))
            {
                minOvertimeHours = (int)_cache[cacheName];
            }
            else
            {
                Workday workday = GetWorkday(date, shiftId);

                switch (workday.DayType)
                {
                    case WorkdayType.WeekWorkday: // 1 週間工作日
                    case WorkdayType.MakeUpWorkday: // 7 補班日
                        minOvertimeHours = AttendanceParameters.MinOvertimeHours;
                        break;
                    case WorkdayType.SundayRegularHoliday: // 3 週日例假日
                    case WorkdayType.SundayHoliday: // 6 週日國定假日
                        minOvertimeHours = 0; //禁止加班
                        break;
                    case WorkdayType.WeekHoliday: //4 週間國定假日
                    case WorkdayType.MakeUpHoliday: //補假日
                                                    //此處改為加班申請卡專用，加班卡改呼叫 GetMinPaidOvertimeHours
                        minOvertimeHours = AttendanceParameters.MinOvertimeHours;
                        break;
                    default:
                        minOvertimeHours = AttendanceParameters.MinOvertimeHours;
                        break;
                }
                lock (CacheLock)
                {
                    _cache.Set(cacheName, minOvertimeHours, CachePolicy);
                }
            }
            return minOvertimeHours;
        }

        /// <summary>取得給薪最少加班時數</summary>
        /// <param name="date">日期</param>
        /// <param name="shiftId">班別ID，預設 1 正常班</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public int GetMinPaidOvertimeHours(DateTime date, int shiftId = 1)
        {
            int minPaidOvertimeHours;
            string cacheName = $"MinPaidOvertimeHours{date.ToString("yyyyMMdd")}-{shiftId}";
            if (_cache.Contains(cacheName))
            {
                minPaidOvertimeHours = (int)_cache[cacheName];
            }
            else
            {
                Workday workday = GetWorkday(date, shiftId);

                switch (workday.DayType)
                {
                    case WorkdayType.WeekWorkday: // 1 週間工作日
                    case WorkdayType.MakeUpWorkday: // 7 補班日
                        minPaidOvertimeHours = AttendanceParameters.MinOvertimeHours;
                        break;
                    case WorkdayType.SundayRegularHoliday: // 3 週日例假日
                    case WorkdayType.SundayHoliday: // 6 週日國定假日
                        minPaidOvertimeHours = 0; //禁止加班
                        break;
                    case WorkdayType.WeekHoliday: //4 週間國定假日
                    case WorkdayType.MakeUpHoliday: //補假日
                        minPaidOvertimeHours = AttendanceParameters.GeneralWorkingHours;
                        break;
                    default:
                        minPaidOvertimeHours = AttendanceParameters.MinOvertimeHours;
                        break;
                }
                lock (CacheLock)
                {
                    _cache.Set(cacheName, minPaidOvertimeHours, CachePolicy);
                }
            }
            return minPaidOvertimeHours;
        }

        /// <summary>
        /// 整月工時
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="shiftId">工作班別ID，預設 1 正常班</param>
        /// <returns>整月工時合計</returns>
        public int GetMonthWorkHours(DateTime date, int shiftId = 1)
        {
            int monthWorkHours;
            string cacheName = $"MonthWorkHours{date.ToString("yyyyMMdd")}-{shiftId}";
            if (_cache.Contains(cacheName))
            {
                monthWorkHours = (int)_cache[cacheName];
            }
            else
            {
                int year = date.Year;
                int month = date.Month;
                monthWorkHours = GetMonthWorkHours(year, month, shiftId);
                lock (CacheLock)
                {
                    _cache.Set(cacheName, monthWorkHours, CachePolicy);
                }
            }
            return monthWorkHours;
        }


        /// <summary>
        /// 整月工時
        /// </summary>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <param name="shiftId">班別ID，預設 1 正常班</param>
        /// <returns>整月工時合計</returns>
        public int GetMonthWorkHours(int year, int month, int shiftId = 1)
        {
            int monthWorkHours;
            string cacheName = $"MonthWorkHours{year}/{month}-{shiftId}";
            if (_cache.Contains(cacheName))
            {
                monthWorkHours = (int)_cache[cacheName];
            }
            else
            {
                monthWorkHours = _workdayDao.GetMonthWorkHours(year, month);
                lock (CacheLock)
                {
                    _cache.Set(cacheName, monthWorkHours, CachePolicy);
                }
            }
            return monthWorkHours;
        }

        /// <summary>
        /// 取得該日的詳細資料
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns></returns>
        public Workday GetWorkday(DateTime date, int shiftId = 1)
        {
            Workday workday = new Workday();
            string cacheName = $"Workday{date.ToString("yyyyMMdd")}-{shiftId}";
            if (_cache.Contains(cacheName))
            {
                workday = (Workday)_cache[cacheName];
            }
            else
            {
                DataTable dt = _workdayDao.GetWorkday(date, shiftId);
                if (dt != null && dt.Rows.Count > 0)
                {
                    DataRow row = dt.Rows[0];
                    workday.ShiftId = shiftId;
                    workday.TypeName = (string)row["TypeName"];
                    workday.DayOff = (bool)row["DayOff"];
                    workday.WorkDate = date;
                    byte b = (byte)row["DayType"];
                    workday.DayType = (WorkdayType)b;
                    workday.ArrivalTime = (DateTime)row["ArrivalTime"];
                    workday.DepartureTime = (DateTime)row["DepartureTime"];
                    workday.FlexibleArrivalBefore = (DateTime)row["FlexibleArrivalBefore"];
                    workday.FlexibleArrivalAfter = (DateTime)row["FlexibleArrivalAfter"];
                    workday.FlexibleDepartureBefore = (DateTime)row["FlexibleDepartureBefore"];
                    workday.FlexibleDepartureAfter = (DateTime)row["FlexibleDepartureAfter"];
                    workday.MiddayBreakStart = (DateTime)row["MiddayBreakStart"];
                    workday.MiddayBreakEnd = (DateTime)row["MiddayBreakEnd"];
                    workday.MorningRestStart = (DateTime)row["MorningRestStart"];
                    workday.MorningRestEnd = (DateTime)row["MorningRestEnd"];
                    workday.AfternoonRestStart = (DateTime)row["AfternoonRestStart"];
                    workday.AfternoonRestEnd = (DateTime)row["AfternoonRestEnd"];
                    workday.WorkHours = (double)row["WorkHours"];
                    workday.WeekDay = (byte)row["WeekDay"];
                    if (row["Comment"] != DBNull.Value)
                    {
                        workday.Comment = (string)row["Comment"];
                    }
                }
                lock (CacheLock)
                {
                    _cache.Set(cacheName, workday, CachePolicy);
                }
            }
            return workday;
        }

        /// <summary>查詢指定期間內之行事曆資料。</summary>
        /// <param name="startDate">起始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns>List<Workday>(行事曆資料)</returns>
        public List<Workday> GetWorkdaysDateRange(DateTime startDate, DateTime endDate, int shiftId = 1)
        {
            List<Workday> list;
            string cacheName = $"WorkdaysDateRange{startDate.ToString("yyyyMMdd")}-{endDate.ToString("yyyyMMdd")}-{shiftId}";
            if (_cache.Contains(cacheName))
            {
                list = (List<Workday>)_cache[cacheName];
            }
            else
            {
                DataTable dt = _workdayDao.GetWorkdaysDateRange(startDate, endDate, shiftId);
                list = SqlHelper.ConvertDataTable<Workday>(dt);

                lock (CacheLock)
                {
                    _cache.Set(cacheName, list, CachePolicy);
                }
            }
            return list;
        }

        /// <summary>
        /// 某月的日曆天資料
        /// </summary>
        /// <param name="year">西元年</param>
        /// <param name="month">月</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns>DataTable(日曆天資料)</returns>
        public DataTable GetWorkDaysInMonth(int year, int month, int shiftId = 1)
        {
            DataTable dt;
            string cacheName = $"WorkDaysInMonth{year}-{month}-{shiftId}";
            if (_cache.Contains(cacheName))
            {
                dt = (DataTable)_cache[cacheName];
            }
            else
            {
                dt = _workdayDao.GetWorkDaysInMonth(year, month, shiftId);
                lock (CacheLock)
                {
                    _cache.Set(cacheName, dt, CachePolicy);
                }
            }
            return dt;
        }

        /// <summary>
        /// Gets the work days in month list.
        /// </summary>
        /// <param name="year">The year.</param>
        /// <param name="month">The month.</param>
        /// <param name="shiftId">The shift identifier.</param>
        /// <returns></returns>
        public List<Workday> GetWorkDaysInMonthList(int year, int month, int shiftId = 1)
        {
            List<Workday> workdays;
            string cacheName = $"WorkDaysInMonthList{year}-{month}-{shiftId}";
            if (_cache.Contains(cacheName))
            {
                workdays = (List<Workday>)_cache[cacheName];
            }
            else
            {
                DataTable dt = _workdayDao.GetWorkDaysInMonth(year, month, shiftId);
                workdays = SqlHelper.CreateListFromTable<Workday>(dt);
                lock (CacheLock)
                {
                    _cache.Set(cacheName, workdays, CachePolicy);
                }
            }
            return workdays;
        }

        /// <summary>取得某日期所屬旬的最後一天工作日之日期</summary>
        /// <param name="year">西元年</param>
        /// <param name="month">月</param>
        /// <param name="tenDays">旬別(1:上旬、2:中旬、3:下旬)</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns>DataTable(日曆天資料)</returns>
        public DataTable GetWorkdaysInTendays(int year, int month, int tenDays, int shiftId = 1)
        {
            DataTable dt;
            string cacheName = $"DataTableWorkdaysInTendays{year}-{month}-{tenDays}-{shiftId}";
            if (_cache.Contains(cacheName))
            {
                dt = (DataTable)_cache[cacheName];
            }
            else
            {
                dt = _workdayDao.GetWorkdaysInTendays(year, month, tenDays, shiftId);
                lock (CacheLock)
                {
                    _cache.Set(cacheName, dt, CachePolicy);
                }
            }
            return dt;
        }

        public string GetWorkdayTypeName(WorkdayType dayType, int shiftId = 1)
        {
            string typeName = string.Empty;
            string cacheName = $"WorkdayTypeDT-{shiftId}";
            DataTable dtWorkdayType;
            if (_cache.Contains(cacheName))
            {
                dtWorkdayType = (DataTable)_cache[cacheName];
            }
            else
            {
                dtWorkdayType = _workdayDao.GetWorkdayTypes(shiftId);
                lock (CacheLock)
                {
                    _cache.Set(cacheName, dtWorkdayType, CachePolicy);
                }
            }
            int iType = (int)dayType;
            foreach (DataRow dr in dtWorkdayType.Rows)
            {
                int i = (byte)dr["TypeId"];
                if (i == iType)
                {
                    typeName = (string)dr["TypeName"];
                    break;
                }
            }

            return typeName;
        }

        /// <summary>
        /// 開始日不算，幾工作日之內
        /// </summary>
        /// <param name="startDate">The start date.</param>
        /// <param name="endDate">The end date.</param>
        /// <param name="days">天數</param>
        /// <param name="shiftId">The shift identifier.</param>
        /// <returns></returns>
        public bool InWorkdays(DateTime startDate, DateTime endDate, int days, int shiftId = 1)
        {
            bool ret = false;
            startDate = new DateTime(startDate.Year, startDate.Month, startDate.Day, 0, 0, 0, DateTimeKind.Local);
            endDate = new DateTime(endDate.Year, endDate.Month, endDate.Day, 0, 0, 0, DateTimeKind.Local);

            if (startDate <= endDate)
            {
                int diffDays = WorkdaysRangeDiff(startDate, endDate, shiftId);
                if (diffDays > 0 && diffDays < days)
                {
                    ret = true;
                }
                else if (diffDays == 0)
                {
                    ret = true;
                }
                else if (diffDays == days && IsWorkday(endDate))
                {
                    ret = true;
                }
                else if (diffDays > days)
                {
                    ret = false;
                }

            }
            return ret;
        }

        /// <summary>
        /// 是否為該旬最後一個工作日
        /// </summary>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <param name="day">日</param>
        /// <param name="shiftId">The shift identifier.</param>
        /// <returns>
        ///   <c>true</c> if [is last work day in ten days]; otherwise, <c>false</c>.
        /// </returns>
        public bool IsLastWorkDayInTenDays(int year, int month, int day, int shiftId = 1)
        {
            return (day == GetLastWorkDayInTenDays(year, month, day, shiftId).Day);
        }

        /// <summary>
        /// Determines whether the specified day type is workday.
        /// </summary>
        /// <param name="dayType">Type of the day.</param>
        /// <returns>
        ///   <c>true</c> if the specified day type is workday; otherwise, <c>false</c>.
        /// </returns>
        public bool IsWorkday(WorkdayType dayType)
        {
            bool isWorkday;
            switch (dayType)
            {
                case WorkdayType.WeekWorkday:
                case WorkdayType.MakeUpWorkday:
                    isWorkday = true;
                    break;
                default:
                    isWorkday = false;
                    break;
            }

            return isWorkday;
        }

        /// <summary>
        /// 是否為工作日
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public bool IsWorkday(DateTime date, string empNo)
        {
            bool isWorkday;
            string cacheName = $"IsWorkday{date.ToString("yyyyMMdd")}-{empNo}";
            if (_cache.Contains(cacheName))
            {
                isWorkday = (bool)_cache[cacheName];
            }
            else
            {
                WorkdayType dayType = (WorkdayType)_workdayDao.GetDayType(date, empNo);
                isWorkday = IsWorkday(dayType);
                lock (CacheLock)
                {
                    _cache.Set(cacheName, isWorkday, CachePolicy);
                }
            }
            return isWorkday;
        }

        /// <summary>
        /// 是否為工作日
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="shiftId">班別ID</param>
        /// <returns>
        ///   <c>true</c> if the specified date is workday; otherwise, <c>false</c>.
        /// </returns>
        public bool IsWorkday(DateTime date, int shiftId = 1)
        {
            bool isWorkday;
            string cacheName = $"IsWorkday{date.ToString("yyyyMMdd")}-{shiftId}";
            if (_cache.Contains(cacheName))
            {
                isWorkday = (bool)_cache[cacheName];
            }
            else
            {
                WorkdayType dayType = (WorkdayType)_workdayDao.GetDayType(date, shiftId);
                isWorkday = IsWorkday(dayType);
                lock (CacheLock)
                {
                    _cache.Set(cacheName, isWorkday, CachePolicy);
                }
            }
            return isWorkday;
        }

        /// <summary>該日是否為工作日</summary>
        /// <param name="year">The year.</param>
        /// <param name="month">The month.</param>
        /// <param name="day">The day.</param>
        /// <param name="shiftId">The shift identifier.</param>
        /// <returns>
        ///   <c>true</c> if the specified year is workday; otherwise, <c>false</c>.</returns>
        public bool IsWorkday(int year, int month, int day, int shiftId = 1)
        {
            DateTime date = new DateTime(year, month, day, 0, 0, 0, DateTimeKind.Local);
            return IsWorkday(date, shiftId);
        }

        /// <summary>
        /// 某旬中最後一日曆天
        /// </summary>
        /// <param name="year">西元年</param>
        /// <param name="month">月</param>
        /// <param name="tenDays">旬</param>
        /// <returns></returns>
        public int LastDayInTenDays(int year, int month, int tenDays)
        {
            if (tenDays == 1)
            {
                return 10;
            }
            else if (tenDays == 2)
            {
                return 20;
            }
            else if (tenDays == 3)
            {
                if (month == 12)
                {
                    DateTime date = new DateTime(year + 1, 1, 1, 0, 0, 0, DateTimeKind.Local).AddDays(-1);
                    return date.Day;
                }
                else
                {
                    DateTime date = new DateTime(year, month + 1, 1, 0, 0, 0, DateTimeKind.Local).AddDays(-1);
                    return date.Day;
                }
            }
            return 0;
        }

        /// <summary>Find the last work day in month.</summary>
        /// <param name="year">The year. 西元年</param>
        /// <param name="month">The month. 月分</param>
        /// <param name="workdays">包含此日期的工作日集合</param>
        /// <returns>最後一個工作天的日期(int)</returns>
        public DateTime LastWorkDayInMonth(int year, int month, List<Workday> workdays)
        {
            DateTime lastWorkDate = new DateTime(year, month, 1, 0, 0, 0, DateTimeKind.Local);
            lastWorkDate = lastWorkDate.AddMonths(1).AddDays(-1); // 本月份的最後一日
            for (int day = lastWorkDate.Day; day > 0; day--)
            {
                DateTime date = new DateTime(year, month, day, 0, 0, 0, DateTimeKind.Local);
                var rows = from x in workdays
                           where x.WorkDate == date
                           select x;
                if (rows.Any())
                {
                    Workday workday = rows.First();
                    if (IsWorkday(workday.DayType))
                    {
                        lastWorkDate = date; // 同 workday.WorkDate
                        break;
                    }
                }
            }
            return lastWorkDate;
        }

        /// <summary>Find the first work day in month.</summary>
        /// <param name="year">The year. 西元年</param>
        /// <param name="month">The month. 月分</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>第一天工作日的日期(int)</returns>
        public DateTime FirstWorkDayInMonth(int year, int month, string empNo)
        {
            string cacheName = $"FirstWorkDayInMonth-{year}-{month}-{empNo}";
            if (_cache.Contains(cacheName))
            {
                return (DateTime)_cache[cacheName];
            }
            else
            {
                DateTime startDate = new DateTime(year, month, 1, 0, 0, 0, DateTimeKind.Local);
                List<Workday> list = GetEmpWorkdaysDateRange(startDate, startDate.AddMonths(1).AddDays(-1), empNo);

                int firstWorkDay = 28;
                foreach (Workday day in list)
                {
                    if (IsWorkday(day.DayType) && day.WorkDate.Day < firstWorkDay)
                    {
                        firstWorkDay = day.WorkDate.Day;
                    }
                }
                DateTime date = new DateTime(year, month, firstWorkDay, 0, 0, 0, DateTimeKind.Local);
                lock (CacheLock)
                {
                    _cache.Set(cacheName, date, CachePolicy);
                }
                return date;
            }
        }

        /// <summary>Find the last work day in month.</summary>
        /// <param name="year">The year. 西元年</param>
        /// <param name="month">The month. 月分</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>最後一天工作日的日期(int)</returns>
        public DateTime LastWorkDayInMonth(int year, int month, string empNo)
        {
            string cacheName = $"LastWorkDayInMonth-{year}-{month}-{empNo}";
            if (_cache.Contains(cacheName))
            {
                return (DateTime)_cache[cacheName];
            }
            else
            {
                DateTime startDate = new DateTime(year, month, 1, 0, 0, 0, DateTimeKind.Local);
                List<Workday> list = GetEmpWorkdaysDateRange(startDate, startDate.AddMonths(1).AddDays(-1), empNo);
                int lastWorkDay = 5;
                foreach (Workday day in list)
                {
                    if (IsWorkday(day.DayType) && day.WorkDate.Day > lastWorkDay)
                    {
                        lastWorkDay = day.WorkDate.Day;
                    }
                }
                DateTime date = new DateTime(year, month, lastWorkDay, 0, 0, 0, DateTimeKind.Local);
                lock (CacheLock)
                {
                    _cache.Set(cacheName, date, CachePolicy);
                }
                return date;
            }
        }


        /// <summary>Find the last work day in month.</summary>
        /// <param name="year">The year. 西元年</param>
        /// <param name="month">The month. 月分</param>
        /// <param name="shiftId">班別ID</param>
        /// <returns>最後一天的日期(int)</returns>
        public DateTime LastWorkDayInMonth(int year, int month, int shiftId = 1)
        {
            string cacheName = $"LastWorkDayInMonth-{year}-{month}-{shiftId}";
            if (_cache.Contains(cacheName))
            {
                return (DateTime)_cache[cacheName];
            }
            else
            {
                DateTime date = _workdayDao.LastWorkDayInMonth(year, month, shiftId);
                lock (CacheLock)
                {
                    _cache.Set(cacheName, date, CachePolicy);
                }
                return date;
            }
        }

        /// <summary>
        /// 找出此日期(含)之後最接近的工作日
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="workdays">包含此日期的工作日集合</param>
        /// <returns></returns>
        public DateTime MostRecentWorkDateAfter(DateTime date, List<Workday> workdays)
        {
            int shiftId = 1;
            var rows = from x in workdays
                       where x.WorkDate == date
                       select x;
            if (rows.Any())
            {
                Workday workdayx = rows.First();
                if (IsWorkday(workdayx.DayType))
                {
                    return date;
                }
            }
            else if (IsWorkday(date, shiftId))
            {
                return date;
            }

            foreach (Workday workday in workdays)
            {
                if (workday.WorkDate > date && IsWorkday(workday.DayType))
                {
                    return workday.WorkDate;
                }
            }
            //若這月份剩下都放假，找下一個月的第一個工作日
            DateTime nextDate = new DateTime(date.Year, date.Month, 1, 0, 0, 0, DateTimeKind.Local);
            nextDate = nextDate.AddMonths(1);
            return MostRecentWorkDateAfter(nextDate, workdays);
        }

        /// <summary>
        /// 找出此日期(含)之前最接近的工作日
        /// </summary>
        /// <param name="date"></param>
        /// <param name="shiftId">班別編號</param>
        /// <returns></returns>
        public DateTime MostRecentWorkDateBefore(DateTime date, List<Workday> workdays)
        {
            int shiftId = 1;
            var rows = from x in workdays
                       where x.WorkDate == date
                       select x;
            if (rows.Any())
            {
                Workday workdayx = rows.First();
                if (IsWorkday(workdayx.DayType))
                {
                    return date;
                }
            }
            else if (IsWorkday(date, shiftId))
            {
                return date;
            }

            Workday[] array = workdays.ToArray();
            for (int i = array.Length - 1; i >= 0; i--)
            {
                Workday workday = array[i];
                if (workday.WorkDate < date && IsWorkday(workday.DayType))
                {
                    return workday.WorkDate;
                }
            }
            //若這月份剩下都放假，找出前一個月的第一個工作日
            DateTime previousDate = new DateTime(date.Year, date.Month, 1, 0, 0, 0, DateTimeKind.Local);
            previousDate = previousDate.AddMonths(-1);
            return LastWorkDayInMonth(previousDate.Year, previousDate.Month, workdays);
        }

        /// <summary>
        /// 依日曆天月份將啟迄時間分段，切分中間自動設為早上08:00 至 下午17:00
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="shiftId"></param>
        /// <returns></returns>
        public List<(DateTime, DateTime)> SplitDateByMonthCalendarDay(DateTime startDate, DateTime endDate, int shiftId)
        {
            DateTime newEndDate = endDate.Date;
            List<Workday> workdays = GetWorkdaysDateRange(startDate, endDate, shiftId);
            //假設您的參數是 startDate 和 endDate
            List<(DateTime start, DateTime end)> result = new List<(DateTime start, DateTime end)>(); //建立一個空的清單來儲存拆分後的日期範圍
            DateTime current = startDate; //設定一個變數來追蹤目前的日期
            while (current <= newEndDate) //當目前的日期小於結束日期時，重複以下步驟
            {
                DateTime next = new DateTime(current.Year, current.Month, 1, 0, 0, 0, DateTimeKind.Local).AddMonths(1); //找出目前日期所在月份的下一個月的第一天
                DateTime newEnd = next.AddDays(-1);
                if (next > newEndDate)
                {
                    newEnd = endDate;
                } //如果下一個月的第一天超過了結束日期，就將它設為結束日期

                //將目前日期和下一個月的第一天減一天（即目前月份的最後一天）加入清單作為一個日期範圍
                if (newEnd.Date == endDate.Date)
                {
                    newEnd = endDate;
                }
                else
                {
                    // 下班時間到 18:00 
                    Workday? workday = GetWorkdayFromList(workdays, newEnd);
                    if (workday == null)
                    {
                        throw new ArgumentNullException(@"找不到工作日", "workday");
                    }
                    newEnd = AdjustOffDutyTime(workday);
                }

                if (current.Date == startDate.Date)
                {
                    current = startDate;
                }
                else
                {
                    // 上班時間由 08:00 開始
                    Workday? workday = GetWorkdayFromList(workdays, current);
                    if (workday == null)
                    {
                        throw new ArgumentNullException(@"找不到工作日", "workday");
                    }
                    current = AdjustOnDutyTime(workday);
                }

                result.Add((current, newEnd));
                current = next; //將目前日期更新為下一個月的第一天，繼續迴圈
            }
            //最後，result 清單就包含了拆分後的日期範圍，您可以回傳或使用它
            return result;
        }

        /// <summary>
        /// 依月份與工作日將啟迄時間分段，切分中間自動設為早上08:00 至 下午17:00
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public List<(DateTime, DateTime)> SplitDateByMonthWorkday(DateTime startDate, DateTime endDate, string empNo)
        {
            DateTime nextMonth = endDate.AddMonths(1); // 預往後抓一個月，避免沒找到資料造成錯誤
            List<Workday> workdays = GetEmpWorkdaysDateRange(startDate, nextMonth, empNo);

            startDate = MostRecentWorkDateAfter(startDate, workdays);
            //shiftId = 1; // 必須先預設 正常班 1
            //rows = from x in workdays
            //       where x.WorkDate == endDate
            //       select x;
            //if (rows.Any())
            //{
            //    shiftId = rows.First().ShiftId;
            //}
            endDate = MostRecentWorkDateBefore(endDate, workdays);
            DateTime newEndDate = endDate.Date;
            //假設您的參數是 startDate 和 endDate
            List<(DateTime start, DateTime end)> result = new List<(DateTime start, DateTime end)>(); //建立一個空的清單來儲存拆分後的日期範圍
            DateTime current = startDate; //設定一個變數來追蹤目前的日期
            while (current <= newEndDate) //當目前的日期小於結束日期時，重複以下步驟
            {
                DateTime next = new DateTime(current.Year, current.Month, 1, 0, 0, 0, DateTimeKind.Local).AddMonths(1); //找出目前日期所在月份的下一個月的第一天
                DateTime newEnd = next.AddDays(-1);
                if (next > newEndDate)
                {
                    newEnd = endDate;
                } //如果下一個月的第一天超過了結束日期，就將它設為結束日期

                //將目前日期和下一個月的第一天減一天（即目前月份的最後一天）加入清單作為一個日期範圍
                if (newEnd.Date == endDate.Date)
                {
                    newEnd = endDate;
                }
                else
                {
                    Workday? workday = GetWorkdayFromList(workdays, newEnd);
                    if (workday != null)
                    {
                        newEnd = AdjustOffDutyTime(workday);
                    }
                }

                if (current.Date == startDate.Date)
                {
                    current = startDate;
                }
                else
                {
                    // 正常上班時間由 08:00 開始
                    Workday? workday = GetWorkdayFromList(workdays, current);
                    if (workday != null)
                    {
                        current = AdjustOnDutyTime(workday);
                    }
                }
                newEnd = MostRecentWorkDateBefore(newEnd, workdays);
                if (newEnd.Hour == 0)
                {
                    Workday? workday = GetWorkdayFromList(workdays, newEnd);
                    if (workday != null)
                    {
                        newEnd = AdjustOffDutyTime(workday);
                    }
                }
                current = MostRecentWorkDateAfter(current, workdays);
                if (current.Hour == 0)
                {
                    Workday? workday = GetWorkdayFromList(workdays, current);
                    if (workday != null)
                    {
                        current = AdjustOnDutyTime(workday);
                    }
                }
                if (current < newEnd)
                {
                    result.Add((current, newEnd));
                }
                current = next; //將目前日期更新為下一個月的第一天，繼續迴圈
            }
            //最後，result 清單就包含了拆分後的日期範圍，您可以回傳或使用它
            return result;
        }

        /// <summary>
        /// 開始日不算，幾工作日之後
        /// </summary>
        /// <param name="startDate">The start date.</param>
        /// <param name="endDate">The end date.</param>
        /// <param name="shiftId">The shift identifier.</param>
        /// <returns></returns>
        public int WorkdaysRangeDiff(DateTime startDate, DateTime endDate, int shiftId = 1)
        {
            int ret;
            string cacheName = $"WorkdaysRangeDiff{startDate.ToString("yyyyMMdd")}-{endDate.ToString("yyyyMMdd")}-{shiftId}";
            if (_cache.Contains(cacheName))
            {
                ret = (int)_cache[cacheName];
            }
            else
            {
                ret = 0;
                if (startDate <= endDate)
                {
                    DataTable dt = _workdayDao.GetWorkdaysDateRange(startDate, endDate, shiftId);
                    if (dt.Rows.Count > 0)
                    {
                        for (int i = 1; i < dt.Rows.Count; i++)
                        {
                            DataRow dr = dt.Rows[i];
                            WorkdayType dayType = (WorkdayType)(int)(byte)dr["DayType"];
                            if (IsWorkday(dayType))
                            {
                                ret++;
                            }
                        }
                    }
                }
                lock (CacheLock)
                {
                    _cache.Set(cacheName, ret, CachePolicy);
                }
            }
            return ret;
        }

    }
}
