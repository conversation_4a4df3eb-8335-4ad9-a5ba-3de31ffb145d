﻿using FakeItEasy;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Sinotech.Mis.Common;
using Sinotech.Mis.Extensions.Configuration;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.Tests;
using System;
using System.IO;
using System.Net.Mail;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.Tests
{
    public class NotificationMailTests
    {

        private readonly IConfiguration _configuration;
        private readonly EmployeeBo _employeeBo;
        private readonly FormFlowBo _formFlowBo;
        private readonly FormTypeBo _formTypeBo;
        private readonly ILogger<NotificationMail> _logger;
        private readonly NotificationMail _notificationMail;

        public NotificationMailTests(EmployeeBo employeeBo, FormTypeBo formTypeBo, FormFlowBo formFlowBo)
        {
            _configuration = new ConfigurationBuilder().
                AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).
                Build();
            _employeeBo = employeeBo;
            _formTypeBo = formTypeBo;
            _formFlowBo = formFlowBo;
            _logger = A.Fake<ILogger<NotificationMail>>();

            _notificationMail = new NotificationMail(
                _employeeBo,
                _formTypeBo,
                _formFlowBo,
                _configuration,
                _logger
            );
            TestHelper.ClearData(_configuration.GetSecuredConnectionString("Attendance"));
        }


        [Fact]
        public void GenerateApproveLevelText_Should_Throw_DirectoryNotFoundException_When_DirectoryPath_Is_Null()
        {
            // Arrange
            var form = new Form();
            var approveDto = new Approve();
            var currentStep = new FormFlow();
            var formText = "Form Text";
            var employee = new Employee();

            // Act & Assert
            Assert.Throws<FileNotFoundException>(() => _notificationMail.GenerateApproveLevelText(form, approveDto, currentStep, formText, employee));
        }

        [Fact]
        public void ReplateTemplateVariables_Should_Replace_Variables_Correctly()
        {
            // Arrange
            var form = new Form
            {
                FormUID = Guid.NewGuid(),
                FormNo = "F001",
                FormID = "A1Card",
                FormInfo = "Form Info",
                EmpNo = "E001",
                EmpName = "Employee Name",
                CreatedTime = DateTime.Now
            };
            var approveDto = new Approve
            {
                Comment = "Approved",
                ApproverName = "Approver Name",
                ApproverEmpNo = "A001",
                FlowStatus = (int)FlowStatus.Agree
            };
            var template = "{RcptName} {Comment} {Approver} {ApproverEmpNo} {form} {FormInfo} {CreatedTime} {FormUID} {FormNo} {FormID} {FormName} {EmpNo} {EmpName} {FlowStatus}";
            var formText = "Form Text";
            var employee = new Employee
            {
                CName = "Recipient Name"
            };

            // Act
            var result = _notificationMail.ReplateTemplateVariables(form, approveDto, template, formText, employee);

            // Assert
            Assert.Contains("Recipient Name", result);
            Assert.Contains("Approved", result);
            Assert.Contains("Approver Name", result);
            Assert.Contains("A001", result);
            Assert.Contains("Form Text", result);
            Assert.Contains("Form Info", result);
            Assert.Contains(form.FormUID.ToString(), result);
            Assert.Contains("F001", result);
            Assert.Contains("A1Card", result);
            Assert.Contains("Employee Name", result);
            Assert.Contains("同意", result);
        }

        [Fact]
        public void SendEmail_Should_Log_Information_When_Email_Sent_Failed()
        {
            // Arrange
            var formUid = Guid.NewGuid();
            var approverEmpNo = "A001";
            var comment = "Approved";
            var smtpHost = "localhost";
            var port = 25;
            var userName = "user";
            var password = "password";
            var subject = "Test Subject";
            var body = "Test Body";
            var employee = new Employee
            {
                EmpNo = "E001",
                Email = "<EMAIL>"
            };
            var from = new MailAddress("<EMAIL>", "From Name");
            var to = new MailAddress("<EMAIL>", "To Name");

            // Act
            Form form = new Form();
            form.FormUID = formUid;
            Approve approveDto = new Approve();
            Assert.Throws<ArgumentNullException>(() => _notificationMail.SendEmail(form, approveDto, smtpHost, port, userName, password, subject, body, employee, employee));
        }


        [Fact]
        public void SendEmailTest()
        {
            Form form = new Form();
            Approve approveDto = new Approve();
            string smtpHost = "127.0.0.1";
            int port = 25;
            string userName = "nobody";
            string password = "password";
            string subject = "test subject";
            string body = "";
            Employee approver = new Employee();
            Employee employee = new Employee();
            _notificationMail.SendEmail(form, approveDto, smtpHost, port, userName, password, subject, body, approver, employee);

        }


        [Fact]
        public void SendNotifyEmail_Should_Send_Email_When_Valid_Data()
        {
            // Arrange
            var form = new Form
            {
                FormUID = Guid.NewGuid(),
                FormID = "A1Card",
                EmpNo = "0395",
                FormSubject = "Test Subject",
                FormInfo = "Test Info",
                FormNo = "F001",
                StartTime = DateTime.Now,
                CreatedTime = DateTime.Now
            };

            CardBase card = new A1Card();

            var approveDto = new Approve
            {
                FlowStatus = (int)FlowStatus.Agree,
                ApproverEmpNo = "A456",
                Comment = "Approved",

                FormUID = form.FormUID,
                ApproverName = "Approver Name"
            };

            var currentStep = new FormFlow
            {
                Step = 1,
                FlowName = "Initial Approval",
                RecipientName = "Recipient",
                RecipientEmpNo = "R789",
                IsNotification = false,
                ApproverName = "Approver",
                ApproverEmpNo = "A456",
                FormUID = form.FormUID,
                FlowUID = Guid.NewGuid(),
                FlowStatus = (int)FlowStatus.Agree
            };


            // Act
            Assert.Throws<NullReferenceException>(() => _notificationMail.SendNotifyEmail(form, card, approveDto, currentStep));
            // Assert
            Assert.True(true);
        }

        [Fact]
        public void SendNotifyEmail_Should_Use_Correct_Template_Based_On_FlowStatus()
        {
            // Arrange
            var form = new Form
            {
                FormUID = Guid.NewGuid(),
                FormID = "A1Card",
                EmpNo = "0395",
                FormSubject = "Test Subject",
                FormInfo = "Test Info",
                FormNo = "F001",
                StartTime = DateTime.Now,
                CreatedTime = DateTime.Now
            };

            CardBase card = new A1Card();

            var approveDto = new Approve
            {
                FlowStatus = (int)FlowStatus.Deny,
                ApproverEmpNo = "A456",
                Comment = "Denied",
                FormUID = form.FormUID,
                ApproverName = "Approver Name"
            };

            var currentStep = new FormFlow
            {
                Step = 1,
                FlowName = "Initial Approval",
                RecipientName = "Recipient",
                RecipientEmpNo = "R789",
                IsNotification = false,
                ApproverName = "Approver",
                ApproverEmpNo = "A456",
                FormUID = form.FormUID,
                FlowUID = Guid.NewGuid(),
                FlowStatus = (int)FlowStatus.Deny
            };

            // Act
            Assert.Throws<NullReferenceException>(() => _notificationMail.SendNotifyEmail(form, card, approveDto, currentStep));
        }

    }
}
