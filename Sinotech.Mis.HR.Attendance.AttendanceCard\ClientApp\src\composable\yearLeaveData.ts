import { ref } from 'vue'
import { GET_EMPLEAVEINFO_URL } from '../api/appUrl'
import { LeaveInfoApiType } from '../api/appType'

/**
 * 年度休假統計時數資料
 * @returns 
 */
export function useYearLeaveData() {
  const yearLeaveDateStatic = ref<Date>(new Date())
  const annualLeaves = ref<LeaveInfoApiType>()
  const compensatoryLeaves = ref<LeaveInfoApiType>()
  const extendedLeaves = ref<LeaveInfoApiType>()

  const onGetYearLeaves = async (date: Date, userId: string, signal: AbortSignal): Promise<void> => {
    yearLeaveDateStatic.value = new Date()

    const params = new URLSearchParams({
      date: date.toISOString(),
      empNo: userId
    })
    const res: Response = await fetch(GET_EMPLEAVEINFO_URL + '?' + params, {
      method: 'GET',
      signal: signal
    })
    if (!res.ok) {
      throw new Error(res.status.toString())
    }
    const jsonData = await res.json()

    annualLeaves.value = jsonData.find((e: LeaveInfoApiType) => e.LeaveName === '特別休息假')
    compensatoryLeaves.value = jsonData.find((e: LeaveInfoApiType) => e.LeaveName === '補休假')
    extendedLeaves.value = jsonData.find((e: LeaveInfoApiType) => e.LeaveName === '延休假')
  }

  return { yearLeaveDateStatic, annualLeaves, compensatoryLeaves, extendedLeaves, onGetYearLeaves }
}