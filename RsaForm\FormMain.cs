﻿using System;
using System.Windows.Forms;
using Sinotech.Mis.Extensions.Configuration;

namespace RsaForm
{
    public partial class FormMain : Form
    {
        public FormMain()
        {
            InitializeComponent();
        }

        private void btEncrypt_Click(object sender, EventArgs e)
        {
            textBoxResult.Text = RsaCrypto.RsaEncrypt("PublicKey.xml", textBoxInput.Text);
            btCopy.Enabled = true;
        }

        private void btDecrypt_Click(object sender, EventArgs e)
        {
            textBoxResult.Text = RsaCrypto.RsaDecrypt("PrivateKey.xml", textBoxInput.Text);
            btCopy.Enabled = true;
        }

        private void btPaste_Click(object sender, EventArgs e)
        {
            // 從剪貼簿複製文字到 textBoxInput 
            textBoxInput.Text = Clipboard.GetText();
            textBoxResult.Text = string.Empty;
            btCopy.Enabled = false;
        }

        private void btCopy_Click(object sender, EventArgs e)
        {
            // 複製文字到剪貼簿
            Clipboard.SetText(textBoxResult.Text);
            // 設定ToolTip的標題和消息文字
            //toolTip1.ToolTipTitle = "複製成功";
            toolTip1.ToolTipIcon = ToolTipIcon.Info; // 設定圖示,也可以是 Warning, Error等
            toolTip1.Show("已複製到剪貼簿", textBoxResult, 2000);
        }
    }
}
