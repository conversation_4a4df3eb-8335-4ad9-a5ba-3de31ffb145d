﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace Sinotech.Mis.HR.Attendance.Common
{
    public interface IB1CardAppBo : ICardBaseBo
    {
        /// <summary>新增 加班申請卡</summary>
        /// <param name="creatorId">填表人 員工編號</param>
        /// <param name="b1CardApp">加班申請卡</param>
        /// <param name="ipAddress">IP address</param>
        /// <param name="hostname">Host name</param>
        /// <returns>傳回 B1CardAppCheckResult</returns>
        Task<B1CardAppCheckResult> AddB1CardApp(string creatorId, B1CardApp b1CardApp, string ipAddress, string hostname);

        /// <summary>依照表單資料補足三卡資料 A1Card/B1Card/B1CardApp/C1Card</summary>
        /// <param name="form"></param>
        /// <param name="card">The card.</param>
        /// <returns></returns>
        bool AmendCard(Form form, CardBase card);

        /// <summary>B1CardAppDto轉為B1CardApp</summary>
        /// <param name="list">The list.</param>
        /// <returns>
        ///   <br />
        /// </returns>
        B1CardApp B1CardAppDto2B1CardApp(List<B1CardAppDto> list);

        /// <summary>
        /// 是否能結案抽單 (管理員抽單)
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        string CanClosedWithdraw(CardBase card);

        /// <summary>
        /// 是否能抽單 (正常情況)
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        string CanWithdraw(CardBase card);

        /// <summary>是否可以報加班</summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>可以則傳回空字串集合 ，不行則會傳回理由集合</returns>
        string IsOvertimeAllowed(DateTime date, string empNo);

        /// <summary>檢查是否為有效的加班申請卡</summary>
        /// <param name="b1CardApp">加班申請卡</param>
        /// <returns>B1CardAppCheckResult</returns>
        B1CardAppCheckResult CheckData(B1CardApp b1CardApp);

        /// <summary>是否可以填加班申請卡</summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>傳回 B1CardAppCheckDateResult</returns>
        B1CardAppCheckResult DateCanFillB1CardApp(DateTime date, string empNo);

        /// <summary>結案</summary>
        /// <param name="card">The card.</param>
        /// <param name="formStatus">The form status.</param>
        /// <param name="updateDto">更新資料物件</param>
        void Finish(CardBase card, FormStatus formStatus, Updater updateDto);

        /// <summary>
        /// 取得加班類型 B1CODE 列表
        /// </summary>
        /// <returns></returns>
        List<B1CardType> GetB1CardTypes();

        /// <summary>
        /// 取得特定卡
        /// </summary>
        /// <param name="formUID"></param>
        /// <returns></returns>
        CardBase? GetCard(Guid formUID);

        /// <summary>取得某段時間內卡，不含流程與附件等</summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <returns>卡 CardsDto 物件 List</returns>
        List<CardBase> GetCards(DateTime startDate, DateTime endDate);

        /// <summary>取得表單及加班申請卡</summary>
        /// <param name="form">The form </param>
        /// <param name="userId">The user identifier.</param>
        /// <returns>表單及加班申請卡 FormCardsDto 物件</returns>
        FormCard GetFormCard(Form form, string userId);

        /// <summary>取得某段時間內表單及卡</summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <returns>表單及卡 FormCardsDto 物件 List</returns>
        List<FormCard> GetFormCards(DateTime startDate, DateTime endDate);

        /// <summary>取得某段時間內表單及卡</summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單及卡 FormCardsDto 物件 List</returns>
        List<FormCard> GetFormCards(DateTime startDate, DateTime endDate, string projNo);

        /// <summary>
        /// 取得總時數
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        int GetHours(CardBase card);

        /// <summary>
        /// 取得提醒資訊
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        (int, string?) GetListRemindMessage(CardBase card);

        /// <summary>
        /// 取得員工在特定月份所有卡，依表單日期為準，非填表時間
        /// </summary>
        /// <param name="empNo">填表員工編號</param>
        /// <param name="date">日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns></returns>
        List<CardBase> GetUserCardsMonth(string empNo, DateTime date, int? status = null);

        /// <summary>
        /// 取得員工在特定月份已填表單及卡，包括他人代填與本人填表，依表單年月為準，非填表時間
        /// </summary>
        /// <param name="empNo">填表員工編號</param>
        /// <param name="year">表單年</param>
        /// <param name="month">表單月</param>
        /// <returns></returns>
        List<FormCard> GetUserSentFormCardsYearMonth(string empNo, int year, int month);

        /// <summary>
        /// 是否可填報加班申請卡
        /// </summary>
        /// <param name="date"></param>
        /// <returns></returns>
        string InAllowOvertimeDays(DateTime date);

        /// <summary>
        /// 是否可填報加班申請卡，僅測試時直接呼叫，正常使用 InAllowDays(DateTime date)
        /// </summary>
        /// <param name="date"></param>
        /// <param name="now">目前時間</param>
        /// <returns></returns>
        string InAllowOvertimeDays(DateTime date, DateTime now);

        /// <summary>
        /// 是否已填加班申請卡
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>若未填則傳回 {FormUID: null, FormNo: null}</returns>
        FormFilled IsFilledB1CardApp(DateTime date, string empNo);

        /// <summary>
        /// 是否逾期
        /// </summary>
        /// <param name="date">加班日期</param>
        /// <returns></returns>
        bool IsOverdue(DateTime date);

        /// <summary>是否逾期，僅測試時直接呼叫，正常使用 InAllowDays(DateTime date)</summary>
        /// <param name="date">加班日期</param>
        /// <param name="fillDate">填報日期</param>
        /// <returns></returns>
        bool IsOverdue(DateTime date, DateTime fillDate);

        /// <summary>
        /// 設定B1CardApp TypeName
        /// </summary>
        /// <param name="card"></param>
        void SetApplicationType(B1CardApp card);

        /// <summary>轉換為Stored Procedure所須的 User Defined Table</summary>
        /// <param name="card">三卡</param>
        /// <param name="isAdd">是否為新增</param>
        /// <returns></returns>
        List<DataTable> ToUserDefinedDataTables(CardBase card, bool isAdd = true);

        /// <summary>更新Card</summary>
        /// <param name="card">The card.</param>
        /// <param name="updateDto">更新資料物件</param>
        void Update(CardBase card, Updater updateDto);

        /// <summary>
        /// 抽單
        /// </summary>
        /// <param name="card"></param>
        /// <param name="withdraw"></param>
        /// <returns></returns>
        bool Withdraw(CardBase card, Withdraw withdraw);

    }
}