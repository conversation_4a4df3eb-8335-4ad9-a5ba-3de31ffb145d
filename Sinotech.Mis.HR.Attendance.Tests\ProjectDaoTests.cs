﻿using Sinotech.Mis.Utilities.DataAccess.Ado;
using Sinotech.Mis.Utilities.DataAccess.Interfaces;
using System;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.Utilities.DataAccess.Ado.Tests
{
    [ExcludeFromCodeCoverage]
    public class ProjectDaoTests
    {

        private readonly IProjectDao _projectDao;

        public ProjectDaoTests(IProjectDao projectDao)
        {
            _projectDao = projectDao;
        }

        [Theory]
        [InlineData(1)]
        [InlineData(2)]
        [InlineData(3)]
        [InlineData(4)]
        public void GetAllDepartmentsProjects_deptNo_Test(int deptNo)
        {
            DataTable dt = _projectDao.GetAllDepartmentsProjects(deptNo);
            Assert.NotNull(dt);
            Assert.True(dt.Rows.Count > 4);
        }

        [Fact]
        public void GetAllDepartmentsProjectsTest()
        {
            DataTable dt = _projectDao.GetAllDepartmentsProjects();
            Assert.NotNull(dt);
            Assert.True(dt.Rows.Count > 4);
        }

        [Fact]
        public void GetEndDateTest()
        {
            DateTime? endDate = _projectDao.GetEndDate("TI21503");
            Assert.NotNull(endDate);
            DateTime date = new DateTime(2023, 2, 8);
            Assert.Equal(date, endDate);
        }

        [Theory]
        [InlineData("1871-01-01", "1872-02-02", false)]
        [InlineData("2003-03-01", "2003-02-02", false)]
        [InlineData("2020-01-01", "2003-02-02", false)]
        [InlineData("2020-03-01", "2020-02-02", false)]
        [InlineData("2020-01-01", "2020-01-01", true)]
        [InlineData("2020-01-01", "2020-02-02", true)]
        [InlineData("2022-01-01", "2022-02-02", true)]
        [InlineData("2048-01-01", "2048-02-02", true)]
        public void GetOpenProjectsDateRangeTest(DateTime startDate, DateTime endDate, bool expected)
        {
            DataTable dt = _projectDao.GetOpenProjectsDateRange(startDate, endDate);
            if (expected)
            {
                Assert.True(dt.Rows.Count > 0);
            }
            else
            {
                Assert.False(dt.Rows.Count > 0);
            }
        }

        [Theory]
        [InlineData("", false)]
        [InlineData("0000003", false)]
        [InlineData("TI21503", true)]
        [InlineData("RP19552", true)]
        [InlineData("RP19553", true)]
        public void GetProjectNameTest(string prjNo, bool expected)
        {
            string prjName = _projectDao.GetProjectName(prjNo);
            if (expected)
            {
                Assert.NotEmpty(prjName);
            }
            else
            {
                Assert.Empty(prjName);
            }
        }

        [Theory]
        [InlineData("", false)]
        [InlineData("0000003", false)]
        [InlineData("TI21503", true)]
        [InlineData("RP19552", true)]
        [InlineData("RP19553", true)]
        public void GetProjectTest(string prjNo, bool expected)
        {
            DataTable dt = _projectDao.GetProject(prjNo);
            if (expected)
            {
                Assert.True(dt.Rows.Count > 0);
            }
            else
            {
                Assert.False(dt.Rows.Count > 0);
            }
        }

    }
}