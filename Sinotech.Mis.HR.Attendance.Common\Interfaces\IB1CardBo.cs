﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace Sinotech.Mis.HR.Attendance.Common
{
    public interface IB1CardBo : ICardBaseBo
    {
        /// <summary>新增 加班卡</summary>
        /// <param name="creatorId">填表人 員工編號</param>
        /// <param name="b1Card">加班卡</param>
        /// <param name="ipAddress">IP address</param>
        /// <param name="hostname">Host name</param>
        /// <returns>傳回 B1CardCheckResult</returns>
        Task<B1CardCheckResult> AddB1Card(string creatorId, B1Card b1Card, string ipAddress, string hostname);

        /// <summary>依照表單資料補足三卡資料 B1Card</summary>
        /// <param name="form">表單</param>
        /// <param name="card">卡</param>
        /// <returns></returns>
        bool AmendCard(Form form, CardBase card);

        /// <summary>
        /// 是否能結案抽單 (管理員抽單)
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        string CanClosedWithdraw(CardBase card);

        /// <summary>
        /// 是否能抽單 (正常情況)
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        string CanWithdraw(CardBase card);

        /// <summary>檢查 加班卡</summary>
        /// <param name="b1Card">加班卡</param>
        /// <returns>傳回 B1CardCheckResult</returns>
        B1CardCheckResult CheckData(B1Card b1Card);

        /// <summary>是否可以填加班卡</summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>傳回 B1CardCheckDateResult</returns>
        B1CardCheckResult DateCanFillB1Card(DateTime date, string empNo);

        /// <summary>結案</summary>
        /// <param name="card">The card.</param>
        /// <param name="formStatus">The form status.</param>
        /// <param name="updateDto">更新資料物件</param>
        void Finish(CardBase card, FormStatus formStatus, Updater updateDto);

        /// <summary>
        /// 取得加班類型 B1CODE 列表
        /// </summary>
        /// <returns></returns>
        List<B1CardType> GetB1CardTypes();

        /// <summary>
        /// 取得卡
        /// </summary>
        /// <param name="formUID">Form UUID</param>
        /// <returns>卡</returns>
        CardBase? GetCard(Guid formUID);

        /// <summary>取得某段時間內卡，不含流程與附件等</summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <returns>卡 CardsDto 物件 List</returns>
        List<CardBase> GetCards(DateTime startDate, DateTime endDate);

        /// <summary>取得表單及加班卡</summary>
        /// <param name="form">The form.</param>
        /// <param name="userId">The user identifier.</param>
        /// <returns>表單及加班卡 FormCardsDto 物件</returns>
        FormCard GetFormCard(Form form, string userId);

        /// <summary>取得某段時間內表單及卡</summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <returns>表單及卡 FormCardsDto 物件 List</returns>
        List<FormCard> GetFormCards(DateTime startDate, DateTime endDate);

        /// <summary>取得某段時間內表單及卡</summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單及卡 FormCardsDto 物件 List</returns>
        List<FormCard> GetFormCards(DateTime startDate, DateTime endDate, string projNo);

        /// <summary>
        /// 取得總時數
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        int GetHours(CardBase card);

        /// <summary>
        /// 取得提醒資訊
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        (int, string?) GetListRemindMessage(CardBase card);

        /// <summary>
        /// 取得特定月份卡，依表單日期為準，非填表時間
        /// </summary>
        /// <param name="empNo">填表員工編號</param>
        /// <param name="date">日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns></returns>
        List<CardBase> GetUserCardsMonth(string empNo, DateTime date, int? status = null);

        /// <summary>
        /// 取得特定月份已填表單及卡，包括他人代填與本人填表，依表單年月為準，非填表時間
        /// </summary>
        /// <param name="empNo">填表員工編號</param>
        /// <param name="year">表單年</param>
        /// <param name="month">表單月</param>
        /// <returns></returns>
        List<FormCard> GetUserSentFormCardsYearMonth(string empNo, int year, int month);

        /// <summary>
        /// 是否已填加班卡
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        bool IsFilled(DateTime date, string empNo);

        /// <summary>
        /// 是否已填加班卡
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>若未填則傳回 {FormUID: null, FormNo: null}</returns>
        FormFilled IsFilledB1Card(DateTime date, string empNo);

        /// <summary>
        /// 是否為有效的加班卡
        /// </summary>
        /// <param name="b1Card"></param>
        /// <returns>B1CardCheckResult物件，包括AlarmMessage與ErrorMessage</returns>
        B1CardCheckResult IsValidB1Card(B1Card b1Card);

        /// <summary>
        /// 設定B1Card TypeName
        /// </summary>
        /// <param name="card"></param>
        void SetApplicationType(B1Card card);

        /// <summary>轉換為Stored Procedure所需的 User Defined Table</summary>
        /// <param name="card">三卡</param>
        /// <param name="isAdd">是否為新增</param>
        /// <returns></returns>
        List<DataTable> ToUserDefinedDataTables(CardBase card, bool isAdd = true);

        /// <summary>更新Card</summary>
        /// <param name="card">The card.</param>
        /// <param name="updateDto">更新資料物件</param>
        void Update(CardBase card, Updater updateDto);

        /// <summary>
        /// 抽單
        /// </summary>
        /// <param name="card"></param>
        /// <param name="withdraw"></param>
        /// <returns></returns>
        bool Withdraw(CardBase card, Withdraw withdraw);

    }

    /// <summary>
    /// 加班類型
    /// </summary>
    public class B1CardType
    {

        /// <summary>
        /// 加班類型編號
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 加班類型
        /// </summary>
        public string TypeName { get; set; } = string.Empty;
    }
}