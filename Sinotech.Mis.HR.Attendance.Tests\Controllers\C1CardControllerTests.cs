﻿using FakeItEasy;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sinotech.Mis.Extensions.Configuration;
using Sinotech.Mis.HR.Attendance.BusinessLogic;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.DataAccess.Interfaces;
using Sinotech.Mis.HR.Attendance.Tests;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.Threading.Tasks;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers.Tests
{
#nullable enable
    [ExcludeFromCodeCoverage]
    public class C1CardControllerTests
    {

        private readonly C1CardBo _c1CardBo;
        private readonly IAttendanceBo _attendanceBo;
        private readonly IConfiguration _configuration;
        private readonly C1CardController _controller;
        private readonly ILogger<C1CardController> _logger;

        public C1CardControllerTests(IAttendanceBo attendanceBo, C1CardBo c1CardBo, ILogger<C1CardController> logger)
        {
            _c1CardBo = c1CardBo;
            _attendanceBo = attendanceBo;
            _logger = logger;
            IConfiguration configuration = new ConfigurationBuilder().
                AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).Build();
            _configuration = configuration;
            _controller = new C1CardController(attendanceBo, c1CardBo, configuration, logger);

            string? ConnectionStringAttendance = configuration.GetSecuredConnectionString("Attendance");
            if (ConnectionStringAttendance != null)
            {
                TestHelper.ClearData(ConnectionStringAttendance);
            }
        }

        private async Task<CardCheckResult?> AddC1Card(string empNo, string deputy, string addSigners, int leaveNumber, int leaveDetailNumber, string startTime, string endTime, string eventDate)
        {
            DateTime createdTime = DateTime.Now;
            DateTime filledTime = createdTime.AddSeconds(-29);

            var fakeC1CardBo = A.Fake<IC1CardBo>();
            DateTime startDate = DateTime.Parse(startTime);
            DateTime endDate = DateTime.Parse(endTime);
            A.CallTo(() => fakeC1CardBo.IfLeaveRecordExists(empNo, startDate, endDate)).Returns(true);
            A.CallTo(() => fakeC1CardBo.GetAnnualLeaveRemainingHours(empNo, startDate)).Returns(56);
            A.CallTo(() => fakeC1CardBo.GetPostponedLeaveRemainingHours(empNo, startDate)).Returns(10);
            A.CallTo(() => fakeC1CardBo.IfLeaveRecordExists(empNo, startDate, endDate)).Returns(true);
            A.CallTo(() => fakeC1CardBo.GetCompensatoryLeaveRemainingHours(empNo, startDate)).Returns(10);

            //string json = $"{{\"EmpNo\":\"{empNo}\",\"StartTime\":\"{startTime}\",\"EndTime\":\"{endTime}\",\"EventDate\":\"{eventDate}\",\"OverPermittedHours\":false," +
            //    $"\"Deputy\":\"{deputy}\",\"LeaveKind\":{leaveNumber},\"LeaveDetailNumber\":{leaveDetailNumber}," +
            //    $"\"RelatedFormNumber\":\"\",\"Reason\":\"test\",\"ProjectNumber\":\"RP19553\"," +
            //    $"\"Location\":\"地點\",\"UpdatedEmpNo\":\"{empNo}\",\"UpdatedName\":null," +
            //    $"\"UpdatedTime\":\"2023-11-07T09:36:55.684+08:00\",\"UpdatedIP\":\"**************\"," +
            //    $"\"UpdatedHost\":\"VS2017\",\"AddSigners\":\"{addSigners}\"," +
            //    $"\"CreatedTime\":\"{createdTime.ToString("yyyy-MM-ddTHH:mm:ss+08:00")}\"," +
            //    $"\"FilledTime\":\"{filledTime.ToString("yyyy-MM-ddTHH:mm:ss+08:00")}\"," +
            //    $"\"UploadedFiles\":[],\"Confirmed\":true}}";
            //LeaveView? leave = JsonConvert.DeserializeObject<LeaveView>(json);

            LeaveView leave = new LeaveView();
            leave.EmpNo = empNo;
            leave.StartTime = DateTime.Parse(startTime);
            leave.EndTime = DateTime.Parse(endTime);
            leave.Deputy = deputy;
            leave.LeaveKind = leaveNumber;
            leave.LeaveDetailNumber = leaveDetailNumber;
            leave.Reason = "test";
            leave.ProjectNumber = "RP19553";
            leave.Location = "地點";
            leave.UpdatedEmpNo = empNo;
            leave.UpdatedTime = DateTime.Parse("2023-11-07T09:36:55.684+08:00");
            leave.UpdatedIP = "**************";
            leave.UpdatedHost = "VS2017";
            leave.AddSigners = addSigners;
            leave.CreatedTime = createdTime;
            leave.FilledTime = filledTime;
            leave.Confirmed = true;
            if (!string.IsNullOrWhiteSpace(eventDate))
            {
                leave.EventDate = DateTime.Parse(eventDate);
            }
            string ret = await _controller.Submit(leave);
            CardCheckResult? result = JsonConvert.DeserializeObject<CardCheckResult>(ret);
            return result;
        }

        private string CheckData(string empNo, string deputy, string addSigners, int leaveNumber, int leaveDetailNumber, string startTime, string endTime)
        {
            DateTime createdTime = DateTime.Now;
            DateTime filledTime = createdTime.AddSeconds(-29);

            string json = $"{{\"EmpNo\":\"{empNo}\",\"StartTime\":\"{startTime}\",\"EndTime\":\"{endTime}\",\"EventDate\":null,\"OverPermittedHours\":false,\"Deputy\":\"{deputy}\",\"LeaveKind\":{leaveNumber},\"LeaveDetailNumber\":{leaveDetailNumber},\"RelatedFormNumber\":\"\",\"Reason\":\"test\",\"ProjectNumber\":\"RP19553\",\"Location\":\"地點\",\"UpdatedEmpNo\":\"{empNo}\",\"UpdatedName\":null,\"UpdatedTime\":\"2023-11-07T09:36:55.684+08:00\",\"UpdatedIP\":\"**************\",\"UpdatedHost\":\"VS2017\",\"AddSigners\":\"{addSigners}\",\"CreatedTime\":\"{createdTime.ToString("yyyy-MM-ddTHH:mm:ss+08:00")}\",\"FilledTime\":\"{filledTime.ToString("yyyy-MM-ddTHH:mm:ss+08:00")}\",\"UploadedFiles\":[],\"Confirmed\":true}}";
            LeaveView? leave = JsonConvert.DeserializeObject<LeaveView>(json);
            string ret = _controller.CheckData(leave);
            return ret;
        }

        [Fact]
        public void C1CardControllerTest()
        {
            IConfiguration configuration = new ConfigurationBuilder().
                AddJsonFile("appsettings.internet.json", optional: true, reloadOnChange: true).Build();
            C1CardController controller = new C1CardController(_attendanceBo, _c1CardBo, configuration, _logger);
            Assert.NotNull(controller);
        }

        [Fact]
        public void CalculateLeaveDayHours_ThrowException()
        {
            IC1CardBo c1CardBo = A.Fake<IC1CardBo>();
            A.CallTo(() => c1CardBo.GetLeaveDayHours(A<int>.Ignored, A<DateTime>.Ignored, A<DateTime>.Ignored)).Throws(new Exception("預計中的錯誤"));
            C1CardController controller = new C1CardController(_attendanceBo, c1CardBo, _configuration, _logger);
            string actual = controller.CalculateLeaveDayHours(0, DateTime.Now, DateTime.Now);
            Assert.Equal("-1", actual);
        }

        [Theory]
        [InlineData(1, "2023-11-01T11:12:00+08:00", "2023-11-01T13:21:00+08:00", "2小時")]
        [InlineData(1, "2023-11-01T09:12:00+08:00", "2023-11-01T14:21:00+08:00", "5小時")]
        [InlineData(1, "2023-11-01T08:00:00+08:00", "2023-11-01T17:00:00+08:00", "1天")]
        [InlineData(1, "2023-10-31T08:00:00+08:00", "2023-11-02T17:00:00+08:00", "3天")]
        [InlineData(1, "2023-10-31T10:00:00+08:00", "2023-11-02T15:00:00+08:00", "2天4小時")]
        public void CalculateLeaveDayHoursTest(int leaveNumber, DateTime startDate, DateTime endDate, string expected)
        {
            string ret = _controller.CalculateLeaveDayHours(leaveNumber, startDate, endDate);
            Assert.Equal(expected, ret);
        }


        [Fact]
        public void CalculateLeaveEndDate_ThrowException()
        {
            IC1CardBo c1CardBo = A.Fake<IC1CardBo>();
            A.CallTo(() => c1CardBo.CalculateLeaveEndDate(A<DateTime>.Ignored, A<int>.Ignored, A<int>.Ignored, A<int>.Ignored)).Throws(new Exception("預計中的錯誤"));
            C1CardController controller = new C1CardController(_attendanceBo, c1CardBo, _configuration, _logger);
            DateTime actual = controller.CalculateLeaveEndDate(DateTime.Now, 1, 0, 1);
            Assert.Equal(DateTime.MaxValue, actual);
        }

        [Theory]
        [InlineData("2023-01-01T08:00+8:00", 1, 0, 1, "2023-01-03T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 1, 0, 1, "2023-11-01T17:00+08:00")]
        [InlineData("2023-01-01T08:00+8:00", 2, 0, 1, "2023-01-11T17:00+08:00")]
        [InlineData("2023-01-02T08:00+8:00", 2, 0, 1, "2023-01-11T17:00+08:00")]
        [InlineData("2023-01-03T08:00+8:00", 2, 0, 1, "2023-01-11T17:00+08:00")]
        [InlineData("2023-01-04T08:00+8:00", 2, 0, 1, "2023-01-12T17:00+08:00")]
        [InlineData("2023-02-24T08:00+8:00", 2, 0, 1, "2023-03-09T17:00+08:00")]
        [InlineData("2023-06-30T08:00+8:00", 2, 0, 1, "2023-07-11T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 2, 0, 1, "2023-11-10T17:00+08:00")]
        [InlineData("2023-11-02T08:00+8:00", 2, 0, 1, "2023-11-13T17:00+08:00")]
        [InlineData("2023-11-03T08:00+8:00", 2, 0, 1, "2023-11-14T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 3, 0, 1, "2023-11-01T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 4, 0, 1, "2023-11-01T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 5, 0, 1, "2023-11-01T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 6, 0, 1, "2023-11-01T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 7, 1, 1, "2023-12-26T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 7, 2, 1, "2023-11-28T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 7, 3, 1, "2023-11-07T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 7, 4, 1, "2023-11-05T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 8, 0, 1, "2023-11-01T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 9, 0, 1, "2023-11-01T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 10, 0, 1, "2023-11-01T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 11, 1, 1, "2023-11-10T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 11, 2, 1, "2023-11-10T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 11, 3, 1, "2023-11-10T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 11, 4, 1, "2023-11-10T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 11, 5, 1, "2023-11-10T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 11, 6, 1, "2023-11-10T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 11, 7, 1, "2023-11-10T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 11, 21, 1, "2023-11-08T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 11, 22, 1, "2023-11-08T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 11, 23, 1, "2023-11-08T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 11, 24, 1, "2023-11-08T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 11, 25, 1, "2023-11-08T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 11, 26, 1, "2023-11-08T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 11, 27, 1, "2023-11-03T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 11, 28, 1, "2023-11-03T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 11, 29, 1, "2023-11-08T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 11, 30, 1, "2023-11-08T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 11, 31, 1, "2023-11-08T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 11, 32, 1, "2023-11-08T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 11, 33, 1, "2023-11-08T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 11, 34, 1, "2023-11-08T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 11, 35, 1, "2023-11-03T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 11, 36, 1, "2023-11-03T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 11, 51, 1, "2023-11-03T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 11, 52, 1, "2023-11-03T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 11, 53, 1, "2023-11-03T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 11, 54, 1, "2023-11-03T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 11, 55, 1, "2023-11-03T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 12, 0, 1, "2023-11-01T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 13, 0, 1, "2023-11-01T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 14, 0, 1, "2023-11-01T17:00+08:00")]
        [InlineData("2023-01-01T08:00+8:00", 15, 0, 1, "2023-01-03T17:00+08:00")]
        [InlineData("2023-01-02T08:00+8:00", 15, 0, 1, "2023-01-03T17:00+08:00")]
        [InlineData("2023-01-03T08:00+8:00", 15, 0, 1, "2023-01-03T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 15, 0, 1, "2023-11-01T17:00+08:00")]
        [InlineData("2023-01-01T08:00+8:00", 16, 0, 1, "2023-01-03T17:00+08:00")]
        [InlineData("2023-01-02T08:00+8:00", 16, 0, 1, "2023-01-03T17:00+08:00")]
        [InlineData("2023-01-03T08:00+8:00", 16, 0, 1, "2023-01-03T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 16, 0, 1, "2023-11-01T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 17, 0, 1, "2023-11-01T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 18, 0, 1, "2023-11-01T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 19, 0, 1, "2023-11-01T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 20, 0, 1, "2023-11-01T17:00+08:00")]
        [InlineData("2023-01-01T08:00+8:00", 21, 0, 1, "2023-01-03T17:00+08:00")]
        [InlineData("2023-01-02T08:00+8:00", 21, 0, 1, "2023-01-03T17:00+08:00")]
        [InlineData("2023-01-03T08:00+8:00", 21, 0, 1, "2023-01-03T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 21, 0, 1, "2023-11-01T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 22, 0, 1, "2023-11-01T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 23, 0, 1, "2023-11-01T17:00+08:00")]
        [InlineData("2023-11-01T08:00+8:00", 24, 0, 1, "2023-11-01T17:00+08:00")]
        public void CalculateLeaveEndDateTest(DateTime startDate, int leaveNumber, int subKindNumber, int shiftId, string expected)
        {
            DateTime date = _controller.CalculateLeaveEndDate(startDate, leaveNumber, subKindNumber, shiftId);
            DateTime expectedDate = DateTime.MaxValue;
            if (!string.IsNullOrWhiteSpace(expected))
            {
                expectedDate = DateTime.Parse(expected, CultureInfo.CreateSpecificCulture("zh-TW"));

            }
            Assert.Equal(expectedDate, date);
        }

        [Fact]
        public void CheckData_ThrowException()
        {
            string empNo = "2268";
            string deputy = "0349";
            string startTime = "2022-11-06T08:00:00+0800";
            string endTime = "2022-11-06T15:00:00+0800";
            int leaveNumber = 1;
            int leaveDetailNumber = 0;
            string addSigners = "0349";
            DateTime createdTime = DateTime.Now;
            DateTime filledTime = createdTime.AddSeconds(-29);

            string json = $"{{\"EmpNo\":\"{empNo}\",\"StartTime\":\"{startTime}\",\"EndTime\":\"{endTime}\",\"EventDate\":null,\"OverPermittedHours\":false,\"Deputy\":\"{deputy}\",\"LeaveKind\":{leaveNumber},\"LeaveDetailNumber\":{leaveDetailNumber},\"RelatedFormNumber\":\"\",\"Reason\":\"test\",\"ProjectNumber\":\"RP19553\",\"Location\":\"地點\",\"UpdatedEmpNo\":\"{empNo}\",\"UpdatedName\":null,\"UpdatedTime\":\"2023-11-07T09:36:55.684+08:00\",\"UpdatedIP\":\"**************\",\"UpdatedHost\":\"VS2017\",\"AddSigners\":\"{addSigners}\",\"CreatedTime\":\"{createdTime.ToString("yyyy-MM-ddTHH:mm:ss+08:00")}\",\"FilledTime\":\"{filledTime.ToString("yyyy-MM-ddTHH:mm:ss+08:00")}\",\"UploadedFiles\":[],\"Confirmed\":true}}";
            LeaveView? leave = JsonConvert.DeserializeObject<LeaveView>(json);
            IC1CardBo c1CardBo = A.Fake<IC1CardBo>();
            A.CallTo(() => c1CardBo.CheckC1Card(A<string>.Ignored, A<LeaveView>.Ignored, A<string>.Ignored, A<string>.Ignored)).Throws(new Exception("預計中的錯誤"));
            C1CardController controller = new C1CardController(_attendanceBo, c1CardBo, _configuration, _logger);

            string actual = controller.CheckData(leave);
            CardCheckResult? result = JsonConvert.DeserializeObject<CardCheckResult>(actual);
            Assert.NotNull(result);
            Assert.Equal(AttendanceParameters.ResultGeneralError, result);
        }

        [Theory]
        //[InlineData("0349", "0395", "", 1, 0, "2023-11-06T09:00:00+08:00", "2023-11-06T16:00:00+08:00", "")]
        //[InlineData("0395", "0349", "", 1, 0, "2023-11-06T09:00:00+08:00", "2023-11-06T16:00:00+08:00", "")]
        [InlineData("0395", "0349", "", 3, 0, "2023-11-06T09:00:00+08:00", "2023-11-06T16:00:00+08:00", "")]
        [InlineData("0395", "0349", "", 5, 0, "2023-11-06T09:00:00+08:00", "2023-11-06T16:00:00+08:00", "")]
        //[InlineData("2268", "0349", "0395,0494", 1, 0, "2023-11-06T08:00:00+08:00", "2023-11-08T17:00:00+08:00", "")]
        [InlineData("2268", "0349", "0395,0494", 3, 0, "2023-11-06T08:00:00+08:00", "2023-11-08T17:00:00+08:00", "")]
        [InlineData("2268", "0349", "0395,0494", 5, 0, "2023-11-06T08:00:00+08:00", "2023-11-08T17:00:00+08:00", "")]
        [InlineData("2268", "0349", "0395,0494", 6, 0, "2023-11-06T08:00:00+08:00", "2023-11-08T17:00:00+08:00", "")]
        public void CheckDataTest(string empNo, string deputy, string addSigners, int leaveNumber, int leaveSubNumber, string startTime, string endTime, string expected)
        {
            string ret = CheckData(empNo, deputy, addSigners, leaveNumber, leaveSubNumber, startTime, endTime);
            CardCheckResult? result = JsonConvert.DeserializeObject<CardCheckResult>(ret);
            Assert.NotNull(result);
            Assert.Equal(AttendanceParameters.GeneralErrorMessage, result.Message); // 因為員工未通過認證
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo(empNo);

            var fakeC1CardBo = A.Fake<IC1CardBo>();
            DateTime startDate = DateTime.Parse(startTime);
            DateTime endDate = DateTime.Parse(endTime);
            A.CallTo(() => fakeC1CardBo.IfLeaveRecordExists(empNo, startDate, endDate)).Returns(true);
            A.CallTo(() => fakeC1CardBo.GetAnnualLeaveRemainingHours(empNo, startDate)).Returns(56);
            A.CallTo(() => fakeC1CardBo.GetPostponedLeaveRemainingHours(empNo, startDate)).Returns(10);
            A.CallTo(() => fakeC1CardBo.IfLeaveRecordExists(empNo, startDate, endDate)).Returns(true);
            A.CallTo(() => fakeC1CardBo.GetCompensatoryLeaveRemainingHours(empNo, startDate)).Returns(10);
            var fakeC1CardDao = A.Fake<IC1CardDao>();
            A.CallTo(() => fakeC1CardDao.GetAnnualLeaveRemainingHours(empNo, startDate)).Returns(56);
            ret = CheckData(empNo, deputy, addSigners, leaveNumber, leaveSubNumber, startTime, endTime);
            if (!string.IsNullOrWhiteSpace(ret))
            {
                result = JsonConvert.DeserializeObject<CardCheckResult>(ret);
                Assert.NotNull(result);
                Assert.Equal(expected, result.Message);
            }
            else
            {
                Assert.Equal(expected, ret);
            }
        }

        [Theory]
        [InlineData("2023-11-28+08:00", "0349", 56)]
        [InlineData("2023-11-28+08:00", "0395", 56)]
        [InlineData("2023-11-28+08:00", "2268", 56)]
        public void GetEmpLeaveInfoTest(DateTime date, string empNo, int expected)
        {
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo(empNo);
            string msg = _controller.GetEmpLeaveInfo(date, empNo);
            List<EmpLeaveInfo>? list = JsonConvert.DeserializeObject<List<EmpLeaveInfo>>(msg);
            Assert.NotNull(list);
            Assert.NotEmpty(list);
            EmpLeaveInfo info = list[0];
            Assert.Equal(expected, info.YearAvailableHours);
        }

        [Theory]
        [InlineData("0349", 15, 0, "[]")]
        [InlineData("0395", 1, 0, "[]")]
        [InlineData("0395", 2, 0, "[]")]
        public void GetEventRelatedSheetsTest(string empNo, int leaveNumber, int leaveSubNumber, string expected)
        {
            string msg = _controller.GetEventRelatedSheets(empNo, leaveNumber, leaveSubNumber);
            Assert.Equal(expected, msg);
        }

        [Theory]
        [InlineData("", 19)]
        [InlineData("0305", 16)]
        [InlineData("0391", 16)]
        [InlineData("0395", 16)]
        [InlineData("0494", 16)]
        [InlineData("2024", 16)]
        [InlineData("2259", 16)]
        [InlineData("2268", 16)]
        [InlineData("0349", 19)]
        [InlineData("0741", 19)]
        [InlineData("2191", 19)]
        public void GetLeaveKindsTest(string empNo, int expected)
        {
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo(empNo);
            string leaveKindsString = _controller.GetLeaveKinds(empNo);
            List<LeaveKind>? leaveKinds = JsonConvert.DeserializeObject<List<LeaveKind>>(leaveKindsString);
            if (leaveKinds != null)
            {
                Assert.Equal(expected, leaveKinds.Count);
            }
            leaveKindsString = _controller.GetLeaveKinds();
            leaveKinds = JsonConvert.DeserializeObject<List<LeaveKind>>(leaveKindsString);
            if (leaveKinds != null)
            {
                Assert.Equal(expected, leaveKinds.Count);
            }
        }

        [Theory]
        [InlineData("2023-11-01+08:00", 1, 0, "0349", "2023-01-03+08:00", "2023-12-29+08:00")]
        [InlineData("2023-11-01+08:00", 2, 0, "0349", "2023-10-22+08:00", "2024-01-22+08:00")]
        [InlineData("2023-11-01+08:00", 3, 0, "0349", "2023-01-03+08:00", "2023-12-29+08:00")]
        [InlineData("2023-11-01+08:00", 4, 0, "0349", "2023-01-03+08:00", "2023-12-29+08:00")]
        [InlineData("2023-11-01+08:00", 5, 0, "0349", "2023-01-03+08:00", "2023-12-29+08:00")]
        [InlineData("2023-11-01+08:00", 6, 0, "0349", "2023-01-03+08:00", "2023-12-29+08:00")]
        [InlineData("2023-11-01+08:00", 7, 1, "0349", "2023-10-04+08:00", "2023-12-27+08:00")]
        [InlineData("2023-11-01+08:00", 7, 2, "0349", "2023-11-01+08:00", "2023-11-29+08:00")]
        [InlineData("2023-11-01+08:00", 7, 3, "0349", "2023-11-01+08:00", "2023-11-08+08:00")]
        [InlineData("2023-11-01+08:00", 7, 4, "0349", "2023-11-01+08:00", "2023-11-06+08:00")]
        [InlineData("2023-11-01+08:00", 7, 4, "0000", "2023-11-01+08:00", "2023-11-06+08:00")]
        [InlineData("2023-11-01+08:00", 8, 0, "0000", "2023-02-01+08:00", "2023-12-01+08:00")]
        [InlineData("2023-11-01+08:00", 9, 0, "0000", "2023-01-03+08:00", "2023-12-29+08:00")]
        [InlineData("2023-11-01+08:00", 10, 0, "0000", "2023-01-03+08:00", "2023-12-29+08:00")]
        [InlineData("2023-11-01+08:00", 11, 1, "0000", "2023-11-01+08:00", "2024-02-29+08:00")]
        [InlineData("2023-11-01+08:00", 11, 2, "0000", "2023-11-01+08:00", "2024-02-29+08:00")]
        [InlineData("2023-11-01+08:00", 11, 3, "0000", "2023-11-01+08:00", "2024-02-29+08:00")]
        [InlineData("2023-11-01+08:00", 11, 4, "0000", "2023-11-01+08:00", "2024-02-29+08:00")]
        [InlineData("2023-11-01+08:00", 11, 5, "0000", "2023-11-01+08:00", "2024-02-29+08:00")]
        [InlineData("2023-11-01+08:00", 11, 6, "0000", "2023-11-01+08:00", "2024-02-29+08:00")]
        [InlineData("2023-11-01+08:00", 11, 7, "0000", "2023-11-01+08:00", "2024-02-29+08:00")]
        [InlineData("2023-11-01+08:00", 11, 21, "0000", "2023-11-01+08:00", "2024-02-29+08:00")]
        [InlineData("2023-11-01+08:00", 11, 22, "0000", "2023-11-01+08:00", "2024-02-29+08:00")]
        [InlineData("2023-11-01+08:00", 11, 23, "0000", "2023-11-01+08:00", "2024-02-29+08:00")]
        [InlineData("2023-11-01+08:00", 11, 24, "0000", "2023-11-01+08:00", "2024-02-29+08:00")]
        [InlineData("2023-11-01+08:00", 11, 25, "0000", "2023-11-01+08:00", "2024-02-29+08:00")]
        [InlineData("2023-11-01+08:00", 11, 26, "0000", "2023-11-01+08:00", "2024-02-29+08:00")]
        [InlineData("2023-11-01+08:00", 11, 27, "0000", "2023-11-01+08:00", "2024-02-29+08:00")]
        [InlineData("2023-11-01+08:00", 11, 28, "0000", "2023-11-01+08:00", "2024-02-29+08:00")]
        [InlineData("2023-11-01+08:00", 11, 29, "0000", "2023-11-01+08:00", "2024-02-29+08:00")]
        [InlineData("2023-11-01+08:00", 11, 30, "0000", "2023-11-01+08:00", "2024-02-29+08:00")]
        [InlineData("2023-11-01+08:00", 11, 31, "0000", "2023-11-01+08:00", "2024-02-29+08:00")]
        [InlineData("2023-11-01+08:00", 11, 32, "0000", "2023-11-01+08:00", "2024-02-29+08:00")]
        [InlineData("2023-11-01+08:00", 11, 33, "0000", "2023-11-01+08:00", "2024-02-29+08:00")]
        [InlineData("2023-11-01+08:00", 11, 34, "0000", "2023-11-01+08:00", "2024-02-29+08:00")]
        [InlineData("2023-11-01+08:00", 11, 35, "0000", "2023-11-01+08:00", "2024-02-29+08:00")]
        [InlineData("2023-11-01+08:00", 11, 36, "0000", "2023-11-01+08:00", "2024-02-29+08:00")]
        [InlineData("2023-11-01+08:00", 11, 51, "0000", "2023-11-01+08:00", "2024-02-29+08:00")]
        [InlineData("2023-11-01+08:00", 11, 52, "0000", "2023-11-01+08:00", "2024-02-29+08:00")]
        [InlineData("2023-11-01+08:00", 11, 53, "0000", "2023-11-01+08:00", "2024-02-29+08:00")]
        [InlineData("2023-11-01+08:00", 11, 54, "0000", "2023-11-01+08:00", "2024-02-29+08:00")]
        [InlineData("2023-11-01+08:00", 11, 55, "0000", "2023-11-01+08:00", "2024-02-29+08:00")]
        public void LeavePermittedPeriodTest(DateTime eventDate, int leaveNumber, int subKindNumber, string empNo, DateTime expectStartDate, DateTime expectEndDate)
        {
            var fakeEmployeeBo = A.Fake<IEmployeeBo>();
            A.CallTo(() => fakeEmployeeBo.IsEmployee(empNo)).Returns(true);
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo(empNo);
            string s = _controller.LeavePermittedPeriod(eventDate, leaveNumber, subKindNumber, empNo);
            DualDateTime? a = JsonConvert.DeserializeObject<DualDateTime>(s);
            Assert.NotNull(a);
            Assert.Equal(expectStartDate, a.ExpirationStartDate);
            Assert.Equal(expectEndDate, a.ExpirationEndDate.Date);
        }

        [Theory]
        [InlineData("0349", "0395", "", 2, 0, "2023-11-06T08:00:00+08:00", "2023-11-06T17:00:00+08:00", "2023-11-01+08:00", "")]
        [InlineData("0349", "0395", "", 2, 0, "2023-11-06T09:00:00+08:00", "2023-11-06T16:00:00+08:00", "2023-11-01+08:00", "請假時間須從正常上班時間～正常下班時間")]
        [InlineData("0395", "0349", "", 3, 0, "2023-11-06T09:00:00+08:00", "2023-11-06T16:00:00+08:00", "2023-11-01+08:00", "")]
        [InlineData("0395", "0349", "", 4, 0, "2023-11-06T09:00:00+08:00", "2023-11-06T16:00:00+08:00", "2023-11-01+08:00", "")]
        [InlineData("0395", "0349", "", 5, 0, "2023-11-06T10:05:00+08:00", "2023-11-06T10:08:00+08:00", "2023-11-06+08:00", "")]
        [InlineData("0395", "0349", "", 5, 0, "2023-11-06T10:00:00+08:00", "2023-11-06T10:10:00+08:00", "2023-11-06+08:00", "")]
        [InlineData("0395", "0349", "", 5, 0, "2023-11-06T12:00:00+08:00", "2023-11-06T13:00:00+08:00", "2023-11-06+08:00", "請假時數為0，無須請假")]
        [InlineData("0395", "0349", "", 5, 0, "2023-11-06T15:00:00+08:00", "2023-11-06T15:10:00+08:00", "2023-11-06+08:00", "")]
        [InlineData("0395", "0349", "", 5, 0, "2023-11-06T15:02:00+08:00", "2023-11-06T15:07:00+08:00", "2023-11-06+08:00", "")]
        [InlineData("0395", "0349", "", 5, 0, "2023-11-06T09:00:00+08:00", "2023-11-06T16:00:00+08:00", "2023-11-01+08:00", "")]
        [InlineData("0395", "0349", "", 5, 0, "2023-11-06T08:00:00+08:00", "2023-11-06T17:10:00+08:00", "2023-11-06+08:00", "請假時數超過每日正常工時，請修改請假時間")]
        [InlineData("0395", "0349", "", 2, 0, "2023-12-31T09:00:00+08:00", "2024-02-02T16:00:00+08:00", "2023-12-31+08:00", "不得跨年請假，請分年度填報請假卡")]
        [InlineData("0395", "0349", "", 3, 0, "2023-12-31T09:00:00+08:00", "2024-02-02T16:00:00+08:00", "2023-12-31+08:00", "不得跨年請假，請分年度填報請假卡")]
        [InlineData("0395", "0349", "", 5, 0, "2023-12-31T09:00:00+08:00", "2024-02-02T16:00:00+08:00", "2023-12-31+08:00", "不得跨年請假，請分年度填報請假卡")]
        [InlineData("0395", "0349", "", 6, 0, "2023-12-31T09:00:00+08:00", "2024-02-02T16:00:00+08:00", "2023-12-31+08:00", "不得跨年請假，請分年度填報請假卡")]
        [InlineData("0395", "0349", "", 7, 0, "2023-12-31T09:00:00+08:00", "2024-02-02T16:00:00+08:00", "2023-12-31+08:00", "【假別細項】不可空白")]
        [InlineData("0395", "0349", "", 7, 1, "2023-12-31T09:00:00+08:00", "2024-02-02T16:00:00+08:00", "2023-12-31+08:00", "產假限女性申請")]
        [InlineData("0395", "0349", "", 8, 0, "2023-12-31T09:00:00+08:00", "2024-02-02T16:00:00+08:00", "2023-12-31+08:00", "不得跨年請假，請分年度填報請假卡")]
        [InlineData("0395", "0349", "", 9, 0, "2023-12-31T09:00:00+08:00", "2024-02-02T16:00:00+08:00", "2023-12-31+08:00", "不得跨年請假，請分年度填報請假卡")]
        [InlineData("0178", "0349", "", 11, 0, "2023-12-31T09:00:00+08:00", "2024-02-02T16:00:00+08:00", "2023-12-31+08:00", "不得跨年請假，請分年度填報請假卡")]
        [InlineData("0178", "0349", "", 15, 0, "2023-12-31T09:00:00+08:00", "2024-02-02T16:00:00+08:00", "2023-12-31+08:00", "產檢假限女性申請")]
        [InlineData("0178", "0349", "", 16, 0, "2023-12-31T09:00:00+08:00", "2024-02-02T16:00:00+08:00", "2023-12-31+08:00", "生理假限女性申請")]
        [InlineData("0178", "0349", "", 21, 0, "2023-12-31T09:00:00+08:00", "2024-02-02T16:00:00+08:00", "2023-12-31+08:00", "您的生日福利方案未選擇【生日假】，不得申請")]
        [InlineData("0178", "0349", "", 22, 0, "2023-12-31T09:00:00+08:00", "2024-02-02T16:00:00+08:00", "2023-12-31+08:00", "不得跨年請假，請分年度填報請假卡")]
        [InlineData("0178", "0349", "", 23, 0, "2023-12-31T09:00:00+08:00", "2024-02-02T16:00:00+08:00", "2023-12-31+08:00", "不得跨年請假，請分年度填報請假卡")]
        [InlineData("2268", "0349", "0395,0494", 3, 0, "2023-11-06T08:00:00+08:00", "2023-11-08T17:00:00+08:00", "2023-11-01+08:00", "")]
        [InlineData("2268", "0349", "0395,0494", 5, 0, "2023-11-06T08:00:00+08:00", "2023-11-08T17:00:00+08:00", "2023-11-01+08:00", "")]
        [InlineData("2268", "0349", "0395,0494", 6, 0, "2023-11-06T08:00:00+08:00", "2023-11-08T17:00:00+08:00", "2023-11-01+08:00", "")]
        [InlineData("2268", "0349", "0395,0494", 7, 0, "2023-11-06T08:00:00+08:00", "2023-11-08T17:00:00+08:00", "2023-11-01+08:00", "【假別細項】不可空白")]
        [InlineData("2268", "0349", "0395,0494", 7, 1, "2023-11-06T08:00:00+08:00", "2023-11-08T17:00:00+08:00", "2023-11-01+08:00", "產假限女性申請")]
        [InlineData("0349", "0395", "0395,0494", 7, 0, "2023-11-06T08:00:00+08:00", "2023-11-08T17:00:00+08:00", "2023-11-01+08:00", "【假別細項】不可空白")]
        [InlineData("0349", "0395", "0395,0494", 7, 1, "2023-11-06T08:00:00+08:00", "2023-11-08T17:00:00+08:00", "2023-11-01+08:00", "")]
        public async Task SubmitTest(string empNo, string deputy, string addSigners, int leaveNumber, int leaveSubNumber, string startTime, string endTime, string eventDate, string expected)
        {
            CardCheckResult? result = await AddC1Card(empNo, deputy, addSigners, leaveNumber, leaveSubNumber, startTime, endTime, eventDate);
            Assert.NotNull(result);
            Assert.Equal(AttendanceParameters.BadEmployeeNumber, result.Message); // 因為員工未通過認證
            var fakeEmployeeBo = A.Fake<IEmployeeBo>();
            A.CallTo(() => fakeEmployeeBo.IsEmployee(empNo)).Returns(true);
            A.CallTo(() => fakeEmployeeBo.IsEmployee(deputy)).Returns(true);

            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo(empNo);
            result = await AddC1Card(empNo, deputy, addSigners, leaveNumber, leaveSubNumber, startTime, endTime, eventDate);
            Assert.NotNull(result);
            Assert.Equal(expected, result.Message);
        }

        private class DualDateTime
        {

            public DateTime ExpirationEndDate = DateTime.MaxValue;
            public DateTime ExpirationStartDate = DateTime.MinValue;

        }

    }
}
