﻿using Sinotech.Mis.Common;
using System.Collections.Generic;
using System.Data;

namespace Sinotech.Mis.HR.Attendance.Common
{
    public interface IEmployeeBo
    {

        /// <summary>取得部門名稱</summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public string GetDepartmentName(string employeeNumber);

        /// <summary>
        /// 取得員工所屬部門編號
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns></returns>
        public int GetDepartmentNumber(string employeeNumber);
        
        /// <summary>取得部門簡稱</summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public string GetDepartmentShortName(string employeeNumber);

        /// <summary>
        /// 取得所有在職員工DataTable
        /// 會將優先部門放在前面
        /// </summary>
        ///<param name="deptNo">優先部門代號，預設 -1 表示不將優先部門置前</param>
        /// <returns></returns>
        public DataTable GetEmployeeDataTable(int deptNo = -1);

        /// <summary>
        /// 取得員工詳細資料
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns></returns>
        public Employee? GetEmployeeDetail(string employeeNumber);

        /// <summary>
        /// 取得員工姓名
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns></returns>
        public string GetEmployeeName(string employeeNumber);

        /// <summary>
        /// 取得所有現職員工 JSON String
        /// 會將優先部門放在前面
        /// </summary>
        ///<param name="deptNo">優先部門代號，預設 -1 表示不將優先部門置前</param>
        /// <returns>所有現職員工</returns>
        public string GetEmployeesSimpleJson(int deptNo);

        /// <summary>
        /// 取得所有現職員工 JSON String
        /// </summary>
        /// <param name="sortField">排序欄位</param>
        /// <param name="ascDesc">升冪或降冪 ASC or DESC</param>
        /// <returns>所有現職員工</returns>
        public string GetEmployeesJson(string sortField = "EmpNo", string ascDesc = "ASC");

        /// <summary>
        /// 取得所有員工 JSON String，包括離職員工
        /// 會將優先部門放在前面
        /// </summary>
        ///<param name="deptNo">優先部門代號</param>
        /// <returns>所有現職員工</returns>
        public string GetAllEmployeesJson(int deptNo);

        /// <summary>取得員工基本資料</summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public string GetEmployeeSimpleJson(string employeeNumber);

        /// <summary>取得員工精簡資料</summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public string GetEmployeeTinyJson(string employeeNumber);

        /// <summary>
        /// 是否為副理以上
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>副理以上傳回<c>true</c>，否則傳回<c>false</c></returns>
        public bool IsAboveDeputyManager(string empNo);

        /// <summary>
        /// 是否為副理以上
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="deptNo">部門代號</param>
        /// <returns>副理以上傳回<c>true</c>，否則傳回<c>false</c></returns>
        public bool IsAboveDeputyManager(string empNo, int deptNo);


        /// <summary>
        /// 員工是否為司機
        /// </summary>
        /// <param name="empNo"></param>
        /// <returns></returns>
        public bool IsDriver(string empNo);

        /// <summary>
        /// 取得組長所屬的團隊編號
        /// </summary>
        /// <param name="empNo"></param>
        /// <returns></returns>
        public List<int> GetTeamLeaderTeamNos(string empNo);

        /// <summary>
        /// 副理以上主管部門編號
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>副理以上主管部門編號列表</returns>
        public List<int> GetAboveDeputyManagerDeptNos(string empNo);

        /// <summary>
        /// 是否為現職員工
        /// </summary>
        /// <param name="empNo">The emp no.</param>
        /// <returns><c>true</c> 是現職員工，否則傳回 <c>false</c>.</returns>
        public bool IsEmployee(string empNo);

        /// <summary>
        /// 是否為該員工的組長
        /// </summary>
        /// <param name="leaderId">組長員工編號</param>
        /// <param name="userId">員工編號</param>
        /// <returns></returns>
        public bool IsTeamLeaderOf(string leaderId, string userId);

    }
}
