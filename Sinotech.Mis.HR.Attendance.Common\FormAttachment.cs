﻿using System;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 表單附件
    /// </summary>
    public class FormAttachment
    {
        /// <summary>
        /// 流水號
        /// </summary>
        public int? ID { get; set; }

        /// <summary>
        /// 表單識別碼
        /// </summary>
        public Guid FormUID { get; set; }

        /// <summary>
        /// 檔案存放目錄
        /// </summary>
        public string FileDirectory { get; set; } = string.Empty;

        /// <summary>
        /// 原始檔名
        /// </summary>
        public string OriginalFileName { get; set; } = string.Empty;

        /// <summary>
        /// 編碼檔名
        /// </summary>
        public string EncodedFileName { get; set; } = string.Empty;

        /// <summary>
        /// 瀏覽網址
        /// </summary>
        public string? URL { get; set; } = null;
    }
}
