﻿using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using System;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves
{
    /// <summary>
    /// 救災復原假類別
    /// </summary>
    [LeaveKind(LeaveKindEnum.DisasterRecoveryLeave)]
    public class DisasterRecoveryLeave : C1CardBase
    {
        #region CheckResult
        // 【請假事由】不可空白
        public const int CodeReasonFieldRequired = 3006303;
        private readonly string _messageReasonFieldRequired = AttendanceParameters.LeaveMustHaveReason;
        private readonly CardStatusEnum _statusReasonFieldRequired = CardStatusEnum.Error;

        private CardCheckResult? _resultReasonFieldRequired;
        private CardCheckResult ResultReasonFieldRequired =>
            _resultReasonFieldRequired ??=
            new CardCheckResult(CodeReasonFieldRequired, _statusReasonFieldRequired, _messageReasonFieldRequired);


        // 「附件」未上傳
        public const int CodeAttachmentRequired = 3024200; //TODO: 暫定，待確認
        private readonly string _messageAttachmentRequired = "請依「員工因應天然災害之慰助要點」第五點檢附相關證明文件及核准簽";
        private readonly CardStatusEnum _statusAttachmentRequired = CardStatusEnum.Error;
        private CardCheckResult? _resultAttachmentRequired;
        private CardCheckResult ResultAttachmentRequired =>
            _resultAttachmentRequired ??=
            new CardCheckResult(CodeAttachmentRequired, _statusAttachmentRequired, _messageAttachmentRequired);


        #endregion


        /// <summary>
        /// 建構子
        /// </summary>
        /// <param name="c1Card"></param>
        /// <param name="c1CardBo"></param>

        public DisasterRecoveryLeave(C1Card c1Card, IC1CardBo c1CardBo) : base(c1Card, c1CardBo)
        {
        }

        /// <summary>
        /// 檢查是否能請此假
        /// </summary>
        /// <returns></returns>
        internal override CardCheckResult CanTakeThisLeave()
        {
            return ResultOk;
        }

        /// <summary>
        /// 檢查是否超假
        /// </summary>
        /// <returns></returns>
        internal override CardCheckResult CheckOverPermittedLeaveHours()
        {
            return ResultOk;
        }

        /// <summary>
        /// 檢查必要欄位
        /// </summary>
        /// <returns></returns>
        internal override CardCheckResult CheckRequiredFields()
        {
            var result = base.CheckRequiredFields();
            if (result != ResultOk)
            {
                return result;
            }

            // 「請假事由」不可空白
            if (string.IsNullOrWhiteSpace(_c1Card.Reason))
            {
                return ResultReasonFieldRequired;
            }

            // 「附件」未上傳
            if (_c1Card.UploadedFiles == null || _c1Card.UploadedFiles.Count == 0)
            {
                return ResultAttachmentRequired;
            }
            return ResultOk;
        }

        /// <summary>
        /// 計算最早可請假日期與最晚可請假日期
        /// </summary>
        /// <param name="date">請假日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public override (DateTime, DateTime) CalculateLeavePermittedPeriod(DateTime date, string empNo) => DefaultCalculateLeavePermittedPeriod(date, empNo);

        /// <summary>
        /// 此性別是否可請此假別
        /// </summary>
        /// <param name="gender"></param>
        /// <returns></returns>
        public static bool IsAllowForThisGender(Gender gender)
        {
            return true;
        }

    }
}
