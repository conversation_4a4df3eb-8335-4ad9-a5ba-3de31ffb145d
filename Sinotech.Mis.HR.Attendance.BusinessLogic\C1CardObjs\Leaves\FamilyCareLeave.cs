﻿﻿using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using System;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves
{
    /// <summary>
    /// 家庭照顧假處理類別
    /// 此類別負責處理員工家庭照顧假的申請、驗證及相關業務邏輯
    /// 家庭照顧假規則：
    /// 1. 每年度有一定的時數上限
    /// 2. 家庭照顧假的時數會併入事假計算，不得超過事假年度上限
    /// 3. 用於照顧家庭成員（如配偶、子女、父母等）的重大疾病或緊急事故
    /// </summary>
    [LeaveKind(LeaveKindEnum.FamilyCareLeave)] // 標記假別類型為家庭照顧假
    public class FamilyCareLeave : C1CardBase
    {
        #region CheckResult
        // 檢查結果定義區域
        // 此區域定義家庭照顧假驗證失敗時的錯誤代碼、狀態及訊息

        /// <summary>
        /// 錯誤代碼：超過家庭照顧假年度額度
        /// 當申請的家庭照顧假時數超過年度上限時使用
        /// </summary>
        public const int CodeExceedQuota = 3019301;
        
        /// <summary>
        /// 錯誤狀態：超過家庭照顧假額度
        /// </summary>
        private readonly CardStatusEnum _statusExceedQuota = CardStatusEnum.Error;
        
        /// <summary>
        /// 錯誤訊息模板：超過家庭照顧假額度
        /// {0} = 本次申請的請假時數
        /// {1} = 家庭照顧假年度上限時數
        /// {2} = 年度累計已請的家庭照顧假時數
        /// </summary>
        private readonly string _messagePatternExceedQuota = "您本次請假 {0} 小時，年度上限 {1} 小時，累計已請 {2} 小時，不得超假";

        /// <summary>
        /// 錯誤代碼：超過事假年度額度（含家庭照顧假）
        /// 家庭照顧假時數會併入事假計算，當總時數超過事假年度上限時使用
        /// </summary>
        public const int CodeExceedPersonalLeaveQuota = 3019302;
        
        /// <summary>
        /// 錯誤狀態：超過事假年度額度
        /// </summary>
        private readonly CardStatusEnum _statusExceedPersonalLeaveQuota = CardStatusEnum.Error;
        
        /// <summary>
        /// 錯誤訊息模板：超過事假年度額度（含家庭照顧假）
        /// {0} = 本次申請的家庭照顧假時數
        /// {1} = 年度累計已請的家庭照顧假時數
        /// {2} = 年度累計已請的事假時數
        /// {3} = 本次申請後的總時數（事假+家庭照顧假）
        /// {4} = 事假年度上限時數
        /// </summary>
        private readonly string _messagePatternExceedPersonalLeaveQuota = "您本次請假 {0} 小時，年度累計已請 {1} 小時，併入事假年度累計已請 {2} 小時，總計 {3} 小時，已超過事假年度上限（{4} 小時），請改以事假申請。＜家庭照顧假請假時數須併入事假計算，如超過事假年度上限必須改以事假申請＞";

        #endregion


        /// <summary>
        /// 家庭照顧假建構子
        /// 初始化家庭照顧假處理物件，注入必要的相依性
        /// </summary>
        /// <param name="c1Card">請假單據物件，包含請假的基本資訊</param>
        /// <param name="c1CardBo">請假業務邏輯處理物件，提供各種業務邏輯方法</param>
        public FamilyCareLeave(C1Card c1Card, IC1CardBo c1CardBo) : base(c1Card, c1CardBo)
        {
            // 建構子主體為空，所有初始化工作由基底類別處理
        }

        /// <summary>
        /// 檢查員工是否符合申請家庭照顧假的條件
        /// 家庭照顧假的申請前提是員工必須已經有休假記錄建立
        /// 這通常表示員工已經完成相關的設定程序
        /// </summary>
        /// <returns>檢查結果，包含狀態碼、狀態及錯誤訊息（如有）</returns>
        internal override CardCheckResult CanTakeThisLeave()
        {
            // 檢查員工是否已經建立休假記錄
            // 家庭照顧假需要有既存的休假記錄作為基礎
            if (!IfLeaveRecordExists())
            {
                return ResultLeaveRecordNotCreatedYet;
            }

            // 所有條件都通過，允許申請
            return ResultOk;
        }

        /// <summary>
        /// 檢查是否超過允許的家庭照顧假時數額度
        /// 家庭照顧假有兩層限制需要檢查：
        /// 1. 家庭照顧假本身的年度上限
        /// 2. 家庭照顧假併入事假後不得超過事假的年度上限
        /// </summary>
        /// <returns>檢查結果，包含狀態碼、狀態及錯誤訊息（如有）</returns>
        internal override CardCheckResult CheckOverPermittedLeaveHours()
        {
            // 第一層檢查：家庭照顧假年度總請假時數不得超過其專屬上限
            var result = CheckExceedQuota();
            if (result.Code != ResultOk.Code)
            {
                return result;
            }

            // 第二層檢查：家庭照顧假併入事假後，不可超過事假全年上限
            // 根據勞基法規定，家庭照顧假時數須併入事假計算
            result = CheckExceedPersonalLeaveQuota();
            if (result.Code != ResultOk.Code)
            {
                return result;
            }

            // 所有額度檢查都通過
            return ResultOk;
        }

        /// <summary>
        /// 檢查是否超過家庭照顧假的年度額度上限
        /// 此方法驗證本次申請的時數加上已使用的時數是否超過年度上限
        /// </summary>
        /// <returns>檢查結果，如果超過額度則回傳錯誤結果，否則回傳成功</returns>
        private CardCheckResult CheckExceedQuota()
        {
            // 取得家庭照顧假的年度可用時數（上限）
            var available = GetFamilyCareLeaveYearAvailableHours();
            // 取得家庭照顧假的年度已使用時數
            var used = GetFamilyCareLeaveYearUsedHours();

            // 檢查本次申請時數加上已使用時數是否超過可用時數
            if (used + TotalHours > available)
            {
                // 建立超額錯誤結果，包含詳細的時數資訊
                return new CardCheckResult(CodeExceedQuota, _statusExceedQuota,
                    string.Format(_messagePatternExceedQuota, TotalHours, available, used));
            }

            // 額度檢查通過
            return ResultOk;
        }

        /// <summary>
        /// 檢查家庭照顧假併入事假後是否超過事假的年度額度上限
        /// 根據勞基法規定，家庭照顧假時數須併入事假計算
        /// 如果併計後超過事假上限，員工必須改以事假申請
        /// </summary>
        /// <returns>檢查結果，如果超過事假額度則回傳錯誤結果，否則回傳成功</returns>
        private CardCheckResult CheckExceedPersonalLeaveQuota()
        {
            // 取得事假的年度可用時數（上限）
            var personalLeaveAvailableHours = GetPersonalLeaveYearAvailableHours();
            // 取得事假的年度已使用時數
            var personalLeaveUsedHours = GetPersonalLeaveYearUsedHours();
            // 取得家庭照顧假的年度已使用時數
            var usedHours = GetFamilyCareLeaveYearUsedHours();

            // 計算事假與家庭照顧假的累計使用時數
            var aggregateUsed = personalLeaveUsedHours + usedHours;

            // 檢查本次申請時數加上累計使用時數是否超過事假可用時數
            if (TotalHours + aggregateUsed > personalLeaveAvailableHours)
            {
                // 建立超過事假額度的錯誤結果，包含詳細的時數分析
                return new CardCheckResult(CodeExceedPersonalLeaveQuota, _statusExceedPersonalLeaveQuota,
                    string.Format(_messagePatternExceedPersonalLeaveQuota,
                                    TotalHours, usedHours, aggregateUsed,
                                    TotalHours + aggregateUsed, personalLeaveAvailableHours));
            }

            // 事假額度檢查通過
            return ResultOk;
        }

        /// <summary>
        /// 檢查家庭照顧假申請單的必要欄位是否已填寫完整
        /// 此方法先呼叫基底類別的欄位檢查，確保基本欄位都已填寫
        /// 家庭照顧假目前沒有額外的必要欄位需要檢查
        /// </summary>
        /// <returns>檢查結果，包含狀態碼、狀態及錯誤訊息（如有）</returns>
        internal override CardCheckResult CheckRequiredFields()
        {
            // 呼叫基底類別的必要欄位檢查
            // 包括員工編號、請假日期、請假時數等基本欄位
            var result = base.CheckRequiredFields();
            if (result != ResultOk)
            {
                return result;
            }

            // 家庭照顧假沒有額外的必要欄位需要檢查
            // 如果基底類別檢查通過，則直接回傳成功
            return ResultOk;
        }

        /// <summary>
        /// 計算家庭照顧假的可申請期間（最早及最晚可請假日期）
        /// 家庭照顧假使用預設的計算邏輯，沒有特殊的時間限制
        /// 通常家庭照顧假的使用期限由系統設定或公司政策決定
        /// </summary>
        /// <param name="date">參考日期（通常為申請日期）</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>Tuple包含最早可請假日期及最晚可請假日期</returns>
        public override (DateTime, DateTime) CalculateLeavePermittedPeriod(DateTime date, string empNo) => DefaultCalculateLeavePermittedPeriod(date, empNo);

        /// <summary>
        /// 檢查指定性別是否可以申請家庭照顧假
        /// 家庭照顧假不分性別，所有員工都可以申請
        /// 這反映了現代家庭照顧責任不分性別的觀念
        /// </summary>
        /// <param name="gender">員工性別</param>
        /// <returns>永遠回傳true，因為家庭照顧假對所有性別開放</returns>
        public static bool IsAllowForThisGender(Gender gender)
        {
            // 家庭照顧假是所有員工都享有的權利，不分性別
            // 無論男性或女性員工都有照顧家庭成員的需要和權利
            return true;
        }

        #region Tools
        // 輔助工具方法區域
        // 此區域包含各種私有方法，用於支援主要業務邏輯的執行
        // 這些方法主要負責從業務邏輯物件中取得家庭照顧假和事假的相關數據

        /// <summary>
        /// 取得員工家庭照顧假的年度可用時數（上限）
        /// 此方法委派給業務邏輯物件來查詢員工在指定年度的家庭照顧假時數上限
        /// </summary>
        /// <returns>員工家庭照顧假的年度可用時數（以小時為單位）</returns>
        private int GetFamilyCareLeaveYearAvailableHours()
        {
            // 委派給業務邏輯物件來取得員工的家庭照顧假年度可用時數
            // 參數包括員工編號和請假起始日期，用於確定適用的年度和政策
            return _c1CardBo.GetFamilyCareLeaveYearAvailableHours(_c1Card.EmpNo, _c1Card.StartDate);
        }

        /// <summary>
        /// 取得員工家庭照顧假的年度已使用時數
        /// 此方法委派給業務邏輯物件來查詢員工在指定年度已經使用的家庭照顧假時數
        /// </summary>
        /// <returns>員工家庭照顧假的年度已使用時數（以小時為單位）</returns>
        private int GetFamilyCareLeaveYearUsedHours()
        {
            // 委派給業務邏輯物件來取得員工的家庭照顧假年度已使用時數
            // 計算會考慮截至請假起始日期前的所有家庭照顧假使用記錄
            return _c1CardBo.GetFamilyCareLeaveYearUsedHours(_c1Card.EmpNo, _c1Card.StartDate);
        }

        /// <summary>
        /// 取得員工事假的年度可用時數（上限）
        /// 此方法委派給業務邏輯物件來查詢員工在指定年度的事假時數上限
        /// 因為家庭照顧假須併入事假計算，所以需要取得事假的額度資訊
        /// </summary>
        /// <returns>員工事假的年度可用時數（以小時為單位）</returns>
        private int GetPersonalLeaveYearAvailableHours()
        {
            // 委派給業務邏輯物件來取得員工的事假年度可用時數
            // 這個額度將用於檢查家庭照顧假併入事假後是否超過上限
            return _c1CardBo.GetPersonalLeaveYearAvailableHours(_c1Card.EmpNo, _c1Card.StartDate);
        }

        /// <summary>
        /// 取得員工事假的年度已使用時數
        /// 此方法委派給業務邏輯物件來查詢員工在指定年度已經使用的事假時數
        /// 這個數據用於計算家庭照顧假併入事假後的總使用時數
        /// </summary>
        /// <returns>員工事假的年度已使用時數（以小時為單位）</returns>
        private int GetPersonalLeaveYearUsedHours()
        {
            // 委派給業務邏輯物件來取得員工的事假年度已使用時數
            // 計算會考慮截至請假起始日期前的所有事假使用記錄
            // 此數據與家庭照顧假使用時數合併，用於檢查是否超過事假上限
            return _c1CardBo.GetPersonalLeaveYearUsedHours(_c1Card.EmpNo, _c1Card.StartDate);
        }

        #endregion

    }
}
