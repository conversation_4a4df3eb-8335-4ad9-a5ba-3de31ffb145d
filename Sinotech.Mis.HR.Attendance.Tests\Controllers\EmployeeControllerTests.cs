﻿using FakeItEasy;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sinotech.Mis.HR.Attendance.BusinessLogic;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.Tests;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using Xunit;
using static Antlr4.Runtime.Atn.SemanticContext;

#nullable enable
namespace Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers.Tests
{

    [ExcludeFromCodeCoverage]
    public class EmployeeControllerTests
    {

        private readonly IAttendanceBo _attendanceBo;
        private readonly EmployeeController _controller;
        private readonly EmployeeBo _employeeBo;
        private readonly IAttendanceBo _fakeAttendanceBo;
        private readonly IConfiguration _configuration;
        private readonly EmployeeController _fakeController;

        private readonly EmployeeBo _fakeEmployeeBo;
        private readonly SinoSignBo _fakeSinoSignBo;
        private readonly SinoSignBo _sinoSignBo;

        public EmployeeControllerTests(EmployeeBo employeeBo, IAttendanceBo attendanceBo, SinoSignBo sinoSignBo, ILogger<EmployeeController> logger)
        {
            _employeeBo = employeeBo;
            _attendanceBo = attendanceBo;
            _sinoSignBo = sinoSignBo;
            IConfiguration configuration = new ConfigurationBuilder().
                AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).Build();
            _configuration = configuration;
            _controller = new EmployeeController(
                _employeeBo,
                _attendanceBo,
                _sinoSignBo,
                configuration,
                logger);


            _fakeEmployeeBo = A.Fake<EmployeeBo>();
            _fakeAttendanceBo = A.Fake<IAttendanceBo>();
            _fakeSinoSignBo = A.Fake<SinoSignBo>();


            _fakeController = new EmployeeController(
                _fakeEmployeeBo,
                _fakeAttendanceBo,
                _fakeSinoSignBo,
                configuration,
                logger);
        }

        [Theory]
        [InlineData("2025")]
        [InlineData("2259")]
        public void CanCallGetDeputiesWithValidEmpNo(string empNo)
        {
            // Act
            var result = _controller.GetDeputies(empNo);

            // Assert
            Assert.NotEmpty(result);
            Assert.NotEqual("{}", result);
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("   ")]
        public void CannotCallGetDeputiesWithStringWithInvalidEmpNo(string empNo)
        {
            var result = _controller.GetDeputies(empNo);
            Assert.NotEmpty(result);
            Assert.Equal("{}", result);
        }

        [Fact]
        public void GetAllEmployeesTest()
        {
            string result = _controller.GetAllEmployees();
            Assert.NotEmpty(result);
            Assert.NotEqual("[]", result);
        }

        /// <summary>
        /// 此處回傳 false 表示該部門沒有可加班員工，或是目前無此部門
        /// </summary>
        /// <param name="deptNo"></param>
        /// <param name="expected"></param>
        [Theory]
        [InlineData(-2, false)]
        [InlineData(-1, false)]
        [InlineData(0, false)]
        [InlineData(1, false)]
        [InlineData(2, true)]
        [InlineData(3, true)]
        [InlineData(4, true)]
        [InlineData(5, false)]
        [InlineData(6, false)]
        [InlineData(7, false)]
        [InlineData(8, true)]
        [InlineData(9, true)]
        [InlineData(10, true)]
        [InlineData(11, false)]
        [InlineData(12, false)]
        [InlineData(13, false)]
        [InlineData(14, false)]
        [InlineData(15, false)]
        [InlineData(16, true)]
        [InlineData(17, true)]
        [InlineData(18, true)]

        public void GetEligibleOvertimeEmployeesTest(int deptNo, bool expected)
        {
            string result = _controller.GetEligibleOvertimeEmployees(deptNo);
            DataTable? dt = JsonConvert.DeserializeObject<DataTable>(result);
            Assert.NotNull(dt);
            DataRow dr = dt.Rows[0];
            int actualDeptno = int.Parse(dr["DeptNo"].ToString());
            bool actual = deptNo == actualDeptno;
            Assert.Equal(expected, actual);
        }

        [Theory]
        [InlineData(-2, true)]
        [InlineData(-1, true)]
        [InlineData(0, true)]
        [InlineData(1, true)]
        [InlineData(2, true)]
        [InlineData(3, true)]
        [InlineData(4, true)]
        [InlineData(5, true)]
        [InlineData(6, true)]
        [InlineData(7, true)]
        [InlineData(8, true)]
        [InlineData(9, true)]
        [InlineData(10, true)]
        [InlineData(11, true)]
        [InlineData(12, true)]
        [InlineData(13, true)]
        [InlineData(14, true)]
        [InlineData(15, true)]
        [InlineData(16, true)]
        [InlineData(17, true)]
        [InlineData(18, true)]
        public void GetEmployeesTest(int deptNo, bool expected)
        {
            string result = _controller.GetEmployees(deptNo);
            DataTable? dt = JsonConvert.DeserializeObject<DataTable>(result);
            Assert.NotNull(dt);
            if (expected)
            {
                Assert.NotEmpty(dt.Rows);
            }
            else
            {
                Assert.Empty(dt.Rows);
            }
        }

        [Theory]
        [InlineData("2268", 1)]
        [InlineData("2096", 2)]
        [InlineData("2010", 2)]
        [InlineData("0395", 1)]
        public void GetUserRoles_EmpNo_Test(string empNo, int expected)
        {
            string result = _controller.GetUserRoles(empNo);
            List<Role>? list = JsonConvert.DeserializeObject<List<Role>>(result);
            Assert.NotNull(list);
            Assert.Equal(expected, list.Count);
        }

        [Theory]
        [InlineData("", false)]
        [InlineData("0274", false)]
        [InlineData("0276", true)]
        [InlineData("0391", false)]
        [InlineData("0178", true)]
        [InlineData("0349", true)]
        [InlineData("0395", true)]
        [InlineData("2268", true)]
        public void IsOvertimeAllowedTest(string empNo, bool expected)
        {
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo(empNo);
            bool result = _controller.IsOvertimeAllowed(empNo);
            Assert.Equal(expected, result);
        }

        [Fact]
        public void IsOvertimeAllowed_Exception()
        {
            string empNo = "0395";
            DateTime date = DateTime.Now;
            IAttendanceBo attendanceBo = A.Fake<IAttendanceBo>();
            A.CallTo(() => _fakeAttendanceBo.IsOvertimeAllowed(A<string>.Ignored, A<DateTime>.Ignored)).Throws<Exception>();
            EmployeeController controller = new EmployeeController(_fakeEmployeeBo, attendanceBo, _fakeSinoSignBo, _configuration, A.Fake<ILogger<EmployeeController>>());
            controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo(empNo);
            bool result = controller.IsOvertimeAllowed(empNo);
            Assert.False(false);
        }

    }
}

