﻿using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves.Tests
{
    [ExcludeFromCodeCoverage]
    public class PaternityLeaveTests
    {

        private readonly IC1CardBo _c1CardBo;

        public PaternityLeaveTests(C1CardBo c1CardBo)
        {
            _c1CardBo = c1CardBo;
        }

        [Theory]
        [InlineData("2022-10-11", "2268", "2022-01-11", "2022-11-11")]
        [InlineData("2022-10-31", "2268", "2022-01-31", "2022-11-30")]
        [InlineData("2022-11-30", "2268", "2022-02-28", "2022-12-30")]
        [InlineData("2022-12-10", "2268", "2022-03-10", "2023-01-10")]
        [InlineData("2023-01-28", "2268", "2022-04-28", "2023-02-28")]
        [InlineData("2023-01-29", "2268", "2022-04-29", "2023-02-28")]
        [InlineData("2023-01-30", "2268", "2022-04-30", "2023-02-28")]
        [InlineData("2023-01-31", "2268", "2022-04-30", "2023-02-28")]
        [InlineData("2023-02-01", "2268", "2022-05-01", "2023-03-01")]
        [InlineData("2023-02-02", "2268", "2022-05-02", "2023-03-02")]
        [InlineData("2023-02-14", "2268", "2022-05-14", "2023-03-14")]
        [InlineData("2023-02-28", "2268", "2022-05-28", "2023-03-28")]
        [InlineData("2023-04-10", "2268", "2022-07-10", "2023-05-10")]
        [InlineData("2024-02-28", "2268", "2023-05-28", "2024-03-28")]
        [InlineData("2024-02-29", "2268", "2023-05-29", "2024-03-29")]
        [InlineData("2024-03-01", "2268", "2023-06-01", "2024-04-01")]
        public void CalculateLeavePermittedPeriodTest(DateTime eventDate, string empNo, DateTime expectedStartDate, DateTime expectedEndDate)
        {
            C1Card c1Card = new C1Card();
            c1Card.EventDate = eventDate;
            PaternityLeave paternityLeave = new PaternityLeave(c1Card, _c1CardBo);
            (DateTime startDate, DateTime endDate) = paternityLeave.CalculateLeavePermittedPeriod(eventDate, empNo);
            Assert.Equal(expectedStartDate, startDate);
            Assert.Equal(expectedEndDate, endDate);
            (startDate, endDate) = paternityLeave.CalculateLeavePermittedPeriod(DateTime.MinValue, empNo);
            Assert.Equal(expectedStartDate, startDate);
            Assert.Equal(expectedEndDate, endDate);
            (startDate, endDate) = paternityLeave.CalculateLeavePermittedPeriod(DateTime.MaxValue, empNo);
            Assert.Equal(expectedStartDate, startDate);
            Assert.Equal(expectedEndDate, endDate);
        }

        [Theory]
        [InlineData("2022-10-11", "2268", "2022-01-31", "2022-11-30", 3008301)]
        [InlineData("2022-10-11", "2268", "2022-01-01", "2022-01-02", 3000315)]
        [InlineData("2022-10-11", "2268", "2022-02-28", "2022-03-01", 3000100)]
        public void CheckOverPermittedLeaveHoursTest(DateTime eventDate, string empNo, DateTime startDate, DateTime endDate, int expectedCode)
        {
            C1Card c1Card = new C1Card();
            c1Card.StartDate = startDate;
            c1Card.EndDate = endDate;
            c1Card.EventDate = eventDate;
            PaternityLeave paternityLeave = new PaternityLeave(c1Card, _c1CardBo);
            paternityLeave.CalculateLeavePermittedPeriod(eventDate, empNo);
            C1CardBase cardBase = paternityLeave;
            CardCheckResult result = cardBase.CheckOverPermittedLeaveHours();
            Assert.Equal(expectedCode, result.Code);
        }

        [Theory]
        [InlineData(Gender.Female, true)]
        [InlineData(Gender.Male, true)]
        [InlineData((Gender)2, true)]
        public void IsAllowForThisGenderTest(Gender gender, bool expected)
        {
            bool result = PaternityLeave.IsAllowForThisGender(gender);
            Assert.Equal(expected, result);
        }


        [Fact]
        public void CanTakeThisLeaveTest()
        {
            C1Card c1Card = new C1Card();
            PaternityLeave paternityLeave = new PaternityLeave(c1Card, _c1CardBo);
            CardCheckResult result = paternityLeave.CanTakeThisLeave();
            Assert.Equal(CardStatusEnum.Success, result.Status);
        }

        [Fact]
        public void CheckRemainHoursTest()
        {
            C1Card c1Card = new C1Card();
            c1Card.EventDate = DateTime.Now;
            c1Card.RelatedFormNumber = "12345";
            PaternityLeave paternityLeave = new PaternityLeave(c1Card, _c1CardBo);
            CardCheckResult result = paternityLeave.CheckRemainHours();
            Assert.Equal(CardStatusEnum.Success, result.Status);
        }

    }
}
