chcp 65001

dotnet sonarscanner begin /k:"Attendance" /d:sonar.host.url="http://jenkins.sinotech.org.tw:9000"  /d:sonar.login="sqp_410d0272ca0613a243d424ded485ae49f4ab8b5e"  /d:sonar.cs.dotcover.reportsPaths=dotCover.Output.html  /d:sonar.cs.opencover.reportsPaths=coverage.xml  /d:sonar.dotnet.excludeTestProjects=true 

dotnet build --no-incremental  /p:Configuration=Debug /p:Platform="Any CPU"

dotnet test --collect "Code Coverage" /p:Configuration=Debug /p:Platform="Any CPU"  /p:ExcludeByAttribute="ExcludeFromCodeCoverage"
coverlet .\Sinotech.Mis.HR.Attendance.Tests\bin\Debug\net8.0\Sinotech.Mis.HR.Attendance.Tests.dll --target "dotnet" --targetargs "test --no-build"  --exclude-by-attribute="ExcludeFromCodeCoverage" -f=opencover -o="coverage.xml" 
rem dotnet dotcover test --dcReportType=HTML /p:Configuration=Debug /p:Platform="Any CPU" 
dotnet sonarscanner end  /d:sonar.login="sqp_410d0272ca0613a243d424ded485ae49f4ab8b5e"

