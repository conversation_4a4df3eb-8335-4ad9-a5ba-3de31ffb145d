﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 加班卡
    /// </summary>
    public class B1Card : CardBase
    {

        /// <summary>
        /// Only for 比對，並非真正 EasyClone
        /// </summary>
        /// <returns></returns>
        public B1Card EasyClone()
        {
            B1Card card = new B1Card();
            card.AddSigners = AddSigners;
            card.CreatedTime = CreatedTime;
            card.FilledTime = FilledTime;
            card.FilledTime = FilledTime;
            card.ApplicationType = ApplicationType;
            card.EmpNo = EmpNo;
            card.Reason = Reason;
            card.Details = Details;
            return card;
        }

        /// <summary>
        /// 簡單比對，不嚴謹
        /// </summary>
        /// <param name="other"></param>
        /// <returns></returns>
        public bool EasyEquals(B1Card? other)
        {
            return other is not null &&
                   AddSigners == other.AddSigners &&
                   CreatedTime == other.CreatedTime &&
                   FilledTime == other.FilledTime &&
                   ApplicationType == other.ApplicationType &&
                   EmpNo == other.EmpNo &&
                   Reason == other.Reason &&
                   Details.Count == other.Details.Count;
        }

        /// <summary>
        /// 設定申請別
        /// </summary>
        /// <returns></returns>
        public override void SetApplicationType()
        {
            List<(int, string)> list = new List<(int, string)>();
            foreach (B1CardDetail detail in Details)
            {
                switch (detail.B1_CODE)
                {
                    case 1:
                        detail.ApplicationType = "加班";
                        break;
                    case 2:
                        detail.ApplicationType = "社外加班";
                        break;
                    case 3:
                        detail.ApplicationType = "補休假";
                        break;
                    default:
                        detail.ApplicationType = string.Empty;
                        break;
                }
                var variable = (detail.B1_CODE, detail.ApplicationType);
                if (!string.IsNullOrWhiteSpace(detail.ApplicationType) && !list.Contains(variable))
                {
                    list.Add(variable);
                }
            }
            list.Sort();
            var arr = list.ToArray();
            StringBuilder bld = new StringBuilder();
            for (int i = 0; i < arr.Length; i++)
            {
                if (i > 0)
                {
                    bld.Append("、");
                }
                bld.Append(arr[i].Item2);
            }
            ApplicationType = bld.ToString();
        }

        /// <summary>
        /// 核卡日期時間
        /// </summary>
        public DateTime? B1_ADate { get; set; }

        /// <summary>
        /// 填卡日期時間
        /// </summary>
        public DateTime B1_WDate { get; set; }

        /// <summary>
        /// 加班日期
        /// </summary>
        public DateTime OvertimeDate { get; set; }

        /// <summary>
        /// 加班日期類型編號
        /// </summary>
        public int DateTypeId { get; set; } = 1;

        /// <summary>
        /// 加班卡填報資料
        /// </summary>
        public List<B1CardDetail> Details { get; set; } = new List<B1CardDetail>();

        /// <summary>
        /// 員工編號
        /// </summary>
        public string EmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 此次加班需累計加班總時數
        /// </summary>
        public int InOvertimeHours { get; set; }

        /// <summary>
        /// 卡片類別名稱
        /// </summary>
        public override string Name { get; } = "B1Card";

        /// <summary>
        /// 加班事由
        /// </summary>
        public string Reason { get; set; } = string.Empty;

        /// <summary>
        /// 加班卡卡號(表單單號)
        /// </summary>
        public string SheetNo { get; set; } = string.Empty;

        /// <summary>
        /// 資料來源：
        /// FlowMaster：FlowMaster系統
        /// Attendance：本系統
        /// SECINC：系統承辦人
        /// </summary>
        public string Source { get; set; } = "Attendance";

        /// <summary>
        /// 簽核狀態 1:未簽核 2:核可 3:不核可 4:抽單
        /// </summary>
        public int Status { get; set; } = 1;

        /// <summary>
        /// 此次加班總時數
        /// </summary>
        public int TotalHours { get; set; }

    }
}