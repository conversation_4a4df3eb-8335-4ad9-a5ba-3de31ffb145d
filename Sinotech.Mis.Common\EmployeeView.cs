﻿using System;
using System.Collections.Generic;
using System.Data;

namespace Sinotech.Mis.Common
{
    /// <summary>
    /// 員工基本資料Simple
    /// </summary>
    public class EmployeeView
    {
        /// <summary>
        /// 員工編號
        /// </summary>
        public string EmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 中文姓名
        /// </summary>
        public string CName { get; set; } = string.Empty;

        /// <summary>
        /// 部門編號
        /// </summary>
        public int DeptNo { get; set; }

        /// <summary>
        /// 部門簡稱
        /// </summary>
        public string DeptSName { get; set; } = string.Empty;


        /// <summary>
        /// 組別編號
        /// </summary>
        public int? TeamID { get; set; }

        /// <summary>
        /// 組別名稱
        /// </summary>
        public string? TeamCName { get; set; }

        /// <summary>
        /// 是否為組長
        /// </summary>
        public bool IsTeamLeader { get; set; }

        /// <summary>
        /// 是否為副理以上
        /// </summary>
        public bool IsAboveDeputyManager { get; set; } = false;

        /// <summary>
        /// 是否為部門主管
        /// </summary>
        public bool IsManager { get; set; } = false;


        /// <summary>
        /// 將 List Employee 轉換為 List EmployeeView 物件
        /// </summary>
        /// <param name="list">Employee list</param>
        /// <returns>EmployeeView list</returns>
        public static List<EmployeeView> EmployeeToEmployeeView(List<Employee> list)
        {
            List<EmployeeView> result = new List<EmployeeView>();
            foreach (var item in list)
            {
                result.Add(EmployeeToEmployeeView(item));
            }
            return result;
        }

        /// <summary>
        /// 將 DataRow 轉換為 EmployeeView 物件
        /// </summary>
        /// <param name="row"></param>
        /// <returns></returns>
        public static EmployeeView FromDataRow(DataRow row)
        {
            EmployeeView employee = new EmployeeView();
            if (row.Table.Columns.Contains("EmpNo") && row["EmpNo"] != DBNull.Value)
            {
                employee.EmpNo = (string)row["EmpNo"];
            }
            if (row.Table.Columns.Contains("CName") && row["CName"] != DBNull.Value)
            {
                employee.CName = (string)row["CName"];
            }
            if (row.Table.Columns.Contains("DeptNo") && row["DeptNo"] != DBNull.Value)
            {
                employee.DeptNo = (int)row["DeptNo"];
            }
            if (row.Table.Columns.Contains("DeptSName") && row["DeptSName"] != DBNull.Value)
            {
                employee.DeptSName = (string)row["DeptSName"];
            }

            if (row.Table.Columns.Contains("TeamID") && row["TeamID"] != DBNull.Value)
            {
                employee.TeamID = (int?)row["TeamID"];
            }
            if (row.Table.Columns.Contains("TeamCName") && row["TeamCName"] != DBNull.Value)
            {
                employee.TeamCName = (string?)row["TeamCName"];
            }
            if (row.Table.Columns.Contains("IsTeamLeader") && row["IsTeamLeader"] != DBNull.Value)
            {
                employee.IsTeamLeader = (bool)row["IsTeamLeader"];
            }

            if (row.Table.Columns.Contains("IsAboveDeputyManager") && row["IsAboveDeputyManager"] != DBNull.Value)
            {
                employee.IsAboveDeputyManager = (bool)row["IsAboveDeputyManager"];
            }
            if (row.Table.Columns.Contains("IsManager") && row["IsManager"] != DBNull.Value)
            {
                employee.IsManager = (bool)row["IsManager"];
            }

            return employee;
        }

        /// <summary>
        /// 將 Employee 物件轉換為 EmployeeView 物件
        /// </summary>
        /// <param name="employee">要轉換的員工物件</param>
        /// <returns>轉換後的 EmployeeView 物件</returns>
        public static EmployeeView EmployeeToEmployeeView(Employee employee)
        {
            if (employee == null)
                return null!;

            return new EmployeeView
            {
                EmpNo = employee.EmpNo,
                CName = employee.CName,
                DeptNo = employee.DeptNo,
                DeptSName = employee.DeptSName,
                TeamID = employee.TeamID,
                TeamCName = employee.TeamCName,
                IsTeamLeader = employee.IsTeamLeader,
                IsAboveDeputyManager = employee.IsAboveDeputyManager,
                IsManager = employee.IsManager
            };
        }
    }
}
