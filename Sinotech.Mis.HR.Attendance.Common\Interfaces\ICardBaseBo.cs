﻿using System;
using System.Collections.Generic;
using System.Data;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// CardBase 商業物件的介面
    /// </summary>
    public interface ICardBaseBo
    {

        /// <summary>依照表單資料補足三卡資料 A1Card/B1Card/B1CardApp/C1Card</summary>
        /// <param name="form"></param>
        /// <param name="card">The card.</param>
        /// <returns></returns>
        public bool AmendCard(Form form, CardBase card);

        /// <summary>結案</summary>
        /// <param name="card">The card.</param>
        /// <param name="formStatus">The form status.</param>
        /// <param name="updateDto">更新資料物件</param>
        public void Finish(CardBase card, FormStatus formStatus, Updater updateDto);

        /// <summary>
        /// 取得卡
        /// </summary>
        /// <param name="formUID">Form UUID</param>
        /// <returns>卡</returns>
        public CardBase? GetCard(Guid formUID);

        /// <summary>取得表單及卡</summary>
        /// <param name="form">The form</param>
        /// <param name="userId">The user identifier.</param>
        /// <returns>表單及卡 FormCardsDto 物件</returns>
        public FormCard GetFormCard(Form form, string userId);

        /// <summary>取得某段時間內卡，不含流程與附件等</summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <returns>卡 Card 物件 List</returns>
        public List<CardBase> GetCards(DateTime startDate, DateTime endDate);

        /// <summary>取得某段時間內表單及卡</summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <returns>表單及卡 FormCardsDto 物件 List</returns>
        public List<FormCard> GetFormCards(DateTime startDate, DateTime endDate);

        /// <summary>取得某段時間內表單及卡</summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單及卡 FormCardsDto 物件 List</returns>
        public List<FormCard> GetFormCards(DateTime startDate, DateTime endDate, string projNo);

        /// <summary>
        /// 取得總時數
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        public int GetHours(CardBase card);

        /// <summary>
        /// 取得提醒資訊 (正常工作卡無)
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        public (int, string?) GetListRemindMessage(CardBase card);

        /// <summary>
        /// 取得特定月份卡，依表單日期為準，非填表時間
        /// </summary>
        /// <param name="empNo">填表員工編號</param>
        /// <param name="date">日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns></returns>
        public List<CardBase> GetUserCardsMonth(string empNo, DateTime date, int? status = null);

        /// <summary>
        /// 取得特定月份已填表單及卡，包括他人代填與本人填表，依表單年月為準，非填表時間
        /// </summary>
        /// <param name="empNo">填表員工編號</param>
        /// <param name="year">表單年</param>
        /// <param name="month">表單月</param>
        /// <returns></returns>
        public List<FormCard> GetUserSentFormCardsYearMonth(string empNo, int year, int month);

        /// <summary>轉換為Stored Procedure所需的 User Defined Table</summary>
        /// <param name="card">三卡</param>
        /// <param name="isAdd">是否為新增</param>
        /// <returns></returns>
        public List<DataTable> ToUserDefinedDataTables(CardBase card, bool isAdd = true);

        /// <summary>更新Card</summary>
        /// <param name="card">The card.</param>
        /// <param name="updateDto">更新資料物件</param>
        public void Update(CardBase card, Updater updateDto);

        /// <summary>
        /// 抽單
        /// </summary>
        /// <param name="card"></param>
        /// <param name="withdraw"></param>
        /// <returns></returns>
        public bool Withdraw(CardBase card, Withdraw withdraw);

        /// <summary>
        /// 是否能抽單 (正常情況)
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        public abstract string CanWithdraw(CardBase card);

        /// <summary>
        /// 是否能結案抽單 (管理員抽單)
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        public abstract string CanClosedWithdraw(CardBase card);
    }
}
