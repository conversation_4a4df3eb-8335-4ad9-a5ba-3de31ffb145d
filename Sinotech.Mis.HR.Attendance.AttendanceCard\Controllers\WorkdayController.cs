﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sinotech.Mis.HR.Attendance.BusinessLogic;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Collections.Generic;

namespace Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers
{
    
    [Authorize]
    [Route("/api/[controller]/[action]")]
    [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
    [ApiController]
    public class WorkdayController : ControllerBase
    {

        private readonly ILogger<WorkdayController> _logger;
        private readonly IWorkdayBo _workdayBo;

        /// <summary><see cref="EmployeeController" /> 的建構函式</summary>
        /// <param name="workdayBo"></param>
        /// <param name="logger">Logger物件</param>
        public WorkdayController(IWorkdayBo workdayBo, ILogger<WorkdayController> logger)
        {
            _workdayBo = workdayBo;
            _logger = logger;
        }

        /// <summary>
        /// 查詢指定期間內之日曆天資料
        /// </summary>
        /// <param name="startDate">起始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>JSON(日曆天資料)</returns>
        [Authorize]
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public string GetEmployeeWorkdaysDateRange(DateTime startDate, DateTime endDate, string empNo)
        {
            startDate = startDate.ToLocalTime();
            endDate = endDate.ToLocalTime();
            List<Workday> workdays = _workdayBo.GetEmpWorkdaysDateRange(startDate, endDate, empNo);
            return JsonConvert.SerializeObject(workdays, Formatting.None);
        }

        /// <summary>查詢指定期間內之日曆天資料</summary>
        /// <param name="startDate">起始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns>JSON(日曆天資料)</returns>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public string GetWorkdaysDateRange(DateTime startDate, DateTime endDate, int shiftId = 1)
        {
            startDate = startDate.ToLocalTime();
            endDate = endDate.ToLocalTime();
            List<Workday> workdays = _workdayBo.GetWorkdaysDateRange(startDate, endDate, shiftId);
            return JsonConvert.SerializeObject(workdays, Formatting.None);
        }
    }
}
