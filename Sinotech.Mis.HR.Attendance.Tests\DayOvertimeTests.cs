﻿using System;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.Common.Tests
{
    [ExcludeFromCodeCoverage]
    public class DayOvertimeTests
    {

        [Fact]
        public void ShouldInitializeWithEmptyHourDetails()
        {
            // Arrange & Act
            var dayOvertime = new DayOvertime();

            // Assert
            Assert.Empty(dayOvertime.HourDetails);
        }
        [Fact]
        public void ShouldInitializeWithEmptyOvertimeRecords()
        {
            // Arrange & Act
            var dayOvertime = new DayOvertime();

            // Assert
            Assert.Empty(dayOvertime.OvertimeRecords);
        }

        [Fact]
        public void ShouldSetInOvertimeHours()
        {
            // Arrange
            var expectedInOvertimeHours = 5;
            var dayOvertime = new DayOvertime();

            // Act
            dayOvertime.InOvertimeHours = expectedInOvertimeHours;

            // Assert
            Assert.Equal(expectedInOvertimeHours, dayOvertime.InOvertimeHours);
        }

        [Fact]
        public void ShouldSetOvertimeDate()
        {
            // Arrange
            var expectedDate = new DateTime(2023, 10, 1, 0, 0, 0, DateTimeKind.Local);
            var dayOvertime = new DayOvertime();

            // Act
            dayOvertime.OvertimeDate = expectedDate;

            // Assert
            Assert.Equal(expectedDate, dayOvertime.OvertimeDate);
        }

        [Fact]
        public void ShouldSetPaidHours()
        {
            // Arrange
            var expectedPaidHours = 10.5;
            var dayOvertime = new DayOvertime();

            // Act
            dayOvertime.PaidHours = expectedPaidHours;

            // Assert
            Assert.Equal(expectedPaidHours, dayOvertime.PaidHours);
        }

        [Fact]
        public void ShouldSetTotalHours()
        {
            // Arrange
            var expectedTotalHours = 8;
            var dayOvertime = new DayOvertime();

            // Act
            dayOvertime.TotalHours = expectedTotalHours;

            // Assert
            Assert.Equal(expectedTotalHours, dayOvertime.TotalHours);
        }

        [Fact]
        public void ShouldSetWorkdayType()
        {
            // Arrange
            var expectedWorkdayType = WorkdayType.SaturdayRestday;
            var dayOvertime = new DayOvertime();

            // Act
            dayOvertime.WorkdayType = expectedWorkdayType;

            // Assert
            Assert.Equal(expectedWorkdayType, dayOvertime.WorkdayType);
        }

    }
}

namespace Sinotech.Mis.HR.Attendance.Common.Tests
{
    [ExcludeFromCodeCoverage]
    public class OvertimeRecordTests
    {

        [Fact]
        public void ShouldInitializeWithDefaultValues()
        {
            // Arrange & Act
            var overtimeRecord = new OvertimeRecord()
            {
                OvertimeType = OvertimeType.Overtime,
            };

            // Assert
            Assert.Equal(OvertimeType.Overtime, overtimeRecord.OvertimeType);
            Assert.Equal(1, overtimeRecord.Order);
            Assert.Equal(string.Empty, overtimeRecord.ProjectNumber);
            Assert.Equal(default(DateTime), overtimeRecord.StartTime);
            Assert.Equal(default(DateTime), overtimeRecord.EndTime);
            Assert.Equal(0, overtimeRecord.Hours);
            Assert.Equal(0, overtimeRecord.PaidHours);
            Assert.Equal(0, overtimeRecord.InOvertimeHours);
            Assert.Empty(overtimeRecord.RateDetails);
            Assert.Empty(overtimeRecord.HourDetails);
        }

    }

    public class OvertimeRecordRateTests
    {

        [Fact]
        public void ShouldInitializeWithDefaultValues()
        {
            // Arrange & Act
            var overtimeRecordRate = new OvertimeRecordRate()
            {
                OvertimeType = OvertimeType.Overtime,
                RateType = OvertimeRateType.One
            };

            // Assert
            Assert.Equal(OvertimeType.Overtime, overtimeRecordRate.OvertimeType);
            Assert.Null(overtimeRecordRate.ProjectNumber);
            Assert.Equal(0, overtimeRecordRate.Hours);
            Assert.Equal(0, overtimeRecordRate.InOvertimeLimitHours);
            Assert.False(overtimeRecordRate.InOvertime);
            Assert.Equal(OvertimeRateType.One, overtimeRecordRate.RateType);
            Assert.Equal(0, overtimeRecordRate.PaidHours);
            Assert.Empty(overtimeRecordRate.DetailHours);
        }

    }

    public class OvertimeHourTests
    {

        [Fact]
        public void ShouldInitializeWithDefaultValues()
        {
            // Arrange & Act
            var overtimeHour = new OvertimeHour()
            {
                TypeOrder = 1,
                OvertimeType = OvertimeType.Overtime,
                RateType = OvertimeRateType.One,
                PaidRateType = OvertimeRateType.One
            };

            // Assert
            Assert.Equal(OvertimeType.Overtime, overtimeHour.OvertimeType);
            Assert.Equal(1, overtimeHour.TypeOrder);
            Assert.Equal(string.Empty, overtimeHour.ProjectNumber);
            Assert.Equal(0, overtimeHour.NumberOfHours);
            Assert.False(overtimeHour.InOvertime);
            Assert.Equal(OvertimeRateType.One, overtimeHour.RateType);
            Assert.Equal(OvertimeRateType.One, overtimeHour.PaidRateType);
            Assert.Equal(0, overtimeHour.Rate);
            Assert.Equal(0, overtimeHour.PaidHour);
        }

    }
}