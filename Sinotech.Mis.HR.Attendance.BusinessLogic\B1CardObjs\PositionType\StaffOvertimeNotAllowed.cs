﻿using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Diagnostics.CodeAnalysis;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.PositionType
{
    [ExcludeFromCodeCoverage]
    internal class StaffOvertimeNotAllowed : B1CardPositionBase
    {
        internal override bool IsOvertimeAllowed => false;
        protected override bool HasAppliedOvertimeWork() => throw new InvalidOperationException();
        protected override B1CardResult CheckAppliedHours() => throw new InvalidOperationException();
        protected override B1CardResult CheckDailyOvertimeHours() => throw new InvalidOperationException();
        protected override B1CardResult CheckMonthlyOvertimeHours() => throw new InvalidOperationException();
        protected override B1CardResult CheckQuarterlyOvertimeHours() => throw new InvalidOperationException();
        protected override B1CardResult GetWarningResult() => throw new InvalidOperationException();
        public StaffOvertimeNotAllowed
            (string employeeNumber, DateTime overtimeDate, IB1CardDataProvider provider) :
            base(employeeNumber, overtimeDate, provider)
        {
        }
    }
}