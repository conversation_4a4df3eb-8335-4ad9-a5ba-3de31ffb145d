﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>disable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RootNamespace>Sinotech.Mis.HR.Attendance.BusinessLogic</RootNamespace>
    <GenerateDocumentationFile>False</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Dangl.TextConverter" Version="3.0.4" />
    <PackageReference Include="HtmlSanitizer" Version="8.1.870" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Serilog" Version="4.1.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.3" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="8.0.4" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.PeriodicBatching" Version="5.0.0" />
    <PackageReference Include="System.Runtime.Caching" Version="8.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Sinotech.Mis.Common\Sinotech.Mis.Common.csproj" />
    <ProjectReference Include="..\Sinotech.Mis.Extensions.Configuration\Sinotech.Mis.Extensions.Configuration.csproj" />
    <ProjectReference Include="..\Sinotech.Mis.HR.Attendance.Common\Sinotech.Mis.HR.Attendance.Common.csproj" />
    <ProjectReference Include="..\Sinotech.Mis.HR.Attendance.DataAccess.Interfaces\Sinotech.Mis.HR.Attendance.DataAccess.Interfaces.csproj" />
    <ProjectReference Include="..\Sinotech.Mis.HR.Attendance.DataAccess.Ado\Sinotech.Mis.HR.Attendance.DataAccess.Ado.csproj" />
    <ProjectReference Include="..\Sinotech.Mis.Helpers\Sinotech.Mis.Helpers.csproj" />
    <ProjectReference Include="..\Sinotech.Mis.HR.Attendance.Utilities\Sinotech.Mis.HR.Attendance.Utilities.csproj" />
    <ProjectReference Include="..\Sinotech.Mis.Utilities.DataAccess.Ado\Sinotech.Mis.Utilities.DataAccess.Ado.csproj" />
    <ProjectReference Include="..\Sinotech.Mis.Utilities.DataAccess.Interfaces\Sinotech.Mis.Utilities.DataAccess.Interfaces.csproj" />
  </ItemGroup>

  <ItemGroup>
    <InternalsVisibleTo Include="Sinotech.Mis.HR.Attendance.Tests" />
  </ItemGroup>
  <ItemGroup>
    <InternalsVisibleTo Include="Sinotech.Mis.HR.Attendance.UnitTests" /> 
  </ItemGroup>

</Project>
