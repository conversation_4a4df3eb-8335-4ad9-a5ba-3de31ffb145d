﻿using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Diagnostics.CodeAnalysis;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves
{
    [ExcludeFromCodeCoverage]
    public class C1CardLeaveNotProvided : C1CardBase
    {
        /// <summary>
        /// 建構子
        /// </summary>
        /// <param name="c1Card"></param>
        /// <param name="c1CardBo"></param>
        public C1CardLeaveNotProvided(C1Card c1Card, IC1CardBo c1CardBo) : base(c1Card, c1CardBo)
        {
        }

        /// <summary>
        /// 檢查是否能請此假
        /// </summary>
        /// <returns></returns>
        internal override CardCheckResult CanTakeThisLeave()
        {
            throw new InvalidOperationException();
        }

        /// <summary>
        /// 檢查是否超假
        /// </summary>
        /// <returns></returns>
        internal override CardCheckResult CheckOverPermittedLeaveHours()
        {
            throw new InvalidOperationException();
        }

        /// <summary>
        /// 檢查必要欄位
        /// </summary>
        /// <returns></returns>
        internal override CardCheckResult CheckRequiredFields()
        {
            throw new InvalidOperationException();
        }

        /// <summary>
        /// 計算最早可請假日期與最晚可請假日期
        /// </summary>
        /// <param name="date">請假日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public override (DateTime, DateTime) CalculateLeavePermittedPeriod(DateTime date, string empNo) => DefaultCalculateLeavePermittedPeriod(date, empNo);

        /// <summary>
        /// 此性別是否可請此假別
        /// </summary>
        /// <param name="gender"></param>
        /// <returns></returns>
        public static bool IsAllowForThisGender(Gender gender)
        {
            return true;
        }
    }
}
