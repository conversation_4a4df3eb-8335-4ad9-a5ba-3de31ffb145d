using FakeItEasy;
using Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.PositionType;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.Tests
{
    [ExcludeFromCodeCoverage]
    public class TestCheckOvertimeSegment : TestB1CardBase
    {
        [Theory]
        [InlineData(WorkdayType.WeekWorkday, 525)]
        [InlineData(WorkdayType.SaturdayRestday, 0)]
        [InlineData(WorkdayType.SaturdayHoliday, 0)]
        [InlineData(WorkdayType.MakeUpHoliday, 0)]
        [InlineData(WorkdayType.MakeUpWorkday, 525)]
        [InlineData(WorkdayType.FlexbleHoliday, 0)]
        public void CheckOvertimeSegment(WorkdayType type, int code)
        {
            var provider = GetFakeObject(B1CardPositionEnum.GeneralStaff);
            var p = B1CardPositionFactory.GetB1CardPositionObject(provider);

            var dateInfo = new Workday()
            {
                DayType = type,
                ArrivalTime = _now + TimeSpan.FromHours(8),
                FlexibleDepartureBefore = _now + TimeSpan.FromHours(16.5),
            };

            A.CallTo(() => provider.GetOverTimeDateInfo())
                .Returns(dateInfo);

            _b1Card.Details.Clear();
            _b1Card.Details.Add(
                new B1CardDetail()
                {
                    B1_CODE = 1,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now + TimeSpan.FromHours(7.5),
                    EndTime = _now + TimeSpan.FromHours(8.5),
                    Hour = 1,
                    Project = _project1,
                }
            );

            _b1Card.TotalHours = 1;

            var result = p.CheckOvertimeSegment();
            Assert.Equal(code, result.Code);
        }

        [Theory]
        [InlineData(WorkdayType.SundayRegularHoliday)]
        [InlineData(WorkdayType.SundayHoliday)]
        [InlineData(WorkdayType.WeekNaturalDisasterDay)]
        [InlineData(WorkdayType.SaturdayNaturalDisasterDay)]
        [InlineData(WorkdayType.WeekRestday)]

        public void CheckOvertimeSegment2(WorkdayType type)
        {
            var provider = GetFakeObject(B1CardPositionEnum.GeneralStaff);
            var p = B1CardPositionFactory.GetB1CardPositionObject(provider);

            var dateInfo = new Workday()
            {
                DayType = type,
                ArrivalTime = _now + TimeSpan.FromHours(8),
                FlexibleDepartureBefore = _now + TimeSpan.FromHours(16.5),
            };

            A.CallTo(() => provider.GetOverTimeDateInfo())
                .Returns(dateInfo);

            _b1Card.Details.Clear();
            _b1Card.Details.Add(
                new B1CardDetail()
                {
                    B1_CODE = 1,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now + TimeSpan.FromHours(7.5),
                    EndTime = _now + TimeSpan.FromHours(8.5),
                    Hour = 1,
                    Project = _project1,
                }
            );

            _b1Card.TotalHours = 1;

            Assert.Throws<InvalidOperationException>(() => p.CheckOvertimeSegment());
        }
    }
}