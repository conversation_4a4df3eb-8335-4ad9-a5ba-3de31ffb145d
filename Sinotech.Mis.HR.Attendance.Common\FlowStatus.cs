﻿namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 流程關卡狀態 0:未傳送,1:簽核中, 2:同意, 3:不同意, 4:已抽單, 5: 已通知
    /// </summary>
    public enum FlowStatus
    {
        /// <summary>
        /// 未傳送
        /// </summary>
        NotSend = 0,
        /// <summary>
        /// 簽核中
        /// </summary>
        Processing = 1,
        /// <summary>
        /// 同意
        /// </summary>
        Agree = 2,
        /// <summary>
        /// 不同意
        /// </summary>
        Deny = 3,
        /// <summary>
        /// 已抽單
        /// </summary>
        Withdraw = 4,
        /// <summary>
        /// 已通知
        /// </summary>
        Notified = 5
    }
}
