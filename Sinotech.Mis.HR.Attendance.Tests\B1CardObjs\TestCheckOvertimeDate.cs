using FakeItEasy;
using Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.PositionType;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.Tests
{
    [ExcludeFromCodeCoverage]
    public class TestCheckOvertimeDate : TestB1CardBase
    {
        [Theory]
        [InlineData(true, 501)]
        [InlineData(false, 0)]
        public void B1CardAlreadyFilled(bool filled, int code)
        {
            var provider = GetFakeObject(B1CardPositionEnum.GeneralStaff);
            A<PERSON>To(() => provider.GetHasB1CardFilled())
                .Returns(filled);

            var p = B1CardPositionFactory.GetB1CardPositionObject(provider);

            _b1Card.Details.Clear();
            _b1Card.Details.Add(
                new B1CardDetail()
                {
                    B1_CODE = 1,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now,
                    EndTime = _now + TimeSpan.FromHours(1),
                    Hour = 1,
                    Project = _project1,
                }
            );
            _b1Card.TotalHours = 1;

            var result = p.CheckOvertimeDate();
            Assert.Equal(code, result.Code);
        }

        [Theory]
        [InlineData(WorkdayType.SundayRegularHoliday, 502)]
        [InlineData(WorkdayType.SundayHoliday, 502)]
        [InlineData(WorkdayType.WeekNaturalDisasterDay, 503)]
        [InlineData(WorkdayType.SaturdayNaturalDisasterDay, 503)]
        [InlineData(WorkdayType.WeekWorkday, 0)]
        [InlineData(WorkdayType.SaturdayRestday, 0)]
        [InlineData(WorkdayType.WeekHoliday, 0)]
        [InlineData(WorkdayType.SaturdayHoliday, 0)]
        [InlineData(WorkdayType.MakeUpHoliday, 0)]
        [InlineData(WorkdayType.MakeUpWorkday, 0)]
        [InlineData(WorkdayType.FlexbleHoliday, 0)]
        [InlineData(WorkdayType.WeekRestday, 0)]
        public void IllegalOvertimeDay(WorkdayType type, int code)
        {
            var provider = GetFakeObject(B1CardPositionEnum.GeneralStaff);
            var p = B1CardPositionFactory.GetB1CardPositionObject(provider);

            _b1Card.Details.Clear();
            _b1Card.Details.Add(
                new B1CardDetail()
                {
                    B1_CODE = 1,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now,
                    EndTime = _now + TimeSpan.FromHours(1),
                    Hour = 1,
                    Project = _project1,
                }
            );
            _b1Card.TotalHours = 1;

            _workday.DayType = type;

            var result = p.CheckOvertimeDate();
            Assert.Equal(code, result.Code);
        }
    }
}