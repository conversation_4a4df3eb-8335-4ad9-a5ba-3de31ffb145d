<template>
  <VueSelect
    ref="selectRef"
    :class="customClass"
    :options="selectOptions"
    :filter="filter"
    :clearable="clearable"
    :modelValue="modelValue"
    @option:selected="onChange"
    @search="onSearch"
  >
    <template #no-options>
      <span class="text-danger">
        {{ noOptions }}
      </span>
    </template>
    <template #option="option">
      {{ option.userId + ' ' + option.userName }}
    </template>
    <template #selected-option="option">
      {{ (option?.userId && option?.userName) ? (option.userId + ' ' + option.userName) : '' }}
    </template>
  </VueSelect>
</template>
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import VueSelect from 'vue-select'
import type { VueSelectInstance } from 'vue-select'
import type { PropType } from 'vue'
import type { EmployeeStoreBaseType } from '../api/appType'

const props = defineProps({
  customClass: {
    type: String,
    default: ''
  },
  clearable: {
    type: Boolean,
    default: true
  },
  mode: {
    type: String,
    default: ''
  },
  modelValue: {
    type: Object as PropType<EmployeeStoreBaseType>,
    default: () => {
      return {
        userId: '',
        userName: '',
        deptNo: 0,
        deptSName: ''
      }
    }
  },
  employeeData: {
    type: Array<EmployeeStoreBaseType>,
    default: []
  },
  filter: {
    type: Function,
    default: () => {}
  }
})
const emits = defineEmits(['delete', 'update:modelValue'])

const selectRef = ref<VueSelectInstance | undefined>()
const noOptions = ref<string>('')
const selectOptions = computed<Array<EmployeeStoreBaseType>>(() =>
  props.employeeData.map((e: EmployeeStoreBaseType) => {
    return {
      userId: e.userId,
      userName: e.userName,
      deptNo: e.deptNo,
      deptSName: e.deptSName
    }
  }
))

const onChange = (event: EmployeeStoreBaseType): void => {
  emits('update:modelValue', {
    userId: event.userId,
    userName: event.userName,
    deptNo: event.deptNo,
    deptSName: event.deptSName
  })
}

/**
 * 只會變更noOptions的值，不影響搜尋的結果
 * @param search
 */
const onSearch = (): void => {
  if (props.mode === 'apply') {
    noOptions.value = '不允許填報'
  } else {
    noOptions.value = '查無資料'
  }
}

onMounted(() => {
  selectRef.value!.$refs.clearButton.title = '刪除'
  selectRef.value!.$refs.clearButton.addEventListener('click', () => emits('delete'))
})
</script>