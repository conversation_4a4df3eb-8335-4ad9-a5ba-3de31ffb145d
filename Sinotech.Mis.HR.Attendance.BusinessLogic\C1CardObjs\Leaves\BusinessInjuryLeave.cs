﻿using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Diagnostics.CodeAnalysis;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves;

/// <summary>
/// 公傷假類別
/// </summary>
[ExcludeFromCodeCoverage]
[LeaveKind(LeaveKindEnum.BusinessInjuryLeave)]
public class BusinessInjuryLeave : LeaveWithEvent
{

    /// <summary>
    /// 建構子
    /// </summary>
    /// <param name="c1Card"></param>
    /// <param name="c1CardBo"></param>
    public BusinessInjuryLeave(C1Card c1Card, IC1CardBo c1CardBo) : base(c1Card, c1CardBo)
    {
    }

    /// <summary>
    /// 檢查是否能請此假
    /// </summary>
    /// <returns></returns>
    internal override CardCheckResult CanTakeThisLeave()
    {
        // 公傷假均可請假
        return ResultOk;
    }

    /// <summary>
    /// 檢查請假期限
    /// </summary>
    /// <returns>
    /// </returns>
    internal override CardCheckResult CheckLeaveRange()
    {
        // 公傷假不用檢查請假期限
        return ResultOk;
    }

    /// <summary>
    /// 檢查是否超假
    /// </summary>
    /// <returns></returns>
    internal override CardCheckResult CheckOverPermittedLeaveHours()
    {
        // 公傷假不用檢查超假
        return ResultOk;
    }

    /// <summary>
    /// 檢查剩餘可休
    /// </summary>
    /// <returns></returns>
    internal override CardCheckResult CheckRemainHours()
    {
        // 公傷假不用檢查剩餘可休
        return ResultOk;
    }
    
    /// <summary>
    /// 計算最早可請假日期與最晚可請假日期
    /// </summary>
    /// <param name="date">請假日期</param>
    /// <param name="empNo">員工編號</param>
    /// <returns></returns>
    public override (DateTime, DateTime) CalculateLeavePermittedPeriod(DateTime date, string empNo) => DefaultCalculateLeavePermittedPeriod(date, empNo);

    /// <summary>
    /// 此性別是否可請此假別
    /// </summary>
    /// <param name="gender"></param>
    /// <returns></returns>
    public static bool IsAllowForThisGender(Gender gender)
    {
        return true;
    }

}

