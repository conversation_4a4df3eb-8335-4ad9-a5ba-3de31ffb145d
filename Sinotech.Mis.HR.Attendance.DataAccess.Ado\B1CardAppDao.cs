﻿using Sinotech.Mis.Helpers;
using Sinotech.Mis.HR.Attendance.DataAccess.Interfaces;
using Sinotech.Mis.HR.Attendance.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;

namespace Sinotech.Mis.HR.Attendance.DataAccess.Ado
{
    public class B1CardAppDao : IB1CardAppDao
    {

        /// <summary>
        /// The connection string
        /// </summary>
        private readonly string _connectionString;

        /// <summary>Initializes a new instance of the <see cref="AttendanceDao" /> class.</summary>
        /// <param name="connectionString">The connection string.</param>
        public B1CardAppDao(string connectionString)
        {
            _connectionString = connectionString;
        }

        /// <summary>取得加班申請單</summary>
        /// <param name="date">日期</param>
        /// <param name="userId"></param>
        /// <returns>
        ///   <br />
        /// </returns>
        public DataTable GetB1CardApp(DateTime date, string userId)
        {
            string sql = "SELECT * FROM B1CARDAPP WHERE B1_Date=@Date AND B1_EmpNo=@EmpNO;";
            SqlParameter[] parameters = new SqlParameter[2];
            parameters[0] = new SqlParameter("@Date", SqlDbType.SmallDateTime);
            parameters[0].Value = date;
            parameters[1] = new SqlParameter("@EmpNO", SqlDbType.NVarChar, 4);
            parameters[1].Value = userId;
            return SqlHelper.GetDataTable(_connectionString, sql, parameters);
        }

        /// <summary>
        /// 取得 表單關係人 某月份加班申請卡
        /// </summary>
        /// <param name="empNo">表單關係人 員工編號</param>
        /// <param name="date">日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns>已填表單</returns>
        public DataTable GetB1CardAppMonth(string empNo, DateTime date, int? status = null)
        {
            List<SqlParameter> parameters = new List<SqlParameter>();
            string strSql = @"SELECT * FROM B1CARDAPP WHERE B1_EmpNo=@EmpNo AND B1_Date>=@StartDate AND B1_Date <=@EndDate ";

            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar, 5);
            parameterEmpNo.Value = empNo;
            parameters.Add(parameterEmpNo);

            DateTime startDate = new DateTime(date.Year, date.Month, 1, 0, 0, 0, DateTimeKind.Local);
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);

            DateTime endDate = startDate.AddMonths(1).AddMilliseconds(-1);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);

            if (status != null)
            {
                strSql += @" AND B1_Status=@Status";
                SqlParameter parameterStatus = new SqlParameter("@Status", SqlDbType.Int);
                parameterStatus.Value = status;
                parameters.Add(parameterStatus);
            }
            strSql += " ORDER BY B1_Date;";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql, parameters);
            return dt;
        }

        /// <summary>
        /// 取得B1CardApp
        /// </summary>
        /// <param name="formUID"></param>
        /// <returns></returns>
        public DataTable GetB1CardApps(Guid formUID)
        {
            string sql = @"SELECT * FROM B1CardApp WHERE FormUID=@FormUID";
            SqlParameter parameter = new SqlParameter("@FormUID", SqlDbType.UniqueIdentifier);
            parameter.Value = formUID;
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameter);
            return dt;
        }

        /// <summary>
        /// 依日期區間取得所有加班申請卡
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public DataTable GetB1CardApps(DateTime startDate, DateTime endDate)
        {
            string sql = @"SELECT * FROM B1CardApp WHERE B1_WDate >= @StartDate AND B1_WDate <= @EndDate ORDER BY ID;";
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            return dt;
        }

        /// <summary>
        /// 依日期區間取得某計畫所有加班申請卡
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="projNo"></param>
        /// <returns></returns>
        public DataTable GetB1CardApps(DateTime startDate, DateTime endDate, string projNo)
        {
            string sql = @"SELECT * FROM B1CardApp WHERE B1_WDate >= @StartDate AND B1_WDate <= @EndDate 
            AND B1_PrjNo like @ProjNo ORDER BY ID;";
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);
            SqlParameter parameterProjNo = new SqlParameter("@ProjNo", SqlDbType.VarChar, 10); // 為做 Like查詢要放寬
            parameterProjNo.Value = $"%{projNo}%";
            parameters.Add(parameterProjNo);
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            return dt;
        }

        /// <summary>
        /// 依日期區間取得所有表單及加班申請卡
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public DataTable GetB1CardAppsForms(DateTime startDate, DateTime endDate)
        {
            string sql = @"SELECT B1CARDAPP.ID,FormFlow.Step,Form.CurrentStep,B1CARDAPP.B1_FormID,B1CARDAPP.B1_EmpNo,
 B1CARDAPP.B1_DeptNo,B1CARDAPP.B1_Date,B1CARDAPP.B1_DateTypeId,B1CARDAPP.B1_Hour,
 B1CARDAPP.B1_PaidHour,B1CARDAPP.B1_Code,B1CARDAPP.B1_PrjNo,B1CARDAPP.B1_Reason,
 B1CARDAPP.B1_IsOverdue,B1CARDAPP.B1_OverdueReason,B1CARDAPP.B1_WritedEmpNo,
 B1CARDAPP.B1_WDate,B1CARDAPP.B1_UpdatedEmpNo,B1CARDAPP.B1_UDate,
 B1CARDAPP.B1_ShouldSignEmpNo,B1CARDAPP.B1_ADate,B1CARDAPP.B1_Status,B1CARDAPP.RequisitionID,
 B1CARDAPP.B1_SOURCE,B1CARDAPP.UpdatedEmpNo,B1CARDAPP.UpdatedName,B1CARDAPP.UpdatedTime,
 B1CARDAPP.UpdatedIP,B1CARDAPP.UpdatedHost,B1CARDAPP.ValidFrom,B1CARDAPP.ValidTo,
 FormFlow.ID AS FormFlowID,FormFlow.FlowUID,FormFlow.RecipientEmpNo,FormFlow.RecipientDeptNo,
 FormFlow.RecipientName,FormFlow.RecipientDeptSName,FormFlow.RecipientTeamID,
 FormFlow.RecipientTeamCName,FormFlow.FlowName,FormFlow.ApproverEmpNo,FormFlow.ApproverName,
 FormFlow.ApproverDeptNo,FormFlow.ApproverDeptSName,FormFlow.ApproverTeamID,
 FormFlow.ApproverTeamCName,FormFlow.ApproveTime,FormFlow.ApproveIP,FormFlow.ApproveHost,
 FormFlow.IsAgentApprove,FormFlow.FlowStatus,FormFlow.IsNotification,
 FormFlowStatus.Name AS FlowStatusName,Form.FormID,Form.FormNo,Form.FormSubject,Form.FormInfo,
 Form.EmpNo,Form.EmpName,Form.DeptNo,Form.DeptSName,Form.TeamID,Form.TeamCName,Form.RankNo,
 Form.RankName,Form.JobNo,Form.JobName,Form.ContentStartTime,Form.ContentEndTime,Form.CreatedEmpNo,
 Form.CreatedName,Form.FilledTime,Form.CreatedTime,Form.CreatedIP,Form.CreatedHost,Form.AddedSigner,
 Form.StartTime,Form.EndTime,Form.FormStatus,Form.TotalSteps,Form.FormUID,
 FormStatus.Name AS FormStatusName,FormFlow.ID AS FormIntID,FormAttachment.FileDirectory,
 FormAttachment.OriginalFileName,FormAttachment.EncodedFileName,FormAttachment.ID AS AttachmentID
 FROM B1CARDAPP INNER JOIN Form ON B1CARDAPP.FormUID = Form.FormUID LEFT JOIN
 FormFlow ON Form.FormUID = FormFlow.FormUID INNER JOIN
 FormStatus ON Form.FormStatus = FormStatus.ID LEFT JOIN
 FormFlowStatus ON FormFlow.FlowStatus = FormFlowStatus.ID LEFT JOIN
 FormAttachment ON Form.FormUID = FormAttachment.FormUID
 WHERE (CONVERT(DATE,B1CARDAPP.B1_WDate) >= CONVERT(DATE,@StartDate)) AND (CONVERT(DATE,B1CARDAPP.B1_WDate) <= CONVERT(DATE,@EndDate))
 ORDER BY B1CARDAPP.ID,FormFlow.Step;";
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            return dt;
        }

        /// <summary>
        /// 依日期區間取得某計畫所有表單及加班申請卡
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="projNo"></param>
        /// <returns></returns>
        public DataTable GetB1CardAppsForms(DateTime startDate, DateTime endDate, string projNo)
        {
            string sql = @"SELECT B1CARDAPP.ID,FormFlow.Step,Form.CurrentStep,B1CARDAPP.B1_FormID,B1CARDAPP.B1_EmpNo,
 B1CARDAPP.B1_DeptNo,B1CARDAPP.B1_Date,B1CARDAPP.B1_DateTypeId,B1CARDAPP.B1_Hour,
 B1CARDAPP.B1_PaidHour,B1CARDAPP.B1_Code,B1CARDAPP.B1_PrjNo,B1CARDAPP.B1_Reason,
 B1CARDAPP.B1_IsOverdue,B1CARDAPP.B1_OverdueReason,B1CARDAPP.B1_WritedEmpNo,
 B1CARDAPP.B1_WDate,B1CARDAPP.B1_UpdatedEmpNo,B1CARDAPP.B1_UDate,
 B1CARDAPP.B1_ShouldSignEmpNo,B1CARDAPP.B1_ADate,B1CARDAPP.B1_Status,B1CARDAPP.RequisitionID,
 B1CARDAPP.B1_SOURCE,B1CARDAPP.UpdatedEmpNo,B1CARDAPP.UpdatedName,B1CARDAPP.UpdatedTime,
 B1CARDAPP.UpdatedIP,B1CARDAPP.UpdatedHost,B1CARDAPP.ValidFrom,B1CARDAPP.ValidTo,
 FormFlow.ID AS FormFlowID,FormFlow.FlowUID,FormFlow.RecipientEmpNo,FormFlow.RecipientDeptNo,
 FormFlow.RecipientName,FormFlow.RecipientDeptSName,FormFlow.RecipientTeamID,
 FormFlow.RecipientTeamCName,FormFlow.FlowName,FormFlow.ApproverEmpNo,FormFlow.ApproverName,
 FormFlow.ApproverDeptNo,FormFlow.ApproverDeptSName,FormFlow.ApproverTeamID,
 FormFlow.ApproverTeamCName,FormFlow.ApproveTime,FormFlow.ApproveIP,FormFlow.ApproveHost,
 FormFlow.IsAgentApprove,FormFlow.FlowStatus,FormFlow.IsNotification,
 FormFlowStatus.Name AS FlowStatusName,Form.FormID,Form.FormNo,Form.FormSubject,Form.FormInfo,
 Form.EmpNo,Form.EmpName,Form.DeptNo,Form.DeptSName,Form.TeamID,Form.TeamCName,Form.RankNo,
 Form.RankName,Form.JobNo,Form.JobName,Form.ContentStartTime,Form.ContentEndTime,Form.CreatedEmpNo,
 Form.CreatedName,Form.FilledTime,Form.CreatedTime,Form.CreatedIP,Form.CreatedHost,Form.AddedSigner,
 Form.StartTime,Form.EndTime,Form.FormStatus,Form.TotalSteps,Form.FormUID,
 FormStatus.Name AS FormStatusName,FormFlow.ID AS FormIntID,FormAttachment.FileDirectory,
 FormAttachment.OriginalFileName,FormAttachment.EncodedFileName,FormAttachment.ID AS AttachmentID
 FROM B1CARDAPP INNER JOIN Form ON B1CARDAPP.FormUID = Form.FormUID LEFT JOIN
 FormFlow ON Form.FormUID = FormFlow.FormUID INNER JOIN FormStatus ON Form.FormStatus = FormStatus.ID LEFT JOIN
 FormFlowStatus ON FormFlow.FlowStatus = FormFlowStatus.ID LEFT JOIN
 FormAttachment ON Form.FormUID = FormAttachment.FormUID
 WHERE (CONVERT(DATE,B1CARDAPP.B1_WDate) >= CONVERT(DATE,@StartDate)) AND (CONVERT(DATE,B1CARDAPP.B1_WDate) <= CONVERT(DATE,@EndDate)) 
 AND (B1CARDAPP.B1_PrjNo like @ProjNo )
 ORDER BY B1CARDAPP.ID,FormFlow.Step;";
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);
            SqlParameter parameterProjNo = new SqlParameter("@ProjNo", SqlDbType.VarChar, 10);// 為做 Like查詢要放寬
            parameterProjNo.Value = $"%{projNo}%";
            parameters.Add(parameterProjNo);
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            return dt;
        }

        /// <summary>
        /// 取得加班類型列表 B1Code
        /// </summary>
        /// <returns></returns>
        public DataTable GetB1CardTypes()
        {
            string sql = @"SELECT ID,Name FROM B1CODE ORDER BY ID ASC;";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql);
            return dt;
        }

        /// <summary>
        /// 取得 填表人/表單關係人 某月份已填加班申請單
        /// </summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <param name="status">表單狀態</param>
        /// <returns>已填表單</returns>
        public DataTable GetSentB1CardAppYearMonth(string empNo, int year, int month, int? status = null)
        {
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar, 5);
            parameterEmpNo.Value = empNo;
            parameters.Add(parameterEmpNo);
            string strSql = @"SELECT B1CARDAPP.ID,FormFlow.Step,Form.CurrentStep,B1CARDAPP.B1_FormID,B1CARDAPP.B1_EmpNo,
 B1CARDAPP.B1_DeptNo,B1CARDAPP.B1_Date,B1CARDAPP.B1_DateTypeId,B1CARDAPP.B1_Hour,
 B1CARDAPP.B1_PaidHour,B1CARDAPP.B1_Code,B1CARDAPP.B1_PrjNo,B1CARDAPP.B1_Reason,
 B1CARDAPP.B1_IsOverdue,B1CARDAPP.B1_OverdueReason,B1CARDAPP.B1_WritedEmpNo,
 B1CARDAPP.B1_WDate,B1CARDAPP.B1_UpdatedEmpNo,B1CARDAPP.B1_UDate,
 B1CARDAPP.B1_ShouldSignEmpNo,B1CARDAPP.B1_ADate,B1CARDAPP.B1_Status,B1CARDAPP.RequisitionID,
 B1CARDAPP.B1_SOURCE,B1CARDAPP.UpdatedEmpNo,B1CARDAPP.UpdatedName,B1CARDAPP.UpdatedTime,
 B1CARDAPP.UpdatedIP,B1CARDAPP.UpdatedHost,B1CARDAPP.ValidFrom,B1CARDAPP.ValidTo,
 FormFlow.ID AS FormFlowID,FormFlow.FlowUID,FormFlow.RecipientEmpNo,FormFlow.RecipientDeptNo,
 FormFlow.RecipientName,FormFlow.RecipientDeptSName,FormFlow.RecipientTeamID,
 FormFlow.RecipientTeamCName,FormFlow.FlowName,FormFlow.ApproverEmpNo,FormFlow.ApproverName,
 FormFlow.ApproverDeptNo,FormFlow.ApproverDeptSName,FormFlow.ApproverTeamID,
 FormFlow.ApproverTeamCName,FormFlow.ApproveTime,FormFlow.ApproveIP,FormFlow.ApproveHost,
 FormFlow.IsAgentApprove,FormFlow.FlowStatus,FormFlow.IsNotification,
 FormFlowStatus.Name AS FlowStatusName,Form.FormID,Form.FormNo,Form.FormSubject,Form.FormInfo,
 Form.EmpNo,Form.EmpName,Form.DeptNo,Form.DeptSName,Form.TeamID,Form.TeamCName,Form.RankNo,
 Form.RankName,Form.JobNo,Form.JobName,Form.ContentStartTime,Form.ContentEndTime,Form.CreatedEmpNo,
 Form.CreatedName,Form.FilledTime,Form.CreatedTime,Form.CreatedIP,Form.CreatedHost,Form.AddedSigner,
 Form.StartTime,Form.EndTime,Form.FormStatus,Form.TotalSteps,Form.FormUID,
 FormStatus.Name AS FormStatusName,FormFlow.ID AS FormIntID,FormAttachment.FileDirectory,
 FormAttachment.OriginalFileName,FormAttachment.EncodedFileName,FormAttachment.ID AS AttachmentID
 FROM B1CARDAPP INNER JOIN Form ON B1CARDAPP.FormUID = Form.FormUID 
 INNER JOIN FormStatus ON Form.FormStatus = FormStatus.ID 
 LEFT JOIN FormFlow ON Form.FormUID = FormFlow.FormUID 
 LEFT JOIN FormFlowStatus ON FormFlow.FlowStatus = FormFlowStatus.ID 
 LEFT JOIN FormAttachment ON Form.FormUID = FormAttachment.FormUID
 WHERE ((Form.CreatedEmpNo = @EmpNo) OR (Form.EmpNo = @EmpNo)) AND (Form.FormInfo LIKE @FormInfo)";

            int rocYear = CardUtility.RocChineseYear(year);
            string formInfo = $"{rocYear}_{month.ToString("00")}%";
            SqlParameter parameterFormInfo = new SqlParameter("@FormInfo", SqlDbType.VarChar, 50);
            parameterFormInfo.Value = formInfo;
            parameters.Add(parameterFormInfo);
            if (status != null)
            {
                strSql += @" AND Form.FormStatus=@Status";
                SqlParameter parameterStatus = new SqlParameter("@Status", SqlDbType.Int);
                parameterStatus.Value = status;
                parameters.Add(parameterStatus);
            }
            strSql += " ORDER BY FormInfo,Step;";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql, parameters);
            return dt;
        }


        /// <summary>
        /// 是否已填該日的加班申請單，aka B1_Status in (1,2)
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public DataTable IsFilledB1CardApp(DateTime date, string empNo)
        {
            string sql = "SELECT * FROM B1CARDAPP WHERE B1_Date=@Date AND B1_EmpNo=@EmpNo AND B1_Status in (1, 2);";
            SqlParameter[] parameters = new SqlParameter[2];
            parameters[0] = new SqlParameter("@Date", SqlDbType.SmallDateTime);
            parameters[0].Value = new DateTime(date.Year, date.Month, date.Day);
            parameters[1] = new SqlParameter("@EmpNO", SqlDbType.Char, 4);
            parameters[1].Value = empNo;
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            return dt;
        }

    }
}
