﻿import { createWeb<PERSON><PERSON><PERSON>, createRouter, NavigationFailureType, isNavigationFailure } from 'vue-router'
import { useAuthUserStore } from '../store/index'
import { useUiStore } from '../store/ui'
import { useNavMenuBadgeStore } from '../store/navMenuBadge'
import { useCardStore } from '../store/card'
import { useMessageStore } from '../store/message'
import {
  GET_ISADMIN_URL,
  POST_ISADMIN_URL,
  GET_ISOVERTIMEALLOWED_URL,
  GET_SENTFORMCARD_URL,
  GET_SIGNEDFORMCARD_URL,
  GET_APPROVALFORMCARD_URL,
  GET_NOTIFYFORMCARD_URL,
  GET_ISAUTHORIZEDTOQUERYDEPARTMENTSENTBOX_URL,
  GET_ISAUTH_URL,
  GET_LOGOUT_URL
} from '../api/appUrl'
import { IDLE_TIMEOUT_TIME, SYSTEM_UPDATE_MESSAGE } from '../api/appConst'
import type { CardApiType, FormFlowApiType } from '../api/appType'
import type { RouteLocationNormalized, NavigationFailure, RouteRecordRaw } from 'vue-router'

const mode = import.meta.env.MODE
let intervalId: ReturnType<typeof setInterval> | undefined = undefined

const SystemLogin = () => import('../pages/SystemLogin.vue')
const SystemError = () => import('../pages/SystemError.vue')
const AttendanceHome = () => import('../pages/AttendanceHome.vue')
const A1CardForm = () => import('../pages/A1CardForm.vue')
const B1CardAppForm = () => import('../pages/B1CardAppForm.vue')
const B1CardForm = () => import('../pages/B1CardForm.vue')
const C1CardForm = () => import('../pages/C1CardForm.vue')
const DeptSentRecord = () => import('../pages/DeptSentRecord.vue')
const SignInbox = () => import('../pages/SignInbox.vue')
const A1CardInbox = () => import('../pages/A1CardInbox.vue')
const B1CardAppInbox = () => import('../pages/B1CardAppInbox.vue')
const B1CardInbox = () => import('../pages/B1CardInbox.vue')
const C1CardInbox = () => import('../pages/C1CardInbox.vue')
const NotifyInbox = () => import('../pages/NotifyInbox.vue')
const A1CardNotify = () => import('../pages/A1CardNotify.vue')
const B1CardAppNotify = () => import('../pages/B1CardAppNotify.vue')
const B1CardNotify = () => import('../pages/B1CardNotify.vue')
const C1CardNotify = () => import('../pages/C1CardNotify.vue')
const SentRecord = () => import('../pages/SentRecord.vue')
const A1CardSent = () => import('../pages/A1CardSent.vue')
const B1CardAppSent = () => import('../pages/B1CardAppSent.vue')
const B1CardSent = () => import('../pages/B1CardSent.vue')
const C1CardSent = () => import('../pages/C1CardSent.vue')
const A1CardDeptSent = () => import('../pages/A1CardDeptSent.vue')
const B1CardAppDeptSent = () => import('../pages/B1CardAppDeptSent.vue')
const B1CardDeptSent = () => import('../pages/B1CardDeptSent.vue')
const C1CardDeptSent = () => import('../pages/C1CardDeptSent.vue')
const SignedRecord = () => import('../pages/SignedRecord.vue')
const A1CardSigned = () => import('../pages/A1CardSigned.vue')
const B1CardAppSigned = () => import('../pages/B1CardAppSigned.vue')
const B1CardSigned = () => import('../pages/B1CardSigned.vue')
const C1CardSigned = () => import('../pages/C1CardSigned.vue')
const FormSentMessage = () => import('../pages/FormSentMessage.vue')

const publicURL = import.meta.env.BASE_URL
const routes = [
  {
    path: `${publicURL}`,
    name: 'Login',
    component: SystemLogin,
    beforeEnter: () => {
      if (mode.split('.')[1] === 'intranet') {
        router.push({
          name: 'Home'
        })
      }
    }
  },
  {
    path: `${publicURL}Error`,
    name: 'Error',
    component: SystemError
  },
  {
    path: `${publicURL}Home`,
    name: 'Home',
    component: AttendanceHome
  },
  {
    // path: `${publicURL}Form/A1Card/:year(\\d{4})?/:month(\\d{1,2})?/:tenDays(\\d)?`,
    path: `${publicURL}Form/A1Card`,
    name: 'A1CardForm',
    component: A1CardForm
  },
  {
    path: `${publicURL}Form/B1Card`,
    name: 'B1CardForm',
    component: B1CardForm,
    beforeEnter: async (_to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
      const guard = await workOvertimeFormNavigationGuards() 
      if (guard === false) {
        return { name: 'Home' }
      }
    }
  },
  {
    path: `${publicURL}Form/B1CardApp`,
    name: 'B1CardAppForm',
    component: B1CardAppForm,
    beforeEnter: async (_to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
      const guard = await workOvertimeFormNavigationGuards() 
      if (guard === false) {
        return { name: 'Home' }
      }
    }
  },
  {
    path: `${publicURL}Form/C1Card`,
    name: 'C1CardForm',
    component: C1CardForm
  },
  {
    path: `${publicURL}Notify`,
    name: 'Notify',
    component: NotifyInbox
  },
  {
    path: `${publicURL}Notify/A1Card/:formUID/:id`,
    name: 'A1CardNotify',
    component: A1CardNotify,
    beforeEnter: async (to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
      const guard = await cardNotifyNavigationGuards(to)
      if (guard === false) {
        return { name: 'Home' }
      }
    }
  },
  {
    path: `${publicURL}Notify/B1CardApp/:formUID/:id`,
    name: 'B1CardAppNotify',
    component: B1CardAppNotify,
    beforeEnter: async (to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
      const guard = await cardNotifyNavigationGuards(to)
      if (guard === false) {
        return { name: 'Home' }
      }
    }
  },
  {
    path: `${publicURL}Notify/B1Card/:formUID/:id`,
    name: 'B1CardNotify',
    component: B1CardNotify,
    beforeEnter: async (to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
      const guard = await cardNotifyNavigationGuards(to)
      if (guard === false) {
        return { name: 'Home' }
      }
    }
  },
  {
    path: `${publicURL}Notify/C1Card/:formUID/:id`,
    name: 'C1CardNotify',
    component: C1CardNotify,
    beforeEnter: async (to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
      const guard = await cardNotifyNavigationGuards(to)
      if (guard === false) {
        return { name: 'Home' }
      }
    }
  },
  {
    path: `${publicURL}Sent`,
    name: 'Sent',
    component: SentRecord
  },
  {
    path: `${publicURL}Sent/A1Card/:formUID`,
    name: 'A1CardSent',
    component: A1CardSent,
    beforeEnter: async (to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
      const guard = await cardSentNavigationGuards(to)
      if (guard === false) {
        return { name: 'Home' }
      }
    }
  },
  {
    path: `${publicURL}Sent/B1CardApp/:formUID`,
    name: 'B1CardAppSent',
    component: B1CardAppSent,
    beforeEnter: async (to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
      const guard = await cardSentNavigationGuards(to)
      if (guard === false) {
        return { name: 'Home' }
      }
    }
  },
  {
    path: `${publicURL}Sent/B1Card/:formUID`,
    name: 'B1CardSent',
    component: B1CardSent,
    beforeEnter: async (to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
      const guard = await cardSentNavigationGuards(to)
      if (guard === false) {
        return { name: 'Home' }
      }
    }
  },
  {
    path: `${publicURL}Sent/C1Card/:formUID`,
    name: 'C1CardSent',
    component: C1CardSent,
    beforeEnter: async (to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
      const guard = await cardSentNavigationGuards(to)
      if (guard === false) {
        return { name: 'Home' }
      }
    }
  },
  {
    path: `${publicURL}DeptSent`,
    name: 'DeptSent',
    component: DeptSentRecord,
    beforeEnter: async (_to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
      const userStore = useAuthUserStore()

      let resJson = false
      const res: Response = await fetch(GET_ISAUTHORIZEDTOQUERYDEPARTMENTSENTBOX_URL + '/' + userStore.userId, {
        method: 'POST'
      })
      if (res.ok) {
        resJson = await res.json()
      }
      if (resJson === false) {
        return { name: 'Home' }
      }
    }
  },
  {
    path: `${publicURL}DeptSent/A1Card/:formUID`,
    name: 'A1CardDeptSent',
    component: A1CardDeptSent,
    beforeEnter: async (to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
      const guard = await cardDeptSentNavigationGuards(to)
      if (guard === false) {
        return { name: 'Home' }
      }
    }
  },
  {
    path: `${publicURL}DeptSent/B1CardApp/:formUID`,
    name: 'B1CardAppDeptSent',
    component: B1CardAppDeptSent,
    beforeEnter: async (to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
      const guard = await cardDeptSentNavigationGuards(to)
      if (guard === false) {
        return { name: 'Home' }
      }
    }
  },
  {
    path: `${publicURL}DeptSent/B1Card/:formUID`,
    name: 'B1CardDeptSent',
    component: B1CardDeptSent,
    beforeEnter: async (to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
      const guard = await cardDeptSentNavigationGuards(to)
      if (guard === false) {
        return { name: 'Home' }
      }
    }
  },
  {
    path: `${publicURL}DeptSent/C1Card/:formUID`,
    name: 'C1CardDeptSent',
    component: C1CardDeptSent,
    beforeEnter: async (to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
      const guard = await cardDeptSentNavigationGuards(to)
      if (guard === false) {
        return { name: 'Home' }
      }
    }
  },
  {
    path: `${publicURL}Signed`,
    name: 'Signed',
    component: SignedRecord
  },
  {
    path: `${publicURL}Signed/A1Card/:formUID`,
    name: 'A1CardSigned',
    component: A1CardSigned,
    beforeEnter: async (to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
      const guard = await cardSignedSentNavigationGuards(to)
      if (guard === false) {
        return { name: 'Home' }
      }
    }
  },
  {
    path: `${publicURL}Signed/B1CardApp/:formUID`,
    name: 'B1CardAppSigned',
    component: B1CardAppSigned,
    beforeEnter: async (to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
      const guard = await cardSignedSentNavigationGuards(to)
      if (guard === false) {
        return { name: 'Home' }
      }
    }
  },
  {
    path: `${publicURL}Signed/B1Card/:formUID`,
    name: 'B1CardSigned',
    component: B1CardSigned,
    beforeEnter: async (to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
      const guard = await cardSignedSentNavigationGuards(to)
      if (guard === false) {
        return { name: 'Home' }
      }
    }
  },
  {
    path: `${publicURL}Signed/C1Card/:formUID`,
    name: 'C1CardSigned',
    component: C1CardSigned,
    beforeEnter: async (to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
      const guard = await cardSignedSentNavigationGuards(to)
      if (guard === false) {
        return { name: 'Home' }
      }
    }
  },
  {
    path: `${publicURL}Inbox`,
    name: 'Inbox',
    component: SignInbox
  },
  {
    path: `${publicURL}Inbox/A1Card/:formUID`,
    name: 'A1CardInbox',
    component: A1CardInbox,
    beforeEnter: async (to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
      const guard = await cardInboxNavigationGuards(to)
      if (guard === false) {
        return { name: 'Home' }
      }
    }
  },
  {
    path: `${publicURL}Inbox/B1CardApp/:formUID`,
    name: 'B1CardAppInbox',
    component: B1CardAppInbox,
    beforeEnter: async (to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
      const guard = await cardInboxNavigationGuards(to)
      if (guard === false) {
        return { name: 'Home' }
      }
    }
  },
  {
    path: `${publicURL}Inbox/B1Card/:formUID`,
    name: 'B1CardInbox',
    component: B1CardInbox,
    beforeEnter: async (to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
      const guard = await cardInboxNavigationGuards(to)
      if (guard === false) {
        return { name: 'Home' }
      }
    }
  },
  {
    path: `${publicURL}Inbox/C1Card/:formUID`,
    name: 'C1CardInbox',
    component: C1CardInbox,
    beforeEnter: async (to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
      const guard = await cardInboxNavigationGuards(to)
      if (guard === false) {
        return { name: 'Home' }
      }
    }
  },
  {
    path: `${publicURL}Message`,
    name: 'Message',
    component: FormSentMessage,
    beforeEnter: (_to: RouteLocationNormalized, from: RouteLocationNormalized) => {
      const messageStore = useMessageStore()
      if (from.matched.length === 0 || messageStore.data.length === 0) {
        return { name: 'Home' }
      }
    }
  }
]

// 內網才開放管理相關功能，外網則不開放
if (mode.split('.')[1] === 'intranet') {
  const DataManagement = () => import('../pages/DataManagement.vue')
  const A1CardDataManagementQuery = () => import('../pages/A1CardDataManagementQuery.vue')
  const B1CardAppDataManagementQuery = () => import('../pages/B1CardAppDataManagementQuery.vue')
  const B1CardDataManagementQuery = () => import('../pages/B1CardDataManagementQuery.vue')
  const C1CardDataManagementQuery = () => import('../pages/C1CardDataManagementQuery.vue')
  const A1CardDataManagementEdit = () => import('../pages/A1CardDataManagementEdit.vue')

  routes.push(
    {
      path: `${publicURL}DataManagement`,
      name: 'DataManagement',
      component: DataManagement,
      beforeEnter: async (_to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
        const userStore = useAuthUserStore()
        const PromiseData: any = await Promise.all([
          fetch(GET_ISADMIN_URL, {
            method: 'GET'
          }).then((res: Response) => {
            if (!res.ok) {
              throw new Error(res.status.toString())
            }
            return res.json()
          }).catch((err: Error): void => {
            throw err
          }),
          fetch(POST_ISADMIN_URL + '/' + userStore.userId, {
            method: 'POST'
          }).then((res: Response) => {
            if (!res.ok) {
              throw new Error(res.status.toString())
            }
            return res.json()
          }).catch((err: Error): void => {
            throw err
          })
        ]).catch((err: Error): void => {
          throw err
        })

        if (!(PromiseData[0] === true && PromiseData[1] === true)) {
          return { name: 'Home' }
        }
      }
    },
    {
      path: `${publicURL}DataManagement/Query/A1Card/:formUID`,
      name: 'A1CardDataManagementQuery',
      component: A1CardDataManagementQuery,
      beforeEnter: async (to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
        const userStore = useAuthUserStore()
        const PromiseData: any = await Promise.all([
          fetch(GET_ISADMIN_URL, {
            method: 'GET'
          }).then((res: Response) => {
            if (!res.ok) {
              throw new Error(res.status.toString())
            }
            return res.json()
          }).catch((err: Error): void => {
            throw err
          }),
          fetch(POST_ISADMIN_URL + '/' + userStore.userId, {
            method: 'POST'
          }).then((res: Response) => {
            if (!res.ok) {
              throw new Error(res.status.toString())
            }
            return res.json()
          }).catch((err: Error): void => {
            throw err
          })
        ]).catch((err: Error): void => {
          throw err
        })

        if (!(PromiseData[0] === true && PromiseData[1] === true)) {
          return { name: 'Home' }
        } else {
          const cardStore = useCardStore()
          const params = new URLSearchParams({
            formUID: to.params.formUID as string,
            empNo: userStore.userId
          })

          cardStore.setQueryDataTime()
          let resJson: (CardApiType & FormFlowApiType) | null = null
          const res: Response = await fetch(GET_SENTFORMCARD_URL + '?' + params, {
            method: 'GET'
          })
          if (res.ok) {
            resJson = await res.json()
          }
          if (resJson?.ID === undefined) {
            return { name: 'Home' }
          } else {
            cardStore.setData(resJson)
          }
        }
      }
    },
    {
      path: `${publicURL}DataManagement/Query/B1CardApp/:formUID`,
      name: 'B1CardAppDataManagementQuery',
      component: B1CardAppDataManagementQuery,
      beforeEnter: async (to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
        const userStore = useAuthUserStore()
        const PromiseData: any = await Promise.all([
          fetch(GET_ISADMIN_URL, {
            method: 'GET'
          }).then((res: Response) => {
            if (!res.ok) {
              throw new Error(res.status.toString())
            }
            return res.json()
          }).catch((err: Error): void => {
            throw err
          }),
          fetch(POST_ISADMIN_URL + '/' + userStore.userId, {
            method: 'POST'
          }).then((res: Response) => {
            if (!res.ok) {
              throw new Error(res.status.toString())
            }
            return res.json()
          }).catch((err: Error): void => {
            throw err
          })
        ]).catch((err: Error): void => {
          throw err
        })

        if (!(PromiseData[0] === true && PromiseData[1] === true)) {
          return { name: 'Home' }
        } else {
          const cardStore = useCardStore()
          const params = new URLSearchParams({
            formUID: to.params.formUID as string,
            empNo: userStore.userId
          })

          cardStore.setQueryDataTime()
          let resJson: (CardApiType & FormFlowApiType) | null = null
          const res: Response = await fetch(GET_SENTFORMCARD_URL + '?' + params, {
            method: 'GET'
          })
          if (res.ok) {
            resJson = await res.json()
          }
          if (resJson?.ID === undefined) {
            return { name: 'Home' }
          } else {
            cardStore.setData(resJson)
          }
        }
      }
    },
    {
      path: `${publicURL}DataManagement/Query/B1Card/:formUID`,
      name: 'B1CardDataManagementQuery',
      component: B1CardDataManagementQuery,
      beforeEnter: async (to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
        const userStore = useAuthUserStore()
        const PromiseData: any = await Promise.all([
          fetch(GET_ISADMIN_URL, {
            method: 'GET'
          }).then((res: Response) => {
            if (!res.ok) {
              throw new Error(res.status.toString())
            }
            return res.json()
          }).catch((err: Error): void => {
            throw err
          }),
          fetch(POST_ISADMIN_URL + '/' + userStore.userId, {
            method: 'POST'
          }).then((res: Response) => {
            if (!res.ok) {
              throw new Error(res.status.toString())
            }
            return res.json()
          }).catch((err: Error): void => {
            throw err
          })
        ]).catch((err: Error): void => {
          throw err
        })

        if (!(PromiseData[0] === true && PromiseData[1] === true)) {
          return { name: 'Home' }
        } else {
          const cardStore = useCardStore()
          const params = new URLSearchParams({
            formUID: to.params.formUID as string,
            empNo: userStore.userId
          })

          cardStore.setQueryDataTime()
          let resJson: (CardApiType & FormFlowApiType) | null = null
          const res: Response = await fetch(GET_SENTFORMCARD_URL + '?' + params, {
            method: 'GET'
          })
          if (res.ok) {
            resJson = await res.json()
          }
          if (resJson?.ID === undefined) {
            return { name: 'Home' }
          } else {
            cardStore.setData(resJson)
          }
        }
      }
    },
    {
      path: `${publicURL}DataManagement/Query/C1Card/:formUID`,
      name: 'C1CardDataManagementQuery',
      component: C1CardDataManagementQuery,
      beforeEnter: async (to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
        const userStore = useAuthUserStore()
        const PromiseData: any = await Promise.all([
          fetch(GET_ISADMIN_URL, {
            method: 'GET'
          }).then((res: Response) => {
            if (!res.ok) {
              throw new Error(res.status.toString())
            }
            return res.json()
          }).catch((err: Error): void => {
            throw err
          }),
          fetch(POST_ISADMIN_URL + '/' + userStore.userId, {
            method: 'POST'
          }).then((res: Response) => {
            if (!res.ok) {
              throw new Error(res.status.toString())
            }
            return res.json()
          }).catch((err: Error): void => {
            throw err
          })
        ]).catch((err: Error): void => {
          throw err
        })

        if (!(PromiseData[0] === true && PromiseData[1] === true)) {
          return { name: 'Home' }
        } else {
          const cardStore = useCardStore()
          const params = new URLSearchParams({
            formUID: to.params.formUID as string,
            empNo: userStore.userId
          })

          cardStore.setQueryDataTime()
          let resJson: (CardApiType & FormFlowApiType) | null = null
          const res: Response = await fetch(GET_SENTFORMCARD_URL + '?' + params, {
            method: 'GET'
          })
          if (res.ok) {
            resJson = await res.json()
          }
          if (resJson?.ID === undefined) {
            return { name: 'Home' }
          } else {
            cardStore.setData(resJson)
          }
        }
      }
    },
    {
      path: `${publicURL}DataManagement/Edit/A1Card/:formUID`,
      name: 'A1CardDataManagementEdit',
      component: A1CardDataManagementEdit,
      beforeEnter: async (to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
        const userStore = useAuthUserStore()
        const PromiseData: any = await Promise.all([
          fetch(GET_ISADMIN_URL, {
            method: 'GET'
          }).then((res: Response) => {
            if (!res.ok) {
              throw new Error(res.status.toString())
            }
            return res.json()
          }).catch((err: Error): void => {
            throw err
          }),
          fetch(POST_ISADMIN_URL + '/' + userStore.userId, {
            method: 'POST'
          }).then((res: Response) => {
            if (!res.ok) {
              throw new Error(res.status.toString())
            }
            return res.json()
          }).catch((err: Error): void => {
            throw err
          })
        ]).catch((err: Error): void => {
          throw err
        })

        if (!(PromiseData[0] === true && PromiseData[1] === true)) {
          return { name: 'Home' }
        } else {
          const cardStore = useCardStore()
          const params = new URLSearchParams({
            formUID: to.params.formUID as string,
            empNo: userStore.userId
          })

          cardStore.setQueryDataTime()
          let resJson: (CardApiType & FormFlowApiType) | null = null
          const res: Response = await fetch(GET_SENTFORMCARD_URL + '?' + params, {
            method: 'GET'
          })
          if (res.ok) {
            resJson = await res.json()
          }
          if (resJson?.ID === undefined) {
            return { name: 'Home' }
          } else {
            cardStore.setData(resJson)
          }
        }
      }
    }
  )
}

const router = createRouter({
  history: createWebHistory(),
  routes: routes as unknown as Array<RouteRecordRaw>
})

const setLogoutInterval = (): void => {
  const expiresTime = new Date(new Date().getTime() + IDLE_TIMEOUT_TIME)
  if (intervalId !== null) {
    clearInterval(intervalId)
  }
  intervalId = setInterval(async () => {
    const timer = new Date(expiresTime).getTime() - new Date().getTime()
    if (timer <= 0) {
      clearInterval(intervalId)
      intervalId = undefined

      const loginRoute = await logoutHandler()
      router.push(loginRoute)
    }
  }, 1000)
}

router.beforeEach(async (to: RouteLocationNormalized) => {
  let redirectRoute = null
  const userStore = useAuthUserStore()
  const uiStore = useUiStore()
  const navMenuBadgeStore = useNavMenuBadgeStore()
  uiStore.toggle(true)

  try {
    const isAuthRes = await fetch(GET_ISAUTH_URL, {
      method: 'GET'
    })
    if (!isAuthRes.ok) {
      throw new Error(isAuthRes.status.toString())
    }
    const isAuthJson = await isAuthRes.json()
    if (isAuthJson.isAuthenticated === true) {
      if (to.name !== 'Error') {
        await userStore.setLogonUser(isAuthJson.userId)

        if (mode.split('.')[1] === 'internet') {
          setLogoutInterval()
        }
        navMenuBadgeStore.setBadge(userStore.userId, null)
        if ((to.name === 'Login') || (to.matched.length === 0)) {
          redirectRoute = { name: 'Home' }
        }
      }
    } else {
      userStore.$reset()
      if (to.name !== 'Login' && to.name !== 'Error') {
        redirectRoute = unauthRedirectRoute()
      }
    }
  } catch (err: unknown) {
    console.error(err)
    redirectRoute = await logoutHandler()
  } finally {
    if (redirectRoute !== null) {
      return redirectRoute
    }
  }
})

router.afterEach((to: RouteLocationNormalized, from: RouteLocationNormalized, failure: void | NavigationFailure) => {
  const uiStore = useUiStore()
  uiStore.toggle(false)

  if (from.name === 'Message') {
    router.go(0)
  } else if (isNavigationFailure(failure) && (to.fullPath !== from.fullPath)) {
    document.body.textContent = SYSTEM_UPDATE_MESSAGE
  }
})

/**
 * vite重新建置檔案後，預設會在檔名後加上hash，且index.html也會自動綁定這些新的檔案，如果只有單一檔案就能藉此讓使用者端知道有更新。
 * 但因為一般會建置多個檔案以降低使用者端載入的時間，加上SPA只是模擬路由，並不會主動重新呼叫伺服器端。
 * 如果使用者端有快取的話就可能會呼叫舊的檔案，造成"Failed to fetch dynamically imported module"。
 * @link https://stackoverflow.com/questions/69300341/typeerror-failed-to-fetch-dynamically-imported-module-on-vue-vite-vanilla-set/74057337#74057337
 */
router.onError(() => {
  document.body.textContent = SYSTEM_UPDATE_MESSAGE
})

const workOvertimeFormNavigationGuards = async (): Promise<boolean> => {
  const userStore = useAuthUserStore()
  const empNo = userStore.userId
  const res: Response = await fetch(GET_ISOVERTIMEALLOWED_URL + '/' + empNo, {
    method: 'GET'
  })
  if (res.ok) {
    const resJson = await res.json()
    return resJson
  }
  return false
}

const cardNotifyNavigationGuards = async (to: RouteLocationNormalized): Promise<boolean> => {
  const userStore = useAuthUserStore()
  const cardStore = useCardStore()
  const params = new URLSearchParams({
    formUID: to.params.formUID as string,
    notifyId: to.params.id as string,
    userId: userStore.userId
  })

  cardStore.setQueryDataTime()
  let resJson: (CardApiType & FormFlowApiType) | null = null
  const res: Response = await fetch(GET_NOTIFYFORMCARD_URL + '?' + params, {
    method: 'GET'
  })
  if (res.ok) {
    resJson = await res.json()
  }
  if (resJson?.ID === undefined) {
    return false
  } else {
    cardStore.setData(resJson)
    return true
  }
}

const cardSentNavigationGuards = async (to: RouteLocationNormalized): Promise<boolean> => {
  const userStore = useAuthUserStore()
  const cardStore = useCardStore()
  const params = new URLSearchParams({
    formUID: to.params.formUID as string,
    empNo: userStore.userId
  })

  cardStore.setQueryDataTime()
  let resJson: (CardApiType & FormFlowApiType) | null = null
  const res: Response = await fetch(GET_SENTFORMCARD_URL + '?' + params, {
    method: 'GET'
  })
  if (res.ok) {
    resJson = await res.json()
  }
  if (resJson?.ID === undefined) {
    return false
  } else {
    cardStore.setData(resJson)
    return true
  }
}

const cardDeptSentNavigationGuards = async (to: RouteLocationNormalized): Promise<boolean> => {
  const userStore = useAuthUserStore()

  let resJson = false
  const res: Response = await fetch(GET_ISAUTHORIZEDTOQUERYDEPARTMENTSENTBOX_URL + '/' + userStore.userId, {
    method: 'POST'
  })
  if (res.ok) {
    resJson = await res.json()
  }
  if (resJson === true && to.params.formUID !== undefined) {
    const cardStore = useCardStore()
    const params = new URLSearchParams({
      formUID: to.params.formUID as string,
      empNo: userStore.userId
    })

    cardStore.setQueryDataTime()
    let resJson: (CardApiType & FormFlowApiType) | null = null
    const res: Response = await fetch(GET_SENTFORMCARD_URL + '?' + params, {
      method: 'GET'
    })
    if (res.ok) {
      resJson = await res.json()
    }
    if (resJson?.ID === undefined) {
      return false
    } else {
      cardStore.setData(resJson)
      return true
    }
  } else {
    return false
  }
}

const cardSignedSentNavigationGuards = async (to: RouteLocationNormalized): Promise<boolean> => {
  const userStore = useAuthUserStore()
  const cardStore = useCardStore()
  const params = new URLSearchParams({
    formUID: to.params.formUID as string,
    empNo: userStore.userId
  })

  cardStore.setQueryDataTime()
  let resJson: (CardApiType & FormFlowApiType) | null = null
  const res: Response = await fetch(GET_SIGNEDFORMCARD_URL + '?' + params, {
    method: 'GET'
  })
  if (res.ok) {
    resJson = await res.json()
  }
  if (resJson?.ID === undefined) {
    return false
  } else {
    cardStore.setData(resJson)
    return true
  }
}

const cardInboxNavigationGuards = async (to: RouteLocationNormalized): Promise<boolean> => {
  const userStore = useAuthUserStore()
  const cardStore = useCardStore()
  const params = new URLSearchParams({
    formUID: to.params.formUID as string,
    empNo: userStore.userId
  })

  cardStore.setQueryDataTime()
  let resJson: (CardApiType & FormFlowApiType) | null = null
  const res: Response = await fetch(GET_APPROVALFORMCARD_URL + '?' + params, {
    method: 'GET'
  })
  if (res.ok) {
    resJson = await res.json()
  }
  if (resJson?.ID === undefined) {
    return false
  } else {
    cardStore.setData(resJson)
    return true
  }
}

const pushHandler = (routerName: string): void => {
  router.push({
    name: routerName
  }).then((failure) => {
    if (failure instanceof Error) {
      if (isNavigationFailure(failure, NavigationFailureType.cancelled)) {
        console.error(failure)
      }
    }
  }).catch((err: Error): void => {
    console.error(err)
  })
}

const logoutHandler = async (): Promise<{ name: string }> => {
  let errorCatch = null
  const abortController = new AbortController()
  
  abortController.signal.addEventListener('abort', () => {
    if (abortController.signal.reason.message === '401') {
      const userStore = useAuthUserStore()
      userStore.$reset()
      router.push({ name: 'Login' })
    } else if (abortController.signal.reason.message === '400' || abortController.signal.reason.message === '500') {
      const userStore = useAuthUserStore()
      userStore.$reset()
      router.push({ name: 'Error' })
    }
  })

  if (mode.split('.')[1] === 'internet') {
    try {
      const res: Response = await fetch(GET_LOGOUT_URL, {
        method: 'GET',
        signal: abortController.signal
      })
      if (!res.ok) {
        if (res.status === 401) {
          throw new Error('Unauthorized Attendance')
        } else {
          throw new Error(res.status.toString())
        }
      }
    } catch (err: unknown) {
      console.error(err)
      errorCatch = err
    }
  }

  const userStore = useAuthUserStore()
  userStore.$reset()

  if (errorCatch === null) {
    return unauthRedirectRoute()
  } else if (errorCatch instanceof Error && errorCatch.message === 'Unauthorized Attendance') {
    return unauthRedirectRoute()
  } else {
    return { name: 'Error' }
  }
}

const unauthRedirectRoute = (): { name: string } => {
  // 內網登出的話會導向錯誤頁面
  if (mode.split('.')[1] === 'intranet') {
    return { name: 'Error' }
  } else {
    return { name: 'Login' }
  }
}

const routerExtend = Object.assign(router, { pushHandler })
export { logoutHandler, routerExtend }

export default router
