﻿using System;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 加班申請卡
    /// </summary>
    public class B1CardApp : CardBase
    {
        /// <summary>
        /// Only for 比對，並非真正 EasyClone
        /// </summary>
        /// <returns></returns>
        public B1CardApp EasyClone()
        {
            B1CardApp card = new B1CardApp();
            card.AddSigners = AddSigners;
            card.CreatedTime = CreatedTime;
            card.FilledTime = FilledTime;
            card.FilledTime = FilledTime;
            card.ApplicationType = ApplicationType;
            card.B1_EmpNo = B1_EmpNo;
            card.B1_DeptNo = B1_DeptNo;
            card.B1_Date = B1_Date;
            card.B1_Hour = B1_Hour;
            card.B1_Code = B1_Code;
            return card;
        }

        /// <summary>
        /// 簡單比對，不嚴謹
        /// </summary>
        /// <param name="other"></param>
        /// <returns></returns>
        public bool EasyEquals(B1CardApp? other)
        {
            return other is not null &&
                   AddSigners == other.AddSigners &&
                   CreatedTime == other.CreatedTime &&
                   FilledTime == other.FilledTime &&
                   ApplicationType == other.ApplicationType &&
                   B1_EmpNo == other.B1_EmpNo &&
                   B1_DeptNo == other.B1_DeptNo &&
                   B1_Date == other.B1_Date &&
                   B1_Hour == other.B1_Hour &&
                   B1_Code == other.B1_Code;
        }

        /// <summary>
        /// 設定申請別/旬別名稱
        /// </summary>
        /// <returns></returns>
        public override void SetApplicationType()
        {
            switch (B1_Code)
            {
                case '1':
                    ApplicationType = "加班";
                    break;
                case '2':
                    ApplicationType = "社外加班";
                    break;
                case '3':
                    ApplicationType = "補休假";
                    break;
            }
        }

        /// <summary>
        /// 核卡日期時間
        /// </summary>
        public DateTime? B1_ADate { get; set; }

        /// <summary>
        /// 加班申請別
        /// </summary>
        public char B1_Code { get; set; }

        /// <summary>
        /// 預定加班日期
        /// </summary>
        public DateTime B1_Date { get; set; }

        /// <summary>
        /// 預定加班日期類型
        /// </summary>
        public int B1_DateTypeId { get; set; }

        /// <summary>
        /// 員工所屬部門編號
        /// </summary>
        public int B1_DeptNo { get; set; }

        /// <summary>
        /// 員工編號
        /// </summary>
        public string B1_EmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 加班申請單單號
        /// </summary>
        public string? B1_FormID { get; set; }

        /// <summary>
        /// 預定加班時數
        /// </summary>
        public int B1_Hour { get; set; }

        /// <summary>
        /// 是否為逾期填報
        /// </summary>
        public bool B1_IsOverdue { get; set; }

        /// <summary>
        /// 逾期填報原因
        /// </summary>
        public string B1_OverdueReason { get; set; } = "";

        /// <summary>
        /// 預定認列加班時數 <br />
        /// 「週間國定假日及補假日」，如加班不足8小時，最低預定認列加班時數設定為8小時，其餘同預定加班時數。
        /// </summary>
        public int B1_PaidHour { get; set; }

        /// <summary>
        /// 計畫編號
        /// </summary>
        public string B1_PrjNo { get; set; } = string.Empty;

        /// <summary>
        /// 加班事由
        /// </summary>
        public string B1_Reason { get; set; } = string.Empty;

        /// <summary>
        /// 應簽核主管員工編號(申請卡送出時即先填入, 主管簽核後填入簽核日期及簽核狀態)
        /// </summary>
        public string? B1_ShouldSignEmpNo { get; set; } = null;

        /// <summary>
        /// 寫入資料來源
        /// FlowMaster：FlowMaster系統
        /// Attendance：本系統
        /// SECINC：系統承辦人
        /// </summary>
        public string? B1_SOURCE { get; set; } = "Attendance";

        /// <summary>
        /// 簽核狀態 1:未簽核 2:核可 3:不核可 4:抽單
        /// </summary>
        public int B1_Status { get; set; }
        /// <summary>
        /// 修改日期時間
        /// </summary>
        public DateTime? B1_UDate { get; set; }

        /// <summary>
        /// 修改人員工編號
        /// </summary>
        public string? B1_UpdatedEmpNo { get; set; }
        /// <summary>
        /// 填卡日期時間
        /// </summary>
        public DateTime B1_WDate { get; set; }

        /// <summary>
        /// 填卡人員工編號
        /// </summary>
        public string B1_WritedEmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 表單識別碼
        /// </summary>
        public override Guid FormUID { get; set; } = Guid.Empty;

        /// <summary>
        /// 加班申請卡 ID
        /// </summary>
        public int? ID { get; set; }

        /// <summary>
        /// 卡片類別名稱
        /// </summary>
        public override string Name { get; } = "B1CardApp";

        /// <summary>
        /// FlowMaster申請單編號
        /// </summary>
        public string? RequisitionID { get; set; } = null;
    }
}
