﻿namespace Sinotech.Mis.HR.Attendance.Common
{
    public interface ICardChecker
    {
        /// <summary>檢查新增正常工作卡</summary>
        /// <param name="a1Card">正常工作卡</param>
        /// <returns>CardCheckResult，正常傳回 Code == 0</returns>
        public CardCheckResult CheckA1CardAdd(A1Card a1Card);

        /// <summary>檢查更新正常工作卡</summary>
        /// <param name="a1Card">正常工作卡</param>
        /// <returns>CardCheckResult，正常傳回 Code == 0</returns>
        public CardCheckResult CheckA1CardUpdate(A1Card a1Card);
    }
}
