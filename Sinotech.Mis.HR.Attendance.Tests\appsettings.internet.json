{
  "ConnectionStrings": {
    "Attendance": "pvzv9cBGmEWWb7C4Sk9kZ3xV9HoqIGcYWyeVmmCp92nPeJHgEZbJM/G661A93uSJoBGfjp/mMvAofyxSbKzqor1RfzpasZCCHJAZa7L+bVYwbdJqzUhjS+G6Sids9TSA8y/Lc8c6lytOCTZCj3hVJ3J9mzds9UmZiJCF7Xx7PzejrpIbp5hRpzo6bBv4+XRSHR7Ou6m6ylMJiukxuiC3rDbBeJzkeTm1u+PGx8zOr5YhpdTbYZcQZ5TvL+A5tWArHjj+wH2egTYvIgnDTUV4bn1qHy866+vjrWsPYk4/9qz9CvyYK/LjuiJiws96bQb8zwby0HlTikzDjmX6Ku3JaWfAzo9JwMInwT3nmPur/XAO5YHyMKc874x7JauoXe1Hc5T9W2U7tGBG6mqXdfa43bHKBQHAu393sGrqeRQ2p2j3Sz88xKOQsDMWX6T9RLmsgwrDSpJWHXbg1B1dqetPp6pdyyz1Gk080wkPSwJVe1FiI8stfph+oiuwE/LVTE+Kesx79An7CUDVaASwBkpVptfS8CBRoPBjDHJ++rFxL7WrZh8KYhcKlPy7Mb36QbsxpJztuedNG0NScUHXdiSYR1Uh+fWZLwe2gofYVxZFpxdR7XhDCL8MHt1fR2ueKdEgcadZ3tjLwdbCqey4J0WOoRViBw3R/P4CIKOtGDFjSjk=",
    "MIS": "pvzv9cBGmEWWb7C4Sk9kZ3xV9HoqIGcYWyeVmmCp92nPeJHgEZbJM/G661A93uSJoBGfjp/mMvAofyxSbKzqor1RfzpasZCCHJAZa7L+bVYwbdJqzUhjS+G6Sids9TSA8y/Lc8c6lytOCTZCj3hVJ3J9mzds9UmZiJCF7Xx7PzejrpIbp5hRpzo6bBv4+XRSHR7Ou6m6ylMJiukxuiC3rDbBeJzkeTm1u+PGx8zOr5YhpdTbYZcQZ5TvL+A5tWArHjj+wH2egTYvIgnDTUV4bn1qHy866+vjrWsPYk4/9qz9CvyYK/LjuiJiws96bQb8zwby0HlTikzDjmX6Ku3JaWfAzo9JwMInwT3nmPur/XAO5YHyMKc874x7JauoXe1Hc5T9W2U7tGBG6mqXdfa43bHKBQHAu393sGrqeRQ2p2j3Sz88xKOQsDMWX6T9RLmsgwrDSpJWHXbg1B1dqetPp6pdyyz1Gk080wkPSwJVe1FiI8stfph+oiuwE/LVTE+Kesx79An7CUDVaASwBkpVptfS8CBRoPBjDHJ++rFxL7WrZh8KYhcKlPy7Mb36QbsxpJztuedNG0NScUHXdiSYR1Uh+fWZLwe2gofYVxZFpxdR7XhDCL8MHt1fR2ueKdEgcadZ3tjLwdbCqey4J0WOoRViBw3R/P4CIKOtGDFjSjk=",
    "SinoSign": "e3XBlAYHpV/QlmXYuaNqWeUEacK8Zxl8b49OnQBmAJvt+7mCm1czEF1NV/yevP+3giHObxI9DmLzRWYC+FR+BxeqBuacFk/ZIao0eSRWOIC0QTB/u+E+q0SR6Ny4yOX3EOO3ANq+f8g8XKNKGlPdvijxP9PIIcBA0Iz0TfEIePZOsdnGQzUMjptti0iSDGek8L86uXWvQFcYLUQa8Nz8HAUF9/yBAEJVn1KBnvaSpW0PGCoV22LwQ2PC3guairTvN3nhfra5LhQ0I8cbC8Oxm4Uens9bu8RVw5h7kVz15Stnyh7hxDkNL1Sg9myK6rvAXamDOer26A4ZtPrKYY0bwVXkwu47QUVBfKqAT2u0YCjcTrFj93vkzdCQp3onKEphHywKczHPtrn6m4pVPRddL94Tq3HHnLebDRDI9cJ79NLaoYYpO6QnyjROKJuHHAqqpaM27EWdzwJxPZKmEeCF0Y66hmY9l8Esa06ima5ni5ZJ5wU1SkwY0frqpxMwhRZPiAt/qymTA1yqwhdRXtPCtlITSDtyhcuILkFD1/JfcFf7HbTL9DMjzboHCZOoHf+4sxxh2/qElKvxYAVzFrahiS9LCz9yoGdflOc6b0NiI9Yl45AyVi8mFf03CULiebgbXnwMpbEQvDov6c3N6gDz9elD3qAJ1OQIAl00+NxfF2E=",
    "Workday": "revAN/A73L+IoBsaRfqg7kVB3ziKchHE1S0seIwk5NqfES50En2I48kUbZzvy2KbPZ1Cg06YRanwIW6y73pyZXmNN409/0wgtMMbqy37p3nh1a/+BUWXFmWV6OSm1MwwMiwj3bIQysxLqaHKEic0hBXHASGB30s4XlArDJOaR5sa4noI8iNIvuX8WTJ0QKF3tsDSpoG6eJyOI4NVIGpccecsHdKFdC/mxqWXMHiciM2dmL5X+gtGkeiK1khEc8gpi2cpw/uVSYXBEsenV7KxuFtRbMWHbzLbPNKKR1pI14GNmUzpjX5csMdqP5pp6tLq8xjSuf6opBF0r57x1ECs/ywiBWE/H7UbpJUlOy573LVv05Qt7PwUceBljmDpzLq1T1vv4mLBTghszKwqUYbRFEhTB4VPfiC0RIGX9ZmEcd99tUlAwr0I3F0yetVYApkO2XQqenmPysAjYGdmmKt70NNW3jZhoZjNNQMiagfGGEEV6zMHJ8VF5dD5Mk0xeyg1rEXaTBynqNsYV5iGUw5rkeyVLx0TWL+ABKfAbE9MCZU1GnLitD2mhA6m0NnR2tw3bEERYPX0qZP/3kFzVBpJksOV2G0qSHGezGBu0CAVeEEiLSnCK4H46hXQnKduM/KFG1fFJHAjrkyAjh5OEZtAeD6o/BKydgiJTQVYRLjchpE=",
    "SeriLog": "dHIZUQezrjl7uGndEC4gOTq6lo34+IscNllnvVulBlFfNYtPCzm2RLOW0KdbJnNQa3ewJc6x3+05SUlkKrzCPOtdfG+xDN7sWrRMAw+QMKtsrAfuT0QVLpVscQw0D+ut1/bicmnrvKefeu1LPWxZYtjPOLKDlKYrQ00yHqzAvntvt95pEoi6Tx49PXjgL43L3zPXQFMJTo1vP1i8GRvqV015xw/ac/HXVCyWEGpujsCuushlTuXRjrnpWzhRnwYW2kI2rg9qWkXyTRukKyRVDSjo08yCp6xRsdyfeJBCev30UtpmvhlvJRkiQ9gE+hVsvoZNqJ3yzpQXUIxNTN7tI/p/7hVEYrh1qve/yOQiqHsFsl/GPRheSBURP15b742/bzV4Sf1esB1a/oYeeEOrzLphdVCQCkUmAPJYCwzsCKyyg2jygVvgQgmP0RXP99sU3Ho7KNeq1iOItwfz9JSFT7d41/+7re4Rjujk6NPmsB7BbVooXabYCgat8iZAyaNJjnQYm4AzHBbEQNEso3QNLP2YxlaNBATh52yEIO6K2Q4ib11iVy0S1z3csoKuENp9VU6MXgGBF7DJa9eJMTEC8pWkCUxRNuY3lMJolwCm3simMGN/ClFONB9azQysbqHLlz/pESy0V3DpVdXii31vj47V3v+L5OEqy7i67mwtv3Q="
  },
  "ConnectionStringKeys": {
    "Attendance": "Attendance",
    "Project": "MIS",
    "Department": "MIS",
    "Employee": "MIS",
    "SinoSign": "SinoSign",
    "Workday": "Workday",
    "SeriLog": "Serilog"
  },
  "Email": {
    "SmtpHost-decrypt": "mail.sinotech.org.tw",
    "SmtpHost": "xyYKvy3lMfrOtwue3Fm7Y/bY77650NRuzSPvoymr/uMUgvxz/kmLEWputkqPqZYSn+A41VFsDoUOrQQNXnKdLwXOdbIGWDUINANbSzCQ5999B952/CEOmehzeq5J0iETLd99C5aj8R/63T3BggTN2aacVZTVdUp1RR2wldu7cwsIkiDB99K2+ahdn2wIWt1CHdOhP2iTh0GS+USiu5+tkDQGsUuibAasuuu3pK/uFB1ez6vEHTqoJjulTbTJYXICBJYZ6VHBWi4uvpLBRnmAAz1oHZwXqLiytWYihCNAIVZIKiaGQyflg1uR0LDu/NNi42LgQZrRbCTHolUvxc8Hu3CZWQYU/cg/t456exkbgTiJ2/PuiIHYIwuYsMx3/MpLfLkXNR7g/t7rs0nlXfl39VzPtyvgnW4PTPYESYPUOskK2HUIj0tn+T28ON8f74pL02LB5OaEGsIjpez81KXeskNXe9Si/JOMQQoaa3vlhMRpGBOpvOrVpbEvS/x+FlPfE+Y921EXcarqh0cOy6gm9YCgf99xyXRVCbwXuBpC2a4SzVNTcnXqkqDAwI57a86eNPFNVXCAL1p+kM4s3v9ZUuwqY2LnxYfIn2u7p23HmahnaVBOtRRAqWA7v+EIw2tUDjcGo6jBb2zFicSNEmH3GPTH3ku32dw2+wJ9opcSxKY=",
    "Port": 25,
    "Username-decrypt": "sinosys",
    "Password-decrypt": "S!no280SS",
    "Username": "Uv/RMLGIGRoj2plvKJ5agU7XKGyKO1XY9Eawx3veLzAQPgokllxgshXzg5L/i/DzAIq0kRunsOSc26OKuyjTsDwMaKcH9sRHAiCNMrP8QigmpfjIyOBQCR3hXInTiVp2K7jercJ59AM813ZWG1cBJ+D+oU3lyXs3Y/hVku+AQxS30bTqbXocV51XhXhP2q+UVm/4pgitReLwvEBRHTR49g3/sPW9HIh6ac6TiGuzFgW3kMpCyHP8qE6AxCBhziHzhc4/T2WRx+vJDHJAfSseGIWTeywAfstnAStlsjSBslFjAGWXa13F8UrYt0g2MAjIswMIa3+I6MbE0pE3+nKsviTQMvk1CcqZjibIDBTJI2fdf0UT40tTB5PZvIQjJHWeckssmg26hE0yDRzz6Jq1cKDt8l3AI1K4SCqe89tPzJimbUqQ+mWtyLmGrcAfGw0NDahtbqJIzrIbxWGqH9KLXRWwhHMiYqla2OJs/vmemyyLZBHddi0Y7YXh5FHOys/rugoeZJpEUmj0ee8rOQOtl3PJrdLy0VULAEYkG8jjl3DCHimuatUznM3y2gKkvAdscMvy4Vg5mdf9XnDQi2YMtJ2IMclXQ4mUESX8syxSKaDizqJ0Ms7LOyLgwMvQXdSpTDM3Coe9n7amaJwPr/cZUyU/IEb1bDKJ3dz0YpdwAf4=",
    "Password": "eTa5TEc9MYXSI/HryEtIbq6jBzXxvOX01T5LkoE1/ksZZO637cwWe1x8z8lYh27BPxaVaNgzbO4HL2e6IBTvW2UuPYdziClwGKAXKrm64xuXJALJTRQpmdYhyDHrjoBcazxAxRF4hEdn3z/mil9LGrMKWQhWZUZofOg12mmnO+VDC8iItwAzRvCX6YRZRjXDxxqqkt+F3IgYf/AHUNwSYRLbt5eZdrAnKa1xRBW56TZrvazqwfRT8A9gdFY8JtnY+draHQGYQbrUGwuA9ml0i3WYsLJGmiJqbZrz5tgZBFazCAkHUS3wXzqMWGD5kpvaPDBmBLuhDaxDNrS4+7k0CMwPYh89Fs9ICeyZUQCKPhK0doBp3pdfmrkPB0WrF4t7djiGP/tAtuxVMt1WinTcqdiOMVKMxLqGeZ0HRmSu+V9rbIO4kikfLIAkA1A4nqtsLn9akfvpqhMBZZ3KrgS9BxEi2dnhljgHjOt64XLIr7YAXhxBNEXiHkkeeO/WwSMlXlOXRBLDikNxPAlhH8gZh+ZZzGDNzCZwEA3TqcF0Eg17maKqhJXTbaCXSyqryVRda8PzE7+l6drIEHWWTYdiDcTzG4X6s8n3SCnMlfEzrXBzCYjAD0eZ4CcW7Jzder0VRTDDMv3x8BDrbEORSouPexl7f93ZO6obxBtrHp3Akhc=",
    "ApplicationURL": "https://attendancedev.sinotech.org.tw/AttendanceCard/"
  },
  "IsMultipleVicePresidents": true,
  "UseNegotiate": false,
  "UploadDirectory": "C:\\Windows\\Temp",
  "GeoDbDirectory": "d:\\GeoIP",
  "ApproveCommentsHtml": false,
  "AllowedHosts": "*",
  "Attachments": {
    "ShareFolder": "c:\\Windows\\Temp",
    "Domain": "",
    "UserName": "",
    "Password": ""
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Error",
      "Override": {
        "Microsoft": "Error",
        // 合併多個Log, 寫成Information等級
        "Serilog.AspNetCore.RequestLoggingMiddleware": "Error",
        // 加入參數，輸出Response， 寫成Information等級
        "Sinotech.Mis.Serilog.Utilities.Middlewares.SerilogRequestLogger": "Error",
        "Vue": "Error"
      }
    },
    "WriteTo": [
      {
        "Name": "Console",
        "Args": {
          "restrictedToMinimumLevel": "Information"
        }
      },
      {
        "Name": "File",
        "Args": {
          "path": ".\\Logs\\logTest.txt",
          "rollingInterval": "Day",
          "outputTemplate": "{Timestamp} {Message}{NewLine:1}{Exception:1}",
          "restrictedToMinimumLevel": "Information",
          "shared": "true"
        }
      }
    ],
    "Enrich": [
      "FromLogContext",
      "WithMachineName",
      "WithProcessId",
      "WithProcessName",
      "WithCorrelationId",
      "WithThreadId"
    ],
    "Properties": {
      "ApplicationName": "AttendanceCard",
      "ApplicationVersion": "1.0"
    }
  },
  "Serilog.MsSqlServer": {
    "Name": "MSSqlServer",
    "Args": {
      "connectionString": "Serilog",
      "sinkOptionsSection": {
        "tableName": "A_LogEvents",
        "useBulkInsert": false,
        "useSqlBulkCopy": false,
        "schemaName": "dbo",
        "autoCreateSqlTable": false
      },
      "restrictedToMinimumLevel": "Information",
      "columnOptionsSection": {
        "disableTriggers": true,
        "clusteredColumnstoreIndex": false,
        "primaryKeyColumnName": "Id",
        "addStandardColumns": [ "LogEvent" ],
        "removeStandardColumns": [ "Properties" ],
        "additionalColumns": [
          {
            "ColumnName": "Username",
            "DataType": "nvarchar",
            "AllowNull": true,
            "DataLength": 256
          },
          {
            "ColumnName": "ClientIp",
            "DataType": "nvarchar",
            "AllowNull": true,
            "DataLength": 128
          },
          {
            "ColumnName": "UserAgent",
            "DataType": "nvarchar",
            "AllowNull": true,
            "DataLength": 256
          },
          {
            "ColumnName": "MachineName",
            "DataType": "nvarchar",
            "AllowNull": true,
            "DataLength": 128
          },
          {
            "ColumnName": "ProcessName",
            "DataType": "nvarchar",
            "AllowNull": true,
            "DataLength": 256
          },
          {
            "ColumnName": "SourceContext",
            "DataType": "nvarchar",
            "AllowNull": true,
            "DataLength": 512
          },
          {
            "ColumnName": "RequestHost",
            "DataType": "nvarchar",
            "AllowNull": true,
            "DataLength": 512
          },
          {
            "ColumnName": "RequestQueryString",
            "DataType": "nvarchar",
            "AllowNull": true,
            "DataLength": -1
          },
          {
            "ColumnName": "RequestContentType",
            "DataType": "nvarchar",
            "AllowNull": true,
            "DataLength": 256
          },
          {
            "ColumnName": "RequestProtocol",
            "DataType": "nvarchar",
            "AllowNull": true,
            "DataLength": 128
          },
          {
            "ColumnName": "RequestScheme",
            "DataType": "nvarchar",
            "AllowNull": true,
            "DataLength": 128
          },
          {
            "ColumnName": "RequestMethod",
            "DataType": "nvarchar",
            "AllowNull": true,
            "DataLength": 10
          },
          {
            "ColumnName": "RequestPath",
            "DataType": "nvarchar",
            "AllowNull": true,
            "DataLength": -1
          },
          {
            "ColumnName": "RequestHeaders",
            "DataType": "nvarchar",
            "AllowNull": true,
            "DataLength": -1
          },
          {
            "ColumnName": "RequestBody",
            "DataType": "nvarchar",
            "AllowNull": true,
            "DataLength": -1
          },
          {
            "ColumnName": "ResponseBody",
            "DataType": "nvarchar",
            "AllowNull": true,
            "DataLength": -1
          },
          {
            "ColumnName": "StatusCode",
            "DataType": "nvarchar",
            "AllowNull": true,
            "DataLength": 10
          }
        ],
        "id": { "nonClusteredIndex": true },
        "message": { "columnName": "Message" },
        "messageTemplate": { "columnName": "MessageTemplate" },
        "level": {
          "columnName": "Level",
          "storeAsEnum": false
        },
        "timeStamp": {
          "columnName": "TimeStamp",
          "convertToUtc": false
        },
        "exception": { "columnName": "Exception" },
        "logEvent": {
          "columnName": "LogEvent",
          "excludeAdditionalProperties": false,
          "excludeStandardColumns": false
        }
      }
    }
  }
  //"ProjectBusinessObject": {
  //  "ConnectionStringKey": "MIS",
  //  "DaoAssemblyName": "Sinotech.Mis.Utilities.DataAccess.Ado",
  //  "DaoTypeName": "Sinotech.Mis.Utilities.DataAccess.Ado.ProjectDao",
  //  "BusinessObjectAssemblyName": "Sinotech.Mis.HR.Attendance.BusinessLogic",
  //  "BusinessObjectTypeName": "Sinotech.Mis.HR.Attendance.BusinessLogic.ProjectBo"
  //},
  //"DataAccessAssemblies": {
  //  "AttendanceDao": "Sinotech.Mis.HR.Attendance.DataAccess.Ado",
  //  "DepartmentDao": "Sinotech.Mis.HR.Attendance.DataAccess.Ado",
  //  "EmployeeDao": "Sinotech.Mis.HR.Attendance.DataAccess.Ado",
  //  "ProjectDao": "Sinotech.Mis.HR.Attendance.DataAccess.Ado",
  //  "SinoSignDao": "Sinotech.Mis.HR.Attendance.DataAccess.Ado",
  //  "WorkdayDao": "Sinotech.Mis.HR.Attendance.DataAccess.Ado"
  //},
  //"DataAccessTypes": {
  //  "AttendanceDao": "Sinotech.Mis.HR.Attendance.DataAccess.Ado.AttendanceDao",
  //  "DepartmentDao": "Sinotech.Mis.HR.Attendance.DataAccess.Ado.DepartmentDao",
  //  "EmployeeDao": "Sinotech.Mis.HR.Attendance.DataAccess.Ado.EmployeeDao",
  //  "ProjectDao": "Sinotech.Mis.HR.Attendance.DataAccess.Ado.ProjectDao",
  //  "SinoSignDao": "Sinotech.Mis.HR.Attendance.DataAccess.Ado.SinoSignDao",
  //  "WorkdayDao": "Sinotech.Mis.HR.Attendance.DataAccess.Ado.WorkdayDao"
  //}
}
