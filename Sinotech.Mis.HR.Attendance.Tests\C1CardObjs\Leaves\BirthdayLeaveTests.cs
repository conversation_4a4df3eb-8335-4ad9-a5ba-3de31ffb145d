﻿using FakeItEasy;
using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves.Tests
{
    public class BirthdayLeaveTests : TestC1CardBase
    {
        public BirthdayLeaveTests()
        {
            #region Prepare Data

            _c1Card.LeaveNumber = LeaveKindEnum.BirthdayLeave;

            #endregion
        }

        [Theory]
        [InlineData(0, 1, 1, true, BirthdayLeave.CodeNotRegistered)]
        [InlineData(0, 1, 2, true, BirthdayLeave.CodeNotRegistered)]
        [InlineData(1, 1, 1, true, BirthdayLeave.CodeOk)]
        [InlineData(1, 1, 2, true, BirthdayLeave.CodeNotInRange)]
        [InlineData(2, 1, 1, true, BirthdayLeave.CodeNotRegistered)]
        [InlineData(2, 1, 2, true, BirthdayLeave.CodeNotRegistered)]
        [InlineData(3, 1, 1, true, BirthdayLeave.CodeNotRegistered)]
        [InlineData(3, 1, 2, true, BirthdayLeave.CodeNotRegistered)]
        [InlineData(0, 1, 1, false, BirthdayLeave.CodeNotRegistered)]
        [InlineData(0, 1, 2, false, BirthdayLeave.CodeNotRegistered)]
        [InlineData(1, 1, 1, false, BirthdayLeave.CodeOk)]
        [InlineData(1, 1, 2, false, BirthdayLeave.CodeNotInRange)]
        [InlineData(2, 1, 1, false, BirthdayLeave.CodeNotRegistered)]
        [InlineData(2, 1, 2, false, BirthdayLeave.CodeNotRegistered)]
        [InlineData(3, 1, 1, false, BirthdayLeave.CodeNotRegistered)]
        [InlineData(3, 1, 2, false, BirthdayLeave.CodeNotRegistered)]
        public void TestCanTakeThisLeave(int welfare, int birthdayMonth, int applyMonth, bool isUsed, int returnCode)
        {
            var birthday = new System.DateTime(2000, birthdayMonth, 1);
            A.CallTo(() => _c1CardBo.GetBirthday(A<string>.Ignored)).Returns(birthday);
            _c1Card.StartDate = new System.DateTime(DateTime.Now.Year, applyMonth, 1);

            A.CallTo(() => _c1CardBo.GetBirthdayWelfare(A<string>.Ignored, A<int>.Ignored)).Returns(welfare);
            A.CallTo(() => _c1CardBo.IsBirthdayLeaveTaken(A<string>.Ignored, A<int>.Ignored)).Returns(isUsed);

            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CanTakeThisLeave();

            Assert.Equal(returnCode, result.Code);
        }

        [Theory]
        [InlineData(0, true, 8, BirthdayLeave.CodeExceedQuota)]
        [InlineData(0, false, 8, BirthdayLeave.CodeOk)]
        [InlineData(0, false, 10, BirthdayLeave.CodeIllegalRange)]
        [InlineData(0, false, 1, BirthdayLeave.CodeIllegalRange)]
        [InlineData(0, false, 9, BirthdayLeave.CodeIllegalRange)]
        [InlineData(0, false, 16, BirthdayLeave.CodeIllegalRange)]
        [InlineData(0, true, 16, BirthdayLeave.CodeExceedQuota)]
        [InlineData(1, true, 8, BirthdayLeave.CodeExceedQuota)]
        [InlineData(1, false, 8, BirthdayLeave.CodeOk)]
        [InlineData(1, false, 10, BirthdayLeave.CodeIllegalRange)]
        [InlineData(1, false, 1, BirthdayLeave.CodeIllegalRange)]
        [InlineData(1, false, 9, BirthdayLeave.CodeIllegalRange)]
        [InlineData(1, false, 16, BirthdayLeave.CodeIllegalRange)]
        [InlineData(1, true, 16, BirthdayLeave.CodeExceedQuota)]
        [InlineData(2, true, 8, BirthdayLeave.CodeExceedQuota)]
        [InlineData(2, false, 8, BirthdayLeave.CodeOk)]
        [InlineData(2, false, 10, BirthdayLeave.CodeIllegalRange)]
        [InlineData(2, false, 1, BirthdayLeave.CodeIllegalRange)]
        [InlineData(2, false, 9, BirthdayLeave.CodeIllegalRange)]
        [InlineData(2, false, 16, BirthdayLeave.CodeIllegalRange)]
        [InlineData(2, true, 16, BirthdayLeave.CodeExceedQuota)]
        public void TestExceedQuota(int welfare, bool isUsed, int totalHours, int returnCode)
        {
            A.CallTo(() => _c1CardBo.GetBirthdayWelfare(A<string>.Ignored, A<int>.Ignored)).Returns(welfare);
            A.CallTo(() => _c1CardBo.IsBirthdayLeaveTaken(A<string>.Ignored, A<int>.Ignored)).Returns(isUsed);
            A.CallTo(() => _c1CardBo.CalculateEmpTakeLeaveWorkingHours(A<string>.Ignored, A<DateTime>.Ignored, A<DateTime>.Ignored)).Returns(totalHours);

            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CheckOverPermittedLeaveHours();

            Assert.Equal(returnCode, result.Code);
        }

        [Theory]
        [InlineData(1, BirthdayLeave.CodeIllegalRange)]
        [InlineData(9, BirthdayLeave.CodeIllegalRange)]
        [InlineData(8, BirthdayLeave.CodeOk)]
        public void TestRange(int totalHours, int returnCode)
        {
            A.CallTo(() => _c1CardBo.IsBirthdayLeaveTaken(A<string>.Ignored, A<int>.Ignored)).Returns(false);
            A.CallTo(() => _c1CardBo.CalculateEmpTakeLeaveWorkingHours(A<string>.Ignored, A<DateTime>.Ignored, A<DateTime>.Ignored)).Returns(totalHours);

            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CheckOverPermittedLeaveHours();

            Assert.Equal(returnCode, result.Code);
        }

        [Fact]
        public void TestCheckRequiredFields()
        {
            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CheckRequiredFields();

            Assert.Equal(BirthdayLeave.CodeOk, result.Code);
        }

        [Theory]
        [InlineData(Gender.Female, true)]
        [InlineData(Gender.Male, true)]
        public void TestGender(Gender gender, bool expected)
        {
            var result = BirthdayLeave.IsAllowForThisGender(gender);
            Assert.Equal(expected, result);
        }
    }
}