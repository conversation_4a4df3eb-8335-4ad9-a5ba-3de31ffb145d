﻿using Xunit;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves;
using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves.Tests
{
    /// <summary>
    /// 公出假測試類別
    /// 從 nUnit 轉換為 xUnit 版本
    /// </summary>
    public class BusinessOutLeaveTests : TestC1CardBase
    {
        /// <summary>
        /// 初始化公出假測試資料
        /// 取代 nUnit 的 [SetUp] 方法
        /// </summary>
        public BusinessOutLeaveTests()
        {
            #region Prepare Data

            _c1Card.LeaveNumber = LeaveKindEnum.BusinessOutLeave;

            #endregion
        }

        [Fact]
        public void TestCanTakeThisLeave()
        {
            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CanTakeThisLeave();

            Assert.Equal(BusinessOutLeave.CodeOk, result.Code);
        }

        [Fact]
        public void TestExceedQuota()
        {
            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CheckOverPermittedLeaveHours();

            Assert.Equal(BusinessOutLeave.CodeOk, result.Code);
        }

        [Theory]
        [InlineData(null, BusinessOutLeave.CodeReasonFieldRequired)]
        [InlineData("", BusinessOutLeave.CodeReasonFieldRequired)]
        [InlineData(" ", BusinessOutLeave.CodeReasonFieldRequired)]
        [InlineData(" \t", BusinessOutLeave.CodeReasonFieldRequired)]
        [InlineData("Reason", BusinessOutLeave.CodeOk)]
        public void TestCheckRequiredFields(string reason, int expectedCode)
        {
            _c1Card.Reason = reason;
            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CheckRequiredFields();

            Assert.Equal(expectedCode, result.Code);
        }

        [Theory]
        [InlineData(Gender.Female, true)]
        [InlineData(Gender.Male, true)]
        public void TestGender(Gender gender, bool expected)
        {
            var result = BusinessOutLeave.IsAllowForThisGender(gender);
            Assert.Equal(expected, result);
        }
    }
}
