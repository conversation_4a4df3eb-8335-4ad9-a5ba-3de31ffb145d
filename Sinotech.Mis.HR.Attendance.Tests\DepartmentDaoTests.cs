﻿using Microsoft.Extensions.Configuration;
using Sinotech.Mis.Extensions.Configuration;
using Sinotech.Mis.Utilities.DataAccess.Ado;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.Utilities.DataAccess.Ado.Tests
{
    [ExcludeFromCodeCoverage]
    public class DepartmentDaoTests
    {

        private readonly DepartmentDao _departmentDao;

        public DepartmentDaoTests()
        {
            IConfiguration Configuration = new ConfigurationBuilder().
                AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).
                Build();
            string connectionStringDepartment = Configuration.GetSecuredConnectionString("Department");
            _departmentDao = new DepartmentDao(connectionStringDepartment);
        }

        [Theory]
        [InlineData(-1, true)]
        [InlineData(0, true)]
        [InlineData(1, false)]
        [InlineData(2, false)]
        [InlineData(3, false)]
        [InlineData(4, false)]
        [InlineData(5, false)]
        [InlineData(6, false)]
        [InlineData(7, false)]
        [InlineData(8, false)]
        [InlineData(9, false)]
        [InlineData(10, false)]
        [InlineData(11, false)]
        [InlineData(12, false)]
        [InlineData(13, true)]
        [InlineData(14, false)]
        [InlineData(15, false)]
        [InlineData(16, false)]
        [InlineData(17, false)]
        [InlineData(18, false)]
        [InlineData(19, true)]
        [InlineData(20, true)]
        public void GetDepartmentNameTest(int deptNo, bool expect)
        {
            string departmentName = _departmentDao.GetDepartmentName(deptNo);
            bool actual = string.IsNullOrWhiteSpace(departmentName);
            Assert.Equal(expect, actual);
        }

        [Theory]
        [InlineData(-1, true)]
        [InlineData(0, true)]
        [InlineData(1, false)]
        [InlineData(2, false)]
        [InlineData(3, false)]
        [InlineData(4, false)]
        [InlineData(5, false)]
        [InlineData(6, false)]
        [InlineData(7, false)]
        [InlineData(8, false)]
        [InlineData(9, false)]
        [InlineData(10, false)]
        [InlineData(11, false)]
        [InlineData(12, false)]
        [InlineData(13, true)]
        [InlineData(14, false)]
        [InlineData(15, false)]
        [InlineData(16, false)]
        [InlineData(17, false)]
        [InlineData(18, false)]
        [InlineData(19, true)]
        [InlineData(20, true)]
        public void GetDepartmentShortNameTest(int deptNo, bool expect)
        {
            string departmentName = _departmentDao.GetDepartmentShortName(deptNo);
            bool actual = string.IsNullOrWhiteSpace(departmentName);
            Assert.Equal(expect, actual);
        }

        [Fact]
        public void GetAboveDeputyManagerDataTableTest()
        {
            DataTable dt = _departmentDao.GetAboveDeputyManagerDataTable();
            Assert.NotNull(dt);
            Assert.NotEmpty(dt.Rows);
        }

        [Theory]
        [InlineData(-1, true)]
        [InlineData(0, true)]
        [InlineData(1, false)]
        [InlineData(2, false)]
        [InlineData(3, false)]
        [InlineData(4, false)]
        public void GetDepartmentsManagerTest(int departmentNumber, bool expect)
        {
            DataTable dt = _departmentDao.GetDepartmentsManager(departmentNumber);
            bool actual = dt.Rows.Count == 0;
            Assert.Equal(expect, actual);
        }

        [Fact]
        public void GetDepartmentsManagersTest()
        {
            DataTable dt = _departmentDao.GetDepartments();
            Assert.NotNull(dt);
            Assert.NotEmpty(dt.Rows);
        }

        [Fact]
        public void GetDepartmentsTeamsTest()
        {
            DataTable dt = _departmentDao.GetDepartmentsTeams();
            Assert.NotNull(dt);
            Assert.NotEmpty(dt.Rows);
        }

        [Fact]
        public void GetDepartmentsTeamsEmployeesTest()
        {
            DataTable dt = _departmentDao.GetDepartmentsTeamsEmployees();
            Assert.NotNull(dt);
            Assert.NotEmpty(dt.Rows);
        }

        [Fact]
        public void GetTeamLeadersTest()
        {
            DataTable dt = _departmentDao.GetTeamLeaders();
            Assert.NotNull(dt);
            Assert.NotEmpty(dt.Rows);
        }

        [Theory]
        [InlineData(-1, false)]
        [InlineData(0, false)]
        [InlineData(1, false)]
        [InlineData(2, false)]
        [InlineData(3, false)]
        [InlineData(4, false)]
        [InlineData(5, false)]
        [InlineData(6, false)]
        [InlineData(8, true)]
        [InlineData(10, true)]
        public void GetDepartmentTeamsTest(int departmentNumber, bool expected)
        {
            DataTable dt = _departmentDao.GetDepartmentTeams(departmentNumber);
            bool actual = dt.Rows.Count > 0;
            Assert.Equal(expected, actual);
        }
    }
}