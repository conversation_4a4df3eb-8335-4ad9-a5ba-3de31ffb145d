﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;

namespace Sinotech.Mis.HR.Attendance.Common
{
    [ExcludeFromCodeCoverage]
    /// <summary>
    /// 請假卡
    /// </summary>
    public class C1Card : CardBase
    {
        /// <summary>
        /// 流水號
        /// </summary>
        public int? ID { get; set; }

        /// <summary>
        /// 卡片類別名稱
        /// </summary>
        public override string Name { get; } = "C1Card";

        /// <summary>
        /// 表單識別碼
        /// </summary>
        public override Guid FormUID { get; set; } = Guid.Empty;

        /// <summary>
        /// 員工編號
        /// </summary>
        public string EmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 請假起始日期時間
        /// </summary>
        public DateTime StartDate { get; set; }


        /// <summary>
        /// 請假截止日期時間
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 請假天數
        /// </summary>
        public int Days { get; set; }

        /// <summary>
        /// 去除請假日數的時數
        /// </summary>
        public int NetHours { get; set; }

        /// <summary>
        /// 請假時數，僅紀錄工作日時數
        /// </summary>
        public int Hours { get; set; }

        /// <summary>
        /// 請假天數時數
        /// </summary>
        public string DayHours { get; set; } = string.Empty;

        /// <summary>
        /// 請假別
        /// </summary>
        /// <value>
        /// 01:特別休息假,02:婚假,03:公假,04:事假,05:公出,06:出差,07:產假,08:陪產檢及陪產假,
        /// 09:公傷假,10:病假,11:喪假,12:保留代休假,13:休國外假,14:延休假,15:產檢假,16:生理假,
        /// 17:防疫照顧假,18:防疫隔離假,19:家庭照顧假,20:疫苗接種假,21:生日假,22:防災假
        /// </value>
        public LeaveKindEnum LeaveNumber { get; set; } = LeaveKindEnum.AnnualLeave;

        /// <summary>
        /// 假別細項代碼
        /// </summary>
        public int LeaveSubNumber { get; set; } = 0;

        /// <summary>
        /// 請假事由
        /// </summary>
        public string Reason { get; set; } = string.Empty;

        /// <summary>
        /// 計畫編號
        /// </summary>
        /// <value>
        /// 計畫編號
        /// </value>
        public string ProjectNumber { get; set; } = string.Empty;

        /// <summary>
        /// 出差地點
        /// </summary>
        public string Location { get; set; } = string.Empty;

        /// <summary>
        /// 代理人
        /// </summary>
        /// <value>
        /// 代理人員工編號
        /// </value>
        public string Deputy { get; set; } = string.Empty;

        /// <summary>
        /// 代理人姓名
        /// </summary>
        /// <value>
        /// 代理人姓名
        /// </value>
        public string DeputyName { get; set; } = string.Empty;

        /// <summary>
        /// YYYMMDD    填卡日期民國年月日
        /// </summary>
        public DateTime WDate { get; set; }
        /// <summary>
        /// 事件日期
        /// </summary>
        public DateTime? EventDate { get; set; } = null;
        //public DateTime EventDate { get; set; } = DateTime.MinValue;

        /// <summary>
        /// 請假期限起始日
        /// </summary>
        public DateTime ExpirationStartDate { get; set; } = DateTime.MinValue;

        /// <summary>
        /// 請假期限截止日
        /// </summary>
        public DateTime ExpirationEndDate { get; set; } = DateTime.MinValue;
#nullable enable
        /// <summary>
        /// YYYMMDD    結案核卡日期民國年月日
        /// </summary>
        public DateTime? ADate { get; set; }

        /// <summary>
        /// 簽核狀態
        /// </summary>
        /// <value>
        ///  1:未簽核 2:核可 3:不核可 4:抽單
        /// </value>
        public int Status { get; set; } = 1;

        /// <summary>
        /// 是否須檢附證明文件
        /// </summary>
        public bool CertificateRequired { get; set; } = false;

        /// <summary>
        /// 超假狀態
        /// </summary>
        public bool OverPermittedHours { get; set; } = false;

        /// <summary>
        /// 請假卡表單單號
        /// </summary>
        public string FormNumber { get; set; } = string.Empty;

        /// <summary>
        /// 表單資訊
        /// </summary>
        public string FormInfo { get; set; } = string.Empty;

        /// <summary>
        /// 假別上限，單位為 LeaveUnit
        /// </summary>
        public int? LeaveMaximum { get; set; } = null;

        /// <summary>
        /// 假別單位
        /// </summary>
        public string LeaveUnit { get; set; } = "H";

        /// <summary>
        /// 假別名稱
        /// </summary>
        public string LeaveName { get; set; } = string.Empty;

        /// <summary>
        /// 假別單位
        /// </summary>
        public string LeaveMinimumUnit { get; set; } = "H";

        /// <summary>
        /// 相關單號
        /// </summary>
        /// <value>
        /// 單號
        /// </value>
        public string RelatedFormNumber { get; set; } = string.Empty;

        /// <summary>
        /// 已確認
        /// </summary>
        public bool Confirmed { get; set; } = false;

        /// <summary>
        /// 請假單依月份拆單
        /// </summary>
        public List<C1CardDetail> Details { get; set; } = new List<C1CardDetail>();

        /// <summary>
        /// C1Card to LeaveView
        /// </summary>
        /// <param name="c1Card">C1Card</param>
        /// <returns></returns>
        public static LeaveView C1CardToLeaveView(C1Card c1Card)
        {
            LeaveView leave = new LeaveView();
            leave.EmpNo = c1Card.EmpNo;
            leave.StartTime = c1Card.StartDate;
            leave.EndTime = c1Card.EndDate;

            //leave.ExpirationStartDate = c1Card.ExpirationStartDate;
            //leave.ExpirationEndDate = c1Card.ExpirationEndDate;

            leave.AddSigners = c1Card.AddSigners;
            leave.Reason = c1Card.Reason;
            leave.OverPermittedHours = c1Card.OverPermittedHours;
            if (c1Card.EventDate != null && c1Card.EventDate != DateTime.MinValue)
            {
                leave.EventDate = c1Card.EventDate;
            }
            leave.Deputy = c1Card.Deputy;
            leave.EndTime = c1Card.EndDate;
            leave.CreatedTime = c1Card.CreatedTime;
            leave.FilledTime = c1Card.FilledTime;
            leave.LeaveKind = (int)c1Card.LeaveNumber;
            leave.LeaveDetailNumber = c1Card.LeaveSubNumber;
            leave.RelatedFormNumber = c1Card.RelatedFormNumber;
            leave.ProjectNumber = c1Card.ProjectNumber;
            leave.Location = c1Card.Location;
            leave.UploadedFiles = c1Card.UploadedFiles;
            leave.UpdatedEmpNo = c1Card.UpdatedEmpNo;
            leave.UpdatedName = c1Card.UpdatedName;
            leave.UpdatedTime = c1Card.UpdatedTime;
            leave.UpdatedIP = c1Card.UpdatedIP;
            leave.UpdatedHost = c1Card.UpdatedHost;
            leave.Confirmed = c1Card.Confirmed;
            return leave;
        }

        /// <summary>
        /// Only for 比對，並非真正 EasyClone
        /// </summary>
        /// <returns></returns>
        public C1Card EasyClone()
        {
            C1Card card = new C1Card();
            card.AddSigners = AddSigners;
            card.CreatedTime = CreatedTime;
            card.FilledTime = FilledTime;
            card.FilledTime = FilledTime;
            card.ApplicationType = ApplicationType;
            card.EmpNo = EmpNo;
            card.Reason = Reason;
            card.Details = Details;
            return card;
        }

        /// <summary>
        /// 簡單比對，不嚴謹
        /// </summary>
        /// <param name="other"></param>
        /// <returns></returns>
        public bool EasyEquals(C1Card? other)
        {
            return other is not null &&
                   AddSigners == other.AddSigners &&
                   CreatedTime == other.CreatedTime &&
                   FilledTime == other.FilledTime &&
                   ApplicationType == other.ApplicationType &&
                   EmpNo == other.EmpNo &&
                   Reason == other.Reason &&
                   Details.Count == other.Details.Count;
        }

        /// <summary>
        /// 設定申請別
        /// </summary>
        public override void SetApplicationType()
        {
            ApplicationType = LeaveName;
        }
    }
}
