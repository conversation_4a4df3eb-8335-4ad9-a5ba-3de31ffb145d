﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Sinotech.Mis.HR.Attendance.AttendanceCard.Models;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.Utilities;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

#nullable enable

namespace Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers
{
    /// <summary>
    /// 帳號 Web API
    /// 登入 Login、登出 Logout 及 是否已登入 IsAuthenticated
    /// </summary>
    [RequireHttps]
    [ApiController]
    [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
    [Route("/")]
    public class AccountController : ControllerBase
    {
        private const int MinimumAccountLength = 4;
        private const int MinimumPasswordLength = 8;
        private const int MaximumAccountLength = 50;
        private const int MaximumPasswordLength = 50;

        private readonly IAccountBo _accountBo;
        private readonly IAttendanceBo _attendanceBo;
        private readonly IEmployeeBo _employeeBo;
        private readonly ILogger<AccountController> _logger;
        /// <summary>
        /// 是否使用 Negotiate 認證，表示在內網使用Windows認證
        /// </summary>
        private readonly bool _useNegotiate;

        /// <summary>
        /// 建構元
        /// </summary>
        /// <param name="attendanceBo">考勤 Business Object</param>
        /// <param name="employeeBo">員工 Business Object</param>
        /// <param name="accountBo">帳號 Business Object</param>
        /// <param name="configuration">設定</param>
        /// <param name="logger">日誌記錄器</param>
        public AccountController(IAttendanceBo attendanceBo, IEmployeeBo employeeBo, 
            IAccountBo accountBo, IConfiguration configuration, ILogger<AccountController> logger)
        {
            _employeeBo = employeeBo;
            _accountBo = accountBo;
            if (configuration == null || employeeBo == null)
            {
                throw new ArgumentNullException(nameof(configuration));
            }

            _attendanceBo = attendanceBo;
            _employeeBo = employeeBo;
            _useNegotiate = configuration.GetValue<bool>("UseNegotiate");
            _logger = logger;
        }

        /// <summary>
        /// Common part of IsAuthenticated
        /// </summary>
        /// <returns> {IsAuthenticated: true/false, UserId: 員工編號 } </returns>
        private IActionResult CommonIsAuthenticated()
        {
            // 判斷是否已登入
            OkObjectResult ret = Ok(new { IsAuthenticated = false });
            if (HttpContext.User.Identity != null && HttpContext.User.Identity.IsAuthenticated
                && HttpContext.User.Identity.Name != null)
            {
                string userId = HttpContext.User.Identity.Name;
                if (userId.Contains('\\'))
                {
                    userId = CardUtility.GetValidEmpNo(userId);
                }
                string name = _employeeBo.GetEmployeeName(userId);
                if (!string.IsNullOrWhiteSpace(name))
                {
                    ret = Ok(new { IsAuthenticated = true, UserId = userId });
                }
            }
            return ret;
        }

        /// <summary>
        /// 解密
        /// </summary>
        /// <param name="cipherText"></param>
        /// <returns></returns>
        private string? Decrypt(string cipherText)
        {
            string? decrypt = null;
            // 使用私鑰解密
            const string privateKeyString = @"**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
            try
            {
                byte[] encryptedData = Convert.FromBase64String(cipherText);
                RSA rsa = RSA.Create();
                rsa.ImportFromPem(privateKeyString.ToCharArray());
                byte[] decryptedData = rsa.Decrypt(encryptedData, RSAEncryptionPadding.OaepSHA256);
                decrypt = Encoding.UTF8.GetString(decryptedData);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"{ex.Message} \n {ex.StackTrace}");
            }
            return decrypt;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="ret"></param>
        /// <param name="failCount"></param>
        /// <returns>鎖定訊息JSON, 如 { IsAuthenticated = false, ErrorMessage = AttendanceParameters.AccountLockoutMessage }</returns>
        private IActionResult LockdownMessage(IActionResult ret, int failCount)
        {
            if (failCount >= AttendanceParameters.AccountLockoutFailTimes) // 先前登入失敗達3次，15分鐘內，不允許登入
            {
                ret = Ok(new { IsAuthenticated = false, ErrorMessage = AttendanceParameters.AccountLockoutMessage });
            }
            return ret;
        }

        /// <summary>
        /// 登入並設定 Cookie
        /// </summary>
        /// <param name="claims"></param>
        /// <returns>無傳回值</returns>
        [ExcludeFromCodeCoverage]
        private async Task LoginAndSetCookie(List<Claim> claims)
        {
            var claimsIdentity = new ClaimsIdentity(
                claims, CookieAuthenticationDefaults.AuthenticationScheme);
            var authProperties = new AuthenticationProperties
            {
                // AllowRefresh = <bool>,
                // Refreshing the authentication session should be allowed.
                AllowRefresh = true, // 在 效期剩一半時間內，存取網頁會自動重新設定ExpiresUTC時間

                // ExpiresUtc = DateTimeOffset.UtcNow.AddMinutes(10),
                // The time at which the authentication ticket expires. DualDateTime 
                // value set here overrides the ExpireTimeSpan option of 
                // CookieAuthenticationOptions set with AddCookie.
                ExpiresUtc = DateTimeOffset.UtcNow.AddMinutes(20),

                IsPersistent = true
                // IsPersistent = true 表示持久化 Cookie，直到 ExpiresUtc
                // Whether the authentication session is persisted across 
                // multiple requests. When used with cookies, controls
                // whether the cookie's lifetime is absolute (matching the
                // lifetime of the authentication ticket) or session-based.
                // IsPersistent = false // 預設值
                // IsPersistent = false Cookie 將被視為 Session Cookie 關閉瀏覽器 Cookie 才會失效，此時忽略ExpiresUtc

                // IssuedUtc = <DateTimeOffset>,
                // The time at which the authentication ticket was issued.
                // RedirectUri = <string>
                // The full path or absolute URI to be used as an http 
                // redirect response value.
            };

            await HttpContext.SignInAsync(
                    CookieAuthenticationDefaults.AuthenticationScheme,
                    new ClaimsPrincipal(claimsIdentity),
                    authProperties);
        }

        /// <summary>
        /// Intranet 適用的 IsAuthenticated，內網呼叫 IsAuthenticated 時會自動轉向至此
        /// </summary>
        /// <returns>{IsAuthenticated: true/false, UserId: 員工編號 }</returns>
        [ApiExplorerSettings(IgnoreApi = true)]
        [Authorize]
        [HttpGet]
        [ProducesResponseType<string>(StatusCodes.Status200OK)]
        [Route("api/[controller]/[action]")]
        public IActionResult IntranetIsAuthenticated()
        {
            return CommonIsAuthenticated();
        }

        /// <summary>
        /// 使用者是否已登入
        /// </summary>
        /// <returns>{IsAuthenticated: true/false, UserId: 員工編號 }</returns>
        [AllowAnonymous]
        [HttpGet]
        [ProducesResponseType<string>(StatusCodes.Status200OK)]
        [Route("api/[controller]/[action]")]
        public IActionResult IsAuthenticated()
        {
            IActionResult result;
            if (_useNegotiate)
            {
                result = RedirectToAction("IntranetIsAuthenticated");
            }
            else
            {
                result = CommonIsAuthenticated();
            }
            return result;
        }

        /// <summary>
        /// 登入
        /// </summary>
        /// <param name="loginModel">使用者帳號與密碼</param>
        /// <returns>{IsAuthenticated: true/false, UserId: 員工編號 }</returns>
        [RequireHttps]
        [HttpPost]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("api/[controller]/[action]")]
        public async Task<IActionResult> Login([FromBody] LoginModel loginModel)
        {
            ArgumentNullException.ThrowIfNull(nameof(loginModel));
            DateTime loginTime = DateTime.Now;

            IActionResult ret = Ok(new { IsAuthenticated = false, ErrorMessage = "登入失敗" });
            string username = Decrypt(loginModel.Username) ?? loginModel.Username;
            string password = Decrypt(loginModel.Password) ?? loginModel.Password;
            var (ipAddress, hostname) = CardUtility.GetIP_Hostname(Request.HttpContext);
            if (ipAddress == null)
            {
                throw new ArgumentNullException(nameof(loginModel), "IPAddress is null");
            }


            if (username.Contains('\\'))
            {
                username = CardUtility.GetValidEmpNo(username);
            }

            // 檢查是否有帳號密碼
            if (!string.IsNullOrWhiteSpace(username) && !string.IsNullOrWhiteSpace(password))
            {
                string userAgent = HttpContext.Request.Headers.UserAgent.ToString();
                // 檢查帳號密碼長度
                if (username.Length < MinimumAccountLength || password.Length < MinimumPasswordLength ||
                     username.Length > MaximumAccountLength || password.Length > MaximumPasswordLength)
                {
                    // 若不要記錄在AccountLockout資料表，則使用
#pragma warning disable S125 // Sections of code should not be commented out
                    // _accountBo.InsertLoginRecord(userAgent, loginTime, username,

                    //    AccountLogInOutAction.Login, AccountLogInOutResult.Fail, ipAddress, hostname);
#pragma warning restore S125 // Sections of code should not be commented out

                    // 以下會同時記錄在AccountLockout資料表與AttendanceLogInOut資料表
                    _accountBo.LogLoginFail(userAgent, loginTime, username, ipAddress, hostname);
                    return ret; // 帳號或密碼長度不足或太長
                }

                int failCount = _accountBo.GetLoginFailCount(username);

                if (failCount >= AttendanceParameters.AccountLockoutFailTimes) // 先前登入失敗達3次，15分鐘內，不允許登入
                {
                    // 鎖定時間內無論密碼正確與否仍當做失敗
                    failCount++;
                    ret = LockdownMessage(ret, failCount);
                    _accountBo.LogLoginFail(userAgent, loginTime, username, ipAddress, hostname);
                    return ret;
                }

                ApplicationUser? applicationUser = _accountBo.AuthenticateUser(username, password);
                if (applicationUser != null && !string.IsNullOrWhiteSpace(applicationUser.CName))
                {
                    ret = Ok(new { IsAuthenticated = true, UserId = username });
                    var claims = new List<Claim>
                    {
                        new Claim(ClaimTypes.Name, username)
                    };

                    await LoginAndSetCookie(claims);
                    _accountBo.LogLoginSuccess(userAgent, loginTime, ipAddress, hostname, username);
                }
                else
                {
                    failCount++;
                    ret = LockdownMessage(ret, failCount);
                    _accountBo.LogLoginFail(userAgent, loginTime, username, ipAddress, hostname);
                }
            }
            return ret;
        }

        /// <summary>
        /// 登出，在內網時無效
        /// </summary>
        /// <returns>無傳回值</returns>
        [RequireHttps]
        [Authorize]
        [HttpGet]
        [Route("api/[controller]/[action]")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> Logout()
        {
            if (!_useNegotiate) // 如果不是使用 Negotiate 認證，表示在外網，執行登出
            {
                DateTime logoutTime = DateTime.Now;
                var (ip, hostname) = CardUtility.GetIP_Hostname(Request.HttpContext);
                string userAgent = HttpContext.Request.Headers.UserAgent.ToString();
                string userId = string.Empty;

                // 如果沒有登入，則LOG紀錄User.Identity is null
                if (User.Identity == null || string.IsNullOrWhiteSpace(User.Identity.Name))
                {
                    _logger.LogInformation("Logout Info: User.Identity is null");
                    // return StatusCode(500, new { error = "驗證登入失敗，請通知系統管理者" });
                }
                else
                {
                    // 取得使用者員工編號
                    userId = User.Identity.Name;
                    if (userId.Contains('\\'))
                    {
                        userId = CardUtility.GetValidEmpNo(userId);
                    }
                }

                // 如果沒有來源IP，則LOG紀錄
                if (ip == null)
                {
                    _logger.LogInformation($"Logout Info: 員工：{userId}登出時沒有來源IP");
                    // throw new Exception($"員工：{userId}登出時沒有來源IP");
                }

                try
                {
                    // 登出，這裡會清除 Cookie
                    await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Logout failed: {ex.Message} \n {ex.StackTrace}");
                    return StatusCode(500, new { error = $"員工：{userId}登出時發生錯誤，請通知系統管理者" });
                }
                _accountBo.LogLogout(userAgent, logoutTime, ip, hostname, userId);

                return Ok();
            }
            else
            {
                _logger.LogInformation($"Logout Info: 社內網域不須登出");
                return StatusCode(400, new { error = $"社內網域不須登出" });
            }
        }
    }
}
