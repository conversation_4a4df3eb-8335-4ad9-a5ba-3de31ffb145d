﻿using Sinotech.Mis.Utilities.DataAccess.Ado;
using Microsoft.Extensions.Configuration;
using Sinotech.Mis.Extensions.Configuration;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.Utilities.DataAccess.Ado.Tests
{
    [ExcludeFromCodeCoverage]
    public class EmployeeDaoTests
    {

        private readonly EmployeeDao _employeeDao;

        public EmployeeDaoTests()
        {
            IConfiguration configuration = new ConfigurationBuilder().
                AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).Build();
            string connectionString = configuration.GetSecuredConnectionString("Employee");
            _employeeDao = new EmployeeDao(connectionString);
        }

        [Fact]
        public void EmployeeDaoTest()
        {
            int count = _employeeDao.EmployeeCount;
            Assert.True(count > 0);
        }

        [Fact]
        public void GetAboveDeputyManagerDataTableTest()
        {
            DataTable dataTable = _employeeDao.GetAboveDeputyManagerDataTable();
            Assert.NotEmpty(dataTable.Rows);
        }


        [Fact]
        public void GetAllEmployeesTest()
        {
            DataTable dt = _employeeDao.GetAllEmployees();
            Assert.NotEmpty(dt.Rows);
        }

        [Theory]
        [InlineData("0349", true)]
        [InlineData("0395", true)]
        [InlineData("0022", false)]
        [InlineData("1111", false)]
        public void GetDepartmentName_Test(string empNo, bool expected)
        {
            //Assert 
            Assert.NotNull(_employeeDao);

            string departmentName = _employeeDao.GetDepartmentName(empNo);

            // Arrange
            if (expected)
            {
                Assert.NotEmpty(departmentName);
            }
            else
            {
                Assert.Empty(departmentName);
            }

        }

        [Theory]
        [InlineData("0349", true)]
        [InlineData("0395", true)]
        [InlineData("2268", true)]
        [InlineData("2295", true)]
        [InlineData("0022", false)]
        [InlineData("1111", false)]
        public void GetDepartmentNumberTest(string empNo, bool expected)
        {
            int departmentNumber = _employeeDao.GetDepartmentNumber(empNo);
            Assert.NotEqual(0, departmentNumber);
        }


        [Theory]
        [InlineData("0349", true)]
        [InlineData("0395", true)]
        [InlineData("0022", false)]
        [InlineData("1111", false)]
        public void GetDepartmentShortName_Test(string empNo, bool expected)
        {
            //Assert 
            Assert.NotNull(_employeeDao);

            string departmentName = _employeeDao.GetDepartmentShortName(empNo);

            // Arrange
            if (expected)
            {
                Assert.NotEmpty(departmentName);
            }
            else
            {
                Assert.Empty(departmentName);
            }

        }

        [Fact]
        public void GetDepartmentsTest()
        {
            DataTable dt = _employeeDao.GetDepartments();
            Assert.NotNull(dt);
            Assert.NotEmpty(dt.Rows);
        }
        [Theory]
        [InlineData(0, false)]
        [InlineData(1, true)]
        [InlineData(2, true)]
        [InlineData(3, true)]
        [InlineData(4, true)]
        [InlineData(5, true)]
        [InlineData(6, true)]
        [InlineData(7, true)]
        [InlineData(8, true)]
        [InlineData(9, true)]
        [InlineData(10, true)]
        [InlineData(11, true)]
        [InlineData(12, true)]
        [InlineData(13, true)]
        [InlineData(14, true)]
        [InlineData(15, true)]
        [InlineData(16, true)]
        [InlineData(17, true)]
        [InlineData(18, true)]
        [InlineData(999, false)]
        public void GetEmployeeCount_Test(int departmentNumber, bool expected)
        {
            // Assert
            Assert.NotNull(_employeeDao);

            // Act
            int employeeCount = _employeeDao.GetEmployeeCount(departmentNumber);

            // Assert
            if (expected)
            {
                Assert.True(employeeCount >= 0);
            }
            else
            {
                Assert.Equal(0, employeeCount);
            }
        }

        [Theory]
        [InlineData("0349")]
        [InlineData("0395")]
        [InlineData("0022")]
        [InlineData("1111")]
        public void GetEmployeeDetail_Test(string empNo)
        {
            // Assert
            Assert.NotNull(_employeeDao);

            // Act
            DataTable employeeDetail = _employeeDao.GetEmployeeDetail(empNo);

            // Assert
            Assert.NotNull(employeeDetail);
            if (employeeDetail.Rows.Count > 0)
            {
                Assert.NotEmpty(employeeDetail.Rows);
            }
            else
            {
                Assert.Empty(employeeDetail.Rows);
            }
        }

        [Theory]
        [InlineData("0000", true)]
        [InlineData("0001", true)]
        [InlineData("0002", true)]
        [InlineData("0349", false)]
        [InlineData("0395", false)]
        [InlineData("2265", false)]
        [InlineData("2268", false)]
        [InlineData("2295", false)]
        public void GetEmployeeNameTest(string empNo, bool expect)
        {
            string name = _employeeDao.GetEmployeeName(empNo);
            bool actual = string.IsNullOrEmpty(name);
            Assert.Equal(expect, actual);
        }

        [Theory]
        [InlineData(0, true)]
        [InlineData(1, false)]
        [InlineData(2, false)]
        [InlineData(3, false)]
        [InlineData(4, false)]
        public void GetEmployees_departmentNumber_Test(int departmentNumber, bool expected)
        {
            var employees = _employeeDao.GetEmployees(departmentNumber);
            bool empty = employees.Rows.Count == 0;
            Assert.Equal(expected, empty);
        }

        [Fact]
        public void GetEmployeesTest()
        {
            var employees = _employeeDao.GetEmployees();
            Assert.NotEmpty(employees.Rows);
        }


        [Fact]
        public void GetLeftEmployeesTest()
        {
            DataTable dt = _employeeDao.GetLeftEmployees();
            Assert.NotEmpty(dt.Rows);
        }

        [Fact]
        public void GetTeamLeadersTest()
        {
            DataTable dt = _employeeDao.GetTeamLeaders();
            Assert.NotEmpty(dt.Rows);
        }

        [Theory]
        [InlineData("0000", false)]
        [InlineData("0001", false)]
        [InlineData("0002", false)]
        [InlineData("0349", false)]
        [InlineData("0395", false)]
        [InlineData("2265", false)]
        [InlineData("2268", false)]
        [InlineData("2295", false)]
        [InlineData("0391", true)]
        [InlineData("2096", true)]
        [InlineData("2273", true)]
        public void IsAboveDeputyManagerTest(string empNo, bool expect)
        {
            bool actual = _employeeDao.IsAboveDeputyManager(empNo);
            Assert.Equal(expect, actual);
        }

    }
}