﻿using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.Tests
{
    [ExcludeFromCodeCoverage]
    public class DepartmentBoTests
    {
        private readonly DepartmentBo _departmentBo;

        /// <summary>
        /// </summary>
        public DepartmentBoTests(DepartmentBo departmentBo)
        {
            _departmentBo = departmentBo ?? throw new ArgumentNullException(nameof(departmentBo));
        }

        [Fact]
        public void GetAdministrativedepartmentsTest()
        {
            List<Department> departments = _departmentBo.GetAdministrativeDepartments();
            Assert.True(departments.Count > 0, "Expected at least one administrative department.");
            Assert.True(departments.Exists(departments => departments.DeptNo == 1), "Expected administrative department with DeptNo 1 to exist.");
            Assert.True(departments.Exists(departments => departments.DeptNo == 2), "Expected administrative department with DeptNo 1 to exist.");
        }

        [Fact]
        public void GetResearchCentersTest()
        {
            List<Department> departments = _departmentBo.GetResearchCenters();
            Assert.True(departments.Count > 0, "Expected at least one administrative department.");
            Assert.False(departments.Exists(departments => departments.DeptNo == 1), "Expected administrative department with DeptNo 1 to exist.");
            Assert.False(departments.Exists(departments => departments.DeptNo == 2), "Expected administrative department with DeptNo 1 to exist.");
        }

        [Theory]
        [InlineData(1, "治理")]
        [InlineData(2, "行政處")]
        public void GetDepartmentShortNameTest(int departmentId, string expected)
        {
            string departmentShortName = _departmentBo.GetDepartmentShortName(departmentId);
            Assert.Equal(expected, departmentShortName);
        }

        [Theory]
        [InlineData(4, "0391", true)]
        [InlineData(2, "0391", false)]
        [InlineData(4, "0392", false)]
        public void IsAboveDeputyManagerTest(int deptNo, string empNo, bool expected)
        {
            bool result = _departmentBo.IsAboveDeputyManager(deptNo, empNo);
            Assert.Equal(expected, result);
        }
    }
}