﻿using Sinotech.Mis.HR.Attendance.BusinessLogic;
using Ganss.Xss;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Sinotech.Mis.Common;
using Sinotech.Mis.Extensions.Configuration;
using Sinotech.Mis.Helpers;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.DataAccess.Ado;
using Sinotech.Mis.HR.Attendance.Tests;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using Xunit;

[assembly: CollectionBehavior(DisableTestParallelization = true)]

#nullable enable
namespace Sinotech.Mis.HR.Attendance.BusinessLogic.Tests
{
    [ExcludeFromCodeCoverage]
    public class FormBoTests : IDisposable
    {

        private readonly A1CardBo _a1CardBo;
        private readonly A1CardDao _a1CardDao;
        private readonly CardBoFactory _cardBoFactory;
        private readonly IFormBo _formBo;
        private readonly IFormFlowBo _formFlowBo;
        private readonly IFormTypeBo _formTypeBo;
        private readonly ProjectBo _projectBo;
        private readonly IConfiguration Configuration;
        private readonly string ConnectionStringAttendance;

        /// <summary>Initializes a new instance of the <see cref="FormBoTests" /> class.</summary>
        /// <param name="a1CardBo"></param>
        /// <param name="cardBoFactory"></param>
        /// <param name="formBo">The Form 商業物件.</param>
        /// <param name="projectBo"></param>
        /// <param name="employeeBo"></param>
        /// <param name="sinoSignBo"></param>
        /// <param name="attendanceBo"></param>
        public FormBoTests(A1CardBo a1CardBo, CardBoFactory cardBoFactory, IFormBo formBo, ProjectBo projectBo,
                IFormTypeBo formTypeBo, IFormFlowBo formFlowBo)
        {
            _formBo = formBo;
            //_formDao = formDao;
            _cardBoFactory = cardBoFactory;
            _formTypeBo = formTypeBo;
            _formFlowBo = formFlowBo;
            Configuration = new ConfigurationBuilder().
            AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).
            Build();
            ConnectionStringAttendance = Configuration.GetSecuredConnectionString("Attendance");
            //string sql = "DELETE FROM A1Card; DELETE FROM FormFlow; DELETE FROM Form;";
            //SqlHelper.ExecuteSqlNonQuery(ConnectionStringAttendance, sql);
            _a1CardDao = new A1CardDao(ConnectionStringAttendance);
            _a1CardBo = a1CardBo;
            _projectBo = projectBo;
            TestHelper.ClearData(ConnectionStringAttendance);
        }

        private string AddA1Card()
        {
            Form form = new Form();
            form.FormUID = new Guid("*************-7777-7777-************");
            form.FormSubject = "正常工作卡-白燕菁-111年04月中旬";
            form.FormNo = "旬111000104";
            form.FormID = "A1Card";
            form.FormInfo = "111年04月中旬";
            form.EmpNo = "0349";
            form.EmpName = "白燕菁";
            form.DeptNo = 4;
            form.DeptSName = "企劃處";
            form.TeamID = null;
            form.TeamCName = null;
            form.CreatedEmpNo = "0349";
            form.CreatedName = "白燕菁";
            form.CreatedTime = DateTime.Parse("2022-07-27T07:54:48.44").ToLocalTime();
            form.FilledTime = DateTime.Parse("2022-07-27T07:53:33.913").ToLocalTime();
            form.CreatedIP = "***********";
            form.AddedSigner = "0274";
            form.StartTime = DateTime.Parse("2022-07-27T07:53:33.913").ToLocalTime();
            form.FormStatus = 1;
            form.TotalSteps = 2;
            form.CurrentStep = 0;

            form.Flows = new List<FormFlow>();
            FormFlow flow = new FormFlow();
            flow.FlowUID = new Guid("*************-4444-4444-************");
            flow.FormUID = form.FormUID;
            flow.IsNotification = true;
            flow.FlowName = "申請人部門登記桌";
            flow.RecipientEmpNo = "04";
            flow.RecipientName = "企劃處登記桌";
            flow.RecipientDeptNo = 4;
            flow.RecipientDeptSName = "企劃處";
            flow.Step = 1;
            flow.FlowStatus = 0;
            form.Flows.Add(flow);


            flow = new FormFlow();
            flow.FlowUID = new Guid("*************-5555-5555-************");
            flow.FormUID = form.FormUID;
            flow.IsNotification = false;
            flow.FlowName = "加會人員";
            flow.RecipientEmpNo = "0274";
            flow.RecipientName = "薛強";
            flow.RecipientDeptNo = 10;
            flow.RecipientDeptSName = "土研中心";
            flow.Step = 2;
            flow.FlowStatus = 0;
            form.Flows.Add(flow);

            flow = new FormFlow();
            flow.FlowUID = new Guid("*************-6666-6666-************");
            flow.FormUID = form.FormUID;
            flow.IsNotification = false;
            flow.FlowName = "申請人部門主管";
            flow.RecipientEmpNo = "L04";
            flow.RecipientName = "企劃處經理";
            flow.RecipientDeptNo = 4;
            flow.RecipientDeptSName = "企劃處";
            flow.Step = 3;
            flow.FlowStatus = 0;
            form.Flows.Add(flow);

            ICardBaseBo? cardBo = _cardBoFactory.GetCardBo("A1Card");
            Assert.NotNull(cardBo);

            A1Card a1Card = new A1Card();
            a1Card.FormUID = new Guid("*************-7777-7777-************");
            a1Card.A1_EMPNO = "0349";
            a1Card.A1_YYMM = "11104";
            a1Card.A1_NN = '2';
            a1Card.A1_SOURCE = "Attendance";
            a1Card.A1_SHEETNO = "111年04月中旬";
            a1Card.A1_AYYMMDD = "       ";
            a1Card.A1_WYYMMDD = "1110727";
            a1Card.A1_STATUS = 1;
            a1Card.AddSigners = "0274";
            a1Card.A1_WDate = DateTime.Parse("2022-07-26T23:53:33.913Z");
            a1Card.CreatedTime = DateTime.Parse("2022-07-26T23:54:48.440");
            A1CardDetail detail = new A1CardDetail();
            detail.A1_PROJNO = "TI21508";
            detail.A1_DDHH = "8007081100";
            detail.A1_SERIALNO = "01";
            detail.A1_HOUR = 25;
            a1Card.Details.Add(detail);
            detail = new A1CardDetail();
            detail.A1_PROJNO = "RP19553";
            detail.A1_DDHH = "0001007700";
            detail.A1_HOUR = 15;
            detail.A1_SERIALNO = "02";
            a1Card.Details.Add(detail);

            string errorMessage = _formBo.AddForm(form, cardBo, a1Card);
            return errorMessage;
        }


        private string AddA1Card2()
        {
            Form form = new Form();
            form.FormUID = new Guid("*************-8888-8888-************");
            form.FormSubject = "正常工作卡-林伯勳-111年04月中旬";
            form.FormNo = "旬111000104";
            form.FormID = "A1Card";
            form.FormInfo = "111年04月中旬";
            form.EmpNo = "2069";
            form.EmpName = "林伯勳";
            form.DeptNo = 16;
            form.DeptSName = "防災中心";
            form.TeamID = null;
            form.TeamCName = null;
            form.CreatedEmpNo = "2069";
            form.CreatedName = "林伯勳";
            form.CreatedTime = DateTime.Parse("2022-07-27T07:54:48.44").ToLocalTime();
            form.FilledTime = DateTime.Parse("2022-07-27T07:53:33.913").ToLocalTime();
            form.CreatedIP = "***********";
            form.AddedSigner = "";
            form.StartTime = DateTime.Parse("2022-07-27T07:53:33.913").ToLocalTime();
            form.FormStatus = 1;
            form.TotalSteps = 2;
            form.CurrentStep = 0;

            form.Flows = new List<FormFlow>();


            FormFlow flow = new FormFlow();
            flow.FlowUID = new Guid("11111111-1111-1111-1111-111111111111");
            flow.FormUID = form.FormUID;
            flow.IsNotification = false;
            flow.FlowName = "加會人員";
            flow.RecipientEmpNo = "0274";
            flow.RecipientName = "薛強";
            flow.RecipientDeptNo = 10;
            flow.RecipientDeptSName = "土研中心";
            flow.Step = 1;
            flow.FlowStatus = 0;
            form.Flows.Add(flow);

            flow = new FormFlow();
            flow.FlowUID = new Guid("*************-2222-2222-************");
            flow.FormUID = form.FormUID;
            flow.IsNotification = false;
            flow.FlowName = "申請人部門主管";
            flow.RecipientEmpNo = "L16";
            flow.RecipientName = "防災中心主任";
            flow.RecipientDeptNo = 4;
            flow.RecipientDeptSName = "防災中心";
            flow.Step = 2;
            flow.FlowStatus = 0;
            form.Flows.Add(flow);

            ICardBaseBo? cardBo = _cardBoFactory.GetCardBo("A1Card");
            Assert.NotNull(cardBo);
            A1Card card = new A1Card();
            card.FormUID = new Guid("*************-8888-8888-************");
            card.A1_EMPNO = "2069";
            card.A1_YYMM = "11104";
            card.A1_NN = '2';
            card.A1_WYYMMDD = "1110727";
            card.A1_AYYMMDD = "       ";
            card.A1_STATUS = 1;
            card.A1_SHEETNO = "111年04月中旬";
            card.A1_SOURCE = "Attendance";
            card.AddSigners = "0274";
            card.A1_WDate = DateTime.Parse("2022-07-26T23:53:33.913Z");
            card.CreatedTime = DateTime.Parse("2022-07-26T23:54:48.440");
            card.UpdatedEmpNo = "2069";
            card.UpdatedName = "林伯勳";
            card.UpdatedTime = DateTime.Parse("2022-07-26T23:54:48.440");
            card.UpdatedIP = "***********";
            card.UpdatedHost = "03-xxx.secinc";
            A1CardDetail detail = new A1CardDetail();
            detail.A1_PROJNO = "TD22504";
            detail.A1_SERIALNO = "01";
            detail.A1_DDHH = "8007081100";
            detail.A1_HOUR = 25;
            card.Details.Add(detail);
            string errorMessage = _formBo.AddForm(form, cardBo, card);
            return errorMessage;
        }

        private string AddA1CardSimple()
        {
            Form form = new Form();
            form.FormUID = new Guid("*************-2222-2222-************");
            form.FormSubject = "正常工作卡-岳美熹-111年04月中旬";
            form.FormNo = "旬111000104";
            form.FormID = "A1Card";
            form.FormInfo = "111年04月中旬";
            form.EmpNo = "2130";
            form.EmpName = "岳美熹";
            form.DeptNo = 16;
            form.DeptSName = "防災中心";
            form.TeamID = null;
            form.TeamCName = null;
            form.CreatedEmpNo = "2130";
            form.CreatedName = "岳美熹";
            form.CreatedTime = DateTime.Parse("2022-07-27T07:54:48.44").ToLocalTime();
            form.FilledTime = DateTime.Parse("2022-07-27T07:53:33.913").ToLocalTime();
            form.CreatedIP = "***********";
            form.AddedSigner = "";
            form.StartTime = DateTime.Parse("2022-07-27T07:53:33.913").ToLocalTime();
            form.FormStatus = 1;
            form.TotalSteps = 2;
            form.CurrentStep = 0;

            form.Flows = new List<FormFlow>();

            A1Card a1Card = new A1Card();

            a1Card.FormUID = new Guid("*************-2222-2222-************");
            a1Card.A1_EMPNO = "2069";
            a1Card.A1_YYMM = "11104";
            a1Card.A1_NN = '2';
            a1Card.A1_WYYMMDD = "1110727";
            a1Card.A1_AYYMMDD = "       ";
            a1Card.A1_STATUS = 1;
            a1Card.A1_SHEETNO = "111年04月中旬";
            a1Card.A1_SOURCE = "Attendance";
            a1Card.AddSigners = "0274";
            a1Card.A1_WDate = DateTime.Parse("2022-07-26T23:53:33.913Z");
            a1Card.CreatedTime = DateTime.Parse("2022-07-26T23:54:48.440");
            a1Card.UpdatedEmpNo = "2069";
            a1Card.UpdatedName = "林伯勳";
            a1Card.UpdatedTime = DateTime.Parse("2022-07-26T23:54:48.440");
            a1Card.UpdatedIP = "***********";
            a1Card.UpdatedHost = "03-xxx.secinc";
            A1CardDetail detail = new A1CardDetail();
            detail.A1_PROJNO = "TD22504";
            detail.A1_SERIALNO = "01";
            detail.A1_DDHH = "8007081100";
            detail.A1_HOUR = 25;
            a1Card.Details.Add(detail);

            ICardBaseBo? cardBo = _cardBoFactory.GetCardBo("A1Card");
            Assert.NotNull(cardBo);
            string errorMessage = _formBo.AddForm(form, cardBo, a1Card);
            return errorMessage;
        }
        private void AddB1CardApp1()
        {
            string strForm = "{\"ID\":393,\"FormUID\":\"7bea2f17-b9e0-4157-8d47-97c74bcbe21c\",\"FormID\":\"B1CardApp\",\"FormNo\":\"申111000002\",\"FormSubject\":\"加班申請卡-孫睿宏-111/08/01\",\"FormInfo\":\"111/08/01\",\"EmpNo\":\"2268\",\"EmpName\":\"孫睿宏\",\"DeptNo\":4,\"DeptSName\":\"企劃處\",\"TeamID\":null,\"TeamCName\":\"\",\"CreatedEmpNo\":\"2268\",\"CreatedName\":\"孫睿宏\",\"FilledTime\":\"2022-08-02T11:59:29.657\",\"CreatedTime\":\"2022-08-02T11:59:59.997\",\"CreatedIP\":\"***********\",\"AddedSigner\":\"\",\"StartTime\":\"2022-08-02T12:00:00.483\",\"EndTime\":null,\"FormStatus\":1,\"TotalSteps\":1,\"CurrentStep\":1,\"UpdatedEmpNo\":null,\"UpdatedTime\":null,\"UpdatedIP\":null,\"Flows\":[{\"ID\":583,\"FlowUID\":\"b0128060-40f2-4c5a-afa6-609bfaac3d2b\",\"FormUId\":\"00000000-0000-0000-0000-000000000000\",\"RecipientEmpNo\":\"L04\",\"RecipientName\":\"企劃處經理\",\"RecipientDeptNo\":4,\"RecipientDeptSName\":\"企劃處\",\"RecipientTeamID\":null,\"RecipientTeamCName\":null,\"FlowName\":\"申請人部門主管\",\"Step\":1,\"ApproverEmpNo\":null,\"ApproverName\":null,\"ApproverDeptNo\":0,\"ApproverDeptSName\":null,\"ApproverTeamID\":null,\"ApproverTeamCName\":null,\"ApproveTime\":null,\"IsAgentApprove\":false,\"FlowStatus\":1,\"IsNotification\":false}]}";
            Form? form = JsonConvert.DeserializeObject<Form>(strForm);
            Assert.NotNull(form);
            string strFlows = "[{\"ID\":583,\"FlowUID\":\"b0128060-40f2-4c5a-afa6-609bfaac3d2b\",\"FormUId\":\"00000000-0000-0000-0000-000000000000\",\"RecipientEmpNo\":\"L04\",\"RecipientName\":\"企劃處經理\",\"RecipientDeptNo\":4,\"RecipientDeptSName\":\"企劃處\",\"RecipientTeamID\":null,\"RecipientTeamCName\":null,\"FlowName\":\"申請人部門主管\",\"Step\":1,\"ApproverEmpNo\":null,\"ApproverName\":null,\"ApproverDeptNo\":0,\"ApproverDeptSName\":null,\"ApproverTeamID\":null,\"ApproverTeamCName\":null,\"ApproveTime\":null,\"IsAgentApprove\":false,\"FlowStatus\":1,\"IsNotification\":false}]";
            List<FormFlow>? flows = JsonConvert.DeserializeObject<List<FormFlow>>(strFlows);
            Assert.NotNull(flows);
            string strB1CardApp = "{\"Name\":\"B1CardApp\",\"FormUID\":\"7bea2f17-b9e0-4157-8d47-97c74bcbe21c\",\"B1_FormID\":\"申111000002\",\"B1_EmpNo\":\"2268\",\"B1_DeptNo\":4,\"B1_Date\":\"2022-08-01T00:00:00\",\"B1_Hour\":4,\"B1_Code\":\"1\",\"B1_PrjNo\":\"RP19553\",\"B1_Reason\":\"test\",\"B1_WritedEmpNo\":\"2268\",\"B1_WDate\":\"2022-08-02T11:59:29.657\",\"B1_UpdatedEmpNo\":null,\"B1_UDate\":null,\"B1_ShouldSignEmpNo\":\"    \",\"B1_ADate\":null,\"B1_Status\":\"1\",\"RequisitionID\":null,\"B1_SOURCE\":\"Attendance\",\"AddSigners\":\"\",\"CreatedTime\":\"2022-07-26T23:54:48.440\", \"UpdatedEmpNo\":\"0349\",\"UpdatedName\":\"白燕菁\",\"UpdatedTime\":\"2022-07-26T23:54:48.440\",\"UpdatedIP\":\"***********\",\"UpdatedHost\":\"03-333.secinc\", \"UpdatedEmpNo\":\"0349\",\"UpdatedName\":\"白燕菁\",\"UpdatedTime\":\"2022-07-26T23:54:48.440\",\"UpdatedIP\":\"***********\",\"UpdatedHost\":\"03-333.secinc\"}";
            B1CardApp? b1CardApp = JsonConvert.DeserializeObject<B1CardApp>(strB1CardApp);
            Assert.NotNull(b1CardApp);
            ICardBaseBo? cardBo = _cardBoFactory.GetCardBo("B1CardApp");
            Assert.NotNull(cardBo);
            string errorMessage = _formBo.AddForm(form, cardBo, b1CardApp);

            //Assert.Empty(errorMessage);
        }
        private string AddSimpleB1CardApp(DateTime date, string empNo, Guid guid)
        {
            string guidString = guid.ToString();
            Guid flowUID2 = Guid.NewGuid();
            string jsonForm = "{\"ID\":null,\"FormUID\":\"" + guidString + "\",\"FormID\":\"B1CardApp\",\"FormNo\":null,\"FormSubject\":\"加班申請卡-" + empNo + "-111/08/18\",\"FormInfo\":\"111/08/18\",\"EmpNo\":\"" + empNo + "\",\"EmpName\":\"白燕菁\",\"DeptNo\":4,\"DeptSName\":\"企劃處\",\"TeamID\":null,\"TeamCName\":null,\"CreatedEmpNo\":\"" + empNo + "\",\"CreatedName\":\"曾騰毅\",\"FilledTime\":\"2022-08-19T08:35:42.582+08:00\",\"CreatedTime\":\"2022-08-19T08:36:07.111+08:00\",\"CreatedIP\":\"***********\",\"CreatedHost\":\"03-769\",\"AddedSigner\":\"0274\",\"StartTime\":\"2022-08-19T08:36:35.8711009+08:00\",\"EndTime\":null,\"FormStatus\":1,\"TotalSteps\":2,\"CurrentStep\":0,\"UpdatedEmpNo\":null,\"UpdatedName\":null,\"UpdatedTime\":null,\"UpdatedIP\":null,\"UpdatedHost\":null,\"Flows\":["
                            + "{\"ID\":null,\"FlowUID\":\"" + flowUID2 + "\",\"FormUID\":\"" + guid + "\",\"RecipientEmpNo\":\"L04\",\"RecipientName\":\"企劃處經理\",\"RecipientDeptNo\":4,\"RecipientDeptSName\":\"企劃處\",\"RecipientTeamID\":null,\"RecipientTeamCName\":null,\"FlowName\":\"申請人部門主管\",\"Step\":1,\"ApproverEmpNo\":null,\"ApproverName\":null,\"ApproverDeptNo\":0,\"ApproverDeptSName\":null,\"ApproverTeamID\":null,\"ApproverTeamCName\":null,\"ApproverIP\":null,\"ApproverHost\":null,\"ApproveTime\":null,\"IsAgentApprove\":false,\"FlowStatus\":1,\"IsNotification\":false}]}";
            Form? form = JsonConvert.DeserializeObject<Form>(jsonForm);
            Assert.NotNull(form);
            form.FormStatus = (int)FormStatus.Processing;
            B1CardApp card = new B1CardApp();
            card.FormUID = guid;
            card.B1_EmpNo = empNo;
            card.B1_DeptNo = 4;
            card.B1_Date = date;
            card.B1_Hour = 4;
            card.B1_Code = '2';
            card.B1_PrjNo = "RP19553";
            card.B1_Reason = "test";
            card.B1_WritedEmpNo = empNo;
            card.B1_WDate = date;
            card.B1_UpdatedEmpNo = null;
            card.B1_Status = (int)FormStatus.Processing;
            card.B1_SOURCE = "Attendance";
            card.UpdatedEmpNo = empNo;
            card.UpdatedName = "曾騰毅";
            card.UpdatedTime = date;
            card.UpdatedIP = "***********";
            card.UpdatedHost = "03-769";
            card.AddSigners = "0274";
            card.CreatedTime = date;

            ICardBaseBo? cardBo = _cardBoFactory.GetCardBo("B1CardApp");
            Assert.NotNull(cardBo);
            string errorMessage = _formBo.AddForm(form, cardBo, card);
            return errorMessage;
        }

        [Fact]
        public void AddAttachmentsTest()
        {

            DataTable dtCardForms = _a1CardDao.GetSentFormA1CardYearMonth("0395", 2022, 11);
            DataRow dr = dtCardForms.NewRow();
            dr["FormUID"] = new Guid("11111111-1111-1111-1111-111111111111");
            dr["FormID"] = "A1Card";
            dr["FormNo"] = "旬111000104";
            dr["A1_SERIALNO"] = "01";
            dr["FormSubject"] = "正常工作卡-白燕菁-111年04月中旬";
            dr["FormInfo"] = "111年04月中旬";
            dr["A1_SOURCE"] = "Attendance";
            dr["A1_SHEETNO"] = "旬111000104";
            dr["EMPNO"] = "0349";
            dr["A1_EMPNO"] = "0349";
            dr["A1_YYMM"] = "11104";
            dr["A1_DDHH"] = "01000000000";
            dr["A1_ITEMNO"] = "1";
            dr["A1_WYYMMDD"] = "1110727";
            dr["A1_AYYMMDD"] = "";
            dr["A1_HOUR"] = 1;
            dr["A1_EXPNO"] = "1";
            dr["A1_NN"] = "2";
            dr["A1_STATUS"] = "1";
            dr["A1_PROJNO"] = "RP19553";
            dr["EmpName"] = "白燕菁";
            dr["DeptNo"] = 4;
            dr["DeptSName"] = "企劃處";
            dr["TeamID"] = DBNull.Value;
            dr["TeamCName"] = DBNull.Value;
            dr["CreatedEmpNo"] = "0349";
            dr["CreatedName"] = "白燕菁";
            dr["CreatedTime"] = DateTime.Parse("2022-07-27T07:54:48.44").ToLocalTime();
            dr["FilledTime"] = DateTime.Parse("2022-07-27T07:53:33.913").ToLocalTime();
            dr["CreatedIP"] = "127.0.0.1";
            dr["AddedSigner"] = "0274";
            dr["StartTime"] = DateTime.Parse("2022-07-27T07:53:33.913").ToLocalTime();
            dr["ValidFrom"] = dr["StartTime"];
            dr["ValidTo"] = dr["StartTime"];
            dr["FormStatus"] = 1;
            dr["TotalSteps"] = 2;
            dr["FormFormUID"] = new Guid("11111111-1111-1111-1111-111111111111");
            dr["CurrentStep"] = 1;
            dr["AttachmentID"] = 1;
            dr["OriginalFileName"] = "test.txt";
            dr["EncodedFileName"] = "test.txt";
            dr["FileDirectory"] = "FileDirectory";

            dtCardForms.Rows.Add(dr);
            FormCard formCard = new FormCard();
            DataRow[] drs = new DataRow[1];
            for (int i = 0; i < dtCardForms.Rows.Count; i++)
            {
                drs[i] = dtCardForms.Rows[i];
            }
            _formBo.AddAttachments(dtCardForms, formCard, drs);

        }

        [Fact]
        public void AddFlowByProjectTest()
        {
            Form form = new Form();
            form.EmpNo = "0349";
            form.DeptNo = 4;
            form.TotalSteps = 0;
            List<FormFlow> flows = new List<FormFlow>();
            //ProjectDto project = _project
            form.Flows = flows;
            Project project = _projectBo.GetProject("9652K");
            int count = _formBo.AddFlowByProject(form, project);
            Assert.Equal(1, count);

            Assert.Equal(1, form.TotalSteps);
            Assert.Single(form.Flows);

            project = _projectBo.GetProject("MP05003");
            count = _formBo.AddFlowByProject(form, project);
            Assert.Equal(0, count);

            project = _projectBo.GetProject("MI05003");
            count = _formBo.AddFlowByProject(form, project);
            Assert.Equal(1, count);
            Assert.Equal(2, form.TotalSteps);
            Assert.Equal(2, flows.Count);
        }

        [Fact]
        public void AddFormTest()
        {
            Form form = new Form();
            form.FormUID = new Guid("*************-6666-6666-************");
            form.FormSubject = "正常工作卡-白燕菁-111年04月中旬";
            form.FormNo = "旬111000104";
            form.FormID = "A1Card";
            form.FormInfo = "111年04月中旬";
            form.EmpNo = "0349";
            form.EmpName = "白燕菁";
            form.DeptNo = 4;
            form.DeptSName = "企劃處";
            form.TeamID = null;
            form.TeamCName = null;
            form.CreatedEmpNo = "0349";
            form.CreatedName = "白燕菁";
            form.CreatedTime = DateTime.Parse("2022-07-27T07:54:48.44").ToLocalTime();
            form.FilledTime = DateTime.Parse("2022-07-27T07:53:33.913").ToLocalTime();
            form.CreatedIP = "***********";
            form.AddedSigner = "0274";
            form.StartTime = DateTime.Parse("2022-07-27T07:53:33.913").ToLocalTime();
            form.FormStatus = 1;
            form.TotalSteps = 2;
            form.CurrentStep = 0;
            form.Flows = new List<FormFlow>();

            FormFlow flow = new FormFlow();
            flow.FlowUID = new Guid("11111111-1111-1111-1111-111111111111");
            flow.FormUID = form.FormUID;
            flow.IsNotification = true;
            flow.FlowName = "申請人部門登記桌";
            flow.RecipientEmpNo = "04";
            flow.RecipientName = "企劃處登記桌";
            flow.RecipientDeptNo = 4;
            flow.RecipientDeptSName = "企劃處";
            flow.Step = 1;
            flow.FlowStatus = 0;
            form.Flows.Add(flow);


            flow = new FormFlow();
            flow.FlowUID = new Guid("*************-2222-2222-************");
            flow.FormUID = form.FormUID;
            flow.IsNotification = true;
            flow.FlowName = "代理人";
            flow.RecipientEmpNo = "0305";
            flow.RecipientName = "蔣明峰";
            flow.RecipientDeptNo = 4;
            flow.RecipientDeptSName = "企劃處";
            flow.Step = 2;
            flow.FlowStatus = 0;
            form.Flows.Add(flow);

            flow = new FormFlow();
            flow.FlowUID = new Guid("*************-3333-3333-************");
            flow.FormUID = form.FormUID;
            flow.IsNotification = false;
            flow.FlowName = "加會人員";
            flow.RecipientEmpNo = "0274";
            flow.RecipientName = "薛強";
            flow.RecipientDeptNo = 10;
            flow.RecipientDeptSName = "土研中心";
            flow.Step = 3;
            flow.FlowStatus = 0;
            form.Flows.Add(flow);

            flow = new FormFlow();
            flow.FlowUID = new Guid("*************-4444-4444-************");
            flow.FormUID = form.FormUID;
            flow.IsNotification = true;
            flow.FlowName = "申請人部門主管";
            flow.RecipientEmpNo = "L04";
            flow.RecipientName = "企劃處經理";
            flow.RecipientDeptNo = 4;
            flow.RecipientDeptSName = "企劃處";
            flow.Step = 4;
            flow.FlowStatus = 0;
            form.Flows.Add(flow);

            ICardBaseBo? cardBo = _cardBoFactory.GetCardBo("A1Card");
            Assert.NotNull(cardBo);
            A1Card card = new A1Card();
            card.FormUID = new Guid("*************-6666-6666-************");
            card.A1_EMPNO = "0349";
            card.A1_YYMM = "11104";
            card.A1_NN = '2';
            card.A1_SOURCE = "Attendance";
            card.A1_SHEETNO = "111年04月中旬";
            card.A1_WYYMMDD = "1110727";
            card.A1_AYYMMDD = "       ";
            card.AddSigners = "0274";
            card.A1_STATUS = 1;
            card.A1_WDate = DateTime.Parse("2022-07-26T23:53:33.913");
            card.CreatedTime = DateTime.Parse("2022-07-26T23:54:48.440");
            A1CardDetail detail = new A1CardDetail();
            detail.A1_PROJNO = "TI21508";
            detail.A1_DDHH = "8007081100";
            detail.A1_SERIALNO = "01";
            detail.A1_HOUR = 25;
            card.Details.Add(detail);
            detail = new A1CardDetail();
            detail.A1_PROJNO = "RP19553";
            detail.A1_DDHH = "0001007700";
            detail.A1_HOUR = 15;
            detail.A1_SERIALNO = "02";
            card.Details.Add(detail);

            string errorMessage = _formBo.AddForm(form, cardBo, card);
            Assert.Empty(errorMessage);
        }

        [Fact]
        public void AddFormTest_Invalid()
        {
            Form form = null;
            ICardBaseBo cardBo = null;
            CardBase card = null;
            string result = _formBo.AddForm(form, cardBo, card);
            Assert.Equal("輸入資料不全", result);
        }

        [Theory]
        [InlineData("0349", "0349 白燕菁")]
        [InlineData("0395,0349", "0395 曾騰毅,0349 白燕菁")]
        public void AddSignersAddNameTest(string input, string expected)
        {
#nullable enable
            string? result = _formBo.AddSignersAddName(input);
            Assert.Equal(expected, result);
        }

        [Fact]
        public void ApproveInvalid_Test()
        {
            Approve approve = new Approve();
            approve.FormID = "Not Valid";
            approve.FormUID = Guid.Empty;
            approve.IsAgentApprove = false;
            approve.FlowStatus = (int)FlowStatus.Agree;
            approve.ApproverTeamID = null;
            approve.ApproveTime = DateTime.Now;
            approve.ApproverTeamCName = null;
            ICardBaseBo? cardBo = _cardBoFactory.GetCardBo("A1Card");
            Assert.NotNull(cardBo);
            approve.FlowUID = Guid.Empty;
            approve.ApproverEmpNo = "XXXX";
            approve.ApproverName = "薛強";
            approve.ApproverDeptNo = 10;
            approve.ApproverDeptSName = "土研中心";
            approve.Comment = "<strong>有條件同意</strong>";
            string errorMessage = _formBo.Approve(approve, cardBo, approve.ApproverEmpNo);
            Assert.NotEmpty(errorMessage);
            Assert.Equal("員工資料不存在", errorMessage);
            approve.ApproverEmpNo = "0274";
            errorMessage = _formBo.Approve(approve, cardBo, approve.ApproverEmpNo);
            Assert.NotEmpty(errorMessage);
        }

        [Fact]
        public async Task ApproveTest()
        {
            Approve? approveDto = null;
            ICardBaseBo? cardBo = null;
            string userId = string.Empty;
            string errorMessage = _formBo.Approve(approveDto, cardBo, userId);
            Assert.NotEmpty(errorMessage);

            // 測試A1Card
            A1Card a1Card = new A1Card();
            a1Card.FormUID = Guid.Empty;
            a1Card.A1_EMPNO = "2092";
            a1Card.A1_YYMM = "11103";
            a1Card.A1_NN = '2';
            a1Card.A1_SOURCE = "Attendance";
            a1Card.A1_WYYMMDD = "1110727";
            a1Card.AddSigners = "2016";
            a1Card.A1_STATUS = 0;
            a1Card.A1_WDate = DateTime.Parse("2022-07-26T23:53:33.913");
            a1Card.FilledTime = DateTime.Parse("2022-07-26T23:52:48.440");
            a1Card.CreatedTime = DateTime.Parse("2022-07-26T23:54:48.440");
            A1CardDetail detail = new A1CardDetail();
            detail.A1_PROJNO = "TI21508";
            detail.A1_DDHH = "8007081100";
            detail.A1_SERIALNO = "01";
            detail.A1_HOUR = 25;
            a1Card.Details.Add(detail);
            detail = new A1CardDetail();
            detail.A1_PROJNO = "RP19553";
            detail.A1_DDHH = "0001007700";
            detail.A1_HOUR = 15;
            detail.A1_SERIALNO = "02";
            a1Card.Details.Add(detail);
            string creatorId = "2092";
            string ipAddress = "***********";
            string hostname = "03-769.secinc";

            Guid? guid;
            (guid, errorMessage) = await _a1CardBo.AddA1Card(creatorId, a1Card, ipAddress, hostname);
            Assert.Empty(errorMessage);
            Assert.NotEqual(Guid.Empty, guid);
            List<RoleInbox> inbox = _formBo.GetInbox("2016", _cardBoFactory);
            Assert.NotNull(inbox);
            Assert.Single(inbox);
            Assert.Single(inbox[0].Inboxes);

            Approve approve = new Approve();
            approve.FormID = inbox[0].Inboxes[0].FormID;
            approve.FormUID = inbox[0].Inboxes[0].FormUID;
            approve.IsAgentApprove = false;
            approve.FlowStatus = (int)FlowStatus.Agree;
            approve.ApproverTeamID = null;
            approve.ApproveTime = DateTime.Now;
            approve.ApproverTeamCName = null;
            cardBo = _cardBoFactory.GetCardBo(inbox[0].Inboxes[0].FormID);

            approve.FlowUID = inbox[0].Inboxes[0].FlowUID;
            approve.ApproverEmpNo = "0391";
            approve.ApproverName = "陳正忠";
            approve.ApproverDeptNo = 4;
            approve.ApproverDeptSName = "企劃處";
            Assert.NotNull(cardBo);
            errorMessage = _formBo.Approve(approve, cardBo, "0391");
            string expected = "無簽核權限";
            Assert.Equal(expected, errorMessage);
            approve.ApproverEmpNo = "2016";
            approve.ApproverName = "石豐銘";
            approve.ApproverDeptNo = 17;
            approve.ApproverDeptSName = "防震中心";
            errorMessage = _formBo.Approve(approve, cardBo, "2016");
            Assert.Empty(errorMessage);

            // 加一筆 B1CardApp
            AddB1CardApp1();
            inbox = _formBo.GetInbox("0391", _cardBoFactory);
            Assert.NotNull(inbox);
            Assert.Equal(2, inbox.Count);
            Assert.Empty(inbox[0].Inboxes);
            Assert.Single(inbox[1].Inboxes);


            approve.FormID = inbox[1].Inboxes[0].FormID;
            approve.FormUID = inbox[1].Inboxes[0].FormUID;
            approve.FlowUID = inbox[1].Inboxes[0].FlowUID;
            approve.ApproverEmpNo = "2016";
            approve.ApproverName = "石豐銘";
            approve.ApproverDeptNo = 17;
            approve.ApproverDeptSName = "防震中心";
            cardBo = _cardBoFactory.GetCardBo(inbox[1].Inboxes[0].FormID);
            errorMessage = _formBo.Approve(approve, cardBo, "2016");
            Assert.NotEmpty(errorMessage);

            Assert.NotNull(inbox);
            Assert.Equal(2, inbox.Count);
            Assert.Empty(inbox[0].Inboxes);
            Assert.Single(inbox[1].Inboxes);

            approve.ApproverEmpNo = "0391";
            approve.ApproverName = "陳正忠";
            approve.ApproverDeptNo = 4;
            approve.ApproverDeptSName = "企劃處";
            errorMessage = _formBo.Approve(approve, cardBo, "0391");
            Assert.Empty(errorMessage);

            approve.ApproverEmpNo = "0494";
            approve.ApproverName = "賴建志";

            errorMessage = _formBo.Approve(approve, cardBo, "0494");
            Assert.NotEmpty(errorMessage);

            approve.ApproverEmpNo = "0395";
            approve.ApproverName = "曾騰毅";
            errorMessage = _formBo.Approve(approve, cardBo, "0395");
            Assert.NotEmpty(errorMessage);

            AddA1Card();
            inbox = _formBo.GetInbox("0391", _cardBoFactory);
            approve.FormID = "A1Card";
            approve.FormUID = new Guid("*************-7777-7777-************");
            approve.FlowUID = new Guid("*************-6666-6666-************");
            approve.ApproverEmpNo = "0391";
            approve.ApproverName = "陳正忠";
            errorMessage = _formBo.Approve(approve, cardBo, "0391");
            Assert.NotEmpty(errorMessage); //前一關還沒簽核，所以不能簽
            Assert.Equal(2, inbox.Count);
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("   ")]
        public void CannotCallCanSeeApprovalFormCardWithInvalidEmpNo(string empNo)
        {
            Guid guid = Guid.NewGuid();
            DateTime date = new DateTime(2023, 1, 2);
            bool result = _formBo.CanSeeApprovalFormCard(guid, empNo);
            Assert.False(result);
        }

        [Theory]
        [InlineData(2023, 3, 8, "0349", "0391")]
        [InlineData(2023, 1, 1, "0395", "0391")]
        public void CanSeeApprovalFormCard(int year, int month, int day, string empNo, string manager)
        {
            // Arrange
            DateTime date = new DateTime(year, month, day);
            Guid guid = Guid.NewGuid();
            string err = AddSimpleB1CardApp(date, empNo, guid);

            // Act
            bool result = _formBo.CanSeeApprovalFormCard(guid, manager);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void CanSeeApprovalFormCard_EmptyGuid_Test()
        {
            string empNo = "3333";
            bool result = _formBo.CanSeeApprovalFormCard(Guid.Empty, empNo);
            Assert.False(result);
        }

        [Fact]
        public void CanSeeInboxTest()
        {
            bool canSee = _formBo.CanSeeInbox("0395", "0395");
            Assert.True(canSee);

            canSee = _formBo.CanSeeInbox("0349", "0349");
            Assert.True(canSee);

            canSee = _formBo.CanSeeInbox("0395", "0349");
            Assert.True(canSee);

            canSee = _formBo.CanSeeInbox("2025", "0276");
            Assert.False(canSee);
        }

        [Fact]
        public void CanSeeNotifyFormCardTest()
        {
            bool expacted = false;
            Guid formUID = Guid.NewGuid();
            int notifyId = 1;
            string logonUserId = "0395";
            string userId = "0395";
            bool actual = _formBo.CanSeeNotifyFormCard(formUID, notifyId, logonUserId, userId);
            Assert.Equal(expacted, actual);
        }

        [Theory]
        [InlineData("0395", true)]
        [InlineData("0349", true)]
        [InlineData("0741", true)]
        [InlineData("0391", true)]
        [InlineData("0494", false)]
        public void CanSeeSentFormCardTest(string empNo, bool expected)
        {

            AddA1Card();
            Guid formUID = new Guid("*************-7777-7777-************");
            bool canSee = _formBo.CanSeeSentFormCard(formUID, empNo);
            Assert.Equal(expected, canSee);
        }

        [Theory]
        [InlineData("0494", false)]
        [InlineData("0349", true)]
        [InlineData("0391", false)]
        [InlineData("0395", true)]
        [InlineData("0741", false)]
        [InlineData("0931", false)]
        [InlineData("2092", false)]
        [InlineData("2096", false)]
        [InlineData("2016", true)]
        public async Task CanSeeSignedFormCardTest(string empNo, bool expected)
        {
            // 測試A1Card
            A1Card a1Card = new A1Card();
            a1Card.A1_EMPNO = "2092";
            a1Card.A1_YYMM = "11103";
            a1Card.A1_NN = '2';
            a1Card.A1_SOURCE = "Attendance";
            a1Card.A1_WYYMMDD = "1110727";
            a1Card.AddSigners = "2016";
            a1Card.A1_STATUS = 0;
            a1Card.A1_WDate = DateTime.Parse("2022-07-26T23:53:33.913");
            a1Card.FilledTime = DateTime.Parse("2022-07-26T23:52:48.440");
            a1Card.CreatedTime = //DateTime.Parse("2022-07-26T23:54:48.440");
                DateTime.Now;
            A1CardDetail detail = new A1CardDetail();
            detail.A1_PROJNO = "TI21508";
            detail.A1_DDHH = "8007081100";
            detail.A1_SERIALNO = "01";
            detail.A1_HOUR = 25;
            a1Card.Details.Add(detail);
            detail = new A1CardDetail();
            detail.A1_PROJNO = "RP19553";
            detail.A1_DDHH = "0001007700";
            detail.A1_HOUR = 15;
            detail.A1_SERIALNO = "02";
            a1Card.Details.Add(detail);
            string creatorId = "2092";
            string ipAddress = "***********";
            string hostname = "03-769.secinc";

            Guid? guid;
            string errorMessage;
            (guid, errorMessage) = await _a1CardBo.AddA1Card(creatorId, a1Card, ipAddress, hostname);
            Assert.Empty(errorMessage);
            Assert.NotNull(guid);
            a1Card.FormUID = (Guid)guid;
            Assert.NotEqual(Guid.Empty, guid);
            List<RoleInbox> inbox = _formBo.GetInbox("2016", _cardBoFactory);
            Assert.NotNull(inbox);
            Assert.Single(inbox);
            Assert.Single(inbox[0].Inboxes);

            Approve approve = new Approve();
            approve.FormID = inbox[0].Inboxes[0].FormID;
            approve.FormUID = a1Card.FormUID;
            approve.IsAgentApprove = false;
            approve.FlowStatus = (int)FlowStatus.Agree;
            approve.ApproverTeamID = null;
            approve.ApproveTime = DateTime.Now;
            approve.ApproverTeamCName = null;
            ICardBaseBo? cardBo = _cardBoFactory.GetCardBo(inbox[0].Inboxes[0].FormID);

            approve.FlowUID = inbox[0].Inboxes[0].FlowUID;
            approve.ApproverEmpNo = "0391";
            approve.ApproverName = "陳正忠";
            approve.ApproverDeptNo = 4;
            approve.ApproverDeptSName = "企劃處";
            Assert.NotNull(cardBo);
            errorMessage = _formBo.Approve(approve, cardBo, "0391");

            string expectedError = "無簽核權限";
            Assert.Equal(expectedError, errorMessage);
            approve.ApproverEmpNo = "2016";
            approve.ApproverName = "石豐銘";
            approve.ApproverDeptNo = 17;
            approve.ApproverDeptSName = "防震中心";
            errorMessage = _formBo.Approve(approve, cardBo, "2016");
            Assert.Empty(errorMessage);
            bool canSee = _formBo.CanSeeSignedFormCard(a1Card.FormUID, empNo);
            Assert.Equal(expected, canSee);

        }

        public void Dispose()
        {
            // ClearData();
        }

        [Theory]
        [InlineData("申113000473", 1, "20240203_加班事由.PNG", "申113000473-1.png")]
        [InlineData("申113000473", 3, "20240203_加班事由x.png", "申113000473-3.png")]
        [InlineData("加113000520", 9, "1130723抗癌蔬菜-青花菜.pDf", "加113000520-9.pdf")]
        [InlineData("假113001618", 37, "where not good.docx", "假113001618-37.docx")]
        public void EncodeAttachmentNameTest(string formNo, int serial, string fileName, string expect)
        {
            string encodedFileName = _formBo.EncodeAttachmentName(formNo, serial, fileName);
            Assert.Equal(expect, encodedFileName);
        }

        [Fact]
        public void FlowDedupContinuousTest()
        {
            List<FormFlow> flows = new List<FormFlow>();

            FormFlow flow = new FormFlow();
            flow.RecipientEmpNo = "0349";
            flow.FlowName = "1";
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "0349";
            flow.FlowName = "2";
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "L04";
            flow.FlowName = "3";
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "0349";
            flow.FlowName = "4";
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "L04";
            flow.FlowName = "5";
            flows.Add(flow);

            List<FormFlow> flows2 = _formFlowBo.FlowDedupContinuous(flows);
            Assert.Equal(4, flows2.Count);
            Assert.Equal("1", flows2[0].FlowName);
            Assert.Equal("3", flows2[1].FlowName);
            Assert.Equal("4", flows2[2].FlowName);
            Assert.Equal("5", flows2[3].FlowName);

            //再一輪測試

            flows = new List<FormFlow>();
            flow = new FormFlow();
            flow.RecipientEmpNo = "0349";
            flow.FlowName = "1";
            flow.IsNotification = true; //通知不能算重覆
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "0349";
            flow.FlowName = "2";
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "L04";
            flow.FlowName = "3";
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "0349";
            flow.FlowName = "4";
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "0349";
            flow.FlowName = "5";

            flows.Add(flow);
            flow = new FormFlow();
            flow.RecipientEmpNo = "L04";
            flow.FlowName = "6";
            flows.Add(flow);

            List<FormFlow> flows3 = _formFlowBo.FlowDedupContinuous(flows);
            Assert.Equal(5, flows3.Count);
            Assert.Equal("1", flows3[0].FlowName);
            Assert.Equal("2", flows3[1].FlowName);
            Assert.Equal("3", flows3[2].FlowName);
            Assert.Equal("4", flows3[3].FlowName);
            Assert.Equal("6", flows3[4].FlowName);

            //再一輪測試

            flows = new List<FormFlow>();
            flow = new FormFlow();
            flow.RecipientEmpNo = "0349";
            flow.FlowName = "1";
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "0349";
            flow.FlowName = "2";
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "L04";
            flow.FlowName = "3";
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "0349";
            flow.FlowName = "4";
            flow.IsNotification = true; //通知不能算重覆
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "0349";
            flow.FlowName = "5";

            flows.Add(flow);
            flow = new FormFlow();
            flow.RecipientEmpNo = "L04";
            flow.FlowName = "6";
            flows.Add(flow);

            List<FormFlow> flows4 = _formFlowBo.FlowDedupContinuous(flows);
            Assert.Equal(5, flows4.Count);
            Assert.Equal("1", flows4[0].FlowName);
            Assert.Equal("3", flows4[1].FlowName);
            Assert.Equal("4", flows4[2].FlowName);
            Assert.Equal("5", flows4[3].FlowName);
            Assert.Equal("6", flows4[4].FlowName);

            //再一輪測試

            flows = new List<FormFlow>();
            flow = new FormFlow();
            flow.RecipientEmpNo = "0349";
            flow.FlowName = "1";
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "0349"; //重覆，刪
            flow.FlowName = "2";
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "0349"; //重覆，刪
            flow.FlowName = "3";
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "L04";
            flow.FlowName = "4";
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "L04"; //重覆，刪
            flow.FlowName = "5";
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "0349";
            flow.FlowName = "6";
            flow.IsNotification = true; //通知不能算重覆
            flows.Add(flow);
            flow = new FormFlow();
            flow.RecipientEmpNo = "0349";
            flow.FlowName = "7";
            flow.IsNotification = true; //重覆通知，刪
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "0349";
            flow.FlowName = "8";

            flows.Add(flow);
            flow = new FormFlow();
            flow.RecipientEmpNo = "L04";
            flow.FlowName = "9";
            flows.Add(flow);

            List<FormFlow> flows5 = _formFlowBo.FlowDedupContinuous(flows);
            Assert.Equal(5, flows5.Count);
            Assert.Equal("1", flows5[0].FlowName);
            Assert.Equal("4", flows5[1].FlowName);
            Assert.Equal("6", flows5[2].FlowName);
            Assert.Equal("8", flows5[3].FlowName);
            Assert.Equal("9", flows5[4].FlowName);
        }

        [Fact]
        public void FlowDedupTest()
        {
            List<FormFlow> flows = new List<FormFlow>();

            FormFlow flow = new FormFlow();
            flow.RecipientEmpNo = "0349";
            flow.FlowName = "1";
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "0349";
            flow.FlowName = "2";
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "L04";
            flow.FlowName = "3";
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "0349";
            flow.FlowName = "4";
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "L04";
            flow.FlowName = "5";
            flows.Add(flow);

            List<FormFlow> flows2 = _formFlowBo.FlowDedup(flows);
            Assert.Equal(2, flows2.Count);
            Assert.Equal("1", flows2[0].FlowName);
            Assert.Equal("3", flows2[1].FlowName);

            //再一輪測試

            flows = new List<FormFlow>();
            flow = new FormFlow();
            flow.RecipientEmpNo = "0349";
            flow.FlowName = "1";
            flow.IsNotification = true; //通知不能算重覆
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "0349";
            flow.FlowName = "2";
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "L04";
            flow.FlowName = "3";
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "0349";
            flow.FlowName = "4";
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "0349";
            flow.FlowName = "5";

            flows.Add(flow);
            flow = new FormFlow();
            flow.RecipientEmpNo = "L04";
            flow.FlowName = "6";
            flows.Add(flow);

            List<FormFlow> flows3 = _formFlowBo.FlowDedup(flows);
            Assert.Equal(3, flows3.Count);
            Assert.Equal("1", flows3[0].FlowName);
            Assert.Equal("2", flows3[1].FlowName);
            Assert.Equal("3", flows3[2].FlowName);

            //再一輪測試

            flows = new List<FormFlow>();
            flow = new FormFlow();
            flow.RecipientEmpNo = "0349";
            flow.FlowName = "1";
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "0349";
            flow.FlowName = "2";
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "L04";
            flow.FlowName = "3";
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "0349";
            flow.FlowName = "4";
            flow.IsNotification = true; //通知不能算重覆
            flows.Add(flow);

            flow = new FormFlow();
            flow.RecipientEmpNo = "0349";
            flow.FlowName = "5";

            flows.Add(flow);
            flow = new FormFlow();
            flow.RecipientEmpNo = "L04";
            flow.FlowName = "6";
            flows.Add(flow);

            List<FormFlow> flows4 = _formFlowBo.FlowDedup(flows);
            Assert.Equal(3, flows4.Count);
            Assert.Equal("1", flows4[0].FlowName);
            Assert.Equal("3", flows4[1].FlowName);
            Assert.Equal("4", flows4[2].FlowName);
        }

        [Fact]
        public void GenerateFlowDtoTest()
        {
            Form form = new Form();
            form.EmpNo = "0349";
            form.DeptNo = 4;
            form.TotalSteps = 0;

            FormFlow flow = _formFlowBo.GenerateFlowDto(form, "L04", "申請人部門主管", 4, "企劃處", "企劃處經理", false);
            Assert.Equal(4, flow.RecipientDeptNo);
            Assert.Equal("L04", flow.RecipientEmpNo);
            Assert.Equal("申請人部門主管", flow.FlowName);
            Assert.Equal("企劃處", flow.RecipientDeptSName);
            Assert.False(flow.IsNotification);
            Assert.Empty(flow.ApproverEmpNo);
        }

        [Theory]
        [InlineData(4, 2022, 1, 1, 2022, 8, 24, false, true)]
        [InlineData(16, 2022, 1, 1, 2022, 8, 24, false, false)]
        [InlineData(8, 2022, 1, 1, 2022, 8, 24, false, false)]
        [InlineData(0, 2022, 1, 1, 2022, 8, 24, true, false)]
        public void GetDepartmentSentBoxByContentDateTest(int deptNo,
            int startYear, int startMonth, int startDay,
            int endYear, int endMonth, int endDay,
            bool isEmpty, bool isSingle)
        {
            AddA1Card2();
            AddB1CardApp1();

            DateTime startDate = new DateTime(startYear, startMonth, startDay);
            DateTime endDate = new DateTime(endYear, endMonth, endDay);
            List<SentBox> sentBoxes = _formBo.GetDepartmentSentBoxByContentDate(deptNo, startDate, endDate);
            if (isSingle)
            {
                Assert.Single(sentBoxes);
            }
            if (isEmpty)
            {
                Assert.Empty(sentBoxes);
            }
        }

        [Fact]
        public void GetDepartmentSentBoxJson_EmpNo_Test()
        {
            string str = _formBo.GetDepartmentSentBoxJson("0395");
            Assert.NotEmpty(str);
        }

        [Theory]
        [InlineData(4, "0741", 2022, 1, 1, 2022, 8, 24, false, true)]
        [InlineData(16, "0349", 2022, 1, 1, 2022, 8, 24, true, false)]
        [InlineData(4, "0391", 2022, 1, 1, 2022, 8, 24, false, true)]
        [InlineData(0, "0391", 2022, 1, 1, 2022, 8, 24, false, false)]
        public void GetDepartmentSentBoxJsonTest(int deptNo, string empNo,
            int startYear, int startMonth, int startDay,
            int endYear, int endMonth, int endDay,
            bool isEmpty, bool isSingle)
        {
            AddA1Card2();
            AddB1CardApp1();

            DateTime startDate = new DateTime(startYear, startMonth, startDay, 0, 0, 0, DateTimeKind.Local);
            DateTime endDate = new DateTime(endYear, endMonth, endDay, 0, 0, 0, DateTimeKind.Local);
            GetFormCardsRequest request = new GetFormCardsRequest();
            request.EmpNo = empNo;
            request.DeptNo = deptNo;
            request.StartDate = startDate;
            request.EndDate = endDate;
            string json = _formBo.GetDepartmentSentBoxJson(request);
            List<SentBox>? sentBoxes = JsonConvert.DeserializeObject<List<SentBox>>(json);
            Assert.NotNull(sentBoxes);
            if (isSingle)
            {
                Assert.Single(sentBoxes);
            }
            if (isEmpty)
            {
                Assert.Empty(sentBoxes);
            }

        }

        [Fact]
        public void GetDepartmentSentBoxTest()
        {
            AddA1Card2();
            AddB1CardApp1();
            DateTime startDate = new DateTime(2022, 4, 1, 0, 0, 0, DateTimeKind.Local);
            DateTime endDate = new DateTime(2022, 8, 30, 0, 0, 0, DateTimeKind.Local);
            GetFormCardsRequest request = new GetFormCardsRequest();
            request.EmpNo = "0349";
            request.DeptNo = 4;
            request.StartDate = startDate;
            request.EndDate = endDate;
            List<SentBox>? sentBoxes = _formBo.GetDepartmentSentBox(request);
            Assert.NotEmpty(sentBoxes);
            Assert.Single(sentBoxes);
            request.DeptNo = 16;
            sentBoxes = _formBo.GetDepartmentSentBox(request);
            Assert.Single(sentBoxes);
            request.DeptNo = 2;
            sentBoxes = _formBo.GetDepartmentSentBox(request);
            Assert.Empty(sentBoxes);
            AddA1Card();
            request.DeptNo = 4;
            sentBoxes = _formBo.GetDepartmentSentBox(request);
            Assert.Equal(2, sentBoxes.Count);
            request.EmpNo = "0741";
            string json = _formBo.GetDepartmentSentBoxJson(request);
            sentBoxes = JsonConvert.DeserializeObject<List<SentBox>>(json);
            Assert.NotNull(sentBoxes);
            Assert.NotEmpty(sentBoxes);
            Assert.Equal(2, sentBoxes.Count);
        }

        [Fact]
        public void GetDepartmentSentBoxTest1()
        {
            AddA1Card2();
            AddB1CardApp1();
            int deptNo = 4;
            List<SentBox> sentBoxes = _formBo.GetDepartmentSentBox(deptNo);
            Assert.Empty(sentBoxes);
        }

        [Fact]
        public void GetFormFlowsTest()
        {
            Guid guid = new Guid("*************-6666-6666-************");
            List<FormFlow> flows = _formFlowBo.GetFormFlows(guid);
            Assert.Empty(flows);

            Form form = new Form();
            form.FormUID = new Guid("*************-6666-6666-************");
            form.FormSubject = "正常工作卡-白燕菁-111年04月中旬";
            form.FormNo = "旬111000104";
            form.FormID = "A1Card";
            form.FormInfo = "111年04月中旬";
            form.EmpNo = "0349";
            form.EmpName = "白燕菁";
            form.DeptNo = 4;
            form.DeptSName = "企劃處";
            form.TeamID = null;
            form.TeamCName = null;
            form.CreatedEmpNo = "0349";
            form.CreatedName = "白燕菁";
            form.CreatedTime = DateTime.Parse("2022-07-27T07:54:48.44").ToLocalTime();
            form.FilledTime = DateTime.Parse("2022-07-27T07:53:33.913").ToLocalTime();
            form.CreatedIP = "***********";
            form.AddedSigner = "0274";
            form.StartTime = DateTime.Parse("2022-07-27T07:53:33.913").ToLocalTime();
            form.FormStatus = 1;
            form.TotalSteps = 2;
            form.CurrentStep = 0;
            form.Flows = new List<FormFlow>();
            FormFlow flow = new FormFlow();
            flow.FlowUID = new Guid("11111111-1111-1111-1111-111111111111");
            flow.FormUID = form.FormUID;
            flow.IsNotification = true;
            flow.FlowName = "申請人部門登記桌";
            flow.RecipientEmpNo = "04";
            flow.RecipientName = "企劃處登記桌";
            flow.RecipientDeptNo = 4;
            flow.RecipientDeptSName = "企劃處";
            flow.Step = 1;
            flow.FlowStatus = 0;
            form.Flows.Add(flow);
            flow = new FormFlow();
            flow.FlowUID = new Guid("*************-2222-2222-************");
            flow.FormUID = form.FormUID;
            flow.IsNotification = true;
            flow.FlowName = "申請人部門主管";
            flow.RecipientEmpNo = "L04";
            flow.RecipientName = "企劃處經理";
            flow.RecipientDeptNo = 4;
            flow.RecipientDeptSName = "企劃處";
            flow.Step = 2;
            flow.FlowStatus = 0;
            form.Flows.Add(flow);

            ICardBaseBo cardBo = _cardBoFactory.GetCardBo("A1Card");

            A1Card card = new A1Card();
            card.FormUID = new Guid("*************-6666-6666-************");
            card.A1_EMPNO = "0349";
            card.A1_YYMM = "11104";
            card.A1_NN = '2';
            card.A1_SOURCE = "Attendance";
            card.A1_SHEETNO = "111年04月中旬";
            card.A1_AYYMMDD = "       ";
            card.A1_WYYMMDD = "1110727";
            card.A1_STATUS = 1;
            card.AddSigners = "0274";
            card.A1_WDate = DateTime.Parse("2022-07-26T23:53:33.913Z");
            card.CreatedTime = DateTime.Parse("2022-07-26T23:54:48.440");
            A1CardDetail detail = new A1CardDetail();
            detail.A1_PROJNO = "TI21508";
            detail.A1_DDHH = "8007081100";
            detail.A1_SERIALNO = "01";
            detail.A1_HOUR = 25;
            card.Details.Add(detail);
            detail = new A1CardDetail();
            detail.A1_PROJNO = "RP19553";
            detail.A1_DDHH = "0001007700";
            detail.A1_HOUR = 15;
            detail.A1_SERIALNO = "02";
            card.Details.Add(detail);
            _formBo.AddForm(form, cardBo, card);
            flows = _formFlowBo.GetFormFlows(guid);
            Assert.Equal(2, flows.Count);
        }

        [Fact]
        public void GetFormOnlyTest()
        {
            Guid guid = Guid.NewGuid();
            Form form = _formBo.GetFormOnly(guid);
            Assert.NotNull(form);
        }

        [Theory]
        [InlineData("2022-07-01", "2022-07-31", "", true)]
        [InlineData("2022-08-01", "2022-08-31", "RP19553", true)]
        public void GetFormsByContentDateProjectTest(DateTime startDate, DateTime endDate, string projNo, bool expected)
        {
            List<FormView> list = _formBo.GetFormsByContentDate(startDate, endDate, projNo);
            bool actual = list.Count == 0;
            Assert.Equal(expected, actual);
        }

        [Theory]
        [InlineData("2022-07-01", "2022-07-31", true)]
        [InlineData("2022-08-01", "2022-08-31", true)]
        public void GetFormsByContentDateTest(DateTime startDate, DateTime endDate, bool expected)
        {
            List<FormView> list = _formBo.GetFormsByContentDate(startDate, endDate);
            bool actual = list.Count == 0;
            Assert.Equal(expected, actual);
        }

        [Theory]
        [InlineData("", "", "2022-07-01", "2022-07-31")]
        [InlineData("0000", "RP19553", "2022-07-01", "2022-07-31")]
        [InlineData("0395", "", "2022-07-01", "2022-07-31")]
        [InlineData("0395", "RP19553", "2022-07-01", "2022-07-31")]
        public void GetFormsTest(string empNo, string projNo, DateTime startDate, DateTime endDate)
        {
            List<FormView> list = _formBo.GetForms(startDate, endDate);
            Assert.NotNull(list);
            Assert.Empty(list);
            list = _formBo.GetForms(startDate, endDate, projNo);
            Assert.NotNull(list);
            Assert.Empty(list);
            list = _formBo.GetForms(empNo, startDate, endDate, projNo);
            Assert.NotNull(list);
            Assert.Empty(list);
        }

        [Fact]
        public void GetFormTest()
        {
            string strForm = "{\"ID\":393,\"FormUID\":\"7bea2f17-b9e0-4157-8d47-97c74bcbe21c\",\"FormID\":\"B1CardApp\",\"FormNo\":\"申111000002\",\"FormSubject\":\"加班申請卡-孫睿宏-111/08/01\",\"FormInfo\":\"111/08/01\",\"EmpNo\":\"2268\",\"EmpName\":\"孫睿宏\",\"DeptNo\":4,\"DeptSName\":\"企劃處\",\"TeamID\":null,\"TeamCName\":\"\",\"CreatedEmpNo\":\"2268\",\"CreatedName\":\"孫睿宏\",\"FilledTime\":\"2022-08-02T11:59:29.657\",\"CreatedTime\":\"2022-08-02T11:59:59.997\",\"CreatedIP\":\"***********\",\"AddedSigner\":\"\",\"StartTime\":\"2022-08-02T12:00:00.483\",\"EndTime\":null,\"FormStatus\":1,\"TotalSteps\":1,\"CurrentStep\":1,\"UpdatedEmpNo\":null,\"UpdatedTime\":null,\"UpdatedIP\":null,\"Flows\":[{\"ID\":583,\"FlowUID\":\"b0128060-40f2-4c5a-afa6-609bfaac3d2b\",\"FormUId\":\"7bea2f17-b9e0-4157-8d47-97c74bcbe21c\",\"RecipientEmpNo\":\"L04\",\"RecipientName\":\"企劃處經理\",\"RecipientDeptNo\":4,\"RecipientDeptSName\":\"企劃處\",\"RecipientTeamID\":null,\"RecipientTeamCName\":null,\"FlowName\":\"申請人部門主管\",\"Step\":1,\"ApproverEmpNo\":null,\"ApproverName\":null,\"ApproverDeptNo\":0,\"ApproverDeptSName\":null,\"ApproverTeamID\":null,\"ApproverTeamCName\":null,\"ApproveTime\":null,\"IsAgentApprove\":false,\"FlowStatus\":1,\"IsNotification\":false}]}";
            Form? form = JsonConvert.DeserializeObject<Form>(strForm);
            Assert.NotNull(form);
            form.ContentStartTime = new DateTime(2022, 8, 2, 11, 59, 59, DateTimeKind.Local);
            string strFlows = "[{\"ID\":583,\"FlowUID\":\"b0128060-40f2-4c5a-afa6-609bfaac3d2b\",\"FormUId\":\"00000000-0000-0000-0000-000000000000\",\"RecipientEmpNo\":\"L04\",\"RecipientName\":\"企劃處經理\",\"RecipientDeptNo\":4,\"RecipientDeptSName\":\"企劃處\",\"RecipientTeamID\":null,\"RecipientTeamCName\":null,\"FlowName\":\"申請人部門主管\",\"Step\":1,\"ApproverEmpNo\":null,\"ApproverName\":null,\"ApproverDeptNo\":0,\"ApproverDeptSName\":null,\"ApproverTeamID\":null,\"ApproverTeamCName\":null,\"ApproveTime\":null,\"IsAgentApprove\":false,\"FlowStatus\":1,\"IsNotification\":false}]";
            List<FormFlow>? flows = JsonConvert.DeserializeObject<List<FormFlow>>(strFlows);
            Assert.NotNull(flows);
            string strB1CardApp = "{\"Name\":\"B1CardApp\",\"FormUID\":\"7bea2f17-b9e0-4157-8d47-97c74bcbe21c\",\"B1_FormID\":\"申111000002\",\"B1_EmpNo\":\"2268\",\"B1_DeptNo\":4,\"B1_Date\":\"2022-08-01T00:00:00\",\"B1_Hour\":4,\"B1_Code\":\"1\",\"B1_PrjNo\":\"RP19553\",\"B1_Reason\":\"test\",\"B1_WritedEmpNo\":\"2268\",\"B1_WDate\":\"2022-08-02T11:59:29.657\",\"B1_UpdatedEmpNo\":null,\"B1_UDate\":null,\"B1_ShouldSignEmpNo\":\"    \",\"B1_ADate\":null,\"B1_Status\":\"1\",\"RequisitionID\":null,\"B1_SOURCE\":\"Attendance\",\"AddSigners\":null,\"CreatedTime\":\"2022-08-02T11:59:59.997\"}";
            B1CardApp? b1CardApp = JsonConvert.DeserializeObject<B1CardApp>(strB1CardApp);
            Assert.NotNull(b1CardApp);
            b1CardApp.UploadedFiles = new List<UploadedFile>();
            UploadedFile file = new UploadedFile();
            file.FileName = "test.txt";
            b1CardApp.UploadedFiles.Add(file);

            ICardBaseBo? cardBo = _cardBoFactory.GetCardBo("B1CardApp");
            Assert.NotNull(cardBo);
            _formBo.AddForm(form, cardBo, b1CardApp);

            string sql = "SELECT TOP 1 * FROM FORM;";
            DataTable dt = SqlHelper.GetDataTable(ConnectionStringAttendance, sql);
            if (dt.Rows.Count > 0)
            {
                string? sguid = dt.Rows[0]["FormUID"].ToString();
                Assert.NotNull(sguid);
                Guid guid = new Guid(sguid);
                Form form2 = _formBo.GetForm(guid);
                Assert.Equal(guid, form2.FormUID);
            }
            Guid guid3 = new Guid("7bea2f17-b9e0-4157-8d47-97c74bcbe21c");
            Form form3 = _formBo.GetForm(guid3);
            Assert.Equal(guid3, form3.FormUID);

            AddA1CardSimple();
            Guid guid4 = new Guid("*************-2222-2222-************");
            Form form4 = _formBo.GetForm(guid4);
            Assert.Equal(guid4, form4.FormUID);
        }

        [Fact]
        public void GetFormTypesTest()
        {
            List<FormType> formTypes = _formTypeBo.GetFormTypes();
            Assert.NotEmpty(formTypes);
            Assert.Equal(4, formTypes.Count);
        }

        [Theory]
        [InlineData("", 0)]
        [InlineData("0349", 0)]
        [InlineData("0395", 0)]
        [InlineData("2265", 0)]
        [InlineData("2268", 0)]
        public void GetInboxCountTest(string empNo, int expected)
        {
            int count = _formBo.GetInboxCount(empNo);
            Assert.Equal(expected, count);
        }

        [Fact]
        public async Task GetInboxForRemindTest()
        {
            // 測試A1Card
            A1Card a1Card = new A1Card();
            a1Card.A1_EMPNO = "2092";
            a1Card.A1_YYMM = "11103";
            a1Card.A1_NN = '2';
            a1Card.A1_SOURCE = "Attendance";
            a1Card.A1_WYYMMDD = "1110727";
            a1Card.AddSigners = "2016";
            a1Card.A1_STATUS = 0;
            a1Card.A1_WDate = DateTime.Parse("2022-07-26T23:53:33.913");
            a1Card.FilledTime = DateTime.Parse("2022-07-26T23:52:48.440");
            a1Card.CreatedTime = //DateTime.Parse("2022-07-26T23:54:48.440");
                DateTime.Now;
            A1CardDetail detail = new A1CardDetail();
            detail.A1_PROJNO = "TI21508";
            detail.A1_DDHH = "8007081100";
            detail.A1_SERIALNO = "01";
            detail.A1_HOUR = 25;
            a1Card.Details.Add(detail);
            detail = new A1CardDetail();
            detail.A1_PROJNO = "RP19553";
            detail.A1_DDHH = "0001007700";
            detail.A1_HOUR = 15;
            detail.A1_SERIALNO = "02";
            a1Card.Details.Add(detail);
            string creatorId = "2092";
            string ipAddress = "***********";
            string hostname = "03-769.secinc";

            Guid? guid;
            string errorMessage;
            (guid, errorMessage) = await _a1CardBo.AddA1Card(creatorId, a1Card, ipAddress, hostname);
            Assert.Empty(errorMessage);
            Assert.NotNull(guid);
            a1Card.FormUID = (Guid)guid;
            Assert.NotEqual(Guid.Empty, guid);
            List<RoleInbox> inbox = _formBo.GetInbox("2016", _cardBoFactory);
            Assert.NotNull(inbox);
            Assert.Single(inbox);
            Assert.Single(inbox[0].Inboxes);

            Approve approve = new Approve();
            approve.FormID = inbox[0].Inboxes[0].FormID;
            approve.FormUID = a1Card.FormUID;
            approve.IsAgentApprove = false;
            approve.FlowStatus = (int)FlowStatus.Agree;
            approve.ApproverTeamID = null;
            approve.ApproveTime = DateTime.Now;
            approve.ApproverTeamCName = null;
            ICardBaseBo? cardBo = _cardBoFactory.GetCardBo(inbox[0].Inboxes[0].FormID);

            string str = _formBo.GetInboxForRemind("0391", "0391", _cardBoFactory);
            Assert.Empty(str);
            approve.FlowUID = inbox[0].Inboxes[0].FlowUID;
            approve.ApproverEmpNo = "0391";
            approve.ApproverName = "陳正忠";
            approve.ApproverDeptNo = 4;
            approve.ApproverDeptSName = "企劃處";
            Assert.NotNull(cardBo);
            errorMessage = _formBo.Approve(approve, cardBo, "0391");
            string expectedError = "無簽核權限";
            Assert.Equal(expectedError, errorMessage);

            str = _formBo.GetInboxForRemind("2016", "2016", _cardBoFactory);
            Assert.NotEmpty(str);
            Assert.Contains("Inbox", str);
        }

        [Theory]
        [InlineData(1, "0395", "0395", true)]
        [InlineData(1, "2268", "2268", true)]
        public void GetNotifyFormCardTest(int notifyId, string logonUserId, string userId, bool expected)
        {
            Guid formUID = Guid.Empty;
            ICardBaseBo cardBo = _a1CardBo;
            string result = _formBo.GetNotifyFormCard(formUID, cardBo, notifyId,
                logonUserId, userId);
            bool actual = result == "[]";
            Assert.Equal(expected, actual);
        }

        [Fact]
        public void GetOpenFormsTest()
        {
            AddA1Card2();
            AddB1CardApp1();
            string json = _formBo.GetOpenFormsJson("2069");
            Assert.NotEmpty(json);
            Assert.NotEqual("[]", json);
            List<Form>? userForms = JsonConvert.DeserializeObject<List<Form>>(json);
            Assert.NotNull(userForms);
            Assert.Single(userForms);
            string jsonAdmin = _formBo.GetOpenFormsJson("0349");
            Assert.NotEmpty(jsonAdmin);
            Assert.NotEqual("[]", jsonAdmin);
            Assert.NotEqual(json, jsonAdmin);
            List<Form>? adminForms = JsonConvert.DeserializeObject<List<Form>>(jsonAdmin);
            Assert.NotNull(adminForms);
            Assert.Equal(2, adminForms.Count);
            AddA1CardSimple();
            Guid guid4 = new Guid("*************-2222-2222-************");
            Form form4 = _formBo.GetForm(guid4);
            Assert.Equal(guid4, form4.FormUID);
        }

        [Fact]
        public void GetSentBoxJsonTest()
        {
            AddA1Card();
            AddB1CardApp1();
            string sentBoxJson = _formBo.GetSentBoxJson("0349", "0349");
            Assert.NotEmpty(sentBoxJson);
            List<SentBox>? sentBox = JsonConvert.DeserializeObject<List<SentBox>>(sentBoxJson);
            Assert.NotNull(sentBox);
            Assert.Empty(sentBox);
            string sentBoxJsonFail = _formBo.GetSentBoxJson("0349", "1052");
            Assert.Equal("[]", sentBoxJsonFail);
            sentBoxJson = _formBo.GetSentBoxJson("0349", "2268");
            sentBox = JsonConvert.DeserializeObject<List<SentBox>>(sentBoxJson);
            Assert.NotNull(sentBox);
            Assert.Empty(sentBox);
            DateTime startDate = new DateTime(2022, 4, 1, 0, 0, 0, DateTimeKind.Local);
            DateTime endDate = new DateTime(2022, 4, 30, 0, 0, 0, DateTimeKind.Local);
            sentBoxJson = _formBo.GetUserSentBoxJson("0349", "2268", startDate, endDate);
            sentBox = JsonConvert.DeserializeObject<List<SentBox>>(sentBoxJson);
            Assert.NotNull(sentBox);
            Assert.Empty(sentBox);

            startDate = new DateTime(2022, 7, 1, 0, 0, 0, DateTimeKind.Local);
            endDate = new DateTime(2022, 8, 30, 0, 0, 0, DateTimeKind.Local);
            sentBoxJson = _formBo.GetUserSentBoxJson("0349", "2268", startDate, endDate);
            sentBox = JsonConvert.DeserializeObject<List<SentBox>>(sentBoxJson);
            Assert.NotNull(sentBox);
            Assert.Single(sentBox);

            string agreedSentBoxJson = _formBo.GetAgreedSentBoxJson("0349", "2268", startDate, endDate);
            Assert.Equal("[]", agreedSentBoxJson);
            agreedSentBoxJson = _formBo.GetAgreedSentBoxYearMonthJson("2268", "2268", 2022, 9);
            Assert.Equal("[]", agreedSentBoxJson);
        }

        [Fact]
        public void GetSentBoxYearMonthJsonTest()
        {
            AddA1Card2();
            AddB1CardApp1();
            string empno = "2069";
            string json = _formBo.GetUserSentBoxYearMonthJson(_cardBoFactory, empno, empno, 2022, 4);
            List<SentBox>? sentBoxes = JsonConvert.DeserializeObject<List<SentBox>>(json);
            Assert.NotNull(sentBoxes);
            Assert.Single(sentBoxes);
            SentBox sentBox = sentBoxes[0];
            Assert.Equal(empno, sentBox.CreatedEmpNo);
            Assert.Equal(empno, sentBox.EmpNo);

            empno = "2268";
            json = _formBo.GetUserSentBoxYearMonthJson(_cardBoFactory, empno, empno, 2022, 8);
            sentBoxes = JsonConvert.DeserializeObject<List<SentBox>>(json);
            Assert.NotNull(sentBoxes);
            Assert.NotEmpty(sentBoxes);
            Assert.Single(sentBoxes);
            sentBox = sentBoxes[0];
            Assert.Equal(empno, sentBox.CreatedEmpNo);
            Assert.Equal(empno, sentBox.EmpNo);
        }

        [Fact]
        public void GetSentFormCardTest()
        {
            Guid formUID = Guid.NewGuid();
            ICardBaseBo cardBo = _a1CardBo;
            string empNo = "0395";
            string userId = "0395";
            Assert.Throws<KeyNotFoundException>(() =>
            _formBo.GetSignedFormCard(formUID, cardBo, empNo, userId));
        }

        [Fact]
        public void GetSignedFormCardTest()
        {
            Guid formUID = Guid.NewGuid();
            ICardBaseBo cardBo = _a1CardBo;
            string empNo = "0395";
            string userId = "0395";
            Assert.Throws<System.Collections.Generic.KeyNotFoundException>(() =>
            _formBo.GetSignedFormCard(formUID, cardBo, empNo, userId));

        }

        [Fact]
        public async Task GetSignedFormsJsonTest()
        {
            A1Card a1Card = new A1Card();
            a1Card.FormUID = Guid.Empty;
            a1Card.A1_EMPNO = "0349";
            a1Card.A1_YYMM = "11103";
            a1Card.A1_NN = '2';
            a1Card.A1_SOURCE = "Attendance";
            a1Card.A1_WYYMMDD = "1110727";
            a1Card.AddSigners = "0274";
            a1Card.A1_STATUS = 0;
            a1Card.A1_WDate = DateTime.Parse("2022-07-26T23:53:33.913");
            a1Card.FilledTime = DateTime.Parse("2022-07-26T23:53:18");
            a1Card.CreatedTime = DateTime.Parse("2022-07-26T23:54:48.440");
            A1CardDetail detail = new A1CardDetail();
            detail.A1_PROJNO = "TI21508";
            detail.A1_DDHH = "8007081100";
            detail.A1_SERIALNO = "01";
            detail.A1_HOUR = 25;
            a1Card.Details.Add(detail);
            detail = new A1CardDetail();
            detail.A1_PROJNO = "RP19553";
            detail.A1_DDHH = "0001007700";
            detail.A1_HOUR = 15;
            detail.A1_SERIALNO = "02";
            a1Card.Details.Add(detail);
            string creatorId = "0395";
            string ipAddress = "***********";
            string hostname = "03-769.secinc";

            Guid? guid;
            string errorMessage;
            (guid, errorMessage) = await _a1CardBo.AddA1Card(creatorId, a1Card, ipAddress, hostname);


            Approve approve = new Approve();
            List<RoleInbox> inbox = _formBo.GetInbox("0274", _cardBoFactory);
            approve.FormID = inbox[0].Inboxes[0].FormID;
            approve.FormUID = inbox[0].Inboxes[0].FormUID;
            approve.IsAgentApprove = false;
            approve.FlowStatus = (int)FlowStatus.Agree;
            approve.ApproverTeamID = null;
            approve.ApproveTime = DateTime.Now;
            approve.ApproverTeamCName = null;
            ICardBaseBo? cardBo = _cardBoFactory.GetCardBo(inbox[0].Inboxes[0].FormID);
            Assert.NotNull(cardBo);
            approve.FlowUID = inbox[0].Inboxes[0].FlowUID;
            approve.ApproverEmpNo = "0274";
            approve.ApproverName = "薛強";
            approve.ApproverDeptNo = 10;
            approve.ApproverDeptSName = "土研中心";
            approve.Comment = "<strong>有條件同意</strong>";
            _formBo.Approve(approve, cardBo, "0274");

            string json = _formBo.GetSignedFormsJson(approve.ApproverEmpNo, approve.ApproverEmpNo, 3);
            Assert.NotEmpty(json);
            List<SignedForm>? signedForms = JsonConvert.DeserializeObject<List<SignedForm>>(json);
            Assert.NotNull(signedForms);
            Assert.NotEmpty(signedForms);

            // 加一筆 B1CardApp
            AddB1CardApp1();
            inbox = _formBo.GetInbox("0391", _cardBoFactory);
            approve.FormID = inbox[1].Inboxes[0].FormID;
            approve.FormUID = inbox[1].Inboxes[0].FormUID;
            approve.FlowUID = inbox[1].Inboxes[0].FlowUID;
            approve.ApproverDeptNo = 4;
            approve.ApproverDeptSName = "企劃處";
            approve.ApproverEmpNo = "0391";
            approve.ApproverName = "陳正忠";
            cardBo = _cardBoFactory.GetCardBo(inbox[1].Inboxes[0].FormID);
            Assert.NotNull(cardBo);
            errorMessage = _formBo.Approve(approve, cardBo, "0391");
            Assert.Empty(errorMessage);
            json = _formBo.GetSignedFormsJson(approve.ApproverEmpNo, approve.ApproverEmpNo, 3);
            Assert.NotEmpty(json);
            List<SignedForm>? list = JsonConvert.DeserializeObject<List<SignedForm>>(json);
            Assert.NotNull(list);
            Assert.Single(list);
            Assert.Equal(approve.ApproverEmpNo, list[0].ApproverEmpNo);
        }

        [Theory]
        [InlineData("0395", "0395", 1, false)]
        [InlineData("0349", "0349", 1, false)]
        [InlineData("0741", "0741", 1, false)]
        [InlineData("0391", "0391", 1, false)]
        [InlineData("0494", "0494", 1, false)]
        public void GetTopApproveCommentsTest(string userId, string empNo, int count, bool expected)
        {
            AddA1Card();
            List<string> result = _formBo.GetTopApproveComments(userId, empNo, count);
            if (expected)
            {
                Assert.NotEmpty(result);
            }
            else
            {
                Assert.Empty(result);
            }
        }

        [Fact]
        public void HtmlSanitizerTest()
        {
            var sanitizer = new HtmlSanitizer();
            var html = @"<script>alert('xss')</script><div onload=""alert('xss')"" style=""background-color: rgba(0, 0, 0, 1)"">Test<img src=""test.png"" style=""background-image: url(javascript:alert('xss')); margin: 10px""></div>";
            var sanitized = sanitizer.Sanitize(html);
            var expected = @"<div style=""background-color: rgba(0, 0, 0, 1)"">Test<img src=""test.png"" style=""margin: 10px""></div>";
            Assert.Equal(expected, sanitized);
            html = @"<script>alert('xss')</script>
<div onload=""alert('xss')"">測試<span style='color:red;font-style: italic;'>防駭</span> + HTML Tag
<span style='color:green;'>簽核意見</span>
</div>";
            sanitized = sanitizer.Sanitize(html).Trim();
            expected = "<div>測試<span style=\"color: rgba(255, 0, 0, 1); font-style: italic\">防駭</span> + HTML Tag\n<span style=\"color: rgba(0, 128, 0, 1)\">簽核意見</span>\n</div>";
            Assert.Equal(expected, sanitized);
        }

        [Fact]
        public void InferenceTest()
        {
            string errorMessage = AddA1Card();
            Assert.Empty(errorMessage);
        }

        [Theory]
        [InlineData("0349", "0395", false)] // 管理員
        [InlineData("2008", "0000", false)] // 員工不存在
        [InlineData("0349", "0000", false)] // 管理員員工不存在也可查
        [InlineData("2008", "2029", false)] // 非同組員工
        [InlineData("2008", "2008", false)] // 員工本人
        [InlineData("0395", "0395", false)] // 員工本人
        [InlineData("0349", "0349", false)] // 員工本人
        [InlineData("2008", "2308", false)] // 同組員工
        [InlineData("2029", "2260", false)] // 同組員工
        [InlineData("2029", "2008", false)] // 非同組員工
        [InlineData("2029", "2029", false)] // 員工本人
        [InlineData("0274", "2029", false)] // 部門主管
        public void MarkDeliveredNotificationsTest(string logonUserId, string userId, bool expected)
        {
            bool actual = _formBo.MarkDeliveredNotifications(logonUserId, userId);
            Assert.Equal(expected, actual);
        }

        [Fact]
        public async Task MultipleApproveTest()
        {
            // 測試A1Card
            A1Card a1Card = new A1Card();
            a1Card.FormUID = Guid.Empty;
            a1Card.A1_EMPNO = "0349";
            a1Card.A1_YYMM = "11103";
            a1Card.A1_NN = '2';
            a1Card.A1_SOURCE = "Attendance";
            a1Card.A1_WYYMMDD = "1110727";
            a1Card.A1_STATUS = 0;
            a1Card.A1_WDate = DateTime.Parse("2022-07-26T23:53:33.913");
            a1Card.FilledTime = DateTime.Parse("2022-07-26T23:53:18");
            a1Card.CreatedTime = DateTime.Parse("2022-07-26T23:54:48.440");
            A1CardDetail detail = new A1CardDetail();
            detail.A1_PROJNO = "TI21508";
            detail.A1_DDHH = "8007081100";
            detail.A1_SERIALNO = "01";
            detail.A1_HOUR = 25;
            a1Card.Details.Add(detail);
            detail = new A1CardDetail();
            detail.A1_PROJNO = "RP19553";
            detail.A1_DDHH = "0001007700";
            detail.A1_HOUR = 15;
            detail.A1_SERIALNO = "02";
            a1Card.Details.Add(detail);
            string creatorId = "0395";
            string ipAddress = "***********";
            string hostname = "03-769.secinc";

            Guid? guid;
            string errorMessage;
            (guid, errorMessage) = await _a1CardBo.AddA1Card(creatorId, a1Card, ipAddress, hostname);
            string userId = "0276";
            if (guid == null) guid = Guid.Empty;
            Form form = _formBo.GetForm((Guid)guid);
            string jsonString = $"{{\"FormID\":\"A1Card\",\"FlowUID\":\"{form.Flows[0].FlowUID}\",\"FormUID\":\"{form.FormUID}\",\"ApproverEmpNo\":\"1455\",\"ApproverName\":\"陳冠吟\",\"ApproverDeptNo\":2,\"ApproverDeptSName\":\"行政處\",\"ApproverTeamID\":null,\"ApproverTeamCName\":null,\"ApproveTime\":\"2022-08-08T08:25:39.411Z\",\"IsAgentApprove\":false,\"FlowStatus\":1,\"IsNotification\":true}}";
            Approve? approveDto = JsonConvert.DeserializeObject<Approve>(jsonString);
            Assert.NotNull(approveDto);
            List<Approve> approveList = new List<Approve>();
            approveList.Add(approveDto);
            List<string> result = _formBo.MultipleApprove(approveList, _cardBoFactory, userId);
            Assert.Single(result);
            Assert.Equal("正常工作卡-白燕菁-111年03月中旬 簽核失敗:無簽核權限", result[0]);
            userId = "1455";
            result = _formBo.MultipleApprove(approveList, _cardBoFactory, userId);
            Assert.Single(result);
            Assert.Equal("正常工作卡-白燕菁-111年03月中旬 簽核失敗:無簽核權限", result[0]);
            approveDto.ApproverEmpNo = "1742";
            approveDto.ApproverName = "鍾志成";
            approveDto.ApproverDeptNo = 10;
            approveDto.ApproverDeptSName = "土研中心";
            approveDto.FlowStatus = (int)FlowStatus.Agree;
            approveList = new List<Approve>();
            approveList.Add(approveDto);
            userId = "1742";
            result = _formBo.MultipleApprove(approveList, _cardBoFactory, userId);
            Assert.Empty(result); // 簽核成功
        }

    }
}