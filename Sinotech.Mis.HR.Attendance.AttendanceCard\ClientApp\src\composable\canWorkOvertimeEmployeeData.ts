import { ref } from 'vue'
import { GET_ELIGIBLEOVERTIMEEMPLOYEES_URL } from '../api/appUrl'
import type { EmployeeStoreBaseType, EmployeeApiBaseType } from '../api/appType'

export function useCanWorkOvertimeEmployeeData() {
  const canWorkOvertimeEmployeeData = ref<Array<EmployeeStoreBaseType>>([])

  const onSetCanWorkOvertimeEmployeeData = async (deptNo: number, signal: (AbortSignal | null)): Promise<void> => {
    const params = new URLSearchParams({
      deptNo: deptNo.toString()
    })
    const res: Response = await fetch(GET_ELIGIBLEOVERTIMEEMPLOYEES_URL + '?' + params, {
      method: 'GET',
      signal: signal
    })
    if (!res.ok) {
      throw new Error(res.status.toString())
    }
    const jsonData = await res.json()
    canWorkOvertimeEmployeeData.value = jsonData.map((e: EmployeeApiBaseType) => {
      return {
        userId: e.EmpNo,
        userName: e.CName,
        deptNo: e.DeptNo,
        deptSName: e.DeptSName
      }
    })
  }

  return { canWorkOvertimeEmployeeData, onSetCanWorkOvertimeEmployeeData }
}