﻿using Sinotech.Mis.HR.Attendance.BusinessLogic;
using Microsoft.Extensions.Configuration;
using Sinotech.Mis.Extensions.Configuration;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.Tests;
using System;
using System.Diagnostics.CodeAnalysis;
using System.Net;
using Xunit;
#nullable enable

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.Tests
{
    [ExcludeFromCodeCoverage]
    public class AccountBoTests
    {

        private readonly IAccountBo _accountBo;

        /// <summary>
        /// 建構元
        /// </summary>
        /// <param name="accountBo"></param>
        public AccountBoTests(IAccountBo accountBo)
        {
            _accountBo = accountBo;
            IConfiguration configuration = new ConfigurationBuilder().
     AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).Build();
            string ConnectionStringAttendance = configuration.GetSecuredConnectionString("Attendance");
            TestHelper.ClearData(ConnectionStringAttendance);
        }

        /// <summary>
        /// 測試建構元丟出例外
        /// </summary>
        [Fact]
        public void AccountBo_Throw_Exception()
        {
            Assert.Throws<ArgumentNullException>(() => new AccountBo(null, null, null, null));
        }

        /// <summary>
        /// 測試驗證帳號密碼
        /// </summary>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <param name="expected"></param>
        [Theory]
        [InlineData("test", "fake password", null)]
        [InlineData("0395", "fake password", null)]
        [InlineData("0349", "fake password", null)]
        [InlineData("2268", "fake password", null)]
        public void AuthenticateUserTest(string username, string password, ApplicationUser? expected)
        {
            ApplicationUser? user = _accountBo.AuthenticateUser(username, password);
            Assert.Equal(expected, user);
        }

        /// <summary>
        /// 測試讀取登入失敗次數
        /// </summary>
        /// <param name="empNo"></param>
        /// <param name="expect"></param>
        [Theory]
        [InlineData("test", 0)]
        [InlineData("0395", 0)]
        [InlineData("0349", 0)]
        [InlineData("2268", 0)]
        public void GetLoginFailCountTest(string empNo, int expect)
        {
            // Act
            int failCount = _accountBo.GetLoginFailCount(empNo);
            // Assert
            Assert.Equal(expect, failCount);

            //Arrange 
            string userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";
            string ip = "127.0.0.1";
            IPAddress ipaddress = IPAddress.Parse(ip);
            DateTime loginTime = DateTime.Now;
            _accountBo.LogLoginFail(userAgent, loginTime, empNo, ipaddress, null);
            failCount = _accountBo.GetLoginFailCount(empNo);
            expect++;
            // Assert
            Assert.Equal(expect, failCount);

            // Arraange
            userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:131.0) Gecko/******** Firefox/131.0";
            loginTime = loginTime.AddSeconds(73);
            string hostName = "test1.test.com";
            // Act
            _accountBo.LogLoginFail(userAgent, loginTime, empNo, ipaddress, hostName);
            failCount = _accountBo.GetLoginFailCount(empNo);
            expect++;
            // Assert
            Assert.Equal(expect, failCount);

            // Arraange
            userAgent = "Opera/9.78.(X11; Linux x86_64; de-AT) Presto/2.9.165 Version/10.00";
            loginTime = loginTime.AddSeconds(63);
            hostName = "tw.yahoo.com";
            // Act
            _accountBo.LogLoginFail(userAgent, loginTime, empNo, ipaddress, hostName);
            failCount = _accountBo.GetLoginFailCount(empNo);
            expect++;
            // Assert
            Assert.Equal(expect, failCount);

            // Arraange
            userAgent = "Mozilla/5.0 (iPhone; U; CPU iPhone OS 4_3_5 like Mac OS X; en-us) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8L1 Safari/6533.18.5";
            loginTime = loginTime.AddMinutes(AttendanceParameters.AccountLockoutTime).AddSeconds(13);
            hostName = "www.sinotech.org.tw";
            // Act
            _accountBo.LogLoginFail(userAgent, loginTime, empNo, ipaddress, hostName);
            failCount = _accountBo.GetLoginFailCount(empNo);
            expect = 1; // 重置
            // Assert
            Assert.Equal(expect, failCount);
        }

        /// <summary>
        /// 測試登入失敗
        /// </summary>
        /// <param name="empNo"></param>
        /// <param name="expect"></param>
        [Theory]
        [InlineData("test", true)]
        [InlineData("0395", true)]
        [InlineData("0349", true)]
        [InlineData("2268", true)]
        public void LogLoginFailTest(string empNo, bool expect)
        {
            string userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";
            string ip = "127.0.0.1";
            IPAddress ipaddress = IPAddress.Parse(ip);
            DateTime loginTime = DateTime.Now;
            bool actual = _accountBo.LogLoginFail(userAgent, loginTime, empNo, ipaddress, null);
            Assert.Equal(expect, actual);
        }

        /// <summary>
        /// 測試記錄登入成功
        /// </summary>
        /// <param name="empNo"></param>
        /// <param name="expect"></param>
        [Theory]
        [InlineData("test", true)]
        [InlineData("0395", true)]
        [InlineData("0349", true)]
        [InlineData("2268", true)]
        public void LogLoginSuccessTest(string empNo, bool expect)
        {
            string userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";
            string ip = "127.0.0.1";
            IPAddress ipaddress = IPAddress.Parse(ip);
            DateTime loginTime = DateTime.Now;
            bool actual = _accountBo.LogLoginSuccess(userAgent, loginTime, ipaddress, null, empNo);
            Assert.Equal(expect, actual);
        }

        /// <summary>
        /// 測試記錄登出
        /// </summary>
        /// <param name="empNo"></param>
        /// <param name="expect"></param>
        [Theory]
        [InlineData("test", true)]
        [InlineData("0395", true)]
        [InlineData("0349", true)]
        [InlineData("2268", true)]
        public void LogLogoutTest(string empNo, bool expect)
        {
            string userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";
            string ip = "127.0.0.1";
            IPAddress ipaddress = IPAddress.Parse(ip);
            DateTime loginTime = DateTime.Now;
            bool actual = _accountBo.LogLogout(userAgent, loginTime, ipaddress, null, empNo);
            Assert.Equal(expect, actual);
        }

        /// <summary>
        /// 測試記錄解鎖
        /// </summary>
        /// <param name="empNo"></param>
        /// <param name="unlocker"></param>
        /// <param name="expect"></param>
        [Theory]
        [InlineData("test", "0395", true)]
        [InlineData("0395", "0349", true)]
        [InlineData("0349", "0395", true)]
        [InlineData("2268", "0349", true)]
        public void LogUnlockTest(string empNo, string unlocker, bool expect)
        {
            string userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";
            string ip = "127.0.0.1";
            IPAddress ipaddress = IPAddress.Parse(ip);
            DateTime unlockTime = DateTime.Now;
            AccountLogInOutResult result = AccountLogInOutResult.Success;
            bool actual = _accountBo.LogUnlock(userAgent, unlockTime, empNo, unlocker, ipaddress, result, null);
            Assert.Equal(expect, actual);
        }

        [Fact]
        public void InsertLoginRecordTest()
        {
            bool result =_accountBo.InsertLoginRecord("test", DateTime.Now, "0395", AccountLogInOutAction.Login,
                AccountLogInOutResult.Fail, IPAddress.Parse("*************"), "VS2017");
            Assert.True(result);
        }
    }
}