﻿using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Sinotech.Mis.HR.Attendance.Extensions.ServiceCollection;
using System.Diagnostics.CodeAnalysis;

namespace Sinotech.Mis.HR.Attendance.Tests
{
    [ExcludeFromCodeCoverage]
    public class Startup
    {
        private IConfiguration _configuration;
        public void ConfigureHost(IHostBuilder hostBuilder)
        {
            _configuration = new ConfigurationBuilder().
            AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).
            Build();
        }
        public void ConfigureServices(IServiceCollection services)
        {
            // 從附檔名取得 ContentType
            services.AddSingleton<FileExtensionContentTypeProvider>();
            services.AddAttendanceBusinessObjects(_configuration);
        }

    }
}
