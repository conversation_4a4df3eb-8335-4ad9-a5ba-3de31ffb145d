﻿using System;
using System.Data;

namespace Sinotech.Mis.Utilities.DataAccess.Interfaces
{
    /// <summary>
    /// 員工資料存取介面
    /// </summary>
    public interface IEmployeeDao
    {

        /// <summary>
        /// 取得副理以上資料表
        /// </summary>
        /// <returns></returns>
        public DataTable GetAboveDeputyManagerDataTable();

        /// <summary>
        /// 取得所有員工，包括離職員工
        /// </summary>
        /// <returns>所有員工，包括離職員工</returns>
        public DataTable GetAllEmployees();

        /// <summary>
        /// 取得所有員工，包括指定日期後離職之員工
        /// </summary>
        /// <param name="date">離職日期</param>
        /// <returns>所有員工</returns>
        //public DataTable GetAllEmployees(DateTime date);

        /// <summary>
        /// 取得該員工所屬部門名稱
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns></returns>
        public string GetDepartmentName(string employeeNumber);

        /// <summary>
        /// 取得該員工所屬部門編號
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns></returns>
        public int GetDepartmentNumber(string employeeNumber);

        /// <summary>
        /// 取得部門資料
        /// </summary>
        /// <returns></returns>
        public DataTable GetDepartments();

        /// <summary>
        /// 取得該員工所屬部門名稱
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns></returns>
        public string GetDepartmentShortName(string employeeNumber);

        /// <summary>
        /// 取得該部門所有現職員工人數
        /// </summary>
        /// <param name="departmentNumber">員工編號</param>
        /// <returns>該部門所有現職員工人數</returns>
        public int GetEmployeeCount(int departmentNumber);

        /// <summary>
        /// 取得員工詳細資料
        /// </summary>
        /// <param name="employeeNumber">The employee number.</param>
        /// <returns></returns>
        public DataTable GetEmployeeDetail(string employeeNumber);

        /// <summary>
        /// 取得員工姓名
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns></returns>
        public string GetEmployeeName(string employeeNumber);

        /// <summary>
        /// 取得所有現職員工
        /// </summary>
        /// <returns>所有現職員工</returns>
        public DataTable GetEmployees();

        /// <summary>
        /// 取得該部門所有現職員工
        /// </summary>
        /// <param name="departmentNumber">部門編號</param>
        /// <returns>該部門所有現職員工</returns>
        public DataTable GetEmployees(int departmentNumber);

        /// <summary>
        /// 取得所有離職員工
        /// </summary>
        /// <returns>所有離職員工</returns>
        public DataTable GetLeftEmployees();

        /// <summary>
        /// 取得所有現役組長
        /// </summary>
        /// <returns></returns>
        public DataTable GetTeamLeaders();

        /// <summary>
        /// 是否為副理以上
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>副理以上傳回<c>true</c>，否則傳回<c>false</c></returns>
        public bool IsAboveDeputyManager(string empNo);

        /// <summary>
        /// 取得所有現職員工人數
        /// </summary>
        /// <returns>現職員工人數</returns>
        public int EmployeeCount { get; }

    }
}