using FakeItEasy;
using Xunit;
using Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.PositionType;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Diagnostics.CodeAnalysis;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.Tests
{
    [ExcludeFromCodeCoverage]
    public class TestB1CardCheckOvertimeSegment : TestB1CardBase
    {
        // 每一加班時段可填報加班時間範圍
        //      a.工作日及補班日：除「正常上班起始時間～最早彈性下班時間」外，皆可填報加班。
        //        如不符規定，提示訊息：
        //        「您填報的加班時段內包含上班時間，請修正」
        //      b.週間國定假日、補假日、週六休息日、週六國定假日、彈性放假日：全天時間皆可填報加班。

        //[Theory]
        //[InlineData(WorkdayType.SundayRegularHoliday, 0)]
        //[InlineData(WorkdayType.SundayHoliday, 0)]
        //[InlineData(WorkdayType.WeekNaturalDisasterDay, 0)]
        //[InlineData(WorkdayType.SaturdayNaturalDisasterDay, 0)]
        //[InlineData(WorkdayType.WeekRestday, 0)]
        [Theory]
        [InlineData(WorkdayType.WeekHoliday, 0)]
        [InlineData(WorkdayType.WeekWorkday, 525)]
        [InlineData(WorkdayType.SaturdayRestday, 0)]
        [InlineData(WorkdayType.SaturdayHoliday, 0)]
        [InlineData(WorkdayType.MakeUpHoliday, 0)]
        [InlineData(WorkdayType.MakeUpWorkday, 525)]
        [InlineData(WorkdayType.FlexbleHoliday, 0)]
        public void CheckOvertimeSegment(WorkdayType type, int code)
        {
            var provider = GetFakeObject(B1CardPositionEnum.GeneralStaff);
            var p = B1CardPositionFactory.GetB1CardPositionObject(provider);

            var dateInfo = new Workday()
            {
                DayType = type,
                ArrivalTime = _now + TimeSpan.FromHours(8),
                FlexibleDepartureBefore = _now + TimeSpan.FromHours(16.5),
            };

            A.CallTo(() => provider.GetOverTimeDateInfo())
                .Returns(dateInfo);

            _b1Card.Details.Clear();
            _b1Card.Details.Add(
                new B1CardDetail()
                {
                    B1_CODE = 1,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now + TimeSpan.FromHours(7.5),
                    EndTime = _now + TimeSpan.FromHours(8.5),
                    Hour = 1,
                    Project = _project1,
                }
            );

            _b1Card.TotalHours = 1;

            var result = p.CheckOvertimeSegment();
            Assert.Equal(code, result.Code);
        }

        // 每一加班時段可填報加班時間範圍，規格外檢查
        [Theory]
        [InlineData(WorkdayType.SundayRegularHoliday)]
        [InlineData(WorkdayType.SundayHoliday)]
        [InlineData(WorkdayType.WeekNaturalDisasterDay)]
        [InlineData(WorkdayType.SaturdayNaturalDisasterDay)]
        [InlineData(WorkdayType.WeekRestday)]
        //[InlineData(WorkdayType.WeekWorkday)]
        //[InlineData(WorkdayType.SaturdayRestday)]
        //[InlineData(WorkdayType.SaturdayHoliday)]
        //[InlineData(WorkdayType.MakeUpHoliday)]
        //[InlineData(WorkdayType.MakeUpWorkday)]
        //[InlineData(WorkdayType.FlexbleHoliday)]
        public void CheckOvertimeSegment2(WorkdayType type)
        {
            var provider = GetFakeObject(B1CardPositionEnum.GeneralStaff);
            var p = B1CardPositionFactory.GetB1CardPositionObject(provider);

            var dateInfo = new Workday()
            {
                DayType = type,
                ArrivalTime = _now + TimeSpan.FromHours(8),
                FlexibleDepartureBefore = _now + TimeSpan.FromHours(16.5),
            };

            A.CallTo(() => provider.GetOverTimeDateInfo())
                .Returns(dateInfo);

            _b1Card.Details.Clear();
            _b1Card.Details.Add(
                new B1CardDetail()
                {
                    B1_CODE = 1,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now + TimeSpan.FromHours(7.5),
                    EndTime = _now + TimeSpan.FromHours(8.5),
                    Hour = 1,
                    Project = _project1,
                }
            );

            _b1Card.TotalHours = 1;

            Assert.Throws<InvalidOperationException>(() => p.CheckOvertimeSegment());
        }
    }
}