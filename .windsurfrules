- Always speak zh-TW.

- My testing Framework is xUnit and vitest.
    - folder: Sinotech.Mis.HR.Attendance.Tests
    - prefer FakeItEasy over Moq


- WebAPI is written in .NET 8.0 C#
    - folder: Sinotech.Mis.HR.Attendance.AttendanceCard


- Web is written in vite 6 + vue 3 + TypeScript
    - folder: Sinotech.Mis.HR.Attendance.AttendanceCard/ClientApp

- Create comments / docString in zh-TW as after creating code.

- Use PrimeVue for UI components.
