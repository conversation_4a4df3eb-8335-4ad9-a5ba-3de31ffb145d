﻿using System;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 抽單
    /// </summary>
    public class Withdraw
    {
        /// <summary>
        /// 卡名
        /// </summary>
        /// <value>
        /// A1Card、B1Card、B1CardApp、C1Card
        /// </value>
        public string FormID { get; set; } = string.Empty;

        /// <summary>
        /// 表單 UUID
        /// </summary>
        public Guid FormUID { get; set; } = Guid.Empty;

        /// <summary>
        /// 抽單人員工編號
        /// </summary>
        public string WithdrawEmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 抽單人姓名
        /// </summary>
        public string WithdrawName { get; set; } = string.Empty;

        /// <summary>
        /// 抽單時間
        /// </summary>
        public DateTime WithdrawTime { get; set; }

        /// <summary>
        /// 抽單IP
        /// </summary>
        public string? WithdrawIP { get; set; }

        /// <summary>
        /// 抽單主機
        /// </summary>
        public string? WithdrawHost { get; set; }
    }
}
