﻿﻿
using Sinotech.Mis.HR.Attendance.Common;
using System;

/// <summary>
/// 請假類型屬性標籤
/// 用於標記請假類別與特定請假類型枚舉值的對應關係
/// 此屬性僅適用於類別，不可繼承且不允許重複使用
/// </summary>
/// <remarks>
/// 這個屬性主要用於工廠模式中，透過反射機制來建立正確的請假類型實例
/// 確保每個請假類別都能與對應的 LeaveKindEnum 值進行正確映射
/// </remarks>
[AttributeUsage(AttributeTargets.Class, Inherited = false, AllowMultiple = false)]
public sealed class LeaveKindAttribute : Attribute
{
    /// <summary>
    /// 取得請假類型枚舉值
    /// </summary>
    /// <value>
    /// 代表特定請假類型的枚舉值，用於識別該類別所對應的請假種類
    /// </value>
    public LeaveKindEnum LeaveKind { get; }

    /// <summary>
    /// 初始化 LeaveKindAttribute 的新執行個體
    /// </summary>
    /// <param name="leaveKind">要關聯的請假類型枚舉值</param>
    /// <remarks>
    /// 建構函式會將傳入的請假類型枚舉值與此屬性進行綁定
    /// 這個綁定關係將用於工廠模式中的類型解析
    /// </remarks>
    public LeaveKindAttribute(LeaveKindEnum leaveKind)
    {
        LeaveKind = leaveKind;
    }
}
