﻿using System;
using System.Collections.Generic;
using System.Net.Mail;
using System.Text;

namespace Sinotech.Mis.Helpers
{
    /// <summary>
    /// 寄Email
    /// </summary>
    public static class Mailer
    {

        /// <summary>
        /// 寄Email
        /// </summary>
        /// <param name="subject"></param>
        /// <param name="body"></param>
        /// <param name="from"></param>
        /// <param name="toList"></param>
        /// <param name="smtpServer"></param>
        /// <param name="port"></param>
        /// <param name="priority"></param>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <returns></returns>
        public static bool SendMail(string subject, string body, MailAddress from,
            List<MailAddress> toList,
            string smtpServer, int port = 25, MailPriority priority = MailPriority.Normal,
            string? username = null, string? password = null)
        {
            List<MailAddress> ccList = new List<MailAddress>();
            List<MailAddress> bccList = new List<MailAddress>();
            bool ret = SendMail(subject, body, from, toList, ccList, bccList,
                smtpServer, port, priority, username, password);
            return ret;
        }

        /// <summary>
        /// 寄Email
        /// </summary>
        /// <param name="subject"></param>
        /// <param name="body"></param>
        /// <param name="from"></param>
        /// <param name="to"></param>
        /// <param name="smtpHost"></param>
        /// <param name="port"></param>
        /// <param name="priority"></param>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <returns></returns>
        public static bool SendMail(string subject, string body, MailAddress from,
            MailAddress to, string smtpHost, int port = 25, MailPriority priority = MailPriority.Normal,
    string? username = null, string? password = null)
        {
            List<MailAddress> toList = new List<MailAddress>();
            List<MailAddress> ccList = new List<MailAddress>();
            List<MailAddress> bccList = new List<MailAddress>();
            toList.Add(to);
            bool ret = SendMail(subject, body, from, toList, ccList, bccList, smtpHost, port, priority, username, password);
            return ret;
        }

        /// <summary>
        /// 寄Email
        /// </summary>
        /// <param name="subject"></param>
        /// <param name="body"></param>
        /// <param name="from"></param>
        /// <param name="toList"></param>
        /// <param name="ccList"></param>
        /// <param name="bccList"></param>
        /// <param name="smtpHost"></param>
        /// <param name="port"></param>
        /// <param name="priority"></param>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <returns></returns>
        public static bool SendMail(string subject, string body, MailAddress from,
            List<MailAddress> toList, List<MailAddress> ccList, List<MailAddress> bccList,
            string smtpHost, int port = 25, MailPriority priority = MailPriority.Normal,
            string? username = null, string? password = null)
        {
            bool ret = true;
            SmtpClient smtp = new SmtpClient(smtpHost, port);

            if (username != null && password != null)
            {
                smtp.Credentials = new System.Net.NetworkCredential(username, password);
            }
            MailMessage message = new MailMessage();
            message.BodyEncoding = Encoding.UTF8;
            message.IsBodyHtml = true;
            message.Subject = subject;
            message.Priority = priority;
            message.From = from;
            message.Body = body;
            //收件人：
            foreach (var to in toList)
            {
                message.To.Add(to);
            }
            //副本收件人：
            foreach (var cc in ccList)
            {
                message.CC.Add(cc);
            }
            //秘件收件人：
            foreach (var bcc in bccList)
            {
                message.Bcc.Add(bcc);
            }
            try
            {
                smtp.Send(message);
            }
            catch (Exception ex)
            {
                ret = false;
                Console.WriteLine($"{ex.Message}  \n{ex.StackTrace}");
            }
            return ret;
        }

    }
}
