﻿using System;
using System.Collections.Generic;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 卡片基礎抽象類別
    /// </summary>
    public abstract class CardBase
    {
        /// <summary>
        /// 卡片類別名稱
        /// </summary>
        public virtual string Name { get; } = "";

        /// <summary>
        /// 表單識別碼
        /// </summary>
        public virtual Guid FormUID { get; set; }

        /// <summary>
        /// 最後修改者員工編號
        /// </summary>
        public string? UpdatedEmpNo { get; set; }

        /// <summary>
        /// 最後修改者姓名
        /// </summary>
        public string? UpdatedName { get; set; }

        /// <summary>
        /// 最後修改日期時間
        /// </summary>
        public DateTime? UpdatedTime { get; set; }

        /// <summary>
        /// 最後修改來源IP 位址
        /// </summary>
        public string? UpdatedIP { get; set; }

        /// <summary>
        /// 最後修改來源電腦名稱
        /// </summary>
        public string? UpdatedHost { get; set; }

        /// <summary>
        /// 加會人員
        /// </summary>
        /// <value></value>
        public string? AddSigners { get; set; } = "";

        /// <summary>
        /// 前端按送出的時間
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 前端填寫(打開網頁)的時間
        /// </summary>
        public DateTime FilledTime { get; set; }

        /// <summary>
        /// 申請別
        /// </summary>
        public string ApplicationType { get; set; } = string.Empty;

        /// <summary>
        /// 附件
        /// </summary>
        public List<UploadedFile>? UploadedFiles { get; set; } = null;

        /// <summary>
        /// 是否提醒簽核人
        /// </summary>
        public bool RemindSigner { get; set; } = false;

        /// <summary>
        /// 提醒訊息類型 , 0: 無訊息、 1以上各卡不同
        /// </summary>
        public int RemindMessageType { get; set; } = 0;

        /// <summary>
        /// 提醒訊息
        /// </summary>
        public string? RemindMessage { get; set; } = null;

        /// <summary>
        /// 設定申請別
        /// </summary>
        public abstract void SetApplicationType();
    }
}
