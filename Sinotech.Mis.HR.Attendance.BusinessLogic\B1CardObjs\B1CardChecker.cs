﻿using Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.PositionType;
using Sinotech.Mis.HR.Attendance.Common;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs
{
    /// <summary>
    /// 提供 WEBAPI 呼叫的資料檢核物件
    /// </summary>
    public class B1CardChecker
    {
        #region 提供前端的錯誤物件

        /// <summary>
        /// 【加班截止時間】需晚於【加班起始時間】的錯誤物件
        /// </summary>
        public static B1CardResult TimeNotInSequenceResult { get; } = B1CardPositionBase.TimeNotInSequenceResult;

        /// <summary>
        /// 加班時間重疊的錯誤物件
        /// </summary>
        public static B1CardResult OverlappedTimeSegmentResult { get; } = B1CardPositionBase.OverlappedTimeSegmentResult;

        #endregion

        // Global Object <summary>

        // 加班卡資料物件
        private readonly B1CardParameters _parameters;

        // 資料提供物件
        private readonly IB1CardDataProvider _provider;

        // 職等物件，用以執行加班各項檢查行為
        private readonly B1CardPositionBase _position;

        /// <summary>
        /// 建構函數
        /// </summary>
        /// <param name="parameters">加班卡資料物件</param>
        public B1CardChecker(B1CardParameters parameters)
        {
            _parameters = parameters;
            _provider = new B1CardDataProvider(_parameters);
            _position = B1CardPositionFactory.GetB1CardPositionObject(_provider);
        }

        /// <summary>
        /// 檢查所有規格
        /// </summary>
        /// <returns>B1CardResult物件</returns>
        public B1CardResult CheckData()
        {
            return _position.CheckData();
        }

        /// <summary>
        /// 檢查員工職等是否可以加班
        /// </summary>
        /// <returns>B1CardResult物件</returns>
        public B1CardResult IsOvertimeAllowed()
        {
            return _position.CheckIsOvertimeAllowed();
        }

        /// <summary>
        /// 檢查指定日期是不是可以填報加班卡，檢查下列
        /// 每一申請人每日限填一張加班卡，
        /// 週日為例假日，不開放同仁填報加班卡，
        /// 如遇天災停班日不閞放填報
        /// </summary>
        /// <returns>B1CardResult物件</returns>
        public B1CardResult CheckOvertimeDate()
        {
            return _position.CheckOvertimeDate();
        }

        /// <summary>
        /// 檢查每一加班時段可填報加班時間範圍
        /// 每一加班時段可填報加班時間範圍
        ///      a.工作日及補班日：除「正常上班起始時間～最早彈性下班時間」外，皆可填報加班
        ///        如不符規定，提示訊息：
        ///        「您填報的加班時段內包含上班時間，請修正」
        ///      b.週間國定假日、補假日、週六休息日、週六國定假日、彈性放假日：全天時間皆可填報加班
        /// </summary>
        /// <returns>B1CardResult物件</returns>
        public B1CardResult CheckOvertimeSegment()
        {
            return _position.CheckOvertimeSegment();
        }

        /// <summary>
        /// 檢查是否已經填報加班申請卡且已核准
        /// </summary>
        /// <returns>B1CardResult物件</returns>
        public B1CardResult CheckIfAppliedOvertimeWork()
        {
            return _position.CheckIfAppliedOvertimeWork();
        }
    }
}