import { ref } from 'vue'
import { POST_APPROVEINBOX_URL, DELETE_WITHDRAW_URL, DELETE_CLOSEDWITHDRAW_URL, GET_APPROVALFORMCARD_URL, GET_SENTFORMCARD_URL } from '../api/appUrl'
import { FLOW_STATUS_CODE, NIL_UUID } from '../api/appConst'
import type { EmployeeStoreBaseType, CardStoreType, FormFlowType, CardApiType, FormFlowApiType } from '../api/appType'

/**
 * 簽核
 * @returns 
 */
export function useFormFlow() {
  const flowRes = ref<string>('')

  /**
   * 同意
   * @param userData 
   * @param cardData 
   */
  const onFlowAgree = async (userData: EmployeeStoreBaseType, cardData: (CardStoreType & FormFlowType), comment: string, signal: AbortSignal): Promise<void> => {
    const res: Response = await fetch(POST_APPROVEINBOX_URL, {
      method: 'POST',
      headers: {
        'content-type': 'application/json'
      },
      body: JSON.stringify({
        formID: cardData.formID,
        formUID: cardData.formUID,
        flowUID: cardData.flows[cardData.currentStep - 1].flowUID,
        approverEmpNo: userData.userId,
        approverName: userData.userName,
        approverDeptNo: userData.deptNo,
        approverDeptSName: userData.deptSName,
        approveTime: new Date(),
        isAgentApprove: false,
        flowStatus: FLOW_STATUS_CODE.Agree,
        isNotification: false,
        comment: comment
      }),
      signal: signal
    })
    if (!res.ok) {
      throw new Error(res.status.toString())
    }
    flowRes.value = await res.text()
  }

  /**
   * 不同意
   * @param userData 
   * @param cardData 
   */
  const onFlowDisagree = async (userData: EmployeeStoreBaseType, cardData: (CardStoreType & FormFlowType), comment: string, signal: AbortSignal): Promise<void> => {
    const res: Response = await fetch(POST_APPROVEINBOX_URL, {
      method: 'POST',
      headers: {
        'content-type': 'application/json'
      },
      body: JSON.stringify({
        formID: cardData.formID,
        formUID: cardData.formUID,
        flowUID: cardData.flows[cardData.currentStep - 1].flowUID,
        approverEmpNo: userData.userId,
        approverName: userData.userName,
        approverDeptNo: userData.deptNo,
        approverDeptSName: userData.deptSName,
        approveTime: new Date(),
        isAgentApprove: false,
        flowStatus: FLOW_STATUS_CODE.Deny,
        isNotification: false,
        comment: comment
      }),
      signal: signal
    })
    if (!res.ok) {
      throw new Error(res.status.toString())
    }
    flowRes.value = await res.text()
  }

  /**
   * 抽單
   * @param userData 
   * @param cardData 
   */
  const onFlowWithdraw = async (userData: EmployeeStoreBaseType, cardData: (CardStoreType & FormFlowType), signal: AbortSignal): Promise<void> => {
    const res: Response = await fetch(DELETE_WITHDRAW_URL, {
      method: 'DELETE',
      headers: {
        'content-type': 'application/json'
      },
      body: JSON.stringify({
        formID: cardData.formID,
        formUID: cardData.formUID,
        withdrawEmpNo: userData.userId,
        withdrawName: userData.userName,
        withdrawTime: new Date()
      }),
      signal: signal
    })
    if (!res.ok) {
      throw new Error(res.status.toString())
    }
    flowRes.value = await res.text()
  }

  /**
   * 結案抽單
   * @param userData 
   * @param cardData 
   */
  const onFlowClosedWithdraw = async (userData: EmployeeStoreBaseType, cardData: (CardStoreType & FormFlowType), signal: AbortSignal): Promise<void> => {
    const res: Response = await fetch(DELETE_CLOSEDWITHDRAW_URL, {
      method: 'DELETE',
      headers: {
        'content-type': 'application/json'
      },
      body: JSON.stringify({
        formID: cardData.formID,
        formUID: cardData.formUID,
        withdrawEmpNo: userData.userId,
        withdrawName: userData.userName,
        withdrawTime: new Date()
      }),
      signal: signal
    })
    if (!res.ok) {
      throw new Error(res.status.toString())
    }
    flowRes.value = await res.text()
  }

  /**
   * 如果簽核發生錯誤，則重讀表單資料
   * @param userData 
   * @param cardData 
   */
  const onFlowReviewError = async (userData: EmployeeStoreBaseType, cardData: (CardStoreType & FormFlowType), signal: AbortSignal): Promise<CardApiType & FormFlowApiType> => {
    const params = new URLSearchParams({
      formUID: cardData?.formUID ? cardData.formUID : NIL_UUID,
      empNo: userData.userId
    })
  
    const res: Response = await fetch(GET_APPROVALFORMCARD_URL + '?' + params, {
      method: 'GET',
      signal: signal
    })
    if (!res.ok) {
      throw new Error(res.status.toString())
    }
    return await res.json()
  }

   /**
   * 如果抽單發生錯誤，則重讀表單資料
   * @param userData 
   * @param cardData 
   */
   const onFlowWithdrawError = async (userData: EmployeeStoreBaseType, cardData: (CardStoreType & FormFlowType), signal: AbortSignal): Promise<CardApiType & FormFlowApiType> => {
    const params = new URLSearchParams({
      formUID: cardData?.formUID ? cardData.formUID : NIL_UUID,
      empNo: userData.userId
    })
  
    const res: Response = await fetch(GET_SENTFORMCARD_URL + '?' + params, {
      method: 'GET',
      signal: signal
    })
    if (!res.ok) {
      throw new Error(res.status.toString())
    }
    return await res.json()
  }

  return { flowRes, onFlowAgree, onFlowDisagree, onFlowWithdraw, onFlowClosedWithdraw, onFlowReviewError, onFlowWithdrawError }
}