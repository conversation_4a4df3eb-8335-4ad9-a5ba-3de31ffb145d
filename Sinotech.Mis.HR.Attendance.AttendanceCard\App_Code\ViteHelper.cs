﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.SpaServices;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Linq;
using System.Net.NetworkInformation;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace Sinotech.Mis.HR.Attendance.AttendanceCard
{
    [ExcludeFromCodeCoverage]
    public static class ViteHelper
    {
        // done message of 'npm run serve' command.
        private static string DoneMessage { get; } = "Starting the development server";

        /// <summary>
        /// Adds Connection to Vite Hosted VueApplication
        /// configured per <seealso cref="SpaOptions"/> on the <paramref name="spa"/>.
        /// NOTE: (this will create devcert.pfx and vite.config.js in your Vue Application on first run)
        /// </summary>
        /// <param name="spa"></param>
        /// <param name="port">埠號</param>
        /// <param name="sourcePath">Vite app source path</param>
        public static void UseViteDevelopmentServer(this ISpaBuilder spa, int? port = null, string sourcePath = null)
        {
            ValidateAndSetupOptions(spa, port, sourcePath);
            var context = CreateViteContext(spa);
            
            if (!IsViteServerRunning(context.DevServerPort))
            {
                SetupViteEnvironment(context);
                StartViteServer(context);
            }
            
            spa.UseProxyToSpaDevelopmentServer(context.DevServerEndpoint);
        }

        private static void ValidateAndSetupOptions(ISpaBuilder spa, int? port, string sourcePath)
        {
            spa.Options.DevServerPort = port ?? (spa.Options.DevServerPort == 0 ? 5173 : spa.Options.DevServerPort);
            
            if (!string.IsNullOrWhiteSpace(sourcePath))
            {
                spa.Options.SourcePath = sourcePath;
            }
            else if (string.IsNullOrWhiteSpace(spa.Options.SourcePath))
            {
                throw new ArgumentNullException(nameof(spa), "Must specific Spa Client App path");
            }
        }

        private class ViteContext
        {
            public Uri DevServerEndpoint { get; set; }
            public int DevServerPort { get; set; }
            public string SpaFolder { get; set; }
            public ILogger Logger { get; set; }
            public bool IsWindows { get; set; }
            public ISpaBuilder Spa { get; set; }
        }

        private static ViteContext CreateViteContext(ISpaBuilder spa)
        {
            var loggerFactory = spa.ApplicationBuilder.ApplicationServices.GetService<ILoggerFactory>();
            var webHostEnvironment = spa.ApplicationBuilder.ApplicationServices.GetService<IWebHostEnvironment>();
            
            return new ViteContext
            {
                DevServerEndpoint = new Uri($"https://localhost:{spa.Options.DevServerPort}"),
                DevServerPort = spa.Options.DevServerPort,
                SpaFolder = Path.Combine(webHostEnvironment.ContentRootPath, spa.Options.SourcePath),
                Logger = loggerFactory.CreateLogger("Vue"),
                IsWindows = RuntimeInformation.IsOSPlatform(OSPlatform.Windows),
                Spa = spa
            };
        }

        private static bool IsViteServerRunning(int port)
        {
            return IPGlobalProperties.GetIPGlobalProperties()
                .GetActiveTcpListeners()
                .Select(x => x.Port)
                .Contains(port);
        }

        private static void SetupViteEnvironment(ViteContext context)
        {
            if (!Directory.Exists(context.SpaFolder))
                throw new DirectoryNotFoundException(context.SpaFolder);

            SetupDevCertificate(context);
            InstallNodeModulesIfNeeded(context);
        }

        private static void SetupDevCertificate(ViteContext context)
        {
            var viteConfigPath = GetViteConfigFile(context.SpaFolder);
            var tempPfx = Path.Combine(context.SpaFolder, "devcert.pfx");
            var serverOptionFile = Path.Combine(context.SpaFolder, $"serverOption{new FileInfo(viteConfigPath).Extension}");

            if (!File.Exists(serverOptionFile) || !File.Exists(tempPfx))
            {
                ExportDevCertificate(context, tempPfx, serverOptionFile);
                InjectionViteConfig(viteConfigPath, serverOptionFile);
            }
        }

        private static void ExportDevCertificate(ViteContext context, string tempPfx, string serverOptionFile)
        {
            var pfxPassword = Guid.NewGuid().ToString("N");
            context.Logger.LogInformation($"Exporting dotnet dev cert to {tempPfx} for Vite");
            context.Logger.LogDebug($"Export password: {pfxPassword}");

            var process = RunProcess(context, "dotnet.exe", $" dev-certs https -v -ep {tempPfx} -p {pfxPassword}");
            LogProcessResult(context.Logger, process);

            File.WriteAllText(serverOptionFile, BuildServerOption(tempPfx, pfxPassword));
            context.Logger.LogInformation($"Creating Vite config: {serverOptionFile}");
        }

        private static void InstallNodeModulesIfNeeded(ViteContext context)
        {
            if (!Directory.Exists(Path.Combine(context.Spa.Options.SourcePath, "node_modules")))
            {
                context.Logger.LogWarning($"node_modules not found , run npm install...");
                var process = RunProcess(context, "npm", "install");
                process.WaitForExit();
                context.Logger.LogWarning($"npm install done.");
            }
        }

        private static void StartViteServer(ViteContext context)
        {
            var runningPort = $" -- --port {context.DevServerPort} --debug ";
            var process = RunProcess(context, "npm", $"run dev{runningPort}");
            
            var tcs = new TaskCompletionSource<int>();
            MonitorProcessOutput(process, context.Logger, tcs);

            if (!tcs.Task.Wait(context.Spa.Options.StartupTimeout))
            {
                throw new TimeoutException();
            }
        }

        private static Process RunProcess(ViteContext context, string command, string arguments)
        {
            var processInfo = new ProcessStartInfo
            {
                FileName = context.IsWindows ? "cmd" : command,
                Arguments = context.IsWindows ? $"/c {command} {arguments}" : arguments,
                WorkingDirectory = context.Spa.Options.SourcePath,
                RedirectStandardError = true,
                RedirectStandardInput = true,
                RedirectStandardOutput = true,
                UseShellExecute = false,
            };
            return Process.Start(processInfo);
        }

        private static void MonitorProcessOutput(Process process, ILogger logger, TaskCompletionSource<int> tcs)
        {
            MonitorStandardOutput(process, logger, tcs);
            MonitorStandardError(process, logger, tcs);
        }

        private static void MonitorStandardOutput(Process process, ILogger logger, TaskCompletionSource<int> tcs)
        {
            _ = Task.Run(() =>
            {
                try
                {
                    string line;
                    while ((line = process.StandardOutput.ReadLine()?.Trim()) != null)
                    {
                        if (!String.IsNullOrEmpty(line))
                        {
                            logger.LogInformation(line);
                            if (!tcs.Task.IsCompleted && line.Contains(DoneMessage, StringComparison.OrdinalIgnoreCase))
                            {
                                tcs.SetResult(1);
                            }
                        }
                    }
                }
                catch (EndOfStreamException ex)
                {
                    logger.LogError(ex, "{Message} {StackTrace}", ex.Message, ex.StackTrace);
                    tcs.SetException(new InvalidOperationException("'npm run dev' failed.", ex));
                }
            });
        }

        private static void MonitorStandardError(Process process, ILogger logger, TaskCompletionSource<int> tcs)
        {
            _ = Task.Run(() =>
            {
                try
                {
                    string line;
                    while ((line = process.StandardError.ReadLine()?.Trim()) != null)
                    {
                        logger.LogError(line);
                    }
                }
                catch (EndOfStreamException ex)
                {
                    logger.LogError(ex.ToString());
                    tcs.SetException(new InvalidOperationException("'npm run dev' failed.", ex));
                }
            });
        }

        private static void LogProcessResult(ILogger logger, Process process)
        {
            if (process.ExitCode == 0)
                logger.LogInformation(process.StandardOutput.ReadToEnd());
            else
                logger.LogError(process.StandardError.ReadToEnd());
        }
        
        /// <summary>
        /// Injection vite.config file to use serverOption file
        /// </summary>
        private static void InjectionViteConfig(string viteConfigPath, string serverOptionFile)
        {
            var optionFile = new FileInfo(serverOptionFile);
            var serverOption = optionFile.Name[..^optionFile.Extension.Length];
            var data = File.ReadAllLines(viteConfigPath).ToList();

            // Already injection
            if (data.Any(x => x.Contains($"./{serverOption}")))
                return;

            data.Insert(0, $"import serverOption from './{serverOption}'");

            var exportDefaultLine = data.FindIndex(x => x.Contains("export default"));
            if (exportDefaultLine == -1)
                return;

            data.Insert(exportDefaultLine + 1, "  server : serverOption,");

            File.WriteAllLines(viteConfigPath, data);
        }

        /// <summary>
        /// Get vite.config file Path (support .ts and .js)
        /// </summary>
        private static string GetViteConfigFile(string rootPath)
        {
            var configFile = Directory.GetFiles(rootPath)
                .Single(x => Path.GetFileNameWithoutExtension(x)
                .Equals("vite.config", StringComparison.OrdinalIgnoreCase));

            return configFile;
        }

        /// <summary>
        /// Build Vite https server option
        /// </summary>
        private static string BuildServerOption(string certfile, string pass)
        {
            var sb = new StringBuilder();
            sb.AppendLine("export default {");
            sb.AppendLine($"https: {{ pfx: '{Path.GetFileName(certfile)}', passphrase: '{pass}' }}");
            sb.AppendLine("}");
            sb.AppendLine();

            return sb.ToString();
        }
    }
}
