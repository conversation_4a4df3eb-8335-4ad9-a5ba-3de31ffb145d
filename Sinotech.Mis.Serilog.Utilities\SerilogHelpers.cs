﻿// SerilogHelpers, Helper functions for Serilog
// 2022/07/12
// By 林志偉

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using Serilog.Debugging;
using Serilog.Events;
using Sinotech.Mis.Extensions.Configuration;
using System;
using System.Diagnostics.CodeAnalysis;

namespace Sinotech.Mis.Serilog.Utilities
{
    [ExcludeFromCodeCoverage]
    public class SerilogHelpers
    {
        private static readonly string _serilogSectionName = "Serilog";
        private static readonly string _sqlServerSectionName = "Serilog.MsSqlServer";
        /// <summary>
        /// 建立 Serilog
        /// </summary>
        /// <param name="host">.NET Core的Hosting object</param>
        /// <param name="enableSelfLog">是否開啟Serilog的偵錯功能</param>
        /// <param name="serilogSectionName">設定檔的Serilog區段名</param>
        /// <param name="sqlServerSectionName">設定檔的Serilog SqlServer區段名</param>
        [ExcludeFromCodeCoverage]
        public static void CreateSeriLog(IHost host, bool enableSelfLog = true)
        {
            IConfiguration config;

            // https://docs.microsoft.com/zh-tw/aspnet/core/fundamentals/dependency-injection?view=aspnetcore-5.0
            using (var serviceScope = host.Services.CreateScope())
            {
                var services = serviceScope.ServiceProvider;
                config = services.GetRequiredService<IConfiguration>();
            }

            if (enableSelfLog)
            {
                SelfLog.Enable(Console.Error);
            }

            // Load Settings
            var loggerConfig = new LoggerConfiguration().ReadFrom.Configuration(config, _serilogSectionName);

            loggerConfig = LoadSqlServerSetting(config, loggerConfig);
            Log.Logger = loggerConfig.CreateLogger();
        }

        private static LoggerConfiguration LoadSqlServerSetting(IConfiguration config, LoggerConfiguration loggerConfig)
        {
            if (config.GetSection(_sqlServerSectionName).Exists())
            {
                var key = config[$"{_sqlServerSectionName}:Args:connectionString"];
                if (string.IsNullOrEmpty(key))
                {
                    if (key is null)
                    {
                        throw new ArgumentNullException($"{_sqlServerSectionName} 區段找不到連線字串設定");
                    }
                    return loggerConfig;
                }
                var connectionString = config.GetSecuredConnectionString(key);

                var sqlserverSection = config.GetSection($"{_sqlServerSectionName}:Args");
                var columnOptionsSection = sqlserverSection.GetSection("columnOptionsSection");
                var sinkOptionsSection = sqlserverSection.GetSection("sinkOptionsSection");
                var minimumLevel = ParseLogEventLevel(config[$"{_sqlServerSectionName}:Args:restrictedToMinimumLevel"]);

                loggerConfig = loggerConfig.WriteTo.MSSqlServer(
                    connectionString: connectionString,
                    sinkOptionsSection: sinkOptionsSection,
                    restrictedToMinimumLevel: minimumLevel,
                    appConfiguration: config,
                    columnOptionsSection: columnOptionsSection);
            }

            return loggerConfig;
        }

        private static LogEventLevel ParseLogEventLevel(string value, LogEventLevel defaultLevel = LogEventLevel.Information)
        {
            LogEventLevel parsedLevel = defaultLevel;
            if (!string.IsNullOrEmpty(value) && !Enum.TryParse(value, out parsedLevel))
            {
                throw new InvalidOperationException($"不合理的 LogLevel： {value}");
            }
            return parsedLevel;
        }
    }
}