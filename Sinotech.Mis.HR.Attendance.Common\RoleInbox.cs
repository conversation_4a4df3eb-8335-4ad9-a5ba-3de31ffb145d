﻿using System.Collections.Generic;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 角色收件匣/角色待審表單列表
    /// </summary>
    public class RoleInbox
    {
        /// <summary>
        /// 角色
        /// </summary>
        /// <value>
        /// The role.
        /// </value>
        public string Role { get; set; } = string.Empty;

        /// <summary>
        /// 此同仁擁有或代理所有角色的收件匣List
        /// </summary>
        /// <value>
        /// The inboxes.
        /// </value>
        public List<Inbox> Inboxes { get; set; } = new List<Inbox>();

        /// <summary>
        /// 是否為代理人
        /// </summary>
        public bool IsAgency { get; set; } = false;
    }
}
