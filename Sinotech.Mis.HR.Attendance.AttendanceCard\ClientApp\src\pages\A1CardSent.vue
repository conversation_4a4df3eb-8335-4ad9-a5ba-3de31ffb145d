<template>
  <CardSent :modelValue="data">
    <template #header>
      <i class="bi bi-search me-1" />
      <span>填報查詢 / </span>
      <span>{{ FORM_ID.A1Card }}</span>
    </template>
  </CardSent>
  <div class="container border border-2 px-0">
    <A1Card :modelValue="data" />
  </div>
  <div class="container px-0">
    <FormFlow :modelValue="data" />
  </div>

  <div class="container px-0">
    <div class="text-black-50 text-end">
      <span>
        查詢時間：
      </span>
      <span class="fst-italic">
        {{ dateToRocString(cardStore.queryDataTime) }}
      </span>
    </div>
  </div>
</template>
<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useToast } from 'primevue/usetoast'
import { useCardStore } from '../store/card'
import { onBeforeRouteLeave } from 'vue-router'
import { FORM_ID } from '../api/appConst'
import { dateToRocString } from '../api/appFunction'
import CardSent from '../components/CardSent.vue'
import A1Card from '../components/A1Card.vue'
import FormFlow from '../components/FormFlow.vue'

const cardStore = useCardStore()
const { data } = storeToRefs(cardStore)
const toast = useToast()

onBeforeRouteLeave(() => {
  cardStore.$reset()
  toast.removeGroup('app')
})
</script>