﻿using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sinotech.Mis.Common;
using Sinotech.Mis.Helpers;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.DataAccess.Interfaces;
using Sinotech.Mis.HR.Attendance.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic
{
    /// <summary>
    /// 正常工作卡 商業物件
    /// </summary>
    public class A1CardBo : IA1CardBo
    {

        // 避免重覆送出鎖定物件
        private static readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);

        private readonly IA1CardDao _a1CardDao;
        private readonly IEmployeeBo _employeeBo;
        private readonly IFormBo _formBo;
        private readonly IFormFlowBo _formFlowBo;
        private readonly ILogger<A1CardBo> _logger;
        private readonly IProjectBo _projectBo;
        private readonly IWorkdayBo _workdayBo;

        /// <summary>Initializes a new instance of the <see cref="A1CardBo" /> class.</summary>
        /// <param name="a1CardDao">The a1 card DAO.</param>
        /// <param name="employeeBo">The employee bo.</param>
        /// <param name="formBo">The form bo.</param>
        /// <param name="formFlowBo"></param>
        /// <param name="projectBo">The project bo.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="workdayBo"></param>
        public A1CardBo(IA1CardDao a1CardDao, IEmployeeBo employeeBo, IFormBo formBo, 
            IFormFlowBo formFlowBo, IProjectBo projectBo, ILogger<A1CardBo> logger, IWorkdayBo workdayBo)
        {
            _a1CardDao = a1CardDao;
            _employeeBo = employeeBo;
            _formBo = formBo;
            _formFlowBo = formFlowBo;
            _projectBo = projectBo;
            _logger = logger;
            _workdayBo = workdayBo;
        }

        /// <summary>
        /// 檢查該日期是否已填過A1Card
        /// </summary>
        /// <param name="a1Card"></param>
        /// <returns>已填過 true, 未填過 false</returns>
        private bool CheckIfAlreadyFilled(A1Card a1Card)
        {
            //檢查是否已填過
            FormFilled filled = IsFilled(a1Card.A1_EMPNO, a1Card.A1_YYMM, a1Card.A1_NN);
            if (filled.FormUID == null)
            {
                return false;
            }
            return true;
        }

        /// <summary>
        /// 檢查工時是否正確
        /// </summary>
        /// <param name="a1Card"></param>
        /// <param name="form"></param>
        /// <param name="dayTotalHours"></param>
        /// <returns></returns>
        private string CheckWorkingHours(A1Card a1Card, Form form, int[] dayTotalHours)
        {
            string errorMessage = string.Empty;
            List<Workday> daysList = _workdayBo.GetEmpWorkdaysDateRange((DateTime)form.ContentStartTime, (DateTime)form.ContentEndTime, a1Card.A1_EMPNO);
            Workday[] workdays = daysList.ToArray();
            for (int i = 0; i < workdays.Length; i++)
            {
                if (dayTotalHours[i] > workdays[i].WorkHours)
                {
                    string rocDate = CardUtility.RocChineseDateString(workdays[i].WorkDate);
                    errorMessage = $"{rocDate}填報的工時超過當日的正常工作時數，請修正";
                    break;
                }
            }
            return errorMessage;
        }

        /// <summary>
        /// 填表及檢查
        /// </summary>
        /// <param name="creatorId"></param>
        /// <param name="a1Card"></param>
        /// <param name="ipAddress"></param>
        /// <param name="hostname"></param>
        /// <returns></returns>
        private (Form, string) FillFormAndCheck(string creatorId, A1Card a1Card, string ipAddress, string hostname)
        {
            Form form = new Form();
            string errorMessage;

            // 檢查是否已填報過
            if (CheckIfAlreadyFilled(a1Card))
            {
                return (form, AttendanceParameters.A1CardAlreadyFilled);
            }

            // 設定表單資料
            (form, int year, int month, int iTenDays) = SetFormData(creatorId, a1Card, ipAddress, hostname);

            // 處理加簽者
            ProcessAdditionalSigners(form, a1Card);

            // 計算每日總工時與流程關卡
            int[] dayTotalHours;
            (dayTotalHours, errorMessage) = CalculateDayTotalHoursAndFlows(a1Card, year, month, iTenDays, ref form);
            if (!string.IsNullOrEmpty(errorMessage))
            {
                return (form, errorMessage);
            }

            // 檢查旬別設定
            if (form.ContentStartTime == null || form.ContentEndTime == null)
            {
                errorMessage = "旬別錯誤";
                return (form, errorMessage);
            }

            // 檢查每日工時是否超過上班時數
            errorMessage = CheckWorkingHours(a1Card, form, dayTotalHours);
            if (!string.IsNullOrEmpty(errorMessage))
            {
                return (form, errorMessage);
            }

            // 更新表單狀態
            UpdateFormStatus(a1Card, form);

            return (form, string.Empty);
        }

        /// <summary>
        /// 處理加會人者
        /// </summary>
        /// <param name="form"></param>
        /// <param name="a1Card"></param>
        private void ProcessAdditionalSigners(Form form, A1Card a1Card)
        {
            if (a1Card.AddSigners != null)
            {
                a1Card.AddSigners = a1Card.AddSigners.TrimEnd(',');
                form.AddedSigner = a1Card.AddSigners;
                string[] signers = a1Card.AddSigners.Split(',');
                _formFlowBo.FlowAddSigners(form, signers);
            }
        }

        /// <summary>
        /// 計算每日總工時與流程關卡
        /// </summary>
        /// <param name="a1Card"></param>
        /// <param name="year"></param>
        /// <param name="month"></param>
        /// <param name="iTenDays"></param>
        /// <param name="form"></param>
        private (int[], string) CalculateDayTotalHoursAndFlows(A1Card a1Card, int year, int month, int iTenDays, ref Form form)
        {
            string errorMessage = string.Empty;
            int serialNo = 0;
            int[] dayTotalHours = new int[a1Card.Details[0].A1_DDHH.Length];

            foreach (A1CardDetail detail in a1Card.Details)
            {
                serialNo++;
                detail.A1_SERIALNO = serialNo.ToString();

                Project project = _projectBo.GetProject(detail.A1_PROJNO);
                if (project == null)
                {
                    errorMessage = $"查無計畫編號：{detail.A1_PROJNO}";
                    break;
                }

                // 檢查計畫細節並更新工時
                errorMessage = ValidateAndUpdateProjectHours(detail, project, year, month, iTenDays, ref dayTotalHours);
                if (errorMessage != string.Empty)
                {
                    break;
                }

                // 若計畫屬於其他部門，添加流程關卡
                _formBo.AddFlowByProject(form, project);
            }

            return (dayTotalHours, errorMessage);
        }

        /// <summary>
        /// 檢查計畫細節並更新工時統計
        /// </summary>
        /// <param name="detail"></param>
        /// <param name="project"></param>
        /// <param name="year"></param>
        /// <param name="month"></param>
        /// <param name="iTenDays"></param>
        /// <param name="dayTotalHours"></param>
        /// <returns></returns>
        private static string ValidateAndUpdateProjectHours(A1CardDetail detail, Project project, int year, int month, int iTenDays, ref int[] dayTotalHours)
        {
            string errorMessage = string.Empty;
            int projectHours = 0;
            char[] hoursArray = detail.A1_DDHH.ToCharArray();

            for (int i = 0; i < hoursArray.Length; i++)
            {
                int hours = int.Parse(hoursArray[i].ToString());
                if (hours > 0)
                {
                    projectHours += hours;
                    dayTotalHours[i] += hours;

                    DateTime date = new DateTime(year, month, 10 * (iTenDays - 1) + i + 1, 0, 0, 0, DateTimeKind.Local);
                    errorMessage = IsProjectValidOnDate(project, date);
                    if (errorMessage != string.Empty)
                    {
                        return errorMessage;
                    }
                }
            }

            if (projectHours == 0)
            {
                errorMessage = $"計畫編號：{project.PrjNo} 工時有誤，每旬總工時須大於 0 小時";
                return errorMessage;
            }

            detail.A1_HOUR = projectHours;
            return errorMessage;
        }

        /// <summary>
        /// 檢查計畫編號在A1Card的填報日是否有效
        /// </summary>
        /// <param name="project"></param>
        /// <param name="date"></param>
        /// <returns></returns>
        private static string IsProjectValidOnDate(Project project, DateTime date)
        {
            string errorMessage = string.Empty;
            //檢查計畫編號在A1Card的申請日是否成立
            if (project.BDate != null && project.BDate > date)
            {
                errorMessage = $"計畫編號「{project.PrjNo}」成立於{project.BDate:yyyy/MM/dd}，在{date:yyyy/MM/dd}尚未成立";
            }

            if (project.EDate != null && project.EDate <= date)
            {
                errorMessage = $"計畫編號「{project.PrjNo}」於{project.EDate:yyyy/MM/dd}已結案";
            }

            //檢查計畫編號在A1Card的申請日是否已截止填報
            if (project.SubmitDueDate != null && project.SubmitDueDate <= date)
            {
                errorMessage = $"計畫編號「{project.PrjNo}」進度已達100%，填報截止日為{project.SubmitDueDate:yyyy/MM/dd}，在{date:yyyy/MM/dd}已截止填報";
            }

            return errorMessage;
        }

        /// <summary>
        /// 更新表單狀態
        /// </summary>
        private void UpdateFormStatus(A1Card a1Card, Form form)
        {
#pragma warning disable S125 // Sections of code should not be commented out
            //若需要加申請人部門主管必須加上 _formBo.FlowAddManager(form, employee);
#pragma warning restore S125 // Sections of code should not be commented out
            form.Flows = _formFlowBo.FlowDedup(form.Flows);
            form.TotalSteps = form.Flows.Count;

            if (form.TotalSteps == 0)
            {
                a1Card.A1_AYYMMDD = a1Card.A1_WYYMMDD;
                a1Card.A1_STATUS = (int)FormStatus.Agree;
            }
            else
            {
                a1Card.A1_AYYMMDD = "       ";
                a1Card.A1_STATUS = (int)FormStatus.Processing;
            }
        }

        /// <summary>
        /// 取得某段時間內表單及卡
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="dtCardForms"></param>
        /// <returns></returns>
        private List<FormCard> GetFormCards(DateTime startDate, DateTime endDate, DataTable dtCardForms)
        {
            List<FormCard> formCards = new List<FormCard>();
            DataTable dtCards = _a1CardDao.GetA1Cards(startDate, endDate);
            var guids = (from DataRow dr in dtCardForms.Rows
                         select (Guid)dr["FormUID"]).Distinct().OrderBy(FormUID => FormUID);

            foreach (var guid in guids)
            {
                FormCard formCard = new FormCard();
                formCard.FormUID = guid;
                A1Card card = new A1Card();

                formCard.Flows = new List<FormFlow>();
                DataRow[] drs = dtCardForms.Select($"FormUID='{guid}'");
                if (drs.Length > 0)
                {
                    DataRow row = drs[0];
                    formCard.ID = (int)row["FormIntID"];
                    formCard.FormUID = guid;
                    formCard.FormID = (string)row["FormID"];
                    formCard.FormNo = (string)row["FormNo"];
                    formCard.FormSubject = (string)row["FormSubject"];
                    formCard.FormInfo = (string)row["FormInfo"];
                    formCard.EmpNo = (string)row["EmpNo"];
                    formCard.EmpName = (string)row["EmpName"];
                    formCard.DeptNo = (int)row["DeptNo"];
                    formCard.DeptSName = SqlHelper.GetColumnStringValue(row, "DeptSName");
                    formCard.TeamID = SqlHelper.GetColumnIntValue(row, "TeamID");
                    formCard.TeamCName = SqlHelper.GetColumnStringValue(row, "TeamCName");
                    if (row["RankNo"] != DBNull.Value)
                    {
                        formCard.RankNo = (string)row["RankNo"];
                    }
                    if (row["RankName"] != DBNull.Value)
                    {
                        formCard.RankName = (string)row["RankName"];
                    }

                    formCard.JobNo = SqlHelper.GetColumnStringValue(row, "JobNo");
                    formCard.JobName = SqlHelper.GetColumnStringValue(row, "JobName");

                    formCard.ContentStartTime = SqlHelper.GetColumnDateTimeValue(row, "ContentStartTime");
                    formCard.ContentEndTime = SqlHelper.GetColumnDateTimeValue(row, "ContentEndTime");
                    formCard.CreatedEmpNo = (string)row["CreatedEmpNo"];
                    formCard.CreatedName = (string)row["CreatedName"];
                    formCard.FilledTime = (DateTime)row["FilledTime"];
                    formCard.CreatedTime = (DateTime)row["CreatedTime"];
                    formCard.CreatedIP = (string)row["CreatedIP"];
                    formCard.CreatedHost = SqlHelper.GetColumnStringValue(row, "CreatedHost");

                    formCard.AddedSigner = SqlHelper.GetColumnStringValue(row, "AddedSigner");
                    formCard.AddedSigner = _formBo.AddSignersAddName(formCard.AddedSigner);
                    formCard.StartTime = (DateTime)row["StartTime"];

                    formCard.EndTime = SqlHelper.GetColumnDateTimeValue(row, "EndTime");

                    formCard.FormStatus = (byte)row["FormStatus"];
                    formCard.FormStatusName = (string)row["FormStatusName"];
                    formCard.TotalSteps = (byte)row["TotalSteps"];
                    formCard.CurrentStep = (byte)row["CurrentStep"];

                    card.FormUID = guid;
                    card.UpdatedEmpNo = SqlHelper.GetColumnStringValue(row, "UpdatedEmpNo");
                    card.UpdatedName = SqlHelper.GetColumnStringValue(row, "UpdatedName");
                    card.UpdatedTime = SqlHelper.GetColumnDateTimeValue(row, "UpdatedTime");
                    card.UpdatedIP = SqlHelper.GetColumnStringValue(row, "UpdatedIP");
                    card.UpdatedHost = SqlHelper.GetColumnStringValue(row, "UpdatedHost");
                    card.CreatedTime = (DateTime)row["CreatedTime"];
                    card.AddSigners = formCard.AddedSigner;
                    card.A1_EMPNO = (string)row["A1_EMPNO"];
                    card.A1_NN = Convert.ToChar((string)row["A1_NN"]);
                    card.A1_YYMM = (string)row["A1_YYMM"];
                    card.A1_STATUS = (byte)row["A1_STATUS"];
                    card.A1_SOURCE = (string)row["A1_SOURCE"];
                    card.A1_SHEETNO = (string)row["A1_SHEETNO"];
                    card.A1_WYYMMDD = (string)row["A1_WYYMMDD"];
                    card.A1_AYYMMDD = SqlHelper.GetColumnStringValue(row, "A1_AYYMMDD");
                    card.A1_WDate = (DateTime)row["A1_WDate"];
                    card.A1_ADate = SqlHelper.GetColumnDateTimeValue(row, "A1_ADate");
                    var detailRows = from DataRow dr in dtCards.Rows
                                     where (Guid)dr["FormUID"] == guid
                                     orderby dr["ID"]
                                     select dr;

                    foreach (DataRow detailRow in detailRows)
                    {
                        A1CardDetail detail = new A1CardDetail();
                        detail.ID = (int)detailRow["ID"];
                        detail.A1_PROJNO = (string)detailRow["A1_PROJNO"];
                        detail.A1_DDHH = (string)detailRow["A1_DDHH"];
                        detail.A1_HOUR = (byte)detailRow["A1_HOUR"];
                        detail.A1_SERIALNO = (string)detailRow["A1_SERIALNO"];
                        card.Details.Add(detail);
                    }

                    if (row["Step"] != DBNull.Value)
                    {
                        var stepRows = (from DataRow dr in drs select (byte)row["Step"])
                                    .Distinct().OrderBy(Step => Step);
                        foreach (byte step in stepRows)
                        {
                            var k = from DataRow dr in drs
                                    where (byte)dr["Step"] == step
                                    select dr;

                            if (k != null && k.Any())
                            {
                                DataRow dr7 = k.First();
                                FormFlow flow = FormFlow.DataRowToFormFlow(dr7);
                                flow.ID = (int)dr7["FormFlowID"];
                                flow.FlowStatusName = _formFlowBo.GetFlowStatusName(flow.FlowStatus);
                                formCard.Flows.Add(flow);
                            }
                        }
                    }
                }

                card.SetApplicationType();
                formCard.Card = card;

                _formBo.AddAttachments(dtCardForms, formCard, drs);
                formCards.Add(formCard);
            }

            return formCards;
        }

        /// <summary>
        /// 取得旬別
        /// </summary>
        private static string GetTendays(A1Card a1Card, Form form, int year, int month)
        {
            string tenDays = "上旬";
            switch (a1Card.A1_NN)
            {
                case '1':
                    tenDays = "上旬";
                    form.ContentStartTime = new DateTime(year, month, 1, 0, 0, 0, DateTimeKind.Local);
                    form.ContentEndTime = new DateTime(year, month, 10, 0, 0, 0, DateTimeKind.Local);
                    break;
                case '2':
                    tenDays = "中旬";
                    form.ContentStartTime = new DateTime(year, month, 11, 0, 0, 0, DateTimeKind.Local);
                    form.ContentEndTime = new DateTime(year, month, 20, 0, 0, 0, DateTimeKind.Local);
                    break;
                case '3':
                    tenDays = "下旬";
                    form.ContentStartTime = new DateTime(year, month, 21, 0, 0, 0, DateTimeKind.Local);
                    form.ContentEndTime = CardUtility.LastDayOfMonth((DateTime)form.ContentStartTime);
                    break;
            }

            return tenDays;
        }

        /// <summary>
        /// 設定表單資料
        /// </summary>
        private (Form form, int year, int month, int iTenDays) SetFormData(string creatorId, A1Card a1Card, string ipAddress, string hostname)
        {
            if (a1Card.A1_WDate < a1Card.CreatedTime)
            {
                a1Card.A1_WDate = a1Card.CreatedTime;
            }

            Form form = new Form();
            form.FormID = "A1Card";
            form.FormUID = Guid.NewGuid();
            form.EmpNo = a1Card.A1_EMPNO;
            Employee? employee = _employeeBo.GetEmployeeDetail(form.EmpNo);
            Employee? employeeCreator = _employeeBo.GetEmployeeDetail(creatorId);
            if (employee != null && employee.CName != null)
            {
                form.EmpName = employee.CName;
                form.DeptNo = employee.DeptNo;
                form.DeptSName = employee.DeptSName;
                form.TeamID = employee.TeamID;
                form.TeamCName = employee.TeamCName;
                form.RankNo = employee.RankNo;
                form.RankName = employee.RankName;
                form.JobNo = employee.JobNo;
                form.JobName = employee.JobName;
            }

            a1Card.A1_WDate = a1Card.A1_WDate.ToLocalTime();
            form.FilledTime = a1Card.FilledTime.ToLocalTime();
            form.CreatedTime = a1Card.CreatedTime.ToLocalTime();
            form.UpdatedTime = form.CreatedTime;
            form.UpdatedEmpNo = creatorId;
            if (employeeCreator != null && employeeCreator.CName != null)
            {
                form.UpdatedName = employeeCreator.CName;
                a1Card.UpdatedName = employeeCreator.CName;
                form.CreatedName = employeeCreator.CName;
            }
            form.UpdatedIP = ipAddress;
            form.UpdatedHost = hostname;

            a1Card.UpdatedTime = form.CreatedTime;
            a1Card.UpdatedEmpNo = creatorId;
            a1Card.UpdatedIP = ipAddress;
            a1Card.UpdatedHost = hostname;
            int year = int.Parse(a1Card.A1_YYMM.Substring(0, 3)) + 1911;
            int month = int.Parse(a1Card.A1_YYMM.Substring(3, 2));
            int iTenDays = int.Parse(a1Card.A1_NN.ToString());
            string tenDays = GetTendays(a1Card, form, year, month);
            form.FormSubject = $"正常工作卡-{form.EmpName}-{a1Card.A1_YYMM.Substring(0, 3)}年{a1Card.A1_YYMM.Substring(3, 2)}月{tenDays}";
            form.CreatedTime = a1Card.CreatedTime.ToLocalTime();

            //FormInfo範例：111年01月上旬
            form.FormInfo = $"{a1Card.A1_YYMM.Substring(0, 3)}年{a1Card.A1_YYMM.Substring(3, 2)}月{tenDays}";
            form.CreatedEmpNo = creatorId;
            DateTime filledTimeTemp = a1Card.A1_WDate;
            form.FilledTime = filledTimeTemp.ToLocalTime();
            form.CreatedIP = ipAddress;
            form.CreatedHost = hostname;
            form.AddedSigner = a1Card.AddSigners;
            form.StartTime = DateTime.Now;
            form.FormStatus = (int)FormStatus.Processing; //簽核狀態：1:簽核中
            form.CurrentStep = 0; //所有表單都預設為0，讓FormBo自動計算
            return (form, year, month, iTenDays);
        }

        /// <summary>
        /// 正常工作卡寫入資料庫
        /// </summary>
        /// <param name="a1Card"></param>
        /// <param name="form"></param>
        /// <returns></returns>
        private (Guid?, string) SubmitForm(A1Card a1Card, Form form)
        {
            Guid? guid = null;
            string errorMessage = string.Empty;
                //呼叫 FormBO 寫入資料庫
                errorMessage = _formBo.AddForm(form, this, (CardBase)a1Card);
            if (errorMessage == string.Empty)
            {
                guid = form.FormUID;
                _LastCard = a1Card.EasyClone(); //記錄上次填報的表單
            }
            return (guid, errorMessage);
        }

        /// <summary>
        /// 上次填報的正常工作卡
        /// </summary>
        private static A1Card _LastCard { get; set; } = new A1Card();

        /// <summary>
        /// Convert A1CardDto to A1Card
        /// </summary>
        /// <param name="a1CardDto"></param>
        /// <returns></returns>
        public static A1Card A1CardDto2A1Card(A1CardDto a1CardDto)
        {
            A1Card card = new A1Card();
            card.FormUID = a1CardDto.FormUID;
            card.A1_EMPNO = a1CardDto.A1_EMPNO;
            card.A1_YYMM = a1CardDto.A1_YYMM;
            card.A1_NN = a1CardDto.A1_NN;
            card.A1_WYYMMDD = a1CardDto.A1_WYYMMDD;
            card.A1_AYYMMDD = a1CardDto.A1_AYYMMDD;
            card.A1_STATUS = a1CardDto.A1_STATUS;
            card.A1_SHEETNO = a1CardDto.A1_SHEETNO;
            card.A1_SOURCE = a1CardDto.A1_SOURCE;
            card.A1_WDate = a1CardDto.A1_WDate;
            card.A1_ADate = a1CardDto.A1_ADate;
            card.UpdatedEmpNo = a1CardDto.UpdatedEmpNo;
            card.UpdatedName = a1CardDto.UpdatedName;
            card.UpdatedTime = a1CardDto.UpdatedTime;
            card.UpdatedIP = a1CardDto.UpdatedIP;
            card.UpdatedHost = a1CardDto.UpdatedHost;
            card.SetApplicationType();
            A1CardDetail detail = new A1CardDetail();
            detail.ID = a1CardDto.ID;
            detail.A1_PROJNO = a1CardDto.A1_PROJNO;
            detail.A1_DDHH = a1CardDto.A1_DDHH;
            detail.A1_HOUR = a1CardDto.A1_HOUR;
            detail.A1_SERIALNO = a1CardDto.A1_SERIALNO;
            card.Details.Add(detail);
            return card;
        }

        /// <summary>
        /// Convert List of A1CardDto to A1Card
        /// </summary>
        /// <param name="a1CardDtos"></param>
        /// <returns></returns>
        public static A1Card A1CardDtos2A1Card(List<A1CardDto> a1CardDtos)
        {
            A1Card card = new A1Card();
            if (a1CardDtos.Count > 0)
            {
                A1CardDto a1CardDto = a1CardDtos[0];
                card.FormUID = a1CardDto.FormUID;
                card.A1_EMPNO = a1CardDto.A1_EMPNO;
                card.A1_YYMM = a1CardDto.A1_YYMM;
                card.A1_NN = a1CardDto.A1_NN;
                card.A1_WYYMMDD = a1CardDto.A1_WYYMMDD;
                card.A1_AYYMMDD = a1CardDto.A1_AYYMMDD;
                card.A1_STATUS = a1CardDto.A1_STATUS;
                card.A1_SHEETNO = a1CardDto.A1_SHEETNO;
                card.A1_SOURCE = a1CardDto.A1_SOURCE;
                card.A1_WDate = a1CardDto.A1_WDate;
                card.A1_ADate = a1CardDto.A1_ADate;
                card.UpdatedEmpNo = a1CardDto.UpdatedEmpNo;
                card.UpdatedName = a1CardDto.UpdatedName;
                card.UpdatedTime = a1CardDto.UpdatedTime;
                card.UpdatedIP = a1CardDto.UpdatedIP;
                card.UpdatedHost = a1CardDto.UpdatedHost;
                card.SetApplicationType();
                foreach (A1CardDto cardDto in a1CardDtos)
                {
                    A1CardDetail detail = new A1CardDetail();
                    detail.ID = cardDto.ID;
                    detail.A1_PROJNO = cardDto.A1_PROJNO;
                    detail.A1_DDHH = cardDto.A1_DDHH;
                    detail.A1_HOUR = cardDto.A1_HOUR;
                    detail.A1_SERIALNO = cardDto.A1_SERIALNO;
                    card.Details.Add(detail);
                }
            }
            return card;
        }

        /// <summary>
        /// Convert List of A1CardForm to A1Card
        /// </summary>
        /// <param name="a1CardForms"></param>
        /// <returns></returns>
        public static A1Card A1CardForm2A1Card(List<A1CardForm> a1CardForms)
        {
            A1Card card = new();
            if (a1CardForms.Count > 0)
            {
                A1CardForm a1CardForm = a1CardForms[0];
                card.FormUID = a1CardForm.FormUID;
                card.A1_EMPNO = a1CardForm.A1_EMPNO;
                card.A1_YYMM = a1CardForm.A1_YYMM;
                card.A1_NN = a1CardForm.A1_NN;
                card.SetApplicationType();
                card.A1_WYYMMDD = a1CardForm.A1_WYYMMDD;
                card.A1_AYYMMDD = a1CardForm.A1_AYYMMDD;
                card.A1_STATUS = a1CardForm.A1_STATUS;
                card.A1_SHEETNO = a1CardForm.A1_SHEETNO;
                card.A1_SOURCE = a1CardForm.A1_SOURCE;
                card.A1_WDate = a1CardForm.A1_WDate.ToLocalTime();
                card.A1_ADate = a1CardForm.A1_ADate;
                card.AddSigners = a1CardForm.AddSigners;
                if (a1CardForm.CreatedTime != null)
                {
                    DateTime dTemp = (DateTime)a1CardForm.CreatedTime;
                    card.CreatedTime = dTemp.ToLocalTime();
                }
                else
                {
                    card.CreatedTime = a1CardForm.A1_WDate;
                }

                if (card.CreatedTime > card.A1_WDate)
                {
                    card.A1_WDate = card.CreatedTime;
                }

                if (a1CardForm.FilledTime != null)
                {
                    DateTime dTemp = (DateTime)a1CardForm.FilledTime;
                    card.FilledTime = dTemp.ToLocalTime();
                }
                else
                {
                    card.FilledTime = a1CardForm.A1_WDate;
                }

                ////card.UpdatedEmpNo = a1CardForm.UpdatedEmpNo;
                ////card.UpdatedName = a1CardForm.UpdatedName;
                ////card.UpdatedTime = a1CardForm.UpdatedTime;
                ////card.UpdatedIP = a1CardForm.UpdatedIP;
                ////card.UpdatedHost = a1CardForm.UpdatedHost;
                foreach (A1CardForm cardForm in a1CardForms)
                {
                    A1CardDetail detail = new A1CardDetail();
                    detail.ID = a1CardForm.ID;
                    detail.A1_PROJNO = cardForm.A1_PROJNO;
                    detail.A1_DDHH = cardForm.A1_DDHH;
                    detail.A1_HOUR = cardForm.A1_HOUR;
                    detail.A1_SERIALNO = cardForm.A1_SERIALNO;
                    card.Details.Add(detail);
                }
            }
            return card;
        }

        /// <summary>加入正常工作卡</summary>
        /// <param name="creatorId">填表人 員工編號</param>
        /// <param name="a1Card">正常工作卡</param>
        /// <param name="ipAddress">IP Address</param>
        /// <param name="hostname">Host name</param>
        /// <returns>Form Guid</returns>
        public async Task<(Guid?, string)> AddA1Card(string creatorId, A1Card a1Card, string ipAddress,
            string hostname)
        {
            Guid? guid = null;
            string errorMessage = string.Empty;
            if (a1Card.Details.Count == 0)
            {
                errorMessage = "請填報計畫";
                return (guid, errorMessage);
            }
            await _semaphore.WaitAsync();
            try
            {
                if (!a1Card.EasyEquals(_LastCard))
                {
                    Form form;
                    (form, errorMessage) = FillFormAndCheck(creatorId, a1Card, ipAddress, hostname);
                    if (errorMessage == string.Empty)
                    {
                        (guid, errorMessage) = SubmitForm(a1Card, form);
                    }
                }
                else
                {
                    errorMessage = "重覆送出正常工作卡";
                }
            }
            catch (Exception ex)
            {
                errorMessage = AttendanceParameters.SubmitErrorMessage;
                string formJson = JsonConvert.SerializeObject(a1Card, Formatting.Indented);
                _logger.LogError(ex, "Action: {Action} 發生錯誤，錯誤訊息: {Message} {StackTrace}，內容為 {FormJson}", nameof(AddA1Card), ex.Message, ex.StackTrace, formJson);
                Console.WriteLine($"{DateTime.Now} AddA1Card 發生錯誤 {ex.Message} {ex.StackTrace}");
            }
            finally
            {
                _semaphore.Release();
            }
            return (guid, errorMessage);
        }

        /// <summary>依照表單資料補足三卡資料 A1Card/B1Card/B1CardApp/C1Card</summary>
        /// <param name="form"></param>
        /// <param name="card">The card.</param>
        /// <returns></returns>
        public bool AmendCard(Form form, CardBase card)
        {
            A1Card a1Card = (A1Card)card;

            foreach (A1CardDetail detail in a1Card.Details)
            {
                detail.A1_DDHH = CardUtility.TailFillZero(detail.A1_DDHH, 11);
                a1Card.A1_WDate = a1Card.A1_WDate.ToLocalTime();
                a1Card.A1_SHEETNO = form.FormNo;
            }
            a1Card.SetApplicationType();
            return true;
        }

        /// <summary>
        /// 是否能結案抽單 (管理員抽單)
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        public string CanClosedWithdraw(CardBase card)
        {
            A1Card a1Card = (A1Card)card;
            if (a1Card.A1_STATUS == (int)FormStatus.Withdraw)
            {
                return "表單已抽單";
            }
            if (a1Card.A1_STATUS == (int)FormStatus.Processing)
            {
                return "表單進行中";
            }
            return string.Empty;
        }

        /// <summary>
        /// 是否能抽單 (正常情況)
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        public string CanWithdraw(CardBase card)
        {
            return string.Empty;
        }

        /// <summary>結案</summary>
        /// <param name="card">The card.</param>
        /// <param name="formStatus">The form status.</param>
        /// <param name="updateDto">更新資料物件</param>
        public void Finish(CardBase card, FormStatus formStatus, Updater updateDto)
        {
            Update(card, updateDto);
            A1Card a1Card = (A1Card)card;
            a1Card.A1_STATUS = (int)formStatus;
            a1Card.A1_ADate = updateDto.UpdatedTime;
        }

        /// <summary>
        /// 取得卡
        /// </summary>
        /// <param name="formUID">Form UUID</param>
        /// <returns>卡</returns>
        public CardBase? GetCard(Guid formUID)
        {
            CardBase card = new A1Card();
            DataTable dt = _a1CardDao.GetA1Cards(formUID);
            if (dt != null && dt.Rows.Count > 0)
            {
                List<A1CardDto> a1Cards = SqlHelper.ConvertDataTable<A1CardDto>(dt);
                card = A1CardDtos2A1Card(a1Cards);
            }
            return card;
        }

        /// <summary>取得某段時間內卡，不含流程與附件等</summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <returns>卡 CardsDto 物件 List</returns>
        public List<CardBase> GetCards(DateTime startDate, DateTime endDate)
        {
            List<CardBase> cards = new List<CardBase>();
            DataTable dt = _a1CardDao.GetA1Cards(startDate, endDate);
            if (dt != null && dt.Rows.Count > 0)
            {
                List<A1CardDto> a1CardDtos = SqlHelper.ConvertDataTable<A1CardDto>(dt);
                var formNos = a1CardDtos.Select(x => x.A1_SHEETNO).Distinct();
                foreach (var formNo in formNos)
                {
                    var result = (from a1CardDto in a1CardDtos
                                  where a1CardDto.A1_SHEETNO == formNo
                                  select a1CardDto).ToList();
                    A1Card card = A1CardDtos2A1Card(result);
                    cards.Add(card);
                }
            }
            return cards;
        }

        /// <summary>取得表單及旬卡</summary>
        /// <param name="form">The form.</param>
        /// <param name="userId">The user identifier.</param>
        /// <returns>表單及旬卡 FormCardsDto 物件</returns>
        public FormCard GetFormCard(Form form, string userId)
        {
            FormCard formCard = new FormCard();
            formCard.ID = form.ID;
            formCard.FormID = form.FormID;
            formCard.FormUID = form.FormUID;
            formCard.FormNo = form.FormNo;
            formCard.FormSubject = form.FormSubject;
            formCard.FormInfo = form.FormInfo;
            formCard.EmpNo = form.EmpNo;
            formCard.EmpName = form.EmpName;
            formCard.DeptNo = form.DeptNo;
            formCard.DeptSName = form.DeptSName;
            formCard.TeamID = form.TeamID;
            formCard.TeamCName = form.TeamCName;
            formCard.RankNo = form.RankNo;
            formCard.RankName = form.RankName;
            formCard.JobNo = form.JobNo;
            formCard.JobName = form.JobName;
            formCard.ContentStartTime = form.ContentStartTime;
            formCard.ContentEndTime = form.ContentEndTime;
            formCard.CreatedEmpNo = form.CreatedEmpNo;
            formCard.CreatedName = form.CreatedName;
            formCard.FilledTime = form.FilledTime;
            formCard.CreatedTime = form.CreatedTime;
            formCard.CreatedIP = form.CreatedIP;
            formCard.CreatedHost = form.CreatedHost;
            formCard.StartTime = form.StartTime;
            formCard.EndTime = form.EndTime;
            formCard.FormStatus = form.FormStatus;
            formCard.FormStatusName = _formBo.GetFormStatusName(form.FormStatus);
            formCard.TotalSteps = form.TotalSteps;
            formCard.CurrentStep = form.CurrentStep;
            formCard.UpdatedEmpNo = form.UpdatedEmpNo;
            formCard.UpdatedName = form.UpdatedName;
            formCard.UpdatedTime = form.UpdatedTime;
            formCard.UpdatedIP = form.UpdatedIP;
            formCard.UpdatedHost = form.UpdatedHost;
            formCard.AddedSigner = _formBo.AddSignersAddName(form.AddedSigner);
            formCard.Attachments = form.Attachments;
            formCard.Flows = form.Flows;
            formCard.Hours = 0;

            A1Card? a1Card = (A1Card?)GetCard(form.FormUID);
            if (a1Card == null)
            {
                throw new ArgumentNullException($"A1Card 讀不到 {form.FormUID}");
            }
            A1CardView card = new A1CardView();
            card.FormUID = a1Card.FormUID;
            card.A1_NN = a1Card.A1_NN;
            card.A1_SOURCE = a1Card.A1_SOURCE;
            card.A1_STATUS = a1Card.A1_STATUS;
            card.A1_YYMM = a1Card.A1_YYMM;
            card.A1_AYYMMDD = a1Card.A1_AYYMMDD;
            card.A1_EMPNO = a1Card.A1_EMPNO;
            card.A1_SHEETNO = a1Card.A1_SHEETNO;
            card.A1_WYYMMDD = a1Card.A1_WYYMMDD;
            card.FilledTime = form.FilledTime;
            card.CreatedTime = form.CreatedTime;
            card.UpdatedEmpNo = a1Card.UpdatedEmpNo;
            card.UpdatedName = a1Card.UpdatedName;
            card.UpdatedIP = a1Card.UpdatedIP;
            card.UpdatedHost = a1Card.UpdatedHost;
            card.AddSigners = formCard.AddedSigner;

            foreach (A1CardDetail detail in a1Card.Details)
            {
                A1CardViewDetail viewDetail = new A1CardViewDetail();
                viewDetail.ID = detail.ID;
                viewDetail.A1_HOUR = detail.A1_HOUR;
                viewDetail.A1_PROJNO = detail.A1_PROJNO;
                string? projectName = _projectBo.GetProjectName(detail.A1_PROJNO);
                if (projectName != null)
                {
                    viewDetail.ProjectName = projectName;
                }
                viewDetail.A1_SERIALNO = detail.A1_SERIALNO;
                viewDetail.A1_DDHH = detail.A1_DDHH;

                int year = 1911 + int.Parse(card.A1_YYMM.Substring(0, 3));
                int month = int.Parse(card.A1_YYMM.Substring(3, 2));
                int tenDays = int.Parse(card.A1_NN.ToString());
                DataTable dtWorkdays = _workdayBo.GetWorkdaysInTendays(year, month, tenDays);
                List<Workday> workdays = SqlHelper.ConvertDataTable<Workday>(dtWorkdays);
                char[] charArray = detail.A1_DDHH.ToCharArray();

                for (int i = 0; i < workdays.Count; i++)
                {
                    A1CardViewDetail.FillDayDetail fillDay = new A1CardViewDetail.FillDayDetail();
                    fillDay.date = workdays[i].WorkDate;
                    fillDay.weekday = workdays[i].WeekDay;
                    fillDay.hour = int.Parse(charArray[i].ToString());
                    fillDay.day = fillDay.date.Day;
                    fillDay.rocWeekDay = CardUtility.RocWeekDayChar(fillDay.weekday);
                    viewDetail.DayDetails.Add(fillDay);
                }
                card.Details.Add(viewDetail);
                formCard.Hours += detail.A1_HOUR;
            }
            formCard.Attachments = form.Attachments;
            formCard.Card = card;
            return formCard;
        }

        /// <summary>取得某段時間內表單及卡</summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <returns>表單及卡 FormCardsDto 物件 List</returns>
        public List<FormCard> GetFormCards(DateTime startDate, DateTime endDate)
        {
            DataTable dtCardForms = _a1CardDao.GetA1CardsForms(startDate, endDate);
            List<FormCard> formCards = GetFormCards(startDate, endDate, dtCardForms);
            return formCards;
        }

        /// <summary>取得某段時間內表單及卡</summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單及卡 FormCardsDto 物件 List</returns>
        public List<FormCard> GetFormCards(DateTime startDate, DateTime endDate, string projNo)
        {
            DataTable dtCardForms = _a1CardDao.GetA1CardsForms(startDate, endDate, projNo);
            List<FormCard> formCards = GetFormCards(startDate, endDate, dtCardForms);
            return formCards;
        }

        /// <summary>
        /// 取得總時數
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        public int GetHours(CardBase card)
        {
            int hours = 0;
            A1Card a1Card = (A1Card)card;
            foreach (A1CardDetail detail in a1Card.Details)
            {
                hours += detail.A1_HOUR;
            }
            return hours;
        }

        /// <summary>
        /// 取得提醒資訊 (正常工作卡無)
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        public (int, string?) GetListRemindMessage(CardBase card)
        {
            string empNo = string.Empty;

            if (card is A1Card)
            {
                A1Card a1Card = (A1Card)card;
                empNo = a1Card.A1_EMPNO;
            }
            if (card is A1CardView)
            {
                A1CardView a1Card = (A1CardView)card;
                empNo = a1Card.A1_EMPNO;
            }

            if (!_employeeBo.IsEmployee(empNo))
            {
                string empName = _employeeBo.GetEmployeeName(empNo);
                string errorMessage = @"申請人" + AttendanceParameters.IsOffServiceStaff;
                return (99, errorMessage);
            }
            else
            {
                return (0, null);
            }
        }

        /// <summary>
        /// 取得特定月份卡，依表單日期為準，非填表時間
        /// </summary>
        /// <param name="empNo">填表員工編號</param>
        /// <param name="date">日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns></returns>
        public List<CardBase> GetUserCardsMonth(string empNo, DateTime date, int? status = null)
        {
            List<CardBase> cards = new List<CardBase>();
            DataTable dtA1Cards = _a1CardDao.GetA1CardMonth(empNo, date, status);
            if (dtA1Cards != null && dtA1Cards.Rows.Count > 0)
            {
                List<A1CardDto> a1CardDtos = SqlHelper.ConvertDataTable<A1CardDto>(dtA1Cards);
                foreach (A1CardDto a1CardDto in a1CardDtos)
                {
                    A1Card a1Card = A1CardDto2A1Card(a1CardDto);
                    a1Card.SetApplicationType();
                    cards.Add(a1Card);
                }
            }
            return cards;
        }

        /// <summary>
        ///取得 表單關係人 某月份已填正常工時單，依表單年月為準，非填表時間
        /// </summary>
        /// <param name="empNo">填表員工編號</param>
        /// <param name="year">表單年</param>
        /// <param name="month">表單月</param>
        /// <returns></returns>
        public List<FormCard> GetUserSentFormCardsYearMonth(string empNo, int year, int month)
        {
            List<FormCard> formCards = new List<FormCard>();
            DataTable dtCardForms = _a1CardDao.GetSentFormA1CardYearMonth(empNo, year, month);
            if (dtCardForms != null && dtCardForms.Rows.Count > 0)
            {
                DateTime startDate = DateTime.MaxValue;
                DateTime endDate = DateTime.MinValue;
                foreach (DataRow dr in dtCardForms.Rows)
                {
                    DateTime createTime = (DateTime)dr["CreatedTime"];
                    if (createTime < startDate)
                    {
                        startDate = createTime;
                    }
                    if (createTime > endDate)
                    {
                        endDate = createTime;
                    }
                }
                formCards = GetFormCards(startDate, endDate, dtCardForms);
            }
            return formCards;
        }

        /// <summary>
        /// 檢查某員工是否已填某旬之有效（簽核中及同意）正常工作卡
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="chineseYearMonth">民國年月(yyymm)</param>
        /// <param name="tenDays">旬別(1:上旬、2:中旬、3:下旬)</param>
        /// <returns>若未填則傳回 {FormUID: null, FormNo: null}</returns>
        public FormFilled IsFilled(string empNo, string chineseYearMonth, char tenDays)
        {
            FormFilled filled = new FormFilled();
            DataTable dt = _a1CardDao.IsFilledA1Card(empNo, chineseYearMonth, tenDays);
            if (dt != null && dt.Rows.Count > 0)
            {
                if (dt.Rows[0]["FormUID"] != DBNull.Value)
                {
                    filled.FormUID = (Guid)dt.Rows[0]["FormUID"];
                }
                filled.FormNo = (string)dt.Rows[0]["A1_SHEETNO"];
                int iApproved = (int)(byte)dt.Rows[0]["A1_STATUS"];
                if (iApproved == (int)FormStatus.Agree)
                {
                    filled.IsApproved = true;
                }
                else
                {
                    if (iApproved == (int)FormStatus.Deny || iApproved == (int)FormStatus.Withdraw)
                    {
                        filled = new FormFilled();
                    }
                }
            }
            return filled;
        }

        /// <summary>轉換為Stored Procedure所需的 User Defined Table</summary>
        /// <param name="card">三卡</param>
        /// <param name="isAdd">是否為新增</param>
        /// <returns></returns>
        public List<DataTable> ToUserDefinedDataTables(CardBase card, bool isAdd = true)
        {
            List<A1CardDto> list = new List<A1CardDto>();
            A1Card a1Card = (A1Card)card;
            foreach (A1CardDetail detail in a1Card.Details)
            {
                A1CardDto a1CardDto = new A1CardDto();
                a1CardDto.ID = detail.ID;
                a1CardDto.FormUID = a1Card.FormUID;
                a1CardDto.A1_EMPNO = a1Card.A1_EMPNO;
                a1CardDto.A1_YYMM = a1Card.A1_YYMM;
                a1CardDto.A1_NN = a1Card.A1_NN;
                a1CardDto.A1_PROJNO = detail.A1_PROJNO;
                a1CardDto.A1_DDHH = detail.A1_DDHH;
                a1CardDto.A1_HOUR = detail.A1_HOUR;
                a1CardDto.A1_WYYMMDD = a1Card.A1_WYYMMDD;
                a1CardDto.A1_AYYMMDD = a1Card.A1_AYYMMDD;
                a1CardDto.A1_STATUS = a1Card.A1_STATUS;
                a1CardDto.A1_SHEETNO = a1Card.A1_SHEETNO;
                a1CardDto.A1_SERIALNO = detail.A1_SERIALNO;
                a1CardDto.A1_SOURCE = a1Card.A1_SOURCE;
                a1CardDto.A1_WDate = a1Card.A1_WDate;
                a1CardDto.A1_ADate = a1Card.A1_ADate;
                a1CardDto.UpdatedEmpNo = a1Card.UpdatedEmpNo;
                a1CardDto.UpdatedName = a1Card.UpdatedName;
                if (a1Card.UpdatedTime != DateTime.MinValue && a1Card.UpdatedTime != DateTime.MaxValue)
                {
                    a1CardDto.UpdatedTime = a1Card.UpdatedTime;
                }
                else
                {
                    a1CardDto.UpdatedTime = a1Card.A1_WDate;
                }
                a1CardDto.UpdatedIP = a1Card.UpdatedIP;
                a1CardDto.UpdatedHost = a1Card.UpdatedHost;
                list.Add(a1CardDto);
            }
            DataTable dt = SqlHelper.CreateDataTable<A1CardDto>(list);
            dt.TableName = "A1Card";
            List<DataTable> dataTables = new List<DataTable> { dt };
            return dataTables;
        }

        /// <summary>更新Card</summary>
        /// <param name="card">The card.</param>
        /// <param name="updateDto">更新資料物件</param>
        public void Update(CardBase card, Updater updateDto)
        {
            A1Card a1Card = (A1Card)card;
            a1Card.A1_AYYMMDD = CardUtility.RocChineseDateString(updateDto.UpdatedTime).Replace("/", string.Empty);
            a1Card.A1_ADate = updateDto.UpdatedTime;
            if (updateDto.UpdatedIP != null)
            {
                a1Card.UpdatedIP = updateDto.UpdatedIP;
                a1Card.UpdatedHost = updateDto.UpdatedHost;
            }
            a1Card.UpdatedTime = updateDto.UpdatedTime;
            a1Card.UpdatedEmpNo = updateDto.UpdatedEmpNo;
            a1Card.UpdatedName = updateDto.UpdatedName;
        }

        /// <summary>
        /// 更新A1Card，限管理員使用，僅允許修改計畫時數
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="cards"></param>
        /// <param name="ipAddress"></param>
        /// <param name="hostname"></param>
        /// <returns></returns>
        public CardCheckResult UpdateA1CardProjectNo(string userId, List<A1CardForm> cards, string ipAddress, string hostname)
        {
            CardCheckResult result = AttendanceParameters.ResultOk;
            Guid formUid = cards[0].FormUID;
            DataTable dt = _a1CardDao.GetA1Cards(formUid);
            DateTime updatedTime = DateTime.Now;
            foreach (A1CardForm card in cards)
            {
                card.A1_DDHH = CardUtility.TailFillZero(card.A1_DDHH, 11);
            }
            foreach (A1CardForm a1CardForm in cards)
            {
                DataRow[] rows = dt.Select($"ID={a1CardForm.ID}");
                if (rows != null && rows.Length == 1)
                {
                    List<A1CardDto> list = SqlHelper.ConvertDataTable<A1CardDto>(rows);
                    if (list.Any())
                    {
                        A1CardDto a1Card = list.First();
                        if (a1Card.A1_DDHH != a1CardForm.A1_DDHH)
                        {
                            a1Card.A1_DDHH = a1CardForm.A1_DDHH;
                            a1Card.UpdatedEmpNo = userId;
                            a1Card.UpdatedName = "管理者";
                            a1Card.UpdatedIP = ipAddress;
                            a1Card.UpdatedHost = hostname;
                            a1Card.UpdatedTime = updatedTime;
                            char[] chars = a1Card.A1_DDHH.ToCharArray();
                            int projectHours = 0;
                            for (int i = 0; i < chars.Length; i++)
                            {
                                int hours = int.Parse(chars[i].ToString());
                                if (hours > 0)
                                {
                                    projectHours += hours; // 本計畫累計工時
                                }
                            }
                            a1Card.A1_HOUR = projectHours;
                            bool status = _a1CardDao.UpdateA1CardProjectNo(a1Card);
                            if (!status)
                            {
                                result = AttendanceParameters.ResultUpdateA1CardError;
                                break;
                            }
                        }
                    }
                }
            }
            return result;
        }

        /// <summary>
        /// 抽單
        /// </summary>
        /// <param name="card"></param>
        /// <param name="withdraw"></param>
        /// <returns></returns>
        public bool Withdraw(CardBase card, Withdraw withdraw)
        {
            bool ret = false;
            if (withdraw != null)
            {
                A1Card a1Card = (A1Card)card;
                a1Card.A1_STATUS = (int)FormStatus.Withdraw;
                a1Card.A1_AYYMMDD = CardUtility.RocChineseYYYMMDD(withdraw.WithdrawTime);
                a1Card.A1_ADate = withdraw.WithdrawTime;
                ret = true;
            }
            return ret;
        }

    }
}
