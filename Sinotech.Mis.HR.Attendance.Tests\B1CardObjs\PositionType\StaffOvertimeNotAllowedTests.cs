﻿using Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.PositionType;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.Tests
{
    [ExcludeFromCodeCoverage]
    public class StaffOvertimeNotAllowedTests
    {
        private readonly StaffOvertimeNotAllowed _staffOvertimeNotAllowed;

        public StaffOvertimeNotAllowedTests()
        {
            var provider = new MockB1CardDataProvider();
            _staffOvertimeNotAllowed = new StaffOvertimeNotAllowed("12345", DateTime.Now, provider);
        }

        [Fact]
        public void IsOvertimeAllowed_ShouldReturnFalse()
        {
            Assert.False(_staffOvertimeNotAllowed.IsOvertimeAllowed);
        }

    }

}
