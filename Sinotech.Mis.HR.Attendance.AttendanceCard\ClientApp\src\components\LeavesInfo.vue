<template>
  <table class="table table-sm table-bordered caption-top text-center mb-0">
    <caption>
      <span>{{ modelValue?.yearForStatic - 1911 }}</span>
      <span>年度休假統計(統計時間：</span>
      <span>{{ dateToRocString(modelValue?.yearLeaveDateStatic) }}</span>
      <span>)</span>
      <router-link
        v-slot="{ href, navigate }"
        :to="{ name: 'Home' }"
        custom
      >
        <a
          class="ms-1"
          role="button"
          tabindex="0"
          @keydown.enter="onKeydownEditLink(navigate)"
          @keydown.space="onKeydownEditLink(navigate)"
          @click="onClickEditLink($event, href, navigate)"
        >
          more
        </a>
      </router-link>
    </caption>
    <thead class="table-light">
      <tr>
        <th />
        <th>延休假</th>
        <th>特別休息假</th>
        <th>補休假</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>可休時數</td>
        <td>{{ modelValue?.leavesData?.extendedLeaves?.YearAvailableHours ?? 0 }}</td>
        <td>{{ modelValue?.leavesData?.annualLeaves?.YearAvailableHours ?? 0 }}</td>
        <td>{{ modelValue?.leavesData?.compensatoryLeaves?.YearAvailableHours ?? 0 }}</td>
      </tr>
      <tr>
        <td>已休時數</td>
        <td>{{ modelValue?.leavesData?.extendedLeaves?.YearUsedHours ?? 0 }}</td>
        <td>{{ modelValue?.leavesData?.annualLeaves?.YearUsedHours ?? 0 }}</td>
        <td>{{ modelValue?.leavesData?.compensatoryLeaves?.YearUsedHours ?? 0 }}</td>
      </tr>
      <tr>
        <td>剩餘時數</td>
        <td>
          <template v-if="modelValue?.leavesData?.extendedLeaves?.YearAvailableHours === undefined || modelValue?.leavesData?.extendedLeaves?.YearUsedHours === undefined">
            0
          </template>
          <template v-else>
            {{ modelValue?.leavesData?.extendedLeaves?.YearAvailableHours - modelValue?.leavesData?.extendedLeaves?.YearUsedHours }}
          </template>
        </td>
        <td>
          <template v-if="modelValue?.leavesData?.annualLeaves?.YearAvailableHours === undefined || modelValue?.leavesData?.annualLeaves?.YearUsedHours === undefined">
            0
          </template>
          <template v-else>
            {{ modelValue?.leavesData?.annualLeaves?.YearAvailableHours - modelValue?.leavesData?.annualLeaves?.YearUsedHours }}
          </template>
        </td>
        <td>
          <template v-if="modelValue?.leavesData?.compensatoryLeaves?.YearAvailableHours === undefined || modelValue?.leavesData?.compensatoryLeaves?.YearUsedHours === undefined">
            0
          </template>
          <template v-else>
            {{ modelValue?.leavesData?.compensatoryLeaves?.YearAvailableHours - modelValue?.leavesData?.compensatoryLeaves?.YearUsedHours }}
          </template>
        </td>
      </tr>
    </tbody>
  </table>
</template>
<script setup lang="ts">
import { dateToRocString } from '../api/appFunction'
import type { NavigationFailure } from 'vue-router'
import type { LeaveInfoApiType } from '../api/appType'

defineProps<{
  modelValue: {
    yearForStatic: number,
    yearLeaveDateStatic: Date
    leavesData: {
      annualLeaves: LeaveInfoApiType | undefined,
      compensatoryLeaves: LeaveInfoApiType | undefined,
      extendedLeaves: LeaveInfoApiType | undefined
    }
  }
}>()

const onKeydownEditLink = (navigate: (e?: MouseEvent) => Promise<void | NavigationFailure>): void => {
  navigate()
}

const onClickEditLink = (event: MouseEvent, href: string, navigate: (e?: MouseEvent) => Promise<void | NavigationFailure>): void => {
  if (event.ctrlKey === true || event.shiftKey === true) {
    window.open(href, '_blank')
  } else {
    navigate()
  }
}
</script>