﻿using Sinotech.Mis.Helpers;
using System;
using System.Collections.Generic;
using System.Data;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 通知DTO，欄位順序不可更改
    /// </summary>
    public class Notification
    {
        /// <summary>
        /// 流水號
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public int ID { get; set; }

        /// <summary>
        /// 表單識別號
        /// </summary>
        /// <value>
        /// The form uuid.
        /// </value>
        public Guid FormUID { get; set; }

        /// <summary>
        /// 被通知者員工編號
        /// </summary>
        /// <value>
        /// The notify emp no.
        /// </value>
        public string NotifyEmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 被通知者姓名
        /// </summary>
        /// <value>
        /// The name of the notify.
        /// </value>
        public string NotifyName { get; set; } = string.Empty;

        /// <summary>
        /// 通知日期時間
        /// </summary>
        /// <value>
        /// The notify time.
        /// </value>
        public DateTime NotifyTime { get; set; }

        /// <summary>
        /// 通知讀取日期時間
        /// </summary>
        /// <value>
        /// The view time.
        /// </value>
        public DateTime? ViewTime { get; set; }

        /// <summary>
        /// 轉為資料庫儲存之格式
        /// </summary>
        /// <param name="flows">The flows.</param>
        /// <returns></returns>
        public static DataTable ToUserDefinedDataTable(List<Notification> flows)
        {
            DataTable dt = SqlHelper.CreateDataTable<Notification>(flows);
            dt.TableName = "Notification";
            dt.Columns.Remove("ID");
            return dt;
        }
    }
}
