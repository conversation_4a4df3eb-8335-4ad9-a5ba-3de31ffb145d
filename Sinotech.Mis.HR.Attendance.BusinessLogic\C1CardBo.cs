﻿using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sinotech.Mis.Common;
using Sinotech.Mis.Helpers;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs;
using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.DataAccess.Interfaces;
using Sinotech.Mis.HR.Attendance.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Runtime.Caching;
using System.Threading;
using System.Threading.Tasks;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic
{
    /// <summary>
    /// 請假卡 商業物件
    /// </summary>
    public class C1CardBo : IC1CardBo
    {
        private static ObjectCache _cache = MemoryCache.Default;
        public static readonly object _cacheLock = new object();
        private readonly IAttendanceBo _attendanceBo;
        private readonly IC1CardDao _c1CardDao;
        private readonly IEmployeeBo _employeeBo;
        private readonly IFormBo _formBo;
        private readonly IFormFlowBo _formFlowBo;
        private readonly ILogger<IC1CardBo> _logger;
        // 避免重覆送出鎖定物件
        private static readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);
        private readonly IWorkdayBo _workdayBo;

        /// <summary>
        /// C1CardBo 建構元
        /// </summary>
        /// <param name="attendanceBo"></param>
        /// <param name="c1CardDao"></param>
        /// <param name="employeeBo"></param>
        /// <param name="formBo"></param>
        /// <param name="formFlowBo"></param>
        /// <param name="workdayBo"></param>
        public C1CardBo(IAttendanceBo attendanceBo, IC1CardDao c1CardDao, IEmployeeBo employeeBo,
            IFormBo formBo, IFormFlowBo formFlowBo, ILogger<IC1CardBo> logger, IWorkdayBo workdayBo)
        {
            _attendanceBo = attendanceBo;
            _c1CardDao = c1CardDao;
            _employeeBo = employeeBo;
            _formBo = formBo;
            _formFlowBo = formFlowBo;
            _logger = logger;
            _workdayBo = workdayBo;
        }

        /// <summary>
        /// 調整請假單截止時間，僅計算工時
        /// </summary>
        /// <param name="endDate">請假結束時間</param>
        /// <param name="flexibleDepartureAfter">彈性下班時間</param>
        /// <param name="middayBreakStart">中午休息開始時間</param>
        /// <param name="middayBreakEnd">中午休息結束時間</param>
        /// <param name="morningRestStart">上午休息開始時間</param>
        /// <param name="morningRestEnd">上午休息結束時間</param>
        /// <param name="afternoonRestStart">下午休息開始時間</param>
        /// <param name="afternoonRestEnd">下午休息結束時間</param>
        /// <returns>調整後的請假結束時間</returns>
        private static DateTime AdjustTakeLeaveEndDate(DateTime endDate, DateTime flexibleDepartureAfter, DateTime middayBreakStart, DateTime middayBreakEnd, DateTime morningRestStart, DateTime morningRestEnd, DateTime afternoonRestStart, DateTime afternoonRestEnd)
        {
            // 如果請假結束時間超過彈性下班時間，將結束時間調整為彈性下班時間
            if (endDate > flexibleDepartureAfter)
            {
                endDate = flexibleDepartureAfter;
            }
            // 如果請假結束時間落在中午休息時間內，將結束時間調整為中午休息開始時間
            else if (endDate > middayBreakStart && endDate <= middayBreakEnd)
            {
                endDate = middayBreakStart;
            }
            // 2025/06 更新: 依照 A-IS-P0204-001-11400004 文，早上與下午休息時間不再計算
            // https://sinodams.sinotech.org.tw/SinoSign/Form/Edit/37c5c9f0-7910-4000-8000-000000000000
            // // 如果請假結束時間落在上午休息時間內，將結束時間調整為上午休息開始時間
            // else if (endDate > morningRestStart && endDate <= morningRestEnd)
            // {
            //     endDate = morningRestStart;
            // }
            // // 如果請假結束時間落在下午休息時間內，將結束時間調整為下午休息開始時間
            // else if (endDate > afternoonRestStart && endDate <= afternoonRestEnd)
            // {
            //     endDate = afternoonRestStart;
            // }

            // 回傳調整後的請假結束時間
            return endDate;
        }

        /// <summary>
        /// 調整請假單開始時間，僅計算工時
        /// </summary>
        /// <param name="startDate">請假開始時間</param>
        /// <param name="arrivalTime">標準上班時間</param>
        /// <param name="middayBreakStart">中午休息開始時間</param>
        /// <param name="middayBreakEnd">中午休息結束時間</param>
        /// <param name="morningRestStart">上午休息開始時間</param>
        /// <param name="morningRestEnd">上午休息結束時間</param>
        /// <param name="afternoonRestStart">下午休息開始時間</param>
        /// <param name="afternoonRestEnd">下午休息結束時間</param>
        /// <returns></returns>
        private static DateTime AdjustTakeLeaveStartDate(DateTime startDate, DateTime arrivalTime, DateTime middayBreakStart, DateTime middayBreakEnd, DateTime morningRestStart, DateTime morningRestEnd, DateTime afternoonRestStart, DateTime afternoonRestEnd)
        {
            // 檢查請假開始時間是否早於上班時間
            if (startDate < arrivalTime)
            {
                // 若請假開始時間早於上班時間，則將開始時間調整為上班時間
                // 避免計算到非工作時間的請假工時
                startDate = arrivalTime;
            }
            // 檢查請假開始時間是否落在中午休息時間區間內
            else if (startDate >= middayBreakStart && startDate < middayBreakEnd) // 若起介於中午休息時間
            {
                // 若請假開始時間落在中午休息時間內，調整為中午休息結束時間
                // 確保不會計算到休息時間的請假工時
                startDate = middayBreakEnd;
            }
            // 2025/06 更新: 依照文號 A-IS-P0204-001-11400004 ，早上與下午休息時間不再計算
            // https://sinodams.sinotech.org.tw/SinoSign/Form/Edit/37c5c9f0-7910-4000-8000-000000000000
            // 檢查請假開始時間是否落在早上休息時間區間內
            // else if (startDate >= morningRestStart && startDate < morningRestEnd) // 若起介於早上休息時間
            // {
            //     // 若請假開始時間落在早上休息時間內，調整為早上休息結束時間
            //     // 確保不會計算到休息時間的請假工時
            //     startDate = morningRestEnd;
            // }
            // 檢查請假開始時間是否落在下午休息時間區間內
            // else if (startDate >= afternoonRestStart && startDate < afternoonRestEnd) // 若起介於下午休息時間
            // {
            //     // 若請假開始時間落在下午休息時間內，調整為下午休息結束時間
            //     // 確保不會計算到休息時間的請假工時
            //     startDate = afternoonRestEnd;
            // }

            // 返回調整後的請假開始時間
            return startDate;
        }

        /// <summary>
        /// 將 C1CardDto 轉為List<C1CardDto> ，若C1CardDto有跨月時依月份切割
        /// </summary>
        /// <param name="c1CardDto"></param>
        /// <param name="isCalendarDay">是否以日曆天計算</param>
        /// <returns></returns>
        private List<C1CardDto> C1CardDto2ListC1CardDtos(C1CardDto c1CardDto, bool isCalendarDay)
        {
            // 本方法只能在跨月份時使用，同月份時會錯

            List<(DateTime, DateTime)> result;
            if (isCalendarDay)
            {
                result = _workdayBo.SplitDateByMonthCalendarDay(c1CardDto.C1_StartDate, c1CardDto.C1_EndDate, 1); //只計算正常班
            }
            else
            {
                result = _workdayBo.SplitDateByMonthWorkday(c1CardDto.C1_StartDate, c1CardDto.C1_EndDate, c1CardDto.C1_EMPNO);
            }
            List<C1CardDto> list = SplitC1CardDto(c1CardDto, result, isCalendarDay);
            return list;
        }

        /// <summary>
        /// 將 C1Card 轉為 C1CardDto 物件
        /// </summary>
        /// <param name="c1Card"></param>
        /// <returns>C1CardDto 物件</returns>
        private static C1CardDto C1CardToC1CardDto(C1Card c1Card)
        {
            C1CardDto c1CardDto = new C1CardDto();
            c1CardDto.ID = c1Card.ID;

            c1CardDto.FormUID = c1Card.FormUID;
            c1CardDto.C1_EMPNO = c1Card.EmpNo;
            c1CardDto.C1_YYMM = CardUtility.RocChineseYYYMM(c1Card.StartDate);
            c1CardDto.C1_SDD = c1Card.StartDate.ToString("dd");
            c1CardDto.C1_SHH = c1Card.StartDate.ToString("HH");
            c1CardDto.C1_SMM = c1Card.StartDate.ToString("mm");
            c1CardDto.C1_EDD = c1Card.EndDate.ToString("dd");
            c1CardDto.C1_EHH = c1Card.EndDate.ToString("HH");
            c1CardDto.C1_EMM = c1Card.EndDate.ToString("mm");
            c1CardDto.C1_HOUR = c1Card.Hours;
            c1CardDto.C1_CODE = ((int)c1Card.LeaveNumber).ToString("00");
            if (c1Card.LeaveSubNumber != 0) // 為零時 C1_CODE2 維持 string.Empty
            {
                c1CardDto.C1_CODE2 = c1Card.LeaveSubNumber.ToString("00");
            }
            else
            {
                c1CardDto.C1_CODE2 = "  ";
            }

            c1CardDto.C1_DeadlineStartDate = c1Card.ExpirationStartDate;
            c1CardDto.C1_DeadlineEndDate = c1Card.ExpirationEndDate;
            c1CardDto.C1_RelationSheetNo = c1Card.RelatedFormNumber;
            c1CardDto.C1_LeaveMaximum = c1Card.LeaveMaximum;
            c1CardDto.C1_LeaveUnit = c1Card.LeaveUnit;
            c1CardDto.C1_PrjNo = c1Card.ProjectNumber;
            c1CardDto.C1_Location = c1Card.Location;
            c1CardDto.C1_Reason = c1Card.Reason;
            c1CardDto.C1_Agent = c1Card.Deputy;
            c1CardDto.C1_WYYMMDD = CardUtility.RocChineseYYYMMDD(c1Card.WDate);
            if (c1Card.ADate != null && c1Card.ADate != DateTime.MinValue && c1Card.ADate != DateTime.MaxValue)
            {
                c1CardDto.C1_AYYMMDD = CardUtility.RocChineseYYYMMDD((DateTime)c1Card.ADate);
            }
            c1CardDto.C1_STATUS = c1Card.Status;
            c1CardDto.C1_OVER = c1Card.OverPermittedHours ? "Y" : "N";
            c1CardDto.C1_SHEETNO = c1Card.FormNumber;
            c1CardDto.C1_StartDate = c1Card.StartDate;
            c1CardDto.C1_EndDate = c1Card.EndDate;
            c1CardDto.C1_WDate = c1Card.WDate;
            if (c1Card.ADate != null && c1Card.ADate != DateTime.MinValue && c1Card.ADate != DateTime.MaxValue)
            {
                c1CardDto.C1_ADate = c1Card.ADate;
            }
            c1CardDto.UpdatedEmpNo = c1Card.UpdatedEmpNo;
            c1CardDto.UpdatedName = c1Card.UpdatedName;
            c1CardDto.UpdatedTime = c1Card.UpdatedTime;
            c1CardDto.UpdatedIP = c1Card.UpdatedIP;
            c1CardDto.UpdatedHost = c1Card.UpdatedHost;
            if (c1Card.EventDate != null && c1Card.EventDate != DateTime.MinValue && c1Card.EventDate != DateTime.MaxValue)
            {
                c1CardDto.C1_EventDate = c1Card.EventDate;
            }
            // 非事件假均將不必要欄位清空
            switch (c1Card.LeaveNumber)
            {
                case LeaveKindEnum.BusinessInjuryLeave: // 公傷假
                    c1CardDto.C1_DeadlineStartDate = null;
                    c1CardDto.C1_DeadlineEndDate = null;
                    c1CardDto.C1_RelationSheetNo = "";
                    c1CardDto.C1_LeaveMaximum = null;
                    c1CardDto.C1_LeaveUnit = null;
                    break;
                case LeaveKindEnum.MarriageLeave: // 婚假
                case LeaveKindEnum.ObstetricInspectionLeave: // 產檢假
                case LeaveKindEnum.MaternityLeave: // 產假
                case LeaveKindEnum.FuneralLeave: // 喪假
                case LeaveKindEnum.PaternityLeave: // 陪產檢及陪產假
                    break;
                default:
                    c1CardDto.C1_EventDate = null;
                    c1CardDto.C1_DeadlineStartDate = null;
                    c1CardDto.C1_DeadlineEndDate = null;
                    c1CardDto.C1_RelationSheetNo = "";
                    c1CardDto.C1_LeaveMaximum = null;
                    c1CardDto.C1_LeaveUnit = null;
                    break;
            }
            return c1CardDto;
        }

        /// <summary>
        /// 依照相關單號檢查是否能抽單
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        private string CheckRelatedNumber(CardBase card)
        {
            string message = string.Empty;
            C1Card c1Card = (C1Card)card;
            if (FormNumberWasRelated(c1Card.FormNumber))
            {
                message = AttendanceParameters.Leave_Used_Other_Related_Error;
            }
            return message;
        }

        /// <summary>
        /// 同一天只顯示日期 的 FormInfo
        /// </summary>
        /// <param name="date">請假日期</param>
        /// <returns>FormInfo</returns>
        private string FormInfoDate(DateTime date)
        {
            string formInfo = $"{CardUtility.RocChineseDateString(date)}";
            return formInfo;
        }

        /// <summary>
        /// 不同天只顯示起始截止日期 的 FormInfo
        /// </summary>
        /// <param name="startDate">起始日期時間</param>
        /// <param name="endDate">截止日期時間</param>
        /// <returns>FormInfo</returns>
        private string FormInfoDateOnly(DateTime startDate, DateTime endDate)
        {
            string formInfo = $"{CardUtility.RocChineseDateString(startDate)} ~ {CardUtility.RocChineseDateString(endDate)}";
            return formInfo;
        }

        /// <summary>
        /// 不同天顯示起始截止時間 的 FormInfo
        /// </summary>
        /// <param name="startDate">起始日期時間</param>
        /// <param name="endDate">截止日期時間</param>
        /// <returns>FormInfo</returns>
        private string FormInfoDateTime(DateTime startDate, DateTime endDate)
        {
            string formInfo = $"{CardUtility.RocChineseDateTimeString(startDate)} ~ {CardUtility.RocChineseDateTimeString(endDate)}";
            return formInfo;
        }

        /// <summary>
        /// 同一天顯示起始截止時間 的 FormInfo
        /// </summary>
        /// <param name="startDate">起始日期時間</param>
        /// <param name="endDate">截止日期時間</param>
        /// <returns>FormInfo</returns>
        private string FormInfoDateTime1Day(DateTime startDate, DateTime endDate)
        {
            string formInfo = $"{CardUtility.RocChineseDateString(startDate)} {CardUtility.TimeString(startDate)} ~ {CardUtility.TimeString(endDate)}";
            return formInfo;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="dtCardForms"></param>
        /// <returns></returns>
        private List<FormCard> GetFormCards(DateTime startDate, DateTime endDate, DataTable dtCardForms)
        {
            List<FormCard> formCards = new List<FormCard>();

            var guids = (from DataRow dr in dtCardForms.Rows
                         select (Guid)dr["FormUID"]).Distinct().OrderBy(FormUID => FormUID);

            foreach (var guid in guids)
            {
                FormCard formCard = new FormCard();
                formCard.FormUID = guid;

                formCard.Flows = new List<FormFlow>();
                DataRow[] drs = dtCardForms.Select($"FormUID='{guid}'");
                DataRow row = drs[0];
                formCard.ID = (int)row["FormIntID"];
                formCard.FormUID = guid;
                formCard.FormID = (string)row["FormID"];
                formCard.FormNo = (string)row["FormNo"];
                formCard.FormSubject = (string)row["FormSubject"];
                formCard.FormInfo = (string)row["FormInfo"];
                formCard.EmpNo = (string)row["EmpNo"];
                formCard.EmpName = (string)row["EmpName"];
                formCard.DeptNo = (int)row["DeptNo"];
                formCard.DeptSName = SqlHelper.GetColumnStringValue(row, "DeptSName");
                formCard.TeamID = SqlHelper.GetColumnIntValue(row, "TeamID");
                formCard.TeamCName = SqlHelper.GetColumnStringValue(row, "TeamCName");
                if (row["RankNo"] != DBNull.Value)
                {
                    formCard.RankNo = (string)row["RankNo"];
                }
                if (row["RankName"] != DBNull.Value)
                {
                    formCard.RankName = (string)row["RankName"];
                }

                formCard.JobNo = SqlHelper.GetColumnStringValue(row, "JobNo");
                formCard.JobName = SqlHelper.GetColumnStringValue(row, "JobName");
                formCard.ContentStartTime = SqlHelper.GetColumnDateTimeValue(row, "ContentStartTime");
                formCard.ContentEndTime = SqlHelper.GetColumnDateTimeValue(row, "ContentEndTime");
                formCard.CreatedEmpNo = (string)row["CreatedEmpNo"];
                formCard.CreatedName = (string)row["CreatedName"];
                formCard.FilledTime = (DateTime)row["FilledTime"];
                formCard.CreatedTime = (DateTime)row["CreatedTime"];
                formCard.CreatedIP = (string)row["CreatedIP"];
                formCard.CreatedHost = SqlHelper.GetColumnStringValue(row, "CreatedHost");

                formCard.AddedSigner = SqlHelper.GetColumnStringValue(row, "AddedSigner");
                formCard.AddedSigner = _formBo.AddSignersAddName(formCard.AddedSigner);
                formCard.StartTime = (DateTime)row["StartTime"];

                formCard.EndTime = SqlHelper.GetColumnDateTimeValue(row, "EndTime");

                formCard.FormStatus = (byte)row["FormStatus"];
                formCard.FormStatusName = (string)row["FormStatusName"];
                formCard.TotalSteps = (byte)row["TotalSteps"];
                formCard.CurrentStep = (byte)row["CurrentStep"];

                DataTable dtCards = _c1CardDao.GetC1Cards(startDate, endDate);
                DataRow[] drsX = dtCards.Select($"FormUID='{guid}'");
                List<C1CardDto> c1CardDtos = SqlHelper.ConvertDataTable<C1CardDto>(drsX);
                C1Card card = C1CardDto2C1Card(c1CardDtos);
                card.DeputyName = _employeeBo.GetEmployeeName(card.Deputy);
                card.CreatedTime = (DateTime)row["CreatedTime"];
                card.SetApplicationType();
                formCard.Card = card;

                _formBo.AddAttachments(dtCardForms, formCard, drs);
                formCards.Add(formCard);
            }
            return formCards;
        }

        /// <summary>
        /// 取得假別提示訊息
        /// </summary>
        /// <param name="kind">假別種類</param>
        /// <returns></returns>
        private LeaveKind GetLeaveMessage(LeaveKind kind)
        {
            DataTable dt = _c1CardDao.GetLeaveMessage((int)kind.Number);
            if (dt == null || dt.Rows.Count == 0)
            {
                return kind;
            }

            // 定義訊息類型和對應屬性的對照表
            var messageTypeMapping = new Dictionary<string, Action<LeaveKind, string>>
            {
                { "010", (k, msg) => k.EndTimeExplanation = msg },         // 截止時間填報說明
                { "020", (k, msg) => k.AttachmentExplanation = msg },      // 附件填報說明
                { "021", (k, msg) => k.AttachmentPageExplanation = msg },  // 附件頁面填報說明
                { "030", (k, msg) => k.EventExplanation = msg },           // 事件發生日填報說明
                { "040", (k, msg) => k.SwitchLeavePrompt = msg },          // 附件頁面填報說明
                { "050", (k, msg) => k.PageExplanation = msg },            // 頁面填報說明
                { "060", (k, msg) => k.LeaveKindExplanation = msg }        // 假別下方的提示訊息
            };

            // 為每種訊息類型設定對應的屬性
            foreach (var mapping in messageTypeMapping)
            {
                SetLeaveMessageProperty(dt, kind, mapping.Key, mapping.Value);
            }

            return kind;
        }

        /// <summary>
        /// 設定假別訊息屬性的輔助方法
        /// </summary>
        /// <param name="dt">訊息資料表</param>
        /// <param name="kind">假別物件</param>
        /// <param name="messageTypeSuffix">訊息類型後罴</param>
        /// <param name="setter">屬性設定動作</param>
        private void SetLeaveMessageProperty(DataTable dt, LeaveKind kind, string messageTypeSuffix, Action<LeaveKind, string> setter)
        {
            string messageId = $"3{((int)kind.Number).ToString("000")}{messageTypeSuffix}";
            
            var matchingRows = dt.AsEnumerable()
                                .Where(row => (int)row["MessageId"] == int.Parse(messageId))
                                .ToList();

            if (matchingRows.Count > 0)
            {
                string message = (string)matchingRows[0]["Message"];
                setter(kind, message);
            }
        }

        /// <summary>
        /// 將 C1CardDetail依照月份拆單，本方法只能在跨月份時使用，同月份時會錯
        /// </summary>
        /// <param name="c1Card"></param>
        /// <param name="isCalendarDay"></param>
        /// <returns></returns>
        private List<C1CardDetail> SplitC1CardDetail(C1Card c1Card, bool isCalendarDay)
        {
            // 本方法只能在跨月份時使用，同月份時會錯
            C1CardDetail detail = c1Card.Details[0];

            List<(DateTime, DateTime)> result;
            if (isCalendarDay)
            {
                result = _workdayBo.SplitDateByMonthCalendarDay(detail.C1_StartDate, detail.C1_EndDate, 1); //只計算正常班
            }
            else
            {
                result = _workdayBo.SplitDateByMonthWorkday(detail.C1_StartDate, detail.C1_EndDate, c1Card.EmpNo);
            }
            List<C1CardDetail> list = SplitC1CardDetail(detail, result, isCalendarDay);
            return list;
        }

        /// <summary>
        /// 將 C1CardDetail依照月份拆單
        /// </summary>
        /// <param name="detail"></param>
        /// <param name="result"></param>
        /// <param name="isCalendarDay">是否為日曆天</param>
        /// <returns></returns>
        private List<C1CardDetail> SplitC1CardDetail(C1CardDetail detail, List<(DateTime, DateTime)> result, bool isCalendarDay)
        {
            List<C1CardDetail> list = new List<C1CardDetail>();
            int serial = 0;
            foreach (var (start, end) in result)
            {
                serial++;
                C1CardDetail newDetail = new C1CardDetail();
                if (detail.ID != null)
                {
                    newDetail.ID = detail.ID;
                }
                newDetail.C1_StartDate = start;
                newDetail.C1_EndDate = end;
                if (isCalendarDay)
                {
                    TimeSpan dateDiff = end - start;
                    int days = dateDiff.Days + 1;
                    // 產假以日曆天計算時數                   
                    newDetail.C1_HOUR = AttendanceParameters.DefaultWorkingHours * days;
                }
                else
                {
                    newDetail.C1_HOUR = CalculateTakeLeaveWorkingHours(start, end);
                }
                newDetail.C1_SERIALNO = serial.ToString(); // 不補零
                newDetail.C1_YYMM = CardUtility.RocChineseYYYMM(start);
                newDetail.C1_SDD = start.ToString("dd");
                newDetail.C1_SHH = start.ToString("HH");
                newDetail.C1_SMM = start.ToString("mm");
                newDetail.C1_EDD = end.ToString("dd");
                newDetail.C1_EHH = end.ToString("HH");
                newDetail.C1_EMM = end.ToString("mm");
                list.Add(newDetail);
            }
            return list;
        }

        /// <summary>
        /// 以月份分割 C1CardDto
        /// </summary>
        /// <param name="c1CardDto">C1CardDto</param>
        /// <param name="result">月份分割的啟始結束日期</param>
        /// <param name="isCalendarDay">是否以日曆天計算</param>
        /// <returns></returns>
        private List<C1CardDto> SplitC1CardDto(C1CardDto c1CardDto, List<(DateTime, DateTime)> result, bool isCalendarDay)
        {
            List<C1CardDto> list = new List<C1CardDto>();
            int serial = 0;
            foreach (var (start, end) in result)
            {
                serial++;
                C1CardDto cardClone = c1CardDto.ShallowCopy();
                cardClone.C1_StartDate = start;
                cardClone.C1_EndDate = end;
                if (isCalendarDay)
                {
                    TimeSpan dateDiff = end - start;
                    int days = dateDiff.Days + 1;
                    cardClone.C1_HOUR = // 原  CalculateTakeLeaveWorkingHours(start, end, 1); //只計算正常班
                                        // 產假以日曆天計算時數                                    
                    AttendanceParameters.DefaultWorkingHours * days;
                }
                else
                {
                    cardClone.C1_HOUR = CalculateTakeLeaveWorkingHours(start, end);
                }
                cardClone.C1_SERIALNO = serial.ToString(); // 不補零
                cardClone.C1_YYMM = CardUtility.RocChineseYYYMM(start);
                cardClone.C1_SDD = start.ToString("dd");
                cardClone.C1_SHH = start.ToString("HH");
                cardClone.C1_SMM = start.ToString("mm");
                cardClone.C1_EDD = end.ToString("dd");
                cardClone.C1_EHH = end.ToString("HH");
                cardClone.C1_EMM = end.ToString("mm");
                list.Add(cardClone);
            }
            return list;
        }

        private static LeaveView _LastCard { get; set; } = new LeaveView();

        /// <summary>
        /// 快取政策
        /// </summary>
        private static CacheItemPolicy CachePolicy
        {
            get
            {
                CacheItemPolicy policy = new CacheItemPolicy();
                policy.AbsoluteExpiration = DateTimeOffset.Now.AddMinutes(2.0);
                return policy;
            }
        }

        /// <summary>
        /// 快取政策10秒
        /// </summary>
        private static CacheItemPolicy CachePolicy10Seconds
        {
            get
            {
                CacheItemPolicy policy = new CacheItemPolicy();
                policy.AbsoluteExpiration = DateTimeOffset.Now.AddSeconds(10);
                return policy;
            }
        }

        /// <summary>新增 請假卡</summary>
        /// <param name="creatorId">填表人 員工編號</param>
        /// <param name="leave">請假卡</param>
        /// <param name="ipAddress">IP address</param>
        /// <param name="hostname">Host name</param>
        /// <returns>正確傳回空字串，若有錯誤則傳回</returns>
        public async Task<CardCheckResult> AddC1Card(string creatorId, LeaveView leave, string ipAddress,
            string hostname)
        {
            CardCheckResult result;
            if (leave == null)
            {
                result = AttendanceParameters.ResultGeneralError;
                return result;
            }
            await _semaphore.WaitAsync();
            try
            {
                if (leave.EasyEquals(_LastCard))
                {
                    result = new CardCheckResult(1000311, CardStatusEnum.Error, AttendanceParameters.RepeatSubmitForm);
                }
                else
                {
                    leave.UpdatedName = _employeeBo.GetEmployeeName(creatorId);
                    C1Card c1Card = LeaveView.LeaveDtoToC1Card(leave);
                    c1Card.DeputyName = _employeeBo.GetEmployeeName(c1Card.Deputy);
                    c1Card.LeaveUnit = GetLeaveUnit(c1Card); // 由資料庫得到請假單位
                    c1Card.LeaveMinimumUnit = GetLeaveMinimumUnit(c1Card); // 由資料庫得到請假單位
                    c1Card.LeaveMaximum = GetLeaveMaximum(c1Card); // 由資料庫得到最大請假數量，依請假單位
                    c1Card.Hours = CalculateTakeLeaveWorkingHours(c1Card.StartDate, c1Card.EndDate);
                    c1Card.Days = CalculateTakeLeaveWorkingDays(c1Card.Hours);
                    c1Card.NetHours = c1Card.Hours % AttendanceParameters.DefaultWorkingHours;
                    c1Card.CertificateRequired = GetCertificateRequired(c1Card);
                    GenerateC1CardDetail(c1Card);

                    #region 呼叫檢查程式，檢查是否有錯誤
                    C1CardChecker c1CardChecker = new C1CardChecker(c1Card, this);
                    result = c1CardChecker.CheckData();
                    if (result.Status != CardStatusEnum.Success)
                    {
                        return result;
                    }
                    #endregion

                    Form form = new Form();
                    form.FormID = "C1Card";
                    form.FormUID = Guid.NewGuid();
                    form.EmpNo = c1Card.EmpNo;

                    Employee employee = _employeeBo.GetEmployeeDetail(form.EmpNo);
                    form.EmpName = employee.CName;
                    form.DeptNo = employee.DeptNo;
                    form.DeptSName = employee.DeptSName;
                    form.TeamID = employee.TeamID;
                    form.TeamCName = employee.TeamCName;
                    form.RankNo = employee.RankNo;
                    form.RankName = employee.RankName;
                    form.JobNo = employee.JobNo;
                    form.JobName = employee.JobName;
                    form.ContentStartTime = c1Card.StartDate;
                    form.ContentEndTime = c1Card.EndDate;
                    form.FormInfo = GenerateFormInfo(c1Card);
                    form.FormSubject = $"請假卡-{form.EmpName}-{form.FormInfo}";
                    form.CreatedEmpNo = creatorId;
                    form.CreatedName = _employeeBo.GetEmployeeName(form.CreatedEmpNo);
                    form.FilledTime = c1Card.FilledTime;
                    form.CreatedTime = c1Card.CreatedTime;
                    form.CreatedIP = ipAddress;
                    form.CreatedHost = hostname;
                    form.AddedSigner = c1Card.AddSigners;
                    form.StartTime = DateTime.Now;
                    form.UpdatedTime = form.CreatedTime;
                    form.UpdatedEmpNo = creatorId;
                    form.UpdatedName = leave.UpdatedName;
                    form.UpdatedIP = ipAddress;
                    form.UpdatedHost = hostname;
                    form.FormStatus = (int)FormStatus.Processing; //簽核狀態：1:簽核中
                    form.CurrentStep = 0; //所有表單都預設為0，讓FormBo自動計算

                    c1Card.FormUID = form.FormUID;
                    c1Card.UpdatedTime = form.CreatedTime;
                    c1Card.UpdatedEmpNo = creatorId;
                    c1Card.UpdatedName = _employeeBo.GetEmployeeName(creatorId);
                    c1Card.UpdatedIP = ipAddress;
                    c1Card.UpdatedHost = hostname;

                    SetFlows(c1Card, form, employee);

                    // 呼叫 FormBO 寫入資料庫
                    form.TotalSteps = form.Flows.Count;
                    string ret;
                    ret = _formBo.AddForm(form, this, c1Card);
                    if (!string.IsNullOrWhiteSpace(ret))
                    {
                        result = new CardCheckResult(3000399, CardStatusEnum.Error, ret);
                    }
                    else
                    {
                        result = AttendanceParameters.ResultOk;
                        _LastCard = leave.EasyClone();
                    }
                }
            }
            catch (Exception ex)
            {
                result = new CardCheckResult(3000399, CardStatusEnum.Error, AttendanceParameters.SubmitErrorMessage);
                string formJson = JsonConvert.SerializeObject(leave, Formatting.Indented);
                _logger.LogError(ex, "Action: {Action} 發生錯誤，錯誤訊息: {Message} {StackTrace}，內容為 {formJson}", nameof(AddC1Card), ex.Message, ex.StackTrace, formJson);
                Console.WriteLine($"{DateTime.Now} AddB1Card 發生錯誤 {ex.Message} {ex.StackTrace}");
            }
            finally
            {
                _semaphore.Release();
            }
            return result;
        }

        /// <summary>
        /// 此處的 days 只允許 正整數
        /// </summary>
        /// <param name="startDate">開始日期</param>
        /// <param name="days">天數</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public DateTime AddWorkDays(DateTime startDate, int days, string empNo)
        {
            return _workdayBo.AddWorkDays(startDate, days, empNo);
        }

        /// <summary>依照表單資料補足三卡資料 C1Card</summary>
        /// <param name="form"></param>
        /// <param name="card">The card.</param>
        /// <returns></returns>
        public bool AmendCard(Form form, CardBase card)
        {
            C1Card c1Card = (C1Card)card;
            c1Card.FormNumber = form.FormNo;
            c1Card.FormInfo = form.FormInfo;
            return false;
        }

        /// <summary>
        /// List<C1CardDto> 轉為 C1Card 物件
        /// </summary>
        /// <param name="c1CardDtos"></param>
        /// <returns></returns>
        public C1Card C1CardDto2C1Card(List<C1CardDto> c1CardDtos)
        {
            C1Card c1Card = new C1Card();
            DateTime? expirationStartDate = null;
            DateTime? expirationEndDate = null;
            if (c1CardDtos.Count > 0)
            {
                C1CardDto c1CardDto = c1CardDtos[0];
                c1Card.ID = c1CardDto.ID;
                c1Card.FormUID = c1CardDto.FormUID;
                c1Card.EmpNo = c1CardDto.C1_EMPNO;
                c1Card.StartDate = c1CardDto.C1_StartDate;
                c1Card.EndDate = c1CardDto.C1_EndDate;
                c1Card.Hours = c1CardDto.C1_HOUR;
                c1Card.LeaveNumber = (LeaveKindEnum)int.Parse(c1CardDto.C1_CODE);
                if (!string.IsNullOrWhiteSpace(c1CardDto.C1_CODE2))
                {
                    c1Card.LeaveSubNumber = int.Parse(c1CardDto.C1_CODE2);
                }
                else
                {
                    c1Card.LeaveSubNumber = 0;
                }
                c1Card.Reason = c1CardDto.C1_Reason;
                c1Card.ProjectNumber = c1CardDto.C1_PrjNo;
                c1Card.Location = c1CardDto.C1_Location;
                c1Card.Deputy = c1CardDto.C1_Agent;
                c1Card.DeputyName = _employeeBo.GetEmployeeName(c1Card.Deputy);
                c1Card.WDate = c1CardDto.C1_WDate;
                c1Card.EventDate = c1CardDto.C1_EventDate;
                if (c1CardDto.C1_DeadlineStartDate != null && c1CardDto.C1_DeadlineStartDate != DateTime.MinValue
                     && c1CardDto.C1_DeadlineStartDate != DateTime.MaxValue)
                {
                    expirationStartDate = (DateTime)c1CardDto.C1_DeadlineStartDate;
                }
                if (c1CardDto.C1_DeadlineEndDate != null && c1CardDto.C1_DeadlineEndDate != DateTime.MinValue
                    && c1CardDto.C1_DeadlineEndDate != DateTime.MaxValue)
                {
                    expirationEndDate = (DateTime)c1CardDto.C1_DeadlineEndDate;
                }
                c1Card.ADate = c1CardDto.C1_ADate;
                c1Card.Status = c1CardDto.C1_STATUS;
                if (c1CardDto.C1_OVER == "Y")
                {
                    c1Card.OverPermittedHours = true;
                }
                else
                {
                    c1Card.OverPermittedHours = false;
                }
                c1Card.FormNumber = c1CardDto.C1_SHEETNO;
                if (c1CardDto.C1_LeaveMaximum != null)
                {
                    c1Card.LeaveMaximum = (int)c1CardDto.C1_LeaveMaximum;
                }
                if (c1CardDto.C1_LeaveUnit != null)
                {
                    c1Card.LeaveUnit = c1CardDto.C1_LeaveUnit;
                }

                if (!string.IsNullOrWhiteSpace(c1CardDto.C1_RelationSheetNo))
                {
                    c1Card.RelatedFormNumber = c1CardDto.C1_RelationSheetNo;
                }

                if (c1CardDtos.Count > 1) // 合併 StartDate、EndDate與Hours
                {
                    c1Card.Hours = 0;
                    foreach (C1CardDto dto in c1CardDtos)
                    {
                        if (dto.C1_StartDate < c1Card.StartDate)
                        {
                            c1Card.StartDate = dto.C1_StartDate;
                        }
                        if (dto.C1_EndDate > c1Card.EndDate)
                        {
                            c1Card.EndDate = dto.C1_EndDate;
                        }
                        c1Card.Hours += dto.C1_HOUR;
                    }
                }
            }
            foreach (C1CardDto dto in c1CardDtos)
            {
                C1CardDetail detail = new C1CardDetail();
                detail.ID = dto.ID;
                detail.C1_YYMM = dto.C1_YYMM;
                detail.C1_SDD = dto.C1_SDD;
                detail.C1_SHH = dto.C1_SHH;
                detail.C1_SMM = dto.C1_SMM;
                detail.C1_EDD = dto.C1_EDD;
                detail.C1_EHH = dto.C1_EHH;
                detail.C1_EMM = dto.C1_EMM;
                detail.C1_HOUR = dto.C1_HOUR;
                detail.C1_SERIALNO = dto.C1_SERIALNO;
                detail.C1_StartDate = dto.C1_StartDate;
                detail.C1_EndDate = dto.C1_EndDate;
                c1Card.Details.Add(detail);
            }

            // 不要計算，要用加的

            try
            {
                c1Card.Days = c1Card.Hours / AttendanceParameters.GeneralWorkingHours;
                //c1Card.CertificateRequired = GetCertificateRequired(c1Card);
                string? name = GetLeaveName((int)c1Card.LeaveNumber);
                if (name != null)
                {
                    c1Card.LeaveName = name;
                }

                c1Card.NetHours = c1Card.Hours % AttendanceParameters.DefaultWorkingHours;
                c1Card.DayHours = string.Empty;
                if (c1Card.Days > 0)
                {
                    c1Card.DayHours += $"{c1Card.Days}天";
                }

                if (c1Card.NetHours > 0)
                {
                    c1Card.DayHours += $"{c1Card.NetHours}小時";
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message + ex.StackTrace);
            }

            if (expirationStartDate != null)
            {
                c1Card.ExpirationStartDate = (DateTime)expirationStartDate;
            }
            if (expirationEndDate != null)
            {
                c1Card.ExpirationEndDate = (DateTime)expirationEndDate;
            }
            return c1Card;
        }

        /// <summary>
        /// 將 C1CardDto 轉為List<C1CardDto> ，若C1CardDto有跨月時依月份切割
        /// </summary>
        /// <param name="c1CardDto"></param>
        /// <returns></returns>
        public List<C1CardDto> C1CardDto2ListC1CardDtos(C1CardDto c1CardDto)
        {
            List<C1CardDto> list;
            // 若同年同月份
            if (c1CardDto.C1_StartDate.Year == c1CardDto.C1_EndDate.Year &&
                c1CardDto.C1_StartDate.Month == c1CardDto.C1_EndDate.Month)
            {
                list = new List<C1CardDto> { c1CardDto };
            }
            else // 不同月份
            {
                switch (c1CardDto.C1_CODE)
                {
                    case "07": // 產假 以日曆天計
                        list = C1CardDto2ListC1CardDtos(c1CardDto, true);
                        break;
                    default:
                        list = C1CardDto2ListC1CardDtos(c1CardDto, false);
                        break;
                }
            }
            return list;
        }

        /// <summary>
        /// C1Card物件 轉為 List<C1CardDto>
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        public List<C1CardDto> C1CardToC1CardDtos(CardBase card)
        {
            C1Card c1Card = (C1Card)card;
            List<C1CardDto> list = new List<C1CardDto>();
            for (int i = 0; i < c1Card.Details.Count; i++)
            {
                C1CardDetail detail = c1Card.Details[i];
                C1CardDto c1CardDto = new C1CardDto();
                c1CardDto.ID = detail.ID;

                c1CardDto.FormUID = c1Card.FormUID;
                c1CardDto.C1_EMPNO = c1Card.EmpNo;
                c1CardDto.C1_YYMM = detail.C1_YYMM;
                c1CardDto.C1_SDD = detail.C1_SDD;
                c1CardDto.C1_SHH = detail.C1_SHH;
                c1CardDto.C1_SMM = detail.C1_SMM;
                c1CardDto.C1_EDD = detail.C1_EDD;
                c1CardDto.C1_EHH = detail.C1_EHH;
                c1CardDto.C1_EMM = detail.C1_EMM;
                c1CardDto.C1_HOUR = detail.C1_HOUR;
                c1CardDto.C1_SERIALNO = detail.C1_SERIALNO;
                c1CardDto.C1_StartDate = detail.C1_StartDate;
                c1CardDto.C1_EndDate = detail.C1_EndDate;
                c1CardDto.C1_ADate = c1Card.ADate;
                c1CardDto.C1_CODE = ((int)c1Card.LeaveNumber).ToString("00");
                if (c1Card.LeaveSubNumber != 0) // 為零時 C1_CODE2 維持 string.Empty
                {
                    c1CardDto.C1_CODE2 = c1Card.LeaveSubNumber.ToString("00");
                }
                else
                {
                    c1CardDto.C1_CODE2 = "  ";
                }

                c1CardDto.C1_DeadlineStartDate = c1Card.ExpirationStartDate;
                c1CardDto.C1_DeadlineEndDate = c1Card.ExpirationEndDate;
                c1CardDto.C1_RelationSheetNo = c1Card.RelatedFormNumber;
                c1CardDto.C1_LeaveMaximum = c1Card.LeaveMaximum;
                c1CardDto.C1_LeaveUnit = c1Card.LeaveUnit;
                c1CardDto.C1_PrjNo = c1Card.ProjectNumber;
                c1CardDto.C1_Location = c1Card.Location;
                c1CardDto.C1_Reason = c1Card.Reason;
                c1CardDto.C1_Agent = c1Card.Deputy;
                c1CardDto.C1_WYYMMDD = CardUtility.RocChineseYYYMMDD(c1Card.WDate);
                if (c1Card.ADate != null && c1Card.ADate != DateTime.MinValue && c1Card.ADate != DateTime.MaxValue)
                {
                    c1CardDto.C1_AYYMMDD = CardUtility.RocChineseYYYMMDD((DateTime)c1Card.ADate);
                }
                c1CardDto.C1_STATUS = c1Card.Status;
                c1CardDto.C1_OVER = c1Card.OverPermittedHours ? "Y" : "N";
                c1CardDto.C1_SHEETNO = c1Card.FormNumber;
                c1CardDto.C1_WDate = c1Card.WDate;
                if (c1Card.ADate != null && c1Card.ADate != DateTime.MinValue && c1Card.ADate != DateTime.MaxValue)
                {
                    c1CardDto.C1_ADate = c1Card.ADate;
                }
                c1CardDto.UpdatedEmpNo = c1Card.UpdatedEmpNo;
                c1CardDto.UpdatedName = c1Card.UpdatedName;
                c1CardDto.UpdatedTime = c1Card.UpdatedTime;
                c1CardDto.UpdatedIP = c1Card.UpdatedIP;
                c1CardDto.UpdatedHost = c1Card.UpdatedHost;
                if (c1Card.EventDate != null && c1Card.EventDate != DateTime.MinValue && c1Card.EventDate != DateTime.MaxValue)
                {
                    c1CardDto.C1_EventDate = c1Card.EventDate;
                }
                // 非事件假均將不必要欄位清空
                switch (c1Card.LeaveNumber)
                {
                    case LeaveKindEnum.BusinessInjuryLeave: // 公傷假
                        c1CardDto.C1_DeadlineStartDate = null;
                        c1CardDto.C1_DeadlineEndDate = null;
                        c1CardDto.C1_RelationSheetNo = "";
                        c1CardDto.C1_LeaveMaximum = null;
                        c1CardDto.C1_LeaveUnit = null;
                        break;
                    case LeaveKindEnum.MarriageLeave: // 婚假
                    case LeaveKindEnum.ObstetricInspectionLeave: // 產檢假
                    case LeaveKindEnum.MaternityLeave: // 產假
                    case LeaveKindEnum.FuneralLeave: // 喪假
                    case LeaveKindEnum.PaternityLeave: // 陪產檢及陪產假
                        break;
                    default:
                        c1CardDto.C1_EventDate = null;
                        c1CardDto.C1_DeadlineStartDate = null;
                        c1CardDto.C1_DeadlineEndDate = null;
                        c1CardDto.C1_RelationSheetNo = "";
                        c1CardDto.C1_LeaveMaximum = null;
                        c1CardDto.C1_LeaveUnit = null;
                        break;
                }
                list.Add(c1CardDto);
            }
            return list;
        }

        /// <summary>
        /// C1Card物件 轉為 List<C1CardDto>
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        public List<C1CardDto> C1CardToC1CardDtosOld(CardBase card)
        {
            C1Card c1Card = (C1Card)card;
            C1CardDto c1CardDto = C1CardToC1CardDto(c1Card);
            // C1CardDto 不能跨月，必須要拆單，在 C1CardDto2ListC1CardDtos 處理
            List<C1CardDto> list = C1CardDto2ListC1CardDtos(c1CardDto);
            return list;
        }

        /// <summary>
        /// 計算請假單的天數，無條件捨去時數
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">起始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <returns>天數</returns>
        public int CalculateEmpTakeLeaveWorkingDays(string empNo, DateTime startDate, DateTime endDate)
        {
            int days = 0;
            // 以 AttendanceParameters.GeneralWorkingHours 為一天 (正常班8小時)
            int hours = CalculateEmpTakeLeaveWorkingHours(empNo, startDate, endDate);
            days = hours / AttendanceParameters.GeneralWorkingHours;
            return days;
        }

        /// <summary>計算請假區間之工時，採無條件進位法，每日目前最多8小時 <br />
        /// 僅計算工作日，而非日曆天
        /// </summary>
        /// <param name="empNo"></param>
        /// <param name="startDate">起始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <returns></returns>
        public int CalculateEmpTakeLeaveWorkingHours(string empNo, DateTime startDate, DateTime endDate)
        {
            double hours = 0.0;

            List<Workday> workdays = _workdayBo.GetEmpWorkdaysDateRange(startDate, endDate, empNo);
            foreach (Workday workday in workdays)
            {
                bool isWorkday = _workdayBo.IsWorkday(workday.DayType);
                double workHours = workday.WorkHours;
                DateTime date = workday.WorkDate;
                DateTime arrivalTime = workday.ArrivalTime;
                DateTime departureTime = workday.DepartureTime;
                DateTime flexibleDepartureAfter = workday.FlexibleDepartureAfter;
                DateTime middayBreakStart = workday.MiddayBreakStart;
                DateTime middayBreakEnd = workday.MiddayBreakEnd;
                DateTime morningRestStart = workday.MorningRestStart;
                DateTime morningRestEnd = workday.MorningRestEnd;
                DateTime afternoonRestStart = workday.AfternoonRestStart;
                DateTime afternoonRestEnd = workday.AfternoonRestEnd;

                if (isWorkday) // 工作日才算工時
                {
                    if (date == startDate.Date && endDate.Date == startDate.Date) // 起迄同一天
                    {
                        // 請在休息時間，來亂的
                        if (startDate == middayBreakStart && endDate == middayBreakEnd)
                        {
                            return 0;
                        }

                        if (startDate <= arrivalTime && endDate >= departureTime)
                        {
                            startDate = arrivalTime;
                            endDate = departureTime;
                        }
                        else
                        {
                            startDate = AdjustTakeLeaveStartDate(startDate, arrivalTime, middayBreakStart, middayBreakEnd, morningRestStart, morningRestEnd, afternoonRestStart, afternoonRestEnd);
                            endDate = AdjustTakeLeaveEndDate(endDate, flexibleDepartureAfter, middayBreakStart, middayBreakEnd, morningRestStart, morningRestEnd, afternoonRestStart, afternoonRestEnd);
                        }

                        double hoursDiff = (endDate - startDate).TotalHours;
                        hours = hoursDiff;

                        // 若起早於中午休息時間，迄晚於中午休息時間
                        if (startDate <= middayBreakStart && endDate >= middayBreakEnd)
                        {
                            double middleHours = (middayBreakEnd - middayBreakStart).TotalHours;
                            hours -= middleHours;
                        }
                    }
                    else // 起迄不同天
                    {
                        if (date == startDate.Date) // 請假起始日
                        {
                            startDate = AdjustTakeLeaveStartDate(startDate, arrivalTime, middayBreakStart, middayBreakEnd, morningRestStart, morningRestEnd, afternoonRestStart, afternoonRestEnd);
                            if (startDate <= arrivalTime)
                            {
                                hours += workHours;
                            }
                            else
                            {
                                double hoursDiff = (departureTime - startDate).TotalHours;
                                if (startDate <= middayBreakStart)
                                {
                                    hoursDiff -= 1.0;
                                }
                                hours += Math.Ceiling(hoursDiff);
                            }
                        }
                        else if (date == endDate.Date) // 請假截止日
                        {
                            endDate = AdjustTakeLeaveEndDate(endDate, flexibleDepartureAfter, middayBreakStart, middayBreakEnd, morningRestStart, morningRestEnd, afternoonRestStart, afternoonRestEnd);

                            double hoursDiff = (endDate - arrivalTime).TotalHours; // 以標準上班時起算
                            if (endDate >= arrivalTime && endDate.Hour > middayBreakStart.Hour) //超過中午要扣除
                            {
                                hoursDiff -= 1.0;
                            }
                            int hour = (int)Math.Ceiling(hoursDiff);
                            if (hour > AttendanceParameters.GeneralWorkingHours)
                            {
                                hour = AttendanceParameters.GeneralWorkingHours;
                            }
                            hours += hour;
                        }
                        else //不是起始日也不是截止日
                        {
                            hours += workHours;  //直接加入該日工時
                        }
                    }
                }
            }

            int iHours = (int)Math.Ceiling(hours);
            return iHours;
        }

        /// <summary>
        /// 計算請假日數時數
        /// </summary>
        /// <param name="leaveNumber">假別編號</param>
        /// <param name="startDate">請假起始日期</param>
        /// <param name="endDate">請假結束日期</param>
        /// <returns>請假日數時數</returns>

        public (int, int) CalculateLeaveDayHours(int leaveNumber, DateTime startDate, DateTime endDate)
        {
            int days;
            int netHours = 0;
            if (leaveNumber != (int)LeaveKindEnum.MaternityLeave) // 產假是例外
            {
                int hours = CalculateTakeLeaveWorkingHours(startDate, endDate);
                days = CalculateTakeLeaveWorkingDays(hours);
                netHours = hours % AttendanceParameters.DefaultWorkingHours;
            }
            else
            {
                TimeSpan timeSpan = endDate.Date - startDate.Date;
                days = (int)timeSpan.TotalDays + 1;
            }
            return (days, netHours);
        }


        /// <summary>
        /// 依假別計算預設請假截止日
        /// </summary>
        /// <param name="startDate">請假開始</param>
        /// <param name="leaveNumber">假別代碼</param>
        /// <param name="subKindNumber">假別細項代碼</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>請假截止日</returns>
        public DateTime CalculateLeaveEndDate(DateTime startDate, int leaveNumber, int subKindNumber, string empNo)
        {
            DateTime endDate = startDate;
            switch ((LeaveKindEnum)leaveNumber)
            {
                case LeaveKindEnum.MaternityLeave: // 7 產假 
                    switch (subKindNumber)
                    {
                        case 1: // 分娩假(8星期)
                            // 從申請日起算 56天 日曆天
                            endDate = startDate.AddDays(55);
                            break;
                        case 2: // 流產假(4星期)
                            // 從申請日起算 28天 日曆天
                            endDate = startDate.AddDays(27);
                            break;
                        case 3: // 流產假(1星期)
                            // 從申請日起算 7天 日曆天
                            endDate = startDate.AddDays(6);
                            break;
                        case 4: // 流產假(5日)
                            // 從申請日起算 5天 日曆天
                            endDate = startDate.AddDays(4);
                            break;
                    }
                    break;
                case LeaveKindEnum.ObstetricInspectionLeave: // 15 產檢假
                case LeaveKindEnum.MenstrualLeave: // 16 生理假
                case LeaveKindEnum.BirthdayLeave: // 21 生日假
                    endDate = startDate;
                    break;
                case LeaveKindEnum.MarriageLeave: // 2 婚假
                    // 八工作天
                    endDate = _workdayBo.AddWorkDays(startDate, 7, empNo);
                    break;
                case LeaveKindEnum.FuneralLeave: // 11 喪假
                    switch (subKindNumber)
                    {
                        case 1: // 父
                        case 2: // 母
                        case 3: // 養父
                        case 4: // 養母
                        case 5: // 繼父
                        case 6: // 繼母
                        case 7: // 配偶
                            // 八工作天
                            endDate = _workdayBo.AddWorkDays(startDate, 7, empNo);
                            break;
                        case 21: // 祖父
                        case 22: // 祖母
                        case 23: // 外祖父
                        case 24:  // 外祖母
                        case 25: // 子
                        case 26: // 女
                        case 29: // 配偶之父
                        case 30: // 配偶之母
                        case 31: // 配偶之養父
                        case 32: // 配偶之養母
                        case 33: // 配偶之繼父
                        case 34: // 配偶之繼母
                                 // 六工作天
                            endDate = _workdayBo.AddWorkDays(startDate, 5, empNo);
                            break;
                        case 27: // 配偶之祖父
                        case 28: // 配偶之祖母
                        case 35: // 配偶之外祖父
                        case 36: // 配偶之外祖母
                        case 51: // 兄
                        case 52: // 弟
                        case 53: // 姊
                        case 54: // 妹
                        case 55: // 曾祖父母
                            // 三工作天
                            endDate = _workdayBo.AddWorkDays(startDate, 2, empNo);
                            break;
                    }
                    break;
                default:
                    endDate = startDate;
                    break;
            }
            return endDate;
        }

        /// <summary>
        /// 依假別計算預設請假截止日
        /// </summary>
        /// <param name="startDate">請假開始</param>
        /// <param name="leaveNumber">假別代碼</param>
        /// <param name="subKindNumber">假別細項代碼</param>
        /// <param name="shiftId">班別</param>
        /// <returns>請假截止日</returns>
        public DateTime CalculateLeaveEndDate(DateTime startDate, int leaveNumber, int subKindNumber, int shiftId = 0)
        {
            DateTime endDate = startDate;
            List<Workday> workdays = _workdayBo.GetWorkdaysDateRange(startDate, startDate.AddMonths(1), shiftId);
            DateTime newStartDate = _workdayBo.MostRecentWorkDateAfter(startDate, workdays);
            switch ((LeaveKindEnum)leaveNumber)
            {
                case LeaveKindEnum.MaternityLeave: // 7 產假 
                    switch (subKindNumber)
                    {
                        case 1: // 分娩假(8星期)
                            // 從申請日起算 56天 日曆天
                            endDate = startDate.AddDays(55);
                            break;
                        case 2: // 流產假(4星期)
                            // 從申請日起算 28天 日曆天
                            endDate = startDate.AddDays(27);
                            break;
                        case 3: // 流產假(1星期)
                            // 從申請日起算 7天 日曆天
                            endDate = startDate.AddDays(6);
                            break;
                        case 4: // 流產假(5日)
                            // 從申請日起算 5天 日曆天
                            endDate = startDate.AddDays(4);
                            break;
                    }
                    break;
                case LeaveKindEnum.MarriageLeave: // 2 婚假
                    // 八工作天
                    endDate = _workdayBo.AddWorkDays(newStartDate, 7, shiftId);
                    break;
                case LeaveKindEnum.FuneralLeave: // 11 喪假
                    switch (subKindNumber)
                    {
                        case 1: // 父
                        case 2: // 母
                        case 3: // 養父
                        case 4: // 養母
                        case 5: // 繼父
                        case 6: // 繼母
                        case 7: // 配偶
                            // 八工作天
                            endDate = _workdayBo.AddWorkDays(newStartDate, 7, shiftId);
                            break;
                        case 21: // 祖父
                        case 22: // 祖母
                        case 23: // 外祖父
                        case 24:  // 外祖母
                        case 25: // 子
                        case 26: // 女
                        case 29: // 配偶之父
                        case 30: // 配偶之母
                        case 31: // 配偶之養父
                        case 32: // 配偶之養母
                        case 33: // 配偶之繼父
                        case 34: // 配偶之繼母
                                 // 六工作天
                            endDate = _workdayBo.AddWorkDays(newStartDate, 5, shiftId);
                            break;
                        case 27: // 配偶之祖父
                        case 28: // 配偶之祖母
                        case 35: // 配偶之外祖父
                        case 36: // 配偶之外祖母
                        case 51: // 兄
                        case 52: // 弟
                        case 53: // 姊
                        case 54: // 妹
                        case 55: // 曾祖父母
                            // 三工作天
                            endDate = _workdayBo.AddWorkDays(newStartDate, 2, shiftId);
                            break;
                    }
                    break;
                case LeaveKindEnum.ObstetricInspectionLeave: // 15 產檢假
                case LeaveKindEnum.MenstrualLeave: // 16 生理假
                case LeaveKindEnum.BirthdayLeave: // 21 生日假
                    endDate = newStartDate;
                    break;
                default:
                    endDate = newStartDate;
                    break;
            }
            endDate = new DateTime(endDate.Year, endDate.Month, endDate.Day, 17, 0, 0, DateTimeKind.Local);
            return endDate;
        }

        /// <summary>
        /// 找出可請假期限
        /// </summary>
        /// <param name="eventDate">事件發生日</param>
        /// <param name="leaveNumber">假別代碼</param>
        /// <param name="subKindNumber">假別細項代碼</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>最早可請假日期與早晚可請假日期</returns>
        public (DateTime, DateTime) CalculateLeavePermittedPeriod(DateTime eventDate, int leaveNumber, int subKindNumber, string empNo)
        {
            DateTime expirationStartDate = DateTime.MinValue;
            DateTime expirationEndDate = DateTime.MaxValue;
            C1Card c1Card = new C1Card();
            c1Card.EmpNo = empNo;
            c1Card.EventDate = eventDate.Date;
            c1Card.StartDate = eventDate;
            c1Card.EndDate = eventDate.AddDays(1);
            c1Card.LeaveNumber = (LeaveKindEnum)leaveNumber;
            c1Card.LeaveSubNumber = subKindNumber;
            C1CardBase? cardBase = C1CardFactory.CreateLeave(c1Card, this);
            if (cardBase != null)
            {
                (c1Card.ExpirationStartDate, c1Card.ExpirationEndDate) = cardBase.CalculateLeavePermittedPeriod(eventDate, empNo);
                expirationStartDate = c1Card.ExpirationStartDate;
                expirationEndDate = c1Card.ExpirationEndDate;
            }
            return (expirationStartDate, expirationEndDate);
        }

        /// <summary>
        /// 計算請假單的天數，無條件捨去
        /// </summary>
        /// <param name="workingHours">請假單的總工作時數</param>
        /// <returns>天數</returns>
        public int CalculateTakeLeaveWorkingDays(int workingHours)
        {
            int days = workingHours / AttendanceParameters.GeneralWorkingHours;
            return days;
        }

        /// <summary>
        /// 計算請假單的天數，無條件捨去時數
        /// </summary>
        /// <param name="startDate">起始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns>天數</returns>
        public int CalculateTakeLeaveWorkingDays(DateTime startDate, DateTime endDate, int shiftId = 1)
        {
            int days = 0;
            // 以 AttendanceParameters.GeneralWorkingHours 為一天 (正常班8小時)
            int hours = CalculateTakeLeaveWorkingHours(startDate, endDate, shiftId);
            days = hours / AttendanceParameters.GeneralWorkingHours;
            return days;
        }

        /// <summary>計算請假單之工時，採無條件進位法，每日目前最多8小時 <br /> 
        /// 僅計算工作日，而非日曆天
        /// </summary>
        /// <param name="startDate">起始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns>時數</returns>
        public int CalculateTakeLeaveWorkingHours(DateTime startDate, DateTime endDate, int shiftId = 1)
        {
            // 初始化累計請假總時數為 0
            double hours = 0.0;

            // 取得請假起迄日期範圍內的所有工作日清單（依指定班別）
            List<Workday> workdays = _workdayBo.GetWorkdaysDateRange(startDate, endDate, shiftId);

            // 逐一處理每個工作日，計算該日的請假時數
            foreach (Workday workday in workdays)
            {
                // 取得當日工作日相關資訊
                bool isWorkday = _workdayBo.IsWorkday(workday.DayType);        // 判斷是否為工作日
                double workHours = workday.WorkHours;                          // 當日標準工作時數
                DateTime date = workday.WorkDate;                              // 工作日期
                DateTime arrivalTime = workday.ArrivalTime;                    // 標準上班時間
                DateTime departureTime = workday.DepartureTime;                // 標準下班時間
                DateTime flexibleDepartureAfter = workday.FlexibleDepartureAfter;  // 彈性下班時間
                DateTime middayBreakStart = workday.MiddayBreakStart;          // 中午休息開始時間
                DateTime middayBreakEnd = workday.MiddayBreakEnd;              // 中午休息結束時間
                DateTime morningRestStart = workday.MorningRestStart;          // 上午休息開始時間
                DateTime morningRestEnd = workday.MorningRestEnd;              // 上午休息結束時間
                DateTime afternoonRestStart = workday.AfternoonRestStart;      // 下午休息開始時間
                DateTime afternoonRestEnd = workday.AfternoonRestEnd;          // 下午休息結束時間

                if (isWorkday) // 僅計算工作日的請假時數，例假日、國定假日不計入
                {
                    if (date == startDate.Date && endDate.Date == startDate.Date) // 處理請假起迄為同一天的情況
                    {
                        // 特殊情況：如果剛好請在中午休息時間，視為惡意請假，不計入任何時數
                        if (startDate == middayBreakStart && endDate == middayBreakEnd)
                        {
                            return 0;
                        }

                        // 如果請假時間涵蓋整個工作時段（早於上班時間且晚於下班時間）
                        if (startDate <= arrivalTime && endDate >= departureTime)
                        {
                            // 將請假時間調整為標準上下班時間範圍
                            startDate = arrivalTime;
                            endDate = departureTime;
                        }
                        else
                        {
                            // 調整請假開始時間，考量各種休息時間的影響
                            startDate = AdjustTakeLeaveStartDate(startDate, arrivalTime, middayBreakStart, middayBreakEnd, morningRestStart, morningRestEnd, afternoonRestStart, afternoonRestEnd);
                            // 調整請假結束時間，考量各種休息時間的影響
                            endDate = AdjustTakeLeaveEndDate(endDate, flexibleDepartureAfter, middayBreakStart, middayBreakEnd, morningRestStart, morningRestEnd, afternoonRestStart, afternoonRestEnd);
                        }

                        // 計算調整後的請假時數差
                        double hoursDiff = (endDate - startDate).TotalHours;
                        hours = hoursDiff;

                        // 若請假時間跨越中午休息時間，需扣除休息時數
                        if (startDate <= middayBreakStart && endDate >= middayBreakEnd)
                        {
                            double middleHours = (middayBreakEnd - middayBreakStart).TotalHours;
                            hours -= middleHours;
                        }
                    }
                    else // 處理請假起迄為不同天的情況
                    {
                        if (date == startDate.Date) // 處理請假開始日
                        {
                            // 調整請假開始時間
                            startDate = AdjustTakeLeaveStartDate(startDate, arrivalTime, middayBreakStart, middayBreakEnd, morningRestStart, morningRestEnd, afternoonRestStart, afternoonRestEnd);

                            // 如果請假開始時間早於或等於標準上班時間，計入整日工作時數
                            if (startDate <= arrivalTime)
                            {
                                hours += workHours;
                            }
                            else
                            {
                                // 計算從請假開始時間到標準下班時間的時數
                                double hoursDiff = (departureTime - startDate).TotalHours;
                                if (hoursDiff > 0)
                                {
                                    // 如果請假開始時間早於中午休息開始，需扣除中午休息1小時
                                    if (startDate <= middayBreakStart)
                                    {
                                        hoursDiff -= 1.0;
                                    }
                                    // 將時數無條件進位後累加
                                    hours += Math.Ceiling(hoursDiff);
                                }
                            }
                        }
                        else if (date == endDate.Date) // 處理請假結束日
                        {
                            // 調整請假結束時間
                            endDate = AdjustTakeLeaveEndDate(endDate, flexibleDepartureAfter, middayBreakStart, middayBreakEnd, morningRestStart, morningRestEnd, afternoonRestStart, afternoonRestEnd);

                            // 計算從標準上班時間到請假結束時間的時數
                            double hoursDiff = (endDate - arrivalTime).TotalHours;
                            if (hoursDiff > 0)
                            {
                                // 如果請假結束時間超過中午休息開始時間，需扣除中午休息1小時
                                if (endDate >= arrivalTime && endDate.Hour > middayBreakStart.Hour)
                                {
                                    hoursDiff -= 1.0;
                                }

                                // 將時數無條件進位
                                int hour = (int)Math.Ceiling(hoursDiff);

                                // 限制不超過標準工作時數上限
                                if (hour > AttendanceParameters.GeneralWorkingHours)
                                {
                                    hour = AttendanceParameters.GeneralWorkingHours;
                                }
                                hours += hour;
                            }
                        }
                        else // 處理請假中間日（既非開始日也非結束日）
                        {
                            // 直接加入該日的標準工作時數
                            hours += workHours;
                        }
                    }
                }
            }

            // 將總時數無條件進位後轉為整數回傳
            int iHours = (int)Math.Ceiling(hours);
            return iHours;
        }

        /// <summary>
        /// 是否能結案抽單 (管理員抽單)
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        public string CanClosedWithdraw(CardBase card)
        {
            C1Card c1Card = (C1Card)card;
            if (c1Card.Status == (int)FormStatus.Withdraw)
            {
                return "表單已抽單";
            }
            if (c1Card.Status == (int)FormStatus.Processing)
            {
                return "表單進行中";
            }
            return CheckRelatedNumber(card);
        }

        /// <summary>
        /// 是否能抽單 (正常情況)
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        public string CanWithdraw(CardBase card)
        {
            return CheckRelatedNumber(card);
        }

        /// <summary>
        /// 檢查是否已請假
        /// </summary>
        ///<param name="startDate">請假起始日期</param>
        ///<param name="endDate">請假結束日期</param>
        ///<param name="empNo">員工編號</param>
        /// <returns>已經請假過傳回 true，無則傳回 false</returns>
        public bool CheckAlreadyTakeLeave(DateTime startDate, DateTime endDate, string empNo)
        {
            // 讀取假卡找到是否有重覆請假
            List<C1CardDto> cards = GetCardsBetween(empNo, startDate, endDate);
            bool found = cards.Exists(c1CardDto => c1CardDto.C1_STATUS == (int)FormStatus.Processing
                        || c1CardDto.C1_STATUS == (int)FormStatus.Agree);
            return found;
        }

        /// <summary>檢查 請假卡</summary>
        /// <param name="creatorId">填表人 員工編號</param>
        /// <param name="leave">請假卡</param>
        /// <param name="ipAddress">IP address</param>
        /// <param name="hostname">Host name</param>
        /// <returns>CardCheckResult</returns>
        public CardCheckResult CheckC1Card(string creatorId, LeaveView leave, string ipAddress, string hostname)
        {
            leave.UpdatedName = _employeeBo.GetEmployeeName(creatorId);
            C1Card c1Card = LeaveView.LeaveDtoToC1Card(leave);
            c1Card.DeputyName = _employeeBo.GetEmployeeName(c1Card.Deputy);
            c1Card.LeaveUnit = GetLeaveUnit(c1Card); // 由資料庫得到請假單位
            c1Card.LeaveMinimumUnit = GetLeaveMinimumUnit(c1Card); // 由資料庫得到請假單位
            c1Card.LeaveMaximum = GetLeaveMaximum(c1Card); // 由資料庫得到最大請假數量，依請假單位
            c1Card.Hours = CalculateTakeLeaveWorkingHours(c1Card.StartDate, c1Card.EndDate);
            c1Card.Days = CalculateTakeLeaveWorkingDays(c1Card.Hours);
            c1Card.NetHours = c1Card.Hours % AttendanceParameters.DefaultWorkingHours;
            c1Card.CertificateRequired = GetCertificateRequired(c1Card);

            // 呼叫檢查程式，檢查是否有錯誤
            C1CardChecker c1CardChecker = new C1CardChecker(c1Card, this);
            CardCheckResult checkResult = c1CardChecker.CheckData();
            return checkResult;
        }

        /// <summary>
        /// 檢查請假日期是否為工作日、結束是否大於起始，是否在上班時間請假
        /// </summary>
        ///<param name="startDate">請假起始日期</param>
        ///<param name="endDate">請假結束日期</param>
        /// <returns>日期異常時顯示訊息，否則傳回空字串</returns>
        public string CheckLeaveDays(DateTime startDate, DateTime endDate)
        {
            if (startDate > endDate)
            {
                return AttendanceParameters.Leave_End_Date_Must_Greater_Than_Start_Date;
            }
            if (startDate.Year != endDate.Year)
            {
                return AttendanceParameters.Not_allowed_to_take_leave_across_the_new_year;
            }
            if (!_workdayBo.IsWorkday(startDate))
            {
                return AttendanceParameters.Leave_Date_Only_Allow_WorkDays;
            }
            if (!_workdayBo.IsWorkday(endDate))
            {
                return AttendanceParameters.Leave_Date_Only_Allow_WorkDays;
            }
            return string.Empty;
        }

        /// <summary>
        /// 檢查請假日期是否為工作日、結束是否大於起始，是否在上班時間請假
        /// </summary>
        ///<param name="startDate">請假起始日期</param>
        ///<param name="endDate">請假結束日期</param>
        ///<param name="empNo">員工編號</param>
        /// <returns>日期異常時顯示訊息，否則傳回空字串</returns>
        public string CheckLeaveDays(DateTime startDate, DateTime endDate, string empNo)
        {
            if (startDate > endDate)
            {
                return AttendanceParameters.Leave_End_Date_Must_Greater_Than_Start_Date;
            }
            if (startDate.Year != endDate.Year)
            {
                return AttendanceParameters.Not_allowed_to_take_leave_across_the_new_year;
            }
            if (!_workdayBo.IsWorkday(startDate))
            {
                return AttendanceParameters.Leave_Date_Only_Allow_WorkDays;
            }
            if (!_workdayBo.IsWorkday(endDate))
            {
                return AttendanceParameters.Leave_Date_Only_Allow_WorkDays;
            }
            // 是否在上班時間請假
            List<Workday> workdays = _workdayBo.GetEmpWorkdaysDateRange(startDate, endDate, empNo);
            if (workdays.Count > 0)
            {
                Workday workdayStart = workdays[0];
                Workday workdayEnd = workdays[workdays.Count - 1];
                if (startDate < workdayStart.ArrivalTime || endDate > workdayEnd.FlexibleDepartureAfter)
                {
                    return AttendanceParameters.Leave_Day_Time_Error;
                }
            }
            return string.Empty;
        }

        /// <summary>結案</summary>
        /// <param name="card">The card.</param>
        /// <param name="formStatus">The form status.</param>
        /// <param name="updateDto">更新資料物件</param>
        public void Finish(CardBase card, FormStatus formStatus, Updater updateDto)
        {
            Update(card, updateDto);
            C1Card c1Card = (C1Card)card;
            c1Card.ADate = updateDto.UpdatedTime;
            c1Card.Status = (int)formStatus;
        }

        /// <summary>Find the first work day in month.</summary>
        /// <param name="year">The year. 西元年</param>
        /// <param name="month">The month. 月分</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>第一天工作日的日期(int)</returns>
        public DateTime FirstWorkDayInMonth(int year, int month, string empNo)
        {
            DateTime date = _workdayBo.FirstWorkDayInMonth(year, month, empNo);
            return date;
        }

        /// <summary>
        /// 單號是否被其他 (已核可或簽核中) 表單關連
        /// </summary>
        /// <param name="formNumber">單號</param>
        /// <returns></returns>
        public bool FormNumberWasRelated(string formNumber)
        {
            bool related;
            DataTable dt = _c1CardDao.GetRelatedCards(formNumber);
            related = dt.Rows.Count > 0;
            return related;
        }

        /// <summary>
        /// 依照C1Card產生C1CardDetails
        /// </summary>
        /// <param name="c1Card"></param>
        public void GenerateC1CardDetail(C1Card c1Card)
        {
            C1CardDetail detail = new C1CardDetail();
            if (c1Card.Details.Count == 0)
            {
                detail.C1_SERIALNO = "1";
                detail.C1_YYMM = CardUtility.RocChineseYYYMM(c1Card.StartDate);
                detail.C1_SDD = c1Card.StartDate.ToString("dd");
                detail.C1_SHH = c1Card.StartDate.ToString("HH");
                detail.C1_SMM = c1Card.StartDate.ToString("mm");
                detail.C1_EDD = c1Card.EndDate.ToString("dd");
                detail.C1_EHH = c1Card.EndDate.ToString("HH");
                detail.C1_EMM = c1Card.EndDate.ToString("mm");
                detail.C1_HOUR = c1Card.Hours;
                detail.C1_StartDate = c1Card.StartDate;
                detail.C1_EndDate = c1Card.EndDate;
                c1Card.Details.Add(detail);
            }
            c1Card.Details = SplitC1CardDetail(c1Card);
        }

        /// <summary>
        /// 產生請假單的FormInfo
        /// </summary>
        /// <param name="card">請假單</param>
        /// <returns>FormInfo</returns>
        public string GenerateFormInfo(C1Card card)
        {
            string formInfo = string.Empty;
            // 同一天
            if (card.StartDate.Date == card.EndDate.Date)
            {
                // 整天
                if (card.LeaveMinimumUnit == "D" || card.Hours == AttendanceParameters.DefaultWorkingHours)
                {
                    formInfo = FormInfoDate(card.StartDate);
                }
                else
                {
                    formInfo = FormInfoDateTime1Day(card.StartDate, card.EndDate);
                }
            }
            else // 跨天
            {
                if (card.LeaveMinimumUnit == "D")
                {
                    formInfo = FormInfoDateOnly(card.StartDate, card.EndDate);
                }
                else
                {
                    List<Workday> workdays = GetEmpWorkdaysDateRange(card.StartDate, card.EndDate, card.EmpNo);
                    var wdays = from w in workdays where w.WorkDate.Date == card.StartDate.Date select w;
                    if (wdays != null) // Workday for StartDate 
                    {
                        Workday arrivial = wdays.First();
                        wdays = from w in workdays where w.WorkDate.Date == card.EndDate.Date select w;
                        if (wdays != null)
                        {
                            Workday departure = wdays.First();
                            if (card.StartDate == arrivial.ArrivalTime && card.EndDate == departure.DepartureTime)
                            {
                                formInfo = FormInfoDateOnly(card.StartDate, card.EndDate);
                            }
                            else
                            {
                                formInfo = FormInfoDateTime(card.StartDate, card.EndDate);
                            }
                        }
                    }
                }
            }
            return formInfo;
        }

        /// <summary>
        /// 取得員工某年的生日之月份第一個工作日期，用以計算生日假請假期限
        /// </summary>
        /// <param name="empNo"></param>
        /// <param name="year"></param>
        /// <returns></returns>
        public DateTime GetBirthdayMonthFirstWorkday(string empNo, int year)
        {
            DateTime date = DateTime.MinValue;
            Employee employee = _employeeBo.GetEmployeeDetail(empNo);
            if (employee.Birthday != null && employee.Birthday != DateTime.MinValue && employee.Birthday != DateTime.MaxValue)
            {
                date = _workdayBo.FirstWorkDayInMonth(year, ((DateTime)employee.Birthday).Month, empNo);
            }
            return date;
        }

        /// <summary>
        /// 取得員工某年的生日之月份最後一個工作日期，用以計算生日假請假期限
        /// </summary>
        /// <param name="empNo"></param>
        /// <param name="year"></param>
        /// <returns></returns>
        public DateTime GetBirthdayMonthLastWorkday(string empNo, int year)
        {
            DateTime date = DateTime.MinValue;
            Employee employee = _employeeBo.GetEmployeeDetail(empNo);
            if (employee.Birthday != null && employee.Birthday != DateTime.MinValue && employee.Birthday != DateTime.MaxValue)
            {
                date = _workdayBo.LastWorkDayInMonth(year, ((DateTime)employee.Birthday).Month, empNo);
            }
            return date;
        }

        /// <summary>
        /// 取得年度員工生日福利資料表
        /// </summary>
        /// <param name="year">西元年</param>
        /// <returns></returns>
        public DataTable GetBirthdayWelfare(int year)
        {
            DataTable dt = _c1CardDao.GetBirthdayWelfare(year);
            return dt;
        }

        /// <summary>
        /// 查詢特定假別特定同仁於特定事件發生日的所有假單，僅限 已同意與簽核中
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="leaveNumber">假別</param>
        /// <param name="leaveSubNumber">假別細項</param>
        /// <param name="eventDate">事件發生日</param>
        /// <returns></returns>
        public List<C1Card> GetC1CardByEventDate(string empNo, LeaveKindEnum leaveNumber, int leaveSubNumber, DateTime eventDate)
        {
            List<C1Card> c1Cards = new List<C1Card>();
            DataTable dt = _c1CardDao.GetC1CardByEventDate(empNo, (int)leaveNumber, leaveSubNumber, eventDate);
            if (dt != null && dt.Rows.Count > 0)
            {
                List<C1CardDto> c1CardDtos = SqlHelper.ConvertDataTable<C1CardDto>(dt);
                var guids = (from DataRow dr in dt.Rows
                             select (Guid)dr["FormUID"]).Distinct().OrderBy(ID => ID);
                foreach (var guid in guids)
                {
                    //DataRow[] drs = dt.Select($"FormUID='{guid}'");
                    //List<C1CardDto> c1CardDtos2 = SqlHelper.ConvertDataTable<C1CardDto>(drs);

                    var enumC1CardDto = from cCardDto in c1CardDtos
                                        where cCardDto.FormUID == guid
                                        select cCardDto;
                    List<C1CardDto> c1CardDtos2 = new List<C1CardDto>();
                    foreach (var cCardDto in enumC1CardDto)
                    {
                        c1CardDtos2.Add(cCardDto);
                    }

                    C1Card c1Card = C1CardDto2C1Card(c1CardDtos2);
                    c1Cards.Add(c1Card);
                }
            }
            return c1Cards;
        }

        /// <summary>
        /// 查詢特定假別特定同仁於特定假別的所有假單，僅限 已同意與簽核中
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="leaveNumber">假別</param>
        /// <param name="leaveSubNumber">假別細項</param>
        public List<C1Card> GetC1CardByLeaveKind(string empNo, LeaveKindEnum leaveNumber, int leaveSubNumber)
        {
            List<C1Card> c1Cards = new List<C1Card>();
            DataTable dt = _c1CardDao.GetC1CardByLeaveKind(empNo, leaveNumber, leaveSubNumber);
            if (dt != null && dt.Rows.Count > 0)
            {
                List<C1CardDto> c1CardDtos = SqlHelper.ConvertDataTable<C1CardDto>(dt);
                var numbers = (from DataRow dr in dt.Rows
                               select (string)dr["C1_SHEETNO"]).Distinct().OrderBy(ID => ID);
                foreach (var number in numbers)
                {
                    var enumC1CardDto = from cCardDto in c1CardDtos
                                        where cCardDto.C1_SHEETNO == number
                                        select cCardDto;
                    List<C1CardDto> c1CardDtos2 = new List<C1CardDto>();
                    foreach (var cCardDto in enumC1CardDto)
                    {
                        c1CardDtos2.Add(cCardDto);
                    }
                    C1Card c1Card = C1CardDto2C1Card(c1CardDtos2);
                    c1Cards.Add(c1Card);
                }
            }
            return c1Cards;
        }

        /// <summary>
        /// 取得卡
        /// </summary>
        /// <param name="formUID"></param>
        /// <returns></returns>
        public CardBase? GetCard(Guid formUID)
        {
            C1Card? c1Card = null;
            DataTable dt = _c1CardDao.GetC1Cards(formUID);
            if (dt != null && dt.Rows.Count > 0)
            {
                List<C1CardDto> c1CardDtos = SqlHelper.ConvertDataTable<C1CardDto>(dt);
                if (c1CardDtos.Count > 0)
                {
                    c1Card = C1CardDto2C1Card(c1CardDtos);
                    c1Card.DeputyName = _employeeBo.GetEmployeeName(c1Card.Deputy);
                }
            }
            return c1Card;
        }

        /// <summary>取得某段時間內卡，不含流程與附件等</summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <returns>卡 CardsDto 物件 List</returns>
        public List<CardBase> GetCards(DateTime startDate, DateTime endDate)
        {
            List<CardBase> cards = new List<CardBase>();
            DataTable dt = _c1CardDao.GetC1Cards(startDate, endDate);
            if (dt != null && dt.Rows.Count > 0)
            {
                List<C1CardDto> c1CardDtos = SqlHelper.ConvertDataTable<C1CardDto>(dt);
                var formNos = c1CardDtos.Select(x => x.C1_SHEETNO).Distinct();
                foreach (var formNo in formNos)
                {
                    var result = (from b1CardAppDto in c1CardDtos
                                  where b1CardAppDto.C1_SHEETNO == formNo
                                  select b1CardAppDto).ToList();
                    C1Card card = C1CardDto2C1Card(result);
                    cards.Add(card);
                }
            }
            return cards;
        }

        /// <summary>取得某段時間內特定員工的請假卡，檢查重覆使用，故不包括頭尾</summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">請假啟始日期</param>
        /// <param name="endDate">請假結束日期</param>
        /// <returns>請假卡 C1CardDto 物件 List</returns>
        public List<C1CardDto> GetCardsBetween(string empNo, DateTime startDate, DateTime endDate)
        {
            DataTable dtCards = _c1CardDao.GetC1CardsBetween(empNo, startDate, endDate);
            List<C1CardDto> formCards = SqlHelper.ConvertDataTable<C1CardDto>(dtCards);
            return formCards;
        }

        /// <summary>
        /// 檢查本次請假與先前請假時數累計是否超過每日正常上班時數
        /// </summary>
        /// <param name="c1Card"></param>
        /// <returns>是否超過每日正常上班時數，並回傳發生日期</returns>
        public (bool, int, DateTime?) CheckAccumulatedOverPermittedWorkingHours(C1Card c1Card)
        {
            bool result = false; // 是否超過每日正常上班時數
            DateTime? occurDate = null;
            int hours = 0;
            // 先找出先前請假的時數
            var previousDayHours = GetAppliedHoursBetween(c1Card.EmpNo, c1Card.StartDate, c1Card.EndDate);

            // 再找出目前請假的時數
            var currentDayHours = CalculateC1CardApplyDayHours(c1Card);

            // 取得員工在日期區間內的工作日
            List<Workday> workdays = _workdayBo.GetEmpWorkdaysDateRange(c1Card.StartDate, c1Card.EndDate, c1Card.EmpNo);

            foreach (var date in currentDayHours.Keys)
            {
                int accumulatedHours = currentDayHours[date];
                if (previousDayHours.ContainsKey(date))
                {
                    accumulatedHours += previousDayHours[date];
                }

                Workday? workday = workdays.FirstOrDefault(x => x.WorkDate == date);
                if (workday != null)
                {
                    if (accumulatedHours > workday.WorkHours) // 請假時數大於工作日時數
                    {
                        result = true;
                        hours = accumulatedHours;
                        occurDate = date;
                        break;
                    }
                }
            }

            return (result, hours, occurDate);
        }


        /// <summary>
        /// 取得單次請假的日期與工時
        /// </summary>
        /// <returns></returns>
        public Dictionary<DateTime, int> CalculateC1CardApplyDayHours(C1Card c1Card)
        {
            Dictionary<DateTime, int> ret = new Dictionary<DateTime, int>();
            DateTime startDateOnly = c1Card.StartDate.Date;
            DateTime endDateOnly = c1Card.EndDate.Date;
            if (startDateOnly > endDateOnly) { return ret; }
            // 產生 startDate 到 endDate 的日期區間
            for (DateTime date = startDateOnly; date <= endDateOnly; date = date.AddDays(1))
            {
                ret.Add(date, 0);
            }
            // 取得員工在日期區間內的工作日
            List<Workday> workdays = _workdayBo.GetEmpWorkdaysDateRange(c1Card.StartDate, c1Card.EndDate, c1Card.EmpNo);
            //累計每日的工時
            for (DateTime date = startDateOnly; date <= endDateOnly; date = date.AddDays(1))
            {
                //找出在 date 的 workday
                var workday = workdays.FirstOrDefault(w => w.WorkDate.Date == date.Date);
                // 找不到 workday 或是 workday 的工時為 0.0 就跳過
                if (workday == null || workday.WorkHours == 0.0) { continue; }
                // 找到請假卡在日期區間內的時數
                if (date == startDateOnly && date == endDateOnly) // 起迄同一天
                {
                    ret[date] += c1Card.Hours; // 直接加入總時數
                }
                else // 起迄不同天
                {
                    if (date == startDateOnly)
                    {
                        int hour = CalculateEmpTakeLeaveWorkingHours(c1Card.EmpNo, c1Card.StartDate, workday.DepartureTime);
                        ret[date] += hour;
                    }
                    else if (date == endDateOnly) // 請假截止日
                    {
                        int hour = CalculateEmpTakeLeaveWorkingHours(c1Card.EmpNo, workday.ArrivalTime, c1Card.EndDate);
                        ret[date] += hour;
                    }
                    else //不是起始日也不是截止日
                    {
                        ret[date] += (int)workday.WorkHours;
                    }
                }
            }
            return ret;
        }

        /// <summary>
        /// 取得日期區間內特定員工已申請與申請中的請假日期與工時，以日期為鍵
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">請假啟始日期</param>
        /// <param name="endDate">請假結束日期</param>
        /// <returns>日期區間內特定員工的請假工時</returns>
        public Dictionary<DateTime, int> GetAppliedHoursBetween(string empNo, DateTime startDate, DateTime endDate)
        {
            Dictionary<DateTime, int> ret = new Dictionary<DateTime, int>();

            if (startDate > endDate) { return ret; }

            // 產生 startDate 到 endDate 的日期區間
            for (DateTime date = startDate.Date; date <= endDate.Date; date = date.AddDays(1))
            {
                ret.Add(date, 0);
            }
            // 取得員工在日期區間內所有的請假
            List<C1CardDto> cards = GetCardsBetween(empNo, startDate.Date, endDate.Date.AddDays(1));
            // 取得員工在日期區間內的工作日
            List<Workday> workdays = _workdayBo.GetEmpWorkdaysDateRange(startDate, endDate, empNo);

            // 累計每日的工時
            foreach (var card in cards)
            {
                if (card.C1_STATUS == (int)FormStatus.Withdraw) { continue; } // 抽單
                if (card.C1_STATUS == (int)FormStatus.Deny) { continue; } // 不同意
                DateTime startDateOnly = card.C1_StartDate.Date;
                DateTime endDateOnly = card.C1_EndDate.Date;
                for (DateTime date = startDateOnly; date <= endDateOnly; date = date.AddDays(1))
                {
                    //找出在 date 的 workday
                    var workday = workdays.FirstOrDefault(w => w.WorkDate.Date == date.Date);
                    // 找不到 workday 或是 workday 的工時為 0.0 就跳過
                    if (workday == null || workday.WorkHours == 0.0) { continue; }
                    // 找到請假卡在日期區間內的時數
                    if (date == startDateOnly && date == endDateOnly) // 起迄同一天
                    {
                        ret[date] += card.C1_HOUR; // 直接加入總時數
                    }
                    else // 起迄不同天
                    {
                        if (date == startDateOnly)
                        {
                            int hour = CalculateEmpTakeLeaveWorkingHours(empNo, card.C1_StartDate, workday.DepartureTime);
                            ret[date] += hour;
                        }
                        else if (date == endDateOnly) // 請假截止日
                        {
                            int hour = CalculateEmpTakeLeaveWorkingHours(empNo, workday.ArrivalTime, card.C1_EndDate);
                            ret[date] += hour;
                        }
                        else //不是起始日也不是截止日
                        {
                            ret[date] += (int)workday.WorkHours;
                        }
                    }
                }
            }
            return ret;
        }


        /// <summary>取得某單號的 C1CardDtos，不含流程與附件等</summary>
        /// <param name="formNumber">相關單號</param>
        /// <returns>卡 Card 物件 List</returns>
        public List<C1CardDto> GetCardsByFormNumber(string formNumber)
        {
            List<C1CardDto> c1CardDtos = new List<C1CardDto>();
            string cacheName = $"CardsByFormNumber-{formNumber}";
            if (_cache.Contains(cacheName))
            {
                c1CardDtos = (List<C1CardDto>)_cache[cacheName];
            }
            else
            {
                DataTable dt = _c1CardDao.GetRelatedCards(formNumber);
                if (dt != null && dt.Rows.Count > 0)
                {
                    c1CardDtos = SqlHelper.ConvertDataTable<C1CardDto>(dt);
                }
                lock (_cacheLock)
                {
                    _cache.Set(cacheName, c1CardDtos, CachePolicy10Seconds);
                }
            }
            return c1CardDtos;
        }

        /// <summary>
        /// 取得假別是否須檢附證明文件
        /// </summary>
        /// <param name="c1Card"></param>
        /// <returns></returns>
        public bool GetCertificateRequired(C1Card c1Card)
        {
            LeaveKind? kind = GetLeaveKind(c1Card.LeaveNumber);
            if (kind != null)
            {
                c1Card.CertificateRequired = kind.CertificateRequired;
            }
            bool ret = c1Card.CertificateRequired;
            return ret;
        }

        /// <summary>
        /// 讀取員工指定月份(依據查詢日期 )休假統計時數，
        /// 包括年度總可用時數、年度已使用時數(簽核中＋已核可)、累至查詢月份已使用時數(簽核中＋已核可)、
        /// 查詢月份已使用時數(簽核中＋已核可)、年度已核可時數(已核可)、
        /// 累至查詢月份年度已核可時數(已核可)、查詢月份已核可時數(已核可)
        /// 提供
        /// 01：特別休息假
        /// 12：補休假
        /// 14：延休假
        /// </summary>
        /// <param name="date">查詢日期</param>
        /// <param name="employeeNumber">員工編號</param>
        public List<EmpLeaveInfo> GetEmpLeaveInfo(DateTime theDate, string employeeNumber)
        {
            List<EmpLeaveInfo> infos = _attendanceBo.GetEmployeeLeaveInformation(theDate, employeeNumber);
            foreach (EmpLeaveInfo empLeaveInfo in infos)
            {
                empLeaveInfo.LeaveName = GetLeaveName(empLeaveInfo.LeaveNumber);
            }
            return infos;
        }

        /// <summary>
        /// 取得員工詳細資料
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns></returns>
        public Employee GetEmployeeDetail(string employeeNumber)
        {
            return _employeeBo.GetEmployeeDetail(employeeNumber);
        }

        // 新版，適用於 非正常班別員工有個人行事曆
        /// <summary>查詢指定期間內員工之行事曆資料。</summary>
        /// <param name="startDate">起始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>List<Workday>(行事曆資料)</returns>
        public List<Workday> GetEmpWorkdaysDateRange(DateTime startDate, DateTime endDate, string empNo)
        {
            return _workdayBo.GetEmpWorkdaysDateRange(startDate, endDate, empNo);
        }

        /// <summary>
        /// 取得同一事件的請假記錄（以事件的第一筆請假卡號查詢相關請假記錄）
        /// </summary>
        /// <param name="formNo">表單編號</param>
        /// <returns></returns>
        public List<EventRelationRecord> GetEventRelatedRecord(string formNo)
        {
            DataTable dt = _c1CardDao.GetEventRelatedRecord(formNo);
            List<EventRelationRecord> records = SqlHelper.ConvertDataTable<EventRelationRecord>(dt);
            return records;
        }

        /// <summary>
        /// 取得同一事件的請假記錄（以事件的第一筆請假卡號查詢相關請假記錄）
        /// </summary>
        /// <param name="formNo">表單編號</param>
        /// <returns></returns>
        public string GetEventRelatedRecordJson(string formNo)
        {
            string json = "[]";
            List<EventRelationRecord> records = GetEventRelatedRecord(formNo);
            if (records.Count > 0)
            {
                json = JsonConvert.SerializeObject(records);
            }
            return json;
        }

        /// 取得指定員工事件假有效期限內的相關卡號資料
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="leaveNumber">假別編號</param>
        /// <param name="leaveSubNumber">假別細項編號</param>
        /// <returns></returns>
        public DataTable GetEventRelatedSheets(string empNo, int leaveNumber, int leaveSubNumber)
        {
            // 規格為 目前日期的前後一年內
            DateTime startDate = DateTime.Now.AddYears(-1);
            DateTime endDate = DateTime.Now.AddYears(1);
            DataTable dtEventRelatedForms = _c1CardDao.GetEventRelatedSheets(empNo, leaveNumber, leaveSubNumber, startDate, endDate);
            return dtEventRelatedForms;
        }

        /// <summary>
        /// 取得指定員工事件假有效期限內的相關卡號資料
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="leaveNumber">假別編號</param>
        /// <param name="leaveSubNumber">假別細項編號</param>
        /// <returns></returns>
        public string GetEventRelatedSheetsJson(string empNo, int leaveNumber, int leaveSubNumber)
        {
            string json = "[]";

            DataTable dt = GetEventRelatedSheets(empNo, leaveNumber, leaveSubNumber);
            if (dt != null && dt.Rows.Count > 0)
            {
                json = JsonConvert.SerializeObject(dt);
            }
            return json;
        }

        /// <summary>
        /// 取得員工某年的第一個工作日期，用以計算請假期限
        /// </summary>
        /// <param name="empNo"></param>
        /// <param name="year"></param>
        /// <returns></returns>
        public DateTime GetFirstWorkdayOfYear(string empNo, int year)
        {
            DateTime date = _workdayBo.FirstWorkDayInMonth(year, 1, empNo);
            return date;
        }

        /// <summary>取得表單及加班卡</summary>
        /// <param name="form">The form.</param>
        /// <param name="userId">The user identifier.</param>
        /// <returns>表單及加班卡 FormCardsDto 物件</returns>
        public FormCard GetFormCard(Form form, string userId)
        {
            FormCard formCard = new FormCard();
            //List<CardBase> cards = new List<CardBase>();
            if (form.FormUID != Guid.Empty && form.FormStatus != 0)
            {
                formCard.ID = form.ID;
                formCard.FormID = form.FormID;
                formCard.FormUID = form.FormUID;
                formCard.FormNo = form.FormNo;
                formCard.FormSubject = form.FormSubject;
                formCard.FormInfo = form.FormInfo;
                formCard.EmpNo = form.EmpNo;
                formCard.EmpName = form.EmpName;
                formCard.DeptNo = form.DeptNo;
                formCard.DeptSName = form.DeptSName;
                formCard.TeamID = form.TeamID;
                formCard.TeamCName = form.TeamCName;
                formCard.CreatedEmpNo = form.CreatedEmpNo;
                formCard.CreatedName = form.CreatedName;
                formCard.FilledTime = form.FilledTime;
                formCard.CreatedTime = form.CreatedTime;
                formCard.CreatedIP = form.CreatedIP;
                formCard.CreatedHost = form.CreatedHost;
                formCard.StartTime = form.StartTime;
                formCard.EndTime = form.EndTime;
                formCard.FormStatus = form.FormStatus;
                formCard.FormStatusName = _formBo.GetFormStatusName(form.FormStatus);
                formCard.TotalSteps = form.TotalSteps;
                formCard.CurrentStep = form.CurrentStep;
                formCard.UpdatedEmpNo = form.UpdatedEmpNo;
                formCard.UpdatedName = form.UpdatedName;
                formCard.UpdatedTime = form.UpdatedTime;
                formCard.UpdatedIP = form.UpdatedIP;
                formCard.UpdatedHost = form.UpdatedHost;
                formCard.AddedSigner = _formBo.AddSignersAddName(form.AddedSigner);
                formCard.Flows = form.Flows;
                formCard.Hours = 0;

                CardBase? cardBase = GetCard(form.FormUID);
                if (cardBase != null)
                {
                    C1Card? c1Card = (C1Card)cardBase;
                    c1Card.AddSigners = _formBo.AddSignersAddName(form.AddedSigner);
                    c1Card.CreatedTime = form.CreatedTime;
                    c1Card.UpdatedTime = form.UpdatedTime;
                    c1Card.FilledTime = form.FilledTime;
                    formCard.Hours += c1Card.Hours;
                    formCard.Card = c1Card;
                    formCard.Attachments = form.Attachments;
                }
            }
            return formCard;
        }

        /// <summary>取得某段時間內表單及卡</summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <returns>表單及卡 FormCardsDto 物件 List</returns>
        public List<FormCard> GetFormCards(DateTime startDate, DateTime endDate)
        {
            DataTable dtCardForms = _c1CardDao.GetC1CardsForms(startDate, endDate);
            List<FormCard> formCards = GetFormCards(startDate, endDate, dtCardForms);
            return formCards;
        }


        /// <summary>取得某段時間內表單及卡</summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單及卡 FormCardsDto 物件 List</returns>
        public List<FormCard> GetFormCards(DateTime startDate, DateTime endDate, string projNo)
        {
            DataTable dtCardForms = _c1CardDao.GetC1CardsForms(startDate, endDate, projNo);
            List<FormCard> formCards = GetFormCards(startDate, endDate, dtCardForms);
            return formCards;
        }

        /// <summary>
        /// 取得總時數
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        public int GetHours(CardBase card)
        {
            C1Card c1Card = (C1Card)card;
            int hours = c1Card.Hours;
            return hours;
        }

        /// <summary>
        /// 取得員工某年的最後一個工作日期，用以計算請假期限
        /// </summary>
        /// <param name="empNo"></param>
        /// <param name="year"></param>
        /// <returns></returns>
        public DateTime GetLastWorkdayOfYear(string empNo, int year)
        {
            DateTime date = _workdayBo.LastWorkDayInMonth(year, 12, empNo);
            return date;
        }

        /// <summary>
        /// 取得假別附件提示
        /// </summary>
        /// <param name="kindEnum"></param>
        /// <returns></returns>
        public string GetLeaveAttachmentPrompt(LeaveKindEnum kindEnum)
        {
            string ret = string.Empty;
            DataTable dt = _c1CardDao.GetLeaveMessage((int)kindEnum);
            string sTemp = ((int)kindEnum).ToString("000");
            int code = int.Parse($"3{sTemp}020");
            if (dt != null && dt.Rows.Count > 0)
            {
                var rows = dt.AsEnumerable()
                 .Where(myRow => myRow.Field<int>("MessageId") == code);
                if (rows != null && rows.Count() == 1)
                {
                    ret = (string)rows.First()["Message"];
                }
            }
            return ret;
        }

        /// <summary>
        /// 取得請假日數時數
        /// </summary>
        /// <param name="leaveNumber">假別編號</param>
        /// <param name="startDate">請假起始日期</param>
        /// <param name="endDate">請假結束日期</param>
        /// <returns>請假日數時數字串，異常時傳回空字串</returns>

        public string GetLeaveDayHours(int leaveNumber, DateTime startDate, DateTime endDate)
        {
            string ret = string.Empty;
            if (leaveNumber != (int)LeaveKindEnum.MaternityLeave) // 產假是例外
            {
                int hours = CalculateTakeLeaveWorkingHours(startDate, endDate);
                int days = CalculateTakeLeaveWorkingDays(hours);
                if (days > 0)
                {
                    ret = $"{days}天";
                }
                int leftHours = hours % AttendanceParameters.DefaultWorkingHours;
                if (leftHours > 0)
                {
                    ret += $"{leftHours}小時";
                }
            }
            else
            {
                TimeSpan timeSpan = endDate.Date - startDate.Date;
                int days = (int)timeSpan.TotalDays + 1;
                ret = $"{days}天";
            }
            return ret;
        }

        /// <summary>
        /// Gets 假別，若無此編號則傳回 Null
        /// </summary>
        /// <param name="leaveNumber">假別編號</param>
        /// <returns>假別</returns>
        public LeaveKind? GetLeaveKind(LeaveKindEnum leaveNumber)
        {
            List<LeaveKind> leaveKinds = GetLeaveKinds();
            //LeaveKind? leaveKind = null;
            //foreach (LeaveKind kind in leaveKinds)
            //{
            //    if (kind.Number == leaveNumber)
            //    {
            //        leaveKind = kind;
            //        break;
            //    }
            //}
            LeaveKind? leaveKind = (from kind in leaveKinds
                                    where kind.Number == leaveNumber
                                    select kind).FirstOrDefault();
            return leaveKind;
        }

        /// <summary>
        /// 依照LeaveDTO取得LeaveKind
        /// </summary>
        /// <param name="leave"></param>
        /// <returns></returns>
        public LeaveKind? GetLeaveKind(LeaveView leave)
        {
            LeaveKind? kind = null;
            List<LeaveKind> kinds = GetLeaveKinds();
            var k = from x in kinds
                    where x.Number == (LeaveKindEnum)leave.LeaveKind
                    select x;
            if (k != null && k.Any())
            {
                kind = k.First();
            }
            return kind;
        }

        /// <summary>
        /// 取得所有休假別
        /// </summary>
        /// <returns></returns>
        public List<LeaveKind> GetLeaveKinds()
        {
            List<LeaveKind> leaveKinds;
            string cacheName = "LeaveKinds";
            if (_cache.Contains(cacheName))
            {
                leaveKinds = (List<LeaveKind>)_cache[cacheName];
            }
            else
            {
                leaveKinds = new List<LeaveKind>();
                DataTable dt = _c1CardDao.GetLeaveKinds();
                DataView view = new DataView(dt);
                DataTable distinctValues = view.ToTable(true, "R1_NO", "R1_NAME", "R1_DisplayOrder");

                foreach (DataRow dr in distinctValues.Rows)
                {
                    string kindNo = (string)dr["R1_NO"];
                    if (kindNo != null)
                    {
                        LeaveKind kind = new LeaveKind();
                        kind.Number = (LeaveKindEnum)int.Parse(kindNo.Trim());
                        if (dr["R1_NAME"] != null && dr["R1_NAME"] != DBNull.Value)
                        {
                            string? sN = dr["R1_NAME"].ToString();
                            if (sN != null)
                            {
                                kind.Name = sN.Trim();
                                kind.DisplayOrder = (int)dr["R1_DisplayOrder"];
                            }
                        }
                        DataRow[] rows = dt.Select($"R1_NO='{((int)kind.Number).ToString("00")}'");
                        if (rows.Length > 0)
                        {
                            string? sUnit = rows[0]["R_UNIT"].ToString();
                            if (sUnit != null)
                            {
                                kind.Unit = sUnit;
                            }
                            string? sMiniMumUnit = rows[0]["MinimumLeaveUnit"].ToString();
                            if (sMiniMumUnit != null)
                            {
                                kind.MinimumUnit = sMiniMumUnit;
                            }
                            if (rows[0]["R_TIME"] != DBNull.Value)
                            {
                                kind.LeaveMaximum = (int)rows[0]["R_TIME"];
                            }
                            kind.IsEvent = (bool)rows[0]["IsEvent"];
                            kind.IsCalendarDay = (bool)rows[0]["IsCalendarDay"];
                            kind.CertificateRequired = (bool)rows[0]["CertificateRequired"];
                            if (rows[0]["R_TIME"] != DBNull.Value)
                            {
                                kind.UpperLimit = (int)rows[0]["R_TIME"];
                            }

                            kind = GetLeaveMessage(kind);
                        }
                        foreach (DataRow row in rows)
                        {
                            if (row["R2_NAME"] != DBNull.Value && row["R2_NO"] != DBNull.Value)
                            {
                                string r2No = (string)row["R2_NO"];
                                string r2Name = (string)row["R2_NAME"];
                                if (!string.IsNullOrWhiteSpace(r2No) && !string.IsNullOrWhiteSpace(r2Name))
                                {
                                    LeaveSubKind detail = new LeaveSubKind();
                                    detail.LeaveSubNumber = int.Parse(r2No.Trim());
                                    detail.LeaveSubName = r2Name;
                                    detail.DisplayOrder = (int)row["R2_DisplayOrder"];
                                    detail.PermitExtraLeave = int.Parse((string)row["R_CHECK"]);
                                    detail.PermittedApplyNumber = int.Parse((string)row["R_APPLY"]);
                                    detail.LeaveSubType = (string)row["R3_NAME"];
                                    if (row["R_TIME"] != DBNull.Value)
                                    {
                                        detail.UpperLimit = (int)row["R_TIME"];
                                    }
                                    kind.Detail.Add(detail);
                                }
                            }
                        }
                        switch (kind.Number)
                        {
                            case LeaveKindEnum.BusinessTripLeave: // 6 出差
                                kind.NeedProject = true;
                                kind.NeedLocation = true;
                                break;
                            case LeaveKindEnum.MaternityLeave: // 7 產假 
                                kind.Gender = 0;
                                kind.AutoEndDate = true;
                                break;
                            case LeaveKindEnum.ObstetricInspectionLeave: // 15 產檢假
                                kind.Gender = 0;
                                break;
                            case LeaveKindEnum.MenstrualLeave: // 16 生理假
                                kind.Gender = 0;
                                kind.AutoEndDate = true;
                                kind.OnlyOneDay = true;
                                break;
                            case LeaveKindEnum.BirthdayLeave: // 21 生日假
                                kind.AutoEndDate = true;
                                kind.OnlyOneDay = true;
                                break;
                            case LeaveKindEnum.FuneralLeave: // 11 喪假
                                break;
                            case LeaveKindEnum.AdjustmentLeave: // 23 調整工時假
                                kind.DisplayOnFrontEnd = false; // 在前端不顯示
                                break;
                            default:
                                kind.AutoEndDate = false;
                                kind.Gender = 2;
                                kind.NeedProject = false;
                                kind.NeedLocation = false;
                                break;
                        }
                        leaveKinds.Add(kind);
                    }
                }
                lock (_cacheLock)
                {
                    _cache.Set(cacheName, leaveKinds, CachePolicy);
                }
            }
            return leaveKinds;
        }

        /// <summary>
        /// 取得此員工依照性別可請的所有休假別
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public List<LeaveKind> GetLeaveKinds(string empNo)
        {
            List<LeaveKind> kinds = GetLeaveKinds();
            Employee? employee = _employeeBo.GetEmployeeDetail(empNo);
            if (employee != null && employee.Sex == "男")
            {
                List<LeaveKind> list = (from kind in kinds
                                        where kind.Gender != 0
                                        select kind).ToList();
                kinds = list;
            }
            else if (employee == null)
            {
                Console.WriteLine($"Error: empNo is null");
            }
            return kinds;
        }


        /// <summary>
        /// 取得所有休假別 JSON 
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public string GetLeaveKindsString(string empNo)
        {
            List<LeaveKind> leaveKinds = GetLeaveKinds(empNo);
            string leaveKindsString = JsonConvert.SerializeObject(leaveKinds);
            return leaveKindsString;
        }


        /// <summary>
        /// 取得假別上限
        /// </summary>
        /// <param name="c1Card"></param>
        /// <returns></returns>
        public int? GetLeaveMaximum(C1Card c1Card)
        {
            int? ret = null;
            LeaveKind? kind = GetLeaveKind(c1Card.LeaveNumber);
            if (kind != null)
            {
                ret = kind.LeaveMaximum;
                if (kind.Detail.Count > 0 && c1Card.LeaveSubNumber > 0)
                {
                    var e = from subkind in kind.Detail
                            where subkind.LeaveSubNumber == c1Card.LeaveSubNumber
                            select subkind;
                    if (e != null && e.Count() > 0)
                    {
                        var k = e.First();
                        if (k != null && k.UpperLimit > 0)
                        {
                            ret = k.UpperLimit;
                        }
                    }
                }
            }
            return ret;
        }

        /// <summary>
        /// 取得假別最小請假單位
        /// </summary>
        /// <param name="c1Card"></param>
        /// <returns></returns>
        public string GetLeaveMinimumUnit(C1Card c1Card)
        {
            string ret = string.Empty;
            LeaveKind? kind = GetLeaveKind(c1Card.LeaveNumber);
            if (kind != null)
            {
                ret = kind.MinimumUnit;
            }
            return ret;
        }

        /// <summary>
        /// 取得假別名稱
        /// </summary>
        /// <param name="leaveNumber"></param>
        /// <returns></returns>
        public string? GetLeaveName(int leaveNumber)
        {
            List<LeaveKind> leaveKinds = GetLeaveKinds();

            //string name = string.Empty;
            //foreach(LeaveKind kind in leaveKinds)
            //{
            //    if((int)kind.Number == leaveNumber)
            //    {
            //        name = kind.FormName;
            //        break;
            //    }
            //}

            string? name = (from k in leaveKinds
                            where (int)k.Number == leaveNumber
                            select k.Name).FirstOrDefault();
            return name;
        }

        /// <summary>
        /// 取得切換假別時需顯示訊息
        /// </summary>
        /// <param name="leaveKindNo">假別代碼</param>
        /// <returns></returns>
        public string GetLeavePreAlert(string leaveKindNo)
        {
            string ret = string.Empty;
            if (!string.IsNullOrWhiteSpace(leaveKindNo))
            {
                LeaveKindEnum leaveKindNumber = (LeaveKindEnum)int.Parse(leaveKindNo);
                switch (leaveKindNumber)
                {
                    case LeaveKindEnum.QuarantineCareLeave: // 17 防疫照顧假
                        ret = AttendanceParameters.QuarantineCareLeave_Pre_Alert;
                        break;
                    case LeaveKindEnum.QuarantineIsolationLeave: // 18 防疫隔離假
                        ret = AttendanceParameters.QuarantineIsolationLeave_Pre_Alert;
                        break;
                    case LeaveKindEnum.VaccinationLeave: // 20 疫苗接種假
                        ret = AttendanceParameters.VaccinationLeave_Pre_Alert;
                        break;
                }
            }
            return ret;
        }

        /// <summary>
        /// 取得假別單位
        /// </summary>
        /// <param name="c1Card"></param>
        /// <returns></returns>
        public string GetLeaveUnit(C1Card c1Card)
        {
            string ret = string.Empty;
            LeaveKind? kind = GetLeaveKind(c1Card.LeaveNumber);
            if (kind != null)
            {
                ret = kind.Unit;
            }
            return ret;
        }

        /// <summary>
        /// 取得列表提醒資訊
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        public (int, string?) GetListRemindMessage(CardBase card)
        {
            string? message = null;
            int messageType = 0;
            C1Card c1Card = (C1Card)card;
            if (!_employeeBo.IsEmployee(c1Card.EmpNo))
            {
                string empName = _employeeBo.GetEmployeeName(c1Card.EmpNo);
                string errorMessage = @"申請人" + AttendanceParameters.IsOffServiceStaff;
                return (99, errorMessage);
            }

            // 簽核提醒
            if (GetCertificateRequired(c1Card))
            {
                messageType = 1;
                message = AttendanceParameters.LeaveCertificateRequired;
            }

            // 防災復原假簽核提醒
            if (c1Card.LeaveNumber == LeaveKindEnum.DisasterRecoveryLeave)
            {
                messageType = 1;
                message = AttendanceParameters.DisasterRecoveryLeaveCertificateRequired;
            }

            return (messageType, message);
        }

        /// <summary>取得相關C1CardDto，不含流程與附件等</summary>
        /// <param name="formNumber">相關單號</param>
        /// <returns>卡 Card 物件 List</returns>
        public List<C1CardDto> GetRelatedCards(string formNumber)
        {
            List<C1CardDto> c1CardDtos = new List<C1CardDto>();
            string cacheName = $"RelatedCards-{formNumber}";
            if (_cache.Contains(cacheName))
            {
                c1CardDtos = (List<C1CardDto>)_cache[cacheName];
            }
            else
            {
                DataTable dt = _c1CardDao.GetCardsByFormNumber(formNumber);
                if (dt != null && dt.Rows.Count > 0)
                {
                    c1CardDtos = SqlHelper.ConvertDataTable<C1CardDto>(dt);
                    lock (_cacheLock)
                    {
                        _cache.Set(cacheName, c1CardDtos, CachePolicy10Seconds);
                    }
                }
            }
            return c1CardDtos;
        }

        /// <summary>
        /// 取得特定月份卡，依表單日期為準，非填表時間
        /// </summary>
        /// <param name="empNo">填表員工編號</param>
        /// <param name="date">日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns></returns>
        public List<CardBase> GetUserCardsMonth(string empNo, DateTime date, int? status = null)
        {
            List<CardBase> cards = new List<CardBase>();
            DataTable dtC1Cards = _c1CardDao.GetC1CardMonth(empNo, date, status);
            if (dtC1Cards != null && dtC1Cards.Rows.Count > 0)
            {
                List<C1CardDto> c1CardDtos = SqlHelper.ConvertDataTable<C1CardDto>(dtC1Cards);
                foreach (C1CardDto c1CardDto in c1CardDtos)
                {
                    List<C1CardDto> list = new List<C1CardDto>
                    {
                        c1CardDto
                    };
                    C1Card c1Card = C1CardDto2C1Card(list);
                    c1Card.FormInfo = GenerateFormInfo(c1Card);
                    c1Card.SetApplicationType();
                    cards.Add(c1Card);
                }
            }
            return cards;
        }

        /// <summary>
        /// 取得特定月份已填表單及卡，包括他人代填與本人填表，依表單年月為準，非填表時間
        /// </summary>
        /// <param name="empNo">填表員工編號</param>
        /// <param name="year">表單年</param>
        /// <param name="month">表單月</param>
        /// <returns></returns>
        public List<FormCard> GetUserSentFormCardsYearMonth(string empNo, int year, int month)
        {
            List<FormCard> formCards = new List<FormCard>();
            DataTable dtCardForms = _c1CardDao.GetSentC1CardYearMonth(empNo, year, month);
            if (dtCardForms != null && dtCardForms.Rows.Count > 0)
            {
                DateTime startDate = DateTime.MaxValue;
                DateTime endDate = DateTime.MinValue;
                foreach (DataRow dr in dtCardForms.Rows)
                {
                    DateTime createTime = (DateTime)dr["CreatedTime"];
                    if (createTime < startDate)
                    {
                        startDate = createTime;
                    }
                    if (createTime > endDate)
                    {
                        endDate = createTime;
                    }
                }
                formCards = GetFormCards(startDate, endDate, dtCardForms);
            }
            return formCards;
        }

        /// <summary>
        /// 員工該年度選擇生日假
        /// </summary>
        /// <param name="year">西元年</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public bool HasChosenBirthdayLeave(int year, string empNo)
        {
            bool ret = false;
            DataTable dt = _c1CardDao.GetBirthdayWelfare(year, empNo);
            if (dt.Rows.Count > 0)
            {
                DataRow dr = dt.Rows[0];
                int birthdayWelfare = (int)dr["BirthdayWelfare"];
                if (birthdayWelfare == 1)
                {
                    ret = true;
                }
            }
            return ret;
        }

        /// <summary>
        /// 是否為管理員
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public bool IsAdmin(string empNo)
        {
            return _attendanceBo.IsAdmin(empNo);
        }

        /// <summary>
        /// 是否為現職員工
        /// </summary>
        /// <param name="employeeNumber">The employee number.</param>
        /// <returns>
        ///   <c>true</c> 是現職員工，否則傳回 <c>false</c>.
        /// </returns>
        public bool IsEmployee(string employeeNumber)
        {
            return _employeeBo.IsEmployee(employeeNumber);
        }

        /// <summary>
        /// 是否為工作日
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public bool IsWorkday(DateTime date, string empNo)
        {
            return _workdayBo.IsWorkday(date, empNo);
        }

        /// <summary>Find the last work day in month.</summary>
        /// <param name="year">The year. 西元年</param>
        /// <param name="month">The month. 月分</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>最後一天工作日的日期(int)</returns>
        public DateTime LastWorkDayInMonth(int year, int month, string empNo)
        {
            DateTime date = _workdayBo.LastWorkDayInMonth(year, month, empNo);
            return date;
        }

        /// <summary>
        /// 取得切換假別時需顯示之訊息
        /// </summary>
        /// <param name="leaveKindNo">假別代碼</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>訊息</returns>
        public string LeaveKindAllowed(string leaveKindNo, string empNo)
        {
            string ret = string.Empty;
            if (!string.IsNullOrWhiteSpace(leaveKindNo))
            {
                Employee employee = _employeeBo.GetEmployeeDetail(empNo);
                LeaveKindEnum leaveKindNumber = (LeaveKindEnum)int.Parse(leaveKindNo);
                switch (leaveKindNumber)
                {
                    case LeaveKindEnum.MaternityLeave: // 7 產假 
                        if (employee.Sex == "男")
                        {
                            ret = AttendanceParameters.OnlyWomenCanApplyForMaternityLeave;
                        }
                        break;
                    case LeaveKindEnum.ObstetricInspectionLeave: // 15 產檢假
                        if (employee.Sex == "男")
                        {
                            ret = AttendanceParameters.OnlyWomenCanApplyForObstetricInspectionLeave;
                        }
                        break;
                    case LeaveKindEnum.MenstrualLeave: // 16 生理假
                        if (employee.Sex == "男")
                        {
                            ret = AttendanceParameters.OnlyWomenCanApplyForMenstrualLeave;
                        }
                        break;
                }
            }
            return ret;
        }

        /// <summary>
        /// 假單設定簽核關卡
        /// </summary>
        /// <param name="c1Card">假單</param>
        /// <param name="form">表單</param>
        /// <param name="employee">員工</param>
        public void SetFlows(C1Card c1Card, Form form, Employee employee)
        {
            if (!string.IsNullOrWhiteSpace(c1Card.Deputy))
            {
                // 通知代理人
                Employee substitute = _employeeBo.GetEmployeeDetail(c1Card.Deputy);
                _formFlowBo.FlowAddNotifySubstitute(form, substitute);
            }

            // 加會人員關卡
            if (!string.IsNullOrWhiteSpace(c1Card.AddSigners))
            {
                if (c1Card.AddSigners.EndsWith(','))
                {
                    c1Card.AddSigners = c1Card.AddSigners.Substring(0, c1Card.AddSigners.Length - 1);
                }
                form.AddedSigner = c1Card.AddSigners;
                string[] signers = c1Card.AddSigners.Split(',');
                _formFlowBo.FlowAddSigners(form, signers);
            }

            // 去除重覆關卡
            form.Flows = _formFlowBo.FlowDedup(form.Flows);

            // Factory 產生物件，自動計算日數
            C1CardBase? leave = C1CardFactory.CreateLeave(c1Card, this);
            if (leave != null)
            {
                leave.CalculateHours(); // 應該自動計算

                //if (c1CardDtos.IsEventLeave)
                //{
                //    c1CardDtos.CalculateExpirationDate();// 應該自動計算
                //}
                //c1CardDtos.Hours = leaveDto.Hours;
            }
            else
            {
                throw new Exception("產生假別物件錯誤");
                //return;
            }
            //依照職級判斷後續關卡
            B1CardPositionEnum position = _attendanceBo.GetPositionType(employee.EmpNo);
            switch (position)
            {
                case B1CardPositionEnum.Chairman: // 董事長
                    break;
                case B1CardPositionEnum.President: // 執行長
                    // 董事長簽核
                    _formFlowBo.FlowAddChairman(form);
                    break;
                case B1CardPositionEnum.VicePresident: // 副執行長
                    // 執行長簽核
                    _formFlowBo.FlowAddCEO(form);
                    // 超過3天 董事長簽核
                    if (leave.Days >= 3)
                    {
                        _formFlowBo.FlowAddChairman(form);
                    }
                    break;
                // 部門主管
                case B1CardPositionEnum.Manager: // 經理
                case B1CardPositionEnum.Director: // 主任
                    // 副執行長簽核
                    _formFlowBo.FlowAddDeputyCEO(form);

                    // 超過2天 執行長簽核
                    if (leave.Days >= 2)
                    {
                        _formFlowBo.FlowAddCEO(form);
                    }

                    // 超過7天 董事長簽核
                    if (leave.Days >= 7)
                    {
                        _formFlowBo.FlowAddChairman(form);
                    }
                    break;
                //case B1CardPositionEnum.DeputyManager:
                //case B1CardPositionEnum.DeputyDirector:
                //case B1CardPositionEnum.ProjectDirector:
                //case B1CardPositionEnum.ProjectDeputyDirector:
                //case B1CardPositionEnum.ChiefAccountant:
                //case B1CardPositionEnum.SectionChief:
                //case B1CardPositionEnum.ProjectSectionChief:
                //case B1CardPositionEnum.GeneralStaff:
                default:
                    // 申請人部門主管簽核
                    _formFlowBo.FlowAddManager(form, employee);

                    // 超過3天 副執行長簽核
                    if (leave.Days >= 3)
                    {
                        _formFlowBo.FlowAddDeputyCEO(form);
                    }

                    // 超過7天 執行長簽核
                    if (leave.Days >= 7)
                    {
                        _formFlowBo.FlowAddCEO(form);
                    }
                    break;
            }

            // 通知申請人部門登記桌
            _formFlowBo.FlowAddDepartmentMailroom(form, employee, true);
        }

        /// <summary>
        /// 將 C1CardDetail依照月份拆單
        /// </summary>
        /// <param name="c1Card"></param>
        /// <returns></returns>
        public List<C1CardDetail> SplitC1CardDetail(C1Card c1Card)
        {
            List<C1CardDetail> list = c1Card.Details;
            if (c1Card.StartDate.Year == c1Card.EndDate.Year && c1Card.StartDate.Month == c1Card.EndDate.Month)
            {
                list = c1Card.Details; // 同月份不拆單
            }
            else if (c1Card.Details.Count == 1) // 只有一張表示未拆單
            {
                switch (c1Card.LeaveNumber)
                {
                    case LeaveKindEnum.MaternityLeave: // 產假 以日曆天計
                        list = SplitC1CardDetail(c1Card, true);
                        break;
                    default:
                        list = SplitC1CardDetail(c1Card, false);
                        break;
                }
            }
            return list;
        }

        /// <summary>轉換為Stored Procedure所需的 User Defined Table</summary>
        /// <param name="card">三卡</param>
        /// <param name="isAdd">是否為新增</param>
        /// <returns></returns>
        public List<DataTable> ToUserDefinedDataTables(CardBase card, bool isAdd = true)
        {
            List<C1CardDto> list = C1CardToC1CardDtos(card);
            C1Card c1Card = (C1Card)card;
            DataTable dt = SqlHelper.CreateDataTable<C1CardDto>(list);
            dt.TableName = "C1Card";
            foreach (DataRow dr in dt.Rows)
            {
                if (c1Card.EventDate == null || c1Card.EventDate == DateTime.MinValue)
                {
                    dr["C1_EventDate"] = DBNull.Value;
                }
                if (c1Card.ExpirationStartDate == DateTime.MinValue)
                {
                    dr["C1_DeadlineStartDate"] = DBNull.Value;
                }
                if (c1Card.ExpirationEndDate == DateTime.MinValue || c1Card.ExpirationEndDate == DateTime.MaxValue)
                {
                    dr["C1_DeadlineEndDate"] = DBNull.Value;
                }
                dr["ExpDate"] = DBNull.Value;
                if (c1Card.ADate == null)
                {
                    dr["C1_ADate"] = DBNull.Value;
                }
            }

            List<DataTable> dataTables = new List<DataTable> { dt };
            return dataTables;
        }

        /// <summary>更新Card</summary>
        /// <param name="card">The card.</param>
        /// <param name="updateDto">更新資料物件</param>
        public void Update(CardBase card, Updater updateDto)
        {
            C1Card c1Card = (C1Card)card;
            if (updateDto.UpdatedIP != null)
            {
                c1Card.UpdatedName = updateDto.UpdatedName;
                c1Card.UpdatedIP = updateDto.UpdatedIP;
                c1Card.UpdatedHost = updateDto.UpdatedHost;
            }
            c1Card.UpdatedTime = updateDto.UpdatedTime;
            c1Card.UpdatedEmpNo = updateDto.UpdatedEmpNo;
            c1Card.ADate = updateDto.UpdatedTime;
        }

        /// <summary>抽單</summary>
        /// <param name="card"></param>
        /// <param name="withdraw"></param>
        /// <returns>
        ///   <br />
        /// </returns>
        public bool Withdraw(CardBase card, Withdraw withdraw)
        {
            bool ret = false;
            if (withdraw != null && withdraw.FormUID != Guid.Empty)
            {
                C1Card c1Card = (C1Card)card;
                c1Card.Status = (int)FormStatus.Withdraw;
                c1Card.ADate = withdraw.WithdrawTime;
                ret = true;
            }
            return ret;
        }
        #region IfLeaveDataExists

        /// <summary>
        /// 檢查個人休假資料表是否有該員工指定年月範圍的資料
        /// </summary>
        /// <param name="empNumber">員工編號</param>
        /// <param name="from">指定年月</param>
        /// <param name="to">指定年月</param>
        /// <returns>若指定個人休假資料表存在則回傳 true</returns>
        public bool IfLeaveRecordExists(string empNumber, DateTime from, DateTime to)
        {
            var range = GetCheckLeaveYearMonthRange(from, to);
            if (range.Count == 0)
            {
                return false;
            }

            foreach (var item in range)
            {
                if (!CheckIfLeaveDataExists(empNumber, item))
                {
                    return false;
                }
            }

            return true;
        }

        private List<DateTime> GetCheckLeaveYearMonthRange(DateTime from, DateTime to)
        {
            from = from.Date;
            to = to.Date.AddDays(1).AddTicks(-1);

            var ft = 0;
            var ft0 = 0;
            var date = new List<DateTime>();
            while (from <= to)
            {
                ft0 = ft;
                ft = from.Year * 100 + from.Month;
                if (ft != ft0)
                {
                    date.Add(from);
                }

                from = from.AddDays(1);
            }
            return date;
        }

        /// <summary>
        /// 檢查個人休假資料表是否有該員工指定年月的資料
        /// </summary>
        /// <param name="empNumber">員工編號</param>
        /// <param name="applyDate">指定年月</param>
        /// <returns>若指定個人休假資料表存在則回傳 true</returns>
        private bool CheckIfLeaveDataExists(string empNumber, DateTime applyDate)
        {
            return _c1CardDao.IfAnnualLeaveDataExists(empNumber, applyDate);
        }

        #endregion

        #region IfPostponedLeaveExists

        /// <summary>
        /// 檢查該員工剩餘年度延休假時數（扣除「已同意」及「簽核中」)
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="year">查詢年度，以西元年表示</param>
        /// <returns>該員工剩餘年度延休假時數</returns>
        public int GetPostponedLeaveRemainingHours(string employeeNumber, DateTime dt)
        {
            return _c1CardDao.GetPostponedLeaveRemainingHours(employeeNumber, dt);
        }

        #endregion

        #region GetAnnualLeaveRemainingHour

        /// <summary>
        /// 取出該員工剩餘年休假時數（扣除「已同意」及「簽核中」)
        /// </summary>
        /// <param name="empNumber">員工編號</param>
        /// <param name="year">查詢年度，以西元年表示</param>
        /// <returns>該員工剩餘年休假時數</returns>
        public int GetAnnualLeaveRemainingHours(string employeeNumber, DateTime dt)
        {
            return _c1CardDao.GetAnnualLeaveRemainingHours(employeeNumber, dt);
        }

        #endregion


        #region GetCompensatoryLeaveRemainingHours

        /// <summary>
        /// 取得補修日(含時間)前可補休的時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="applyDateTime">指定的補休時間</param>
        /// <returns></returns>
        public int GetCompensatoryLeaveRemainingHours(string employeeNumber, DateTime applyDateTime)
        {
            return _c1CardDao.GetCompensatoryLeaveRemainingHours(employeeNumber, applyDateTime);
        }

        #endregion

        #region IsMenstrualLeaveAlreadyTaken

        /// <summary>
        /// 是否指定月份已經申請生理假
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="year">查詢年度，以西元年表示</param>
        /// <param name="month">查詢月份</param>
        /// <returns>是否已申請</returns>
        public bool IsMenstrualLeaveAlreadyTaken(string employeeNumber, int year, int month)
        {
            return _c1CardDao.IsMenstrualLeaveAlreadyTaken(employeeNumber, year, month);
        }

        #endregion

        /// <summary>
        /// 取得年度已休生理假時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>年度已休生理假時數</returns>
        public int GetMenstrualLeaveYearUsedHours(string employeeNumber, DateTime dt)
        {
            return _c1CardDao.GetMenstrualLeaveYearUsedHours(employeeNumber, dt);
        }

        ///// <summary>
        ///// 取得年度病假統計時數
        ///// </summary>
        ///// <param name="employeeNumber">員工編號</param>
        ///// <param name="year">查詢年度，以西元年表示</param>
        ///// <param name="yearAvailableHours">年度總可用時數</param>
        ///// <param name="yearUsedHours">年度已使用時數(簽核中＋已核可)</param>
        ///// <param name="yearApprovedHours">年度已核可時數</param>
        //public void GetSickLeaveYearInfo(string employeeNumber, int year, out int yearAvailableHours, out int yearUsedHours, out int yearApprovedHours)
        //{
        //    _c1CardDao.GetSickLeaveYearInfo(employeeNumber, year, out yearAvailableHours, out yearUsedHours, out yearApprovedHours);
        //}

        /// <summary>
        /// 取得病假年度總可用時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>年度總可用時數</returns>
        public int GetSickLeaveAvailableHours(string employeeNumber, DateTime dt)
        {
            _c1CardDao.GetSickLeaveInfo(employeeNumber, dt,
                out int yearAvailableHours, out _, out _);
            return yearAvailableHours;
        }

        /// <summary>
        /// 取得病假年度已使用時數(簽核中＋已核可)
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>年度已使用時數(簽核中＋已核可)</returns>
        public int GetSickLeaveUsedHours(string employeeNumber, DateTime dt)
        {
            _c1CardDao.GetSickLeaveInfo(employeeNumber, dt,
                out _, out int yearUsedHours, out _);
            return yearUsedHours;
        }

        /// <summary>
        /// 取得病假年度已核可時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>年度已核可時數</returns>
        public int GetSickLeaveApprovedHours(string employeeNumber, DateTime dt)
        {
            _c1CardDao.GetSickLeaveInfo(employeeNumber, dt,
                out int _, out _, out int yearApprovedHours);
            return yearApprovedHours;
        }

        ///// <summary>
        ///// 取得年度事假統計時數
        ///// </summary>
        ///// <param name="employeeNumber">員工編號</param>
        ///// <param name="year">查詢年度，以西元年表示</param>
        ///// <param name="yearAvailableHours">年度總可用時數</param>
        ///// <param name="yearUsedHours">年度已使用時數(簽核中＋已核可)</param>
        ///// <param name="yearApprovedHours">年度已核可時數</param>
        //public void GetPersonalLeaveYearInfo(string employeeNumber, int year, 
        //    out int yearAvailableHours, out int yearUsedHours, out int yearApprovedHours)
        //{
        //    _c1CardDao.GetPersonalLeaveYearInfo(employeeNumber, year, 
        //        out yearAvailableHours, out yearUsedHours, out yearApprovedHours);
        //}

        /// <summary>
        /// 取得事假年度總可用時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>年度總可用時數</returns>
        public int GetPersonalLeaveYearAvailableHours(string employeeNumber, DateTime dt)
        {
            _c1CardDao.GetPersonalLeaveYearInfo(employeeNumber, dt,
                out int yearAvailableHours, out _, out _);

            return yearAvailableHours;
        }

        /// <summary>
        /// 取得事假年度已使用時數(簽核中＋已核可)
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>年度已使用時數(簽核中＋已核可)</returns>
        public int GetPersonalLeaveYearUsedHours(string employeeNumber, DateTime dt)
        {
            _c1CardDao.GetPersonalLeaveYearInfo(employeeNumber, dt,
                out _, out int yearUsedHours, out _);

            return yearUsedHours;
        }

        /// <summary>
        /// 取得事假年度已核可時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>年度已核可時數</returns>
        public int GetPersonalLeaveYearApprovedHours(string employeeNumber, DateTime dt)
        {
            _c1CardDao.GetPersonalLeaveYearInfo(employeeNumber, dt,
                out _, out _, out int yearApprovedHours);

            return yearApprovedHours;
        }

        ///// <summary>
        ///// 取得年度家庭照顧假統計時數
        ///// </summary>
        ///// <param name="employeeNumber">員工編號</param>
        ///// <param name="year">查詢年度，以西元年表示</param>
        ///// <param name="yearUsedHours">年度已使用時數(簽核中＋已核可)</param>
        ///// <param name="yearApprovedHours">年度已核可時數</param>
        //public void GetFamilyCareLeaveYearInfo(string employeeNumber, int year, 
        //    out int yearAvailableHours, out int yearUsedHours, out int yearApprovedHours)
        //{
        //    _c1CardDao.GetFamilyCareLeaveYearInfo(employeeNumber, year,
        //        out yearAvailableHours, out yearUsedHours, out yearApprovedHours);
        //}


        /// <summary>
        /// 取得家庭照顧假年度總可用時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>年度總可用時數</returns>
        public int GetFamilyCareLeaveYearAvailableHours(string employeeNumber, DateTime dt)
        {
            _c1CardDao.GetFamilyCareLeaveYearInfo(employeeNumber, dt,
                out int yearAvailableHours, out _, out _);

            return yearAvailableHours;
        }

        /// <summary>
        /// 取得家庭照顧假年度已使用時數(簽核中＋已核可)
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>年度已使用時數(簽核中＋已核可)</returns>
        public int GetFamilyCareLeaveYearUsedHours(string employeeNumber, DateTime dt)
        {
            _c1CardDao.GetFamilyCareLeaveYearInfo(employeeNumber, dt,
                out _, out int yearUsedHours, out _);

            return yearUsedHours;
        }

        /// <summary>
        /// 取得家庭照顧假年度已核可時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>年度已核可時數</returns>

        public int GetFamilyCareLeaveYearApprovedHours(string employeeNumber, DateTime dt)
        {
            _c1CardDao.GetFamilyCareLeaveYearInfo(employeeNumber, dt,
                out _, out _, out int yearApprovedHours);

            return yearApprovedHours;
        }

        ///// <summary>
        ///// 取得員工生日假資料，包括查詢年度選擇的生日假方案、是否已經使用生日假
        ///// </summary>
        ///// <param name="employeeNumber">員工編號</param>
        ///// <param name="year">查詢年度，以西元年表示</param>
        ///// <param name="birthdayWelfare">查詢年度的生日假方案，0-查無資料，1-生日假，2-生日禮券</param>
        ///// <param name="isUsed">查詢年度是否已經使用生日假</param>
        //public void GetBirthdayLeaveInfo(string employeeNumber, int year, out int birthdayWelfare, out bool isUsed)
        //{
        //    _c1CardDao.GetBirthdayLeaveInfo(employeeNumber, year, out birthdayWelfare, out isUsed);
        //}

        /// <summary>
        /// 取得員工查詢年度選擇的生日假方案
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="year">查詢年度，以西元年表示</param>
        /// <returns>查詢年度的生日假方案，0-查無資料，1-生日假，2-生日禮券</returns>
        public int GetBirthdayWelfare(string employeeNumber, int year)
        {
            _c1CardDao.GetBirthdayLeaveInfo(employeeNumber, year, out int birthdayWelfare, out _);
            return birthdayWelfare;
        }

        /// <summary>
        /// 取得員工年度是否已經使用生日假
        /// </summary>
        /// <param name="employeeNumber"></param>
        /// <param name="year"></param>
        /// <returns>是否已經使用生日假</returns>
        public bool IsBirthdayLeaveTaken(string employeeNumber, int year)
        {
            _c1CardDao.GetBirthdayLeaveInfo(employeeNumber, year, out _, out bool isUsed);
            return isUsed;
        }

        /// <summary>
        /// 取得育嬰假年度總可用時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>年度總可用時數</returns>
        public int GetParentalLeaveYearAvailableHours(string employeeNumber, DateTime dt)
        {
            _c1CardDao.GetParentalLeaveYearInfo(employeeNumber, dt,
                out int yearAvailableHours, out _, out _);

            return yearAvailableHours;
        }

        /// <summary>
        /// 取得育嬰假年度已使用時數(簽核中＋已核可)
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>年度已使用時數(簽核中＋已核可)</returns>
        public int GetParentalLeaveYearUsedHours(string employeeNumber, DateTime dt)
        {
            _c1CardDao.GetParentalLeaveYearInfo(employeeNumber, dt,
                out _, out int yearUsedHours, out _);

            return yearUsedHours;
        }

        /// <summary>
        /// 取得育嬰假年度已核可時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>年度已核可時數</returns>
        public int GetParentalLeaveYearApprovedHours(string employeeNumber, DateTime dt)
        {
            _c1CardDao.GetParentalLeaveYearInfo(employeeNumber, dt,
                 out _, out _, out int yearApprovedHours);

            return yearApprovedHours;
        }


        #region  Get Employee Information

        /// <summary>
        /// 取出該員工生日
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns>該員工生日</returns>
        public DateTime GetBirthday(string employeeNumber)
        {
            var employee = this.GetEmployeeDetail(employeeNumber);
            if (employee.Birthday == null)
            {
                throw new ArgumentNullException();
            }
            return (DateTime)employee.Birthday;
        }

        /// <summary>
        /// 判斷該員工是否為女性
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns>若為女性則傳回true</returns>
        public bool IsFemale(string employeeNumber)
        {
            var employee = this.GetEmployeeDetail(employeeNumber);
            return employee.Sex.Trim() == "女";
        }


        #endregion
    }
}
