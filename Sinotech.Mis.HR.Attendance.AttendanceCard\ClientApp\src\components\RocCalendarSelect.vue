<template>
  <div :style="{ 'margin-bottom': '-1rem' }">
    <input
      type="text"
      readonly
      :class="['p-inputtext p-component', props.inputClass]"
      :style="props.inputStyle"
      :disabled="props.disabled"
      :value="rocDate"
      @click="onInputClick"
    >
  </div>
  <RocCalendar
    ref="calendarRef"
    :inputStyle="{ display: 'none' }"
    :modelValue="props.modelValue"
    :selectOtherMonths="true"
    @date-select="updateDate"
    @month-change="updateYearMonth"
    @year-change="updateYearMonth"
  >
    <template #header>
      <slot name="header" />
    </template>
    <template #footer>
      <slot name="footer" />
    </template>
    <template #date="{ date }">
      <slot
        name="date"
        :date="date"
      >
        {{ date.day }}
      </slot>
    </template>
    <template #decade="{ years }">
      <slot
        name="decade"
        :years="years" 
      />
    </template>
  </RocCalendar>
</template>
<script lang="ts">
import RocCalendar from './RocCalendar.vue'
const inheritedProps = (RocCalendar.extends as any).extends.props
inheritedProps.disabled = false
</script>
<script setup lang="ts">
import { ref, computed } from 'vue'
import type { DayType } from '../api/appType'
const props = defineProps(inheritedProps)

const emits = defineEmits<{
  (event: 'update:modelValue', value: Date): void
  (event: 'update:year-month', value: DayType): void
  (event: 'click'): void
}>()

const calendarRef = ref<any>()

const updateDate = (newValue: Date): void => {
  if (props.modelValue?.getTime() !== newValue.getTime()) {
    emits('update:modelValue', newValue)
  }
}

const updateYearMonth = (event: DayType): void => {
  event.month -= 1
  emits('update:year-month', event)
}

const rocDate = computed<string>((): string => {
  if (props.modelValue) {
    let time = ''
    if (props.showTime) {
      time += props.modelValue.getHours().toString().padStart(2, '0') + ':' + props.modelValue.getMinutes().toString().padStart(2, '0')
    }
    return '民國' + (props.modelValue.getFullYear() - 1911) + '年' + (props.modelValue.getMonth() + 1) + '月' + props.modelValue.getDate() + '日' + time
  }
  return ''
})

const onInputClick = (): void => {
  emits('click')
  calendarRef.value.updateCurrentMetaData()
  setTimeout((): void => calendarRef.value.onButtonClick(), 0)
}
</script>