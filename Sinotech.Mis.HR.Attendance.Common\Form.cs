﻿using Sinotech.Mis.Helpers;
using System;
using System.Collections.Generic;
using System.Data;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 表單DTO ，欄位順序不可更改
    /// </summary>
    public class Form
    {
        /// <summary>
        /// 流水號
        /// </summary>
        public int? ID { get; set; }

        /// <summary>
        /// 表單識別碼
        /// </summary>
        public Guid FormUID { get; set; } = Guid.Empty;

        /// <summary>
        /// 表單編號
        /// </summary>
        public string FormID { get; set; } = string.Empty;

        /// <summary>
        /// 表單單號
        /// </summary>
        public string FormNo { get; set; } = string.Empty;

        /// <summary>
        /// 表單主旨
        /// </summary>
        public string FormSubject { get; set; } = string.Empty;

        /// <summary>
        /// 表單資訊
        /// </summary>
        public string FormInfo { get; set; } = string.Empty;

        /// <summary>
        /// 申請人員工編號
        /// </summary>
        public string EmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 申請人姓名
        /// </summary>
        public string EmpName { get; set; } = string.Empty;

        /// <summary>
        /// 申請人部門編號
        /// </summary>
        public int DeptNo { get; set; }

        /// <summary>
        /// 申請人部門簡稱
        /// </summary>
        public string DeptSName { get; set; } = string.Empty;

        /// <summary>
        /// 申請人組別編號
        /// </summary>
        public int? TeamID { get; set; }

        /// <summary>
        /// 申請人組別名稱
        /// </summary>
        public string? TeamCName { get; set; }

        /// <summary>
        /// 職級編號
        /// </summary>
        public string RankNo { get; set; } = string.Empty;
        /// <summary>
        /// 職級名稱
        /// </summary>
        public string RankName { get; set; } = string.Empty;
        /// <summary>
        /// 職務編號
        /// </summary>
        public string? JobNo { get; set; }
        /// <summary>
        /// 職務名稱
        /// </summary>
        public string? JobName { get; set; }
        /// <summary>
        /// 申請內容開始日期
        /// </summary>
        public DateTime? ContentStartTime { get; set; }
        /// <summary>
        /// 申請內容結束日期
        /// </summary>
        public DateTime? ContentEndTime { get; set; }

        /// <summary>
        /// 填表人員工編號
        /// </summary>
        public string CreatedEmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 填表人姓名
        /// </summary>
        public string CreatedName { get; set; } = string.Empty;

        /// <summary>
        /// 開始填表時間
        /// </summary>
        public DateTime FilledTime { get; set; }

        /// <summary>
        /// 資料建立時間
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 資料建立來源IP 位址
        /// </summary>
        public string CreatedIP { get; set; } = string.Empty;

        /// <summary>
        /// 資料建立來源電腦名稱
        /// </summary>
        public string? CreatedHost { get; set; }

        /// <summary>
        /// 加會人員員工編號
        /// </summary>
        public string? AddedSigner { get; set; }

        /// <summary>
        /// 表單流程開始日期時間
        /// </summary>
        public DateTime StartTime { get; set; }


        /// <summary>
        /// 表單流程結案日期時間
        /// </summary>
        public DateTime? EndTime { get; set; }


        /// <summary>
        /// 表單簽核狀態(資料庫是 tinyint)
        /// </summary>
        public int FormStatus { get; set; }

        /// <summary>
        /// 表單簽核狀態名稱
        /// </summary>
        public string FormStatusName { get; set; } = string.Empty;

        /// <summary>
        /// 總關卡數(共幾關)
        /// </summary>
        public int TotalSteps { get; set; } = 0;

        /// <summary>
        /// 目前關卡
        /// </summary>
        public int CurrentStep { get; set; }

        /// <summary>
        /// 最後修改者員工編號
        /// </summary>
        public string? UpdatedEmpNo { get; set; }

        /// <summary>
        /// 最後修改者姓名
        /// </summary>
        public string? UpdatedName { get; set; }

        /// <summary>
        /// 最後修改日期時間
        /// </summary>
        public DateTime? UpdatedTime { get; set; }

        /// <summary>
        /// 最後修改來源IP 位址
        /// </summary>
        public string? UpdatedIP { get; set; }

        /// <summary>
        /// 最後修改來源電腦名稱
        /// </summary>
        public string? UpdatedHost { get; set; }

        /// <summary>
        /// 表單流程
        /// </summary>
        public List<FormFlow> Flows { get; set; } = new List<FormFlow>();

        /// <summary>
        /// 表單附件
        /// </summary>
        public List<FormAttachment>? Attachments { get; set; } = null;

        /// <summary>
        /// 轉換為資料庫用的DataTable欄位
        /// </summary>
        /// <returns></returns>
        public DataTable ToUserDefinedDataTable()
        {
            List<Form> list = new List<Form>();
            list.Add(this);
            DataTable dt = SqlHelper.CreateDataTable<Form>(list);
            dt.TableName = "Form";
            dt.Columns.Remove("ID");
            dt.Columns.Remove("Flows");
            dt.Columns.Remove("FormStatusName");
            dt.Columns.Remove("Attachments");
            return dt;
        }
    }
}
