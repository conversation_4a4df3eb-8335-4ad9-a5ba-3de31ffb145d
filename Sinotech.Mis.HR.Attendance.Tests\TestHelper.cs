﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Sinotech.Mis.Helpers;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Security.Claims;

namespace Sinotech.Mis.HR.Attendance.Tests
{
#nullable enable
    [ExcludeFromCodeCoverage]
    public static class TestHelper
    {
        [ExcludeFromCodeCoverage]
        public static IServiceCollection AddConfiguration(this IServiceCollection services, IConfiguration config)
        {
            //services.AddSingleton<IProjectDao, ProjectDao>
            //    (sp => new ProjectDao(config.GetSecuredConnectionString("Project")));
            //services.AddSingleton<ProjectBo>();
            services.AddSingleton<IConfiguration>(config);
            // (sp => (IConfiguration)config));
            services.AddSingleton<IConfiguration>();

            return services;
        }

        /// <summary>
        /// 清除測試資料
        /// </summary>
        /// <param name="connectionString"></param>
        public static void ClearData(string connectionString)
        {
            string sql = """
DELETE FROM B1RATE_CompHol;DELETE FROM B1RATEOK;DELETE FROM B1RATE;DELETE FROM dbo.LogEvents;
DELETE FROM FormAttachment;DELETE FROM Notification;DELETE FROM A1Card;DELETE FROM B1CardApp;
DELETE FROM B1Card;DELETE FROM C1Card;DELETE FROM FormFlow; DELETE FROM Form; DELETE From AccountLockout; 
DELETE From AttendanceLogInOut;UPDATE FormCounter SET FormNo=0;
UPDATE REST SET R_USEVAC2=0 WHERE ( R_YYMM LIKE '113%' OR R_YYMM LIKE '112%' );
UPDATE RESTOK SET R_USEVAC2=0 WHERE ( R_YYMM LIKE '113%' OR R_YYMM LIKE '112%' );
""";
            SqlHelper.ExecuteSqlNonQuery(connectionString, sql);
        }

        /// <summary>
        /// Mock登入者為 SECINC\EmpNo
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>

        public static HttpContext FakeHttpContextEmpNo(string empNo)
        {
            return FakeHttpContext(empNo, "SECINC");
        }

        /// <summary>
        /// Mock登入者為 EmpNo
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public static HttpContext FakeHttpContextEmpNoInternet(string empNo)
        {
            return FakeHttpContext(empNo);
        }

        /// <summary>
        /// Mock登入者為 EmpNo 或 domain\EmpNo
        /// </summary>
        /// <param name="empNo"></param>
        /// <param name="domain"></param>
        /// <returns></returns>
        private static HttpContext FakeHttpContext(string empNo, string? domain=null)
        {
            string domainUser = domain != null ? $@"{domain}\{empNo}" : empNo;
            var context = new ControllerContext();
            var claims = new List<Claim>()
            {
                new Claim(ClaimTypes.Name, domainUser),
                new Claim(ClaimTypes.NameIdentifier, empNo),
                new Claim("name", empNo),
            };
            var identity = new ClaimsIdentity(claims, "TestAuthType");
            var claimsPrincipal = new ClaimsPrincipal(identity);

            var httpContext = new DefaultHttpContext();
            context.HttpContext = httpContext;
            context.HttpContext.User = claimsPrincipal;

            context.HttpContext.Connection.RemoteIpAddress = System.Net.IPAddress.Parse("127.0.0.1");
            return httpContext;
        }
    }
}
