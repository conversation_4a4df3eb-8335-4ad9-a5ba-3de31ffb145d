﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Sinotech.Mis.Common;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.DataAccess.Interfaces;
using Sinotech.Mis.HR.Attendance.Utilities;
using System;
using System.Data;
using System.DirectoryServices.Protocols;
using System.IO;
using System.Net;
using UAParser.Objects;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic
{
    public class AccountBo : IAccountBo
    {

        private readonly IAccountDao _accountDao;
        private readonly IEmployeeBo _employeeBo;
        private readonly string _geoDbPath; // IP City 資料庫路徑
        private readonly ILogger<AccountBo> _logger;

        /// <summary>
        /// 建構元
        /// </summary>
        /// <param name="employeeBo">員工 Business Object</param>
        /// <param name="accountDao">帳號 DAO</param>
        /// <param name="configuration">設定</param>
        /// <param name="logger">日誌</param>
        public AccountBo(IEmployeeBo employeeBo, IAccountDao accountDao, 
            IConfiguration configuration, ILogger<AccountBo> logger)
        {
            _accountDao = accountDao;
            if (configuration == null || employeeBo == null || logger == null)
            {
                throw new ArgumentNullException(nameof(configuration));
            }
            var directory = configuration["GeoDbDirectory"];
            directory ??= @"D:\GeoIP";
            _geoDbPath = Path.Combine(directory, "GeoLite2-City.mmdb");
            _employeeBo = employeeBo;
            _logger = logger;
        }

        /// <summary>
        /// 設定 AccountLogInOutDto
        /// </summary>
        /// <param name="userAgent"></param>
        /// <param name="actionTime">動作時間</param>
        /// <param name="iPAddress"></param>
        /// <param name="empNo"></param>
        /// <param name="hostname"></param>
        /// <param name="unlocker"></param>
        /// <returns></returns>
        private AccountLogInOutDto SetAccountLogInOutDto(string userAgent, DateTime actionTime, IPAddress iPAddress, string empNo, string? hostname, string? unlocker)
        {
            AccountLogInOutDto accountLogInOutDto = new AccountLogInOutDto();
            ClientInfo? clientInfo = CardUtility.GetClientInfo(userAgent);
            if (clientInfo == null)
            {
                return accountLogInOutDto; // 不可能 null
            }
            // 取得作業系統名稱與版本
            string osName = clientInfo.OS.Family;
            string osVersion = $"{clientInfo.OS.Major}";
            if (!string.IsNullOrEmpty(clientInfo.OS.Minor))
            {
                osVersion += $".{clientInfo.OS.Minor}";
                if (!string.IsNullOrEmpty(clientInfo.OS.PatchMinor))
                {
                    osVersion += $".{clientInfo.OS.Patch}.{clientInfo.OS.PatchMinor}";
                }
            }
            string browser = clientInfo.Browser.Family;
            string browserVersion = clientInfo.Browser.Version;
            string device = clientInfo.Device.ToString();
            var location = CardUtility.GetLocation(iPAddress, _geoDbPath);
            string? city = location?.City.Name;
            string? country = location?.Country.Name;

            accountLogInOutDto.EmpNo = empNo;
            accountLogInOutDto.ActionTime = actionTime;
            accountLogInOutDto.IP = iPAddress.ToString();
            accountLogInOutDto.Hostname = hostname;
            accountLogInOutDto.Unlocker = unlocker;
            accountLogInOutDto.OperationSystem = osName;
            accountLogInOutDto.OperationSystemVersion = osVersion;
            accountLogInOutDto.Browser = browser;
            accountLogInOutDto.BrowserVersion = browserVersion;
            accountLogInOutDto.Device = device;
            accountLogInOutDto.Country = country;
            accountLogInOutDto.City = city;
            return accountLogInOutDto;
        }

        /// <summary>
        /// 驗證 員工編號 和密碼 是否有效，此處可改變驗證方式
        /// </summary>
        /// <param name="username">員工編號</param>
        /// <param name="password">密碼</param>
        /// <returns></returns>
        private static bool ValidateCredentials(string username, string password)
        {
            bool ret = false;
            string ldapServer = @"pdc.secinc";
            var ldapConnection = new LdapConnection(ldapServer);
            string domain = "secinc";
            try
            {
                // 本專案伺服器限定在 Windows 執行，方能認證使用者帳密
#pragma warning disable CA1416 // Validate platform compatibility
                ldapConnection.SessionOptions.SecureSocketLayer = false; // 用 true 都會錯誤
#pragma warning restore CA1416 // Validate platform compatibility
                ldapConnection.AuthType = AuthType.Negotiate; // Windows 加入網域才能用
                                                              //ldapConnection.AuthType = AuthType.Basic; //這才能跨平臺
                ldapConnection.Credential = new NetworkCredential(username, password, domain);
                ldapConnection.Bind();
                var cert = ldapConnection.ClientCertificates;
                ret = cert != null;
                ldapConnection.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
            }
            return ret;
        }

        /// <summary>
        /// 驗證用戶
        /// </summary>
        /// <param name="userId">員工編號</param>
        /// <param name="password">密碼</param>
        /// <returns></returns>
        public ApplicationUser? AuthenticateUser(string userId, string password)
        {
            ApplicationUser? ret = null;

            bool pass = ValidateCredentials(userId, password);

            if (pass)
            {
                Employee? employee = _employeeBo.GetEmployeeDetail(userId);
                // 日後員工資料表查不到或許得要 throw Exception 

                ApplicationUser user = new ApplicationUser()
                {
                    EmpNo = userId,
                    Email = employee?.Email ?? string.Empty,
                    CName = employee?.CName ?? string.Empty
                };
                ret = user;
            }
            return ret;
        }

        /// <summary>
        /// 登入失敗次數
        /// </summary>
        /// <param name="accountId"></param>
        /// <returns>15分鐘內 登入失敗次數</returns>
        public int GetLoginFailCount(string accountId)
        {
            int failCount = _accountDao.GetLoginFailCount(accountId);
            return failCount;
        }

        /// <summary>
        /// 增加登入記錄
        /// </summary>
        /// <param name="userAgent"></param>
        /// <param name="loginTime">登入時間</param>
        /// <param name="username"></param>
        /// <param name="action"></param>
        /// <param name="result"></param>
        /// <param name="ipAddress"></param>
        /// <param name="hostname"></param>
        public bool InsertLoginRecord(string userAgent, DateTime loginTime, string username,
             AccountLogInOutAction action, AccountLogInOutResult result, IPAddress ipAddress, string? hostname)
        {
            AccountLogInOutDto logInOutDto = SetAccountLogInOutDto(userAgent, loginTime, ipAddress, username, hostname, null);
            logInOutDto.Action = action;
            logInOutDto.Result = result;
            return _accountDao.InsertLoginRecord(logInOutDto);
        }

        /// <summary>
        /// 記錄登入失敗
        /// </summary>
        /// <param name="userAgent"></param>
        /// <param name="loginTime">登入時間</param>
        /// <param name="username"></param>
        /// <param name="ipAddress"></param>
        /// <param name="hostname"></param>
        public bool LogLoginFail(string userAgent, DateTime loginTime, string username, IPAddress ipAddress, string? hostname)
        {
            bool ret = true;
            AccountLogInOutDto accountLogInOutDto = SetAccountLogInOutDto(userAgent, loginTime, ipAddress, username, hostname, null);

            DataRow? dataRow = _accountDao.GetLoginFailRecord(username);
            DateTime firstLoginTime = loginTime;
            DateTime? firstLockTime = null;
            int failCount = 0;
            if (dataRow != null)
            {
                if (dataRow["FirstLockTime"] != DBNull.Value)
                {
                    firstLockTime = (DateTime)dataRow["FirstLockTime"];
                }
                firstLoginTime = (DateTime)dataRow["FirstLoginTime"];
                failCount = (int)dataRow["FailCount"];
                DateTime lastLoginTime = (DateTime)dataRow["LastLoginTime"];
                DateTime lockoutUntil = lastLoginTime.AddMinutes(AttendanceParameters.AccountLockoutTime);
                if (loginTime > lockoutUntil) // 若超過15分鐘，重新計算登入失敗時間與次數
                {
                    firstLockTime = null;
                    firstLoginTime = loginTime;
                    failCount = 0;
                }
            }
            failCount++;
            if (firstLockTime == null && failCount >= AttendanceParameters.AccountLockoutFailTimes) // 錯誤第三次時鎖定
            {
                firstLockTime = loginTime;
            }
            accountLogInOutDto.Action = AccountLogInOutAction.Login;
            accountLogInOutDto.Result = AccountLogInOutResult.Fail;
            _accountDao.LogAccountLoginFail(accountLogInOutDto, firstLoginTime, firstLockTime, failCount);
            _logger.LogError("User {EmpNo} logged in failed From {Hostname} {IP} at {Time}.",
                                    username, hostname, ipAddress.ToString(), loginTime.ToString("yyyy/MM/dd HH:mm:ss.fff"));
            return ret;
        }

        /// <summary>
        /// Log login success information
        /// </summary>
        /// <param name="userAgent"></param>
        /// <param name="loginTime">登入時間</param>
        /// <param name="iPAddress"></param>
        /// <param name="hostname"></param>
        /// <param name="empNo"></param>
        /// <returns></returns>
        public bool LogLoginSuccess(string userAgent, DateTime loginTime, IPAddress iPAddress, string? hostname, string empNo)
        {
            bool ret = true;
            _logger.LogInformation("User {EmpNo} logged in From {Hostname} {IP} at {Time}.",
                empNo, hostname, iPAddress.ToString(), loginTime.ToString("yyyy/MM/dd HH:mm:ss.fff"));

            AccountLogInOutDto accountLogInOutDto = SetAccountLogInOutDto(userAgent, loginTime, iPAddress, empNo, hostname, null);
            accountLogInOutDto.Action = AccountLogInOutAction.Login;
            accountLogInOutDto.Result = AccountLogInOutResult.Success;
            _accountDao.LogAccountLogin(accountLogInOutDto);
            return ret;
        }

        /// <summary>
        /// Log logout information
        /// </summary>
        /// <param name="userAgent"></param>
        /// <param name="logoutTime">登出時間</param>
        /// <param name="iPAddress"></param>
        /// <param name="hostname"></param>
        /// <param name="empNo"></param>
        public bool LogLogout(string userAgent, DateTime logoutTime, IPAddress iPAddress, string? hostname, string empNo)
        {
            bool ret = true;
            _logger.LogInformation("User {Name} logged out From {Hostname} {IP} at {Time}.",
                empNo, hostname, iPAddress.ToString(), logoutTime.ToString("yyyy/MM/dd HH:mm:ss.fff"));
            AccountLogInOutDto accountLogInOutDto = SetAccountLogInOutDto(userAgent, logoutTime, iPAddress, empNo, hostname, null);
            accountLogInOutDto.Action = AccountLogInOutAction.Logout;
            accountLogInOutDto.Result = AccountLogInOutResult.Success;
            _accountDao.LogAccountLogout(accountLogInOutDto);
            return ret;
        }

        /// <summary>
        /// 記錄解鎖
        /// </summary>
        /// <param name="userAgent"></param>
        /// <param name="unlockTime">解鎖時間</param>
        /// <param name="username"></param>
        /// <param name="unlocker"></param>
        /// <param name="ipAddress"></param>
        /// <param name="result"></param>
        /// <param name="hostname"></param>
        public bool LogUnlock(string userAgent, DateTime unlockTime, string username, string unlocker, IPAddress ipAddress, AccountLogInOutResult result, string? hostname)
        {
            bool ret = true;
            string time = unlockTime.ToString("yyyy/MM/dd HH:mm:ss.fff");
            if (AccountLogInOutResult.Success == result)
            {
                _logger.LogInformation("User {Unlocker} unlock {Username} From {Hostname} {IP} at {Time}.",
                    unlocker, username, hostname, ipAddress.ToString(), time);
            }
            else
            {
                // Log userId try to unlock
                _logger.LogError("User {Unlocker} is not admin who try to unlock {Username} From {Hostname} {IP} at {Time}.",
                    unlocker, username, hostname, ipAddress.ToString(), time);
            }
            AccountLogInOutDto accountLogInOutDto = SetAccountLogInOutDto(userAgent, unlockTime, ipAddress, username, hostname, unlocker);
            accountLogInOutDto.Action = AccountLogInOutAction.Unlock;
            accountLogInOutDto.Result = result;
            _accountDao.LogAccountUnlock(accountLogInOutDto);
            return ret;
        }
    }
}
