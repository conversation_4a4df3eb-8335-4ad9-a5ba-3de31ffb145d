﻿using Sinotech.Mis.HR.Attendance.Common;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves
{
    /// <summary>
    /// 防疫照顧假類別
    /// </summary>
    [LeaveKind(LeaveKindEnum.QuarantineCareLeave)]
    public class QuarantineCareLeave : C1CardLeaveNotProvided
    {

        /// <summary>
        /// 防疫照顧假建構子
        /// </summary>
        /// <param name="c1Card"></param>
        /// <param name="c1CardBo"></param>
        public QuarantineCareLeave(C1Card c1Card, IC1CardBo c1CardBo) : base(c1Card, c1CardBo)
        {
        }
    }
}
