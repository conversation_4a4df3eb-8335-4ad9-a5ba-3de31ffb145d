<template>
  <h6 class="my-2 fs-2 text-center">
    <i class="bi bi-envelope-exclamation me-1" />
    <span>待核卡</span>
  </h6>
  <div class="container px-0">
    <DataTable
      ref="a1CardRef"
      v-model:selection="a1CardSelection"
      v-model:expandedRowGroups="a1CardExpand"
      dataKey="formUID"
      rowGroupMode="subheader"
      groupRowsBy="roleID"
      class="border my-2"
      tableClass="border"
      :value="a1CardData"
      :loading="a1CardDataLoading"
      :rowClass="cardWarning"
      :lazy="true"
      :highlightOnSelect="true"
      :expandableRowGroups="true"
      :metaKeySelection="false"
      @row-select="rowSelect($event, a1CardSelection, a1CardData)"
      @row-unselect="rowUnselect($event, a1CardData)"
      @row-select-all="rowSelectAll(a1CardData)"
      @row-unselect-all="rowUnselectAll(a1CardData)"
    >
      <template #header>
        <div class="row">
          <div class="col-6 d-flex align-items-center">
            <div class="fs-5">
              <i class="bi bi-sun me-1" />
              <span>{{ FORM_ID.A1Card }}</span>
            </div>
          </div>
          <div class="col-6 text-end">
            <button
              type="button"
              :class="[
                'btn mx-2',
                (((!a1CardSelection) || (a1CardSelection.length === 0) || (submitted === true)) ? 'btn-outline-primary' : 'btn-primary')
              ]"
              :disabled="(!a1CardSelection) || (a1CardSelection.length === 0) || (submitted === true)"
              @click="onCardAgreeAll(a1CardSelection)"
            >
              <i class="bi bi-check-lg d-inline d-lg-none" />
              <span class="d-none d-lg-inline">同意</span>
            </button>
            <button
              type="button"
              :class="[
                'btn',
                (((!a1CardSelection) || (a1CardSelection.length === 0) || (submitted === true)) ? 'btn-outline-danger' : 'btn-danger')
              ]"
              :disabled="(!a1CardSelection) || (a1CardSelection.length === 0) || (submitted === true)"
              @click="onCardDisagreeAll(a1CardSelection)"
            >
              <i class="bi bi-x-lg d-inline d-lg-none" />
              <span class="d-none d-lg-inline">不同意</span>
            </button>
          </div>
        </div>
      </template>
      <template #empty>
        <div class="text-center">
          <span>無</span>
        </div>
      </template>
      <template #groupheader="slotProps">
        <div class="d-inline-block">
          <input
            :id="'a1Card' + slotProps.data.roleID"
            class="form-check-input mx-1 align-middle"
            type="checkbox"
            role="button"
            :style="{ width: '20px', height: '20px' }"
            :value="slotProps.data.roleSelectCheck"
            :checked="slotProps.data.roleSelectCheck"
            @input="roleSelectCheckInput($event, a1CardRef as InstanceType<typeof DataTable>, slotProps.data, a1CardSelection, a1CardData)"
          >
          <label
            class="form-check-label align-middle"
            role="button"
            :for="'a1Card' + slotProps.data.roleID"
          >
            <template v-if="slotProps.data.isAgency === true">
              <span class="badge rounded-pill bg-warning">代</span>
            </template>
            <span>{{ slotProps.data.roleName }}</span>
          </label>
        </div>
      </template>
      <Column selectionMode="multiple" />
      <Column field="roleID" />
      <Column
        field="formNo"
        header="卡號"
      >
        <template #body="{ data, field }">
          <router-link
            class="fw-bold"
            :to="{
              name: data['formID'] + 'Inbox',
              params: { formUID: data['formUID'] }
            }"
          >
            {{ data[field] }}
          </router-link>
          <i
            v-if="data['remindSigner'] === true"
            v-tooltip="{ value: data['remindMessage'], escape: true }"
            class="bi bi-exclamation-triangle text-danger fs-6 ms-1"
          />
        </template>
      </Column>
      <Column
        field="empName"
        header="申請人"
      />
      <Column
        field="formInfo"
        header="工作年月"
      >
        <template #body="{ data, field }">
          {{ data[field].substring(0, data[field].indexOf('月') + 1) }}
        </template>
      </Column>
      <Column
        field="card"
        header="旬別"
      >
        <template #body="{ data, field }">
          {{ data[field].ApplicationType }}
        </template>
      </Column>
      <Column
        field="hour"
        header="工作時數"
      />
      <Column
        field="receiveDate"
        header="填報時間"
      >
        <template #body="{ data, field }">
          {{ dateToRocString(data[field]) }}
        </template>
      </Column>
    </DataTable>

    <DataTable
      ref="b1CardAppRef"
      v-model:selection="b1CardAppSelection"
      v-model:expandedRowGroups="b1CardAppExpand"
      dataKey="formUID"
      rowGroupMode="subheader"
      groupRowsBy="roleID"
      class="border my-2"
      tableClass="border"
      :value="b1CardAppData"
      :loading="b1CardAppDataLoading"
      :rowClass="cardWarning"
      :lazy="true"
      :highlightOnSelect="true"
      :expandableRowGroups="true"
      :metaKeySelection="false"
      @row-select="rowSelect($event, b1CardAppSelection, b1CardAppData)"
      @row-unselect="rowUnselect($event, b1CardAppData)"
      @row-select-all="rowSelectAll(b1CardAppData)"
      @row-unselect-all="rowUnselectAll(b1CardAppData)"
    >
      <template #header>
        <div class="row">
          <div class="col-6 d-flex align-items-center">
            <div class="fs-5">
              <i class="bi bi-cloud-moon me-1" />
              <span>{{ FORM_ID.B1CardApp }}</span>
            </div>
          </div>
          <div class="col-6 text-end">
            <button
              type="button"
              :class="[
                'btn mx-2',
                (((!b1CardAppSelection) || (b1CardAppSelection.length === 0) || (submitted === true)) ? 'btn-outline-primary' : 'btn-primary')
              ]"
              :disabled="(!b1CardAppSelection) || (b1CardAppSelection.length === 0) || (submitted === true)"
              @click="onCardAgreeAll(b1CardAppSelection)"
            >
              <i class="bi bi-check-lg d-inline d-lg-none" />
              <span class="d-none d-lg-inline">同意</span>
            </button>
            <button
              type="button"
              :class="[
                'btn',
                (((!b1CardAppSelection) || (b1CardAppSelection.length === 0) || (submitted === true)) ? 'btn-outline-danger' : 'btn-danger')
              ]"
              :disabled="(!b1CardAppSelection) || (b1CardAppSelection.length === 0) || (submitted === true)"
              @click="onCardDisagreeAll(b1CardAppSelection)"
            >
              <i class="bi bi-x-lg d-inline d-lg-none" />
              <span class="d-none d-lg-inline">不同意</span>
            </button>
          </div>
        </div>
      </template>
      <template #empty>
        <div class="text-center">
          <span>無</span>
        </div>
      </template>
      <template #groupheader="slotProps">
        <div class="d-inline-block">
          <input
            :id="'b1CardApp' + slotProps.data.roleID"
            class="form-check-input mx-1 align-middle"
            type="checkbox"
            role="button"
            :style="{ width: '20px', height: '20px' }"
            :value="slotProps.data.roleSelectCheck"
            :checked="slotProps.data.roleSelectCheck"
            @input="roleSelectCheckInput($event, b1CardAppRef as InstanceType<typeof DataTable>, slotProps.data, b1CardAppSelection, b1CardAppData)"
          >
          <label
            class="form-check-label align-middle"
            role="button"
            :for="'b1CardApp' + slotProps.data.roleID"
          >
            <template v-if="slotProps.data.isAgency === true">
              <span class="badge rounded-pill bg-warning">代</span>
            </template>
            <span>{{ slotProps.data.roleName }}</span>
          </label>
        </div>
      </template>
      <Column selectionMode="multiple" />
      <Column field="roleID" />
      <Column
        field="formNo"
        header="卡號"
      >
        <template #body="{ data, field }">
          <router-link
            class="fw-bold"
            :to="{
              name: data['formID'] + 'Inbox',
              params: { formUID: data['formUID'] }
            }"
          >
            {{ data[field] }}
          </router-link>
          <i
            v-if="data['remindSigner'] === true"
            v-tooltip="{ value: data['remindMessage'], escape: true }"
            class="bi bi-exclamation-triangle text-danger fs-6 ms-1"
          />
        </template>
      </Column>
      <Column
        field="empName"
        header="申請人"
      />
      <Column
        field="card"
        header="加班別"
      >
        <template #body="{ data, field }">
          {{ data[field].ApplicationType }}
        </template>
      </Column>
      <Column
        field="formInfo"
        header="預定日期"
      />
      <Column
        field="card"
        header="預定時數"
      >
        <template #body="{ data, field }">
          {{ data[field].B1_Hour }}
        </template>
      </Column>
      <Column
        field="receiveDate"
        header="填報時間"
      >
        <template #body="{ data, field }">
          {{ dateToRocString(data[field]) }}
        </template>
      </Column>
    </DataTable>

    <DataTable
      ref="b1CardRef"
      v-model:selection="b1CardSelection"
      v-model:expandedRowGroups="b1CardExpand"
      dataKey="formUID"
      rowGroupMode="subheader"
      groupRowsBy="roleID"
      class="border my-2"
      tableClass="border"
      :value="b1CardData"
      :loading="b1CardDataLoading"
      :rowClass="cardWarning"
      :lazy="true"
      :highlightOnSelect="true"
      :expandableRowGroups="true"
      :metaKeySelection="false"
      @row-select="rowSelect($event, b1CardSelection, b1CardData)"
      @row-unselect="rowUnselect($event, b1CardData)"
      @row-select-all="rowSelectAll(b1CardData)"
      @row-unselect-all="rowUnselectAll(b1CardData)"
    >
      <template #header>
        <div class="row">
          <div class="col-6 d-flex align-items-center">
            <div class="fs-5">
              <i class="bi bi-moon me-1" />
              <span>{{ FORM_ID.B1Card }}</span>
            </div>
          </div>
          <div class="col-6 text-end">
            <button
              type="button"
              :class="[
                'btn mx-2',
                (((!b1CardSelection) || (b1CardSelection.length === 0) || (submitted === true)) ? 'btn-outline-primary' : 'btn-primary')
              ]"
              :disabled="(!b1CardSelection) || (b1CardSelection.length === 0) || (submitted === true)"
              @click="onCardAgreeAll(b1CardSelection)"
            >
              <i class="bi bi-check-lg d-inline d-lg-none" />
              <span class="d-none d-lg-inline">同意</span>
            </button>
            <button
              type="button"
              :class="[
                'btn',
                (((!b1CardSelection) || (b1CardSelection.length === 0) || (submitted === true)) ? 'btn-outline-danger' : 'btn-danger')
              ]"
              :disabled="(!b1CardSelection) || (b1CardSelection.length === 0) || (submitted === true)"
              @click="onCardDisagreeAll(b1CardSelection)"
            >
              <i class="bi bi-x-lg d-inline d-lg-none" />
              <span class="d-none d-lg-inline">不同意</span>
            </button>
          </div>    
        </div>
      </template>
      <template #empty>
        <div class="text-center">
          <span>無</span>
        </div>
      </template>
      <template #groupheader="slotProps">
        <div class="d-inline-block">
          <input
            :id="'b1Card' + slotProps.data.roleID"
            class="form-check-input mx-1 align-middle"
            type="checkbox"
            role="button"
            :style="{ width: '20px', height: '20px' }"
            :value="slotProps.data.roleSelectCheck"
            :checked="slotProps.data.roleSelectCheck"
            @input="roleSelectCheckInput($event, b1CardRef as InstanceType<typeof DataTable>, slotProps.data, b1CardSelection, b1CardData)"
          >
          <label
            class="form-check-label align-middle"
            role="button"
            :for="'b1Card' + slotProps.data.roleID"
          >
            <template v-if="slotProps.data.isAgency === true">
              <span class="badge rounded-pill bg-warning">代</span>
            </template>
            <span>{{ slotProps.data.roleName }}</span>
          </label>
        </div>
      </template>
      <Column selectionMode="multiple" />
      <Column field="roleID" />
      <Column
        field="formNo"
        header="卡號"
      >
        <template #body="{ data, field }">
          <router-link
            class="fw-bold"
            :to="{
              name: data['formID'] + 'Inbox',
              params: { formUID: data['formUID'] }
            }"
          >
            {{ data[field] }}
          </router-link>
          <i
            v-if="data['remindSigner'] === true"
            v-tooltip="{ value: data['remindMessage'], escape: true }"
            class="bi bi-exclamation-triangle text-danger fs-6 ms-1"
          />
        </template>
      </Column>
      <Column
        field="empName"
        header="申請人"
      />
      <Column
        field="card"
        header="加班別"
      >
        <template #body="{ data, field }">
          {{ data[field].ApplicationType }}
        </template>
      </Column>
      <Column
        field="formInfo"
        header="加班日期"
      />
      <Column
        field="hour"
        header="加班時數"
      />
      <Column
        field="receiveDate"
        header="填報時間"
      >
        <template #body="{ data, field }">
          {{ dateToRocString(data[field]) }}
        </template>
      </Column>
    </DataTable>

    <DataTable
      ref="c1CardRef"
      v-model:selection="c1CardSelection"
      v-model:expandedRowGroups="c1CardExpand"
      dataKey="formUID"
      rowGroupMode="subheader"
      groupRowsBy="roleID"
      class="border my-2"
      tableClass="border"
      :value="c1CardData"
      :loading="c1CardDataLoading"
      :rowClass="cardWarning"
      :lazy="true"
      :highlightOnSelect="true"
      :expandableRowGroups="true"
      :metaKeySelection="false"
      @row-select="rowSelect($event, c1CardSelection, c1CardData)"
      @row-unselect="rowUnselect($event, c1CardData)"
      @row-select-all="rowSelectAll(c1CardData)"
      @row-unselect-all="rowUnselectAll(c1CardData)"
    >
      <template #header>
        <div class="row">
          <div class="col-6 d-flex align-items-center">
            <div class="fs-5">
              <i class="bi bi-cloud-sun me-1" />
              <span>{{ FORM_ID.C1Card }}</span>
            </div>
          </div>
          <div class="col-6 text-end">
            <button
              type="button"
              :class="[
                'btn mx-2',
                (((!c1CardSelection) || (c1CardSelection.length === 0) || (submitted === true)) ? 'btn-outline-primary' : 'btn-primary')
              ]"
              :disabled="(!c1CardSelection) || (c1CardSelection.length === 0) || (submitted === true)"
              @click="onCardAgreeAll(c1CardSelection)"
            >
              <i class="bi bi-check-lg d-inline d-lg-none" />
              <span class="d-none d-lg-inline">同意</span>
            </button>
            <button
              type="button"
              :class="[
                'btn',
                (((!c1CardSelection) || (c1CardSelection.length === 0) || (submitted === true)) ? 'btn-outline-danger' : 'btn-danger')
              ]"
              :disabled="(!c1CardSelection) || (c1CardSelection.length === 0) || (submitted === true)"
              @click="onCardDisagreeAll(c1CardSelection)"
            >
              <i class="bi bi-x-lg d-inline d-lg-none" />
              <span class="d-none d-lg-inline">不同意</span>
            </button>
          </div>
        </div>
      </template>
      <template #empty>
        <div class="text-center">
          <span>無</span>
        </div>
      </template>
      <template #groupheader="slotProps">
        <div class="d-inline-block">
          <input
            :id="'c1Card' + slotProps.data.roleID"
            class="form-check-input mx-1 align-middle"
            type="checkbox"
            role="button"
            :style="{ width: '20px', height: '20px' }"
            :value="slotProps.data.roleSelectCheck"
            :checked="slotProps.data.roleSelectCheck"
            @input="roleSelectCheckInput($event, c1CardRef as InstanceType<typeof DataTable>, slotProps.data, c1CardSelection, c1CardData)"
          >
          <label
            class="form-check-label align-middle"
            role="button"
            :for="'c1Card' + slotProps.data.roleID"
          >
            <template v-if="slotProps.data.isAgency === true">
              <span class="badge rounded-pill bg-warning">代</span>
            </template>
            <span>{{ slotProps.data.roleName }}</span>
          </label>
        </div>
      </template>
      <Column selectionMode="multiple" />
      <Column field="roleID" />
      <Column
        field="formNo"
        header="卡號"
      >
        <template #body="{ data, field }">
          <router-link
            class="fw-bold"
            :to="{
              name: data['formID'] + 'Inbox',
              params: { formUID: data['formUID'] }
            }"
          >
            <span>{{ data[field] }}</span>
            <i
              v-if="data['attached']"
              class="bi bi-paperclip ms-1"
            />
          </router-link>
          <i
            v-if="data['remindSigner'] === true"
            v-tooltip="{ value: data['remindMessage'], escape: true }"
            class="bi bi-exclamation-triangle text-danger fs-6 ms-1"
          />
        </template>
      </Column>
      <Column
        field="empName"
        header="申請人"
      />
      <Column
        field="card"
        header="請假別"
      >
        <template #body="{ data, field }">
          {{ data[field].LeaveName }}
        </template>
      </Column>
      <Column
        field="formInfo"
        header="請假時間"
      />
      <Column
        field="card"
        header="請假時數"
      >
        <template #body="{ data, field }">
          {{ data[field].DayHours }}
        </template>
      </Column>
      <Column
        field="receiveDate"
        header="填報時間"
      >
        <template #body="{ data, field }">
          {{ dateToRocString(data[field]) }}
        </template>
      </Column>
    </DataTable>

    <template v-if="queryDataTime !== null">
      <div class="text-black-50 text-end">
        <span>
          查詢時間：
        </span>
        <span class="fst-italic">
          {{ dateToRocString(queryDataTime) }}
        </span>
      </div>
    </template>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'
import { dateToRocString } from '../api/appFunction'
import { POST_MULTIPLE_APPROVEINBOX_URL } from '../api/appUrl'
import { SUCCESS_DISPLAY_TIME, WARN_DISPLAY_TIME, SYSTEM_ERROR_MESSAGE, FORM_ID, FLOW_STATUS_CODE } from '../api/appConst'
import { useAuthUserStore } from '../store/index'
import { useUiStore } from '../store/ui'
import { useInboxData } from '../composable/inboxData'
import { useAbortController } from '../composable/abortController'
import { useNavMenuBadgeStore } from '../store/navMenuBadge'
import { onBeforeRouteLeave } from 'vue-router'
import router from '../router'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import type { SignInboxType, InboxCardApiType } from '../api/appType'

const userStore = useAuthUserStore()
const uiStore = useUiStore()
const { inboxData, onGetInboxData } = useInboxData()
const navMenuBadgeStore = useNavMenuBadgeStore()
const { abortController, fetchErrorHandler, abortListener } = useAbortController()

const queryDataTime = ref<Date | null>(null)

const a1CardRef = ref<InstanceType<typeof DataTable>>()
const b1CardAppRef = ref<InstanceType<typeof DataTable>>()
const b1CardRef = ref<InstanceType<typeof DataTable>>()
const c1CardRef = ref<InstanceType<typeof DataTable>>()

const a1CardData = ref<Array<SignInboxType>>([])
const b1CardAppData = ref<Array<SignInboxType>>([])
const b1CardData = ref<Array<SignInboxType>>([])
const c1CardData = ref<Array<SignInboxType>>([])

const a1CardSelection = ref<Array<SignInboxType> | null>(null)
const b1CardAppSelection = ref<Array<SignInboxType> | null>(null)
const b1CardSelection = ref<Array<SignInboxType> | null>(null)
const c1CardSelection = ref<Array<SignInboxType> | null>(null)

const a1CardExpand = ref<Array<string>>([])
const b1CardAppExpand = ref<Array<string>>([])
const b1CardExpand = ref<Array<string>>([])
const c1CardExpand = ref<Array<string>>([])

const a1CardDataLoading = ref<boolean>(true)
const b1CardAppDataLoading = ref<boolean>(true)
const b1CardDataLoading = ref<boolean>(true)
const c1CardDataLoading = ref<boolean>(true)

const submitted = ref<boolean>(false)

const confirm = useConfirm()
const toast = useToast()

const onCardAgreeAll = (data: Array<SignInboxType> | null): void => {
  let checkSubmitted: number = 0
  submitted.value = true
  confirm.require({
    group: 'app',
    header: '提醒',
    message: '確認同意？',
    acceptProps: {
      severity: 'primary'
    },
    rejectProps: {
      severity: 'secondary'
    },
    accept: (): void => {
      checkSubmitted++
      if (checkSubmitted === 1) {
        fetch(POST_MULTIPLE_APPROVEINBOX_URL, {
          method: 'POST',
          headers: {
            'content-type': 'application/json'
          },
          body: JSON.stringify((data !== null) ? data.map((e: SignInboxType) => {
            return {
              formID: e.formID,
              flowUID: e.flowUID,
              formUID: e.formUID,
              approverEmpNo: userStore.userId,
              approverName: userStore.userName,
              approverDeptNo: userStore.deptNo,
              approverDeptSName: userStore.deptSName,
              approveTime: new Date(),
              isAgentApprove: false,
              flowStatus: FLOW_STATUS_CODE.Agree,
              isNotification: false
            }
          }) : []),
          signal: abortController.signal
        }).then((res: Response): Promise<any> => {
          if (!res.ok) {
            throw new Error(res.status.toString())
          }
          return res.json()
        }).then(async (res: any): Promise<void> => {
          if (res.length === 0) {
            await loadCard()
            navMenuBadgeStore.setBadge(userStore.userId, abortController.signal)

            toast.add({
              severity: 'success',
              summary: '已簽核',
              life: SUCCESS_DISPLAY_TIME,
              group: 'app'
            })
          } else {
            toast.add({
              severity: 'error',
              summary: res,
              group: 'app'
            })
            uiStore.toggle(true)
            setTimeout(() => {
              router.go(0)
            }, WARN_DISPLAY_TIME)
          }
        }).catch((err: Error): void => {
          console.error(err)
          fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
        }).finally((): void => {
          submitted.value = false
        })
      }
    },
    reject: (): void => {
      submitted.value = false
    }
  })
}

const onCardDisagreeAll = (data: Array<SignInboxType> | null): void => {
  let checkSubmitted: number = 0
  submitted.value = true
  confirm.require({
    group: 'app',
    header: '提醒',
    message: '確認不同意？',
    acceptProps: {
      severity: 'primary'
    },
    rejectProps: {
      severity: 'secondary'
    },
    accept: (): void => {
      checkSubmitted++
      if (checkSubmitted === 1) {
        fetch(POST_MULTIPLE_APPROVEINBOX_URL, {
          method: 'POST',
          headers: {
            'content-type': 'application/json'
          },
          body: JSON.stringify((data !== null) ? data.map((e: SignInboxType) => {
            return {
              formID: e.formID,
              flowUID: e.flowUID,
              formUID: e.formUID,
              approverEmpNo: userStore.userId,
              approverName: userStore.userName,
              approverDeptNo: userStore.deptNo,
              approverDeptSName: userStore.deptSName,
              approveTime: new Date(),
              isAgentApprove: false,
              flowStatus: FLOW_STATUS_CODE.Deny,
              isNotification: false
            }
          }) : []),
          signal: abortController.signal
        }).then((res: Response): Promise<any> => {
          if (!res.ok) {
            throw new Error(res.status.toString())
          }
          return res.json()
        }).then(async (res: any): Promise<void> => {
          if (res.length === 0) {
            await loadCard()
            navMenuBadgeStore.setBadge(userStore.userId, abortController.signal)

            toast.add({
              severity: 'success',
              summary: '已簽核',
              life: SUCCESS_DISPLAY_TIME,
              group: 'app'
            })
          } else {
            toast.add({
              severity: 'error',
              summary: res,
              group: 'app'
            })
            uiStore.toggle(true)
            setTimeout(() => {
              router.go(0)
            }, WARN_DISPLAY_TIME)
          }
        }).catch((err: Error): void => {
          console.error(err)
          fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
        }).finally((): void => {
          submitted.value = false
        })
      }
    },
    reject: (): void => {
      submitted.value = false
    }
  })
}

const inboxesDataFilter = (inboxesData: Array<InboxCardApiType>, IsAgency: boolean, cardFormID: string): Array<SignInboxType> => {
  return inboxesData.filter((e: InboxCardApiType) =>
    e.FormID === cardFormID
  ).sort((val1: InboxCardApiType, val2: InboxCardApiType) =>
    new Date(val2.StartTime).getTime() - new Date(val1.StartTime).getTime()
  ).map((e: InboxCardApiType) => {
    let remindMessageHtml = e.RemindMessage
    if (remindMessageHtml) {
      const remindMessage = remindMessageHtml.split('\n')
      if (remindMessage.length > 1) {
        remindMessageHtml = ''
        remindMessage.forEach((word: string, index: number) => {
          remindMessageHtml += (index + 1) + '. ' + word + '\n'
        })
      }
    }
    return {
      roleID: e.RecipientEmpNo,
      roleName: e.RecipientName,
      roleSelectCheck: false, // 角色之下的表單全選、全不選
      isAgency: IsAgency,
      formUID: e.FormUID,
      formID: e.FormID,
      formNo: e.FormNo,
      empName: e.EmpName,
      deptSName: e.DeptSName,
      formInfo: e.FormInfo,
      card: e.Card,
      addedSigner: e.AddedSigner,
      receiveDate: new Date(e.StartTime),
      hour: e.Hours,
      flowUID: e.FlowUID,
      remindMessage: remindMessageHtml,
      remindSigner: e.RemindSigner,
      attached: e.Attachments
    }
  })
}

const setCardInbox = (dataSet: Array<{ Inboxes: Array<InboxCardApiType>, IsAgency: boolean, Role: string }>): void => {
  dataSet.forEach((boxes: { Inboxes: Array<InboxCardApiType>, IsAgency: boolean, Role: string }) => {
    a1CardData.value = a1CardData.value.concat(
      inboxesDataFilter(boxes.Inboxes, boxes.IsAgency, 'A1Card')
    )
    a1CardExpand.value.push(boxes.Role)

    b1CardAppData.value = b1CardAppData.value.concat(
      inboxesDataFilter(boxes.Inboxes, boxes.IsAgency, 'B1CardApp')
    )
    b1CardAppExpand.value.push(boxes.Role)

    b1CardData.value = b1CardData.value.concat(
      inboxesDataFilter(boxes.Inboxes, boxes.IsAgency, 'B1Card')
    )
    b1CardExpand.value.push(boxes.Role)

    c1CardData.value = c1CardData.value.concat(
      inboxesDataFilter(boxes.Inboxes, boxes.IsAgency, 'C1Card')
    )
    c1CardExpand.value.push(boxes.Role)
  })
}

const loadCard = async (): Promise<void> => {
  queryDataTime.value = new Date()

  a1CardData.value = []
  b1CardAppData.value = []
  b1CardData.value = []
  c1CardData.value = []

  a1CardDataLoading.value = true
  b1CardAppDataLoading.value = true
  b1CardDataLoading.value = true
  c1CardDataLoading.value = true

  try {
    await onGetInboxData(userStore.userId, abortController.signal)
  } catch (err: unknown) {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  }

  setCardInbox(inboxData.value)

  a1CardSelection.value = []
  b1CardAppSelection.value = []
  b1CardSelection.value = []
  c1CardSelection.value = []

  a1CardDataLoading.value = false
  b1CardAppDataLoading.value = false
  b1CardDataLoading.value = false
  c1CardDataLoading.value = false
}

const cardWarning = (data: SignInboxType): string => {
  if (data.remindSigner === true) {
    return 'cardWarning'
  }
  return ''
}

const rowSelect = (event: { data: SignInboxType}, cardSelectData: Array<SignInboxType> | null, cardData: Array<SignInboxType>): void => {
  if ((cardSelectData !== null) && cardSelectData.filter((e: SignInboxType) => e.roleID === event.data.roleID).length === cardData.filter((e: SignInboxType) => e.roleID === event.data.roleID).length) {
    cardData.filter((e: SignInboxType) => e.roleID === event.data.roleID).forEach((element: SignInboxType) => {
      element.roleSelectCheck = true
    })
  }
}

const rowUnselect = (event: { data: SignInboxType}, cardData: Array<SignInboxType>): void => {
  cardData.filter((e: SignInboxType) => e.roleID === event.data.roleID).forEach((element: SignInboxType) => {
    element.roleSelectCheck = false
  })
}

const rowSelectAll = (cardData: Array<SignInboxType>): void => {
  cardData.forEach((element: SignInboxType) => {
    element.roleSelectCheck = true
  })
}

const rowUnselectAll = (cardData: Array<SignInboxType>): void => {
  cardData.forEach((element: SignInboxType) => {
    element.roleSelectCheck = false
  })
}

/**
 * 勾選DataTable的group選項
 * @param event 
 * @param dataRef 
 * @param groupData DataTable的group是該group第一row的資料
 * @param cardSelectData 
 * @param cardData 
 */
const roleSelectCheckInput = (event: Event, dataRef: InstanceType<typeof DataTable>, groupData: SignInboxType, cardSelectData: Array<SignInboxType> | null, cardData: Array<SignInboxType>): void => {
  if ((event.target as HTMLInputElement).checked === true) {
    // 篩選留下指定角色，和其他目前有被選的資料
    const filterData = cardData.filter((e: SignInboxType) => {
      if (e.roleID === groupData.roleID) {
        return true
      } else if (cardSelectData !== null) {
        return (cardSelectData.find((cardSelectEach: SignInboxType) => cardSelectEach.formUID === e.formUID) !== undefined)
      }
    })
    dataRef.$emit('update:selection', filterData) // 讓DataTable的row選項打勾

    // 篩選留下指定角色的資料
    cardData.filter((e: SignInboxType) => e.roleID === groupData.roleID).forEach((element: SignInboxType) => {
      element.roleSelectCheck = true
    })
  } else {
    // 篩選留下目前有被選，但不是指定角色的資料
    const filterData = cardData.filter((e: SignInboxType) => {
      if ((e.roleID !== groupData.roleID) && cardSelectData !== null) {
        return (cardSelectData.find((cardSelectEach: SignInboxType) => cardSelectEach.formUID === e.formUID) !== undefined)
      }
    })
    dataRef.$emit('update:selection', filterData) // 讓DataTable的row選項打勾

    // 篩選留下指定角色的資料
    cardData.filter((e: SignInboxType) => e.roleID === groupData.roleID).forEach((element: SignInboxType) => {
      element.roleSelectCheck = false
    })
  }
}

onBeforeRouteLeave(() => {
  toast.removeGroup('app')
})

onMounted(async () => {
  abortListener()
  await loadCard()
})
</script>