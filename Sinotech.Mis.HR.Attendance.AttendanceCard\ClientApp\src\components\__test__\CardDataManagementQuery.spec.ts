import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import CardDataManagementQuery from '../CardDataManagementQuery.vue'

describe('CardDataManagementQuery', () => {
  it('check context', () => {
    const wrapper = mount(CardDataManagementQuery, {
      props: {
        modelValue: {
          formStatus: 1,
          ...CardDataManagementQuery.props.modelValue.default
        }
      },
      global: {
        plugins: [
          createTestingPinia()
        ]
      }
    })
    expect(wrapper.html()).toContain('抽單')
  })
  it('check slots', () => {
    const wrapper = mount(CardDataManagementQuery, {
      slots: {
        header: '<span>三卡管理 / </span>'
      }
    })
    expect(wrapper.html()).toContain('三卡管理 / ')
  })
})