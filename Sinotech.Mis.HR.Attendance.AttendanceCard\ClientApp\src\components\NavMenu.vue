<template>
  <Menubar
    :model="menuItems"
    :class="[prodModeClass, 'py-2']"
  >
    <template #start>
      <router-link
        v-slot="{ navigate }"
        :to="{ name: 'Home' }"
        custom
      >
        <a
          class="router-link-active router-link-exact-active navbar-brand me-0"
          role="button"
          @click="onClickNavigate($event, navigate)"
        >
          <img
            :src="attendanceLogo"
            :style="{ width: '3rem', height: '3rem' }"
            alt="三卡"
            v-tooltip="'三卡首頁'"
          >
        </a>
      </router-link>
    </template>
    <template #end>
      <div class="row">
        <div
          v-if="mode.split('.')[1] === 'intranet'"
          class="col-auto px-0 d-none d-sm-block"
        >
          <a
            class="router-link-active router-link-exact-active navbar-brand text-black"
            role="button"
            @click="onClickNavigateIntranetHome"
          >
            <img
              :src="sinotechLogo"
              :style="{ width: '3rem', height: '3rem' }"
              alt="中興社"
            >
          </a>
        </div>
        <div class="col d-flex align-items-center px-2">
          <template v-if="((logonIsAdmin === true) && (mode.split('.')[1] === 'intranet')) || mode.split('.')[1] === 'internet'">
            <a
              :class="[
                'dropdown-toggle text-decoration-none',
                ((logonIsAdmin === true) && (mode.split('.')[1] === 'intranet')) ? 'text-primary' : 'text-reset'
              ]"
              href="#"
              role="button"
              data-bs-toggle="dropdown"
              aria-expanded="false"
            >
              <i class="bi bi-person-circle" />
              <span>{{ employee.deptSName + ' ' + employee.userName }}</span>
            </a>
            <ul
              v-if="(logonIsAdmin === true) && (mode.split('.')[1] === 'intranet')"
              class="dropdown-menu"
            >
              <li>
                <a
                  class="dropdown-item"
                  style="cursor: pointer;"
                  @click="onClickShowDialog"
                >
                  <span>切換身分</span>
                </a>
              </li>
            </ul>
            <ul
              v-else-if="mode.split('.')[1] === 'internet'"
              class="dropdown-menu"
            >
              <li>
                <a
                  class="dropdown-item"
                  style="cursor: pointer;"
                  @click="onLogout"
                >
                  <span>登出</span>
                </a>
              </li>
            </ul>
          </template>
          <template v-else>
            <div style="cursor: default;">
              <i class="bi bi-person-circle" />
              <span>{{ employee.deptSName + ' ' + employee.userName }}</span>
            </div>
          </template>
        </div>
      </div>
    </template>
    <template #itemicon="menuItem">
      <span
        :class="[menuItem.class, menuItem.item.icon, 'position-relative']"
      >
        <span
          v-if="menuItem.item.badge > 0"
          class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger opacity-75"
        >
          {{ menuItem.item.badge }}
        </span>
      </span>
    </template>
  </Menubar>
  <Dialog
    v-if="(logonIsAdmin === true) && (mode.split('.')[1] === 'intranet')"
    v-model:visible="showDialog"
    position="topright"
    :contentStyle="{ overflow: 'visible' }"
    :style="{ width: '25vw' }"
    :breakpoints="{
      [bootstrapVariables.gridBreakpointsLg]: '35vw',
      [bootstrapVariables.gridBreakpointsSm]: '50vw'
    }"
    :closable="true"
  >
    <template #header>
      <span class="text-success">切換身份</span>
    </template>
    <template #closeicon>
      <i class="bi bi-x-lg" />
    </template>
    <ChooseUser
      :modelValue="{
        userId: employee.userId,
        userName: employee.userName,
        deptNo: employee.deptNo,
        deptSName: employee.deptSName
      }"
      :employeeData="employeeData"
      :filter="onSearchEmployeeData"
      :clearable="false"
      @update:modelValue="onChangeUser"
    />
  </Dialog>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useAuthUserStore } from '../store/index'
import { useUiStore } from '../store/ui'
import { useNavMenuBadgeStore } from '../store/navMenuBadge'
import { useEmployeeData } from '../composable/employeeData'
import { useAbortController } from '../composable/abortController'
import ChooseUser from '../components/ChooseUser.vue'
import Dialog from 'primevue/dialog'
import Menubar from 'primevue/menubar'
import { IISINC_HOME_URL, GET_ISADMIN_URL, POST_ISADMIN_URL, GET_ISOVERTIMEALLOWED_URL, GET_ISAUTHORIZEDTOQUERYDEPARTMENTSENTBOX_URL } from '../api/appUrl'
import { FORM_ID } from '../api/appConst'
import { onSearchEmployeeData } from '../api/appFunction'
import type { EmployeeStoreBaseType } from '../api/appType'
import { useRoute, useRouter } from 'vue-router'
import { logoutHandler } from '../router'
import attendanceLogo from '../assets/Attendance.png'
import sinotechLogo from '../assets/Logo64.png'
import bootstrapVariables from '../assets/scss/_bootstrap_variables.module.scss'
import type { NavigationFailure } from 'vue-router'
import type { MenuItem, MenuItemCommandEvent } from 'primevue/menuitem'
const mode = import.meta.env.MODE
const prodModeClass = import.meta.env.VITE_PROD_MODE_CLASS

const userStore = useAuthUserStore()
const uiStore = useUiStore()
const navMenuBadgeStore = useNavMenuBadgeStore()
const { employeeData, onSetEmployeeData } = useEmployeeData()
const { abortController } = useAbortController()
const route = useRoute()
const router = useRouter()

const { badgeInbox, badgeNotify } = storeToRefs(navMenuBadgeStore)

const employee = ref<EmployeeStoreBaseType>({
  userId: userStore.userId,
  userName: userStore.userName,
  deptNo: userStore.deptNo,
  deptSName: userStore.deptSName
})
const logonIsAdmin = ref<boolean>(false)
const isAdmin = ref<boolean>(false)
const workOvertimeVisible = ref<boolean>(false)
const deptSentVisible = ref<boolean>(false)
const menuItems = ref<Array<MenuItem>>([])
const showDialog = ref<boolean>(false)

/**
 * 管理者切換身分
 * @param user 員工資料
 */
 const onChangeUser = async (user: EmployeeStoreBaseType): Promise<void> => {
  uiStore.toggle(true)
  await userStore.setEmp(user.userId).then(() => {
    uiStore.toggle(false)
    router.go(0) // 重新整理頁面，以讀取換身分後的員工資料
  }).catch((err: Error): void => {
    console.error(err)
  })
}

const onClickNavigate = (event: MouseEvent, navigateFunc: (e?: MouseEvent) => Promise<void | NavigationFailure>): void => {
  if (event.ctrlKey === true || event.shiftKey === true) {
    window.open('Home', '_blank')
  } else if (route.name === 'Home') {
    router.go(0)
  } else {
    navigateFunc(event)
  }
}

const onClickNavigateIntranetHome = (event: MouseEvent): void => {
  if (event.ctrlKey === true || event.shiftKey === true) {
    window.open(IISINC_HOME_URL, '_blank')
  } else {
    window.location.assign(IISINC_HOME_URL)
  }
}

const onClickShowDialog = (): void => {
  showDialog.value = !showDialog.value
}

const onClickMenuItem = (event: MenuItemCommandEvent, routerName: string): void => {
  if ((event.originalEvent as MouseEvent).ctrlKey === true || (event.originalEvent as MouseEvent).shiftKey === true) {
    window.open(event.item.target, '_blank')
  } else if (route.name === routerName) {
    router.go(0)
  } else {
    router.push({ name: routerName })
  }
}

const onLogout = async (): Promise<void> => {
  const loginRoute = await logoutHandler()
  router.push(loginRoute)
}

onMounted(async (): Promise<void> => {
  const empNo = userStore.userId
  const PromiseData: any = await Promise.all([
    fetch(GET_ISADMIN_URL, {
      method: 'GET'
    }).then((res: Response) => {
      if (!res.ok) {
        throw new Error(res.status.toString())
      }
      return res.json()
    }).catch((err: Error): void => {
      throw err
    }),
    fetch(POST_ISADMIN_URL + '/' + userStore.userId, {
      method: 'POST'
    }).then((res: Response) => {
      if (!res.ok) {
        throw new Error(res.status.toString())
      }
      return res.json()
    }).catch((err: Error): void => {
      throw err
    })
  ]).catch((err: Error): void => {
    throw err
  })

  logonIsAdmin.value = PromiseData[0]
  isAdmin.value = PromiseData[1]

  const res: Response = await fetch(GET_ISOVERTIMEALLOWED_URL + '/' + empNo, {
    method: 'GET'
  })
  if (res.ok) {
    const resJson = await res.json()
    workOvertimeVisible.value = resJson
  }

  await fetch(GET_ISAUTHORIZEDTOQUERYDEPARTMENTSENTBOX_URL + '/' + empNo, {
    method: 'POST',
    signal: abortController.signal
  }).then((res: Response): Promise<any> => {
    if (!res.ok) {
      throw new Error(res.status.toString())
    }
    return res.json()
  }).then((res: any): void => {
    deptSentVisible.value = res
  })

  menuItems.value = [
    {
      label: '三卡填報',
      icon: 'bi bi-alarm',
      items: [
        {
          label: FORM_ID.A1Card,
          icon: 'bi bi-sun',
          target: router.resolve({ name: 'A1CardForm' }).fullPath, // 沒設定target的話，event.item.target會是undefined
          command: (event: MenuItemCommandEvent): void => {
            onClickMenuItem(event, 'A1CardForm')
          }
        },
        {
          label: FORM_ID.B1CardApp,
          icon: 'bi bi-cloud-moon',
          visible: workOvertimeVisible.value,
          target: router.resolve({ name: 'B1CardAppForm' }).fullPath,
          command: (event: MenuItemCommandEvent): void => {
            onClickMenuItem(event, 'B1CardAppForm')
          }
        },
        {
          label: FORM_ID.B1Card,
          icon: 'bi bi-moon',
          visible: workOvertimeVisible.value,
          target: router.resolve({ name: 'B1CardForm' }).fullPath,
          command: (event: MenuItemCommandEvent): void => {
            onClickMenuItem(event, 'B1CardForm')
          }
        },
        {
          label: FORM_ID.C1Card,
          icon: 'bi bi-cloud-sun',
          target: router.resolve({ name: 'C1CardForm' }).fullPath,
          command: (event: MenuItemCommandEvent): void => {
            onClickMenuItem(event, 'C1CardForm')
          }
        }
      ]
    },
    {
      label: '三卡簽核',
      icon: 'bi bi-vector-pen',
      badge: badgeInbox,
      items: [
        {
          label: '待核卡',
          icon: 'bi bi-envelope-exclamation',
          badge: badgeInbox,
          target: router.resolve({ name: 'Inbox' }).fullPath,
          command: (event: MenuItemCommandEvent): void => {
            onClickMenuItem(event, 'Inbox')
          }
        },
        {
          label: '簽核紀錄',
          icon: 'bi bi-envelope-paper',
          target: router.resolve({ name: 'Signed' }).fullPath,
          command: (event: MenuItemCommandEvent): void => {
            onClickMenuItem(event, 'Signed')
          }
        }
      ]
    },
    {
      label: '三卡通知',
      icon: 'bi bi-bell',
      badge: badgeNotify,
      target: router.resolve({ name: 'Notify' }).fullPath,
      command: (event: MenuItemCommandEvent): void => {
        onClickMenuItem(event, 'Notify')
      }
    },
    {
      label: '填報查詢',
      icon: 'bi bi-search',
      items: [
        {
          label: '個人填報紀錄',
          icon: 'bi bi-folder',
          target: router.resolve({ name: 'Sent' }).fullPath,
          command: (event: MenuItemCommandEvent): void => {
            onClickMenuItem(event, 'Sent')
          }
        },
        {
          label: '部門填報紀錄',
          icon: 'bi bi-collection',
          visible: deptSentVisible.value,
          target: router.resolve({ name: 'DeptSent' }).fullPath,
          command: (event: MenuItemCommandEvent): void => {
            onClickMenuItem(event, 'DeptSent')
          }
        }
      ]
    }
  ]
  if (mode.split('.')[1] === 'intranet' && logonIsAdmin.value === true && isAdmin.value === true) {
    menuItems.value.push({
      label: '三卡管理',
      icon: 'bi bi-gear',
      target: router.resolve({ name: 'DataManagement' }).fullPath,
      command: (event: MenuItemCommandEvent): void => {
        onClickMenuItem(event, 'DataManagement')
      }
    })
  }

  if (logonIsAdmin.value === true) {
    await onSetEmployeeData(0, abortController.signal)
  }
})
</script>
<style lang="scss" scoped>
@use '../assets/scss/bootstrap_variables' as bootstrap;
.p-menubar {
  border: none;
}

:deep(.p-menubar-root-list > .p-menubar-item > .p-menubar-item-content > .p-menubar-item-link) {
  padding-bottom: bootstrap.$spacer * .75;
  padding-left: bootstrap.$spacer * .25;
  padding-right: bootstrap.$spacer * .25;
  padding-top: bootstrap.$spacer * .75;
}
</style>