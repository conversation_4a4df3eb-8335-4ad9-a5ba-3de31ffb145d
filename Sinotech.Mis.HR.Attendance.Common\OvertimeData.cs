﻿using Sinotech.Mis.HR.Attendance.BusinessLogic;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>加班資料</summary>
    public class OvertimeData
    {
        /// <summary>
        /// 刷卡時間
        /// </summary>
        public DayInTime InTime { get; set; } = new DayInTime();
        /// <summary>
        /// 刷卡時間字串
        /// </summary>
        public string InTimeString { get; set; } = string.Empty;
        /// <summary>
        /// 當日詳細資料
        /// </summary>
        public Workday DayDetail { get; set; } = new Workday();
        /// <summary>
        /// 是否為可加班特殊員工 (組長等，可加班時數與一般員工不同)
        /// </summary>
        public bool IsSpecialStaff { get; set; } = false;
        /// <summary>
        /// 是否為司機
        /// </summary>
        public bool IsDriver { get; set; } = false;
        /// <summary>
        /// 特殊員工每月加權後可加班時數
        /// </summary>
        public double SpecialStaffAllowedMonthWeightedOvertimeHours { get; set; } = 0.0;
        /// <summary>
        /// 特殊員工每月加權後已加班時數
        /// </summary>
        public double SpecialStaffCurrentMonthWeightedOvertimeHours { get; set; } = 0.0;
        /// <summary>
        /// 加班卡，若無則為 null
        /// </summary>
        public B1Card? B1Card { get; set; }
        /// <summary>
        /// 加班申請卡，若無則為 null
        /// </summary>
        public B1CardApp? B1CardApp { get; set; }
        /// <summary>
        /// 加班時數當日下限的值
        /// </summary>
        public int HoursLowerBound { get; set; } = 1;
        /// <summary>
        /// 加班時數當日上限的值
        /// </summary>
        public int HoursUpperBound { get; set; } = AttendanceParameters.DayOvertimeLimit;

        /// <summary>
        /// 加班時數不足一小時 之訊息
        /// </summary>
        public string OverHoursLowerBoundMessage { get; set; } = AttendanceParameters.B1CardAtLeastOneHour;

        /// <summary>
        /// 預定加班時數為零 之訊息
        /// </summary>
        public string HoursAreZeroMessage { get; set; } = AttendanceParameters.HoursAreZero;

        /// <summary>
        /// 超過加班時數當日上限的訊息
        /// </summary>
        public string OverHoursUpperBoundMessage { get; set; } = AttendanceParameters.AboveDayOvertimeLimitError;

        /// <summary>
        /// 加班時數當月即將達上限的值(warn)
        /// </summary>
        public double MonthCloseToHoursUpperBoundValue { get; set; } = AttendanceParameters.MonthCloseToHoursLimit;

        /// <summary>
        /// 加班時數當月即將達上限的訊息(warn)
        /// </summary>
        public string MonthCloseToHoursUpperBoundMessage { get; set; } = AttendanceParameters.MonthCloseToHoursUpperBoundMessage;

        /// <summary>
        /// 加班時數當月達上限的值(danger)
        /// </summary>
        public double MonthOverHoursUpperBoundValue { get; set; } = AttendanceParameters.MonthOvertimeHoursLimit;

        /// <summary>
        /// 加班時數當月達上限的訊息(danger)
        /// </summary>
        public string MonthOverHoursUpperBoundMessage { get; set; } = AttendanceParameters.AboveMonthOvertimeLimitWarning;

        /// <summary>
        /// 加班時數當季達上限的值(danger)
        /// </summary>
        public double QuarterOverHoursUpperBoundValue { get; set; } = AttendanceParameters.QuarterOvertimeHoursLimit;
        /// <summary>
        /// 加班時數當季達上限的訊息(danger)
        /// </summary>
        public string QuarterOverHoursUpperBoundMessage { get; set; } = AttendanceParameters.ReachQuarterOvertimeLimitWarning;
        /// <summary>
        /// 當月加班統計
        /// </summary>
        public MonthOvertimeStatics MonthOvertimeStatics { get; set; } = new MonthOvertimeStatics();
        /// <summary>
        /// 當季加班統計
        /// </summary>
        public QuarterlyOvertimeStatics QuarterlyOvertimeStatics { get; set; } = new QuarterlyOvertimeStatics();
    }
}
