﻿using Sinotech.Mis.Helpers;
using Sinotech.Mis.Utilities.DataAccess.Interfaces;
using System.Data;
using System.Data.SqlClient;

namespace Sinotech.Mis.Utilities.DataAccess.Ado
{
    /// <summary>員工資料存取元件</summary>
    public class EmployeeDao : IEmployeeDao
    {

        private readonly string _connectionString;

        /// <summary>Initializes a new instance of the <see cref="EmployeeDao" /> class.</summary>
        /// <param name="connectionString">The connection string.</param>
        public EmployeeDao(string connectionString)
        {
            _connectionString = connectionString;
        }

        /// <summary>
        /// 取得副理以上資料表
        /// </summary>
        /// <returns></returns>
        public DataTable GetAboveDeputyManagerDataTable()
        {
            string sql = "SELECT DeptNo,DeptSName,JobNo,JobName,EmpNo,CName,IsActive,Email FROM vwDeptManager;";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql);
            return dt;
        }

        /// <summary>
        /// 取得所有員工，包括離職員工
        /// </summary>
        /// <returns>所有員工，包括離職員工</returns>
        public DataTable GetAllEmployees()
        {
            string sql = @"SELECT vwCard_Emp.DeptNo,vwCard_Emp.DeptSName,vwDeptTeamEmp.TeamID,vwDeptTeam.TeamCName,
 vwCard_Emp.EmpNo,vwCard_Emp.CName,vwCard_Emp.JobNo,vwCard_Emp.JobName,vwCard_Emp.Sex,vwCard_Emp.TelExtension,vwCard_Emp.Birthday,
 vwCard_Emp.SpecLine,vwCard_Emp.Email,vwCard_Emp.RankNo,vwCard_Emp.RankName,vwCard_Emp.RankClassNo,vwCard_Emp.RankClassName,vwCard_Emp.Status
FROM vwCard_Emp LEFT JOIN
 vwDeptTeamEmp ON vwCard_Emp.EmpNo = vwDeptTeamEmp.EmpNo LEFT JOIN
 vwDeptTeam ON vwDeptTeamEmp.TeamID = vwDeptTeam.TeamID
ORDER BY vwCard_Emp.DeptNo,vwDeptTeamEmp.TeamID,vwCard_Emp.EmpNo;";
            return SqlHelper.GetDataTable(_connectionString, sql);
        }

        /// <summary>取得該員工所屬部門名稱</summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public string GetDepartmentName(string employeeNumber)
        {
            string sRet = "";
            string sql = "SELECT DeptSName FROM vwCard_Emp WHERE EmpNo=@EmpNo;";
            SqlParameter sqlParameter = new SqlParameter("@EmpNo", SqlDbType.NChar, 4);
            sqlParameter.Value = employeeNumber;
            var ret = SqlHelper.GetFieldValue(_connectionString, sql, sqlParameter);
            if (ret != null)
            {
                sRet = (string)ret;
            }
            return sRet;
        }

        /// <summary>取得該員工所屬部門編號</summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public int GetDepartmentNumber(string employeeNumber)
        {
            int iRet = -1;
            string sql = "SELECT DeptNo FROM vwCard_Emp WHERE EmpNo=@EmpNo;";
            SqlParameter sqlParameter = new SqlParameter("@EmpNo", SqlDbType.NChar, 4);
            sqlParameter.Value = employeeNumber;
            var ret = SqlHelper.GetFieldValue(_connectionString, sql, sqlParameter);
            if (ret != null)
            {
                iRet = (int)ret;
            }
            return iRet;
        }

        /// <summary>
        /// 取得部門資料
        /// </summary>
        /// <returns></returns>
        public DataTable GetDepartments()
        {
            string sql = "SELECT DeptNo, DeptESSName, DeptESName, DeptSSName, DeptSName, DeptName, DeptEName FROM vwDeptData WHERE (StNo=1)";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql);
            return dt;
        }

        /// <summary>取得該員工所屬部門名稱</summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public string GetDepartmentShortName(string employeeNumber)
        {
            string sRet = "";
            string sql = "SELECT DeptSName FROM vwCard_Emp WHERE EmpNo=@EmpNo;";
            SqlParameter sqlParameter = new SqlParameter("@EmpNo", SqlDbType.NChar, 4);
            sqlParameter.Value = employeeNumber;
            var ret = SqlHelper.GetFieldValue(_connectionString, sql, sqlParameter);
            if (ret != null)
            {
                sRet = (string)ret;
            }
            return sRet;
        }

        /// <summary>取得該部門所有現職員工人數</summary>
        /// <param name="departmentNumber">員工編號</param>
        /// <returns>該部門所有現職員工人數</returns>
        public int GetEmployeeCount(int departmentNumber)
        {
            int iRet = -1;
            string sql = "SELECT Count(EmpNo) FROM vwCard_Emp WHERE DeptNo=@DeptNo AND Status=0;";
            SqlParameter sqlParameter = new SqlParameter("@DeptNo", SqlDbType.Int);
            sqlParameter.Value = departmentNumber;
            var ret = SqlHelper.GetFieldValue(_connectionString, sql, sqlParameter);
            if (ret != null)
            {
                iRet = (int)ret;
            }
            return iRet;
        }

        /// <summary>取得員工詳細資料</summary>
        /// <param name="employeeNumber">The employee number.</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public DataTable GetEmployeeDetail(string employeeNumber)
        {
            string sql = @"SELECT vwCard_Emp.DeptNo,vwCard_Emp.DeptSName,vwDeptTeamEmp.TeamID,vwDeptTeam.TeamCName,
 vwCard_Emp.EmpNo,vwCard_Emp.CName,vwCard_Emp.JobNo,vwCard_Emp.JobName,vwCard_Emp.Sex,vwCard_Emp.TelExtension,vwCard_Emp.Birthday,
 vwCard_Emp.SpecLine,vwCard_Emp.Email,vwCard_Emp.RankNo,vwCard_Emp.RankName,vwCard_Emp.RankClassNo,vwCard_Emp.RankClassName 
 FROM vwCard_Emp LEFT JOIN
 vwDeptTeamEmp ON vwCard_Emp.EmpNo = vwDeptTeamEmp.EmpNo LEFT JOIN
 vwDeptTeam ON vwDeptTeamEmp.TeamID = vwDeptTeam.TeamID
WHERE (vwCard_Emp.Status = N'0') AND vwCard_Emp.EmpNo=@EmpNo
ORDER BY vwCard_Emp.DeptNo,vwDeptTeamEmp.TeamID,vwCard_Emp.EmpNo;";
            SqlParameter sqlParameter = new SqlParameter("@EmpNo", SqlDbType.NChar, 4);
            sqlParameter.Value = employeeNumber;
            return SqlHelper.GetDataTable(_connectionString, sql, sqlParameter);
        }

        /// <summary>取得員工姓名</summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public string GetEmployeeName(string employeeNumber)
        {
            string sRet = string.Empty;
            string sql = "SELECT CName FROM vwCard_Emp WHERE EmpNo=@EmpNo;";
            SqlParameter sqlParameter = new SqlParameter("@EmpNo", SqlDbType.NChar, 4);
            sqlParameter.Value = employeeNumber;
            var ret = SqlHelper.GetFieldValue(_connectionString, sql, sqlParameter);
            if (ret != null)
            {
                sRet = (string)ret;
            }
            return sRet;
        }

        /// <summary>取得所有現職員工</summary>
        /// <returns>所有現職員工</returns>
        public DataTable GetEmployees()
        {
            string sql = @"SELECT vwCard_Emp.DeptNo,vwCard_Emp.DeptSName,vwDeptTeamEmp.TeamID,vwDeptTeam.TeamCName,
 vwCard_Emp.EmpNo,vwCard_Emp.CName,vwCard_Emp.JobNo,vwCard_Emp.JobName,vwCard_Emp.Sex,vwCard_Emp.TelExtension,vwCard_Emp.Birthday,
 vwCard_Emp.SpecLine,vwCard_Emp.Email,vwCard_Emp.RankNo,vwCard_Emp.RankName,vwCard_Emp.RankClassNo,vwCard_Emp.RankClassName 
FROM vwCard_Emp LEFT JOIN
 vwDeptTeamEmp ON vwCard_Emp.EmpNo = vwDeptTeamEmp.EmpNo LEFT JOIN
 vwDeptTeam ON vwDeptTeamEmp.TeamID = vwDeptTeam.TeamID
WHERE (vwCard_Emp.Status = N'0')
ORDER BY vwCard_Emp.DeptNo,vwDeptTeamEmp.TeamID,vwCard_Emp.EmpNo";
            return SqlHelper.GetDataTable(_connectionString, sql);
        }

        /// <summary>取得該部門所有現職員工</summary>
        /// <param name="departmentNumber">部門編號</param>
        /// <returns>該部門所有現職員工</returns>
        public DataTable GetEmployees(int departmentNumber)
        {
            string sql = @"SELECT vwCard_Emp.DeptNo,vwCard_Emp.DeptSName,vwDeptTeamEmp.TeamID,vwDeptTeam.TeamCName,
 vwCard_Emp.EmpNo,vwCard_Emp.CName,vwCard_Emp.JobNo,vwCard_Emp.JobName,vwCard_Emp.Sex,vwCard_Emp.TelExtension,vwCard_Emp.Birthday,
 vwCard_Emp.SpecLine,vwCard_Emp.Email,vwCard_Emp.RankNo,vwCard_Emp.RankName,vwCard_Emp.RankClassNo,vwCard_Emp.RankClassName 
FROM vwCard_Emp LEFT JOIN
 vwDeptTeamEmp ON vwCard_Emp.EmpNo = vwDeptTeamEmp.EmpNo LEFT JOIN
 vwDeptTeam ON vwDeptTeamEmp.TeamID = vwDeptTeam.TeamID
WHERE (vwCard_Emp.Status = N'0') AND vwCard_Emp.DeptNo=@DeptNO
ORDER BY vwCard_Emp.DeptNo,vwDeptTeamEmp.TeamID,vwCard_Emp.EmpNo;";
            SqlParameter sqlParameter = new SqlParameter("@DeptNo", SqlDbType.Int);
            sqlParameter.Value = departmentNumber;
            return SqlHelper.GetDataTable(_connectionString, sql, sqlParameter);
        }

        /// <summary>
        /// 取得所有離職員工
        /// </summary>
        /// <returns>所有離職員工</returns>
        public DataTable GetLeftEmployees()
        {
            string sql = @"SELECT vwCard_Emp.DeptNo,vwCard_Emp.DeptSName,vwDeptTeamEmp.TeamID,vwDeptTeam.TeamCName,
 vwCard_Emp.EmpNo,vwCard_Emp.CName,vwCard_Emp.JobNo,vwCard_Emp.JobName,vwCard_Emp.Sex,vwCard_Emp.TelExtension,vwCard_Emp.Birthday,
 vwCard_Emp.SpecLine,vwCard_Emp.Email,vwCard_Emp.RankNo,vwCard_Emp.RankName,vwCard_Emp.RankClassNo,vwCard_Emp.RankClassName 
FROM vwCard_Emp LEFT JOIN
 vwDeptTeamEmp ON vwCard_Emp.EmpNo = vwDeptTeamEmp.EmpNo LEFT JOIN
 vwDeptTeam ON vwDeptTeamEmp.TeamID = vwDeptTeam.TeamID
WHERE (vwCard_Emp.Status <> N'0')
ORDER BY vwCard_Emp.DeptNo,vwDeptTeamEmp.TeamID,vwCard_Emp.EmpNo;";
            return SqlHelper.GetDataTable(_connectionString, sql);
        }

        /// <summary>取得所有現役組長</summary>
        /// <returns>
        ///   <br />
        /// </returns>
        public DataTable GetTeamLeaders()
        {
            string sql = @"SELECT DeptNo, DeptSName, TeamID, TeamCName, JobNo, JobName, TLEmpNo AS EmpNo, CName, 
StartDate, EndDate, IsActive FROM vwDeptTeamLeader WHERE (IsActive = 1)";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql);
            return dt;
        }

        /// <summary>
        /// 是否為副理以上
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>副理以上傳回<c>true</c>，否則傳回<c>false</c></returns>
        public bool IsAboveDeputyManager(string empNo)
        {
            bool ret = false;
            string sql = "SELECT COUNT(EmpNo) FROM vwDeptManager WHERE EmpNo=@EmpNo;";
            SqlParameter sqlParameter = new SqlParameter("@EmpNo", SqlDbType.NChar, 4);
            sqlParameter.Value = empNo;
            var iRet = SqlHelper.GetFieldValue(_connectionString, sql, sqlParameter);
            if (iRet != null)
            {
                int count = (int)iRet;
                ret = count == 1;
            }
            return ret;
        }

        /// <summary>取得所有現職員工人數</summary>
        public int EmployeeCount
        {
            get
            {
                int iRet = -1;
                string sql = "SELECT Count(EmpNo) FROM vwCard_Emp WHERE Status='0';";
                var ret = SqlHelper.GetFieldValue(_connectionString, sql);
                if (ret != null)
                {
                    iRet = (int)ret;
                }
                return iRet;
            }
        }

    }
}
