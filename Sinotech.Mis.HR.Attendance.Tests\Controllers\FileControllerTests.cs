﻿using FakeItEasy;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Threading.Tasks;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.AttendanceCard.Tests.Controllers
{
    [ExcludeFromCodeCoverage]
    public class FileControllerTests
    {
        private readonly FileController _fileController;

        public FileControllerTests()
        {
            ILogger<FileController> logger = A.Fake<ILogger<FileController>>();
            IConfiguration configuration = new ConfigurationBuilder().
                AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).
                Build();
            _fileController = new FileController(configuration, logger);
        }

        [Fact]
        public async Task Upload_ShouldReturnOkResult_WithUploadedFiles()
        {
            // Arrange
            var fakeFormFile = A.Fake<IFormFile>();
            <PERSON><PERSON>(() => fakeFormFile.Length).Returns(1024);
            <PERSON>.<PERSON>o(() => fakeFormFile.FileName).Returns("test.txt");
            A.CallTo(() => fakeFormFile.CopyToAsync(A<Stream>._, default)).Invokes((Stream stream, System.Threading.CancellationToken token) =>
            {
                using (var writer = new StreamWriter(stream))
                {
                    writer.Write("Dummy content");
                }
            });

            var files = new List<IFormFile> { fakeFormFile };

            // Act
            var result = await _fileController.Upload(files);
            OkObjectResult okResult = result as OkObjectResult;
            Assert.NotNull(okResult);

            // Assert
            var value = okResult.Value;
            Assert.NotNull(value);
            //var dynamicResult = value as dynamic;
            //var list = dynamicResult.list as List<UploadedFile>;
            //Assert.NotNull(result);
            //Assert.NotNull(list);
            //Assert.Single(list);
            //Assert.Equal("test.txt", list[0].FileName);
            //Assert.Equal(1024, list[0].Size);
            //Assert.False(string.IsNullOrEmpty(list[0].FilePathName));
        }
    }
}