﻿using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves.Tests
{
    public class DisasterLeaveTests : TestC1CardBypassAnyCheck
    {
        public DisasterLeaveTests()
        {
            #region Prepare Data

            _c1Card.LeaveNumber = LeaveKindEnum.DisasterLeave;

            #endregion
        }

        [Theory]
        [InlineData(Gender.Female, true)]
        [InlineData(Gender.Male, true)]
        public void TestGender(Gender gender, bool expected)
        {
            var result = C1CardBase.IsAllowForThisGender(_c1Card.LeaveNumber, gender);
            Assert.Equal(expected, result);
        }
    }
}