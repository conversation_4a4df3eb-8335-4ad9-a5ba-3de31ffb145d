﻿using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sinotech.Mis.Common;
using Sinotech.Mis.Helpers;
using Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.DataAccess.Interfaces;
using Sinotech.Mis.HR.Attendance.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Runtime.Caching;
using System.Threading;
using System.Threading.Tasks;
using static Antlr4.Runtime.Atn.SemanticContext;


namespace Sinotech.Mis.HR.Attendance.BusinessLogic
{
    /// <summary>
    /// 加班卡 商業物件
    /// </summary>
    public class B1CardBo : IB1CardBo
    {

        private static ObjectCache _cache = MemoryCache.Default;
        private static readonly object _cacheLock = new object();
        // 避免重覆送出鎖定物件
        private static readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);
        private readonly IAttendanceBo _attendanceBo;
        private readonly IB1CardAppBo _b1CardAppBo;
        private readonly IB1CardDao _b1CardDao;
        private readonly IEmployeeBo _employeeBo;
        private readonly IFormBo _formBo;
        private readonly IFormFlowBo _formFlowBo;
        private readonly ILogger<B1CardBo> _logger;
        private readonly IOvertimeBo _overtimeBo;
        private readonly IProjectBo _projectBo;
        private readonly IWorkdayBo _workdayBo;

        /// <summary>Initializes a new instance of the <see cref="B1CardBo" /> class.</summary>
        /// <param name="attendanceBo"></param>
        /// <param name="employeeBo">The employee bo</param>
        /// <param name="formBo"></param>
        /// <param name="b1CardDao"></param>
        /// <param name="overtimeBo">Overtime 商業物件</param>
        /// <param name="projectBo"></param>
        /// <param name="logger">The logger.</param>
        /// <param name="workdayBo"></param>
        public B1CardBo(IAttendanceBo attendanceBo, IB1CardAppBo b1CardAppBo, IEmployeeBo employeeBo,
            IFormBo formBo, IFormFlowBo formFlowBo, IB1CardDao b1CardDao, IOvertimeBo overtimeBo, 
            IProjectBo projectBo,  ILogger<B1CardBo> logger, IWorkdayBo workdayBo)
        {
            _b1CardDao = b1CardDao;
            _attendanceBo = attendanceBo;
            _b1CardAppBo = b1CardAppBo;
            _employeeBo = employeeBo;
            _formBo = formBo;
            _formFlowBo = formFlowBo;
            _projectBo = projectBo;
            _overtimeBo = overtimeBo;
            _logger = logger;
            _workdayBo = workdayBo;
        }

        /// <summary>取得某段時間內表單及卡</summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="dtCardForms">表單及卡的DataTable物件</param>
        /// <returns>表單及卡 FormCardsDto 物件 List</returns>
        private List<FormCard> GetFormCards(DateTime startDate, DateTime endDate, DataTable dtCardForms)
        {
            DataTable dtCards = _b1CardDao.GetB1Cards(startDate, endDate);
            List<FormCard> formCards = new List<FormCard>();

            var guids = (from DataRow dr in dtCardForms.Rows
                         select (Guid)dr["FormUID"]).Distinct().OrderBy(FormUID => FormUID);

            foreach (var guid in guids)
            {
                FormCard formCard = new FormCard();
                formCard.FormUID = guid;
                B1Card card = new B1Card();
                formCard.Flows = new List<FormFlow>();
                DataRow[] drs = dtCardForms.Select($"FormUID='{guid}'");
                if (drs.Length > 0)
                {
                    DataRow row = drs[0];
                    formCard.ID = (int)row["FormIntID"];
                    formCard.FormUID = guid;
                    formCard.FormID = (string)row["FormID"];
                    formCard.FormNo = (string)row["FormNo"];
                    formCard.FormSubject = (string)row["FormSubject"];
                    formCard.FormInfo = (string)row["FormInfo"];
                    formCard.EmpNo = (string)row["EmpNo"];
                    formCard.EmpName = (string)row["EmpName"];
                    formCard.DeptNo = (int)row["DeptNo"];
                    formCard.DeptSName = SqlHelper.GetColumnStringValue(row, "DeptSName");
                    formCard.TeamID = SqlHelper.GetColumnIntValue(row, "TeamID");
                    formCard.TeamCName = SqlHelper.GetColumnStringValue(row, "TeamCName");
                    if (row["RankNo"] != DBNull.Value)
                    {
                        formCard.RankNo = (string)row["RankNo"];
                    }
                    if (row["RankName"] != DBNull.Value)
                    {
                        formCard.RankName = (string)row["RankName"];
                    }

                    formCard.JobNo = SqlHelper.GetColumnStringValue(row, "JobNo");
                    formCard.JobName = SqlHelper.GetColumnStringValue(row, "JobName");

                    formCard.ContentStartTime = SqlHelper.GetColumnDateTimeValue(row, "ContentStartTime");
                    formCard.ContentEndTime = SqlHelper.GetColumnDateTimeValue(row, "ContentEndTime");
                    formCard.CreatedEmpNo = (string)row["CreatedEmpNo"];
                    formCard.CreatedName = (string)row["CreatedName"];
                    formCard.FilledTime = (DateTime)row["FilledTime"];
                    formCard.CreatedTime = (DateTime)row["CreatedTime"];
                    formCard.CreatedIP = (string)row["CreatedIP"];
                    formCard.CreatedHost = SqlHelper.GetColumnStringValue(row, "CreatedHost");

                    formCard.AddedSigner = SqlHelper.GetColumnStringValue(row, "AddedSigner");
                    formCard.AddedSigner = _formBo.AddSignersAddName(formCard.AddedSigner);
                    formCard.StartTime = (DateTime)row["StartTime"];

                    formCard.EndTime = SqlHelper.GetColumnDateTimeValue(row, "EndTime");

                    formCard.FormStatus = (byte)row["FormStatus"];
                    formCard.FormStatusName = (string)row["FormStatusName"];
                    formCard.TotalSteps = (byte)row["TotalSteps"];
                    formCard.CurrentStep = (byte)row["CurrentStep"];

                    card.FormUID = guid;
                    card.UpdatedEmpNo = SqlHelper.GetColumnStringValue(row, "UpdatedEmpNo");
                    card.UpdatedName = SqlHelper.GetColumnStringValue(row, "UpdatedName");
                    card.UpdatedTime = SqlHelper.GetColumnDateTimeValue(row, "UpdatedTime");
                    card.UpdatedIP = SqlHelper.GetColumnStringValue(row, "UpdatedIP");
                    card.UpdatedHost = SqlHelper.GetColumnStringValue(row, "UpdatedHost");
                    card.CreatedTime = (DateTime)row["CreatedTime"];
                    card.AddSigners = formCard.AddedSigner;

                    card.EmpNo = (string)row["B1_EmpNo"];
                    card.Reason = (string)row["B1_Reason"];
                    card.B1_WDate = (DateTime)row["B1_WDate"];
                    card.SheetNo = (string)row["B1_SHEETNO"];
                    card.B1_ADate = SqlHelper.GetColumnDateTimeValue(row, "B1_ADate");

                    var detailRows = from DataRow dr in dtCards.Rows
                                     where (Guid)dr["FormUID"] == guid
                                     orderby dr["ID"]
                                     select dr;
                    card.TotalHours = 0;
                    foreach (DataRow detailRow in detailRows)
                    {
                        B1CardDetail detail = new B1CardDetail();
                        detail.ID = (int)detailRow["ID"];
                        detail.Project = (string)detailRow["B1_PROJNO"];
                        detail.StartTime = (DateTime)detailRow["B1_StartDate"];
                        detail.EndTime = (DateTime)detailRow["B1_EndDate"];
                        detail.Hour = (byte)detailRow["B1_HOUR"];
                        card.TotalHours += detail.Hour;
                        detail.HourLeft = (byte)detailRow["B1_HOURLEFT"];
                        detail.B1_CODE = int.Parse((string)detailRow["B1_CODE"]);
                        detail.SerialNo = int.Parse((string)detailRow["B1_SERIALNO"]);
                        card.Details.Add(detail);
                    }

                    if (drs[0]["Step"] != DBNull.Value)
                    {
                        var stepRows = (from DataRow dr in drs select (byte)row["Step"])
                        .Distinct().OrderBy(Step => Step);

                        foreach (byte step in stepRows)
                        {
                            var k = from DataRow dr in drs
                                    where (byte)dr["Step"] == step
                                    select dr;
                            if (k != null && k.Any())
                            {
                                DataRow dr = k.First();
                                FormFlow flow = FormFlow.DataRowToFormFlow(dr);
                                flow.ID = (int)dr["FormFlowID"];
                                flow.FlowStatusName = _formFlowBo.GetFlowStatusName(flow.FlowStatus);
                                formCard.Flows.Add(flow);
                            }
                        }
                    }
                }
                card.SetApplicationType();
                formCard.Card = card;
                _formBo.AddAttachments(dtCardForms, formCard, drs);
                formCards.Add(formCard);
            }
            return formCards;
        }

        /// <summary>
        /// 上一筆加班卡
        /// </summary>
        private static B1Card _LastCard { get; set; } = new B1Card();

        /// <summary>
        /// 快取 10 分鐘
        /// </summary>
        private static CacheItemPolicy CachePolicy
        {
            get
            {
                CacheItemPolicy policy = new CacheItemPolicy();
                policy.AbsoluteExpiration = DateTimeOffset.Now.AddMinutes(2.0);
                return policy;
            }
        }

        /// <summary>新增 加班卡</summary>
        /// <param name="creatorId">填表人 員工編號</param>
        /// <param name="b1Card">加班卡</param>
        /// <param name="ipAddress">IP address</param>
        /// <param name="hostname">Host name</param>
        /// <returns>傳回 B1CardCheckResult</returns>
        public async Task<B1CardCheckResult> AddB1Card(string creatorId, B1Card b1Card, string ipAddress,
            string hostname)
        {
            b1Card.TotalHours = 0;
            //計算 時數 Hours及給予 Serial Number
            for (int i = 0; i < b1Card.Details.Count; i++)
            {
                B1CardDetail detail = b1Card.Details[i];
                detail.SerialNo = i + 1;
                TimeSpan timeDiff = detail.EndTime - detail.StartTime;
                detail.Hour = timeDiff.Hours;
                b1Card.TotalHours += detail.Hour;
            }

            B1CardCheckResult result = IsValidB1Card(b1Card);
            if (!result.IsValid)
            {
                return result; // false
            }
            string errorMessage = string.Empty;
            await _semaphore.WaitAsync();
            try
            {
                if (b1Card.EasyEquals(_LastCard))
                {
                    result.ErrorMessage = AttendanceParameters.RepeatSubmitForm;
                    result.IsValid = false;
                }
                else
                {
                    DateTime startTime = b1Card.Details[0].StartTime;
                    Form form = new Form();
                    form.FormID = "B1Card";
                    form.FormUID = Guid.NewGuid();
                    form.EmpNo = b1Card.EmpNo;
                    Employee employee = _employeeBo.GetEmployeeDetail(form.EmpNo);
                    Employee employeeCreator = _employeeBo.GetEmployeeDetail(creatorId);
                    form.EmpName = employee.CName;
                    form.DeptNo = employee.DeptNo;
                    form.DeptSName = employee.DeptSName;
                    form.TeamID = employee.TeamID;
                    form.TeamCName = employee.TeamCName;
                    form.RankNo = employee.RankNo;
                    form.RankName = employee.RankName;
                    form.JobNo = employee.JobNo;
                    form.JobName = employee.JobName;
                    form.ContentStartTime = startTime;
                    form.ContentEndTime = b1Card.Details[0].EndTime;
                    form.FormSubject = $"加班卡-{form.EmpName}-{CardUtility.RocChineseDateString(startTime)}";
                    form.FormInfo = $"{CardUtility.RocChineseDateString(startTime)}";
                    form.CreatedEmpNo = creatorId;
                    form.CreatedName = _employeeBo.GetEmployeeName(form.CreatedEmpNo);
                    form.FilledTime = b1Card.FilledTime.ToLocalTime();
                    form.CreatedTime = b1Card.CreatedTime.ToLocalTime();
                    form.CreatedIP = ipAddress;
                    form.CreatedHost = hostname;
                    form.AddedSigner = b1Card.AddSigners;
                    form.StartTime = DateTime.Now;
                    form.UpdatedTime = form.CreatedTime;
                    form.UpdatedEmpNo = creatorId;
                    form.UpdatedName = employeeCreator.CName;
                    form.UpdatedIP = ipAddress;
                    form.UpdatedHost = hostname;
                    form.FormStatus = (int)FormStatus.Processing; //簽核狀態：1:簽核中
                    form.CurrentStep = 0; //所有表單都預設為0，讓FormBo自動計算

                    // 加會人員關卡
                    if (b1Card.AddSigners != null)
                    {
                        if (b1Card.AddSigners.EndsWith(','))
                        {
                            b1Card.AddSigners = b1Card.AddSigners.Substring(0, b1Card.AddSigners.Length - 1);
                        }
                        form.AddedSigner = b1Card.AddSigners;
                        string[] signers = b1Card.AddSigners.Split(',');
                        _formFlowBo.FlowAddSigners(form, signers);
                    }

                    b1Card.DateTypeId = (int)_workdayBo.GetDayType(startTime);
                    b1Card.FormUID = form.FormUID;
                    b1Card.UpdatedTime = form.CreatedTime;
                    b1Card.UpdatedEmpNo = creatorId;
                    b1Card.UpdatedName = employeeCreator.CName;
                    b1Card.UpdatedIP = ipAddress;
                    b1Card.UpdatedHost = hostname;
                    foreach (B1CardDetail detail in b1Card.Details)
                    {
                        Project project = _projectBo.GetProject(detail.Project);
                        // 若計畫屬於其他部門
                        _formBo.AddFlowByProject(form, project);
                    }
                    // 去除重覆關卡
                    form.Flows = _formFlowBo.FlowDedup(form.Flows);

                    // 申請人部門主管，必須在最後一關
                    _formFlowBo.FlowAddManager(form, employee);

                    //呼叫 FormBO 寫入資料庫
                    form.TotalSteps = form.Flows.Count;
                    errorMessage = _formBo.AddForm(form, this, b1Card);
                }
            }
            catch (Exception ex)
            {
                errorMessage = AttendanceParameters.SubmitErrorMessage;
                string formJson = JsonConvert.SerializeObject(b1Card, Formatting.Indented);
                _logger.LogError(ex, "Action: {Action} 發生錯誤，錯誤訊息: {Message} {StackTrace}，內容為 {FormJson}", nameof(AddB1Card), ex.Message, ex.StackTrace, formJson);
                Console.WriteLine($"{DateTime.Now} AddB1Card 發生錯誤 {ex.Message} {ex.StackTrace}");
            }
            finally
            {
                _semaphore.Release();
            }

            if (errorMessage != string.Empty)
            {
                result.IsValid = false;
                result.ErrorMessage = errorMessage;
            }
            return result;
        }

        /// <summary>依照表單資料補足三卡資料 B1Card</summary>
        /// <param name="form">表單</param>
        /// <param name="card">卡</param>
        /// <returns></returns>
        public bool AmendCard(Form form, CardBase card)
        {
            B1Card b1Card = (B1Card)card;
            b1Card.SheetNo = form.FormNo;
            SetApplicationType(b1Card);
            b1Card.SetApplicationType();
            return true;
        }

        /// <summary>
        /// 是否能結案抽單 (管理員抽單)
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        public string CanClosedWithdraw(CardBase card)
        {
            B1Card b1Card = (B1Card)card;
            if (b1Card.Status == (int)FormStatus.Withdraw)
            {
                return "表單已抽單";
            }
            if (b1Card.Status == (int)FormStatus.Processing)
            {
                return "表單進行中";
            }
            return string.Empty;
        }

        /// <summary>
        /// 是否能抽單 (正常情況)
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        public string CanWithdraw(CardBase card)
        {
            return string.Empty;
        }

        /// <summary>檢查 加班卡</summary>
        /// <param name="b1Card">加班卡</param>
        /// <returns>傳回 B1CardCheckResult</returns>
        public B1CardCheckResult CheckData(B1Card b1Card)
        {
            b1Card.TotalHours = 0;
            //計算 時數 Hours及給予 Serial Number
            for (int i = 0; i < b1Card.Details.Count; i++)
            {
                B1CardDetail detail = b1Card.Details[i];
                detail.SerialNo = i + 1;
                TimeSpan timeDiff = detail.EndTime - detail.StartTime;
                detail.Hour = timeDiff.Hours;
                b1Card.TotalHours += detail.Hour;
            }

            B1CardCheckResult result = IsValidB1Card(b1Card);
            return result;
        }

        /// <summary>是否可以填加班卡</summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>傳回 B1CardCheckDateResult</returns>
        public B1CardCheckResult DateCanFillB1Card(DateTime date, string empNo)
        {
            B1CardCheckResult result = new B1CardCheckResult();
            result.IsValid = true;
            if (string.IsNullOrWhiteSpace(empNo))
            {
                result.CanOvertime = false;
                result.IsValid = false;
                result.ErrorMessage = "員工編號有誤";
                result.UserErrorMessage = "員工編號有誤";
                return result; // 未知員工不能填報
            }
            result.CanOvertime = true;
            Employee? emp = _employeeBo.GetEmployeeDetail(empNo);

            if (emp == null)
            {
                result.CanOvertime = false;
                result.IsValid = false;
                result.ErrorMessage = "員工編號有誤";
                result.UserErrorMessage = "員工編號有誤";
                return result; // 未知員工不能填報
            }

            result.Employee = new EmployeeSimple
            {
                EmpNo = emp.EmpNo,
                CName = emp.CName,
                DeptNo = emp.DeptNo,
                DeptSName = emp.DeptSName
            };


            result.DayDetail = _workdayBo.GetWorkday(date);
            result.HoursLowerBound = _workdayBo.GetMinOvertimeHours(date);
            result.MinPaidOvertimeHours = _workdayBo.GetMinPaidOvertimeHours(date);
            result.HoursUpperBound = _workdayBo.GetMaxOvertimeHours(date);

            if (result.DayDetail == null)
            {
                result.CanOvertime = false;
                result.IsValid = false;
                return result; // 日期未設定不能填報
            }

            Workday workday = _workdayBo.GetWorkday(date);
            //檢查日期是否可報加班
            if (workday.DayType == WorkdayType.SundayHoliday ||
                workday.DayType == WorkdayType.SundayRegularHoliday) //星期日不准加班
            {
                result.IsValid = false;
                result.CanOvertime = false;
                result.ErrorMessage = AttendanceParameters.SundayCannotWorkOvertime;
                return result;
            }

            if (workday.DayType == WorkdayType.SaturdayNaturalDisasterDay ||
                workday.DayType == WorkdayType.WeekNaturalDisasterDay) //天災日不准加班
            {
                result.IsValid = false;
                result.CanOvertime = false;
                result.ErrorMessage = AttendanceParameters.DisasterDayWorkOvertimeError;
                return result;
            }

            if (_employeeBo.IsAboveDeputyManager(empNo))
            {
                result.CanOvertime = false;
                // 副理以上不准加班
                result.IsValid = false;
                result.UserErrorMessage = AttendanceParameters.AboveDeputyManagerCannotWorkOvertime;
                result.ErrorMessage = AttendanceParameters.AboveDeputyManagerCannotWorkOvertime;
            }

            FormFilled filledB1CardApp = _b1CardAppBo.IsFilledB1CardApp(date, empNo);

            //檢查是否填過加班申請卡
            if ((filledB1CardApp.FormNo == null || !filledB1CardApp.IsApproved) && !emp.IsDriver) // 司機可不填加班申請卡
            {
                result.IsValid = false;
                result.CanOvertime = false;
                result.IsFilled = false;
                result.FilledForm = filledB1CardApp;
                result.DateErrorMessage = AttendanceParameters.NeedB1CardAppError;
                result.ErrorMessage = AttendanceParameters.NeedB1CardAppError;
                return result;
            }

            //檢查是否填過加班卡
            FormFilled filled = IsFilledB1Card(date, empNo);
            if (filled.FormNo != null)
            {
                result.IsValid = false;
                result.CanOvertime = false;
                result.IsFilled = true;
                result.FilledForm = filled;
                result.DateErrorMessage = AttendanceParameters.OnlyOneB1CardPerDay;
                result.ErrorMessage = AttendanceParameters.OnlyOneB1CardPerDay;
                return result;
            }
            return result;
        }

        /// <summary>結案</summary>
        /// <param name="card">The card.</param>
        /// <param name="formStatus">The form status.</param>
        /// <param name="updateDto">更新資料物件</param>
        public void Finish(CardBase card, FormStatus formStatus, Updater updateDto)
        {
            Update(card, updateDto);
            B1Card b1Card = (B1Card)card;
            b1Card.B1_ADate = updateDto.UpdatedTime;
            b1Card.Status = (int)formStatus;
        }

        /// <summary>
        /// 取得加班類型 B1CODE 列表
        /// </summary>
        /// <returns></returns>
        public List<B1CardType> GetB1CardTypes()
        {
            List<B1CardType> list = new List<B1CardType>();
            string cacheName = "B1CardTypes";
            if (_cache.Contains(cacheName))
            {
                list = (List<B1CardType>)_cache[cacheName];
            }
            else
            {
                DataTable dt = _b1CardDao.GetB1CardTypes();
                foreach (DataRow dr in dt.Rows)
                {
                    int typeID = int.Parse((string)dr["ID"]);
                    string typeName = (string)dr["Name"];
                    list.Add(new B1CardType { Type = typeID, TypeName = typeName });
                }
                lock (_cacheLock)
                {
                    _cache.Set(cacheName, list, CachePolicy);
                }
            }
            return list;
        }

        /// <summary>
        /// 取得卡
        /// </summary>
        /// <param name="formUID">Form UUID</param>
        /// <returns>卡</returns>
        public CardBase? GetCard(Guid formUID)
        {
            B1Card? b1Card = null;
            DataTable dt = _b1CardDao.GetB1Cards(formUID);
            if (dt != null && dt.Rows.Count > 0)
            {
                List<B1CardDto> b1Cards = SqlHelper.ConvertDataTable<B1CardDto>(dt);
                b1Card = B1CardDto.B1CardDtoToB1Card(b1Cards);
                SetApplicationType(b1Card);
                b1Card.SetApplicationType();
            }
            return b1Card;
        }

        /// <summary>取得某段時間內卡，不含流程與附件等</summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <returns>卡 CardsDto 物件 List</returns>
        public List<CardBase> GetCards(DateTime startDate, DateTime endDate)
        {
            List<CardBase> cards = new List<CardBase>();
            DataTable dt = _b1CardDao.GetB1Cards(startDate, endDate);
            if (dt != null && dt.Rows.Count > 0)
            {
                List<B1CardDto> b1CardDtos = SqlHelper.ConvertDataTable<B1CardDto>(dt);
                var formNos = b1CardDtos.Select(x => x.B1_SHEETNO).Distinct();
                foreach (var formNo in formNos)
                {
                    var result = (from b1CardAppDto in b1CardDtos
                                  where b1CardAppDto.B1_SHEETNO == formNo
                                  select b1CardAppDto).ToList();
                    B1Card card = B1CardDto.B1CardDtoToB1Card(result);
                    cards.Add(card);
                }
            }
            return cards;
        }

        /// <summary>取得表單及加班卡</summary>
        /// <param name="form">The form.</param>
        /// <param name="userId">The user identifier.</param>
        /// <returns>表單及加班卡 FormCardsDto 物件</returns>
        public FormCard GetFormCard(Form form, string userId)
        {
            FormCard formCard = new FormCard();
            List<CardBase> cards = new List<CardBase>();
            formCard.ID = form.ID;
            formCard.FormID = form.FormID;
            formCard.FormUID = form.FormUID;
            formCard.FormNo = form.FormNo;
            formCard.FormSubject = form.FormSubject;
            formCard.FormInfo = form.FormInfo;
            formCard.EmpNo = form.EmpNo;
            formCard.EmpName = form.EmpName;
            formCard.DeptNo = form.DeptNo;
            formCard.DeptSName = form.DeptSName;
            formCard.TeamID = form.TeamID;
            formCard.TeamCName = form.TeamCName;
            formCard.CreatedEmpNo = form.CreatedEmpNo;
            formCard.CreatedName = form.CreatedName;
            formCard.FilledTime = form.FilledTime;
            formCard.CreatedTime = form.CreatedTime;
            formCard.CreatedIP = form.CreatedIP;
            formCard.CreatedHost = form.CreatedHost;
            formCard.StartTime = form.StartTime;
            formCard.EndTime = form.EndTime;
            formCard.FormStatus = form.FormStatus;
            formCard.FormStatusName = _formBo.GetFormStatusName(form.FormStatus);
            formCard.TotalSteps = form.TotalSteps;
            formCard.CurrentStep = form.CurrentStep;
            formCard.UpdatedEmpNo = form.UpdatedEmpNo;
            formCard.UpdatedName = form.UpdatedName;
            formCard.UpdatedTime = form.UpdatedTime;
            formCard.UpdatedIP = form.UpdatedIP;
            formCard.UpdatedHost = form.UpdatedHost;
            formCard.AddedSigner = _formBo.AddSignersAddName(form.AddedSigner);
            formCard.Flows = form.Flows;
            formCard.Hours = 0;

            CardBase? card = GetCard(form.FormUID);
            if (card != null)
            {
                B1Card b1Card = (B1Card)card;
                b1Card.AddSigners = _formBo.AddSignersAddName(form.AddedSigner);
                formCard.Hours = b1Card.TotalHours;
                SetApplicationType(b1Card);
                b1Card.SetApplicationType();
                cards.Add(b1Card);
                formCard.Card = b1Card;
            }
            // 附件
            formCard.Attachments = form.Attachments;
            return formCard;
        }

        /// <summary>取得某段時間內表單及卡</summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <returns>表單及卡 FormCardsDto 物件 List</returns>
        public List<FormCard> GetFormCards(DateTime startDate, DateTime endDate)
        {
            DataTable dtCardForms = _b1CardDao.GetB1CardsForms(startDate, endDate);
            List<FormCard> formCards = GetFormCards(startDate, endDate, dtCardForms);
            return formCards;
        }

        /// <summary>取得某段時間內表單及卡</summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單及卡 FormCardsDto 物件 List</returns>
        public List<FormCard> GetFormCards(DateTime startDate, DateTime endDate, string projNo)
        {
            DataTable dtCardForms = _b1CardDao.GetB1CardsForms(startDate, endDate, projNo);
            List<FormCard> formCards = GetFormCards(startDate, endDate, dtCardForms);
            return formCards;
        }

        /// <summary>
        /// 取得總時數
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        public int GetHours(CardBase card)
        {
            int hours = 0;
            B1Card b1Card = (B1Card)card;
            foreach (B1CardDetail detail in b1Card.Details)
            {
                hours += detail.Hour;
            }
            return hours;
        }

        /// <summary>
        /// 取得提醒資訊
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        public (int, string?) GetListRemindMessage(CardBase card)
        {
            string? ret = null;
            int messageType = 0;
            B1Card b1Card = (B1Card)card;
            if (!_employeeBo.IsEmployee(b1Card.EmpNo))
            {
                string empName = _employeeBo.GetEmployeeName(b1Card.EmpNo);
                string errorMessage = @"申請人" + AttendanceParameters.IsOffServiceStaff;
                return (99, errorMessage);
            }
            B1CardPositionEnum position = _attendanceBo.GetPositionType(b1Card.EmpNo);

            if (position != B1CardPositionEnum.Driver) // 非司機都檢查
            {
                DateTime date = b1Card.Details[0].StartTime;
                int day = DateTime.DaysInMonth(date.Year, date.Month);
                date = new DateTime(date.Year, date.Month, day);

                string name = _employeeBo.GetEmployeeName(b1Card.EmpNo);
                OvertimeData overtimeData = _overtimeBo.GetOvertimeData(date, b1Card.EmpNo);
                // 檢查每月累計加班時數
                double monthOvertimeHours = overtimeData.MonthOvertimeStatics.TotalHours;

                // 當月第40~53小時
                if (monthOvertimeHours >= AttendanceParameters.MonthCloseToHoursLimit && monthOvertimeHours < AttendanceParameters.MonthOvertimeHoursLimit)
                {
                    messageType = 1;
                    ret = AttendanceParameters.B1CardMonthOvertimeSignerWarningA.Replace("{name}", name).Replace("{hours}", monthOvertimeHours.ToString());
                }

                // 已達每月上限 第54小時
                if (monthOvertimeHours >= AttendanceParameters.MonthOvertimeHoursLimit)
                {
                    messageType = 2;
                    ret = AttendanceParameters.B1CardMonthOvertimeSignerWarningB.Replace("{name}", name);
                }

                // 檢查每季累計加班時數
                double quarterOvertimeHours = overtimeData.QuarterlyOvertimeStatics.TotalHours;
                if (quarterOvertimeHours >= AttendanceParameters.QuarterOvertimeHoursLimit)
                {
                    messageType = 3;
                    ret = AttendanceParameters.B1CardReachQuarterOvertimeLimitSignerWarning.Replace("{name}", name);
                }
            }
            return (messageType, ret);
        }

        /// <summary>
        /// 取得特定月份卡，依表單日期為準，非填表時間
        /// </summary>
        /// <param name="empNo">填表員工編號</param>
        /// <param name="date">日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns></returns>
        public List<CardBase> GetUserCardsMonth(string empNo, DateTime date, int? status = null)
        {
            List<CardBase> cards = new List<CardBase>();
            DataTable dtB1Cards = _b1CardDao.GetB1CardMonth(empNo, date, status);
            if (dtB1Cards != null && dtB1Cards.Rows.Count > 0)
            {
                List<B1CardDto> b1CardDtos = SqlHelper.ConvertDataTable<B1CardDto>(dtB1Cards);
                var dates = (from b1CardDto in b1CardDtos
                             select b1CardDto.B1_StartDate.Date).Distinct();
                foreach (DateTime theDate in dates)
                {
                    var list = (from b1CardDto in b1CardDtos
                                where b1CardDto.B1_StartDate.Date == theDate
                                select b1CardDto).ToList();
                    if (list.Any())
                    {
                        B1Card b1Card = B1CardDto.B1CardDtoToB1Card(list);
                        SetApplicationType(b1Card);
                        b1Card.SetApplicationType();
                        cards.Add(b1Card);
                    }
                }
            }
            return cards;
        }

        /// <summary>
        /// 取得特定月份已填表單及卡，包括他人代填與本人填表，依表單年月為準，非填表時間
        /// </summary>
        /// <param name="empNo">填表員工編號</param>
        /// <param name="year">表單年</param>
        /// <param name="month">表單月</param>
        /// <returns></returns>
        public List<FormCard> GetUserSentFormCardsYearMonth(string empNo, int year, int month)
        {
            List<FormCard> formCards = new List<FormCard>();
            DataTable dtCardForms = _b1CardDao.GetSentB1CardYearMonth(empNo, year, month);
            if (dtCardForms != null && dtCardForms.Rows.Count > 0)
            {
                DateTime startDate = DateTime.MaxValue;
                DateTime endDate = DateTime.MinValue;
                foreach (DataRow dr in dtCardForms.Rows)
                {
                    DateTime createTime = (DateTime)dr["CreatedTime"];
                    if (createTime < startDate)
                    {
                        startDate = createTime;
                    }
                    if (createTime > endDate)
                    {
                        endDate = createTime;
                    }
                }
                formCards = GetFormCards(startDate, endDate, dtCardForms);
            }
            return formCards;
        }

        /// <summary>
        /// 是否已填加班卡
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public bool IsFilled(DateTime date, string empNo)
        {
            bool ret = _b1CardDao.IsFilled(date, empNo);
            return ret;
        }

        /// <summary>
        /// 是否已填加班卡
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>若未填則傳回 {FormUID: null, FormNo: null}</returns>
        public FormFilled IsFilledB1Card(DateTime date, string empNo)
        {
            FormFilled filled = new FormFilled();
            DataTable dt = _b1CardDao.IsFilledB1Card(date, empNo);
            if (dt != null && dt.Rows.Count > 0)
            {
                if (dt.Rows[0]["FormUID"] != DBNull.Value)
                {
                    filled.FormUID = (Guid)dt.Rows[0]["FormUID"];
                }
                filled.FormNo = (string)dt.Rows[0]["B1_SHEETNO"];
                int iApproved = (int)(byte)dt.Rows[0]["B1_STATUS"];
                if (iApproved == 2)
                {
                    filled.IsApproved = true;
                }
            }
            return filled;
        }

        /// <summary>
        /// 是否為有效的加班卡
        /// </summary>
        /// <param name="b1Card"></param>
        /// <returns>B1CardCheckResult物件，包括AlarmMessage與ErrorMessage</returns>
        public B1CardCheckResult IsValidB1Card(B1Card b1Card)
        {
            B1CardCheckResult result = new B1CardCheckResult();
            result.CanOvertime = true;
            result.IsValid = true;

            if (string.IsNullOrWhiteSpace(b1Card.EmpNo))
            {
                result.CanOvertime = false;
                result.IsValid = false;
                result.ErrorMessage = AttendanceParameters.BadEmployeeNumber;
                result.UserErrorMessage = AttendanceParameters.BadEmployeeNumber;
                return result; // 未知員工不能填報
            }

            Employee? emp = _employeeBo.GetEmployeeDetail(b1Card.EmpNo);
            if (emp == null)
            {
                result.CanOvertime = false;
                result.IsValid = false;
                result.ErrorMessage = AttendanceParameters.BadEmployeeNumber;
                result.UserErrorMessage = AttendanceParameters.BadEmployeeNumber;
                return result; // 未知員工不能填報
            }
            result.Employee = new EmployeeSimple
            {
                EmpNo = emp.EmpNo,
                CName = emp.CName,
                DeptNo = emp.DeptNo,
                DeptSName = emp.DeptSName
            };

            if (!_attendanceBo.IsOvertimeAllowed(b1Card.EmpNo, b1Card.OvertimeDate).IsOvertimeAllowed)
            {
                // 副理以上不准加班
                result.CanOvertime = false;
                result.IsValid = false;
                //result.ErrorMessage = AttendanceParameters.AboveDeputyManagerCannotWorkOvertime;
                result.UserErrorMessage = AttendanceParameters.AboveDeputyManagerCannotWorkOvertime;
                return result; // 副理以上不准加班
            }

            if (b1Card.Details == null || b1Card.Details.Count == 0)
            {
                result.IsValid = false;
                result.ErrorMessage = "未填寫計畫加班資料\n請按【填寫計畫加班資料】後填寫加班申請別和時間";
                return result;
            }

            DateTime date = b1Card.Details[0].StartTime;
            Workday workday = _workdayBo.GetWorkday(date);
            //檢查日期是否可報加班
            if (workday.DayType == WorkdayType.SundayHoliday ||
                workday.DayType == WorkdayType.SundayRegularHoliday) //星期日不准加班
            {
                result.IsValid = false;
                result.CanOvertime = false;
                result.ErrorMessage = AttendanceParameters.SundayCannotWorkOvertime;
                return result;
            }

            if (workday.DayType == WorkdayType.SaturdayNaturalDisasterDay ||
                workday.DayType == WorkdayType.WeekNaturalDisasterDay) //天災日不准加班
            {
                result.IsValid = false;
                result.CanOvertime = false;
                result.ErrorMessage = AttendanceParameters.DisasterDayWorkOvertimeError;
                return result;
            }

            FormFilled filledB1CardApp = _b1CardAppBo.IsFilledB1CardApp(date, b1Card.EmpNo);
            //檢查是否填過加班申請卡
            if ((filledB1CardApp.FormNo == null || !filledB1CardApp.IsApproved) && !emp.IsDriver) // 司機可不填加班申請卡
            {
                result.IsValid = false;
                result.CanOvertime = false;
                result.IsFilled = false;
                result.FilledForm = filledB1CardApp;
                result.DateErrorMessage = AttendanceParameters.NeedB1CardAppError;
                result.ErrorMessage = AttendanceParameters.NeedB1CardAppError;
                return result;
            }

            //檢查是否填過加班卡
            FormFilled filled = IsFilledB1Card(date, b1Card.EmpNo);
            if (filled.FormNo != null)
            {
                result.IsValid = false;
                result.CanOvertime = false;
                result.IsFilled = true;
                result.FilledForm = filled;
                result.DateErrorMessage = AttendanceParameters.OnlyOneB1CardPerDay;
                result.ErrorMessage = AttendanceParameters.OnlyOneB1CardPerDay;
                return result;
            }

            double allowedMonthWeightedOvertimeHour, currentMonthWeightedOvertimeHour;
            bool isSpecialStaff = _attendanceBo.IsSpecialStaff(b1Card.EmpNo, b1Card.Details[0].StartTime,
                out allowedMonthWeightedOvertimeHour, out currentMonthWeightedOvertimeHour);

            MonthOvertimeStatics monthOvertimeStatics;
            QuarterlyOvertimeStatics quarterlyOvertimeStatics;
            int monthOvertimeHours, quarterOvertimeHours;
            DayOvertime dayOvertime = _overtimeBo.B1CardToDayOvertime(b1Card);

            monthOvertimeStatics = _attendanceBo.GetMonthEmployeeOvertimeStatics(b1Card.Details[0].StartTime, b1Card.EmpNo);
            quarterlyOvertimeStatics = _attendanceBo.GetQuarterlyEmployeeOvertimeStatics(b1Card.Details[0].StartTime, b1Card.EmpNo);
            monthOvertimeHours = dayOvertime.TotalHours + monthOvertimeStatics.TotalHours;
            quarterOvertimeHours = dayOvertime.TotalHours + quarterlyOvertimeStatics.TotalHours;

            result.TotalHours = dayOvertime.TotalHours;
            result.InOvertimeHours = dayOvertime.InOvertimeHours;

            result.DayDetail = _workdayBo.GetWorkday(b1Card.Details[0].StartTime);

            // 加班卡與加班申請卡不同，最少當日可加班時數 要以 支薪時數 申請
            result.HoursLowerBound = _workdayBo.GetMinPaidOvertimeHours(date);
            result.MinPaidOvertimeHours = _workdayBo.GetMinPaidOvertimeHours(date);
            result.HoursUpperBound = _workdayBo.GetMaxOvertimeHours(date);

            B1CardParameters b1CardParameters = new B1CardParameters
            {
                OvertimeDate = date,
                EmployeeDetail = emp,
                EmployeePosition = _attendanceBo.GetPositionType(b1Card.EmpNo),
                IsValid = result.IsValid,
                IsFilledB1CardApp = filledB1CardApp.IsApproved,
                IsFilledB1Card = (result.FilledForm != null),
                FilledForm = result.FilledForm,
                IsAboveDeputyManager = emp.IsAboveDeputyManager,
                IsDriver = emp.IsDriver,
                IsSpecialStaff = isSpecialStaff,
                InTime = _attendanceBo.GetDayInTime(date, b1Card.EmpNo),
                DayDetail = result.DayDetail,
                SpecialStaffAllowedMonthWeightedOvertimeHours = allowedMonthWeightedOvertimeHour,
                CurrentMonthWeightedOvertimeHours = currentMonthWeightedOvertimeHour,
                B1Card = b1Card
            };
            if (b1CardParameters.IsFilledB1CardApp)
            {
                b1CardParameters.B1CardApp = _attendanceBo.GetValidB1CardApp(date, b1Card.EmpNo);
            }
            b1CardParameters.CanOvertime = result.CanOvertime;
            b1CardParameters.DayOvertime = dayOvertime;
            b1CardParameters.MonthOvertimeStatics = monthOvertimeStatics;
            b1CardParameters.QuarterlyOvertimeStatics = quarterlyOvertimeStatics;
            b1CardParameters.Projects = new List<Project>();
            List<B1CardDetail> newDetails = new List<B1CardDetail>();
            foreach (var detail in b1Card.Details)
            {
                B1CardDetailProject b1CardDetailProject = new B1CardDetailProject(detail);
                b1CardDetailProject.ProjectDetail = _projectBo.GetProject(detail.Project);
                newDetails.Add(b1CardDetailProject);
                b1CardParameters.Projects.Add(_projectBo.GetProject(detail.Project));
            }
            b1Card.Details = newDetails;


            if (result.IsValid)
            {
                // Call 大哥大的檢查程式
                var checker = new B1CardChecker(b1CardParameters);
                var b1CardResult = checker.CheckData();

                switch (b1CardResult.Status)
                {
                    case B1CardStatusEnum.Error:

                        result.ErrorMessage = b1CardResult.Message;
                        result.IsValid = false;
                        break;
                    case B1CardStatusEnum.Warning:
                        result.AlarmMessage = b1CardResult.Message;
                        break;
                }
            }
            return result;
        }

        /// <summary>
        /// 設定B1Card TypeName
        /// </summary>
        /// <param name="card"></param>
        public void SetApplicationType(B1Card card)
        {
            List<B1CardType> cardTypes = GetB1CardTypes();
            foreach (B1CardDetail detail in card.Details)
            {
                int b1Code = int.Parse(detail.B1_CODE.ToString());
                string? name = (from B1CardType t in cardTypes
                                where t.Type == b1Code
                                select t.TypeName).FirstOrDefault();
                if (name != null)
                {
                    detail.ApplicationType = name;
                }
            }
        }

        /// <summary>轉換為Stored Procedure所需的 User Defined Table</summary>
        /// <param name="card">三卡</param>
        /// <param name="isAdd">是否為新增</param>
        /// <returns></returns>
        public List<DataTable> ToUserDefinedDataTables(CardBase card, bool isAdd = true)
        {
            List<B1CardDto> list = new List<B1CardDto>();
            B1Card b1Card = (B1Card)card;
            DayOvertime overtime = _overtimeBo.B1CardToDayOvertime(b1Card);
            foreach (B1CardDetail detail in b1Card.Details)
            {
                B1CardDto b1CardDto = new B1CardDto
                {
                    ID = detail.ID,
                    FormUID = b1Card.FormUID,
                    B1_EMPNO = b1Card.EmpNo,
                    B1_YYMM = CardUtility.RocChineseYYYMM(detail.StartTime),
                    B1_PROJNO = detail.Project,
                    B1_SDD = detail.StartTime.ToString("dd"),
                    B1_SHH = detail.StartTime.ToString("HH"),
                    B1_SMM = detail.StartTime.ToString("mm")
                };
                // b1CardDto.B1_EDD = detail.EndTime.ToString("dd");
                b1CardDto.B1_EDD = b1CardDto.B1_SDD; // 加班不得跨天日填報，必須為同一日
                b1CardDto.B1_EHH = detail.EndTime.ToString("HH");
                if (b1CardDto.B1_EHH == "00")
                {
                    b1CardDto.B1_EHH = "24";
                }
                b1CardDto.B1_EMM = detail.EndTime.ToString("mm");
                b1CardDto.B1_StartDate = detail.StartTime;
                b1CardDto.B1_EndDate = detail.EndTime;
                b1CardDto.B1_DateTypeId = b1Card.DateTypeId;
                b1CardDto.B1_HOUR = detail.Hour;
                b1CardDto.B1_CODE = detail.B1_CODE;
                b1CardDto.B1_WYYMMDD = CardUtility.RocChineseYYYMMDD(b1Card.B1_WDate);

                if (b1Card.B1_ADate != null)
                {
                    b1CardDto.B1_AYYMMDD = CardUtility.RocChineseYYYMMDD((DateTime)b1Card.B1_ADate);
                }
                else
                {
                    b1CardDto.B1_AYYMMDD = null;
                }

                b1CardDto.B1_STATUS = b1Card.Status;
                b1CardDto.B1_SHEETNO = b1Card.SheetNo;
                b1CardDto.B1_SERIALNO = detail.SerialNo.ToString();
                b1CardDto.B1_SOURCE = b1Card.Source;
                b1CardDto.B1_HOURLEFT = detail.HourLeft;
                b1CardDto.B1_WDate = b1Card.B1_WDate;
                b1CardDto.B1_ADate = b1Card.B1_ADate;
                b1CardDto.B1_Reason = b1Card.Reason;
                b1CardDto.UpdatedEmpNo = b1Card.UpdatedEmpNo;
                b1CardDto.UpdatedName = b1Card.UpdatedName;
                b1CardDto.UpdatedTime = b1Card.UpdatedTime;
                b1CardDto.UpdatedIP = b1Card.UpdatedIP;
                b1CardDto.UpdatedHost = b1Card.UpdatedHost;
                list.Add(b1CardDto);
            }

            DataTable dt = SqlHelper.CreateDataTable<B1CardDto>(list);
            dt.TableName = "B1Card";
            List<DataTable> dataTables;
            if (isAdd)
            {
                DataTable dtB1Rate;
                DataTable dtB1Rate_CompHol;
                (dtB1Rate, dtB1Rate_CompHol) = _overtimeBo.DayOvertimeToB1Rate(overtime, b1Card);
                dataTables = new List<DataTable> { dt, dtB1Rate, dtB1Rate_CompHol };
            }
            else
            {
                dataTables = new List<DataTable> { dt };
            }
            return dataTables;
        }

        /// <summary>更新Card</summary>
        /// <param name="card">The card.</param>
        /// <param name="updateDto">更新資料物件</param>
        public void Update(CardBase card, Updater updateDto)
        {
            B1Card b1Card = (B1Card)card;
            if (updateDto.UpdatedIP != null)
            {
                b1Card.UpdatedName = updateDto.UpdatedName;
                b1Card.UpdatedIP = updateDto.UpdatedIP;
            }
            b1Card.UpdatedEmpNo = updateDto.UpdatedEmpNo;
            b1Card.UpdatedTime = updateDto.UpdatedTime;
            b1Card.B1_ADate = updateDto.UpdatedTime;
            b1Card.UpdatedHost = updateDto.UpdatedHost;
        }

        /// <summary>
        /// 抽單
        /// </summary>
        /// <param name="card"></param>
        /// <param name="withdraw"></param>
        /// <returns></returns>
        public bool Withdraw(CardBase card, Withdraw withdraw)
        {
            bool ret = false;
            if (withdraw != null)
            {
                B1Card b1Card = (B1Card)card;
                b1Card.Status = (int)FormStatus.Withdraw;
                b1Card.B1_ADate = withdraw.WithdrawTime;
                ret = true;
            }
            return ret;
        }
    }
}
