<template>
  <div class="container text-center mt-5 pt-5">
    <p class="h1">
      {{ messageStore.data }}
    </p>
    <router-link :to="{ name: 'Sen<PERSON>' }">
      查詢填報紀錄
    </router-link>
  </div>
</template>
<script setup lang="ts">
import { onBeforeRouteLeave } from 'vue-router'
import { useMessageStore } from '../store/message'

const messageStore = useMessageStore()

onBeforeRouteLeave(() => {
  messageStore.setData('')
})
</script>