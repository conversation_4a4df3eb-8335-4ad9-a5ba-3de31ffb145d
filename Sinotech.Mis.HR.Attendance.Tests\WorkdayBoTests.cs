﻿using Microsoft.Extensions.Configuration;
using Sinotech.Mis.Extensions.Configuration;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.Tests;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.Tests
{

    [ExcludeFromCodeCoverage]
    public class WorkdayBoTests
    {

        IWorkdayBo _workdayBo;

        public WorkdayBoTests(IWorkdayBo workdayBo)
        {
            _workdayBo = workdayBo;
            IConfiguration configuration = new ConfigurationBuilder().
        AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).Build();
            string? ConnectionStringAttendance = configuration.GetSecuredConnectionString("Attendance");
            if (ConnectionStringAttendance != null)
            {
                TestHelper.ClearData(ConnectionStringAttendance);
            }
        }


        [Theory]
        [InlineData("2023-09-22", 6, 1, "2023-10-02")]
        [InlineData("2023-09-23", 3, 1, "2023-09-27")]
        public void AddWorkDaysTest(DateTime startDate, int days, int shiftId, DateTime expect)
        {
            DateTime date = _workdayBo.AddWorkDays(startDate, days, shiftId);
            Assert.Equal(expect, date);
        }

        [Theory]
        [InlineData(2023, 1, 9, 5, "2268", "2023-01-13")]  // 一般工作日，不跨週末
        [InlineData(2023, 1, 9, 10, "2268", "2023-01-30")] // 跨越週末
        [InlineData(2023, 1, 30, 5, "2268", "2023-02-03")]  // 跨月份
        [InlineData(2023, 2, 27, 3, "2268", "2023-03-03")]  // 跨越228和週末
        public void AddWorkDays_ShouldCalculateCorrectWorkDays(int year, int month, int day, int days, string empNo, DateTime expectedDate)
        {
            // Arrange
            var startDate = new DateTime(year, month, day);

            // Act
            var result = _workdayBo.AddWorkDays(startDate, days, empNo);

            // Assert
            Assert.Equal(expectedDate, result);
        }

        [Theory]
        [InlineData(2022, 1, "0395", 31)]
        [InlineData(2022, 2, "0395", 28)]
        [InlineData(2022, 3, "0395", 31)]
        [InlineData(2022, 4, "0395", 30)]
        [InlineData(2022, 5, "0395", 31)]
        [InlineData(2022, 6, "0395", 30)]
        [InlineData(2022, 7, "0395", 31)]
        [InlineData(2022, 8, "0395", 31)]
        [InlineData(2022, 9, "0395", 30)]
        [InlineData(2022, 10, "0395", 31)]
        [InlineData(2022, 11, "0395", 30)]
        [InlineData(2022, 12, "0395", 31)]
        public void GetEmpWorkDaysInMonthTest(int year, int month, string empNo, int expectDays)
        {
            List<Workday> list = _workdayBo.GetEmpWorkDaysInMonth(year, month, empNo);
            int daysCount = list.Count;
            Assert.Equal(expectDays, daysCount);
        }

        [Theory]
        [InlineData("2022-01-21", 4)] // 上班日
        [InlineData("2022-01-22", 4)] // 補班日
        [InlineData("2022-02-28", 12)] // 週間國定假日
        [InlineData("2022-03-25", 4)] // 補班日
        [InlineData("2022-09-09", 12)] // 補假日
        [InlineData("2022-01-15", 12)] // 週六休息日
        [InlineData("2022-09-10", 12)] // 週六國定假日
        [InlineData("2022-01-23", 0)] // 週日例假日
        [InlineData("2022-01-30", 0)] // 週日國定假日
        public void GetMaxOvertimeHoursTest(DateTime date, int expect)
        {
            int maxOverTimeHours = _workdayBo.GetMaxOvertimeHours(date);
            Assert.Equal(expect, maxOverTimeHours);
        }

        [Theory]
        [InlineData("2022-01-22", 1)] // 補班日
        [InlineData("2022-01-21", 1)] // 上班日
        [InlineData("2022-02-28", 1)] // 週間國定假日
        [InlineData("2022-03-25", 1)] // 補班日
        [InlineData("2022-09-09", 1)] // 補假日
        [InlineData("2022-01-15", 1)] // 週六休息日
        [InlineData("2022-09-10", 1)] // 週六國定假日
        [InlineData("2022-01-23", 0)] // 週日例假日
        [InlineData("2022-01-30", 0)] // 週日國定假日
        public void GetMinOvertimeHoursTest(DateTime date, int expect)
        {
            int minOverTimeHours = _workdayBo.GetMinOvertimeHours(date);
            Assert.Equal(expect, minOverTimeHours);
        }

        [Theory]
        [InlineData("2022-01-05", 168)]
        [InlineData("2022-02-02", 120)]
        [InlineData("2022-10-05", 160)]
        [InlineData("2022-12-05", 176)]
        public void GetMonthWorkHoursTest(DateTime date, int expect)
        {
            int monthWorkHours = _workdayBo.GetMonthWorkHours(date);
            Assert.True(monthWorkHours > 0);
            Assert.Equal(expect, monthWorkHours);
        }

        [Theory]
        [InlineData(1, "工作日")]
        [InlineData(4, "週間國定假日")]
        [InlineData(7, "補班日")]
        public void GetWorkdayTypeNameTest(int typeId, string expactedName)
        {
            WorkdayType type = (WorkdayType)typeId;
            string typeName = _workdayBo.GetWorkdayTypeName(type);
            Assert.Equal(expactedName, typeName);
        }

        [Theory]
        [InlineData(2022, 1, 1, 1, false)]
        [InlineData(2022, 1, 2, 1, false)]
        [InlineData(2022, 1, 3, 1, true)]
        [InlineData(2022, 1, 4, 1, true)]
        [InlineData(2022, 1, 22, 1, true)]
        [InlineData(2022, 2, 1, 1, false)]
        [InlineData(2022, 2, 2, 1, false)]
        [InlineData(2022, 2, 3, 1, false)]
        [InlineData(2022, 2, 4, 1, false)]
        [InlineData(2022, 2, 5, 1, false)]
        [InlineData(2022, 2, 6, 1, false)]
        [InlineData(2022, 2, 7, 1, true)]
        [InlineData(2022, 2, 8, 1, true)]
        public void IsWorkdayYearMonthDayTest(int year, int month, int day, int shiftId, bool expected)
        {
            DateTime date = new DateTime(year, month, day, 0, 0, 0, DateTimeKind.Local);
            bool actual = _workdayBo.IsWorkday(date, shiftId);
            Assert.Equal(expected, actual);
        }

        [Theory]
        [InlineData(2021, 2, 1, @"2021-02-26")]
        [InlineData(2022, 1, 1, @"2022-01-28")]
        [InlineData(2022, 2, 1, @"2022-02-25")]
        [InlineData(2022, 3, 1, @"2022-03-31")]
        [InlineData(2022, 4, 1, @"2022-04-29")]
        [InlineData(2022, 10, 1, @"2022-10-31")]
        [InlineData(2022, 11, 1, @"2022-11-30")]
        [InlineData(2022, 12, 1, @"2022-12-30")]
        [InlineData(2023, 2, 1, @"2023-02-24")]
        public void LastWorkDayInMonthTest(int year, int month, int shiftId, DateTime exptect)
        {
            DateTime actual = _workdayBo.LastWorkDayInMonth(year, month, shiftId);
            Assert.Equal(exptect, actual);
            // 再執行一次測試 Cache
            actual = _workdayBo.LastWorkDayInMonth(year, month, shiftId);
            Assert.Equal(exptect, actual);
        }

        [Theory]
        [InlineData(2022, 12, 31, 2023, 1, 3, "0395")]
        [InlineData(2023, 1, 1, 2023, 1, 3, "0395")]
        [InlineData(2023, 1, 2, 2023, 1, 3, "0395")]
        [InlineData(2023, 1, 7, 2023, 1, 7, "0395")]
        [InlineData(2023, 1, 20, 2023, 1, 30, "0395")]
        [InlineData(2023, 1, 24, 2023, 1, 30, "0395")]
        [InlineData(2023, 2, 24, 2023, 2, 24, "0395")]
        [InlineData(2023, 2, 25, 2023, 3, 1, "0395")]
        [InlineData(2023, 4, 29, 2023, 5, 2, "0395")]
        [InlineData(2023, 5, 1, 2023, 5, 2, "0395")]
        public void MostRecentWorkDateAfterTest(int year, int month, int day, int rYear, int rMonth, int rDay, string empNo)
        {
            DateTime date = new DateTime(year, month, day, 0, 0, 0, DateTimeKind.Local);
            DateTime endDate = date.AddMonths(1);
            List<Workday> workdays = _workdayBo.GetEmpWorkdaysDateRange(date, endDate, empNo);
            DateTime expect = new DateTime(rYear, rMonth, rDay, 0, 0, 0, DateTimeKind.Local);
            DateTime result = _workdayBo.MostRecentWorkDateAfter(date, workdays);
            Assert.Equal(expect, result);
        }

        [Theory]
        [InlineData(2023, 1, 1, 2022, 12, 30, "0395")]
        [InlineData(2023, 1, 2, 2022, 12, 30, "0395")]
        [InlineData(2023, 1, 29, 2023, 1, 19, "0395")]
        [InlineData(2023, 1, 30, 2023, 1, 30, "0395")]
        [InlineData(2023, 2, 26, 2023, 2, 24, "0395")]
        [InlineData(2023, 2, 28, 2023, 2, 24, "0395")]
        [InlineData(2023, 4, 1, 2023, 3, 31, "0395")]
        [InlineData(2023, 4, 5, 2023, 3, 31, "0395")]
        [InlineData(2023, 4, 6, 2023, 4, 6, "0395")]
        [InlineData(2023, 5, 1, 2023, 4, 28, "0395")]
        [InlineData(2023, 9, 23, 2023, 9, 23, "0395")]
        [InlineData(2023, 10, 10, 2023, 10, 6, "0395")]
        public void MostRecentWorkDateBeforeTest(int year, int month, int day, int rYear, int rMonth, int rDay, string empNo)
        {
            DateTime date = new DateTime(year, month, day, 0, 0, 0, DateTimeKind.Local);
            DateTime expect = new DateTime(rYear, rMonth, rDay, 0, 0, 0, DateTimeKind.Local);
            DateTime startDate = date.AddMonths(-1);
            List<Workday> workdays = _workdayBo.GetEmpWorkdaysDateRange(startDate, date, empNo);
            DateTime result = _workdayBo.MostRecentWorkDateBefore(date, workdays);
            Assert.Equal(expect, result);
        }

        [Theory]
        [InlineData("2023-04-28 10:00", "2023-05-02 14:00", 2)]
        [InlineData("2023-04-28 10:00", "2023-04-30 17:00", 1)]
        [InlineData("2023-04-28 08:00", "2023-05-02 14:00", 2)]
        [InlineData("2023-03-28 10:00", "2023-05-02 17:00", 3)]
        public void SplitDateByMonthCalendarDayTest(DateTime startDate, DateTime endDate, int expect)
        {
            List<(DateTime, DateTime)> result = _workdayBo.SplitDateByMonthCalendarDay(startDate, endDate, 1); //只計算正常班
            Assert.Equal(expect, result.Count);
        }

        [Theory]
        [InlineData("2023-4-28 10:00", "2023-5-2 14:00", 2, "2023-4-28 10:00", "2023-4-30 17:00")]
        [InlineData("2023-3-28 10:00", "2023-5-2 14:00", 3, "2023-3-28 10:00", "2023-3-31 17:00")]
        public void SplitDateByMonthCalendarDayTest2(string startTime, string endTime, int count, string resultStart, string resultEnd)
        {
            DateTime startDate = DateTime.Parse(startTime);
            DateTime endDate = DateTime.Parse(endTime);
            List<(DateTime, DateTime)> result = _workdayBo.SplitDateByMonthCalendarDay(startDate, endDate, 1); //只計算正常班
            Assert.Equal(count, result.Count);
            DateTime resultStartDate = DateTime.Parse(resultStart);
            DateTime resultEndDate = DateTime.Parse(resultEnd);
            var (start, end) = result[0];
            Assert.Equal(resultStartDate, start);
            Assert.Equal(resultEndDate, end);
        }

        [Theory]
        [InlineData("2022-1-28 10:00", "2022-2-8 16:00", 2, "2022-1-28 10:00", "2022-1-28 17:00", "0395")]
        [InlineData("2022-2-19 10:00", "2022-2-27 14:00", 1, "2022-2-22 8:00", "2022-2-25 17:00", "0395")]
        [InlineData("2022-12-29 10:00", "2023-1-4 14:00", 2, "2022-12-29 10:00", "2022-12-30 17:00", "0395")]
        [InlineData("2023-1-20 10:00", "2023-1-31 14:00", 1, "2023-1-30 8:00", "2023-1-31 14:00", "0395")]
        [InlineData("2023-2-25 10:00", "2023-3-2 14:00", 1, "2023-3-1 8:00", "2023-3-2 14:00", "0395")]
        [InlineData("2023-2-25 10:00", "2023-3-2 0:00", 1, "2023-3-1 8:00", "2023-3-2 17:00", "0395")]
        [InlineData("2023-4-28 10:00", "2023-5-2 14:00", 2, "2023-4-28 10:00", "2023-4-28 17:00", "0395")]
        [InlineData("2023-3-28 10:00", "2023-5-2 14:00", 3, "2023-3-28 10:00", "2023-3-31 17:00", "0395")]
        [InlineData("2023-9-29 10:00", "2023-10-3 14:00", 1, "2023-10-2 08:00", "2023-10-3 14:00", "0395")]
        public void SplitDateByMonthWorkdayTest(DateTime startDate, DateTime endDate, int count, string resultStart, string resultEnd, string empNo)
        {
            List<(DateTime, DateTime)> result = _workdayBo.SplitDateByMonthWorkday(startDate, endDate, empNo);
            Assert.Equal(count, result.Count);
            DateTime resultStartDate = DateTime.Parse(resultStart);
            DateTime resultEndDate = DateTime.Parse(resultEnd);
            var (start, end) = result[0];
            Assert.Equal(resultStartDate, start);
            Assert.Equal(resultEndDate, end);
        }

        [Theory]
        [InlineData("2022-01-01", "2022-12-31", 248)]
        [InlineData("2022-02-24", "2022-02-25", 1)]
        [InlineData("2022-02-24", "2022-02-26", 1)]
        [InlineData("2022-02-24", "2022-02-27", 1)]
        [InlineData("2022-02-24", "2022-02-28", 1)]
        [InlineData("2022-02-24", "2022-03-01", 2)]
        [InlineData("2022-07-01", "2022-07-05", 2)]
        [InlineData("2022-07-02", "2022-07-05", 2)]
        [InlineData("2022-07-03", "2022-07-05", 2)]
        [InlineData("2022-07-04", "2022-07-05", 1)]
        [InlineData("2022-07-05", "2022-07-05", 0)]
        [InlineData("2023-01-01", "2023-01-03", 1)]
        [InlineData("2023-01-01", "2023-12-05", 229)]
        [InlineData("2023-01-01", "2023-12-31", 247)]
        [InlineData("2023-08-31", "2023-11-27", 60)]
        [InlineData("2023-09-01", "2023-09-02", 0)]
        [InlineData("2023-09-01", "2023-11-26", 58)]
        [InlineData("2023-09-01", "2023-11-27", 59)]
        [InlineData("2023-11-27", "2024-3-18", 74)]
        public void WorkdaysRangeDiffTest(DateTime startTime, DateTime endTime, int expectDays)
        {
            int days = _workdayBo.WorkdaysRangeDiff(startTime, endTime);
            Assert.Equal(expectDays, days);
        }

        [Theory]
        [InlineData(2022, 10, 24, "2022-10-31")]
        [InlineData(2023, 10, 24, "2023-10-31")]
        [InlineData(2024, 8, 31, "2024-8-30")]
        public void GetLastWorkDayInTenDaysTest(int year, int month, int day, DateTime expected)
        {
            DateTime actual = _workdayBo.GetLastWorkDayInTenDays(year, month, day);
            Assert.Equal(expected, actual);
            actual = _workdayBo.GetLastWorkDayInTenDays(year, month, day);
            Assert.Equal(expected, actual);
        }

        /// <summary>
        /// 測試計算正常工作時數
        /// </summary>
        /// <param name="startDateStr">開始時間</param>
        /// <param name="endDateStr">結束時間</param>
        /// <param name="shiftId">班別ID</param>
        /// <param name="expectedHours">預期工作時數</param>
        [Theory]
        [InlineData("2024-01-09 08:30:00", "2024-01-09 17:30:00", 1, 8.0)] // 正常工作日
        [InlineData("2024-01-09 07:30:00", "2024-01-09 17:30:00", 1, 8.0)] // 提早到班工時算加班不在工常工時內
        [InlineData("2024-01-09 08:30:00", "2024-01-09 18:30:00", 1, 8.0)] // 晚下班
        [InlineData("2024-01-09 12:00:00", "2024-01-09 17:30:00", 1, 4.5)] // 午休時間開始
        [InlineData("2024-01-09 08:30:00", "2024-01-09 12:30:00", 1, 3.5)] // 午休時間結束
        [InlineData("2024-01-09 08:30:00", "2024-01-10 17:30:00", 1, 15.5)] // 跨天計算
        [InlineData("2024-01-11 08:30:00", "2024-01-11 17:30:00", 1, 8.0)] // 週六不計算
        public void CalculateWorkingHoursDateDiffTest(DateTime startDate, DateTime endDate, int shiftId, double expectedHours)
        {
            // Act
            double actualHours = _workdayBo.CalculateWorkingHoursDateDiff(startDate, endDate, shiftId);

            // Assert
            Assert.Equal(expectedHours, actualHours); // 允許誤差在1小時內
        }
    }
}
