﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Sinotech.Mis.HR.Attendance.BusinessLogic;
using System;
using System.Diagnostics.CodeAnalysis;

namespace Sinotech.Mis.HR.Attendance.HomeInbox.Controllers
{
    /// <summary>社內首頁使用 收件匣/待審表單  物件</summary>
    [AllowAnonymous]
    [ApiController]
    [Route("[controller]")]
    [ExcludeFromCodeCoverage]
    public class InboxController : ControllerBase
    {
        private readonly FormBo _formBo;
        private readonly ILogger<InboxController> _logger;
        private readonly CardBoFactory _cardBoFactory;

        /// <summary>
        ///   <see cref="InboxController" /> 的建構函式</summary>
        /// <param name="formBo">The form 商業物件.</param>
        /// <param name="cardBoFactory">CardBo 工廠</param>
        /// <param name="logger">The logger.</param>
        public InboxController(FormBo formBo, CardBoFactory cardBoFactory, ILogger<InboxController> logger)
        {
            _formBo = formBo;
            _logger = logger;
            _cardBoFactory = cardBoFactory;
        }

        /// <summary>
        /// 取得特定員工的待審表單供社內首頁提醒使用
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>待審表單的社內首頁提醒專用html</returns>
        [AllowAnonymous]
        [HttpGet]
        public string Get(string empNo)
        {
            string ret = "";
            try
            {
                ret = _formBo.GetInboxForRemind(empNo, string.Empty, _cardBoFactory);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                _logger.LogError("/HomeInbox/Inbox?empNo={empNo} 發生錯誤：{Message} {StackTrace}", empNo, ex.Message, ex.StackTrace);
            }
            return ret;
        }
    }
}
