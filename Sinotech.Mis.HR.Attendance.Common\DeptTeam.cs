﻿using Sinotech.Mis.Common;
using System.Collections.Generic;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 部門組別DTO
    /// </summary>
    public class DeptTeam
    {
        /// <summary>
        /// 組別ID
        /// </summary>
        public int? TeamID { get; set; }

        /// <summary>
        /// 部門編號
        /// </summary>
        public int DeptNo { get; set; }

        /// <summary>
        /// 部門簡稱
        /// </summary>
        public string DeptSName { get; set; } = string.Empty;

        /// <summary>
        /// 組別中文名稱
        /// </summary>
        public string TeamCName { get; set; } = string.Empty;

        /// <summary>
        /// 組員List
        /// </summary>
        public List<EmployeeTiny> TeamMembers { get; set; } = new List<EmployeeTiny>();
    }
}
