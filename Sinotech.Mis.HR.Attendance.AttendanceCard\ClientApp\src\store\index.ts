import { defineStore } from 'pinia'
import CryptoJS from 'crypto-js'

import { GET_ISADMIN_URL, GET_EMPLOYEE_URL } from '../api/appUrl'
import { FETCH_TIMEOUT_TIME, SESSION_STORAGE_SECRET } from '../api/appConst'

const mode = import.meta.env.MODE

const setUser = (selfObj: any, userData: any) => {
  selfObj.userId = userData.EmpNo
  selfObj.userName = userData.CName
  selfObj.deptNo = userData.DeptNo
  selfObj.deptSName = userData.DeptSName
}

export const useAuthUserStore = defineStore('authUser', {
  state: () => ({
    logonUserId: '',
    logonUserName: '',
    userId: '',
    userName: '',
    deptNo: 0,
    deptSName: ''
  }),
  actions: {
    /**
     * 取得身分
     * @param empNo 員工編號
     */
    async setLogonUser(empNo: string): Promise<void> {
      let checkLogonData = false
      let employeeDetailData
      let adminData = false
      try {
        const logonPromiseData: any = await Promise.all([
          fetch(GET_EMPLOYEE_URL + '/' + empNo, {
            method: 'GET',
            signal: AbortSignal.timeout(FETCH_TIMEOUT_TIME)
          }).then((res: Response) => {
            if (!res.ok) {
              throw new Error(res.status.toString())
            }
            return res.json()
          }).catch((err: Error): void => {
            throw err
          }),
          fetch(GET_ISADMIN_URL, {
            method: 'GET',
            signal: AbortSignal.timeout(FETCH_TIMEOUT_TIME)
          }).then((res: Response) => {
            if (!res.ok) {
              throw new Error(res.status.toString())
            }
            return res.json()
          }).catch((err: Error): void => {
            throw err
          })
        ]).catch((err: Error): void => {
          throw err
        })
        employeeDetailData = logonPromiseData[0]
        adminData = logonPromiseData[1]

        this.logonUserId = logonPromiseData[0].EmpNo.toString()
        this.logonUserName = logonPromiseData[0].CName
        checkLogonData = true
      } catch (err: unknown) {
        throw err
      }

      if (checkLogonData === true) {
        // 內網才開放切換身分，外網則不開放
        if (mode.split('.')[1] === 'intranet') {
          const storageUserid = sessionStorage.getItem('id')
          if ((adminData === true) && (storageUserid !== null)) {
            try {
              const storageEmpNo = CryptoJS.AES.decrypt(storageUserid, SESSION_STORAGE_SECRET).toString(CryptoJS.enc.Utf8)
              const employeeData: any = await fetch(GET_EMPLOYEE_URL + '/' + storageEmpNo, {
                method: 'GET',
                signal: AbortSignal.timeout(FETCH_TIMEOUT_TIME)
              }).then((res: Response) => {
                if (!res.ok) {
                  throw new Error(res.status.toString())
                }
                return res.json()
              }).catch((err: Error): void => {
                throw err
              })

              setUser(this, employeeData)
            } catch (err: unknown) {
              throw err
            }
          } else {
            sessionStorage.removeItem('id')
            setUser(this, employeeDetailData)
          }
        } else {
          setUser(this, employeeDetailData)
        }
      }
    },
    /**
     * 管理者切換身分
     * @param empNo 員工編號
     */
    async setEmp(empNo: string): Promise<void> {
      const isAdmin: Response = await fetch(GET_ISADMIN_URL, {
        method: 'GET'
      })
      const isAdminJson = await isAdmin.json()

      if (isAdminJson === true) {
        sessionStorage.setItem('id', CryptoJS.AES.encrypt(empNo, SESSION_STORAGE_SECRET).toString())
      }
    }
  }
})
