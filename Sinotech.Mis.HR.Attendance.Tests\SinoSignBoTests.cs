﻿using Sinotech.Mis.HR.Attendance.BusinessLogic;
using Sinotech.Mis.HR.Attendance.Common;
using System.Collections.Generic;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.Tests
{
    public class SinoSignBoTests
    {
        private readonly SinoSignBo _sinoSignBo;

        public SinoSignBoTests(SinoSignBo sinoSignBo)
        {
            _sinoSignBo = sinoSignBo;
        }

        [Fact]
        public void GetRoleUsersTest()
        {
            List<string> users = _sinoSignBo.GetRoleUsers("S01");
            Assert.NotNull(users);
            Assert.NotEmpty(users);
        }

        [Fact]
        public void GetUserRolesTest()
        {
            List<Role> roles = _sinoSignBo.GetUserRoles("2016");
            Assert.NotNull(roles);
            Assert.NotEmpty(roles);
        }

        [Theory]
        [InlineData("S01", "董事長")]
        [InlineData("S02", "執行長")]
        [InlineData("S03", "副執行長")]
        public void GetRoleNameTest(string roleId, string expected)
        {
            string roleName = _sinoSignBo.GetRoleName(roleId);
            Assert.Equal(expected, roleName);
            // 第二次必須由Cache取得
            roleName = _sinoSignBo.GetRoleName(roleId);
            Assert.Equal(expected, roleName);
        }
    }
}