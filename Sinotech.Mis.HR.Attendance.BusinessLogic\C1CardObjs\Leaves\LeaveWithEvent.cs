﻿using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.DataAccess.Ado;
using System;
using System.Collections.Generic;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves;

/// <summary>
/// 具有事件性質的假別
/// </summary>
/// <seealso cref="C1CardBase" />
public abstract class LeaveWithEvent : C1CardBase
{
    //private readonly C1Card _c1Card;
    //private readonly IC1CardBo _c1CardBo;
    /// <summary>
    /// Initializes a new instance of the <see cref="LeaveWithEvent"/> class.
    /// </summary>
    /// <param name="c1Card">請假物件</param>
    /// <param name="c1CardBo">請假商業物件</param>
    public LeaveWithEvent(C1Card c1Card, IC1CardBo c1CardBo) : base(c1Card, c1CardBo)
    {
        //_c1Card = c1Card;
        //_c1CardBo = c1CardBo;
        if (c1Card.EventDate != null && c1Card.EventDate != DateTime.MinValue && c1Card.EventDate != DateTime.MaxValue)
        {
            if (!string.IsNullOrWhiteSpace(c1Card.RelatedFormNumber))
            {
                List<C1CardDto> c1CardDtos = c1CardBo.GetRelatedCards(c1Card.RelatedFormNumber);
                if (c1CardDtos.Count > 0)
                {
                    C1CardDto c1CardDto = c1CardDtos[0];
                    if (c1CardDto.C1_DeadlineStartDate != null)
                    {
                        _c1Card.ExpirationStartDate = (DateTime)c1CardDto.C1_DeadlineStartDate;
                    }
                    if (c1CardDto.C1_DeadlineEndDate != null)
                    {
                        _c1Card.ExpirationEndDate = (DateTime)c1CardDto.C1_DeadlineEndDate;
                    }
                    else
                    {
                        (_c1Card.ExpirationStartDate, _c1Card.ExpirationEndDate) = CalculateLeavePermittedPeriod((DateTime)c1Card.EventDate, c1Card.EmpNo);
                    }
                }
            }
            else
            {
                (_c1Card.ExpirationStartDate, _c1Card.ExpirationEndDate) = CalculateLeavePermittedPeriod((DateTime)c1Card.EventDate, c1Card.EmpNo);
            }
        }
        if (c1Card.LeaveMaximum != null)
        {
            switch (c1Card.LeaveUnit)
            {
                case "D":
                    PermittedHours = (int)c1Card.LeaveMaximum * AttendanceParameters.DefaultWorkingHours;
                    PermittedDays = (int)c1Card.LeaveMaximum;
                    break;
                default:
                    PermittedHours = (int)c1Card.LeaveMaximum;
                    PermittedDays = (int)c1Card.LeaveMaximum / AttendanceParameters.DefaultWorkingHours;
                    break;
            }
        }
    }

    /// <summary>
    /// 檢查請假期限 
    /// </summary>
    /// <returns></returns>
    internal virtual CardCheckResult CheckLeaveRange()
    {
        CardCheckResult result = ResultOk;

        if (_c1Card.EventDate != null && _c1Card.EventDate != DateTime.MinValue && _c1Card.EventDate != DateTime.MaxValue)
        {
            // 請假期限判斷
            if (_c1Card.StartDate.Date < _c1Card.ExpirationStartDate.Date || _c1Card.EndDate.Date > _c1Card.ExpirationEndDate.Date)
            {
                result = ResultOverLeaveRange;
            }
        }
        else
        {
            result = ResultEventDateFieldRequired;
        }
        return result;
    }

    /// <summary>
    /// 檢查是否超假
    /// </summary>
    /// <returns></returns>
    internal override CardCheckResult CheckOverPermittedLeaveHours()
    {
        // 請假剩餘時數檢查
        CardCheckResult result = CheckRemainHours();
        if (result.Status != CardStatusEnum.Success)
        {
            return result;
        }
        // 請假時數期限檢查
        result = CheckLeaveRange();
        if (result.Status != CardStatusEnum.Success)
        {
            return result;
        }

        return result;
    }

    /// <summary>
    /// 檢查剩餘可休  
    /// </summary>
    /// <returns></returns>
    internal abstract CardCheckResult CheckRemainHours();

    /// <summary>
    /// 檢查必要欄位
    /// </summary>
    /// <returns></returns>
    internal override CardCheckResult CheckRequiredFields()
    {
        CardCheckResult result = base.CheckRequiredFields();

        // 通過基本欄位檢查就繼續檢查
        if (result.Status == CardStatusEnum.Success)
        {
            // 未輸入【事件發生日】
            if (_c1Card.EventDate == null || _c1Card.EventDate == DateTime.MinValue || _c1Card.EventDate == DateTime.MaxValue)
            {
                result = ResultEventDateFieldRequired;
            }
        }

        return result;
    }

    /// <summary>
    /// 產生超假錯誤檢查結果，以日為單位
    /// </summary>
    /// <param name="remainAvailableDays">剩餘可休天數</param>
    /// <param name="code">錯誤代碼</param>
    /// <param name="message">錯誤訊息</param>
    /// <returns></returns>
    internal CardCheckResult GenerateOverLeaveDaysError(double remainAvailableDays, int code, string message)
    {
        double days = TotalHours * 1.0 / AttendanceParameters.DefaultWorkingHours;
        message = message.Replace("{X}", $"{days}").Replace("{Y}", $"{remainAvailableDays}");
        CardCheckResult result = new CardCheckResult(code, CardStatusEnum.Error, message);
        return result;
    }

    /// <summary>
    /// 產生超假錯誤檢查結果，以小時為單位
    /// </summary>
    /// <param name="remainAvailableHours">剩餘可休時數</param>
    /// <param name="code">錯誤代碼</param>
    /// <param name="message">錯誤訊息</param>
    /// <returns></returns>
    internal CardCheckResult GenerateOverLeaveHoursError(int remainAvailableHours, int code, string message)
    {
        message = message.Replace("{X}", $"{TotalHours}").Replace("{Y}", $"{remainAvailableHours}");
        CardCheckResult result = new CardCheckResult(code, CardStatusEnum.Error, message);
        return result;
    }

    /// <summary>
    /// 本假別請假數量上限時數
    /// </summary>
    internal int PermittedHours { get; set; }

    /// <summary>
    /// 本假別請假數量上限天數
    /// </summary>
    internal int PermittedDays { get; set; }

    #region CheckResult

    // 【事件發生日】不可空白
    public const int CodeEventDateFieldRequired = 3000314;
    private readonly CardStatusEnum _statusEventDateFieldRequired = CardStatusEnum.Error;

    private CardCheckResult? _resultEventDateFieldRequired;
    /// <summary>
    /// 【事件發生日】不可空白
    /// </summary>
    internal CardCheckResult ResultEventDateFieldRequired =>
        _resultEventDateFieldRequired ??=
        new CardCheckResult(CodeEventDateFieldRequired, _statusEventDateFieldRequired, AttendanceParameters.LeaveMustHaveEventDate);

    // 超出【請假期限】
    public const int CodeOverLeaveRange = 3000315;

    /// <summary>
    /// 超出【請假期限】
    /// </summary>
    internal CardCheckResult ResultOverLeaveRange =
        new CardCheckResult(CodeOverLeaveRange, CardStatusEnum.Error, AttendanceParameters.OverLeaveRange);
    #endregion


}

