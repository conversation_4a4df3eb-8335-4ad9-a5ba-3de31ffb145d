﻿using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sinotech.Mis.Common;
using Sinotech.Mis.Helpers;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.Utilities.DataAccess.Interfaces;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Runtime.Caching;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic
{
    /// <summary>
    /// 員工 商業物件
    /// </summary>
    public class EmployeeBo : IEmployeeBo
    {
        private static readonly object _lock = new object();
        private static ObjectCache _cache = MemoryCache.Default;
        private readonly IEmployeeDao _employeeDao;

        private readonly ILogger<EmployeeBo> _logger;

        // 統一快取鍵值命名規範
        private const string CACHE_KEY_FORMAT_ALL_EMPLOYEES = "AllEmployees-{0}";
        private const int CACHE_EXPIRE_MINUTES = 2; // 可設定為組態值

        /// <summary>
        /// Initializes a new instance of the <see cref="EmployeeBo"/> class.
        /// </summary>
        /// <param name="employeeDao">The employee DAO.</param>
        /// <param name="logger">The logger.</param>
        public EmployeeBo(IEmployeeDao employeeDao, ILogger<EmployeeBo> logger)
        {
            _employeeDao = employeeDao;
            _logger = logger;
        }


        /// <summary>
        /// 安全地從 DataRow 取得字串值
        /// </summary>
        /// <param name="dataRow">資料列</param>
        /// <param name="columnName">欄位名稱</param>
        /// <returns>字串值，如果為 null 或空白則返回 null</returns>
        private static string? GetStringValueOrNull(DataRow dataRow, string columnName)
        {
            if (dataRow[columnName] == DBNull.Value) return null;
            
            string value = (string)dataRow[columnName];
            return string.IsNullOrWhiteSpace(value) ? null : value;
        }

        /// <summary>
        /// 安全地從 DataRow 取得字串值，空值時返回空字串
        /// </summary>
        /// <param name="dataRow">資料列</param>
        /// <param name="columnName">欄位名稱</param>
        /// <returns>字串值，如果為 null 或空白則返回空字串</returns>
        private static string GetStringValueOrEmpty(DataRow dataRow, string columnName)
        {
            if (dataRow[columnName] == DBNull.Value) return string.Empty;
            
            string value = (string)dataRow[columnName];
            return string.IsNullOrWhiteSpace(value) ? string.Empty : value;
        }

        /// <summary>
        /// 安全地從 DataRow 取得可空的整數值
        /// </summary>
        /// <param name="dataRow">資料列</param>
        /// <param name="columnName">欄位名稱</param>
        /// <returns>整數值或 null</returns>
        private static int? GetNullableIntValue(DataRow dataRow, string columnName)
        {
            return dataRow[columnName] == DBNull.Value ? null : (int)dataRow[columnName];
        }

        /// <summary>
        /// 安全地從 DataRow 取得可空的日期值
        /// </summary>
        /// <param name="dataRow">資料列</param>
        /// <param name="columnName">欄位名稱</param>
        /// <returns>日期值或 null</returns>
        private static DateTime? GetNullableDateTimeValue(DataRow dataRow, string columnName)
        {
            return dataRow[columnName] == DBNull.Value ? null : (DateTime)dataRow[columnName];
        }

        /// <summary>
        /// 設置員工基本資訊
        /// </summary>
        /// <param name="employee">員工物件</param>
        /// <param name="dataRow">資料列</param>
        private static void SetEmployeeBasicInfo(Employee employee, DataRow dataRow)
        {
            employee.CName = (string)dataRow["CName"];
            employee.EmpNo = (string)dataRow["EmpNo"];
            employee.DeptNo = (int)dataRow["DeptNo"];
            employee.DeptSName = (string)dataRow["DeptSName"];
            employee.Sex = (string)dataRow["Sex"];
        }

        /// <summary>
        /// 設置員工職位資訊
        /// </summary>
        /// <param name="employee">員工物件</param>
        /// <param name="dataRow">資料列</param>
        private static void SetEmployeeJobInfo(Employee employee, DataRow dataRow)
        {
            employee.JobNo = GetStringValueOrNull(dataRow, "JobNo");
            employee.JobName = GetStringValueOrNull(dataRow, "JobName");
            employee.RankNo = GetStringValueOrEmpty(dataRow, "RankNo");
            employee.RankName = GetStringValueOrEmpty(dataRow, "RankName");
        }

        /// <summary>
        /// 設置員工個人資訊
        /// </summary>
        /// <param name="employee">員工物件</param>
        /// <param name="dataRow">資料列</param>
        private static void SetEmployeePersonalInfo(Employee employee, DataRow dataRow)
        {
            employee.Birthday = GetNullableDateTimeValue(dataRow, "Birthday");
        }

        /// <summary>
        /// 設置員工聯絡資訊
        /// </summary>
        /// <param name="employee">員工物件</param>
        /// <param name="dataRow">資料列</param>
        private static void SetEmployeeContactInfo(Employee employee, DataRow dataRow)
        {
            employee.Email = GetStringValueOrNull(dataRow, "Email");
            employee.TelExtension = GetStringValueOrNull(dataRow, "TelExtension");
            employee.SpecLine = GetStringValueOrNull(dataRow, "SpecLine");
        }

        /// <summary>
        /// 設置員工團隊資訊
        /// </summary>
        /// <param name="employee">員工物件</param>
        /// <param name="dataRow">資料列</param>
        private static void SetEmployeeTeamInfo(Employee employee, DataRow dataRow)
        {
            employee.TeamID = GetNullableIntValue(dataRow, "TeamID");
            employee.TeamCName = GetStringValueOrNull(dataRow, "TeamCName");
        }

        /// <summary>
        /// 設置員工角色標記
        /// </summary>
        /// <param name="employee">員工物件</param>
        /// <param name="dataRow">資料列</param>
        private static void SetEmployeeRoleFlags(Employee employee, DataRow dataRow)
        {
            employee.IsTeamLeader = (bool)dataRow["IsTeamLeader"];
            employee.IsAboveDeputyManager = (bool)dataRow["IsAboveDeputyManager"];
            
            if (employee.RankNo != null)
            {
                employee.IsDriver = IsDriver(employee);
            }
        }

        /// <summary>
        /// 設置員工管理職標記
        /// </summary>
        /// <param name="employee">員工物件</param>
        private static void SetEmployeeManagementFlags(Employee employee)
        {
            switch (employee.JobNo)
            {
                case "593":
                case "591":
                case "497":
                case "492":
                case "491":
                    employee.IsManager = true;
                    break;
                case "585":
                case "486":
                case "482":
                case "481":
                    employee.IsDeputyManager = true;
                    break;
                default:
                    // 預設值已在 Employee 類別中設定
                    break;
            }
        }

        /// <summary>
        /// 轉換 DataRow 為 EmployeeDto
        /// </summary>
        /// <param name="dataRow">DataRow</param>
        /// <returns></returns>
        public static Employee DataRowToEmployeeDto(DataRow dataRow)
        {
            Employee employee = new Employee();
            
            // 設置各類資訊
            SetEmployeeBasicInfo(employee, dataRow);
            SetEmployeeJobInfo(employee, dataRow);
            SetEmployeePersonalInfo(employee, dataRow);
            SetEmployeeContactInfo(employee, dataRow);
            SetEmployeeTeamInfo(employee, dataRow);
            SetEmployeeRoleFlags(employee, dataRow);
            SetEmployeeManagementFlags(employee);
            
            return employee;
        }

        /// <summary>
        /// 取得副理以上資料表
        /// </summary>
        /// <returns></returns>
        private DataTable GetAboveDeputyManagerDataTable()
        {
            string cacheKey = "dtAboveDeputyManager";
            if (_cache.Contains(cacheKey))
            {
                return (DataTable)_cache[cacheKey];
            }
            DataTable dt = _employeeDao.GetAboveDeputyManagerDataTable();
            lock (_lock)
            {
                _cache.Set(cacheKey, dt, CachePolicy);
            }
            return dt;
        }

        /// <summary>
        /// 取得所有離職員工
        /// </summary>
        /// <returns>所有離職員工</returns>
        private DataTable GetLeftEmployeeDataTable()
        {
            DataTable dt = new();
            string cacheKey = "LeftEmployees";

            if (_cache.Contains(cacheKey))
            {
                dt = (DataTable)_cache[cacheKey];
            }
            else
            {
                dt = _employeeDao.GetLeftEmployees();
                if (dt != null && !dt.Columns.Contains("IsTeamLeader"))
                {
                    dt.Columns.Add(new DataColumn("IsTeamLeader", typeof(bool)));
                    dt.Columns.Add(new DataColumn("IsAboveDeputyManager", typeof(bool)));

                    foreach (DataRow dr in dt.Rows)
                    {
                        dr["IsTeamLeader"] = false;
                        dr["IsAboveDeputyManager"] = false;
                    }
                }
                lock (_lock)
                {
                    _cache.Set(cacheKey, dt, CachePolicy);
                }
            }
            return dt;
        }

        /// <summary>
        ///  取得組長資料表
        /// </summary>
        /// <returns></returns>
        private DataTable GetTeamLeadersDataTable()
        {
            string cacheName = "dtTeamLeaders";
            if (_cache.Contains(cacheName))
            {
                return (DataTable)_cache[cacheName];
            }
            DataTable dt = _employeeDao.GetTeamLeaders();
            lock (_lock)
            {
                _cache.Set(cacheName, dt, CachePolicy);
            }
            return dt;
        }

        /// <summary>
        /// 是否為司機
        /// </summary>
        /// <param name="employee"></param>
        /// <returns></returns>
        private static bool IsDriver(Employee employee)
        {
            bool isDriver = false;
            if (employee.RankNo == "091")
            {
                isDriver = true;
            }
            return isDriver;
        }

        /// <summary>
        /// 快取策略
        /// </summary>
        private static CacheItemPolicy CachePolicy
        {
            get
            {
                CacheItemPolicy policy = new CacheItemPolicy();
                policy.AbsoluteExpiration = DateTimeOffset.Now.AddMinutes(CACHE_EXPIRE_MINUTES);
                return policy;
            }
        }


        /// <summary>
        /// 修正部門主管的職稱
        /// </summary>
        /// <param name="name">職稱</param>
        /// <returns></returns>
        public static string CorrectJobName(string name)
        {
            if (name.EndsWith("處主管"))
            {
                name = name.Replace("處主管", "處經理");
            }
            if (name.EndsWith("中心主管"))
            {
                name = name.Replace("中心主管", "中心主任");
            }
            if (name.EndsWith("室主管"))
            {
                name = name.Replace("室主管", "室主任");
            }
            return name;
        }

        /// <summary>
        /// 取得所有員工DataTable，包括離職員工
        /// 會將優先部門放在前面
        /// </summary>
        ///<param name="deptNo">優先部門代號，預設 -1 表示不將優先部門置前</param>
        /// <returns></returns>
        public DataTable GetAllEmployeeDataTable(int deptNo = -1)
        {
            string cacheKey = string.Format(CACHE_KEY_FORMAT_ALL_EMPLOYEES, deptNo);

            if (_cache.Contains(cacheKey))
            {
                return (DataTable)_cache[cacheKey];
            }

            DataTable dt = _employeeDao.GetAllEmployees();
            if (dt == null || dt.Rows.Count == 0)
            {
                throw new Exception("取得所有員工資料錯誤");
            }

            AddEmployeeFlags(dt);
            DataTable dtResult = PrioritizeAndSortEmployees(dt, deptNo);

            lock (_lock)
            {
                _cache.Set(cacheKey, dtResult, CachePolicy);
            }
            return dtResult;
        }

        private void AddEmployeeFlags(DataTable dt)
        {
            if (!dt.Columns.Contains("IsTeamLeader"))
            {
                dt.Columns.Add(new DataColumn("IsTeamLeader", typeof(bool)));
                dt.Columns.Add(new DataColumn("IsAboveDeputyManager", typeof(bool)));
            }

            DataTable dtTeamLeaders = GetTeamLeadersDataTable();

            foreach (DataRow dr in dt.Rows)
            {
                string empNo = dr.Field<string>("EmpNo") ?? string.Empty;
                dr["IsTeamLeader"] = dtTeamLeaders.Select($"EmpNo='{empNo}'").Length == 1;
                dr["IsAboveDeputyManager"] = IsAboveDeputyManager(empNo);
            }
        }

        private static DataTable PrioritizeAndSortEmployees(DataTable dt, int deptNo)
        {
            var orderedRows = dt.AsEnumerable()
                .OrderByDescending(r => r.Field<int>("DeptNo") == deptNo)
                .ThenBy(r => r.Field<string>("Status") == "0" ? 0 : 1)
                .ThenBy(r => r.Field<string>("EmpNo"));

            DataTable dtResult = dt.Clone();
            foreach (DataRow row in orderedRows)
            {
                dtResult.ImportRow(row);
            }

            return dtResult;
        }

        /// <summary>
        /// 取得所有員工 JSON String，包括離職員工
        /// 會將優先部門放在前面
        /// </summary>
        ///<param name="deptNo">優先部門代號</param>
        /// <returns>所有現職員工</returns>
        public string GetAllEmployeesJson(int deptNo)
        {
            DataTable dt = GetAllEmployeeDataTable(deptNo);
            List<Employee> list = Employee.DataTableToList(dt);
            List<EmployeeSimple> employeeList = EmployeeSimple.EmployeeToEmployeeSimple(list);
            return JsonConvert.SerializeObject(employeeList);
        }

        /// <summary>取得部門名稱</summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns></returns>
        public string GetDepartmentName(string employeeNumber)
        {
            //DataTable dt = GetEmployeeDataTable();
            //DataRow[] drs = dt.Select($"EmpNo='{employeeNumber}'");
            //string deptName = (string)drs[0]["DeptName"];
            //return deptName;
            // 目前沒有 DeptName 欄位，所以改用  GetDepartmentShortName
            return GetDepartmentShortName(employeeNumber);
        }

        /// <summary>
        /// 取得員工所屬部門編號
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns></returns>
        public int GetDepartmentNumber(string employeeNumber)
        {
            DataTable dt = GetEmployeeDataTable();
            DataRow[] drs = dt.Select($"EmpNo='{employeeNumber}'");
            int deptNo = 0;
            if (drs.Length > 0)
            {
                deptNo = (int)drs[0]["DeptNo"];
            }
            return deptNo;
        }

        /// <summary>取得所有部門</summary>
        /// <returns>
        ///  部門DataTable
        /// </returns>
        public DataTable GetDepartmentsDataTable()
        {
            DataTable dt;
            string cacheName = "dtDepartments";
            if (_cache.Contains(cacheName))
            {
                dt = (DataTable)_cache[cacheName];
            }
            else
            {
                dt = _employeeDao.GetDepartments();
                _cache.Set(cacheName, dt, CachePolicy);
            }
            return dt;
        }

        /// <summary>取得部門簡稱</summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public string GetDepartmentShortName(string employeeNumber)
        {
            string deptSName = string.Empty;
            DataTable dt = GetEmployeeDataTable();
            DataRow[] drs = dt.Select($"EmpNo='{employeeNumber}'");
            if (drs.Length > 0)
            {
                deptSName = (string)drs[0]["DeptSName"];
            }
            return deptSName;
        }

        /// <summary>
        /// 取得所有在職員工DataTable
        /// 會將優先部門放在前面
        /// </summary>
        ///<param name="deptNo">優先部門代號，預設 -1 表示不將優先部門置前</param>
        /// <returns></returns>
        public DataTable GetEmployeeDataTable(int deptNo = -1)
        {
            DataTable dtResult = new DataTable();
            string cacheName = $"GetEmployeeDataTable-{deptNo}-x";
            if (_cache.Contains(cacheName))
            {
                dtResult = (DataTable)_cache[cacheName];
            }
            else
            {
                DataTable dt = _employeeDao.GetEmployees();
                dt.Columns.Add(new DataColumn("IsTeamLeader", typeof(bool)));
                dt.Columns.Add(new DataColumn("IsAboveDeputyManager", typeof(bool)));

                DataTable dtTeamLeaders = GetTeamLeadersDataTable();
                foreach (DataRow dr in dt.Rows)
                {
                    if (!dr.IsNull("EmpNo"))
                    {
                        string? empNo = dr.Field<string>("EmpNo");
                        if (empNo != null)
                        {
                            dr["IsTeamLeader"] = 0;
                            DataRow[] rows = dtTeamLeaders.Select($"EmpNo={empNo}");
                            if (rows.Length == 1)
                            {
                                dr["IsTeamLeader"] = 1;
                            }

                            dr["IsAboveDeputyManager"] = IsAboveDeputyManager(empNo);
                        }
                    }
                }
                if (dt.Rows.Count > 0)
                {
                    dt.DefaultView.Sort = "EmpNo";
                    dtResult = dt.Clone();
                    var enumerable = dt.DefaultView.ToTable().AsEnumerable();
                    var enumDept = enumerable.Where(r => r.Field<int>("DeptNo") == deptNo);
                    if (enumDept != null && enumDept.Count() > 0)
                    {
                        DataTable dtDept = enumDept.CopyToDataTable();
                        foreach (DataRow row in dtDept.Rows)
                        {
                            DataRow dr = dtResult.NewRow();
                            dr.ItemArray = row.ItemArray;
                            dtResult.Rows.Add(dr);
                        }
                    }

                    DataTable dtOther = enumerable.Where(r => r.Field<int>("DeptNo") != deptNo).CopyToDataTable();

                    if (dtOther != null)
                    {
                        foreach (DataRow row in dtOther.Rows)
                        {
                            DataRow dr = dtResult.NewRow();
                            dr.ItemArray = row.ItemArray;
                            dtResult.Rows.Add(dr);
                        }
                    }

                    lock (_lock)
                    {
                        _cache.Set(cacheName, dtResult, CachePolicy);
                    }
                }
            }

            return dtResult;
        }

        /// <summary>
        /// 取得員工詳細資料
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns></returns>
        public Employee? GetEmployeeDetail(string employeeNumber)
        {
            if (string.IsNullOrWhiteSpace(employeeNumber)) 
            {
                _logger.LogWarning("GetEmployeeDetail: 員工編號為空");
                return null;
            }

            try
            {
                Employee? ret = null;
                DataTable dt = GetEmployeeDataTable();
                DataRow? dataRow = GetEmployeeDataRowFromDataTable(employeeNumber, dt);

                if (dataRow == null) // 從離職員工裡找
                {
                    dt = GetLeftEmployeeDataTable();
                    dataRow = GetEmployeeDataRowFromDataTable(employeeNumber, dt);
                }

                if (dataRow == null) // 找不到該員工
                {
                    _logger.LogDebug("找不到員工資料，員工編號：{EmployeeNumber}", employeeNumber);
                    return null;
                }

                dataRow = GetEmployeeTeamData(dt, dataRow); // 補上 IsTeamLeader, IsAboveDeputyManager 欄位
                ret = DataRowToEmployeeDto(dataRow); // 轉換成 Employee 物件
                return ret;
            }
            catch (Exception ex)
            {
                _logger.LogError("取得員工詳細資料時發生錯誤，員工編號：{EmployeeNumber}，錯誤訊息：{Message}，堆疊追蹤：{StackTrace}", 
                    employeeNumber, ex.Message, ex.StackTrace);
                return null;
            }
        }

        private DataRow GetEmployeeTeamData(DataTable dt, DataRow? dataRow)
        {
            if (dt.Rows.Count > 0 && !dt.Columns.Contains("IsTeamLeader"))
            {
                dt.Columns.Add(new DataColumn("IsTeamLeader", typeof(bool)));
                dt.Columns.Add(new DataColumn("IsAboveDeputyManager", typeof(bool)));

                DataTable dtTeamLeaders = GetTeamLeadersDataTable();

                if (!dataRow.IsNull("EmpNo"))
                {
                    string? empNo = dataRow.Field<string>("EmpNo");
                    if (empNo != null)
                    {
                        dataRow["IsTeamLeader"] = 0;
                        DataRow[] rows = dtTeamLeaders.Select($"EmpNo={empNo}");
                        if (rows.Length == 1)
                        {
                            dataRow["IsTeamLeader"] = 1;
                        }

                        dataRow["IsAboveDeputyManager"] = IsAboveDeputyManager(empNo);
                    }
                }
            }
            return dataRow;
        }

        /// <summary>
        /// 取得員工資料行
        /// </summary>
        /// <param name="employeeNumber"></param>
        /// <param name="dt"></param>
        /// <returns></returns>
        private static DataRow? GetEmployeeDataRowFromDataTable(string employeeNumber, DataTable dt)
        {
            DataRow? dataRow = null;
            if (dt != null && dt.Rows.Count > 0)
            {
                DataRow[] drs = dt.Select($"EmpNo='{employeeNumber}'");
                if (drs.Length > 0)
                {
                    dataRow = drs[0];
                }
            }
            return dataRow;
        }

        /// <summary>
        /// 取得員工Email
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns></returns>
        public string GetEmployeeEmail(string employeeNumber)
        {
            string email = string.Empty;
            DataTable dt = GetEmployeeDataTable();
            DataRow[] drs = dt.Select($"EmpNo='{employeeNumber}'");
            if (drs.Length > 0)
            {
                email = (string)drs[0]["Email"];
            }
            else //從離職員工裡找
            {
                dt = GetLeftEmployeeDataTable();
                DataRow[] drsLeft = dt.Select($"EmpNo='{employeeNumber}'");
                if (drsLeft.Length > 0)
                {
                    email = (string)drsLeft[0]["Email"];
                }
            }
            return email;
        }

        /// <summary>取得員工基本資料</summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public string GetEmployeeSimpleJson(string employeeNumber)
        {
            string ret = "{}";
            Employee? employee = GetEmployeeDetail(employeeNumber);
            if (employee != null)
            {
                EmployeeSimple employeeSimple = EmployeeSimple.EmployeeToEmployeeSimple(employee);
                ret = JsonConvert.SerializeObject(employeeSimple);
            }
            return ret;
        }

        /// <summary>取得員工精簡資料</summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public string GetEmployeeTinyJson(string employeeNumber)
        {
            string ret = "{}";
            Employee? employee = GetEmployeeDetail(employeeNumber);
            if (employee != null)
            {
                EmployeeTiny employeeTiny = EmployeeTiny.EmployeeToEmployeeTiny(employee);
                ret = JsonConvert.SerializeObject(employeeTiny);
            }
            return ret;
        }

        /// <summary>
        /// 取得員工姓名
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns></returns>
        public string GetEmployeeName(string employeeNumber)
        {
            string cName = string.Empty;//"不明員工";
            DataTable dt = GetEmployeeDataTable();
            if (dt.Rows.Count > 0)
            {
                DataRow[] drs = dt.Select($"EmpNo='{employeeNumber}'");
                if (drs.Length > 0)
                {
                    cName = (string)drs[0]["CName"];
                }
                else //從離職員工裡找
                {
                    dt = GetLeftEmployeeDataTable();
                    DataRow[] drsLeft = dt.Select($"EmpNo='{employeeNumber}'");
                    if (drsLeft.Length > 0)
                    {
                        cName = $"{(string)drsLeft[0]["CName"]}(非在職)";
                    }
                }
            }
            return cName;
        }

        /// <summary>
        /// 取得所有現職員工 JSON String
        /// 會將優先部門放在前面
        /// </summary>
        ///<param name="deptNo">優先部門代號，預設 -1 表示不將優先部門置前</param>
        /// <returns>所有現職員工</returns>
        public string GetEmployeesSimpleJson(int deptNo)
        {
            DataTable dt = GetEmployeeDataTable(deptNo);
            List<Employee> list = Employee.DataTableToList(dt);
            List<EmployeeSimple> employeeSimpleList = EmployeeSimple.EmployeeToEmployeeSimple(list);
            return JsonConvert.SerializeObject(employeeSimpleList);
        }

        /// <summary>
        /// 取得所有現職員工 JSON String
        /// </summary>
        /// <param name="sortField">排序欄位</param>
        /// <param name="ascDesc">升冪或降冪 ASC or DESC</param>
        /// <returns>所有現職員工</returns>
        public string GetEmployeesJson(string sortField = "EmpNo", string ascDesc = "ASC")
        {
            DataTable dt = GetEmployeeDataTable();
            dt.DefaultView.Sort = $"{sortField} {ascDesc}";
            dt = dt.DefaultView.ToTable();
            List<Employee> list = Employee.DataTableToList(dt);
            List<EmployeeView> employeeView = EmployeeView.EmployeeToEmployeeView(list);
            return JsonConvert.SerializeObject(employeeView);
        }

        /// <summary>
        /// Gets the team leaders.
        /// </summary>
        /// <returns></returns>
        public List<TeamLeader> GetTeamLeaders()
        {
            string cacheName = "TeamLeaders";
            if (_cache.Contains(cacheName))
            {
                return (List<TeamLeader>)_cache[cacheName];
            }

            DataTable dt = GetTeamLeadersDataTable();
            List<TeamLeader> list = SqlHelper.ConvertDataTable<TeamLeader>(dt);
            _cache.Set(cacheName, list, CachePolicy);
            return list;
        }

        /// <summary>
        /// 是否為副理以上
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>副理以上傳回<c>true</c>，否則傳回<c>false</c></returns>
        public bool IsAboveDeputyManager(string empNo)
        {
            bool isAboveDeputyManager = false;
            DataTable dt = GetAboveDeputyManagerDataTable();
            DataRow[] drs = dt.Select($"EmpNo='{empNo}'");
            if (drs.Length > 0) { isAboveDeputyManager = true; }
            return isAboveDeputyManager;
        }

        /// <summary>
        /// 是否為副理以上
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="deptNo">部門代號</param>
        /// <returns>副理以上傳回<c>true</c>，否則傳回<c>false</c></returns>
        public bool IsAboveDeputyManager(string empNo, int deptNo)
        {
            bool isAboveDeputyManager = false;
            DataTable dt = GetAboveDeputyManagerDataTable();
            DataRow[] drs = dt.Select($"EmpNo='{empNo}' AND DeptNo={deptNo}");
            if (drs.Length > 0) { isAboveDeputyManager = true; }
            return isAboveDeputyManager;
        }

        /// <summary>
        /// 取得組長所屬的團隊編號
        /// </summary>
        /// <param name="empNo"></param>
        /// <returns></returns>
        public List<int> GetTeamLeaderTeamNos(string empNo)
        {
            List<int> teamNos = new List<int>();
            DataTable dt = GetTeamLeadersDataTable();
            DataRow[] drs = dt.Select($"EmpNo='{empNo}'");
            foreach (DataRow dr in drs)
            {
                if (!dr.IsNull("TeamID"))
                {
                    int teamId = (int)dr["TeamID"];
                    if (!teamNos.Contains(teamId))
                    {
                        teamNos.Add(teamId);
                    }
                }
            }
            return teamNos;
        }

        /// <summary>
        /// 副理以上主管部門編號
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>副理以上主管部門編號列表</returns>
        public List<int> GetAboveDeputyManagerDeptNos(string empNo)
        {
            List<int> deptNos = new List<int>();
            DataTable dt = GetAboveDeputyManagerDataTable();
            DataRow[] drs = dt.Select($"EmpNo='{empNo}'");
            foreach(DataRow dr in drs)
            {
                if (!dr.IsNull("DeptNo"))
                {
                    int deptNo = (int)dr["DeptNo"];
                    if (!deptNos.Contains(deptNo))
                    {
                        deptNos.Add(deptNo);
                    }
                }
            }
            return deptNos;
        }

        /// <summary>
        /// 員工是否為司機
        /// </summary>
        /// <param name="empNo"></param>
        /// <returns></returns>
        public bool IsDriver(string empNo)
        {
            bool result = false;
            Employee? employee = GetEmployeeDetail(empNo);
            if (employee != null)
            {
                result = IsDriver(employee);
            }
            return result;
        }

        /// <summary>
        /// 是否為現職員工
        /// </summary>
        /// <param name="empNo">The emp no.</param>
        /// <returns>
        ///   <c>true</c> 是現職員工，否則傳回 <c>false</c>.
        /// </returns>
        public bool IsEmployee(string empNo)
        {
            DataTable dt = GetEmployeeDataTable();
            DataRow[] drs = dt.Select($"EmpNo='{empNo}'");
            bool ret = drs.Length > 0;
            return ret;
        }

        /// <summary>
        /// 員工是否為組長
        /// </summary>
        /// <param name="empNo"></param>
        /// <returns></returns>
        public bool IsTeamLeader(string empNo)
        {
            bool isTeamLeader = false;
            DataTable dtTeamLeaders = GetTeamLeadersDataTable();
            DataRow[] rows = dtTeamLeaders.Select($"EmpNo={empNo}");
            if (rows.Length == 1)
            {
                isTeamLeader = true;
            }
            return isTeamLeader;
        }

        /// <summary>
        /// 是否為該員工的組長
        /// </summary>
        /// <param name="leaderId">組長員工編號</param>
        /// <param name="userId">員工編號</param>
        /// <returns></returns>
        public bool IsTeamLeaderOf(string leaderId, string userId)
        {
            bool result = false;
            Employee? leader = GetEmployeeDetail(leaderId);
            if (leader != null && leader.IsTeamLeader)
            {
                Employee? employee = GetEmployeeDetail(userId);
                List<int> teamNos = GetTeamLeaderTeamNos(leaderId);
                if (employee != null && employee.TeamID != null && teamNos.Contains((int)employee.TeamID))
                {
                    result = true;
                }
            }
            return result;
        }
    }
}
