using FakeItEasy;
using Xunit;
using Sinotech.Mis.Common;
using Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.PositionType;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.Tests
{
    [ExcludeFromCodeCoverage]
    public class TestB1CardCheckProject : TestB1CardBase
    {
        [Fact]
        public void ProjectNotFound()
        {
            var provider = GetFakeObject(B1CardPositionEnum.GeneralStaff);
            var p = B1CardPositionFactory.GetB1CardPositionObject(provider);

            List<Project> projectList = new List<Project>()
            {
                new Project()
                {
                    PrjNo = string.Empty,
                    BDate = _now,
                    EDate = _now,
                }
            };

            A.<PERSON>To(() => provider.GetProjectList())
                .Returns(projectList);

            _b1Card.Details.Clear();
            _b1Card.Details.Add(
                new B1CardDetail()
                {
                    B1_CODE = 1,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now,
                    EndTime = _now + TimeSpan.FromHours(2),
                    Hour = 2,
                    Project = _project1,
                }
            );

            var result = p.CheckProject();
            Assert.Equal(531, result.Code);
        }

        [Theory]
        [InlineData(-1, 0)]
        [InlineData(-2, 0)]
        [InlineData(-3, 0)]
        [InlineData(-6, 0)]
        [InlineData(-10, 0)]
        [InlineData(-11, 532)]
        [InlineData(-12, 532)]
        [InlineData(0, 0)]
        [InlineData(1, 0)]
        [InlineData(2, 0)]
        [InlineData(5, 0)]
        [InlineData(6, 0)]
        [InlineData(7, 0)]
        [InlineData(8, 0)]
        [InlineData(9, 0)]
        [InlineData(10, 532)]
        public void ValidProjectInterval(int offset, int code)
        {
            var provider = GetFakeObject(B1CardPositionEnum.GeneralStaff);
            var p = B1CardPositionFactory.GetB1CardPositionObject(provider);

            List<Project> projectList = new List<Project>()
            {
                new Project()
                {
                    PrjNo = _project1,
                    BDate = _now - TimeSpan.FromDays(10),
                    EDate = _now + TimeSpan.FromDays(10),
                }
            };

            A.CallTo(() => provider.GetProjectList())
                .Returns(projectList);

            _b1Card.Details.Clear();
            _b1Card.Details.Add(
                new B1CardDetail()
                {
                    B1_CODE = 1,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now + TimeSpan.FromDays(offset),
                    EndTime = _now + TimeSpan.FromDays(offset) + TimeSpan.FromHours(3),
                    Hour = 3,
                    Project = _project1,
                }
            );

            var result = p.CheckProject();
            Assert.Equal(code, result.Code);
        }
    }
}