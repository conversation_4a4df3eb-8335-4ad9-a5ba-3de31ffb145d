﻿using FakeItEasy;
using Xunit;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Diagnostics.CodeAnalysis;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves.Tests
{
    [ExcludeFromCodeCoverage]
    public class TestC1CardBase
    {
        protected C1Card _c1Card;
        protected IC1CardBo _c1CardBo;

        public TestC1CardBase()
        {
            #region Prepare Data

            _c1Card = new C1Card
            {
                LeaveNumber = LeaveKindEnum.AnnualLeave,
                EmpNo = "0000",
                Deputy = "0000",
                StartDate = new DateTime(2000, 3, 1),
                EndDate = new DateTime(2000, 3, 3),
            };

            _c1CardBo = A.Fake<IC1CardBo>();
            A.<PERSON>o(() => _c1CardBo.IsEmployee(A<string>.Ignored)).Returns(true);

            #endregion
        }
    }
}