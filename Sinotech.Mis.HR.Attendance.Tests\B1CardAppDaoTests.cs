﻿using Microsoft.Extensions.Configuration;
using Sinotech.Mis.Extensions.Configuration;
using System;
using System.Data;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.DataAccess.Ado.Tests
{
    public class B1CardAppDaoTests
    {
        private readonly B1CardAppDao _b1CardAppDao;
        public B1CardAppDaoTests()
        {
            IConfiguration Configuration = new ConfigurationBuilder().
    AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).
    Build();
            string connectionString = Configuration.GetSecuredConnectionString("Attendance");
            _b1CardAppDao = new B1CardAppDao(connectionString);
        }

        [Theory]
        [InlineData("2022-03-08", "0395", true)]
        [InlineData("2022-03-08", "2268", true)]
        public void GetB1CardAppTest(DateTime date, string userId, bool expected)
        {
            DataTable dt = _b1CardAppDao.GetB1CardApp(date, userId);
            bool actual = dt.Rows.Count == 0;
            Assert.Equal(expected, actual);
        }

        [Theory]
        [InlineData("2022-03-08", "2022-03-10", "RP19553")]
        [InlineData("2022-03-08", "2022-03-10", "9602")]
        public void GetB1CardAppsTest(DateTime startDate, DateTime endDate, string projNo)
        {
            DataTable dt = _b1CardAppDao.GetB1CardApps(startDate, endDate, projNo);
            bool actual = dt.Rows.Count == 0;
            Assert.True(actual);
        }
        [Theory]
        [InlineData("2022-03-08", "2022-03-10")]
        [InlineData("2022-03-10", "2022-03-20")]
        public void GetB1CardAppsFormsTest(DateTime startDate, DateTime endDate)
        {
            DataTable dt = _b1CardAppDao.GetB1CardAppsForms(startDate, endDate);
            bool actual = dt.Rows.Count == 0;
            Assert.True(actual);
        }

        [Fact]
        public void GetSentB1CardAppYearMonthTest()
        {
            DataTable dt = _b1CardAppDao.GetSentB1CardAppYearMonth("0395", 2022, 3, 1);
            bool actual = dt.Rows.Count == 0;
            Assert.True(actual);
        }
    }
}