﻿using Microsoft.Extensions.Configuration;
using Sinotech.Mis.Extensions.Configuration;
using Sinotech.Mis.HR.Attendance.BusinessLogic;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.Tests;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers.Tests
{
    [ExcludeFromCodeCoverage]
    public class DepartmentControllerTests
    {
        private readonly IConfiguration _configuration;
        private readonly DepartmentController _controller;
        public DepartmentControllerTests(IAttendanceBo attendanceBo, IDepartmentBo departmentBo)
        {
            _configuration = new ConfigurationBuilder().
                AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).
                Build();
            _controller = new DepartmentController(attendanceBo, departmentBo, _configuration);

            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo("0349");
            string ConnectionStringAttendance = _configuration.GetSecuredConnectionString("Attendance");
            TestHelper.ClearData(ConnectionStringAttendance);
        }

        [Fact]
        public void GetDepartmentsTest()
        {
            string result = _controller.GetDepartments();
            Assert.NotEmpty(result);
        }

        [Fact]
        public void GetAllowedQueryEmployeesTest()
        {
            string empNo = "0349";
            var result = _controller.GetAllowedQueryEmployees(empNo);
            Assert.NotEmpty(result);
        }

        [Theory]
        [InlineData("", false)]
        [InlineData("0391", true)]
        [InlineData("0741", true)]
        [InlineData("2259", false)]
        [InlineData("2268", false)]
        [InlineData("2273", true)]
        public void GetAllowedQueryDepartmentSentBoxEmployeesTest(string empNo, bool expected)
        {
            var data = _controller.GetAllowedQueryDepartmentSentBoxEmployees(empNo);
            bool result = (data.Count > 0);
            Assert.Equal(expected, result);
        }

        [Theory]
        [InlineData("", false)]
        [InlineData("0391", false)]
        [InlineData("0741", false)]
        [InlineData("2259", false)]
        [InlineData("2268", false)]
        [InlineData("2273", true)]
        public void IsAllowedQueryAllDepartmentSentBoxTest(string empNo, bool expected)
        {
            bool result = _controller.IsAllowedQueryAllDepartmentSentBox(empNo);
            Assert.Equal(expected, result);
        }
    }
}
