﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.Utilities;
using System;

namespace Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers
{
    [Authorize]
    [ApiController]
    [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
    [Route("/api/[controller]/[action]")]
    public class ProjectController : ControllerBase
    {

        private readonly ILogger<ProjectController> _logger;
        private readonly IProjectBo _projectBo;
        private readonly bool _useNegotiate = true;

        /// <summary>
        ///   <see cref="ProjectController" /> 的建構函式</summary>
        /// <param name="projectBo">The project bo.</param>
        /// <param name="configuration"></param>
        /// <param name="logger">The logger.</param>
        public ProjectController(IProjectBo projectBo, IConfiguration configuration, ILogger<ProjectController> logger)
        {
            _projectBo = projectBo;
            _useNegotiate = configuration.GetValue<bool>("UseNegotiate");
            _logger = logger;
        }

        ///<summary>
        /// 查詢指定期間內已成立及未結案計畫。(查詢結束日期(不含)前成立 且 起始日期後結案或未結案 之計畫)
        /// 會將優先部門放在前面
        ///</summary>
        ///<param name="startDate">起始日期</param>
        ///<param name="endDate">結束日期</param>
        ///<param name="deptNo">優先部門代號，預設 -1 表示不將優先部門置前</param>
        ///<returns>JSON String (PrjNo：計畫編號、PrjName：計畫名稱、BDate：成立日期、EDate：結案日期、MainDeptNo：主辦部門代號、DeptSName：主辦部門名稱)</returns>
        [Authorize]
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        //[EnableCors("MyAllowSpecificOrigins")]
        public string GetOpenProjectsDateRange(DateTime startDate, DateTime endDate, int deptNo = -1)
        {
            startDate = startDate.ToLocalTime();
            endDate = endDate.ToLocalTime();
            string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            string ret = "[]";
            if (userId != null)
            {
                ret = _projectBo.GetOpenProjectsDateRange(startDate, endDate, deptNo);
            }
            return ret;
        }

        /// <summary>
        /// 取得計畫名稱
        /// </summary>
        /// <param name="projNo">計畫編號</param>
        /// <returns>計畫名稱</returns>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        //[EnableCors("MyAllowSpecificOrigins")]
        public string GetProjectName(string projNo)
        {
            try
            {
                _logger.LogInformation("Action: {action}, {ProjectNumber}", nameof(GetProjectName), projNo);
                return _projectBo.GetProjectName(projNo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in Action: {action}, {ProjectNumber}", nameof(GetProjectName), projNo);
                throw;
            }
        }

    }
}
