// 1. Include functions first (so you can manipulate colors, SVGs, calc, etc)
@import 'bootstrap/scss/functions';

// 2. Include any default variable overrides here

// 3. Include remainder of required Bootstrap stylesheets (including any separate color mode stylesheets)
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/variables-dark';

// 4. Include any default map overrides here
$a1card: #aceba0;
$b1cardapp: #fefac0;
$b1card: #fefac0;
$c1card: #fec0c0;
$custom-colors: (
  "a1card": $a1card,
  "b1cardapp": $b1cardapp,
  "b1card": $b1card,
  "c1card": $c1card
);
$theme-colors: map-merge($theme-colors, $custom-colors);

// 5. Include remainder of required parts
@import 'bootstrap/scss/maps';
@import 'bootstrap/scss/mixins';
@import 'bootstrap/scss/root';