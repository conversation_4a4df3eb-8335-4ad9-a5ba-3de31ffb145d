<template>
  <div class="container text-center mt-3">
    <div class="row mb-2">
      <div
        :class="[
          'col-12 py-1 px-0',
          toggleChooseUserEx ? 'col-lg-12 col-xl-6' : 'col-lg-6'
        ]"
      >
        <span class="me-1">民國</span>
        <VueSelect
          class="d-inline-block"
          label="rocYear"
          :reduce="(e: any) => e.year"
          :options="getThreeRocYears(now.getFullYear())"
          :clearable="false"
          :searchable="false"
          :modelValue="thisDateYear"
          @update:modelValue="onChangeYear"
          @option:selected="onChangeYearMonth"
        />
        <span class="ms-1 me-4">年</span>

        <VueSelect
          class="d-inline-block"
          label="field"
          :reduce="(e: MonthsOptionsType) => e.optionValue"
          :options="MONTHS_OPTIONS.filter((option: MonthsOptionsType) => option.optionValue !== 0)"
          :clearable="false"
          :searchable="false"
          :modelValue="thisDateMonth"
          @update:modelValue="onChangeMonth"
          @option:selected="onChangeYearMonth"
        />
        <span class="ms-1">月</span>
      </div>

      <div
        v-if="toggleChooseUserEx"
        class="col-12 col-xl-6 px-0"
      >
        <ChooseUserEx
          v-if="userIdQuery.length > 0"
          :modelValue="userIdQuery"
          :employeeData="employeeData"
          :filter="onSearchEmployeeData"
          @change="onChangeUser"
        />
      </div>
    </div>

    <div class="row">
      <div class="col-12 col-lg-6 px-lg-4">
        <div
          class="mb-2"
          :style="{ 'min-height': '25vh' }"
        >
          <p class="h6 my-0 fw-bold py-1">
            <span>刷卡時間表</span>
          </p>
          <FullCalendar
            ref="fullCalendar"
            :options="calendarOptions"
            :style="{ 'cursor': 'default' }"
          />
          <div class="fw-bold text-start text-primary">
            <small>※ 藍色粗體字為公出</small>
          </div>
        </div>

        <div class="mb-2">
          <div class="fw-bold border-bottom border-3 border-secondary">
            <span>待核卡提醒</span>
          </div>
          <div
            v-if="inboxLoaded === false"
            class="d-flex justify-content-center"
          >
            <div>
              <span>讀取中...</span>
              <div
                class="spinner-border ms-auto"
                role="status"
                aria-hidden="true"
              />
            </div>
          </div>
          <template
            v-for="(dataInbox, index) in inbox"
            :key="index"
          >
            <template v-if="index < 10">
              <div class="pb-2">
                <template v-if="userIdQuery === userStore.userId">
                  <router-link
                    :to="{
                      name: dataInbox['formID'] + 'Inbox',
                      params: { formUID: dataInbox['formUID'] }
                    }"
                  >
                    {{ dataInbox['formSubject'] }}
                  </router-link>
                </template>
                <template v-else>
                  {{ dataInbox['formSubject'] }}
                </template>
              </div>
            </template>
          </template>

          <template v-if="inbox.length > 10">
            <router-link
              v-slot="{ navigate }"
              :to="{ name: 'Inbox' }"
              custom
            >
              <button
                type="button"
                :class="(userIdQuery !== userStore.userId) ? 'btn btn-outline-secondary' : 'btn btn-secondary'"
                :disabled="userIdQuery !== userStore.userId"
                @click="navigate"
              >
                <span>更多待核卡資訊</span>
              </button>
            </router-link>
          </template>
        </div>

        <div class="mb-2">
          <div class="fw-bold border-bottom border-3 border-secondary">
            <span>代理人</span>
          </div>
          <div v-if="deputies.length === 0">
            <div>尚未設定</div>
          </div>
          <div v-else>
            <template
              v-for="(deputy, index) in deputies"
              :key="index"
            >
              <div class="pb-2">
                {{ deputy }}
              </div>
            </template>
          </div>

          <button
            v-if="(mode.split('.')[1] === 'intranet') && (userIdQuery === userStore.userId)"
            type="button"
            class="btn btn-secondary"
            @click="onSetDeputy"
          >
            <span>設定</span>
          </button>
        </div>
      </div>

      <div class="col-12 col-lg-6 px-lg-4">
        <div class="mb-2">
          <div class="row">
            <div class="col-6">
              <table
                class="table table-sm table-borderless caption-top mb-1"
                :style="{ 'table-layout': 'fixed' }"
              >
                <caption class="fw-bold text-center text-black border-bottom border-3 border-secondary py-0">
                  <span>特別休息假</span>
                </caption>
                <thead class="d-none">
                  <tr>
                    <th colspan="2" />
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td :style="{ 'width': '60%' }">
                      <span>全年可休</span>
                    </td>
                    <td>{{ leaves.totalAnnualLeaves }}</td>
                  </tr>
                  <tr>
                    <td>
                      <span>本月已休</span>
                    </td>
                    <td>{{ leaves.usedAnnualLeavesMonth }}</td>
                  </tr>
                  <tr>
                    <td>
                      <span>累計已休</span>
                    </td>
                    <td>{{ leaves.usedAnnualLeaves }}</td>
                  </tr>
                  <tr>
                    <td>
                      <span>剩餘可休</span>
                    </td>
                    <td>{{ leaves.remainAnnualLeaves }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="col-6">
              <table
                class="table table-sm table-borderless caption-top mb-1"
                :style="{ 'table-layout': 'fixed' }"
              >
                <caption class="fw-bold text-center text-black border-bottom border-3 border-secondary py-0">
                  <span>延休假</span>
                </caption>
                <thead class="d-none">
                  <tr>
                    <th colspan="2" />
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td :style="{ 'width': '60%' }">
                      <span>全年可休</span>
                    </td>
                    <td>{{ leaves.extendedLeaves }}</td>
                  </tr>
                  <tr>
                    <td>
                      <span>本月已休</span>
                    </td>
                    <td>{{ leaves.usedExtendedLeavesMonth }}</td>
                  </tr>
                  <tr>
                    <td>
                      <span>累計已休</span>
                    </td>
                    <td>{{ leaves.usedExtendedLeaves }}</td>
                  </tr>
                  <tr>
                    <td>
                      <span>剩餘可休</span>
                    </td>
                    <td>{{ leaves.remainedExtendedLeaves }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="col-6">
              <table
                class="table table-sm table-borderless caption-top mb-1"
                :style="{ 'table-layout': 'fixed' }"
              >
                <caption class="fw-bold text-center text-black border-bottom border-3 border-secondary py-0">
                  <span>事假 / 病假</span>
                </caption>
                <thead class="d-none">
                  <tr>
                    <th colspan="2" />
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td :style="{ 'width': '60%' }">
                      <span>本月已休事假</span>
                    </td>
                    <td>{{ leaves.usedPersonalLeavesMonth }}</td>
                  </tr>
                  <tr>
                    <td>
                      <span>累計已休事假</span>
                    </td>
                    <td>{{ leaves.usedPersonalLeaves }}</td>
                  </tr>
                  <tr>
                    <td>
                      <span>本月已休病假</span>
                    </td>
                    <td>{{ leaves.usedSickLeavesMonth }}</td>
                  </tr>
                  <tr>
                    <td>
                      <span>累計已休病假</span>
                    </td>
                    <td>{{ leaves.usedSickLeaves }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="col-6">
              <table
                class="table table-sm table-borderless caption-top mb-1"
                :style="{ 'table-layout': 'fixed' }"
              >
                <caption class="fw-bold text-center text-black border-bottom border-3 border-secondary py-0">
                  <span>補休假</span>
                </caption>
                <thead class="d-none">
                  <tr>
                    <th colspan="2" />
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td :style="{ 'width': '60%' }">
                      <span>本月新增</span>
                    </td>
                    <td>{{ leaves.newRemainCompensatoryLeavesMonth }}</td>
                  </tr>
                  <tr>
                    <td>
                      <span>本月已休</span>
                    </td>
                    <td>{{ leaves.usedCompensatoryLeavesMonth }}</td>
                  </tr>
                  <tr>
                    <td>
                      <span>累計新增</span>
                    </td>
                    <td>{{ leaves.totalNewRemainCompensatoryLeaves }}</td>
                  </tr>
                  <tr>
                    <td>
                      <span>累計已休</span>
                    </td>
                    <td>{{ leaves.totalUsedCompensatoryLeaves }}</td>
                  </tr>
                  <tr>
                    <td>
                      <span>剩餘可休</span>
                    </td>
                    <td>{{ leaves.totalCompensatoryLeaves }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <template v-if="leaves.gender === '女'">
            <table
              class="table table-sm table-borderless caption-top mb-1"
              :style="{ 'table-layout': 'fixed' }"
            >
              <caption class="fw-bold text-center text-black border-bottom border-3 border-secondary py-0">
                <span>生理假</span>
              </caption>
              <thead class="d-none">
                <tr>
                  <th colspan="2" />
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td :style="{ 'width': '60%' }">
                    <span>本月已休</span>
                  </td>
                  <td>{{ leaves.usedMenstruationLeavesMonth }}</td>
                </tr>
                <tr>
                  <td>
                    <span>累計已休</span>
                  </td>
                  <td>{{ leaves.usedMenstruationLeaves }}</td>
                </tr>
              </tbody>
            </table>
          </template>

          <div
            v-if="cardLoaded === false"
            class="d-flex justify-content-center"
          >
            <div>
              <span>讀取中...</span>
              <div
                class="spinner-border ms-auto"
                role="status"
                aria-hidden="true"
              />
            </div>
          </div>

          <div class="fw-bold text-start text-secondary">
            <small>※ 上列為已核可時數</small>
          </div>
        </div>

        <div>
          <div class="table-responsive mb-2">
            <table class="table table-sm caption-top mb-1">
              <caption class="fw-bold text-center text-black border-bottom border-3 border-secondary py-0">
                <span>{{ FORM_ID.A1Card }}</span>
              </caption>
              <thead class="d-none">
                <tr>
                  <th colspan="14" />
                </tr>
              </thead>
              <tbody>
                <template
                  v-for="(form, formCardIndex) in a1Card"
                  :key="formCardIndex"
                >
                  <template v-if="form['card'].length > 0">
                    <tr>
                      <td class="bg-a1card bg-opacity-25">
                        旬別
                      </td>
                      <td class="bg-a1card bg-opacity-25">
                        計畫編號
                      </td>
                      <td class="bg-a1card bg-opacity-25">
                        {{ form['d0'] === true ? form['id'] * 10 + 1 : '-' }}
                      </td>
                      <td class="bg-a1card bg-opacity-25">
                        {{ form['d1'] === true ? form['id'] * 10 + 2 : '-' }}
                      </td>
                      <td class="bg-a1card bg-opacity-25">
                        {{ form['d2'] === true ? form['id'] * 10 + 3 : '-' }}
                      </td>
                      <td class="bg-a1card bg-opacity-25">
                        {{ form['d3'] === true ? form['id'] * 10 + 4 : '-' }}
                      </td>
                      <td class="bg-a1card bg-opacity-25">
                        {{ form['d4'] === true ? form['id'] * 10 + 5 : '-' }}
                      </td>
                      <td class="bg-a1card bg-opacity-25">
                        {{ form['d5'] === true ? form['id'] * 10 + 6 : '-' }}
                      </td>
                      <td class="bg-a1card bg-opacity-25">
                        {{ form['d6'] === true ? form['id'] * 10 + 7 : '-' }}
                      </td>
                      <td class="bg-a1card bg-opacity-25">
                        {{ form['d7'] === true ? form['id'] * 10 + 8 : '-' }}
                      </td>
                      <td class="bg-a1card bg-opacity-25">
                        {{ form['d8'] === true ? form['id'] * 10 + 9 : '-' }}
                      </td>
                      <td class="bg-a1card bg-opacity-25">
                        {{ form['d9'] === true ? form['id'] * 10 + 10 : '-' }}
                      </td>
                      <td class="bg-a1card bg-opacity-25">
                        {{ form['d10'] === true ? form['id'] * 10 + 11 : '-' }}
                      </td>
                      <td class="bg-a1card bg-opacity-25">
                        時數
                      </td>
                    </tr>
                    <template
                      v-for="(card, cardIndex) in form['card']"
                      :key="cardIndex"
                    >
                      <tr :class="{ 'border border-3 border-top-0 border-start-0 border-end-0': (cardIndex === form['card'].length - 1) }">
                        <template v-if="cardIndex === 0">
                          <td
                            :rowspan="form['card'].length"
                            class="align-middle"
                          >
                            <template v-if="form['formUID'] === NIL_UUID">
                              <span>{{ card['applicationType'] }}</span>
                            </template>
                            <template v-else>
                              <router-link
                                :to="{
                                  name: form['formID'] + 'Sent',
                                  params: { formUID: form['formUID'] }
                                }"
                              >
                                <span v-tooltip="{ value: form['formNo'], escape: true }">
                                  {{ card['applicationType'] }}
                                </span>
                              </router-link>
                            </template>
                          </td>
                        </template>
                        <td>{{ card['project'] }}</td>
                        <template
                          v-for="day in 11"
                          :key="day - 1"
                        >
                          <td>{{ card['d' + (day - 1)] ?? '-' }}</td>
                        </template>
                        <td>{{ card['hour'] }}</td>
                      </tr>
                    </template>
                  </template>
                </template>
              </tbody>
            </table>
          </div>
          <div
            v-if="cardLoaded === false"
            class="d-flex justify-content-center"
          >
            <div>
              <span>讀取中...</span>
              <div
                class="spinner-border ms-auto"
                role="status"
                aria-hidden="true"
              />
            </div>
          </div>

          <div class="table-responsive mb-2">
            <table class="table table-sm caption-top mb-1">
              <caption class="fw-bold text-center text-black border-bottom border-3 border-secondary py-0">
                <span>{{ FORM_ID.B1Card }}</span>
              </caption>
              <thead class="d-none">
                <tr>
                  <th colspan="5" />
                </tr>
              </thead>
              <tbody>
                <template v-if="b1Card.length > 0">
                  <tr>
                    <td class="bg-b1card bg-opacity-25">
                      日期
                    </td>
                    <td class="bg-b1card bg-opacity-25">
                      時間
                    </td>
                    <td class="bg-b1card bg-opacity-25">
                      加班別
                    </td>
                    <td class="bg-b1card bg-opacity-25">
                      計畫編號
                    </td>
                    <td class="bg-b1card bg-opacity-25">
                      時數
                    </td>
                  </tr>
                  <template
                    v-for="(form, formCardIndex) in b1Card"
                    :key="formCardIndex"
                  >
                    <template
                      v-for="(card, cardIndex) in form['card']"
                      :key="cardIndex"
                    >
                      <tr :class="{ 'border border-3 border-top-0 border-start-0 border-end-0': ((formCardIndex === b1Card.length - 1) && (cardIndex === form['card'].length - 1)) }">
                        <td
                          v-if="cardIndex === 0"
                          :rowspan="form['card'].length"
                          class="align-middle"
                        >
                          <template v-if="form['formUID'] === NIL_UUID">
                            <span>{{ form['date'] }}</span>
                          </template>
                          <template v-else>
                            <router-link
                              :to="{
                                name: form['formID'] + 'Sent',
                                params: { formUID: form['formUID'] }
                              }"
                            >
                              <span v-tooltip="{ value: form['formNo'], escape: true }">
                                {{ form['date'] }}
                              </span>
                            </router-link>
                          </template>
                        </td>
                        <td>{{ card['startTime'] + ' ~ ' + card['endTime'] }}</td>
                        <td>{{ card['applicationType'] }}</td>
                        <td>{{ card['project'] }}</td>
                        <td>{{ card['hour'] }}</td>
                      </tr>
                    </template>
                  </template>
                </template>
              </tbody>
            </table>
          </div>

          <div class="table-responsive mb-2">
            <table class="table table-sm caption-top mb-1">
              <caption class="fw-bold text-center text-black border-bottom border-3 border-secondary py-0">
                <span>{{ FORM_ID.C1Card }}</span>
              </caption>
              <thead class="d-none">
                <tr>
                  <th colspan="5" />
                </tr>
              </thead>
              <tbody>
                <template v-if="c1Card.length > 0">
                  <tr>
                    <td class="bg-c1card bg-opacity-25">
                      假別
                    </td>
                    <td class="bg-c1card bg-opacity-25">
                      請假日期/時間
                    </td>
                    <td class="bg-c1card bg-opacity-25">
                      天數
                    </td>
                    <td class="bg-c1card bg-opacity-25">
                      時數
                    </td>
                  </tr>
                  <template
                    v-for="(form, formCardIndex) in c1Card"
                    :key="formCardIndex"
                  >
                    <template
                      v-for="(card, cardIndex) in form['card']"
                      :key="cardIndex"
                    >
                      <tr :class="'border border-3 border-top-0 border-start-0 border-end-0'">
                        <td
                          :rowspan="form['card'].length"
                          class="align-middle"
                        >
                          <template v-if="form['formUID'] === NIL_UUID">
                            <span>{{ card['leaveName'] + (card['leaveSubName'] ? '/' + card['leaveSubName'] : '') }}</span>
                          </template>
                          <template v-else>
                            <router-link
                              :to="{
                                name: form['formID'] + 'Sent',
                                params: { formUID: form['formUID'] }
                              }"
                            >
                              <span v-tooltip="{ value: form['formNo'], escape: true }">
                                {{ card['leaveName'] + (card['leaveSubName'] ? '/' + card['leaveSubName'] : '') }}
                              </span>
                            </router-link>
                          </template>
                        </td>
                        <td>{{ form['formInfo'] }}</td>
                        <td>{{ card['days'] }}</td>
                        <td>{{ card['hours'] }}</td>
                      </tr>
                    </template>
                  </template>
                </template>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onBeforeRouteLeave } from 'vue-router'
import { useAuthUserStore } from '../store/index'
import { useWorkday } from '../composable/workdays'
import { useInboxData } from '../composable/inboxData'
import { useLeaveData } from '../composable/leaveData'
import { useAbortController } from '../composable/abortController'
import { GET_ALLOWEDQUERYEMPLOYEES_URL, GET_DEPUTIES_URL, GET_MONTHATTENDANCE_URL, GET_MONTHSENTCARDS_URL } from '../api/appUrl'
import { NIL_UUID, MONTHS_OPTIONS, SYSTEM_ERROR_MESSAGE, FORM_ID } from '../api/appConst'
import type { MonthsOptionsType, DayAttendanceApiType } from '../api/appType'
import { sortCard, getThreeRocYears, getFullCalendarDayClass, dateToRocString, onSearchEmployeeData, onSetDeputy } from '../api/appFunction'
import VueSelect from 'vue-select'
import { useToast } from 'primevue/usetoast'
import ChooseUserEx from '../components/ChooseUserEx.vue'

import FullCalendar from '@fullcalendar/vue3'
import dayGridPlugin from '@fullcalendar/daygrid'
import bootstrap5Plugin from '@fullcalendar/bootstrap5'
import interactionPlugin from '@fullcalendar/interaction'
import zhTW_Fullcalendar from '@fullcalendar/core/locales/zh-tw'

const mode = import.meta.env.MODE
const now = new Date()

const userStore = useAuthUserStore()
const toast = useToast()

const { workdays, calendarLoaded, onLoadCalendarData } = useWorkday()
const { inboxData, onGetInboxData } = useInboxData()
const { leaves, onGetLeaves, onSetLeaves } = useLeaveData()
const { abortController, fetchErrorHandler, abortListener } = useAbortController()

const employeeData = ref<any>([])
const fullCalendar = ref<InstanceType<typeof FullCalendar>>()
const userIdQuery = ref<string>('')
const thisDate = ref<Date>(now)
const deputies = ref<Array<string>>([])
const inbox = ref<Array<any>>([])
const a1Card = ref<Array<{
  id: number
  formID: string
  formUID: string
  formNo: string
  card: Array<any>
  d0: boolean
  d1: boolean
  d2: boolean
  d3: boolean
  d4: boolean
  d5: boolean
  d6: boolean
  d7: boolean
  d8: boolean
  d9: boolean
  d10: boolean
}>>([])
const b1Card = ref<Array<{
  formID: string
  formUID: string
  formNo: string
  date: string
  card: Array<{
    startTime: string
    endTime: string
    applicationType: string
    project: string
    hour: number
  }>
}>>([])
const c1Card = ref<Array<{
  formID: string
  formUID: string
  formNo: string
  formInfo: string,
  card: Array<{
    leaveName: string
    leaveSubName: string
    days: number
    hours: number
  }>
}>>([])
const inboxLoaded = ref<boolean>(false)
const cardLoaded = ref<boolean>(false)

const thisDateYear = computed<number>(() => thisDate.value.getFullYear())
const thisDateMonth = computed<number>(() => thisDate.value.getMonth() + 1)
const startDate = computed<Date>(() => new Date(thisDate.value.getFullYear(), thisDate.value.getMonth(), 1))
const endDate = computed<Date>(() => new Date(thisDate.value.getFullYear(), thisDate.value.getMonth() + 1, 0))
const toggleChooseUserEx = computed<boolean>((): boolean => (employeeData.value.length >= 2) || (employeeData.value.find((e: any) => e.deptNo === userStore.deptNo)?.teamMembers?.length >= 2))

const calendarOptions: any = {
  aspectRatio: 1.00,
  height: 'auto',
  eventDisplay: 'list-item',
  eventTimeFormat: {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  },
  fixedWeekCount: false,
  showNonCurrentDates: false,
  plugins: [
    dayGridPlugin,
    interactionPlugin,
    bootstrap5Plugin
  ],
  themeSystem: 'bootstrap5',
  headerToolbar: {
    start: '',
    center: '',
    end: ''
  },
  initialView: 'dayGridMonth',
  locale: zhTW_Fullcalendar,
  now: now,
  views: {
    dayGridMonth: {
      dayCellClassNames: (content: { date: Date }) => {
        if (calendarLoaded.value === true) {
          const dateData = {
            year: content.date.getFullYear(),
            month: content.date.getMonth(),
            day: content.date.getDate()
          }
          return getFullCalendarDayClass(dateData, workdays.value)
        }
      },
      dayCellContent: (content: { dayNumberText: string }) => {
        return content.dayNumberText.replace(/\D/g, '')
      },
      dayHeaderClassNames: () => {
        return 'bg-secondary text-light'
      },
      dayHeaderContent: (content: { text: string }) => {
        return content.text.replace('週', '')
      }
    }
  }
}

const onGetAllowedQueryEmployeesData = async (empNo: string): Promise<void> => {
  const params = new URLSearchParams({
    empNo: empNo
  })
  const res: Response = await fetch(GET_ALLOWEDQUERYEMPLOYEES_URL + '?' + params, {
    method: 'GET',
    signal: abortController.signal
  })
  if (!res.ok) {
    throw new Error(res.status.toString())
  }
  employeeData.value = await res.json()
}

const onLoadInbox = async (): Promise<void> => {
  inboxLoaded.value = false
  try {
    await onGetInboxData(userIdQuery.value, abortController.signal)
    inbox.value = []
  } catch (err: unknown) {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  }
  if (inboxData.value.length > 0) {
    inboxData.value.forEach((boxes: { Inboxes: Array<{ FormID: string, FormUID: string, FormSubject: string, StartTime: string }> }) => {
      inbox.value = inbox.value.concat(
        boxes.Inboxes.map((e: { FormID: string, FormUID: string, FormSubject: string, StartTime: string }) => {
          return {
            formID: e.FormID,
            formUID: e.FormUID,
            formSubject: e.FormSubject,
            startTime: e.StartTime
          }
        })
      )
    })
    inbox.value.sort((val1: { formID: string, formUID: string, formSubject: string, startTime: string }, val2: { formID: string, formUID: string, formSubject: string, startTime: string }) => {
      if (val1.formID === val2.formID) {
        return new Date(val2.startTime).getTime() - new Date(val1.startTime).getTime()
      } else {
        return sortCard(val1, val2)
      }
    })
  }
  inboxLoaded.value = true
}

const onGetMonthAttendance = async (theDate: Date, empNo: string): Promise<void> => {
  const params = new URLSearchParams({
    date: theDate.toISOString(),
    empNo: empNo
  })
  await fetch(GET_MONTHATTENDANCE_URL + '?' + params, {
    method: 'GET',
    signal: abortController.signal
  }).then((res: Response): Promise<Array<DayAttendanceApiType>> => {
    if (!res.ok) {
      throw new Error(res.status.toString())
    }
    return res.json()
  }).then((res: Array<DayAttendanceApiType>): void => {
    if (fullCalendar.value !== undefined) {
      const fullCalendarApi = fullCalendar.value.getApi()
      fullCalendarApi.removeAllEvents()
      res.forEach((e: DayAttendanceApiType) => {
        if (e.InTimeString.length > 0) {
          const timeInfoArray = e.InTimeString.replace(/\s/g, '').split(',')
          timeInfoArray.forEach((timeInfoString: string) => {
            const timeArray = timeInfoString.substring(0, 5).split(':')
            const info = timeInfoString.substring(5)
            fullCalendarApi.addEvent({
              start: new Date(e.Year, e.Month - 1, e.Day, parseInt(timeArray[0], 10), parseInt(timeArray[1], 10)).toISOString(),
              borderColor: '#000000',
              classNames: [
                (info.length > 0) ? 'fw-bold text-primary' : 'fw-normal text-black'
              ]
            })
          })
        }
      })
    }
  }).catch((err: Error): void => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })
}

const onGetDeputies = async (empNo: string): Promise<void> => {
  await fetch(GET_DEPUTIES_URL + '/' + empNo, {
    method: 'GET',
    signal: abortController.signal
  }).then((res: Response): Promise<any> => {
    if (!res.ok) {
      throw new Error(res.status.toString())
    }
    return res.json()
  }).then((res: any): void => {
    deputies.value = Object.keys(res).map((e: any) => {
      return e + '' + res[e]
    })
  }).catch((err: Error): void => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })
}

const onChangeUser = async (event: string): Promise<void> => {
  userIdQuery.value = event

  await onGetMonthAttendance(thisDate.value, userIdQuery.value)
  if (fullCalendar.value !== undefined) {
    fullCalendar.value.getApi().render()
  }

  onSetScheduling()
}

const onChangeYearMonth = async (): Promise<void> => {
  await Promise.all([
    onLoadCalendarData(startDate.value, endDate.value, userIdQuery.value, abortController.signal),
    onGetMonthAttendance(thisDate.value, userIdQuery.value)
  ]).catch((err: Error): void => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })
  if (fullCalendar.value !== undefined) {
    fullCalendar.value.getApi().render()
  }
  
  onSetLeaves(null)
  onGetLeaves(thisDate.value, userIdQuery.value, abortController.signal)
  onGetSentboxData(userIdQuery.value)
}

const onChangeYear = (event: number): void => {
  // 如果變更的日期超過當月最後一天，則切換為當月最後一天
  let date = new Date(event, thisDate.value.getMonth(), thisDate.value.getDate())
  const lastDayInMonth = new Date(event, thisDate.value.getMonth() + 1, 0)
  if (date > lastDayInMonth) {
    date = lastDayInMonth
  }
  thisDate.value = date
  if (fullCalendar.value !== undefined) {
    fullCalendar.value.getApi().gotoDate(date)
  }
}

const onChangeMonth = (event: number): void => {
  // 如果變更的日期超過當月最後一天，則切換為當月最後一天
  let date = new Date(thisDate.value.getFullYear(), event - 1, thisDate.value.getDate())
  const lastDayInMonth = new Date(thisDate.value.getFullYear(), event, 0)
  if (date > lastDayInMonth) {
    date = lastDayInMonth
  }
  thisDate.value = date
  if (fullCalendar.value !== undefined) {
    fullCalendar.value.getApi().gotoDate(date)
  }
}

const onSetTendaysColumn = (): Array<any> => {
  const column: Array<any> = []

  // 上旬
  let columnData: any = {
    id: 0,
    card: []
  }
  for (let index = 0; index < 11; index++) {
    if (index === 10) {
      columnData['d' + index] = false
    } else {
      columnData['d' + index] = true
    }
  }
  column.push(columnData)

  // 中旬
  columnData = {
    id: 1,
    card: []
  }
  for (let index = 0; index < 11; index++) {
    if (index === 10) {
      columnData['d' + index] = false
    } else {
      columnData['d' + index] = true
    }
  }
  column.push(columnData)

  // 下旬
  columnData = {
    id: 2,
    card: []
  }
  const dayMonthEnd = new Date(thisDate.value.getFullYear(), thisDate.value.getMonth() + 1, 0).getDate()
  for (let index = 0; index < 11; index++) {
    if (index >= (dayMonthEnd - 20)) {
      columnData['d' + index] = false
    } else {
      columnData['d' + index] = true
    }
  }
  column.push(columnData)

  return column
}

const onGetSentboxData = async (employee: string): Promise<void> => {
  cardLoaded.value = false
  const getMonthSentCardsParams = new URLSearchParams({
    empNo: employee,
    date: thisDate.value.toISOString(),
    status: '2'
  })
  await fetch(GET_MONTHSENTCARDS_URL + '?' + getMonthSentCardsParams, {
    method: 'GET',
    signal: abortController.signal
  }).then((res: Response): Promise<any> => {
    if (!res.ok) {
      throw new Error(res.status.toString())
    }
    return res.json()
  }).then(async (res: any): Promise<void> => {
    let resultA1CardData: Array<any> = []
    let resultB1CardData: Array<any> = []
    let resultC1CardData: Array<any> = []

    const sentboxFiltered = res.map((e: any) => {
      return {
        formID: e.FormID,
        cards: e.Cards
      }
    })

    sentboxFiltered.forEach((sentboxFilteredEach: any) => {
      if (sentboxFilteredEach.formID === 'A1Card') {
        resultA1CardData = [...resultA1CardData, ...onSetA1CardData(sentboxFilteredEach)]
      } else if (sentboxFilteredEach.formID === 'B1Card') {
        resultB1CardData = [...resultB1CardData, ...onSetB1CardData(sentboxFilteredEach)]
      } else if (sentboxFilteredEach.formID === 'C1Card') {
        resultC1CardData = [...resultC1CardData, ...onSetC1CardData(sentboxFilteredEach)]
      }
    })
    cardLoaded.value = true

    resultA1CardData.sort((val1: any, val2: any) => val1.id - val2.id)
    resultB1CardData.sort((val1: any, val2: any) => val1.date - val2.date)
    resultC1CardData.sort((val1: any, val2: any) => val1.id - val2.id)
    a1Card.value = resultA1CardData
    b1Card.value = resultB1CardData
    c1Card.value = resultC1CardData
  }).catch((err: Error): void => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })
}

const onSetA1CardData = (sentboxData: any): Array<any> => {
  const a1CardData: Array<any> = onSetTendaysColumn()

  sentboxData.cards.forEach((cardEach: any) => {
    const rowData: Record<PropertyKey, string | undefined> = {}
    const rowFound = a1CardData.findIndex((e: any) => e.id === (parseInt(cardEach.A1_NN, 10) - 1))
    if (rowFound !== -1) {
      const a1Hours: Array<string> = cardEach.Details[0].A1_DDHH.split('')

      for (let index = 0; index < 11; index++) {
        const field: string = 'd' + index
        rowData[field] = undefined
        if (a1CardData[rowFound][field]) {
          rowData[field] = a1Hours[index]
        }
      }

      a1CardData[rowFound].formID = sentboxData.formID
      a1CardData[rowFound].formUID = cardEach.FormUID
      a1CardData[rowFound].formNo = cardEach.A1_SHEETNO
      a1CardData[rowFound].card.push({
        ...rowData,
        ...{
          applicationType: cardEach.ApplicationType,
          project: cardEach.Details[0].A1_PROJNO,
          hour: cardEach.Details[0].A1_HOUR
        }
      })
    }
  })
  return a1CardData
}

const onSetB1CardData = (sentboxData: any): Array<any> => {
  const b1CardData: Array<any> = []
  sentboxData.cards.forEach((cardEach: any, cardEachIndex: number) => {
    b1CardData.push({})
    b1CardData[cardEachIndex].formID = sentboxData.formID
    b1CardData[cardEachIndex].formUID = cardEach.FormUID
    b1CardData[cardEachIndex].formNo = cardEach.SheetNo
    b1CardData[cardEachIndex].date = dateToRocString(new Date(cardEach.Details[0].StartTime), false)
    b1CardData[cardEachIndex].card = []
    cardEach.Details.forEach((cardDetails: any) => {
      const startTime = new Date(cardDetails.StartTime)
      const endTime = new Date(cardDetails.EndTime)
      const startTimeNextDate = new Date(startTime)
      startTimeNextDate.setDate(startTimeNextDate.getDate() + 1)

      const startTimeString = new Date(cardDetails.StartTime).getHours().toString().padStart(2, '0') + ':' + new Date(cardDetails.StartTime).getMinutes().toString().padStart(2, '0')
      let endTimeString = ''

      if (new Date(startTimeNextDate.getFullYear() + '/' + (startTimeNextDate.getMonth() + 1) + '/' + startTimeNextDate.getDate()).getTime() === new Date(endTime.getFullYear() + '/' + (endTime.getMonth() + 1) + '/' + endTime.getDate()).getTime()) {
        endTimeString = '24:00'
      } else {
        endTimeString = endTime.getHours().toString().padStart(2, '0') + ':' + endTime.getMinutes().toString().padStart(2, '0')
      }

      b1CardData[cardEachIndex].card.push({
        applicationType: cardDetails.ApplicationType,
        project: cardDetails.Project,
        hour: cardDetails.Hour,
        startTime: startTimeString,
        endTime: endTimeString
      })
    })
  })
  return b1CardData
}

const onSetC1CardData = (sentboxData: any): Array<any> => {
  const c1CardData: Array<any> = []
  sentboxData.cards.forEach((cardEach: any) => {
    c1CardData.push({
      formID: sentboxData.formID,
      formUID: cardEach.FormUID,
      formNo: cardEach.FormNumber,
      formInfo: cardEach.FormInfo,
      card: [{
        leaveName: cardEach.LeaveName,
        leaveSubName: cardEach.LeaveSubName,
        days: cardEach.Days,
        hours: cardEach.NetHours
      }]
    })
  })

  return c1CardData
}

const onSetScheduling = async (): Promise<void> => {
  onSetLeaves(null)
  await Promise.all([
    onGetLeaves(thisDate.value, userIdQuery.value, abortController.signal),
    onGetDeputies(userIdQuery.value),
    onLoadInbox(),
    onGetSentboxData(userIdQuery.value)
  ]).catch((err: Error): void => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })
}

onBeforeRouteLeave(() => {
  toast.removeGroup('app')
})

onMounted(async (): Promise<void> => {
  abortListener()
  userIdQuery.value = userStore.userId

  await Promise.all([
    onLoadCalendarData(startDate.value, endDate.value, userIdQuery.value, abortController.signal),
    onGetMonthAttendance(thisDate.value, userIdQuery.value)
  ]).catch((err: Error): void => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })
  if (fullCalendar.value !== undefined) {
    fullCalendar.value.getApi().render()
  }

  onSetScheduling()

  await onGetAllowedQueryEmployeesData(userIdQuery.value).catch((err: Error): void => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })
})
</script>
<style lang="scss" scoped>
@use '../assets/scss/bootstrap_variables' as bootstrap;
@use '../assets/scss/calendar' as calendar;
:deep() {
  @include calendar.color;
}

:deep(.fc table) {
  font-size: bootstrap.$font-size-base;
}

:deep(.fc-theme-bootstrap5 td) {
  border-width: 0 2px 2px 2px;
}

:deep(.fc .fc-toolbar.fc-header-toolbar) {
  margin-bottom: 0;
}

:deep(.fc-daygrid-event-dot) {
  display: none;
}

:deep(.fc-daygrid-dot-event:hover) {
  background: unset;
}

:deep(.fc .fc-daygrid-event) {
  margin-top: 0;
}

:deep(.fc .fc-daygrid-body-natural .fc-daygrid-day-events) {
  margin-bottom: 4px;
}

:deep(.fc-daygrid-dot-event) {
  padding: 0;
}

:deep(.fc-event) {
  display: block;
}

:deep(.fc-event-time),
:deep(.fc-event-title) {
  display: inline;
  white-space: normal;
  padding-left: 0.6rem;
}

:deep(.vs__search) {
  padding: 0;
}
</style>