import { setActive<PERSON><PERSON>, createP<PERSON> } from 'pinia'
import { useMessageStore } from '../message'

describe('useMessageStore', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('data', () => {
    const message = useMessageStore()
    expect(message.data).toBe('')
    message.setData('test')
    expect(message.data).toBe('test')
  })

  it('routerLink', () => {
    const message = useMessageStore()
    expect(message.data).toBe('')
    message.setRouterLink('test')
    expect(message.routerLink).toBe('test')
  })
})