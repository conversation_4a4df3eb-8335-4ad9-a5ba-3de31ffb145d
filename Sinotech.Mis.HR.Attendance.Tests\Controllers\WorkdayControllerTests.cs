﻿using FakeItEasy;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sinotech.Mis.HR.Attendance.BusinessLogic;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers.Tests
{
    [ExcludeFromCodeCoverage]
    public class WorkdayControllerTests
    {

        private readonly WorkdayController _controller;

        public WorkdayControllerTests(WorkdayBo workdayBo)
        {
            ILogger<WorkdayController> logger = A.Fake<ILogger<WorkdayController>>();
            _controller = new WorkdayController(workdayBo, logger);
        }

        [Theory]
        [InlineData("2022-1-1", "2022-1-4", "0001", 2)]
        [InlineData("2022-1-1", "2022-1-4", "0349", 2)]
        [InlineData("2022-1-1", "2022-1-4", "0395", 2)]
        [InlineData("2022-1-1", "2022-1-4", "2008", 2)]
        [InlineData("2022-1-1", "2022-1-4", "2016", 2)]
        public void GetEmployeeWorkdaysDateRangeTest(DateTime startDate, DateTime endDate, string empNo, int expected)
        {
            string result = _controller.GetEmployeeWorkdaysDateRange(startDate, endDate, empNo);
            List<Workday> workdays = StringToWorkdays(result);
            Assert.Equal(expected, workdays.Count);
        }

        [Theory]
        [InlineData("2022-1-1", "2022-1-4", 2)]
        [InlineData("2022-3-1", "2022-3-7", 5)]
        public void GetWorkdaysDateRangeTest(DateTime startDate, DateTime endDate, int expected)
        {
            string result = _controller.GetWorkdaysDateRange(startDate, endDate);
            List<Workday> workdays = StringToWorkdays(result);
            Assert.NotEmpty(workdays);
            Assert.Equal(expected, workdays.Count);
        }

        private static List<Workday> StringToWorkdays(string result)
        {
            List<Workday> workdays = JsonConvert.DeserializeObject<List<Workday>>(result);
            workdays = workdays.Where(w => Math.Abs(w.WorkHours - 8.0) < 0.01).ToList();
            return workdays;
        }
    }
}
