﻿using Sinotech.Mis.Common;
using Sinotech.Mis.Helpers;
using Sinotech.Mis.HR.Attendance.BusinessLogic;
using System;
using System.Collections.Generic;
using System.Data;

namespace Sinotech.Mis.HR.Attendance.Common
{
    public interface IAttendanceBo
    {
        /// <summary>
        /// 是否能看出勤資料
        /// </summary>
        /// <param name="watcherNo">觀看者員工編號</param>
        /// <param name="empNo">被看者員工編號</param>
        /// <returns></returns>
        public bool CanSeeAttendance(string watcherNo, string empNo);

        /// <summary>
        /// 新增表單
        /// </summary>
        /// <param name="formID">表單名稱</param>
        /// <param name="chineseYear">ROC年度</param>
        /// <param name="dataSet">包含所有待新增資料表的 Data Set</param>
        /// <returns>(單號，錯誤訊息)</returns>
        public (string?, string) AddForm(string formID, int chineseYear, DataSet dataSet);

        /// <summary>是否可以填加班</summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="date">日期</param>
        /// <returns>傳回B1CardAppCheckResult</returns>
        public B1CardAppCheckResult IsOvertimeAllowed(string empNo, DateTime date);

        /// <summary>
        /// 結案抽單
        /// </summary>
        /// <param name="withdraw">抽單物件</param>
        /// <returns>抽單成功傳回空值，失敗傳回錯誤訊息</returns>
        public string ClosedWithdraw(Withdraw withdraw);

        /// <summary>
        /// 指定員工可查詢之部門、組別、員工的資料 
        /// 
        /// 登入者身分	                                      可查詢資料範圍
        ///系統管理者（業務承辦主管、業務承辦人、系統開發者）	 全社員工資料
        ///治理部門正、副主管	                              全社員工資料
        ///稽核室正、副主管	                                  全社員工資料
        ///行政處正、副主管	                                  全社員工資料
        ///部門正、副主管	                                  管轄部門的員工資料
        /// 組長		                                        管轄組別的員工資料
        /// 一般同仁		                                  個人資料
        /// </summary>
        /// <returns></returns>
        public List<DeptTeam> GetAllowedQueryEmployees(string empNo);

        /// <summary>
        /// 指定員工可查詢之部門、組別、員工的資料 
        /// 
        /// 登入者身分	                                     可查詢資料範圍
        /// 治理部門正、副主管	                              全社員工資料
        /// 部門正、副主管	                                  管轄部門的員工資料
        /// 部門登記桌	                                      所屬部門的員工資料
        /// </summary>
        /// <returns></returns>
        public List<DeptTeam> GetAllowedQueryDepartmentSentBoxEmployees(string empNo);

        /// <summary>
        /// 依指定員工身分，取得該員在【部門填報紀錄】能否查詢全社資料
        /// 
        /// 員工身分	                                     可查詢資料範圍
        /// 治理部門正、副主管	                              全社員工資料
        /// </summary>
        /// <returns></returns>
        public bool IsAllowedQueryAllDepartmentSentBox(string empNo);

        /// <summary>
        /// 紀錄第一次讀取通知的時間
        /// </summary>
        /// <param name="id">通知編號</param>
        /// <returns>紀錄第一次讀取通知的結果是成功或失敗</returns>
        public bool DeliveredNotifications(int id);

        /// <summary>
        /// 取得全部已填表單，以填表日期為準
        /// </summary>
        /// <param name="startDate">最早填卡日期</param>
        /// <param name="endDate">最晚填卡日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns>已填表單</returns>
        public DataTable GetAllSentBox(DateTime startDate, DateTime endDate, int? status = null);

        /// <summary>
        /// 取得全部已填表單，以填表日期為準
        /// </summary>
        /// <param name="startDate">最早填卡日期</param>
        /// <param name="endDate">最晚填卡日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>已填表單</returns>
        public DataTable GetAllSentBox(DateTime startDate, DateTime endDate, string projNo);

        /// <summary>
        /// 取得全部已填表單，以內容日期為準
        /// </summary>
        /// <param name="startDate">最早日期</param>
        /// <param name="endDate">最晚日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>已填表單</returns>
        public DataTable GetAllSentBoxByContentDate(DateTime startDate, DateTime endDate, string projNo);

        /// <summary>
        /// 取得全部已填表單，以內容日期為準
        /// </summary>
        /// <param name="startDate">最早日期</param>
        /// <param name="endDate">最晚日期</param>
        /// <param name="status">表單狀態，預設 null</param>
        /// <returns>已填表單</returns>
        public DataTable GetAllSentBoxByContentDate(DateTime startDate, DateTime endDate, int? status = null);

        /// <summary>
        /// 取得表單附件
        /// </summary>
        /// <param name="formUID">表單Guid</param>
        /// <returns></returns>
        public DataTable GetAttachments(Guid formUID);

        /// <summary>
        /// 取得可加班員工DataTable
        /// 會將優先部門放在前面
        /// </summary>
        ///<param name="deptNo">優先部門代號，預設 -1 表示不將優先部門置前</param>
        /// <returns>可加班員工DataTable</returns>
        public DataTable GetEligibleOvertimeEmployeesDataTable(int deptNo = -1);

        /// <summary>
        /// 取得所有可加班現職員工詳細資料 JSON String
        /// 會將優先部門放在前面
        /// </summary>
        ///<param name="departmentNumber">優先部門代號，Smaller than 1 表示不將優先部門置前</param>
        /// <returns>所有可加班現職員工詳細資料 Json String</returns>
        public string GetEligibleOvertimeEmployeesJson(int departmentNumber);

        /// <summary>
        /// 取得所有可加班現職員工詳細資料 JSON String
        /// </summary>
        /// <param name="sortField">排序欄位</param>
        /// <param name="ascDesc">升冪或降冪 ASC or DESC</param>
        /// <returns>所有可加班現職員工詳細資料 Json String</returns>
        public string GetEligibleOvertimeEmployeesJson(string sortField = "EmpNo", string ascDesc = "ASC");

        /// <summary>
        /// 讀取員工刷卡時間(依據查詢日期 )
        /// </summary>
        /// <param name="theDate">查詢日期.</param>
        /// <param name="empNo">員工編號.</param>
        public DayInTime GetDayInTime(DateTime theDate, string empNo);

        /// <summary>
        /// 讀取員工刷卡時間字串(依據查詢日期 )
        /// </summary>
        /// <param name="theDate">查詢日期.</param>
        /// <param name="empNo">員工編號.</param>
        public string GetDayInTimeString(DateTime theDate, string empNo);

        /// <summary>
        /// 取得部門已填表單
        /// </summary>
        /// <param name="deptNo">部門員工編號</param>
        /// <returns>已填表單</returns>
        public DataTable GetDepartmentSentBox(int deptNo);

        /// <summary>
        /// 取得部門已填表單，以填表日期為準
        /// </summary>
        /// <param name="deptNo">部門員工編號</param>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <returns>已填表單</returns>
        public DataTable GetDepartmentSentBox(int deptNo, DateTime startDate, DateTime endDate);

        /// <summary>
        /// 取得部門已填表單，以填表日期為準
        /// </summary>
        /// <param name="deptNo">部門員工編號</param>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>已填表單</returns>
        public DataTable GetDepartmentSentBox(int deptNo, DateTime startDate, DateTime endDate, string projNo);

        /// <summary>
        /// 取得部門已填表單，以內容日期為準
        /// </summary>
        /// <param name="deptNo">部門員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <returns>已填表單</returns>
        public DataTable GetDepartmentSentBoxByContentDate(int deptNo, DateTime startDate, DateTime endDate);

        /// <summary>
        /// 取得部門已填表單，以內容日期為準
        /// </summary>
        /// <param name="deptNo">部門員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>已填表單</returns>
        public DataTable GetDepartmentSentBoxByContentDate(int deptNo, DateTime startDate, DateTime endDate, string projNo);

        /// <summary>
        /// 取得 部門  某月份已填表單 ，限部門登記桌或管理員
        /// </summary>
        /// <param name="deptNo">部門編號</param>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <returns>已填表單</returns>
        public DataTable GetDepartmentSentBoxYearMonth(int deptNo, int year, int month);

        /// <summary>
        /// 取得當年度1月至該月分之剩餘補休假時數
        /// </summary>
        /// <param name="date"></param>
        /// <param name="empNo"></param>
        /// <returns>剩餘補休假時數</returns>
        public int GetEmployeeCompensatoryLeaveHours(DateTime date, string empNo);

        /// <summary>
        /// 取得員工指定假別休假資料，包括年度總可用時數、年度已使用時數(簽核中＋已核可)、
        /// 累至查詢月份已使用時數(簽核中＋已核可)、查詢月份已使用時數(簽核中＋已核可)、
        /// 年度已核可時數(已核可)、累至查詢月份年度已核可時數(已核可)、
        /// 查詢月份已核可時數(已核可)
        /// 提供以下假別代碼查詢：
        /// 01：特別休息假
        /// 04：事假
        /// 10：病假
        /// 12：補休假(未開發)
        /// 14：延休假
        /// 16：生理假
        /// 19：家庭照顧假
        /// </summary>
        /// <param name="date">查詢日期</param>
        /// <param name="empNo">員工編號</param>
        /// <param name="leaveNumber">假別</param>
        /// <param name="leaveSubNumber">假別細項</param>
        public string GetEmployeeLeaveInfo(DateTime date, string empNo, LeaveKindEnum leaveNumber, int leaveSubNumber);

        /// <summary>
        /// 讀取員工指定月份(依據查詢日期 )休假統計時數，
        /// 包括年度總可用時數、年度已使用時數(簽核中＋已核可)、累至查詢月份已使用時數(簽核中＋已核可)、
        /// 查詢月份已使用時數(簽核中＋已核可)、年度已核可時數(已核可)、
        /// 累至查詢月份年度已核可時數(已核可)、查詢月份已核可時數(已核可)
        /// 提供 01：特別休息假 12：補休假  14：延休假
        /// </summary>
        /// <param name="theDate">查詢日期.</param>
        /// <param name="empNo">員工編號.</param>
        public List<EmpLeaveInfo> GetEmployeeLeaveInformation(DateTime theDate, string empNo);

        /// <summary>取得 Form</summary>
        /// <param name="formUID">表單識別碼</param>
        /// <returns>DataTable Form</returns>
        public DataTable GetForm(Guid formUID);

        /// <summary>取得 FormFLow</summary>
        /// <param name="flowUID">流程識別碼</param>
        /// <returns>DataTable FormFlow</returns>
        public DataTable GetFormFlow(Guid flowUID);

        /// <summary>取得 FormFLow</summary>
        /// <param name="formUID">表單識別碼</param>
        /// <returns>DataTable FormFlow</returns>
        public DataTable GetFormFlows(Guid formUID);

        /// <summary>
        /// 取得 FormFlowStatus名稱列表
        /// </summary>
        /// <returns></returns>
        public Dictionary<int, string> GetFormFlowStatusNames();

        /// <summary>
        /// 依日期區間取得所有表單 by 填報日期，限管理員使用
        /// </summary>
        /// <param name="startDate">開始日</param>
        /// <param name="endDate">結束日</param>
        /// <returns>表單資料表</returns>
        public DataTable GetForms(DateTime startDate, DateTime endDate);

        /// <summary>
        /// 依日期區間取得所有表單 by 填報日期，限管理員使用
        /// </summary>
        /// <param name="startDate">開始日</param>
        /// <param name="endDate">結束日</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單資料表</returns>
        public DataTable GetForms(DateTime startDate, DateTime endDate, string projNo);

        /// <summary>
        /// 依日期區間取得所有表單 by 內容日期，限管理員使用
        /// </summary>
        /// <param name="startDate">開始日</param>
        /// <param name="endDate">結束日</param>
        /// <returns>表單資料表</returns>
        public DataTable GetFormsByContentDate(DateTime startDate, DateTime endDate);

        /// <summary>
        /// 依日期區間取得所有表單 by 內容日期，限管理員使用
        /// </summary>
        /// <param name="startDate">開始日</param>
        /// <param name="endDate">結束日</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單資料表</returns>
        public DataTable GetFormsByContentDate(DateTime startDate, DateTime endDate, string projNo);

        /// <summary>
        /// Get Forms by FormStatus
        /// </summary>
        /// <param name="status">FormStatus</param>
        /// <returns>Forms</returns>
        public DataTable GetFormsByStatus(FormStatus status);

        /// <summary>
        /// Get Forms by FormStatus
        /// </summary>
        /// <param name="status">FormStatus</param>
        /// <param name="startDate">最早填卡日期</param>
        /// <param name="endDate">最晚填卡日期</param>
        /// <returns>Forms</returns>
        public DataTable GetFormsByStatus(FormStatus status, DateTime startDate, DateTime endDate);

        /// <summary>
        /// 取得 FormStatus名稱列表
        /// </summary>
        /// <returns></returns>
        public Dictionary<int, string> GetFormStatusNames();

        /// <summary>
        /// 取得 FormType DataTable
        /// </summary>
        /// <returns></returns>
        public DataTable GetFormTypeDataTable();

        /// <summary>取得收件匣 DataTable</summary>
        /// <param name="roles">角色的List</param>
        /// <returns>收件匣 DataTable</returns>
        public DataTable GetInboxes(List<Role> roles);


        /// <summary>
        /// 讀取員工指定月份出勤時間
        /// </summary>
        /// <param name="date">The date.</param>
        /// <param name="userid">The userId.</param>
        /// <returns></returns>
        public List<DayAttendance> GetMonthAttendance(DateTime date, string userid);

        /// <summary>
        /// 讀取員工指定月份(依據查詢日期 )休假統計時數
        /// </summary>
        /// <param name="theDate">查詢日期.</param>
        /// <param name="empNo">員工編號.</param>
        public EmployeeLeaves GetMonthEmployeeLeaves(DateTime theDate, string empNo);

        /// <summary>
        /// 讀取員工指定月(依據查詢日期 ) 已填報有效(簽核中與同意)且為加班上限範圍內之加班統計
        /// </summary>
        /// <param name="date">查詢日期.</param>
        /// <param name="empNo">員工編號.</param>
        public MonthOvertimeStatics GetMonthEmployeeOvertimeStatics(DateTime date, string empNo);

        /// <summary>取得指定表單新單號 (最大表單單號+1)</summary>
        /// <param name="formID">表單編號</param>
        /// <param name="chineseYear">民國年</param>
        public string GetNewFormNo(string formID, string chineseYear);

        /// <summary>
        /// 取得所有通知
        /// </summary>
        /// <returns>通知 List<Notification></returns>
        public List<Notification> GetNotifications();

        /// <summary>
        /// 取得所有通知表單資料
        /// </summary>
        /// <returns>通知 List<Notification></returns>
        public List<NotifyFormCards> GetNotifyFormCards(string empNo, int deptNo, List<Role> roles, List<int> isRead, List<int>formStatus, DateTime startDate, DateTime endDate);

        /// <summary>
        /// 取得加權加班時數
        /// </summary>
        /// <param name="date"></param>
        /// <param name="hours"></param>
        /// <returns></returns>
        public int GetPaidHour(DateTime date, int hours);

        /// <summary>
        /// 取得員工 B1Card Position
        /// </summary>
        /// <param name="employeeNumber"></param>
        /// <returns></returns>
        public B1CardPositionEnum GetPositionType(string employeeNumber);

        /// <summary>
        /// 讀取員工指定季(依據查詢日期 ) 已填報有效(簽核中與同意)且為加班上限範圍內之加班總時數
        /// </summary>
        /// <param name="theDate">查詢日期.</param>
        /// <param name="empno">員工編號.</param>
        public int GetQuarterlyEmployeeOvertimeHours(DateTime theDate, string empno);

        /// <summary>
        /// 讀取員工指定季(依據查詢日期 ) 已填報有效(簽核中與同意)且為加班上限範圍內之加班時數統計
        /// </summary>
        /// <param name="date">查詢日期.</param>
        /// <param name="empNo">員工編號.</param>
        public QuarterlyOvertimeStatics GetQuarterlyEmployeeOvertimeStatics(DateTime date, string empNo);

        /// <summary>取得 填表人/表單關係人 已填表單</summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns>寄件匣 DataTable</returns>
        public DataTable GetSentBox(string empNo, DateTime startDate, DateTime endDate, int? status = null);

        /// <summary>取得 填表人/表單關係人 已填表單</summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <param name="status">表單狀態</param>
        /// <returns>寄件匣 DataTable</returns>
        public DataTable GetSentBox(string empNo, DateTime startDate, DateTime endDate, string projNo, int? status = null);

        /// <summary>取得 填表人/表單關係人 已填表單</summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns>寄件匣 DataTable</returns>
        public DataTable GetSentBoxByContentDate(string empNo, DateTime startDate, DateTime endDate, int? status = null);

        /// <summary>取得 填表人/表單關係人 已填表單</summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <param name="status">表單狀態</param>
        /// <returns>寄件匣 DataTable</returns>
        public DataTable GetSentBoxByContentDate(string empNo, DateTime startDate, DateTime endDate, string projNo, int? status = null);

        /// <summary>
        /// 取得 填表人/表單關係人 某月份已填表單
        /// </summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <param name="status">表單狀態</param>
        /// <returns>已填表單</returns>
        public DataTable GetSentBoxYearMonth(string empNo, int year, int month, int? status = null);

        /// <summary>
        /// 取得 已簽核表單，包括本人簽核或代理人簽核，及預設本人角色(非代理)簽核
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="roles">角色</param>
        /// <returns>已簽核表單</returns>
        public DataTable GetSignedForms(string empNo, DateTime startDate, DateTime endDate, List<Role> roles);

        /// <summary>
        /// 取得 已簽核表單，包括本人簽核或代理人簽核，及預設本人角色(非代理)簽核，以申請內容日期為準
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="contentStartDate">申請內容啟始日期</param>
        /// <param name="contentEndDate">申請內容結束日期</param>
        /// <param name="roles">角色</param>
        /// <returns>已簽核表單</returns>
        public DataTable GetSignedFormsByContentDate(string empNo, DateTime startDate, DateTime endDate, DateTime contentStartDate, DateTime contentEndDate, List<Role> roles);

        /// <summary>
        /// 取得旬的起始與結束日期
        /// </summary>
        /// <param name="year">西元年</param>
        /// <param name="month">月</param>
        /// <param name="tenDays">旬別(1:上旬、2:中旬、3:下旬)</param>
        /// <returns></returns>
        public (DateTime, DateTime) GetTendays(int year, int month, int tenDays);

        /// <summary>
        /// 取得年月旬的起始與結束日期，包括非工作日
        /// </summary>
        /// <param name="year">西元年，不可為0</param>
        /// <param name="month">月、0:不指定</param>
        /// <param name="tenDays">旬別(1:上旬、2:中旬、3:下旬、0:不指定)</param>
        /// <returns></returns>
        public (DateTime, DateTime) GetStartEndDays(int year, int month, int tenDays);

        /// <summary>
        /// 取得使用者前N筆簽核意見
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="count">數量</param>
        /// <returns>簽核意見</returns>
        public List<string> GetTopApproveComments(string empNo, int count);

        /// <summary>
        /// 取得加班卡
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public B1Card? GetValidB1Card(DateTime date, string empNo);

        /// <summary>
        /// 取得加班申請卡
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public B1CardApp? GetValidB1CardApp(DateTime date, string empNo);


        /// <summary>
        /// 取得本月份的全年病假可休總時數
        /// </summary>
        /// <param name="date"></param>
        /// <param name="empNo"></param>
        /// <returns></returns>
        public int GetYearSickLeaveHours(DateTime date, string empNo);

        /// <summary>
        /// 是否為管理員
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public bool IsAdmin(string empNo);

        /// <summary>
        /// 檢查是否有指定的通知
        /// </summary>
        /// <param name="formUID">The form guid.</param>
        /// <param name="notifyId">The notify id.</param>
        /// <returns></returns>
        public bool IsNotificationExist(Guid formUID, int notifyId);

        /// <summary>
        /// 檢查是否有指定的通知
        /// </summary>
        /// <param name="roles">角色list</param>
        /// <param name="formUID">The form guid.</param>
        /// <param name="notifyId">The notify id.</param>
        /// <returns></returns>
        public bool IsNotificationExist(List<Role> roles, Guid formUID, int notifyId);

        /// <summary>
        /// 判斷是不是可加班特殊員工
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns>若為特殊員工，傳回true</returns>
        public bool IsSpecialStaff(string employeeNumber);

        /// <summary>
        /// 判斷是不是可加班特殊員工
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="date">加班日</param>
        /// <returns>若為特殊員工，傳回true</returns>
        public bool IsSpecialStaff(string employeeNumber, DateTime date);

        /// <summary>
        /// 判斷是不是可加班特殊員工
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="date">加班日</param>
        /// <param name="allowedMonthWeightedOvertimeHour">加班日當月可加班加權時數</param>
        /// <param name="currentMonthWeightedOvertimeHour">加班日當月已加班加權時數</param>
        /// <returns>若為特殊員工，傳回true</returns>
        public bool IsSpecialStaff(string employeeNumber, DateTime date,
            out double allowedMonthWeightedOvertimeHour, out double currentMonthWeightedOvertimeHour);

        /// <summary>
        /// 標註某員工及其對應之角色的所有未讀通知為已讀
        /// </summary>
        /// <param name="roles">員工角色</param>
        /// <returns>有一筆以上的通知被標註已讀則回傳true，否則回傳false</returns>
        public bool MarkDeliveredNotifications(List<Role> roles);

        /// <summary>
        /// 更新表單
        /// </summary>
        /// <param name="formID">表單名稱</param>
        /// <param name="dataSet">包含所有待更新資料表的 Data Set</param>
        /// <returns>成功為空值，失敗時為錯誤訊息 </returns>
        public string UpdateForm(string formID, DataSet dataSet);

        /// <summary>是否有查詢[部門填報紀錄]的權限</summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>有權限傳回<c>true</c>；否則傳回<c>false</c></returns>
        public bool IsAuthorizedToQueryDepartmentSentBox(string empNo);

        /// <summary>是否有查詢[部門填報紀錄]的權限</summary>
        /// <param name="deptNo">部門編號</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>有權限傳回<c>true</c>；否則傳回<c>false</c></returns>
        public bool IsAuthorizedToQueryDepartmentSentBox(int deptNo, string empNo);
    }
}
