﻿using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using System;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves
{
    /// <summary>
    /// 育嬰假類別
    /// </summary>
    [LeaveKind(LeaveKindEnum.ParentalLeave)]
    public class ParentalLeave : C1CardBase
    {
        #region CheckResult
        // 檢查結果定義區域
        // 此區域定義育嬰假驗證失敗時的錯誤代碼、狀態及訊息

        /// <summary>
        /// 錯誤代碼：超過育嬰假年度額度
        /// 當申請的育嬰假時數超過年度上限時使用
        /// </summary>
        public const int CodeExceedQuota = 3025301; //TODO: 暫定，待確認

        /// <summary>
        /// 錯誤狀態：超過育嬰假額度
        /// </summary>
        private readonly CardStatusEnum _statusExceedQuota = CardStatusEnum.Error;

        /// <summary>
        /// 錯誤訊息模板：超過育嬰假額度
        /// {0} = 本次申請的請假時數
        /// {1} = 育嬰假年度上限時數
        /// {2} = 年度累計已請的育嬰假時數
        /// </summary>
        private readonly string _messagePatternExceedQuota = "您本次請假 {0} 小時，年度上限 {1} 小時，累計已請 {2} 小時，不得超假";

        /// <summary>
        /// 錯誤代碼：超過育嬰假年度額度
        /// </summary>
        public const int CodeExceedParentalLeaveQuota = 3025302; //TODO: 暫定，待確認

        /// <summary>
        /// 錯誤狀態：超過育嬰假年度額度
        /// </summary>
        private readonly CardStatusEnum _statusExceedParentalLeaveQuota = CardStatusEnum.Error;

        /// <summary>
        /// 錯誤訊息模板：超過育嬰假年度額度
        /// {0} = 本次申請的育嬰假時數
        /// {1} = 年度累計已請的育嬰假時數
        /// {3} = 本次申請後的育嬰假總時數

        /// </summary>
        private readonly string _messagePatternExceedParentalLeaveQuota = "您本次請假 {0} 小時，年度累計已請 {1} 小時，總計 {2} 小時，已超過育嬰假年度上限（{3} 小時）";


        // 【請假事由】不可空白
        public const int CodeReasonFieldRequired = 3025303; //TODO: 暫定，待確認
        private readonly string _messageReasonFieldRequired = AttendanceParameters.LeaveMustHaveReason;
        private readonly CardStatusEnum _statusReasonFieldRequired = CardStatusEnum.Error;

        private CardCheckResult? _resultReasonFieldRequired;
        private CardCheckResult ResultReasonFieldRequired =>
            _resultReasonFieldRequired ??=
            new CardCheckResult(CodeReasonFieldRequired, _statusReasonFieldRequired, _messageReasonFieldRequired);

        #endregion

        /// <summary>
        /// 建構子
        /// </summary>
        /// <param name="c1Card"></param>
        /// <param name="c1CardBo"></param>
        public ParentalLeave(C1Card c1Card, IC1CardBo c1CardBo) : base(c1Card, c1CardBo)
        {
        }

        /// <summary>
        /// 計算家庭照顧假的可申請期間（最早及最晚可請假日期）
        /// 家庭照顧假使用預設的計算邏輯，沒有特殊的時間限制
        /// 通常家庭照顧假的使用期限由系統設定或公司政策決定
        /// </summary>
        /// <param name="date">參考日期（通常為申請日期）</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>Tuple包含最早可請假日期及最晚可請假日期</returns>
        public override (DateTime, DateTime) CalculateLeavePermittedPeriod(DateTime date, string empNo) => DefaultCalculateLeavePermittedPeriod(date, empNo);

        /// <summary>
        /// 檢查是否能請此假
        /// </summary>
        /// <returns></returns>
        internal override CardCheckResult CanTakeThisLeave()
        {
            // 檢查員工是否已經建立休假記錄
            // 育嬰假需要有既存的休假記錄作為基礎
            if (!IfLeaveRecordExists())
            {
                return ResultLeaveRecordNotCreatedYet;
            }

            // 所有條件都通過，允許申請
            return ResultOk;
        }

        /// <summary>
        /// 檢查必要欄位是否已填寫
        /// 繼承父類別的基本欄位檢查邏輯
        /// </summary>
        /// <returns>檢查結果，包含錯誤代碼、狀態與訊息</returns>
        internal override CardCheckResult CheckRequiredFields()
        {
            // 執行父類別的基本必要欄位檢查
            var result = base.CheckRequiredFields();
            if (result != ResultOk)
            {
                return result;
            }

            return ResultOk;
        }

        /// <summary>
        /// 檢查是否超過允許的育嬰假時數額度
        /// </summary>
        /// <returns>檢查結果，包含狀態碼、狀態及錯誤訊息（如有）</returns>
        internal override CardCheckResult CheckOverPermittedLeaveHours()
        {
            // 取得育嬰假的年度可用時數（上限）
            var available = GetParentalLeaveYearAvailableHours();
            // 取得育嬰假的年度已使用時數
            var used = GetParentalLeaveYearUsedHours();

            // 檢查本次申請時數加上已使用時數是否超過可用時數
            if (used + TotalHours > available)
            {
                // 建立超額錯誤結果，包含詳細的時數資訊
                return new CardCheckResult(CodeExceedQuota, _statusExceedQuota,
                    string.Format(_messagePatternExceedQuota, TotalHours, available, used));
            }

            // 額度檢查通過
            return ResultOk;
        }

        /// <summary>
        /// 取得員工年度已使用育嬰假時數
        /// 計算從年初至今已申請的育嬰假總時數
        /// </summary>
        /// <returns>年度已使用育嬰假時數</returns>
        private int GetParentalLeaveYearUsedHours()
        {
            return _c1CardBo.GetParentalLeaveYearUsedHours(_c1Card.EmpNo, _c1Card.StartDate);
        }

        /// <summary>
        /// 取得員工育嬰假的年度可用時數（上限）
        /// 此方法委派給業務邏輯物件來查詢員工在指定年度的育嬰假時數上限
        /// </summary>
        /// <returns>員工育嬰假的年度可用時數（以小時為單位）</returns>
        private int GetParentalLeaveYearAvailableHours()
        {
            // 委派給業務邏輯物件來取得員工的育嬰假年度可用時數
            // 參數包括員工編號和請假起始日期，用於確定適用的年度和政策
            return _c1CardBo.GetParentalLeaveYearAvailableHours(_c1Card.EmpNo, _c1Card.StartDate);
        }

        /// <summary>
        /// 此性別是否可請此假別
        /// </summary>
        /// <param name="gender"></param>
        /// <returns></returns>
        public static bool IsAllowForThisGender(Gender gender)
        {
            return true;
        }
    }
}
