﻿//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;

//namespace Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.PositionType
//{
//    internal class Chairman : B1CardPositionBase
//    {
//        public string _employeeNumber;
//        public DateTime _overtimeDate;
//        public IB1CardDataProvider _dataProvider;

//        public Chairman(string employeeNumber, DateTime overtimeDate, IB1CardDataProvider dataProvider) :
//            base(employeeNumber, overtimeDate, dataProvider)
//        {
//            _employeeNumber = employeeNumber;
//            _overtimeDate = overtimeDate;
//            _dataProvider = dataProvider;
//        }
//    }
//}