﻿using System;
using System.Collections.Generic;
using System.Data;

namespace Sinotech.Mis.Common
{
    /// <summary>
    /// 員工基本資料 Tiny 版本
    /// </summary>
    public class EmployeeTiny
    {
        /// <summary>
        /// 員工編號
        /// </summary>
        public string EmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 中文姓名
        /// </summary>
        public string CName { get; set; } = string.Empty;


        /// <summary>
        /// 將 List Employee 轉換為 List EmployeeTiny 物件
        /// </summary>
        /// <param name="list">Employee list</param>
        /// <returns>EmployeeTiny list</returns>
        public static List<EmployeeTiny> EmployeeToEmployeeTiny(List<Employee> list)
        {
            List<EmployeeTiny> result = new List<EmployeeTiny>();
            foreach (var item in list)
            {
                result.Add(EmployeeToEmployeeTiny(item));
            }
            return result;
        }

        /// <summary>
        /// 將 DataRow 轉換為 EmployeeTiny 物件
        /// </summary>
        /// <param name="row"></param>
        /// <returns></returns>
        public static EmployeeTiny FromDataRow(DataRow row)
        {
            EmployeeTiny employee = new EmployeeTiny();
            if (row.Table.Columns.Contains("EmpNo") && row["EmpNo"] != DBNull.Value)
            {
                employee.EmpNo = (string)row["EmpNo"];
            }
            if (row.Table.Columns.Contains("CName") && row["CName"] != DBNull.Value)
            {
                employee.CName = (string)row["CName"];
            }
            return employee;
        }

        /// <summary>
        /// 將 Employee 物件轉換為 EmployeeTiny 物件
        /// </summary>
        /// <param name="employee">要轉換的員工物件</param>
        /// <returns>轉換後的 EmployeeTiny 物件</returns>
        public static EmployeeTiny EmployeeToEmployeeTiny(Employee employee)
        {
            if (employee == null)
                return null!;

            return new EmployeeTiny
            {
                EmpNo = employee.EmpNo,
                CName = employee.CName
            };
        }
    }
}
