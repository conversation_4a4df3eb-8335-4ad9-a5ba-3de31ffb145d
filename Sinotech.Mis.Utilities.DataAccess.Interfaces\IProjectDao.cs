﻿using System;
using System.Data;

namespace Sinotech.Mis.Utilities.DataAccess.Interfaces
{
    /// <summary>
    ///   <para>計畫資料存取介面</para>
    /// </summary>
    public interface IProjectDao
    {

        /// <summary>
        /// 取得現有部門的所有計畫
        /// </summary>
        /// <returns></returns>
        public DataTable GetAllDepartmentsProjects();

        /// <summary>
        /// 取得各部門所有計畫
        /// 會將優先部門放在前面
        /// </summary>
        ///<param name="deptNo">優先部門代號，0 表示不將優先部門置前</param>
        /// <returns></returns>
        public DataTable GetAllDepartmentsProjects(int deptNo);

        /// <summary>
        /// 取得所有計畫
        /// </summary>
        /// <returns></returns>
        public DataTable GetAllProjects();


        /// <summary>取得計畫結案日期</summary>
        /// <param name="projectNumber">The project number.</param>
        /// <returns></returns>
        public DateTime? GetEndDate(string projectNumber);

        /// <summary>取得計畫填報截止日</summary>
        /// <param name="projectNumber">The project number.</param>
        /// <returns></returns>
        public DateTime? GetSubmitDueDate(string projectNumber);

        ///<summary>查詢指定期間內已成立及未結案計畫。(查詢結束日期(含)前成立 且 起始日期後結案或未結案 之計畫)</summary>
        ///<param name="startDate">起始日期</param>
        ///<param name="endDate">結束日期</param>
        ///<returns>DataTable(PrjNo：計畫編號、PrjName：計畫名稱、BDate：成立日期、EDate：結案日期、MainDeptNo：主辦部門代號、DeptSName：主辦部門名稱)</returns>
        public DataTable GetOpenProjectsDateRange(DateTime startDate, DateTime endDate);

        ///<summary>
        /// 查詢指定期間內已成立及未結案計畫。(查詢結束日期(不含)前成立 且 起始日期後結案或未結案 之計畫)
        /// 會將優先部門放在前面
        ///</summary>
        ///<param name="startDate">起始日期</param>
        ///<param name="endDate">結束日期</param>
        ///<param name="deptNo">優先部門代號，0 表示不將優先部門置前</param>
        ///<returns>DataTable(PrjNo：計畫編號、PrjName：計畫名稱、BDate：成立日期、EDate：結案日期、MainDeptNo：主辦部門代號、DeptSName：主辦部門名稱)</returns>
        public DataTable GetOpenProjectsDateRange(DateTime startDate, DateTime endDate, int deptNo);

        /// <summary>Gets the project by project number.</summary>
        /// <param name="projectNumber">The project number.</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public DataTable GetProject(string projectNumber);

        /// <summary>取得計畫名稱</summary>
        /// <param name="projectNumber">The project number.</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public string GetProjectName(string projectNumber);

    }
}
