﻿using Sinotech.Mis.Helpers;
using Sinotech.Mis.Utilities.DataAccess.Interfaces;
using System.Data;
using System.Data.SqlClient;

namespace Sinotech.Mis.Utilities.DataAccess.Ado
{

    /// <summary>SinoDAMS資料存取元件</summary>
    public class SinoSignDao : ISinoSignDao
    {
        private readonly string _connectionString;


        /// <summary>Initializes a new instance of the <see cref="SinoSignDao" /> class.</summary>
        /// <param name="connectionString">The connection string.</param>
        public SinoSignDao(string connectionString)
        {
            _connectionString = connectionString;
        }

        /// <summary>
        /// 取得使用者所有的代理人
        /// </summary>
        /// <param name="empNo"></param>
        /// <returns></returns>
        public DataTable GetDeputies(string empNo)
        {
            string sql = @"SELECT DISTINCT userdeputy.deputyuid, [user].name FROM userdeputy 
INNER JOIN [user] ON(userdeputy.deputyuid=[user].empno) 
WHERE (userdeputy.uid=@EMPNO) 
AND (userdeputy.sdate <= GETDATE())
AND ((userdeputy.edate IS NULL) OR (userdeputy.edate > GETDATE()) )
AND ([user].isStop=0);";
            SqlParameter parameter = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            parameter.Value = empNo;
            return SqlHelper.GetDataTable(_connectionString, sql, parameter);
        }

        /// <summary>
        /// 取得角色名稱資料表
        /// </summary>
        /// <returns></returns>
        public DataTable GetRoleGroupNames() 
        {
            string sql = @"SELECT id, name, uid, pid, modifyuid, modifydate FROM [group] WHERE (id <> 'X');";
            return SqlHelper.GetDataTable(_connectionString, sql);
        }

        /// <summary>
        /// 取得使用者所有的角色
        /// </summary>
        /// <param name="empNo"></param>
        /// <returns></returns>
        public DataTable GetUserRoles(string empNo)
        {
            DataTable result = new DataTable();
            if (empNo != null)
            {
                string sqlStr = @"SELECT DISTINCT userdeputy.uid, userdeputy.modifyuid, 
[user].name FROM userdeputy INNER JOIN [user] ON (userdeputy.uid=[user].empno)
WHERE (userdeputy.deputyuid=@EmpNo) 
   AND (userdeputy.sdate <= GETDATE()) 
   AND ((userdeputy.edate IS NULL) OR (userdeputy.edate > GETDATE()))
   AND ([user].isStop=0)
UNION SELECT empno AS uid, 'DB' AS modifyuid, [name] FROM [user] WHERE empno=@EmpNo;";
                SqlParameter parameter = new SqlParameter("@EmpNo", SqlDbType.VarChar);
                parameter.Value = empNo;
                result = SqlHelper.GetDataTable(_connectionString, sqlStr, parameter);
            }
            return result;
        }

        /// <summary>
        /// 取得使用者角色
        /// </summary>
        /// <param name="role">角色</param>
        /// <returns>員工編號 DataTable</returns>
        public DataTable GetRoleUsers(string role)
        {
            DataTable result = new DataTable();
            if (role != null)
            {
                string sqlStr = @"SELECT userdeputy.id, userdeputy.uid, userdeputy.deputyuid, userdeputy.sdate, userdeputy.edate, userdeputy.remark, 
 userdeputy.modifyuid, userdeputy.modifydate, [user].name, [user].email, [user].dutyName, [user].duty
FROM userdeputy INNER JOIN  [user] ON userdeputy.deputyuid = [user].empno
WHERE (userdeputy.uid = @Role) AND (userdeputy.sdate <= GETDATE()) AND (userdeputy.edate IS NULL OR
 userdeputy.edate > GETDATE()) AND ([user].email is not null) AND ([user].isStop=0);";
                SqlParameter parameter = new SqlParameter("@Role", SqlDbType.VarChar);
                parameter.Value = role;
                result = SqlHelper.GetDataTable(_connectionString, sqlStr, parameter);
            }
            return result;
        }

        /// <summary>
        /// 取得角色的預設員工
        /// </summary>
        /// <param name="role">角色</param>
        /// <returns>員工編號 DataTable</returns>
        public DataTable GetRoleDefaultUser(string role)
        {
            DataTable result = new DataTable();
            if (role != null)
            {
                string sqlStr = @"SELECT userdeputy.id, userdeputy.uid, userdeputy.deputyuid, userdeputy.sdate, userdeputy.edate, userdeputy.remark, 
 userdeputy.modifyuid, userdeputy.modifydate, [user].name, [user].email, [user].dutyName, [user].duty
FROM userdeputy INNER JOIN  [user] ON userdeputy.deputyuid = [user].empno
WHERE (userdeputy.uid = @Role) AND (userdeputy.sdate <= GETDATE()) 
 AND ( (userdeputy.edate IS NULL) OR  (userdeputy.edate > GETDATE()) )
 AND (userdeputy.modifyuid = 'DB') 
 AND ([user].email is not null) 
 AND ([user].isStop=0);";
                SqlParameter parameter = new SqlParameter("@Role", SqlDbType.VarChar);
                parameter.Value = role;
                result = SqlHelper.GetDataTable(_connectionString, sqlStr, parameter);
            }
            return result;
        }

        /// <summary>
        /// 取得部門收發員工編號資料表
        /// </summary>
        /// <param name="deptNo">部門編號</param>
        /// <returns>部門收發員工編號 DataTable</returns>
        public DataTable GetDepartmentMailroom(int deptNo)
        {
            string role = deptNo.ToString("00");
            string sql = @$"SELECT userdeputy.uid, userdeputy.deputyuid,[user].name, [user].empno, [user].email
FROM userdeputy INNER JOIN [user] ON userdeputy.deputyuid = [user].empno
WHERE (userdeputy.uid=@Role) 
AND (userdeputy.edate > GETDATE() OR userdeputy.edate IS NULL)
AND ([user].isStop=0) 
ORDER BY userdeputy.uid, userdeputy.deputyuid;";
            SqlParameter parameter = new SqlParameter("@Role", SqlDbType.VarChar);
            parameter.Value = role;
            DataTable result = SqlHelper.GetDataTable(_connectionString, sql, parameter);
            return result;
        }
    }
}
