﻿using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Data;

namespace Sinotech.Mis.HR.Attendance.DataAccess.Interfaces
{
    public interface IA1CardDao
    {

        /// <summary>
        /// 取得 表單關係人 某月份正常工時單
        /// </summary>
        /// <param name="empNo">表單關係人 員工編號</param>
        /// <param name="date">日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns>表單</returns>
        public DataTable GetA1CardMonth(string empNo, DateTime date, int? status = null);

        /// <summary>
        /// 取得A1Cards
        /// </summary>
        /// <param name="formUID">表單識別碼</param>
        /// <returns></returns>
        public DataTable GetA1Cards(Guid formUID);

        /// <summary>
        /// 依填卡日期區間取得所有旬卡
        /// </summary>
        /// <param name="startDate">最早填卡日期</param>
        /// <param name="endDate">最晚填卡日期</param>
        /// <returns>日期區間內所有旬卡</returns>
        public DataTable GetA1Cards(DateTime startDate, DateTime endDate);

        /// <summary>
        /// 依填卡日期區間取得某計畫所有旬卡
        /// </summary>
        /// <param name="startDate">最早填卡日期</param>
        /// <param name="endDate">最晚填卡日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>日期區間內所有旬卡</returns>
        public DataTable GetA1Cards(DateTime startDate, DateTime endDate, string projNo);

        /// <summary>
        /// 依日期區間取得所有表單及旬卡
        /// </summary>
        /// <param name="startDate">最早填卡日期</param>
        /// <param name="endDate">最晚填卡日期</param>
        /// <returns>日期區間內所有表單及旬卡</returns>
        public DataTable GetA1CardsForms(DateTime startDate, DateTime endDate);

        /// <summary>
        /// 依日期區間取得某計畫所有表單及旬卡
        /// </summary>
        /// <param name="startDate">最早填卡日期</param>
        /// <param name="endDate">最晚填卡日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>日期區間內某計畫所有表單及旬卡</returns>
        public DataTable GetA1CardsForms(DateTime startDate, DateTime endDate, string projNo);

        /// <summary>
        /// 取得 填表人/表單關係人 某月份已填正常工時單與表單，依表單年月為準，非填表時間
        /// </summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <param name="status">表單狀態</param>
        /// <returns>已填表單</returns>
        public DataTable GetSentFormA1CardYearMonth(string empNo, int year, int month, int? status = null);

        /// <summary>
        /// 檢查某員工是否已填某旬之有效（簽核中及同意）正常工作卡
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="chineseYearMonth">民國年月(yyymm)</param>
        /// <param name="tenDays">旬別(1:上旬、2:中旬、3:下旬)</param>
        public DataTable IsFilledA1Card(string empNo, string chineseYearMonth, char tenDays);

        /// <summary>
        /// 更新A1Card計畫編號
        /// </summary>
        /// <param name="a1Card"></param>
        /// <returns></returns>
        public bool UpdateA1CardProjectNo(A1CardDto a1Card);

    }
}
