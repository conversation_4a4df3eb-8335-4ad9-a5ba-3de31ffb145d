﻿using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sinotech.Mis.Common;
using Sinotech.Mis.Helpers;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.DataAccess.Interfaces;
using Sinotech.Mis.HR.Attendance.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Runtime.Caching;
using System.Threading;
using System.Threading.Tasks;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic
{
    /// <summary>
    /// 加班申請卡 商業物件
    /// </summary>
    public class B1CardAppBo : IB1CardAppBo
    {

        private static ObjectCache _cache = MemoryCache.Default;
        private static readonly object _cacheLock = new object();
        // 避免重覆送出鎖定物件
        private static readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);
        private readonly IAttendanceBo _attendanceBo;
        private readonly IB1CardAppDao _b1CardAppDao;
        private readonly IEmployeeBo _employeeBo;
        private readonly IFormBo _formBo;
        private readonly IFormFlowBo _formFlowBo;
        private readonly ILogger<B1CardAppBo> _logger;
        private readonly IOvertimeBo _overtimeBo;
        private readonly IProjectBo _projectBo;
        private readonly IWorkdayBo _workdayBo;

        /// <summary>
        ///   <para>
        ///     <see cref="B1CardAppBo" /> 的建構函式 </para>
        /// </summary>
        /// <param name="attendanceBo"></param>
        /// <param name="employeeBo">The employee bo</param>
        /// <param name="formBo"></param>
        /// <param name="formFlowBo"></param>
        /// <param name="b1CardAppDao"></param>
        /// <param name="overtimeBo"></param>
        /// <param name="projectBo"></param>
        /// <param name="logger">The logger.</param>
        /// <param name="workdayBo"></param>
        public B1CardAppBo(IAttendanceBo attendanceBo, IEmployeeBo employeeBo, IFormBo formBo,
            IFormFlowBo formFlowBo, IB1CardAppDao b1CardAppDao, IProjectBo projectBo,
            IOvertimeBo overtimeBo, ILogger<B1CardAppBo> logger, IWorkdayBo workdayBo)
        {
            _b1CardAppDao = b1CardAppDao;
            _attendanceBo = attendanceBo;
            _employeeBo = employeeBo;
            _formBo = formBo;
            _formFlowBo = formFlowBo;
            _projectBo = projectBo;
            _overtimeBo = overtimeBo;
            _logger = logger;
            _workdayBo = workdayBo;
        }

        /// <summary>
        /// 檢查員工是否可以加班
        /// </summary>
        /// <param name="empNo"></param>
        /// <param name="result"></param>
        /// <returns></returns>
        private B1CardAppCheckResult CanEmployeeWorkOvertime(string empNo, B1CardAppCheckResult result)
        {
            if (_employeeBo.IsAboveDeputyManager(empNo))
            {
                result = SetInvalidResult(result, AttendanceParameters.AboveDeputyManagerCannotWorkOvertime);
            }
            return result;
        }

        /// <summary>
        /// 檢查加會人員
        /// </summary>
        /// <param name="b1CardApp"></param>
        /// <param name="result"></param>
        /// <returns></returns>
        private B1CardAppCheckResult CheckAddSigners(B1CardApp b1CardApp, B1CardAppCheckResult result)
        {
            if (result.IsOvertimeAllowed && result.IsValid && result.ErrorMessage == string.Empty && b1CardApp.AddSigners != null)
            {
                if (b1CardApp.AddSigners.EndsWith(','))
                {
                    b1CardApp.AddSigners = b1CardApp.AddSigners.Substring(0, b1CardApp.AddSigners.Length - 1);
                }
                string[] signers = b1CardApp.AddSigners.Split(',');
                foreach (string s in signers)
                {
                    string x = s.Trim();
                    if (x.Length > 0)
                    {
                        if (x == b1CardApp.B1_EmpNo)
                        {
                            result.IsValid = false;
                            result.ErrorMessage = "不得加會申請人";
                        }
                        string name = _employeeBo.GetEmployeeName(x);
                        if (string.IsNullOrWhiteSpace(name))
                        {
                            result.IsValid = false;
                            result.ErrorMessage = $"加會人員{x}不存在";
                        }
                    }
                }
            }
            return result;
        }

        /// <summary>
        /// 檢查指定結果中的日期類型，並根據情況更新與加班申請相關的警告或錯誤訊息。
        /// </summary>
        /// <param name="result">包含要檢查並更新適用警告或錯誤的日期詳細資料的結果物件。
        /// 不能為 null。</param>
        /// <remarks>此方法會針對假日、休息日及天災日設定特定的警報訊息，以引導正確的加班申請。回傳結果的 DateAlarmMessage 屬性可能被修改，或針對特定日期類型將結果標記為無效。</remarks>
        /// <returns>一個 B1CardAppCheckResult 物件，其已根據日期類型更新警告或錯誤訊息。</returns>
        private static B1CardAppCheckResult CheckDayTypeWarnings(B1CardAppCheckResult result)
        {
            if (result.DayDetail != null)
            {
                switch (result.DayDetail.DayType)
                {
                    case WorkdayType.SaturdayHoliday:
                    case WorkdayType.SaturdayRestday:
                        result.DateAlarmMessage = AttendanceParameters.SaturdayOvertimeCannotOverApplyHours;
                        break;
                    case WorkdayType.WeekHoliday:
                    case WorkdayType.MakeUpHoliday:
                        result.DateAlarmMessage = AttendanceParameters.HolidayB1CardAppWarning;
                        break;
                    case WorkdayType.SaturdayNaturalDisasterDay:
                    case WorkdayType.WeekNaturalDisasterDay:
                        result = SetInvalidResult(result, AttendanceParameters.DisasterDayWorkOvertimeError);
                        break;
                }
            }
            return result;
        }

        /// <summary>
        /// 檢查每月加班上限
        /// </summary>
        /// <param name="b1CardApp"></param>
        /// <param name="result"></param>
        /// <param name="overtimeData"></param>
        /// <param name="totalOvertimeHours"></param>
        /// <param name="monthOvertimeHoursLimit"></param>
        private B1CardAppCheckResult CheckMonthError(B1CardApp b1CardApp, B1CardAppCheckResult result, OvertimeData overtimeData, ref double totalOvertimeHours, ref double monthOvertimeHoursLimit)
        {
            Employee? employee = _employeeBo.GetEmployeeDetail(b1CardApp.B1_EmpNo);
            if (result.Employee != null && employee?.IsDriver == true)
            {
                // 司機每月上限
                monthOvertimeHoursLimit = AttendanceParameters.DriverMonthOvertimeHoursLimit;
            }
            else if (overtimeData.IsSpecialStaff)
            {
                //特殊人員每月加班上限不同
                monthOvertimeHoursLimit = overtimeData.SpecialStaffAllowedMonthWeightedOvertimeHours;

                //特殊人員須採用加權小時
                double paidHour = _overtimeBo.GetPaidHour(b1CardApp);
                totalOvertimeHours = overtimeData.SpecialStaffCurrentMonthWeightedOvertimeHours + paidHour; // 加上此次申報時數加權值
            }

            if (totalOvertimeHours > monthOvertimeHoursLimit)
            {
                result.IsOvertimeAllowed = false;
                result.IsValid = false;
                result.HoursErrorMessage = AttendanceParameters.B1CardAppAboveMonthOvertimeLimitError.Replace("{hours}", monthOvertimeHoursLimit.ToString());
                if (overtimeData.IsSpecialStaff)
                {
                    result.HoursErrorMessage = AttendanceParameters.SpecialStaffAboveMonthOvertimeLimitError;
                }
                result.ErrorMessage = result.HoursErrorMessage;
            }
            return result;
        }

        /// <summary>
        /// 檢查累計加班時數上限
        /// </summary>
        /// <param name="date"></param>
        /// <param name="empNo"></param>
        /// <param name="result"></param>
        /// <returns></returns>
        private B1CardAppCheckResult CheckOvertimeLimits(DateTime date, string empNo, B1CardAppCheckResult result)
        {
            OvertimeData overtimeData = _overtimeBo.GetOvertimeData(date, empNo);
            double monthOvertimeHours = overtimeData.MonthOvertimeStatics.TotalHours;
            double totalOvertimeHours = monthOvertimeHours;
            double monthOvertimeHoursLimit = AttendanceParameters.MonthOvertimeHoursLimit;
            Employee? employee = _employeeBo.GetEmployeeDetail(empNo);
            if (employee == null)
            {
                result = SetInvalidResult(result, "員工編號有誤");
                return result;
            }
            if (result.Employee != null && employee.IsDriver)
            {
                monthOvertimeHoursLimit = AttendanceParameters.DriverMonthOvertimeHoursLimit;
            }
            else if (overtimeData.IsSpecialStaff)
            {
                monthOvertimeHoursLimit = overtimeData.SpecialStaffAllowedMonthWeightedOvertimeHours;
                totalOvertimeHours = overtimeData.SpecialStaffCurrentMonthWeightedOvertimeHours;
            }

            if (totalOvertimeHours >= monthOvertimeHoursLimit)
            {
                result = SetInvalidResult(result, AttendanceParameters.ReachMonthOvertimeLimitError);
            }

            double quarterOvertimeHours = overtimeData.QuarterlyOvertimeStatics.TotalHours;
            if (quarterOvertimeHours >= AttendanceParameters.QuarterOvertimeHoursLimit)
            {
                result = SetInvalidResult(result, AttendanceParameters.ReachQuarterOvertimeLimitError);
            }

            B1CardPositionEnum position = _attendanceBo.GetPositionType(empNo);
            if (position != B1CardPositionEnum.Driver)
            {
                if (totalOvertimeHours >= AttendanceParameters.MonthCloseToHoursLimit && totalOvertimeHours < AttendanceParameters.MonthOvertimeHoursLimit)
                {
                    result.HoursAlarmMessage = AttendanceParameters.MonthCloseToHoursUpperBoundMessage.Replace("{hours}", totalOvertimeHours.ToString());
                }
                else if (totalOvertimeHours == AttendanceParameters.MonthOvertimeHoursLimit)
                {
                    result = SetInvalidResult(result, AttendanceParameters.ReachMonthOvertimeLimitError);
                }
                else if (quarterOvertimeHours == AttendanceParameters.QuarterOvertimeHoursLimit)
                {
                    result = SetInvalidResult(result, AttendanceParameters.ReachQuarterOvertimeLimitError);
                }
            }
            return result;
        }

        /// <summary>
        /// 檢查每季加班上限
        /// </summary>
        /// <param name="b1CardApp"></param>
        /// <param name="result"></param>
        /// <param name="quarterOvertimeHours"></param>
        /// <param name="quarterOvertimeHoursLimit"></param>
        /// <returns></returns>
        private (double, B1CardAppCheckResult) CheckQuarterError(B1CardApp b1CardApp, B1CardAppCheckResult result, double quarterOvertimeHours, double quarterOvertimeHoursLimit)
        {
            if (result.IsOvertimeAllowed)
            {
                quarterOvertimeHours += b1CardApp.B1_Hour; // 加上此次申報時數
                                                           // 檢查當季時數
                Employee? employee = _employeeBo.GetEmployeeDetail(b1CardApp.B1_EmpNo);
                if (result.Employee != null && employee?.IsDriver == true)
                {
                    // 司機每季上限... 不檢查
                }
                else if (quarterOvertimeHours > quarterOvertimeHoursLimit)
                {
                    result.IsValid = false;
                    result.IsOvertimeAllowed = false;
                    int x = Convert.ToInt32(quarterOvertimeHours - Convert.ToDouble(quarterOvertimeHoursLimit));
                    result.HoursErrorMessage = AttendanceParameters.B1CardAppAboveQuarterOvertimeLimitError.Replace("{hours}", x.ToString()); ;
                    result.ErrorMessage = result.HoursErrorMessage;
                }
            }

            return (quarterOvertimeHours, result);
        }

        /// <summary>
        /// 檢查 是否須 加班申請 警告訊息
        /// </summary>
        /// <param name="b1CardApp">加班申請</param>
        /// <param name="empNo">員工編號</param>
        /// <param name="monthOvertimeHours">當月加班時數</param>
        /// <param name="quarterOvertimeHours">當季加班時數</param>
        /// <param name="monthOvertimeHoursLimit">每月上限時數</param>
        /// <param name="quarterOvertimeHoursLimit">每季上限時數</param>
        private B1CardAppCheckResult CheckWarn(string empNo, B1CardAppCheckResult result,
            double monthOvertimeHours, double quarterOvertimeHours,
            double monthOvertimeHoursLimit, double quarterOvertimeHoursLimit)
        {
            B1CardPositionEnum position = _attendanceBo.GetPositionType(empNo);
            // 檢查是否須警告
            if (result.IsOvertimeAllowed)
            {
                if (position != B1CardPositionEnum.Driver)
                {
                    if (monthOvertimeHours >= AttendanceParameters.MonthCloseToHoursLimit && monthOvertimeHours < AttendanceParameters.MonthOvertimeHoursLimit)
                    {
                        result.HoursAlarmMessage = AttendanceParameters.MonthCloseToHoursUpperBoundMessage.Replace("{hours}", monthOvertimeHours.ToString());
                    }
                    // 因個人隱私，組長也只檢查一般員工的當月上限警告
                    else if (monthOvertimeHours == AttendanceParameters.MonthOvertimeHoursLimit)
                    {
                        result.HoursAlarmMessage = AttendanceParameters.AboveMonthOvertimeLimitWarning;
                    }
                    else if (quarterOvertimeHours == quarterOvertimeHoursLimit)
                    {
                        result.HoursAlarmMessage = AttendanceParameters.ReachQuarterOvertimeLimitWarning;
                    }
                }
                else if (position == B1CardPositionEnum.Driver)
                {
                    if (monthOvertimeHours == monthOvertimeHoursLimit)
                    {
                        result.HoursAlarmMessage = AttendanceParameters.AboveMonthOvertimeLimitWarning;
                    }
                    // 司機沒有每季上限
                }
            }
            return result;
        }

        /// <summary>
        /// Gets the form and cards.
        /// </summary>
        /// <param name="dtFormCards">The form and cards datatable.</param>
        /// <returns></returns>
        private List<FormCard> GetFormCards(DataTable dtFormCards)
        {
            List<FormCard> formCards = [];
            var guids = (from DataRow dr in dtFormCards.Rows
                         select (Guid)dr["FormUID"]).Distinct().OrderBy(FormUID => FormUID);

            foreach (var guid in guids)
            {
                FormCard formCard = new();
                formCard.FormUID = guid;
                B1CardApp card = new();
                formCard.Flows = [];
                DataRow[] drs = dtFormCards.Select($"FormUID='{guid}'");
                if (drs.Length > 0)
                {
                    DataRow row = drs[0];
                    formCard.ID = (int)row["FormIntID"];
                    formCard.FormUID = guid;
                    formCard.FormID = (string)row["FormID"];
                    formCard.FormNo = (string)row["FormNo"];
                    formCard.FormSubject = (string)row["FormSubject"];
                    formCard.FormInfo = (string)row["FormInfo"];
                    formCard.EmpNo = (string)row["EmpNo"];
                    formCard.EmpName = (string)row["EmpName"];
                    formCard.DeptNo = (int)row["DeptNo"];
                    formCard.DeptSName = SqlHelper.GetColumnStringValue(row, "DeptSName");
                    formCard.TeamID = SqlHelper.GetColumnIntValue(row, "TeamID");
                    formCard.TeamCName = SqlHelper.GetColumnStringValue(row, "TeamCName");
                    if (row["RankNo"] != DBNull.Value)
                    {
                        formCard.RankNo = (string)row["RankNo"];
                    }
                    if (row["RankName"] != DBNull.Value)
                    {
                        formCard.RankName = (string)row["RankName"];
                    }

                    formCard.JobNo = SqlHelper.GetColumnStringValue(row, "JobNo");
                    formCard.JobName = SqlHelper.GetColumnStringValue(row, "JobName");

                    formCard.ContentStartTime = SqlHelper.GetColumnDateTimeValue(row, "ContentStartTime");
                    formCard.ContentEndTime = SqlHelper.GetColumnDateTimeValue(row, "ContentEndTime");
                    formCard.CreatedEmpNo = (string)row["CreatedEmpNo"];
                    formCard.CreatedName = (string)row["CreatedName"];
                    formCard.FilledTime = (DateTime)row["FilledTime"];
                    formCard.CreatedTime = (DateTime)row["CreatedTime"];
                    formCard.CreatedIP = (string)row["CreatedIP"];
                    formCard.CreatedHost = SqlHelper.GetColumnStringValue(row, "CreatedHost");

                    formCard.AddedSigner = SqlHelper.GetColumnStringValue(row, "AddedSigner");
                    formCard.AddedSigner = _formBo.AddSignersAddName(formCard.AddedSigner);
                    formCard.StartTime = (DateTime)row["StartTime"];

                    formCard.EndTime = SqlHelper.GetColumnDateTimeValue(row, "EndTime");

                    formCard.FormStatus = (byte)row["FormStatus"];
                    formCard.FormStatusName = (string)row["FormStatusName"];
                    formCard.TotalSteps = (byte)row["TotalSteps"];
                    formCard.CurrentStep = (byte)row["CurrentStep"];
                    int cardID = (int)row["ID"];
                    card.FormUID = guid;
                    card.UpdatedEmpNo = SqlHelper.GetColumnStringValue(row, "UpdatedEmpNo");
                    card.UpdatedName = SqlHelper.GetColumnStringValue(row, "UpdatedName");
                    card.UpdatedTime = SqlHelper.GetColumnDateTimeValue(row, "UpdatedTime");
                    card.UpdatedIP = SqlHelper.GetColumnStringValue(row, "UpdatedIP");
                    card.UpdatedHost = SqlHelper.GetColumnStringValue(row, "UpdatedHost");
                    card.CreatedTime = (DateTime)row["CreatedTime"];
                    card.AddSigners = formCard.AddedSigner;
                    card.ID = cardID;
                    card.B1_FormID = (string)row["B1_FormID"];
                    card.B1_EmpNo = (string)row["B1_EmpNo"];
                    card.B1_DeptNo = (int)row["B1_DeptNo"];
                    card.B1_Date = (DateTime)row["B1_Date"];
                    if (row["B1_DateTypeId"] != DBNull.Value)
                    {
                        card.B1_DateTypeId = (byte)row["B1_DateTypeId"];
                    }
                    card.B1_Hour = (byte)row["B1_Hour"];
                    if (row["B1_PaidHour"] != DBNull.Value)
                    {
                        card.B1_PaidHour = (byte)row["B1_PaidHour"];
                    }
                    string sB1_Code = (string)row["B1_Code"];
                    card.B1_Code = Char.Parse(sB1_Code);
#pragma warning disable CS8601 // Possible null reference assignment.
                    card.B1_PrjNo = SqlHelper.GetColumnStringValue(row, "B1_PrjNo");

                    card.B1_Reason = SqlHelper.GetColumnStringValue(row, "B1_Reason");
                    if (row["B1_IsOverdue"] != DBNull.Value)
                    {
                        card.B1_IsOverdue = (bool)row["B1_IsOverdue"];
                    }
                    card.B1_OverdueReason = SqlHelper.GetColumnStringValue(row, "B1_OverdueReason");
                    card.B1_WritedEmpNo = SqlHelper.GetColumnStringValue(row, "B1_WritedEmpNo");
                    card.B1_UDate = SqlHelper.GetColumnDateTimeValue(row, "B1_UDate");
                    card.B1_ShouldSignEmpNo = SqlHelper.GetColumnStringValue(row, "B1_ShouldSignEmpNo");
                    card.B1_ADate = SqlHelper.GetColumnDateTimeValue(row, "B1_ADate");
                    card.B1_Status = (byte)row["B1_Status"];
#pragma warning restore CS8601 // Possible null reference assignment.
                    if (drs[0]["Step"] != DBNull.Value)
                    {
                        var stepRows = (from DataRow dr in drs select (byte)row["Step"])
                        .Distinct().OrderBy(Step => Step);

                        foreach (byte step in stepRows)
                        {
                            var k = from DataRow dr in drs
                                    where (byte)dr["Step"] == step
                                    select dr;
                            if (k != null && k.Any())
                            {
                                DataRow dr = k.First();
                                FormFlow flow = FormFlow.DataRowToFormFlow(dr);
                                flow.ID = (int)dr["FormFlowID"];
                                flow.FlowStatusName = _formFlowBo.GetFlowStatusName(flow.FlowStatus);
                                formCard.Flows.Add(flow);
                            }
                        }
                    }
                }
                card.SetApplicationType();
                formCard.Card = card;
                _formBo.AddAttachments(dtFormCards, formCard, drs);
                formCards.Add(formCard);
            }
            return formCards;
        }

        /// <summary>
        /// 初始化檢查結果
        /// </summary>
        /// <param name="empNo">員工編號</param>
        private B1CardAppCheckResult InitializeResult(string empNo)
        {
            B1CardAppCheckResult result = new B1CardAppCheckResult { IsValid = true, IsOvertimeAllowed = true };

            if (string.IsNullOrWhiteSpace(empNo) || !_employeeBo.IsEmployee(empNo))
            {
                result = SetInvalidResult(result, "員工編號有誤");
                return result;
            }

            Employee? employee = _employeeBo.GetEmployeeDetail(empNo);
            if(employee == null)
            {
                result = SetInvalidResult(result, "員工編號有誤");
                return result;
            }
            result.Employee = new EmployeeSimple
            {
                EmpNo = employee.EmpNo,
                CName = employee.CName,
                DeptNo = employee.DeptNo,
                DeptSName = employee.DeptSName
            };
            return result;
        }

        /// <summary>
        /// 檢查是否已填報
        /// </summary>
        /// <param name="date">填報日期</param>
        /// <param name="empNo">員工編號</param>
        /// <param name="result"></param>
        /// <returns></returns>
        private B1CardAppCheckResult IsAlreadyFilled(DateTime date, string empNo, B1CardAppCheckResult result)
        {
            FormFilled filled = IsFilledB1CardApp(date, empNo);
            if (filled.FormNo != null)
            {
                result.IsFilled = true;
                result.FilledForm = filled;
                result.DateErrorMessage = AttendanceParameters.OnlyOneB1CardAppPerDay;
                result = SetInvalidResult(result, AttendanceParameters.OnlyOneB1CardAppPerDay);
            }
            return result;
        }

        /// <summary>
        /// 檢查該日期是否可以填報
        /// </summary>
        /// <param name="date">填報日期</param>
        /// <param name="result"></param>
        /// <returns></returns>
        private B1CardAppCheckResult IsDateNotAllowed(DateTime date, B1CardAppCheckResult result)
        {
            string inAllowDays = InAllowOvertimeDays(date);
            if (!string.IsNullOrEmpty(inAllowDays))
            {
                result = SetInvalidResult(result, inAllowDays);
            }
            return result;
        }

        /// <summary>
        /// 設定不可加班時錯誤訊息
        /// </summary>
        /// <param name="result"></param>
        /// <param name="errorMessage">錯誤訊息</param>
        /// <returns></returns>
        private static B1CardAppCheckResult SetInvalidResult(B1CardAppCheckResult result, string errorMessage)
        {
            result.IsOvertimeAllowed = false;
            result.IsValid = false;
            result.ErrorMessage = errorMessage;
            result.UserErrorMessage = errorMessage;
            return result;
        }

        /// <summary>
        /// 設定加班時段上限
        /// </summary>
        /// <param name="result"></param>
        /// <param name="date">填報日期</param>
        /// <returns></returns>
        private B1CardAppCheckResult SetOvertimeBounds(B1CardAppCheckResult result, DateTime date)
        {
            result.HoursLowerBound = _workdayBo.GetMinOvertimeHours(date);
            result.MinPaidOvertimeHours = _workdayBo.GetMinPaidOvertimeHours(date);
            result.HoursUpperBound = _workdayBo.GetMaxOvertimeHours(date);
            return result;
        }

        /// <summary>
        /// 上次填報的加班申請卡
        /// </summary>
        private static B1CardApp _LastCard { get; set; } = new B1CardApp();


        /// <summary>
        /// 快取 10 分鐘
        /// </summary>
        private static CacheItemPolicy CachePolicy
        {
            get
            {
                CacheItemPolicy policy = new CacheItemPolicy();
                policy.AbsoluteExpiration = DateTimeOffset.Now.AddMinutes(2.0);
                return policy;
            }
        }

        /// <summary>新增 加班申請卡</summary>
        /// <param name="creatorId">填表人 員工編號</param>
        /// <param name="b1CardApp">加班申請卡</param>
        /// <param name="ipAddress">IP address</param>
        /// <param name="hostname">Host name</param>
        /// <returns>傳回 B1CardAppCheckResult</returns>
        public async Task<B1CardAppCheckResult> AddB1CardApp(string creatorId, B1CardApp b1CardApp,
            string ipAddress, string hostname)
        {
            B1CardAppCheckResult result = CheckData(b1CardApp);
            if (!result.IsValid)
            {
                return result; // false
            }
            await _semaphore.WaitAsync();
            try
            {
                if (b1CardApp.EasyEquals(_LastCard))
                {
                    result.ErrorMessage = AttendanceParameters.RepeatSubmitForm;
                    result.IsValid = false;
                }
                else
                {
                    Project project = _projectBo.GetProject(b1CardApp.B1_PrjNo);

                    Form form = new Form();
                    form.FormID = "B1CardApp";
                    form.FormUID = Guid.NewGuid();
                    result.FormUID = form.FormUID;
                    form.EmpNo = b1CardApp.B1_EmpNo;
                    Employee? employee = _employeeBo.GetEmployeeDetail(form.EmpNo);
                    Employee? employeeCreator = _employeeBo.GetEmployeeDetail(creatorId);
                    form.EmpName = employee != null ? employee.CName : string.Empty;
                    form.DeptNo = employee != null ? employee.DeptNo : 0;
                    form.DeptSName = employee != null ? employee.DeptSName : string.Empty;
                    form.TeamID = employee?.TeamID;
                    form.TeamCName = employee?.TeamCName;
                    form.RankNo = employee != null ? employee.RankNo : string.Empty;
                    form.RankName = employee != null ? employee.RankName : string.Empty;
                    form.JobNo = employee?.JobNo;
                    form.JobName = employee?.JobName;
                    form.ContentStartTime = b1CardApp.B1_Date;
                    form.ContentEndTime = b1CardApp.B1_Date;
                    form.FormSubject = $"加班申請卡-{form.EmpName}-{CardUtility.RocChineseDateString(b1CardApp.B1_Date)}";
                    form.FormInfo = $"{CardUtility.RocChineseDateString(b1CardApp.B1_Date)}";
                    form.CreatedEmpNo = b1CardApp.B1_WritedEmpNo;
                    form.CreatedName = _employeeBo.GetEmployeeName(form.CreatedEmpNo);
                    form.CreatedTime = b1CardApp.CreatedTime.ToLocalTime();
                    form.FilledTime = b1CardApp.FilledTime.ToLocalTime();
                    form.CreatedIP = ipAddress;
                    form.CreatedHost = hostname;
                    form.AddedSigner = b1CardApp.AddSigners;
                    form.StartTime = DateTime.Now;
                    form.UpdatedTime = form.CreatedTime;
                    form.UpdatedEmpNo = creatorId;
                    form.UpdatedName = employeeCreator?.CName;
                    form.UpdatedIP = ipAddress;
                    form.UpdatedHost = hostname;
                    form.FormStatus = (int)FormStatus.Processing; //簽核狀態：1:簽核中
                    form.CurrentStep = 0; //所有表單都預設為0，讓FormBo自動計算
                    if (b1CardApp.AddSigners != null)
                    {
                        if (b1CardApp.AddSigners.EndsWith(','))
                        {
                            b1CardApp.AddSigners = b1CardApp.AddSigners.Substring(0, b1CardApp.AddSigners.Length - 1);
                        }
                        form.AddedSigner = b1CardApp.AddSigners;
                        string[] signers = b1CardApp.AddSigners.Split(',');
                        _formFlowBo.FlowAddSigners(form, signers);
                    }
                    b1CardApp.B1_IsOverdue = result.IsOverdue;
                    b1CardApp.B1_DateTypeId = (int)_workdayBo.GetDayType(b1CardApp.B1_Date);
                    b1CardApp.FormUID = form.FormUID;
                    b1CardApp.UpdatedTime = form.CreatedTime;
                    b1CardApp.UpdatedEmpNo = creatorId;
                    b1CardApp.UpdatedName = employeeCreator?.CName;
                    b1CardApp.UpdatedIP = ipAddress;
                    b1CardApp.UpdatedHost = hostname;
                    //計算 B1_PaidHour
                    b1CardApp.B1_PaidHour = _attendanceBo.GetPaidHour(b1CardApp.B1_Date, b1CardApp.B1_Hour);

                    // 若計畫屬於其他部門
                    _formBo.AddFlowByProject(form, project);

                    // 徹底去除重覆關卡，避免漏網之魚
                    form.Flows = _formFlowBo.FlowDedup(form.Flows);

                    // 申請人部門主管，必須在最後一關
                    _formFlowBo.FlowAddManager(form, employee);

                    //呼叫 FormBO 寫入資料庫
                    form.TotalSteps = form.Flows.Count;
                    result.ErrorMessage = _formBo.AddForm(form, this, b1CardApp);
                    if (result.ErrorMessage == string.Empty)
                    {
                        _LastCard = b1CardApp.EasyClone();
                    }
                }
            }
            catch (Exception ex)
            {
                result.ErrorMessage = AttendanceParameters.SubmitErrorMessage;
                string formJson = JsonConvert.SerializeObject(b1CardApp, Formatting.Indented);
                _logger.LogError(ex, "Action: {Action} 發生錯誤，錯誤訊息: {Message} {StackTrace}，內容為 {formJson}", nameof(AddB1CardApp), ex.Message, ex.StackTrace, formJson);
                Console.WriteLine($"{DateTime.Now} AddB1CardApp 發生錯誤 {ex.Message} {ex.StackTrace}");
            }
            finally
            {
                _semaphore.Release();
            }
            return result;
        }

        /// <summary>依照表單資料補足三卡資料 A1Card/B1Card/B1CardApp/C1Card</summary>
        /// <param name="form"></param>
        /// <param name="card">The card.</param>
        /// <returns></returns>
        public bool AmendCard(Form form, CardBase card)
        {
            B1CardApp b1CardApp = (B1CardApp)card;
            b1CardApp.B1_FormID = form.FormNo;
            SetApplicationType(b1CardApp);
            return true;
        }

        /// <summary>B1CardAppDto轉為B1CardApp</summary>
        /// <param name="list">The list.</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public B1CardApp B1CardAppDto2B1CardApp(List<B1CardAppDto> list)
        {
            B1CardApp card = new B1CardApp();
            if (list.Count > 0)
            {
                B1CardAppDto dto = list[0];
                card.ID = dto.ID;
                card.FormUID = dto.FormUID;
                card.B1_FormID = dto.B1_FormID;
                card.B1_EmpNo = dto.B1_EmpNo;
                card.B1_DeptNo = dto.B1_DeptNo;
                card.B1_Date = dto.B1_Date;
                card.B1_DateTypeId = dto.B1_DateTypeId;
                card.B1_Hour = dto.B1_Hour;
                card.B1_PaidHour = dto.B1_PaidHour;
                card.B1_Code = dto.B1_Code;
                card.B1_PrjNo = dto.B1_PrjNo;
                card.B1_Reason = dto.B1_Reason;
                card.B1_IsOverdue = dto.B1_IsOverdue;
                card.B1_OverdueReason = dto.B1_OverdueReason;
                card.B1_WritedEmpNo = dto.B1_WritedEmpNo;
                card.B1_WDate = dto.B1_WDate;
                card.B1_UpdatedEmpNo = dto.B1_UpdatedEmpNo;
                card.B1_UDate = dto.B1_UDate;
                card.B1_ShouldSignEmpNo = dto.B1_ShouldSignEmpNo;
                card.B1_ADate = dto.B1_ADate;
                card.B1_Status = dto.B1_Status;
                card.RequisitionID = dto.RequisitionID;
                card.B1_SOURCE = dto.B1_SOURCE;
                card.UpdatedEmpNo = dto.UpdatedEmpNo;
                card.UpdatedName = dto.UpdatedName;
                card.UpdatedIP = dto.UpdatedIP;
                card.UpdatedHost = dto.UpdatedHost;
                card.UpdatedTime = dto.UpdatedTime;
                card.CreatedTime = dto.B1_WDate;
                SetApplicationType(card);
            }
            return card;
        }

        /// <summary>
        /// 是否能結案抽單 (管理員抽單)
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        public string CanClosedWithdraw(CardBase card)
        {
            B1CardApp b1CardApp = (B1CardApp)card;
            if (b1CardApp.B1_Status == (int)FormStatus.Withdraw)
            {
                return "表單已抽單";
            }
            if (b1CardApp.B1_Status == (int)FormStatus.Processing)
            {
                return "表單進行中";
            }
            return string.Empty;
        }

        /// <summary>
        /// 是否能抽單 (正常情況)
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        public string CanWithdraw(CardBase card)
        {
            return string.Empty;
        }

        /// <summary>是否可以報加班</summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>可以則傳回空字串集合 ，不行則會傳回理由集合</returns>
        public string IsOvertimeAllowed(DateTime date, string empNo)
        {
            string ret = string.Empty;
            if (string.IsNullOrWhiteSpace(empNo) || !_employeeBo.IsEmployee(empNo))
            {
                return "非現職員工不得加班";
            }
            string inAllowDays = InAllowOvertimeDays(date);
            if (string.Empty != inAllowDays) //該日(尚)不准報加班申請卡
            {
                ret = inAllowDays;
            }
            else if (_employeeBo.IsAboveDeputyManager(empNo))
            {
                // 副理以上不准加班
                ret = AttendanceParameters.AboveDeputyManagerCannotWorkOvertime;
            }
            return ret;
        }

        /// <summary>檢查是否為有效的加班申請卡</summary>
        /// <param name="b1CardApp">加班申請卡</param>
        /// <returns>B1CardAppCheckResult</returns>
        public B1CardAppCheckResult CheckData(B1CardApp b1CardApp)
        {
            B1CardAppCheckResult result = DateCanFillB1CardApp(b1CardApp.B1_Date, b1CardApp.B1_EmpNo);
            if (!result.IsOvertimeAllowed)
            {
                return result;
            }
            if (!result.IsValid)
            {
                return result;
            }
            Workday? workday = result.DayDetail;
            if (workday != null)
            {
                b1CardApp.B1_DateTypeId = (int)workday.DayType;
                result.IsValid = true;
            }
            else
            {
                result.IsOvertimeAllowed = false;
                result.IsValid = false;
                result.ErrorMessage = $"{b1CardApp.B1_Date.ToString("yyyy/MM/dd")} 工作日資料未設定";
                return result;
            }

            // 檢查最少時數
            //int minHours = _workdayBo.GetMinOvertimeHours(b1CardApp.B1_Date);
            if (b1CardApp.B1_Hour == 0)
            {
                result.IsValid = false;
                result.HoursErrorMessage = AttendanceParameters.HoursAreZero;
                result.ErrorMessage = result.HoursErrorMessage;
                return result;
            }

            if (b1CardApp.B1_Hour < 1)
            {
                result.IsValid = false;
                result.ErrorMessage = AttendanceParameters.B1CardAtLeastOneHour;
            }

            // 檢查最大時數
            int maxOvertimeHours = _workdayBo.GetMaxOvertimeHours(b1CardApp.B1_Date);
            if (b1CardApp.B1_Hour > maxOvertimeHours)
            {
                result.IsValid = false;
                if (maxOvertimeHours == AttendanceParameters.MaxWorkHours)
                {
                    result.ErrorMessage = $"{workday.TypeName}加班總時數不得超過{AttendanceParameters.MaxWorkHours}小時";
                }
                else
                {
                    result.ErrorMessage = $"每日正常工時{AttendanceParameters.GeneralWorkingHours}小時加延長工時總時數不得超過{AttendanceParameters.MaxWorkHours}小時";
                }
            }

            // 檢查事由
            if (string.IsNullOrWhiteSpace(b1CardApp.B1_Reason))
            {
                result.IsValid = false;
                result.ErrorMessage = "請填寫加班事由";
            }
            // 檢查計畫編號
            if (string.IsNullOrWhiteSpace(b1CardApp.B1_PrjNo))
            {
                result.IsValid = false;
                result.ErrorMessage = "請填寫計畫編號";
            }
            // 檢查計畫編號在B1CardApp的加班日是否結案，前端已先檢查
            Project project = _projectBo.GetProject(b1CardApp.B1_PrjNo);
            if (project == null)
            {
                result.IsValid = false;
                result.ErrorMessage = $"查無計畫編號 {b1CardApp.B1_PrjNo}";
            }

            if (project != null && project.EDate != null && b1CardApp.B1_Date > project.EDate)
            {
                result.IsValid = false;
                string rocDate = CardUtility.RocChineseDateString(b1CardApp.B1_Date);
                result.ErrorMessage = $"計畫編號「{b1CardApp.B1_PrjNo}」在{rocDate}已結案"; // false
            }

            //檢查計畫編號在B1CardApp的申請日是否已截止填報
            if (project != null && project.SubmitDueDate != null && project.SubmitDueDate <= b1CardApp.B1_Date)
            {
                result.ErrorMessage = $"計畫編號「{project.PrjNo}」進度已達100%，填報截止日為{project.SubmitDueDate:yyyy/MM/dd}，在{b1CardApp.B1_Date:yyyy/MM/dd}已截止填報";
                result.IsValid = false;
                return result;
            }

            OvertimeData overtimeData = _overtimeBo.GetOvertimeData(b1CardApp.B1_Date, b1CardApp.B1_EmpNo);
            // 若無其他錯誤，檢查每月/季累計加班時數
            if (result.ErrorMessage == string.Empty && result.IsOvertimeAllowed && result.IsValid)
            {
                #region 檢查每月累計加班時數
                double monthOvertimeHours = overtimeData.MonthOvertimeStatics.TotalHours;

                double totalOvertimeHours = monthOvertimeHours + b1CardApp.B1_Hour; // 加上此次申報時數
                double monthOvertimeHoursLimit = AttendanceParameters.MonthOvertimeHoursLimit; // 一般人每月上限
                result = CheckMonthError(b1CardApp, result, overtimeData, ref totalOvertimeHours, ref monthOvertimeHoursLimit);
                #endregion
                if (!result.IsValid)
                {
                    return result;
                }
                #region 檢查每季累計加班時數
                double quarterOvertimeHours = overtimeData.QuarterlyOvertimeStatics.TotalHours;
                double quarterOvertimeHoursLimit = AttendanceParameters.QuarterOvertimeHoursLimit;
                (quarterOvertimeHours, result) = CheckQuarterError(b1CardApp, result, quarterOvertimeHours, quarterOvertimeHoursLimit);
                #endregion
                if (!result.IsValid)
                {
                    return result;
                }

                #region 檢查警告訊息
                result = CheckWarn(b1CardApp.B1_EmpNo, result, totalOvertimeHours, quarterOvertimeHours, monthOvertimeHoursLimit, quarterOvertimeHoursLimit);
                #endregion
            }

            // 若無其他錯誤，檢查是否足最低認列時數
            if (result.IsValid && result.ErrorMessage == string.Empty && (workday.DayType == WorkdayType.WeekHoliday ||
                    workday.DayType == WorkdayType.MakeUpHoliday) && b1CardApp.B1_Hour < AttendanceParameters.MakeUpHolidayLeastHours)
            {
                result.HoursAlarmMessage += $"\n您填報的預定加班時數為{b1CardApp.B1_Hour}小時，該日加班如不足{AttendanceParameters.MakeUpHolidayLeastHours}小時仍支給{AttendanceParameters.MakeUpHolidayLeastHours}小時加班費";
                result.HoursAlarmMessage = result.HoursAlarmMessage.Trim();
            }

            #region 檢查加會人員
            result = CheckAddSigners(b1CardApp, result);
            if (!result.IsValid)
            {
                return result;
            }
            #endregion

            // 2024/04/25 加上 「週間國定假日及補假日」前8小時不得選擇補休假
            // 檢查 「週間國定假日及補假日」前8小時不得選擇補休假。
            if (result.IsValid && (workday.DayType == WorkdayType.WeekHoliday || workday.DayType == WorkdayType.MakeUpHoliday)
                && b1CardApp.B1_Hour <= 8 && b1CardApp.B1_Code == '3')
            {
                result.IsValid = false;
                result.ErrorMessage = "國定假日8小時內加班別請選填【加班】或【社外加班】";
            }
            // 2024/04/25 加上 「週間國定假日及補假日」前8小時不得選擇補休假 警告訊息
            if (result.IsValid && (workday.DayType == WorkdayType.WeekHoliday || workday.DayType == WorkdayType.MakeUpHoliday)
                && b1CardApp.B1_Hour > 8 && b1CardApp.B1_Code == '3')
            {
                result.HoursAlarmMessage += $"\n{CardUtility.RocChineseDateString(workday.WorkDate)}為國定假日，屆時【加班卡】8小時內加班別請選填【加班】或【社外加班】";
            }
            return result;
        }

        /// <summary>是否可以填加班申請卡</summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>傳回 B1CardAppCheckDateResult</returns>
        public B1CardAppCheckResult DateCanFillB1CardApp(DateTime date, string empNo)
        {
            B1CardAppCheckResult result = InitializeResult(empNo);
            if (!result.IsValid)
            {
                return result;
            }

            result.DayDetail = _workdayBo.GetWorkday(date);
            SetOvertimeBounds(result, date);

            if (result.DayDetail == null)
            {
                result = SetInvalidResult(result, $"{date.ToString("yyyy/MM/dd")} 工作日資料未設定"); // 日期未設定不能填報
                return result;
            }

            result.IsOverdue = IsOverdue(date);
            if (result.IsOverdue)
            {
                result.DateAlarmMessage = "逾期填報";
            }

            result = CanEmployeeWorkOvertime(empNo, result);
            if (!result.IsValid)
            {
                return result;
            }
            // 檢查加班申請卡Date是否為有效
            result = IsDateNotAllowed(date, result);
            if (!result.IsValid)
            {
                return result;
            }
            result = IsAlreadyFilled(date, empNo, result);
            if (!result.IsValid)
            {
                return result;
            }

            result = CheckDayTypeWarnings(result);
            if (!result.IsValid)
            {
                return result;
            }

            result = CheckOvertimeLimits(date, empNo, result);
            return result;
        }

        /// <summary>結案</summary>
        /// <param name="card">The card.</param>
        /// <param name="formStatus">The form status.</param>
        /// <param name="updateDto">更新資料物件</param>
        public void Finish(CardBase card, FormStatus formStatus, Updater updateDto)
        {
            Update(card, updateDto);
            B1CardApp b1CardApp = (B1CardApp)card;
            b1CardApp.B1_Status = (int)formStatus;
            b1CardApp.B1_ADate = updateDto.UpdatedTime;
        }

        /// <summary>
        /// 取得加班類型 B1CODE 列表
        /// </summary>
        /// <returns></returns>
        public List<B1CardType> GetB1CardTypes()
        {
            List<B1CardType> list = new List<B1CardType>();
            string cacheName = "B1CardTypes";
            if (_cache.Contains(cacheName))
            {
                list = (List<B1CardType>)_cache[cacheName];
            }
            else
            {
                DataTable dt = _b1CardAppDao.GetB1CardTypes();
                foreach (DataRow dr in dt.Rows)
                {
                    int typeID = int.Parse((string)dr["ID"]);
                    string typeName = (string)dr["Name"];
                    list.Add(new B1CardType { Type = typeID, TypeName = typeName });
                }
                lock (_cacheLock)
                {
                    _cache.Set(cacheName, list, CachePolicy);
                }
            }
            return list;
        }

        /// <summary>
        /// 取得特定卡
        /// </summary>
        /// <param name="formUID"></param>
        /// <returns></returns>
        public CardBase? GetCard(Guid formUID)
        {
            B1CardApp? b1CardApp = null;
            DataTable dt = _b1CardAppDao.GetB1CardApps(formUID);
            if (dt != null && dt.Rows.Count > 0)
            {
                List<B1CardAppDto> b1CardApps = SqlHelper.ConvertDataTable<B1CardAppDto>(dt);
                b1CardApp = B1CardAppDto2B1CardApp(b1CardApps);
            }
            return b1CardApp;
        }

        /// <summary>取得某段時間內卡，不含流程與附件等</summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <returns>卡 CardsDto 物件 List</returns>
        public List<CardBase> GetCards(DateTime startDate, DateTime endDate)
        {
            List<CardBase> cards = new List<CardBase>();
            DataTable dt = _b1CardAppDao.GetB1CardApps(startDate, endDate);
            if (dt != null && dt.Rows.Count > 0)
            {
                List<B1CardAppDto> b1CardAppDtos = SqlHelper.ConvertDataTable<B1CardAppDto>(dt);
                var formNos = b1CardAppDtos.Select(x => x.B1_FormID).Distinct();
                foreach (var formNo in formNos)
                {
                    var result = (from b1CardAppDto in b1CardAppDtos
                                  where b1CardAppDto.B1_FormID == formNo
                                  select b1CardAppDto).ToList();
                    B1CardApp card = B1CardAppDto2B1CardApp(result);
                    cards.Add(card);
                }
            }
            return cards;
        }

        /// <summary>取得表單及加班申請卡</summary>
        /// <param name="form">The form </param>
        /// <param name="userId">The user identifier.</param>
        /// <returns>表單及加班申請卡 FormCardsDto 物件</returns>
        public FormCard GetFormCard(Form form, string userId)
        {
            FormCard formCard = new FormCard();
            formCard.ID = form.ID;
            formCard.FormID = form.FormID;
            formCard.FormUID = form.FormUID;
            formCard.FormNo = form.FormNo;
            formCard.FormSubject = form.FormSubject;
            formCard.FormInfo = form.FormInfo;
            formCard.EmpNo = form.EmpNo;
            formCard.EmpName = form.EmpName;
            formCard.DeptNo = form.DeptNo;
            formCard.DeptSName = form.DeptSName;
            formCard.TeamID = form.TeamID;
            formCard.TeamCName = form.TeamCName;
            formCard.RankNo = form.RankNo;
            formCard.RankName = form.RankName;
            formCard.JobNo = form.JobNo;
            formCard.JobName = form.JobName;
            formCard.ContentStartTime = form.ContentStartTime;
            formCard.ContentEndTime = form.ContentEndTime;
            formCard.CreatedEmpNo = form.CreatedEmpNo;
            formCard.CreatedName = form.CreatedName;
            formCard.FilledTime = form.FilledTime;
            formCard.CreatedTime = form.CreatedTime;
            formCard.CreatedIP = form.CreatedIP;
            formCard.CreatedHost = form.CreatedHost;
            formCard.StartTime = form.StartTime;
            formCard.EndTime = form.EndTime;
            formCard.FormStatus = form.FormStatus;
            formCard.FormStatusName = _formBo.GetFormStatusName(form.FormStatus);
            formCard.TotalSteps = form.TotalSteps;
            formCard.CurrentStep = form.CurrentStep;
            formCard.UpdatedEmpNo = form.UpdatedEmpNo;
            formCard.UpdatedName = form.UpdatedName;
            formCard.UpdatedTime = form.UpdatedTime;
            formCard.UpdatedIP = form.UpdatedIP;
            formCard.UpdatedHost = form.UpdatedHost;
            formCard.AddedSigner = _formBo.AddSignersAddName(form.AddedSigner);
            formCard.Flows = form.Flows;
            formCard.Hours = 0;

            CardBase? card = GetCard(form.FormUID);
            if (card != null)
            {
                B1CardApp b1Card = (B1CardApp)card;
                SetApplicationType(b1Card);
                b1Card.AddSigners = _formBo.AddSignersAddName(form.AddedSigner);
                formCard.Hours += b1Card.B1_Hour;
                formCard.Card = b1Card;
                formCard.Attachments = form.Attachments;
            }
            return formCard;
        }

        /// <summary>取得某段時間內表單及卡</summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <returns>表單及卡 FormCardsDto 物件 List</returns>
        public List<FormCard> GetFormCards(DateTime startDate, DateTime endDate)
        {
            DataTable dtCardForms = _b1CardAppDao.GetB1CardAppsForms(startDate, endDate);
            List<FormCard> formCards = GetFormCards(dtCardForms);
            return formCards;
        }

        /// <summary>取得某段時間內表單及卡</summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單及卡 FormCardsDto 物件 List</returns>
        public List<FormCard> GetFormCards(DateTime startDate, DateTime endDate, string projNo)
        {
            DataTable dtCardForms = _b1CardAppDao.GetB1CardAppsForms(startDate, endDate, projNo);
            List<FormCard> formCards = GetFormCards(dtCardForms);
            return formCards;
        }

        /// <summary>
        /// 取得總時數
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        public int GetHours(CardBase card)
        {
            int hours = 0;
            B1CardApp b1CardApp = (B1CardApp)card;
            hours = b1CardApp.B1_Hour;
            return hours;
        }

        /// <summary>
        /// 取得提醒資訊
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        public (int, string?) GetListRemindMessage(CardBase card)
        {
            string? ret = null;
            string errorHours = string.Empty;
            int messageType = 0;
            B1CardApp b1CardApp = (B1CardApp)card;
            if (!_employeeBo.IsEmployee(b1CardApp.B1_EmpNo))
            {
                string errorMessage = @"申請人" + AttendanceParameters.IsOffServiceStaff;
                return (99, errorMessage);
            }
            B1CardPositionEnum position = _attendanceBo.GetPositionType(b1CardApp.B1_EmpNo);
            if (position != B1CardPositionEnum.Driver) // 非司機都檢查
            {
                DateTime date = b1CardApp.B1_Date;
                int day = DateTime.DaysInMonth(date.Year, date.Month);
                date = new DateTime(date.Year, date.Month, day);

                string name = _employeeBo.GetEmployeeName(b1CardApp.B1_EmpNo);
                OvertimeData overtimeData = _overtimeBo.GetOvertimeData(date, b1CardApp.B1_EmpNo);
                // 檢查每月累計加班時數
                double monthOvertimeHours = overtimeData.MonthOvertimeStatics.TotalHours;

                // 當月第40~53小時
                if (monthOvertimeHours >= AttendanceParameters.MonthCloseToHoursLimit && monthOvertimeHours < AttendanceParameters.MonthOvertimeHoursLimit)
                {
                    messageType = 1;
                    errorHours = AttendanceParameters.B1CardAppMonthOvertimeSignerWarningA.Replace("{name}", name).Replace("{hours}", monthOvertimeHours.ToString());
                }

                // 已達每月上限 第54小時
                if (monthOvertimeHours >= AttendanceParameters.MonthOvertimeHoursLimit)
                {
                    messageType = 2;
                    errorHours = AttendanceParameters.B1CardAppMonthOvertimeSignerWarningB.Replace("{name}", name);
                }

                // 檢查每季累計加班時數
                double quarterOvertimeHours = overtimeData.QuarterlyOvertimeStatics.TotalHours;
                if (quarterOvertimeHours >= AttendanceParameters.QuarterOvertimeHoursLimit)
                {
                    messageType = 3;
                    errorHours = AttendanceParameters.B1CardAppReachQuarterOvertimeLimitSignerWarning.Replace("{name}", name);
                }
            }
            string errorDayType = string.Empty;
            WorkdayType dayType = (WorkdayType)b1CardApp.B1_DateTypeId;
            switch (dayType)
            {
                case WorkdayType.WeekHoliday:
                case WorkdayType.MakeUpHoliday:
                    if (b1CardApp.B1_Hour < AttendanceParameters.MakeUpHolidayLeastHours)
                    {
                        if (messageType == 0)
                        {
                            messageType = 4;
                        }
                        string typeName = _workdayBo.GetWorkdayTypeName(dayType);
                        string message = AttendanceParameters.HolidayB1CardAppSignerWarning
                            .Replace("{hours}", b1CardApp.B1_Hour.ToString())
                            .Replace("{dayType}", typeName);
                        errorDayType = $"\n{message}";
                    }
                    break;
            }

            string errorOverdue = string.Empty;
            if (IsOverdue(b1CardApp.B1_Date, b1CardApp.B1_WDate))
            {
                messageType = 5;
                errorOverdue = $"\n逾期填報";
            }
            if (messageType != 0)
            {
                ret = $"{errorHours}{errorDayType}{errorOverdue}".Trim();
            }

            return (messageType, ret);
        }

        /// <summary>
        /// 取得員工在特定月份所有卡，依表單日期為準，非填表時間
        /// </summary>
        /// <param name="empNo">填表員工編號</param>
        /// <param name="date">日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns></returns>
        public List<CardBase> GetUserCardsMonth(string empNo, DateTime date, int? status = null)
        {
            List<CardBase> cards = new List<CardBase>();
            DataTable dtB1CardApps = _b1CardAppDao.GetB1CardAppMonth(empNo, date, status);
            if (dtB1CardApps != null && dtB1CardApps.Rows.Count > 0)
            {
                List<B1CardApp> b1CardApps = SqlHelper.ConvertDataTable<B1CardApp>(dtB1CardApps);
                foreach (B1CardApp b1CardApp in b1CardApps)
                {
                    SetApplicationType(b1CardApp);
                    b1CardApp.SetApplicationType();
                    cards.Add(b1CardApp);
                }
            }
            return cards;
        }

        /// <summary>
        /// 取得員工在特定月份已填表單及卡，包括他人代填與本人填表，依表單年月為準，非填表時間
        /// </summary>
        /// <param name="empNo">填表員工編號</param>
        /// <param name="year">表單年</param>
        /// <param name="month">表單月</param>
        /// <returns></returns>
        public List<FormCard> GetUserSentFormCardsYearMonth(string empNo, int year, int month)
        {
            List<FormCard> formCards = new List<FormCard>();
            DataTable dtCardForms = _b1CardAppDao.GetSentB1CardAppYearMonth(empNo, year, month);
            if (dtCardForms != null && dtCardForms.Rows.Count > 0)
            {
                formCards = GetFormCards(dtCardForms);
            }
            return formCards;
        }

        /// <summary>
        /// 是否可填報加班申請卡
        /// </summary>
        /// <param name="date"></param>
        /// <returns></returns>
        public string InAllowOvertimeDays(DateTime date)
        {
            return InAllowOvertimeDays(date, DateTime.Now);
        }

        /// <summary>
        /// 是否可填報加班申請卡，僅測試時直接呼叫，正常使用 InAllowDays(DateTime date)
        /// </summary>
        /// <param name="date"></param>
        /// <param name="now">目前時間</param>
        /// <returns></returns>
        public string InAllowOvertimeDays(DateTime date, DateTime now)
        {
            string ret = string.Empty;
            Workday workday = _workdayBo.GetWorkday(date);
            now = new DateTime(now.Year, now.Month, now.Day, 0, 0, 0, DateTimeKind.Local);
            date = new DateTime(date.Year, date.Month, date.Day, 0, 0, 0, DateTimeKind.Local);
            TimeSpan x = date - now;
            if (date.DayOfWeek == DayOfWeek.Sunday) //星期日不准加班
            {
                ret = $"週日為例假日，不得填報加班申請卡";
            }
            else if (x.Days >= 0 && x.Days > BeforeDaysLimit) // 檢查發生日是否超過限制
            {
                ret = $"僅開放填報當日起 {BeforeDaysLimit} 日內之加班申請卡";
            }
            else if (workday.DayType == WorkdayType.SaturdayNaturalDisasterDay ||
                workday.DayType == WorkdayType.WeekNaturalDisasterDay)
            {
                ret = AttendanceParameters.DisasterDayWorkOvertimeError;
            }
            // 目前取消超出日期限制，仍可填報

#pragma warning disable S125 // Sections of code should not be commented out
            //else if (x.Days < 0 && Math.Abs(x.Days) > AfterDaysLimit)
            //{
            //    if (!_workdayBo.InWorkdays(date, fillDate, AfterDaysLimit))
            //    {
            //        ret = $"您已逾期填報！僅開放補填前 {AfterDaysLimit} 工作日之加班申請卡";
            //    }
            //}
            return ret;
#pragma warning restore S125 // Sections of code should not be commented out
        }


        /// <summary>
        /// 是否已填加班申請卡
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>若未填則傳回 {FormUID: null, FormNo: null}</returns>
        public FormFilled IsFilledB1CardApp(DateTime date, string empNo)
        {
            FormFilled filled = new FormFilled();
            DataTable dt = _b1CardAppDao.IsFilledB1CardApp(date, empNo);
            if (dt != null && dt.Rows.Count > 0)
            {
                filled.FormUID = (Guid)dt.Rows[0]["FormUID"];
                filled.FormNo = (string)dt.Rows[0]["B1_FormID"];
                int iApproved = (int)(byte)dt.Rows[0]["B1_Status"];
                if (iApproved == (int)FormStatus.Agree)
                {
                    filled.IsApproved = true;
                }
            }
            return filled;
        }

        /// <summary>
        /// 是否逾期
        /// </summary>
        /// <param name="date">加班日期</param>
        /// <returns></returns>
        public bool IsOverdue(DateTime date)
        {
            DateTime now = DateTime.Now;
            return IsOverdue(date, now);
        }

        /// <summary>是否逾期，僅測試時直接呼叫，正常使用 InAllowDays(DateTime date)</summary>
        /// <param name="date">加班日期</param>
        /// <param name="fillDate">填報日期</param>
        /// <returns></returns>
        public bool IsOverdue(DateTime date, DateTime fillDate)
        {
            bool ret = false;
            fillDate = new DateTime(fillDate.Year, fillDate.Month, fillDate.Day);
            date = new DateTime(date.Year, date.Month, date.Day);
            TimeSpan x = date - fillDate;
            if (x.Days < 0 && Math.Abs(x.Days) > AfterDaysLimit && !_workdayBo.InWorkdays(date, fillDate, AfterDaysLimit))
            {
                ret = true;
            }
            return ret;
        }
        
        /// <summary>
        /// 設定B1CardApp TypeName
        /// </summary>
        /// <param name="card"></param>
        public void SetApplicationType(B1CardApp card)
        {
            List<B1CardType> cardTypes = GetB1CardTypes();
            int b1Code = int.Parse(card.B1_Code.ToString());
            string? name = (from B1CardType t in cardTypes
                            where t.Type == b1Code
                            select t.TypeName).FirstOrDefault();
            if (name != null)
            {
                card.ApplicationType = name;
            }
        }

        /// <summary>轉換為Stored Procedure所須的 User Defined Table</summary>
        /// <param name="card">三卡</param>
        /// <param name="isAdd">是否為新增</param>
        /// <returns></returns>
        public List<DataTable> ToUserDefinedDataTables(CardBase card, bool isAdd = true)
        {
            List<B1CardAppDto> list = new List<B1CardAppDto>();
            B1CardApp b1CardApp = (B1CardApp)card;
            B1CardAppDto b1CardAppDto = new B1CardAppDto();

            b1CardAppDto.ID = b1CardApp.ID;
            b1CardAppDto.FormUID = b1CardApp.FormUID;
            b1CardAppDto.B1_FormID = b1CardApp.B1_FormID;
            b1CardAppDto.B1_EmpNo = b1CardApp.B1_EmpNo;
            b1CardAppDto.B1_DeptNo = b1CardApp.B1_DeptNo;
            b1CardAppDto.B1_Date = b1CardApp.B1_Date;
            b1CardAppDto.B1_Hour = b1CardApp.B1_Hour;
            b1CardAppDto.B1_DateTypeId = b1CardApp.B1_DateTypeId;
            b1CardAppDto.B1_PaidHour = b1CardApp.B1_PaidHour;
            b1CardAppDto.B1_Code = b1CardApp.B1_Code;
            b1CardAppDto.B1_PrjNo = b1CardApp.B1_PrjNo;
            b1CardAppDto.B1_Reason = b1CardApp.B1_Reason;
            b1CardAppDto.B1_IsOverdue = b1CardApp.B1_IsOverdue;
            b1CardAppDto.B1_OverdueReason = b1CardApp.B1_OverdueReason;
            b1CardAppDto.B1_WritedEmpNo = b1CardApp.B1_WritedEmpNo;
            b1CardAppDto.B1_WDate = b1CardApp.B1_WDate;
            b1CardAppDto.B1_UpdatedEmpNo = b1CardApp.B1_UpdatedEmpNo;
            b1CardAppDto.B1_UDate = b1CardApp.B1_UDate;
            b1CardAppDto.B1_ShouldSignEmpNo = b1CardApp.B1_ShouldSignEmpNo;
            b1CardAppDto.B1_ADate = b1CardApp.B1_ADate;
            b1CardAppDto.B1_Status = b1CardApp.B1_Status;
            b1CardAppDto.RequisitionID = b1CardApp.RequisitionID;
            b1CardAppDto.B1_SOURCE = b1CardApp.B1_SOURCE;
            b1CardAppDto.UpdatedEmpNo = b1CardApp.UpdatedEmpNo;
            b1CardAppDto.UpdatedName = b1CardApp.UpdatedName;
            b1CardAppDto.UpdatedTime = b1CardApp.UpdatedTime;
            b1CardAppDto.UpdatedIP = b1CardApp.UpdatedIP;
            b1CardAppDto.UpdatedHost = b1CardApp.UpdatedHost;
            list.Add(b1CardAppDto);
            DataTable dt = SqlHelper.CreateDataTable<B1CardAppDto>(list);
            dt.TableName = "B1CardApp";
            List<DataTable> dataTables = new List<DataTable> { dt };
            return dataTables;
        }

        /// <summary>更新Card</summary>
        /// <param name="card">The card.</param>
        /// <param name="updateDto">更新資料物件</param>
        public void Update(CardBase card, Updater updateDto)
        {
            B1CardApp b1CardApp = (B1CardApp)card;
            b1CardApp.B1_ADate = updateDto.UpdatedTime;
            if (updateDto.UpdatedIP != null)
            {
                b1CardApp.UpdatedName = updateDto.UpdatedName;
                b1CardApp.UpdatedIP = updateDto.UpdatedIP;
            }
            b1CardApp.UpdatedTime = updateDto.UpdatedTime;
            b1CardApp.UpdatedEmpNo = updateDto.UpdatedEmpNo;
            b1CardApp.UpdatedHost = updateDto.UpdatedHost;
        }

        /// <summary>
        /// 抽單
        /// </summary>
        /// <param name="card"></param>
        /// <param name="withdraw"></param>
        /// <returns></returns>
        public bool Withdraw(CardBase card, Withdraw withdraw)
        {
            bool ret = false;
            if (withdraw != null)
            {
                B1CardApp b1CardApp = (B1CardApp)card;
                b1CardApp.B1_Status = (int)FormStatus.Withdraw;
                b1CardApp.B1_ADate = withdraw.WithdrawTime;
                ret = true;
            }
            return ret;
        }

        /// <summary>
        /// 加班申請卡限制可補申請日數(工作天)
        /// </summary>
        /// <value>
        /// The after days limit.
        /// </value>
        public static int AfterDaysLimit { get; } = 1;

        /// <summary>
        /// 加班申請卡限制可預先申請日數(日曆天)
        /// </summary>
        /// <value>
        /// The before days limit.
        /// </value>
        public static int BeforeDaysLimit { get; } = 30;


    }
}
