import { ref } from 'vue'

export function useTimeInterval() {
  const now = new Date()
  const startTime = ref<Date>(new Date(now.getFullYear(), now.getMonth(), now.getDate() - 30))
  const endTime = ref<Date>(new Date(now.getFullYear(), now.getMonth(), now.getDate()))

  const onSetStartTime = (event: Date): void => {
    startTime.value = event
  }
  
  const onSetEndTime = (event: Date): void => {
    endTime.value = event
  }

  return { startTime, endTime, onSetStartTime, onSetEndTime }
}