﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.4.33205.214
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Sinotech.Mis.HR.Attendance.AttendanceCard", "Sinotech.Mis.HR.Attendance.AttendanceCard.csproj", "{B4708671-D0A6-47FD-858C-860E04DAD080}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Sinotech.Mis.HR.Attendance.Common", "..\Sinotech.Mis.HR.Attendance.Common\Sinotech.Mis.HR.Attendance.Common.csproj", "{4A6B83D1-53AA-43AD-A6A2-3E16A4F0FEA3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Sinotech.Mis.HR.Attendance.BusinessLogic", "..\Sinotech.Mis.HR.Attendance.BusinessLogic\Sinotech.Mis.HR.Attendance.BusinessLogic.csproj", "{20B3F0E3-2988-44DF-92F0-6E29E380F9C3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Sinotech.Mis.HR.Attendance.DataAccess.Interfaces", "..\Sinotech.Mis.HR.Attendance.DataAccess.Interfaces\Sinotech.Mis.HR.Attendance.DataAccess.Interfaces.csproj", "{96C85C75-E76C-4F9C-B230-361EDF039F1B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Sinotech.Mis.HR.Attendance.DataAccess.Ado", "..\Sinotech.Mis.HR.Attendance.DataAccess.Ado\Sinotech.Mis.HR.Attendance.DataAccess.Ado.csproj", "{D1CC61DA-AAA8-4CC9-A6C0-251C96479C47}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Sinotech.Mis.HR.Attendance.Utilities", "..\Sinotech.Mis.HR.Attendance.Utilities\Sinotech.Mis.HR.Attendance.Utilities.csproj", "{7D855820-0D96-481B-A21B-3E0599B833BD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Sinotech.Mis.Common", "..\Sinotech.Mis.Common\Sinotech.Mis.Common.csproj", "{F562637E-2228-4BBC-B24D-4D23E4197761}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Sinotech.Mis.Helpers", "..\Sinotech.Mis.Helpers\Sinotech.Mis.Helpers.csproj", "{326AFCB4-5169-44B6-82BA-6495795EEBA1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Sinotech.Mis.Utilities.DataAccess.Interfaces", "..\Sinotech.Mis.Utilities.DataAccess.Interfaces\Sinotech.Mis.Utilities.DataAccess.Interfaces.csproj", "{B23AF833-3462-4159-8210-7E112F92FA23}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Sinotech.Mis.Utilities.DataAccess.Ado", "..\Sinotech.Mis.Utilities.DataAccess.Ado\Sinotech.Mis.Utilities.DataAccess.Ado.csproj", "{0B0D7A08-FA7D-4C1F-9866-30716794A39A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{FFBA0BD8-43F8-492A-8385-1222CA0D77F5}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Sinotech.Mis.Serilog.Utilities", "..\Sinotech.Mis.Serilog.Utilities\Sinotech.Mis.Serilog.Utilities.csproj", "{CBEF3848-E889-4288-A319-DF03D6B9DD7A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Sinotech.Mis.Extensions.Configuration", "..\Sinotech.Mis.Extensions.Configuration\Sinotech.Mis.Extensions.Configuration.csproj", "{B9D5F251-FB85-4764-840E-A6210E66343D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Sinotech.Mis.HR.Attendance.Extensions.ServiceCollection", "..\Sinotech.Mis.HR.Attendance.Extensions.ServiceCollection\Sinotech.Mis.HR.Attendance.Extensions.ServiceCollection.csproj", "{5E1A538A-3DAD-4E6C-A678-AE870283B143}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{B4708671-D0A6-47FD-858C-860E04DAD080}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B4708671-D0A6-47FD-858C-860E04DAD080}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B4708671-D0A6-47FD-858C-860E04DAD080}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B4708671-D0A6-47FD-858C-860E04DAD080}.Release|Any CPU.Build.0 = Release|Any CPU
		{4A6B83D1-53AA-43AD-A6A2-3E16A4F0FEA3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4A6B83D1-53AA-43AD-A6A2-3E16A4F0FEA3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4A6B83D1-53AA-43AD-A6A2-3E16A4F0FEA3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4A6B83D1-53AA-43AD-A6A2-3E16A4F0FEA3}.Release|Any CPU.Build.0 = Release|Any CPU
		{20B3F0E3-2988-44DF-92F0-6E29E380F9C3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{20B3F0E3-2988-44DF-92F0-6E29E380F9C3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{20B3F0E3-2988-44DF-92F0-6E29E380F9C3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{20B3F0E3-2988-44DF-92F0-6E29E380F9C3}.Release|Any CPU.Build.0 = Release|Any CPU
		{96C85C75-E76C-4F9C-B230-361EDF039F1B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{96C85C75-E76C-4F9C-B230-361EDF039F1B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{96C85C75-E76C-4F9C-B230-361EDF039F1B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{96C85C75-E76C-4F9C-B230-361EDF039F1B}.Release|Any CPU.Build.0 = Release|Any CPU
		{D1CC61DA-AAA8-4CC9-A6C0-251C96479C47}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D1CC61DA-AAA8-4CC9-A6C0-251C96479C47}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D1CC61DA-AAA8-4CC9-A6C0-251C96479C47}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D1CC61DA-AAA8-4CC9-A6C0-251C96479C47}.Release|Any CPU.Build.0 = Release|Any CPU
		{7D855820-0D96-481B-A21B-3E0599B833BD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7D855820-0D96-481B-A21B-3E0599B833BD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7D855820-0D96-481B-A21B-3E0599B833BD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7D855820-0D96-481B-A21B-3E0599B833BD}.Release|Any CPU.Build.0 = Release|Any CPU
		{F562637E-2228-4BBC-B24D-4D23E4197761}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F562637E-2228-4BBC-B24D-4D23E4197761}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F562637E-2228-4BBC-B24D-4D23E4197761}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F562637E-2228-4BBC-B24D-4D23E4197761}.Release|Any CPU.Build.0 = Release|Any CPU
		{326AFCB4-5169-44B6-82BA-6495795EEBA1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{326AFCB4-5169-44B6-82BA-6495795EEBA1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{326AFCB4-5169-44B6-82BA-6495795EEBA1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{326AFCB4-5169-44B6-82BA-6495795EEBA1}.Release|Any CPU.Build.0 = Release|Any CPU
		{B23AF833-3462-4159-8210-7E112F92FA23}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B23AF833-3462-4159-8210-7E112F92FA23}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B23AF833-3462-4159-8210-7E112F92FA23}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B23AF833-3462-4159-8210-7E112F92FA23}.Release|Any CPU.Build.0 = Release|Any CPU
		{0B0D7A08-FA7D-4C1F-9866-30716794A39A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0B0D7A08-FA7D-4C1F-9866-30716794A39A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0B0D7A08-FA7D-4C1F-9866-30716794A39A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0B0D7A08-FA7D-4C1F-9866-30716794A39A}.Release|Any CPU.Build.0 = Release|Any CPU
		{CBEF3848-E889-4288-A319-DF03D6B9DD7A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CBEF3848-E889-4288-A319-DF03D6B9DD7A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CBEF3848-E889-4288-A319-DF03D6B9DD7A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CBEF3848-E889-4288-A319-DF03D6B9DD7A}.Release|Any CPU.Build.0 = Release|Any CPU
		{B9D5F251-FB85-4764-840E-A6210E66343D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B9D5F251-FB85-4764-840E-A6210E66343D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B9D5F251-FB85-4764-840E-A6210E66343D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B9D5F251-FB85-4764-840E-A6210E66343D}.Release|Any CPU.Build.0 = Release|Any CPU
		{5E1A538A-3DAD-4E6C-A678-AE870283B143}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5E1A538A-3DAD-4E6C-A678-AE870283B143}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5E1A538A-3DAD-4E6C-A678-AE870283B143}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5E1A538A-3DAD-4E6C-A678-AE870283B143}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {8384CB60-A438-4B8A-9B68-EB57D0A08413}
	EndGlobalSection
EndGlobal
