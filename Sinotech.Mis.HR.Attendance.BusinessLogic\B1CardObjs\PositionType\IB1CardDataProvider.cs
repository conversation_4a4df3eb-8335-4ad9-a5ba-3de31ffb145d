﻿using Sinotech.Mis.Common;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Collections.Generic;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.PositionType
{
    public interface IB1CardDataProvider
    {
        /// <summary>
        /// 員工編號
        /// </summary>
        string EmployeeNumber { get; }
        /// <summary>
        /// 加班日期
        /// </summary>
        DateTime OvertimeDate { get; }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        int GetAppliedOvertimeHourInQuota();
        /// <summary>
        /// 取出加班申請卡
        /// </summary>
        /// <returns></returns>
        B1CardApp? GetB1CardApp();
        /// <summary>
        /// 取出加班卡資料
        /// </summary>
        /// <returns></returns>
        B1Card GetB1Card();
        /// <summary>
        /// 取出特殊員工當月加權後已加班時數
        /// </summary>
        /// <returns></returns>
        double GetCurrentMonthlyWeightedOvertimeHours();
        /// <summary>
        /// 是否已填加班申請卡
        /// </summary>
        /// <returns></returns>
        bool GetHasAppliedOvertimeWork();
        /// <summary>
        /// 是否已填加班卡
        /// </summary>
        /// <returns></returns>
        bool GetHasB1CardFilled();
        /// <summary>
        /// 取得當月已經扣除費率 1.0 的加班時數
        /// </summary>
        /// <returns></returns>
        int GetMonthOvertimeHourInQuota();
        /// <summary>
        /// 取出加班日的行事曆資料
        /// </summary>
        /// <returns></returns>
        Workday GetOverTimeDateInfo();
        /// <summary>
        /// 取得員工職務列舉
        /// </summary>
        /// <returns></returns>
        B1CardPositionEnum GetPositionType();
        /// <summary>
        /// 取出所有加班卡相關計畫資料
        /// </summary>
        /// <returns></returns>
        List<Project> GetProjectList();
        /// <summary>
        /// 取出指定的計畫資料
        /// </summary>
        /// <param name="projectNumber"></param>
        /// <returns></returns>
        Project? GetProjectInfo(string projectNumber);
        /// <summary>
        /// 取得當季已經扣除費率 1.0 的加班時數
        /// </summary>
        /// <returns></returns>
        int GetQuarterOvertimeHourInQuota();
        /// <summary>
        /// 取出特殊員工每月允許的加權加班時數
        /// </summary>
        /// <returns></returns>
        double GetSpecialStaffAllowedMonthlyWeightedOvertimeHours();
        /// <summary>
        /// 取出加班卡 支薪加班倍率/補休折算加班倍率/加權時數  合計
        /// </summary>
        /// <returns></returns>
        double GetWeightedOvertimeHours();
        /// <summary>
        /// 是否為特殊員工
        /// </summary>
        /// <returns></returns>
        bool IsSpecialStaff();
    }
}