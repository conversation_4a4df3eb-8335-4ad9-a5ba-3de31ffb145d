﻿using Sinotech.Mis.HR.Attendance.Common;
using System;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves.Tests
{
    public class MarriageLeaveTests
    {

        private readonly MarriageLeave marriageLeave;

        public MarriageLeaveTests(C1CardBo c1CardBo)
        {
            C1Card c1Card = new C1Card();
            c1Card.LeaveNumber = Common.LeaveKindEnum.MarriageLeave;
            c1Card.LeaveSubNumber = 0;
            marriageLeave = new MarriageLeave(c1Card, c1CardBo);
        }

        [Theory]
        [InlineData("2022-02-14", "0349", "2022-02-04", "2022-05-04")]
        [InlineData("2022-04-10", "0349", "2022-03-31", "2022-06-30")]
        [InlineData("2022-10-11", "0349", "2022-10-01", "2023-01-01")]
        [InlineData("2022-12-10", "0349", "2022-11-30", "2023-02-28")]
        public void CalculateLeavePermittedPeriodTest(DateTime eventDate, string empNo, DateTime expectedStartDate, DateTime expectedEndDate)
        {
            DateTime startDate;
            DateTime endDate;
            (startDate, endDate) = marriageLeave.CalculateLeavePermittedPeriod(eventDate, empNo);
            Assert.Equal(expectedStartDate, startDate);
            Assert.Equal(expectedEndDate, endDate.Date);
        }

        [Theory]
        [InlineData(Gender.Female, true)]
        [InlineData(Gender.Male, true)]
        [InlineData((Gender)2, true)]
        public void IsAllowForThisGenderTest(Gender gender, bool expected)
        {
            bool result = MarriageLeave.IsAllowForThisGender(gender);
            Assert.Equal(expected, result);
        }
    }
}