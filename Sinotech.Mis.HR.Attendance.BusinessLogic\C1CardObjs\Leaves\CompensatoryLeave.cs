﻿﻿using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Diagnostics.CodeAnalysis;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves
{
    /// <summary>
    /// 補休假處理類別
    /// 此類別負責處理員工補休假的申請、驗證及相關業務邏輯
    /// 補休假規則：員工可使用因加班或特殊工作而累積的補休時數來申請休假
    /// 補休假必須在有效期限內使用，且不能超過現有的補休時數額度
    /// </summary>
    [ExcludeFromCodeCoverage] // 排除程式碼覆蓋率統計
    [LeaveKind(LeaveKindEnum.CompensatoryLeave)] // 標記假別類型為補休假
    public class CompensatoryLeave : C1CardBase
    {
        #region CheckResult
        // 檢查結果定義區域
        // 此區域定義補休假驗證失敗時的錯誤代碼、狀態及訊息

        /// <summary>
        /// 錯誤代碼：超過補休假額度
        /// 當申請的補休假時數超過員工現有的補休時數時使用
        /// </summary>
        public const int CodeExceedQuota = 3001201;
        
        /// <summary>
        /// 錯誤狀態：超過補休假額度
        /// </summary>
        private readonly CardStatusEnum _statusExceedQuota = CardStatusEnum.Error;
        
        /// <summary>
        /// 錯誤訊息模板：超過補休假額度
        /// {0} = 本次申請的請假時數
        /// {1} = 截至請假起始時間前的剩餘補休假時數
        /// </summary>
        private readonly string _messagePatternExceedQuota = "您本次請假 {0} 小時，截至請假起始時間以前剩餘補休假 {1} 小時，不得超假";

        #endregion

        /// <summary>
        /// 補休假建構子
        /// 初始化補休假處理物件，注入必要的相依性
        /// </summary>
        /// <param name="c1Card">請假單據物件，包含請假的基本資訊</param>
        /// <param name="c1CardBo">請假業務邏輯處理物件，提供各種業務邏輯方法</param>
        public CompensatoryLeave(C1Card c1Card, IC1CardBo c1CardBo) : base(c1Card, c1CardBo)
        {
            // 建構子主體為空，所有初始化工作由基底類別處理
        }

        /// <summary>
        /// 檢查員工是否符合申請補休假的條件
        /// 補休假的申請前提是員工必須已經有休假記錄建立
        /// 這通常表示員工已經累積了一定的補休時數
        /// </summary>
        /// <returns>檢查結果，包含狀態碼、狀態及錯誤訊息（如有）</returns>
        internal override CardCheckResult CanTakeThisLeave()
        {
            // 檢查員工是否已經建立休假記錄
            // 補休假需要有既存的休假記錄作為基礎，這表示員工已有補休時數可供使用
            if (!IfLeaveRecordExists())
            {
                return ResultLeaveRecordNotCreatedYet;
            }

            // 所有條件都通過，允許申請
            return ResultOk;
        }

        /// <summary>
        /// 檢查是否超過允許的補休假時數額度
        /// 此方法驗證員工申請的補休假時數是否超過其現有的補休時數餘額
        /// 補休假不能透支，申請時數必須小於等於現有的補休時數
        /// </summary>
        /// <returns>檢查結果，包含狀態碼、狀態及錯誤訊息（如有）</returns>
        internal override CardCheckResult CheckOverPermittedLeaveHours()
        {
            // 取得員工截至請假起始時間前的剩餘補休假時數
            var compensatoryLeaveRemainingHours = GetCompensatoryLeavetRemainingHour();
            
            // 檢查申請的時數是否超過剩餘的補休時數
            // 補休假不允許透支，必須在現有額度範圍內使用
            if (compensatoryLeaveRemainingHours < TotalHours)
            {
                // 建立超額錯誤結果，包含詳細的時數資訊
                return new CardCheckResult(CodeExceedQuota, _statusExceedQuota,
                    string.Format(_messagePatternExceedQuota, TotalHours, compensatoryLeaveRemainingHours));
            }
            
            // 時數檢查通過
            return ResultOk;
        }


        /// <summary>
        /// 檢查補休假申請單的必要欄位是否已填寫完整
        /// 此方法先呼叫基底類別的欄位檢查，確保基本欄位都已填寫
        /// 補休假目前沒有額外的必要欄位需要檢查
        /// </summary>
        /// <returns>檢查結果，包含狀態碼、狀態及錯誤訊息（如有）</returns>
        internal override CardCheckResult CheckRequiredFields()
        {
            // 呼叫基底類別的必要欄位檢查
            // 包括員工編號、請假日期、請假時數等基本欄位
            var result = base.CheckRequiredFields();
            if (result != ResultOk)
            {
                return result;
            }

            // 補休假沒有額外的必要欄位需要檢查
            // 如果基底類別檢查通過，則直接回傳成功
            return ResultOk;
        }

        /// <summary>
        /// 計算補休假的可申請期間（最早及最晚可請假日期）
        /// 補休假使用預設的計算邏輯，沒有特殊的時間限制
        /// 通常補休假的使用期限由系統設定或公司政策決定
        /// </summary>
        /// <param name="date">參考日期（通常為申請日期）</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>Tuple包含最早可請假日期及最晚可請假日期</returns>
        public override (DateTime, DateTime) CalculateLeavePermittedPeriod(DateTime date, string empNo) => DefaultCalculateLeavePermittedPeriod(date, empNo);

        /// <summary>
        /// 檢查指定性別是否可以申請補休假
        /// 補休假不分性別，所有員工都可以申請
        /// 補休假是基於工作時數累積的假別，與性別無關
        /// </summary>
        /// <param name="gender">員工性別</param>
        /// <returns>永遠回傳true，因為補休假對所有性別開放</returns>
        public static bool IsAllowForThisGender(Gender gender)
        {
            // 補休假是所有員工都享有的權利，不分性別
            // 只要有累積補休時數，任何性別的員工都可以申請
            return true;
        }

        #region Delegate to another gateway
        // 委派給其他閘道的方法區域
        // 此區域包含呼叫業務邏輯物件來取得補休假相關資料的方法

        /// <summary>
        /// 取得員工的剩餘補休假時數
        /// 此方法委派給業務邏輯物件來計算員工截至指定日期前的補休時數餘額
        /// 計算會考慮員工的補休時數累積記錄及已使用的補休時數
        /// </summary>
        /// <returns>員工剩餘的補休假時數（以小時為單位）</returns>
        private int GetCompensatoryLeavetRemainingHour()
        {
            // 委派給業務邏輯物件來取得員工的補休假剩餘時數
            // 參數包括員工編號和請假起始日期，用於計算截至該日期前的補休時數餘額
            return _c1CardBo.GetCompensatoryLeaveRemainingHours(_c1Card.EmpNo, _c1Card.StartDate);
        }

        #endregion
    }
}
