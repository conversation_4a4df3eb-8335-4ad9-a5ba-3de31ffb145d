﻿using Sinotech.Mis.HR.Attendance.BusinessLogic;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves;
using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.Tests.C1CardObjs
{
    [ExcludeFromCodeCoverage]
    public class LeavesTest
    {
        private C1CardBo _c1CardBo;

        public LeavesTest(C1CardBo c1CardBo)
        {
            _c1CardBo = c1CardBo;
        }

        [Theory]
        [InlineData(1, typeof(AnnualLeave))] /*
        [InlineData(2, typeof(MarriageLeave))]
        [InlineData(3, typeof(OfficialLeave))]
        [InlineData(4, typeof(PersonalLeave))]
        [InlineData(5, typeof(BusinessOutLeave))]
        [InlineData(6, typeof(BusinessTripLeave))]
        [InlineData(7, typeof(MaternityLeave))]
        [InlineData(8, typeof(PaternityLeave))]
        [InlineData(9, typeof(BusinessInjuryLeave))]
        [InlineData(10, typeof(SickLeave))]
        [InlineData(11, typeof(FuneralLeave))]
        [InlineData(12, typeof(CompensatoryLeave))]
        // [InlineData(13, typeof(ForeignLeave))] //  關閉此假別
        [InlineData(14, typeof(PostponedLeave))]
        [InlineData(15, typeof(ObstetricInspectionLeave))]
        [InlineData(16, typeof(MenstrualLeave))]
        [InlineData(17, typeof(QuarantineCareLeave))]
        //[InlineData(18, typeof(QuarantineIsolationLeave))] // 關閉此假別
        [InlineData(19, typeof(FamilyCareLeave))]
        //[InlineData(20, typeof(VaccinationLeave))] // 關閉此假別
        [InlineData(21, typeof(BirthdayLeave))]
        [InlineData(22, typeof(DisasterLeave))] */
        public void CreateLeaveTypeTest(int leaveNumber, Type type)
        {
            C1Card leave = new C1Card();
            leave.StartDate = new DateTime(2023, 4, 17, 8, 0, 0);
            leave.EndDate = new DateTime(2023, 4, 17, 17, 0, 0);
            leave.EventDate = new DateTime(2023, 4, 17);
            LeaveKind kind = new LeaveKind();
            kind.Number = (LeaveKindEnum)leaveNumber;
            leave.LeaveNumber = kind.Number;

            // 創建一個休假對象
#nullable enable
            C1CardBase? leave1 = C1CardFactory.CreateLeave(leave, _c1CardBo);
            Assert.NotNull(leave1);
            Assert.Equal(type, leave1.GetType());
        }

        [Theory]
        [InlineData(1)]
        public void AnnualLeaveTest(int leaveKindNumber)
        {
            C1Card leave = new C1Card();
            LeaveKind kind = new LeaveKind();
            kind.Number = (LeaveKindEnum)leaveKindNumber;
            //kind.FormName = "特休";
            leave.LeaveNumber = (LeaveKindEnum)leaveKindNumber;
            // 創建一個特休對象
            C1CardBase? leave1 = C1CardFactory.CreateLeave(leave, _c1CardBo);
            Assert.IsType<AnnualLeave>(leave1);
        }
        /*
        [Theory]
        [InlineData(10)]
        public void SickLeaveTest(int leaveKindNumber)
        {
            LeaveDto leaveSick = new LeaveDto();
            leaveSick.LeaveKind = (LeaveKindEnum)leaveKindNumber;
            // 創建一個病假對象
            C1CardBase? leave2 = C1CardFactory.CreateLeave(leaveSick,  _c1CardBo, _employeeBo, _c1CardDal)
            Assert.IsType<SickLeave>(leave2);
        }
        */
        [Theory]
        [InlineData(1)] /*
        [InlineData(3)]
        [InlineData(4)]
        [InlineData(5)]
        [InlineData(6)]
        [InlineData(10)] */
        public void IsEventLeave_Is_False_Test(int leaveKindNumber)
        {
            C1Card leaveSick = new C1Card();
            leaveSick.LeaveNumber = (LeaveKindEnum)leaveKindNumber;
            // 創建一個假別對象
            C1CardBase? leave2 = C1CardFactory.CreateLeave(leaveSick, _c1CardBo);
            Assert.NotNull(leave2);
            Assert.False(leave2 is LeaveWithEvent);
        }
        /*
        [Theory]
        [InlineData(2022, 12, 8, 2)]
        [InlineData(2022, 12, 9, 8)]
        [InlineData(2022, 12, 10, 11)]
        [InlineData(2022, 12, 10, 15)]
        public void IsEventLeave_Is_True_Test(int eventYear, int eventMonth, int eventDay, int leaveKindNumber)
        {
            DateTime eventDate = new DateTime(eventYear, eventMonth, eventDay);
            LeaveDto leaveMarriage = new LeaveDto();
            leaveMarriage.EventDate = eventDate;
            LeaveKind leaveKindMarrage = new LeaveKind();
            leaveKindMarrage.Number = (LeaveKindEnum)leaveKindNumber;
            leaveMarriage.LeaveKind = leaveKindMarrage.Number;

            // 創建一個假對象
            C1CardBase? leaveBase = C1CardFactory.CreateLeave(leaveMarriage,  _c1CardBo, _employeeBo, _c1CardDal)
            Assert.NotNull(leaveBase);
            Assert.True(leaveBase.IsEventLeave);
        }

        [Theory]
        [InlineData(2022, 12, 8, 2023, 2, 28)]
        [InlineData(2022, 12, 9, 2023, 2, 28)]
        [InlineData(2022, 12, 10, 2023, 2, 28)]
        [InlineData(2022, 12, 11, 2023, 3, 1)]
        [InlineData(2022, 12, 12, 2023, 3, 2)]
        [InlineData(2022, 12, 13, 2023, 3, 3)]
        [InlineData(2023, 1, 1, 2023, 3, 22)]
        [InlineData(2023, 12, 8, 2024, 2, 28)]
        [InlineData(2023, 12, 9, 2024, 2, 29)]
        [InlineData(2023, 12, 10, 2024, 2, 29)]
        [InlineData(2023, 12, 11, 2024, 3, 1)]
        [InlineData(2023, 12, 12, 2024, 3, 2)]
        public void MarriageLeaveTest(int eventYear, int eventMonth, int eventDay,
            int expectYear, int expectMonth, int expectDay)
        {
            DateTime eventDate = new DateTime(eventYear, eventMonth, eventDay);
            LeaveDto leaveMarriage = new LeaveDto();
            leaveMarriage.EventDate = eventDate;
            LeaveKind leaveKindMarrage = new LeaveKind();
            leaveKindMarrage.Number = LeaveKindEnum.MarriageLeave;
            leaveMarriage.LeaveKind = leaveKindMarrage.Number;

            // 創建一個婚假對象
            C1CardBase? leaveBase = C1CardFactory.CreateLeave(leaveMarriage,  _c1CardBo, _employeeBo, _c1CardDal)
            Assert.NotNull(leaveBase);

            MarriageLeave? maternityLeave = (MarriageLeave?)leaveBase;
            Assert.IsType<MarriageLeave>(maternityLeave);
            // 執行婚假期限計算
            maternityLeave.CalculateExpirationDate();
            DateTime expirationEndDate = new DateTime(expectYear, expectMonth, expectDay);
            Assert.Equal(expirationEndDate, leaveMarriage.ExpirationEndDate);
        }

        /// <summary>
        /// 測試 LeaveKindEnum
        /// </summary>
        [Fact]
        public void LeaveKindEnumsTest()
        {
            LeaveDto leave = new LeaveDto();
            leave.StartTime = new DateTime(2023, 4, 12, 8, 0, 0);
            leave.EventDate = leave.StartTime;
            leave.EndTime = new DateTime(2023, 4, 12, 15, 0, 0);
            LeaveKind kind = new LeaveKind();
            // 測試LeaveKindEnums 的對應 Class 是否存在
            foreach (string name in Enum.GetNames(typeof(LeaveKindEnum)))
            {
                kind.FormName = name;
                kind.Number = (LeaveKindEnum)Enum.Parse(typeof(LeaveKindEnum), name);
                leave.LeaveKind = kind.Number;
                if (kind.Number != LeaveKindEnum.ForeignLeave && kind.Number != LeaveKindEnum.QuarantineIsolationLeave && kind.Number != LeaveKindEnum.VaccinationLeave)
                {
                    //方法 1：呼叫 工廠 建立物件
                    C1CardBase? leaveBase = C1CardFactory.CreateLeave(leave, c1CardBo);
                    //if (leaveBase == null)
                    //{
                    //    Console.WriteLine(kind.FormName);
                    //}
                    Assert.NotNull(leaveBase);

                    
                    ////方法 2：呼叫 工廠 Reflection 建立物件
                    //leaveBase = C1CardFactory.CreateLeave(leave, name, _c1CardBo);
                    //Assert.NotNull(leaveBase);
                    
                    //方法 3：Pure Reflection
                    Assembly assem = typeof(C1CardBase).Assembly;
                    Type? type = assem.GetType($"Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves.{name}");
                    Assert.NotNull(type);
                    if (type != null)
                    {
                        C1CardBase? x = Activator.CreateInstance(type, new object[] { leave, _c1CardBo, _c1CardDal }) as C1CardBase;
                        Assert.NotNull(x);
                    }
                }
            } 
    }*/
    }
}
