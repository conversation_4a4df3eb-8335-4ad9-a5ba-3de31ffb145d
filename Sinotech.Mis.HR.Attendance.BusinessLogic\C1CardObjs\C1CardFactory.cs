﻿﻿using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;

namespace Sinotech.Mis.HR.Attendance.C1CardObjs
{
    /// <summary>
    /// 請假工廠類別 - 自動註冊所有請假類型
    /// 使用反射機制自動掃描並註冊所有具有 LeaveKindAttribute 屬性的請假類別
    /// 採用混合工廠模式，結合字典快取和 Expression Tree 編譯來優化效能
    /// </summary>
    public static class C1CardFactory
    {
        /// <summary>
        /// 靜態工廠字典，以 LeaveKindEnum 為鍵，對應的建構函式委派為值
        /// 使用 Expression Tree 編譯後的委派，提供高效能的物件建立
        /// </summary>
        private static readonly Dictionary<LeaveKindEnum, Func<C1Card, IC1CardBo, C1CardBase>> _factories;

        /// <summary>
        /// 靜態建構函式，在第一次存取此類別時執行
        /// 負責初始化工廠字典，掃描並註冊所有可用的請假類型
        /// </summary>
        static C1CardFactory()
        {
            _factories = BuildFactories();
        }

        /// <summary>
        /// 自動建立所有請假類型的工廠方法
        /// 透過反射掃描所有 C1CardBase 的子類別，並為具有 LeaveKindAttribute 的類別建立工廠委派
        /// 使用 Expression Tree 技術編譯建構函式，提供接近原生效能的物件建立速度
        /// </summary>
        /// <returns>包含所有請假類型工廠的字典，鍵為 LeaveKindEnum，值為建構函式委派</returns>
        private static Dictionary<LeaveKindEnum, Func<C1Card, IC1CardBo, C1CardBase>> BuildFactories()
        {
            // 初始化工廠字典，用於存放所有請假類型的建構委派
            var factories = new Dictionary<LeaveKindEnum, Func<C1Card, IC1CardBo, C1CardBase>>();

            // 取得所有繼承自 C1CardBase 的請假類型
            var leaveTypes = GetAllLeaveTypes();

            // 遍歷所有請假類型，為每個類型建立對應的工廠委派
            foreach (var type in leaveTypes)
            {
                // 嘗試取得類別上的 LeaveKindAttribute 屬性
                // 此屬性定義了該類別對應的請假種類枚舉值
                var attr = type.GetCustomAttribute<LeaveKindAttribute>();
                if (attr == null) continue; // 沒有屬性的類別跳過

                // 查找具有指定參數簽名的建構函式 (C1Card, IC1CardBo)
                // 所有請假類型都必須實作此建構函式以符合工廠模式要求
                var constructor = type.GetConstructor(new[] { typeof(C1Card), typeof(IC1CardBo) });
                if (constructor == null) continue; // 沒有符合的建構函式則跳過

                // 使用 Expression Tree 建立高效能的建構函式委派
                // 建立 C1Card 參數的表達式
                var paramC1Card = Expression.Parameter(typeof(C1Card), "c1Card");
                // 建立 IC1CardBo 參數的表達式
                var paramBo = Expression.Parameter(typeof(IC1CardBo), "c1CardBo");
                // 建立新物件建構的表達式，使用找到的建構函式和參數
                var newExpr = Expression.New(constructor, paramC1Card, paramBo);

                // 將表達式編譯成 Lambda 表達式，形成可執行的委派
                var lambda = Expression.Lambda<Func<C1Card, IC1CardBo, C1CardBase>>(
                    newExpr, paramC1Card, paramBo);

                // 檢查是否已存在相同的 LeaveKind 註冊
                // 這是為了防止重複註冊同一種請假類型，確保工廠的唯一性
                if (factories.ContainsKey(attr.LeaveKind))
                    throw new InvalidOperationException(
                        $"LeaveKind {attr.LeaveKind} 被多個類別註冊：{factories[attr.LeaveKind].Method.DeclaringType?.FullName} 與 {type.FullName}");

                // 將編譯好的委派加入工廠字典
                // Compile() 方法會將 Expression Tree 編譯成高效能的執行碼
                factories.Add(attr.LeaveKind, lambda.Compile());
            }
#if DEBUG
            // 確認是否有缺少實作
            var allKinds = Enum.GetValues<LeaveKindEnum>();
            var missingKinds = allKinds.Where(k => !factories.ContainsKey(k)).ToArray();
            if (missingKinds.Any())
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ 以下 LeaveKindEnum 沒有對應類別: {string.Join(", ", missingKinds)}");
            }
#endif
            return factories;
        }

        /// <summary>
        /// 取得所有 C1CardBase 的子類別
        /// 透過反射機制掃描當前應用程式域中的所有組件，尋找符合條件的請假類型
        /// 包含錯誤處理機制，確保即使部分組件載入失敗也能正常運作
        /// </summary>
        /// <returns>所有符合條件的請假類型陣列</returns>
        private static Type[] GetAllLeaveTypes()
        {
            return AppDomain.CurrentDomain.GetAssemblies()
                .SelectMany(assembly =>
                {
                    try
                    {
                        // 嘗試取得組件中的所有類型
                        return assembly.GetTypes();
                    }
                    catch (ReflectionTypeLoadException ex)
                    {
                        // 處理載入失敗的情況，只返回成功載入的類型
                        // 這種情況通常發生在組件有相依性問題或權限不足時
                        Console.WriteLine($"警告: C1CardFactory 無法載入組件 {assembly.FullName} 的某些類型。");
                        return ex.Types.OfType<Type>(); // 篩選出非 null 的類型
                    }
                })
                .Where(type =>
                    // 必須繼承自 C1CardBase（但不是 C1CardBase 本身）
                    typeof(C1CardBase).IsAssignableFrom(type)
                    // 不能是抽象類別（無法實例化）
                    && !type.IsAbstract
                    // 不能是介面（無法實例化）
                    && !type.IsInterface
                    // 不能是基底類別本身
                    && type != typeof(C1CardBase))
                .ToArray();
        }

        /// <summary>
        /// 創建請假物件的主要工廠方法
        /// 根據 C1Card 中的 LeaveNumber（請假種類）來決定要建立哪一種具體的請假物件
        /// 使用預編譯的委派來提供高效能的物件建立，避免重複反射操作
        /// </summary>
        /// <param name="c1Card">請假資料物件，包含請假的基本資訊如請假種類、日期等</param>
        /// <param name="c1CardBo">請假商業邏輯物件，提供資料存取和業務驗證功能</param>
        /// <returns>
        /// 根據請假種類建立的具體請假物件實例
        /// 如果找不到對應的請假類型工廠，則返回 null
        /// </returns>
        public static C1CardBase? CreateLeave(C1Card c1Card, IC1CardBo c1CardBo)
        {
            // 從工廠字典中查找對應的建構委派
            // 使用 TryGetValue 避免 KeyNotFoundException，提升效能
            return _factories.TryGetValue(c1Card.LeaveNumber, out var factory)
                ? factory(c1Card, c1CardBo) // 找到工廠則執行委派建立物件
                : null; // 找不到對應工廠則返回 null
        }
    }
}
