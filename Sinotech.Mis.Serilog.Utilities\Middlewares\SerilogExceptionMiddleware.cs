﻿// SerilogExceptionMiddleware, General Exception Handler
// 2022/07/12
// By 林志偉

using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;

namespace Sinotech.Mis.Serilog.Utilities.Middlewares
{
    [ExcludeFromCodeCoverage]
    public class SerilogExceptionMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<SerilogExceptionMiddleware> _logger;
        [ExcludeFromCodeCoverage]
        public SerilogExceptionMiddleware(ILogger<SerilogExceptionMiddleware> logger, RequestDelegate next)
        {
            _logger = logger;
            _next = next;
        }
        [ExcludeFromCodeCoverage]
        public async Task Invoke(HttpContext context)
        {
            try
            {
                await _next(context);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{nameof(SerilogExceptionMiddleware)}: Exception found!");


                //await context.Response
                //    .WriteAsync(errorMessage);

                throw;
            }
        }
    }
}
