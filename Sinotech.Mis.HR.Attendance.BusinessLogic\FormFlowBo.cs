﻿using Sinotech.Mis.Common;
using Sinotech.Mis.Helpers;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Collections.Generic;
using System.Data;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic;

using Microsoft.Extensions.Configuration;
using System.Linq;

public class FormFlowBo : IFormFlowBo
{
    private readonly IAttendanceBo _attendanceBo;
    private readonly IDepartmentBo _departmentBo;
    private readonly IEmployeeBo _employeeBo;
    private readonly ISinoSignBo _sinoSignBo;
    private readonly bool _IsMultipleVicePresidents = false; // 有2位副執行長
    private readonly IConfiguration _configuration;

    /// <summary>
    /// FormFlowBo 的建構函式
    /// </summary>
    /// <param name="attendanceBo"></param>
    /// <param name="departmentBo"></param>
    /// <param name="employeeBo"></param>
    /// <param name="sinoSignBo"></param>
    /// <param name="configuration"></param>
    public FormFlowBo(IAttendanceBo attendanceBo, IDepartmentBo departmentBo, IEmployeeBo employeeBo, 
        ISinoSignBo sinoSignBo, IConfiguration configuration)
    {
        _attendanceBo = attendanceBo;
        _departmentBo = departmentBo;
        _employeeBo = employeeBo;
        _sinoSignBo = sinoSignBo;
        _configuration = configuration;
        // 取得設定檔 IsMultipleVicePresidents
        if (_configuration != null)
        {
            var k = _configuration["IsMultipleVicePresidents"];
            if (!string.IsNullOrWhiteSpace(k))
            {
                _IsMultipleVicePresidents = _configuration.GetValue<bool>("IsMultipleVicePresidents");
            }
        }
    }

    /// <summary>
    /// 執行長關卡
    /// </summary>
    /// <param name="form">表單</param>
    /// <returns>總關卡數</returns>
    public int FlowAddCEO(Form form)
    {
        string roleName = _sinoSignBo.GetRoleName("S02");
        string flowName = roleName; // 在此關卡名與角色名相同，都是董事長
        string departmentName = _departmentBo.GetDepartmentShortName(1);
        FormFlow flowCEO = GenerateFlowDto(form, "S02", flowName, 1, departmentName, roleName);
        form.Flows.Add(flowCEO);
        form.TotalSteps = form.Flows.Count;
        return form.TotalSteps;
    }

    /// <summary>
    /// 董事長關卡
    /// </summary>
    /// <param name="form">表單</param>
    /// <returns>總關卡數</returns>
    public int FlowAddChairman(Form form)
    {
        string roleName = _sinoSignBo.GetRoleName("S01");
        string flowName = roleName; // 在此關卡名與角色名相同，都是董事長
        string departmentName = _departmentBo.GetDepartmentShortName(1);
        FormFlow flowChairman = GenerateFlowDto(form, "S01", flowName, 1, departmentName, roleName);
        form.Flows.Add(flowChairman);
        form.TotalSteps = form.Flows.Count;
        return form.TotalSteps;
    }

    /// <summary>
    /// 流程加入部門登記桌，若無登記桌則跳過
    /// </summary>
    /// <param name="form">申請單</param>
    /// <param name="employee">員工詳細資料</param>
    /// <param name="isNotification">是否為通知，預設否</param>
    /// <returns>表單關卡總數</returns>
    public int FlowAddDepartmentMailroom(Form form, Employee employee, bool isNotification = false)
    {
        List<string> mailroom = _sinoSignBo.GetDepartmentMailroom(employee.DeptNo);
        if (mailroom.Count > 0) //確定該部門登記桌存在，避免關卡無人簽核
        {
            string recipient = employee.DeptNo.ToString("00");
            string flowName = "部門登記桌";
            string roleName = $"{employee.DeptSName}登記桌";
            FormFlow flowMailroom = GenerateFlowDto(form, recipient, flowName, employee.DeptNo, employee.DeptSName, roleName, isNotification);
            form.Flows.Add(flowMailroom);
            form.TotalSteps = form.Flows.Count;
        }
        return form.TotalSteps;
    }

    /// <summary>
    /// 副執行長關卡
    /// </summary>
    /// <param name="form">表單</param>
    /// <returns>總關卡數</returns>
    public int FlowAddDeputyCEO(Form form)
    {
        string stageId, stageName, roleName;
        (stageId, stageName, roleName) = GetDeputyCEOStageInfo(form.DeptNo);
        string departmentName = _departmentBo.GetDepartmentShortName(1);
        FormFlow flowDeputyCEO = GenerateFlowDto(form, stageId, stageName, 1, departmentName, roleName);
        form.Flows.Add(flowDeputyCEO);
        form.TotalSteps = form.Flows.Count;
        return form.TotalSteps;
    }

    /// <summary>
    /// 依照部門編號取得副執行長關卡資訊
    /// </summary>
    /// <param name="deptNo">部門編號</param>
    /// <returns>角色ID, 關卡名稱，角色名稱</returns>
    public (string roleId, string stageName, string roleName) GetDeputyCEOStageInfo(int deptNo)
    {
        string roleId, stageName, roleName;
        roleId = "S03";
        roleName = _sinoSignBo.GetRoleName(roleId); // 取得S03角色名稱
        stageName = roleName; // 以副執行長角色名作為關卡名稱
        if (_IsMultipleVicePresidents)
        {
            // 判斷出勤表單的部門
            List<Department> administrativeDepartment = _departmentBo.GetAdministrativeDepartments();

            // 若表單的部門為行政部門，則由 _AdministrativeS03 簽核
            var dept = administrativeDepartment.FirstOrDefault(department => department.DeptNo == deptNo);

            // 有兩位副執行長，依行政或研究部門決定
            if (dept != null) // 行政部門
            {
                roleId = "S04";
                roleName = _sinoSignBo.GetRoleName("S04"); // 行政副執行長
            }
            else // 研究部門
            {
                roleId = "S05";
                roleName = _sinoSignBo.GetRoleName("S05"); // 研究中心副執行長
            }
            stageName = roleName; // 以副執行長角色名作為關卡名稱
        }
        return (roleId, stageName, roleName);
    }

    /// <summary>
    ///   <para>
    /// 流程加入部門主管</para>
    /// </summary>
    /// <param name="form">The forms.</param>
    /// <param name="employee">The employeeWatcher.</param>
    /// <returns>
    ///  總關卡數
    /// </returns>
    public int FlowAddManager(Form form, Employee employee)
    {
        string recipient = $"L{form.DeptNo.ToString("00")}";
        // Hack: L01 hack L01->L00 因應治理幕僚 hard code ，目前無此需求，但仍先保留
        if (recipient == "L01")
        {
            recipient = "L00";
        }

        string roleName = EmployeeBo.CorrectJobName($"{employee.DeptSName}主管");
        FormFlow flowManager = GenerateFlowDto(form, recipient, "部門主管", employee.DeptNo, employee.DeptSName, roleName);
        form.Flows.Add(flowManager);
        form.TotalSteps = form.Flows.Count;
        return form.TotalSteps;
    }

    /// <summary>
    /// 加入通知代理人流程
    /// </summary>
    /// <param name="form">The forms.</param>
    /// <param name="substitute">代理人</param>
    /// <returns>總關卡數</returns>
    public int FlowAddNotifySubstitute(Form form, Employee substitute)
    {
        string recipient = substitute.EmpNo;
        string flowName = "代理人";
        string roleName = "代理人";
        FormFlow flowManager = GenerateFlowDto(form, recipient, flowName, substitute.DeptNo, substitute.DeptSName, roleName, true);
        form.Flows.Add(flowManager);
        form.TotalSteps = form.Flows.Count;
        return form.TotalSteps;
    }

    /// <summary>流程加入加會人員</summary>
    /// <param name="form">The forms.</param>
    /// <param name="signers">The signers.</param>
    /// <returns>加會關卡數</returns>
    public int FlowAddSigners(Form form, string[] signers)
    {
        int counter = 0;
        for (int i = 0; i < signers.Length; i++)
        {
            string signer = signers[i].Trim();
            // 去除重覆加會人員
            int index = form.Flows.FindIndex(x => x.RecipientEmpNo == signer && x.IsNotification == false);
            if (index == -1 && !string.IsNullOrEmpty(signer))
            {
                Employee empSigner = _employeeBo.GetEmployeeDetail(signer);
                FormFlow flow = GenerateFlowDto(form, signer, "加會人員", empSigner.DeptNo, empSigner.DeptSName, empSigner.CName);
                form.Flows.Add(flow);
                counter++;
            }
        }
        return counter;
    }

    /// <summary>
    /// Deduplicate the flows. 刪除所有重覆流程，包括不連續者
    /// </summary>
    /// <param name="flows">The flows.</param>
    /// <returns></returns>
    public List<FormFlow> FlowDedup(List<FormFlow> flows)
    {
        List<FormFlow> result = new List<FormFlow>();
        foreach (FormFlow flow in flows)
        {
            if (flow.IsNotification ||
                (result.FindIndex(x => (x.RecipientEmpNo == flow.RecipientEmpNo) && (!flow.IsNotification && !x.IsNotification)) == -1))
            {
                result.Add(flow);
            }
        }
        return result;
    }

    /// <summary>
    /// Deduplicate the continuous flows. 刪除所有連續重覆流程
    /// </summary>
    /// <param name="flows">The flows.</param>
    /// <returns></returns>
    public List<FormFlow> FlowDedupContinuous(List<FormFlow> flows)
    {
        List<FormFlow> result = new List<FormFlow>();
        FormFlow[] flowArray = flows.ToArray();
        FormFlow? previousFlow = new FormFlow();

        if (flows.Count > 0)
        {
            previousFlow = flowArray[0];
            result.Add(previousFlow);
        }
        // 刪除非通知連續重覆流程
        for (int i = 1; i < flowArray.Length; i++)
        {
            FormFlow flow = flowArray[i];
            if (flow.IsNotification || previousFlow.IsNotification || previousFlow.RecipientEmpNo != flow.RecipientEmpNo)
            {
                result.Add(flow);
            }
            previousFlow = flow;
        }

        // 刪除連續重覆通知流程
        List<FormFlow> flows2 = result;
        flowArray = result.ToArray();
        result = new List<FormFlow>();
        if (flows2.Count > 0)
        {
            previousFlow = flowArray[0];
            result.Add(previousFlow);
        }
        for (int i = 1; i < flowArray.Length; i++)
        {
            FormFlow flow = flowArray[i];
            if (!flow.IsNotification || !previousFlow.IsNotification || previousFlow.RecipientEmpNo != flow.RecipientEmpNo)
            {
                result.Add(flow);
            }
            previousFlow = flow;
        }

        return result;
    }

    /// <summary>
    /// 產生 <see cref="FormFlow" />流程物件，在此不會修改原 form的 Flows
    /// </summary>
    /// <param name="form">表單</param>
    /// <param name="recipient">收件人</param>
    /// <param name="flowName">流程名稱</param>
    /// <param name="deptNo">部門編號</param>
    /// <param name="deptSName">部門簡稱</param>
    /// <param name="roleName">角色名稱</param>
    /// <param name="isNotification">是否為通知</param>
    /// <returns>
    /// FormFlow物件
    /// </returns>
    public FormFlow GenerateFlowDto(Form form, string recipient, string flowName, int deptNo, string deptSName, string roleName, bool isNotification = false)
    {
        int steps = form.Flows.Count + 1;
        FormFlow flow = new FormFlow();
        flow.Step = steps;
        flow.RecipientEmpNo = recipient.Trim();
        flow.FlowUID = Guid.NewGuid();
        flow.FormUID = form.FormUID;
        flow.FlowName = flowName;
        if (steps == 1)
        {
            flow.FlowStatus = (int)FlowStatus.Processing; // 關卡狀態 1:簽核中
        }
        else
        {
            flow.FlowStatus = (int)FlowStatus.NotSend; // 關卡狀態 0:未傳送
        }
        flow.FlowStatusName = GetFlowStatusName(flow.FlowStatus);
        flow.IsNotification = isNotification;

        Employee? emp = _employeeBo.GetEmployeeDetail(flow.RecipientEmpNo);
        if (emp != null && !string.IsNullOrWhiteSpace(emp.CName)) //員工
        {
            flow.RecipientName = emp.CName;
            flow.RecipientDeptNo = emp.DeptNo;
            flow.RecipientDeptSName = emp.DeptSName;
            flow.RecipientTeamID = emp.TeamID;
            flow.RecipientTeamCName = emp.TeamCName;
        }
        else //角色
        {
            flow.RecipientName = roleName;
            flow.RecipientDeptNo = deptNo;
            flow.RecipientDeptSName = deptSName;
            flow.RecipientTeamID = null;
            flow.RecipientTeamCName = null;
        }
        return flow;
    }

    /// <summary>Gets the name of the flow status.</summary>
    /// <param name="flowStatus">The flow status.</param>
    /// <returns>Flow Status FormName</returns>
    public string GetFlowStatusName(int flowStatus)
    {
        Dictionary<int, string> flowStatusNames = _attendanceBo.GetFormFlowStatusNames();
        string flowStatusName = flowStatusNames[flowStatus];
        return flowStatusName;
    }

    /// <summary>
    /// 由FormUID取得流程
    /// </summary>
    /// <param name="formUID"></param>
    /// <param name="step"></param>
    /// <returns></returns>
    public FormFlow? GetFormFlow(Guid formUID, int step)
    {
        List<FormFlow> flows = GetFormFlows(formUID);
        FormFlow? flow = null;
        foreach (FormFlow formFlow in flows)
        {
            if (formFlow.Step == step)
            {
                flow = formFlow;
                flow.FlowStatusName = GetFlowStatusName(flow.FlowStatus);
            }
        }
        return flow;
    }

    /// <summary>由FlowUID取得流程 FormFLow</summary>
    /// <param name="flowUID">The flow forms.</param>
    /// <returns>DataTable FormFlow</returns>
    public FormFlow GetFormFlowByFlowUID(Guid flowUID)
    {
        FormFlow flow = new FormFlow();
        DataTable dt = _attendanceBo.GetFormFlow(flowUID);
        if (dt != null && dt.Rows.Count > 0)
        {
            List<FormFlow> forms = SqlHelper.ConvertDataTable<FormFlow>(dt);
            flow = forms[0];
        }
        return flow;
    }

    /// <summary>取得 FormFLow List</summary>
    /// <param name="formUID">The forms forms.</param>
    /// <returns>List of FormFlow</returns>
    public List<FormFlow> GetFormFlows(Guid formUID)
    {
        List<FormFlow> flows = new List<FormFlow>();
        DataTable dt = _attendanceBo.GetFormFlows(formUID);

        if (dt != null && dt.Rows.Count > 0)
        {
            flows = SqlHelper.ConvertDataTable<FormFlow>(dt);
            foreach (FormFlow flow in flows)
            {
                flow.FlowStatusName = GetFlowStatusName(flow.FlowStatus);
            }
        }
        return flows;
    }

}