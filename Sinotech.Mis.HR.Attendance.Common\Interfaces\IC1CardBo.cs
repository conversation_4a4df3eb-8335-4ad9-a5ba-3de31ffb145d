﻿
using Sinotech.Mis.Common;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace Sinotech.Mis.HR.Attendance.Common
{
    public interface IC1CardBo : ICardBaseBo
    {

        /// <summary>新增 請假卡</summary>
        /// <param name="creatorId">填表人 員工編號</param>
        /// <param name="leave">請假卡</param>
        /// <param name="ipAddress">IP address</param>
        /// <returns>傳回CardCheckResult</returns>
        public Task<CardCheckResult> AddC1Card(string creatorId, LeaveView leave, string ipAddress,
            string hostname);

        /// <summary>
        /// 此處的 days 只允許 正整數
        /// </summary>
        /// <param name="startDate">開始日期</param>
        /// <param name="days">天數</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public DateTime AddWorkDays(DateTime startDate, int days, string empNo);

        /// <summary>
        /// List<C1CardDto> 轉為 C1Card 物件
        /// </summary>
        /// <param name="c1CardDtos"></param>
        /// <returns></returns>
        public C1Card C1CardDto2C1Card(List<C1CardDto> c1CardDtos);

        /// <summary>
        /// 將 C1CardDto 轉為List<C1CardDto> ，若C1CardDto有跨月時依月份切割
        /// </summary>
        /// <param name="c1CardDto"></param>
        /// <returns></returns>
        public List<C1CardDto> C1CardDto2ListC1CardDtos(C1CardDto c1CardDto);

        /// <summary>
        /// C1Card物件 轉為 List<C1CardDto>
        /// </summary>
        /// <param name="card"></param>
        /// <returns></returns>
        public List<C1CardDto> C1CardToC1CardDtos(CardBase card);

        /// <summary>
        /// 計算請假單的天數，無條件捨去時數
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">起始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <returns>天數</returns>
        public int CalculateEmpTakeLeaveWorkingDays(string empNo, DateTime startDate, DateTime endDate);

        /// <summary>計算請假區間之工時，採無條件進位法，每日目前最多8小時 <br />
        /// 僅計算工作日，而非日曆天
        /// </summary>
        /// <param name="empNo"></param>
        /// <param name="startDate">起始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <returns></returns>
        public int CalculateEmpTakeLeaveWorkingHours(string empNo, DateTime startDate, DateTime endDate);

        /// <summary>
        /// 依假別計算預設請假截止日
        /// </summary>
        /// <param name="startDate">請假開始</param>
        /// <param name="leaveNumber">假別代碼</param>
        /// <param name="subKindNumber">假別細項代碼</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>請假截止日</returns>
        public DateTime CalculateLeaveEndDate(DateTime startDate, int leaveNumber, int subKindNumber, string empNo);

        /// <summary>
        /// 依假別計算預設請假截止日
        /// </summary>
        /// <param name="startDate">請假開始</param>
        /// <param name="leaveNumber">假別代碼</param>
        /// <param name="subKindNumber">假別細項代碼</param>
        /// <param name="shiftId">班別</param>
        /// <returns>請假截止日</returns>
        public DateTime CalculateLeaveEndDate(DateTime startDate, int leaveNumber, int subKindNumber, int shiftId = 0);

        /// <summary>
        /// 找出可請假期限
        /// </summary>
        /// <param name="eventDate">事件發生日</param>
        /// <param name="leaveNumber">假別代碼</param>
        /// <param name="subKindNumber">假別細項代碼</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>最早可請假日期與早晚可請假日期</returns>
        public (DateTime, DateTime) CalculateLeavePermittedPeriod(DateTime eventDate, int leaveNumber, int subKindNumber, string empNo);

        /// <summary>
        /// 計算請假單的天數，無條件捨去
        /// </summary>
        /// <param name="workingHours">請假單的總工作時數</param>
        /// <returns>天數</returns>
        public int CalculateTakeLeaveWorkingDays(int workingHours);

        /// <summary>
        /// 計算請假單的天數，無條件捨去時數
        /// </summary>
        /// <param name="startDate">起始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns>天數</returns>
        public int CalculateTakeLeaveWorkingDays(DateTime startDate, DateTime endDate, int shiftId = 1);

        /// <summary>計算請假單之工時，採無條件進位法，每日目前最多8小時 <br />
        /// 僅計算工作日，而非日曆天
        /// </summary>
        /// <param name="startDate">起始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns>時數</returns>
        public int CalculateTakeLeaveWorkingHours(DateTime startDate, DateTime endDate, int shiftId = 1);

        /// <summary>
        /// 檢查是否已請假
        /// </summary>
        ///<param name="startDate">請假起始日期</param>
        ///<param name="endDate">請假結束日期</param>
        ///<param name="empNo">員工編號</param>
        /// <returns>已經請假過傳回 true，無則傳回 false</returns>
        public bool CheckAlreadyTakeLeave(DateTime startDate, DateTime endDate, string empNo);

        /// <summary>檢查 請假卡</summary>
        /// <param name="creatorId">填表人 員工編號</param>
        /// <param name="leave">請假卡</param>
        /// <param name="ipAddress">IP address</param>
        /// <param name="hostname">Host name</param>
        /// <returns>CardCheckResult</returns>
        public CardCheckResult CheckC1Card(string creatorId, LeaveView leave, string ipAddress, string hostname);

        /// <summary>
        /// 檢查請假日期是否為工作日、結束是否大於起始，是否在上班時間請假
        /// </summary>
        ///<param name="startDate">請假起始日期</param>
        ///<param name="endDate">請假結束日期</param>
        /// <returns>日期異常時顯示訊息，否則傳回空字串</returns>
        public string CheckLeaveDays(DateTime startDate, DateTime endDate);

        /// <summary>
        /// 檢查請假日期是否為工作日、結束是否大於起始，是否在上班時間請假
        /// </summary>
        ///<param name="startDate">請假起始日期</param>
        ///<param name="endDate">請假結束日期</param>
        ///<param name="empNo">員工編號</param>
        /// <returns>日期異常時顯示訊息，否則傳回空字串</returns>
        public string CheckLeaveDays(DateTime startDate, DateTime endDate, string empNo);

        /// <summary>Find the first work day in month.</summary>
        /// <param name="year">The year. 西元年</param>
        /// <param name="month">The month. 月分</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>第一天工作日的日期(int)</returns>
        public DateTime FirstWorkDayInMonth(int year, int month, string empNo);

        /// <summary>
        /// 單號是否被其他 (已核可或簽核中) 表單關連
        /// </summary>
        /// <param name="formNumber">單號</param>
        /// <returns></returns>
        public bool FormNumberWasRelated(string formNumber);

        /// <summary>
        /// 依照C1Card產生C1CardDetails
        /// </summary>
        /// <param name="c1Card"></param>
        public void GenerateC1CardDetail(C1Card c1Card);

        /// <summary>
        /// 產生請假單的FormInfo
        /// </summary>
        /// <param name="card">請假單</param>
        /// <returns>FormInfo</returns>
        public string GenerateFormInfo(C1Card card);


        /// <summary>
        /// 取得員工某年的生日之月份第一個工作日期，用以計算生日假請假期限
        /// </summary>
        /// <param name="empNo"></param>
        /// <param name="year"></param>
        /// <returns></returns>
        public DateTime GetBirthdayMonthFirstWorkday(string empNo, int year);

        /// <summary>
        /// 取得員工某年的生日之月份最後一個工作日期，用以計算生日假請假期限
        /// </summary>
        /// <param name="empNo"></param>
        /// <param name="year"></param>
        /// <returns></returns>
        public DateTime GetBirthdayMonthLastWorkday(string empNo, int year);

        /// <summary>
        /// 取得年度員工生日福利資料表
        /// </summary>
        /// <param name="year">西元年</param>
        /// <returns></returns>
        public DataTable GetBirthdayWelfare(int year);

        /// <summary>
        /// 查詢特定假別特定同仁於特定事件發生日的所有假單，僅限 已同意與簽核中
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="leaveNumber">假別</param>
        /// <param name="leaveSubNumber">假別細項</param>
        /// <param name="eventDate">事件發生日</param>
        /// <returns></returns>
        public List<C1Card> GetC1CardByEventDate(string empNo, LeaveKindEnum leaveNumber, int leaveSubNumber, DateTime eventDate);

        /// <summary>
        /// 查詢特定假別特定同仁於特定假別的所有假單，僅限 已同意與簽核中
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="leaveNumber">假別</param>
        /// <param name="leaveSubNumber">假別細項</param>
        public List<C1Card> GetC1CardByLeaveKind(string empNo, LeaveKindEnum leaveNumber, int leaveSubNumber);

        /// <summary>
        /// 檢查本次請假與先前請假時數累計是否超過每日正常上班時數
        /// </summary>
        /// <param name="c1Card"></param>
        /// <returns></returns>
        public (bool, int, DateTime?) CheckAccumulatedOverPermittedWorkingHours(C1Card c1Card);

        /// <summary>取得某段時間內特定員工的請假卡，檢查重覆使用，故不包括頭尾</summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">請假啟始日期</param>
        /// <param name="endDate">請假結束日期</param>
        /// <returns>請假卡 C1CardDto 物件 List</returns>
        public List<C1CardDto> GetCardsBetween(string empNo, DateTime startDate, DateTime endDate);

        /// <summary>
        /// 取得單次請假的日期與工時
        /// </summary>
        /// <returns></returns>
        public Dictionary<DateTime, int> CalculateC1CardApplyDayHours(C1Card c1Card);

        /// <summary>
        /// 取得日期區間內特定員工已申請與申請中的請假日期與工時
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">請假啟始日期</param>
        /// <param name="endDate">請假結束日期</param>
        /// <returns>日期區間內特定員工的請假工時</returns>
        public Dictionary<DateTime, int> GetAppliedHoursBetween(string empNo, DateTime startDate, DateTime endDate);

        /// <summary>
        /// 讀取員工指定月份(依據查詢日期 )休假統計時數，
        /// 包括年度總可用時數、年度已使用時數(簽核中＋已核可)、累至查詢月份已使用時數(簽核中＋已核可)、
        /// 查詢月份已使用時數(簽核中＋已核可)、年度已核可時數(已核可)、
        /// 累至查詢月份年度已核可時數(已核可)、查詢月份已核可時數(已核可)
        /// 提供
        /// 01：特別休息假
        /// 12：補休假
        /// 14：延休假
        /// </summary>
        /// <param name="date">查詢日期</param>
        /// <param name="employeeNumber">員工編號</param>
        public List<EmpLeaveInfo> GetEmpLeaveInfo(DateTime theDate, string employeeNumber);

        /// <summary>
        /// 取得員工詳細資料
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns></returns>
        public Employee GetEmployeeDetail(string employeeNumber);

        // 新版，適用於 非正常班別員工有個人行事曆
        /// <summary>查詢指定期間內員工之行事曆資料。</summary>
        /// <param name="startDate">起始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>List<Workday>(行事曆資料)</returns>
        public List<Workday> GetEmpWorkdaysDateRange(DateTime startDate, DateTime endDate, string empNo);

        /// <summary>
        /// 取得同一事件的請假記錄（以事件的第一筆請假卡號查詢相關請假記錄）
        /// </summary>
        /// <param name="formNo">表單編號</param>
        /// <returns></returns>
        public List<EventRelationRecord> GetEventRelatedRecord(string formNo);

        /// <summary>
        /// 取得同一事件的請假記錄（以事件的第一筆請假卡號查詢相關請假記錄）
        /// </summary>
        /// <param name="formNo">表單編號</param>
        /// <returns></returns>
        public string GetEventRelatedRecordJson(string formNo);


        /// 取得指定員工事件假有效期限內的相關卡號資料
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="leaveNumber">假別編號</param>
        /// <param name="leaveSubNumber">假別細項編號</param>
        /// <returns></returns>
        public DataTable GetEventRelatedSheets(string empNo, int leaveNumber, int leaveSubNumber);

        /// <summary>
        /// 取得指定員工事件假有效期限內的相關卡號資料
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="leaveNumber">假別編號</param>
        /// <param name="leaveSubNumber">假別細項編號</param>
        /// <returns></returns>
        public string GetEventRelatedSheetsJson(string empNo, int leaveNumber, int leaveSubNumber);

        /// <summary>
        /// 取得員工某年的第一個工作日期，用以計算請假期限
        /// </summary>
        /// <param name="empNo"></param>
        /// <param name="year"></param>
        /// <returns></returns>
        public DateTime GetFirstWorkdayOfYear(string empNo, int year);

        /// <summary>
        /// 取得員工某年的最後一個工作日期，用以計算請假期限
        /// </summary>
        /// <param name="empNo"></param>
        /// <param name="year"></param>
        /// <returns></returns>
        public DateTime GetLastWorkdayOfYear(string empNo, int year);

        /// <summary>
        /// 取得假別附件提示
        /// </summary>
        /// <param name="kindEnum"></param>
        /// <returns></returns>
        public string GetLeaveAttachmentPrompt(LeaveKindEnum kindEnum);

        /// <summary>
        /// 取得請假日數時數
        /// </summary>
        /// <param name="leaveNumber">假別編號</param>
        /// <param name="startDate">請假起始日期</param>
        /// <param name="endDate">請假結束日期</param>
        /// <returns>請假日數時數字串，異常時傳回空字串</returns>

        public string GetLeaveDayHours(int leaveNumber, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Gets 假別，若無此編號則傳回 Null
        /// </summary>
        /// <param name="leaveNumber">假別編號</param>
        /// <returns>假別</returns>
        public LeaveKind? GetLeaveKind(LeaveKindEnum leaveNumber);

        /// <summary>
        /// 取得所有休假別
        /// </summary>
        /// <returns></returns>
        public List<LeaveKind> GetLeaveKinds();

        /// <summary>
        /// 取得所有休假別 JSON
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public string GetLeaveKindsString(string empNo);

        /// <summary>
        /// 取得假別上限
        /// </summary>
        /// <param name="c1Card"></param>
        /// <returns></returns>
        public int? GetLeaveMaximum(C1Card c1Card);

        /// <summary>
        /// 取得假別最小請假單位
        /// </summary>
        /// <param name="c1Card"></param>
        /// <returns></returns>
        public string GetLeaveMinimumUnit(C1Card c1Card);

        /// <summary>
        /// 取得切換假別時需顯示訊息
        /// </summary>
        /// <param name="leaveKindNo">假別代碼</param>
        /// <returns></returns>
        public string GetLeavePreAlert(string leaveKindNo);

        /// <summary>
        /// 取得假別單位
        /// </summary>
        /// <param name="c1Card"></param>
        /// <returns></returns>
        public string GetLeaveUnit(C1Card c1Card);

        /// <summary>取得相關於某單號的 C1CardDtos，不含流程與附件等</summary>
        /// <param name="formNumber">相關單號</param>
        /// <returns>卡 Card 物件 List</returns>
        public List<C1CardDto> GetRelatedCards(string formNumber);

        /// <summary>取得某單號的 C1CardDtos，不含流程與附件等</summary>
        /// <param name="formNumber">相關單號</param>
        /// <returns>卡 Card 物件 List</returns>
        public List<C1CardDto> GetCardsByFormNumber(string formNumber);

        /// <summary>
        /// 取得假別是否須檢附證明文件
        /// </summary>
        /// <param name="c1Card"></param>
        /// <returns></returns>
        public bool GetCertificateRequired(C1Card c1Card);

        /// <summary>
        /// 員工該年度選擇生日假
        /// </summary>
        /// <param name="year">西元年</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public bool HasChosenBirthdayLeave(int year, string empNo);

        /// <summary>
        /// 是否為管理員
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public bool IsAdmin(string empNo);

        /// <summary>
        /// 是否為現職員工
        /// </summary>
        /// <param name="employeeNumber">The employee number.</param>
        /// <returns>
        ///   <c>true</c> 是現職員工，否則傳回 <c>false</c>.
        /// </returns>
        public bool IsEmployee(string employeeNumber);

        /// <summary>
        /// 是否為工作日
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public bool IsWorkday(DateTime date, string empNo);

        /// <summary>Find the last work day in month.</summary>
        /// <param name="year">The year. 西元年</param>
        /// <param name="month">The month. 月分</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>最後一天工作日的日期(int)</returns>
        public DateTime LastWorkDayInMonth(int year, int month, string empNo);

        /// <summary>
        /// 取得切換假別時需顯示之訊息
        /// </summary>
        /// <param name="leaveKindNo">假別代碼</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>訊息</returns>
        public string LeaveKindAllowed(string leaveKindNo, string empNo);

        /// <summary>
        /// 假單設定簽核關卡
        /// </summary>
        /// <param name="c1Card">假單</param>
        /// <param name="form">表單</param>
        /// <param name="employee">員工</param>
        public void SetFlows(C1Card c1Card, Form form, Employee employee);

       /// <summary>
        /// 檢查個人休假資料表是否有該員工指定年月的資料
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="applyDate">指定年月</param>
        /// <returns>若指定個人休假資料表存在則回傳 true</returns>
        public bool IfLeaveRecordExists(string employeeNumber, DateTime from, DateTime to);

        /// <summary>
        /// 檢查該員工於指定時間之剩餘年度延休假時數（扣除「已同意」及「簽核中」)
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>該員工剩餘年度延休假時數</returns>
        public int GetPostponedLeaveRemainingHours(string employeeNumber, DateTime dt);

        /// <summary>
        /// 取出該員工剩餘年休假時數（扣除「已同意」及「簽核中」)
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>該員工剩餘年休假時數</returns>
        public int GetAnnualLeaveRemainingHours(string employeeNumber, DateTime dt);

        /// <summary>
        /// 取得補修日(含時間)前可補休的時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="applyDateTime">指定的補休時間</param>
        /// <returns></returns>
        public int GetCompensatoryLeaveRemainingHours(string employeeNumber, DateTime applyDateTime);





















        /// <summary>
        /// 取出該員工生日
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns>該員工生日</returns>
        public DateTime GetBirthday(string employeeNumber);

        /// <summary>
        /// 判斷該員工是否為女性
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns>若為女性則傳回true</returns>
        public bool IsFemale(string employeeNumber);

        /// <summary>
        /// 是否指定月份已經申請生理假
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="year">查詢年度，以西元年表示</param>
        /// <param name="month">查詢月份</param>
        /// <returns>是否已申請</returns>
        public bool IsMenstrualLeaveAlreadyTaken(string employeeNumber, int year, int month);

        /// <summary>
        /// 取得年度已休生理假時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>年度已休生理假時數</returns>
        public int GetMenstrualLeaveYearUsedHours(string employeeNumber, DateTime dt);

        ///// <summary>
        ///// 取得年度病假統計時數
        ///// </summary>
        ///// <param name="employeeNumber">員工編號</param>
        ///// <param name="year">查詢年度，以西元年表示</param>
        ///// <param name="yearAvailableHours">年度總可用時數</param>
        ///// <param name="yearUsedHours">年度已使用時數(簽核中＋已核可)</param>
        ///// <param name="yearApprovedHours">年度已核可時數</param>
        //public void GetSickLeaveYearInfo(string employeeNumber, int year,
        //    out int yearAvailableHours, out int yearUsedHours, out int yearApprovedHours);

        /// <summary>
        /// 取得病假年度總可用時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>年度總可用時數</returns>
        public int GetSickLeaveAvailableHours(string employeeNumber, DateTime dt);

        /// <summary>
        /// 取得病假年度已使用時數(簽核中＋已核可)
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>年度已使用時數(簽核中＋已核可)</returns>

        public int GetSickLeaveUsedHours(string employeeNumber, DateTime dt);

        /// <summary>
        /// 取得病假年度已核可時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>年度已核可時數</returns>
        public int GetSickLeaveApprovedHours(string employeeNumber, DateTime dt);

        ///// <summary>
        ///// 取得年度事假統計時數
        ///// </summary>
        ///// <param name="employeeNumber">員工編號</param>
        ///// <param name="year">查詢年度，以西元年表示</param>
        ///// <param name="yearAvailableHours">年度總可用時數</param>
        ///// <param name="yearUsedHours">年度已使用時數(簽核中＋已核可)</param>
        ///// <param name="yearApprovedHours">年度已核可時數</param>
        //public void GetPersonalLeaveYearInfo(string employeeNumber, int year,
        //    out int yearAvailableHours, out int yearUsedHours, out int yearApprovedHours);


        /// <summary>
        /// 取得事假年度總可用時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>年度總可用時數</returns>
        public int GetPersonalLeaveYearAvailableHours(string employeeNumber, DateTime dt);

        /// <summary>
        /// 取得事假年度已使用時數(簽核中＋已核可)
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>年度已使用時數(簽核中＋已核可)</returns>
        public int GetPersonalLeaveYearUsedHours(string employeeNumber, DateTime dt);

        /// <summary>
        /// 取得事假年度已核可時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>年度已核可時數</returns>
        public int GetPersonalLeaveYearApprovedHours(string employeeNumber, DateTime dt);

        ///// <summary>
        ///// 取得年度家庭照顧假統計時數
        ///// </summary>
        ///// <param name="employeeNumber">員工編號</param>
        ///// <param name="year">查詢年度，以西元年表示</param>
        ///// <param name="yearAvailableHours">年度總可用時數</param>
        ///// <param name="yearUsedHours">年度已使用時數(簽核中＋已核可)</param>
        ///// <param name="yearApprovedHours">年度已核可時數</param>
        //public void GetFamilyCareLeaveYearInfo(string employeeNumber, int year,
        //    out int yearAvailableHours, out int yearUsedHours, out int yearApprovedHours);

        /// <summary>
        /// 取得家庭照顧假年度總可用時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>年度總可用時數</returns>
        public int GetFamilyCareLeaveYearAvailableHours(string employeeNumber, DateTime dt);

        /// <summary>
        /// 取得家庭照顧假年度已使用時數(簽核中＋已核可)
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>年度已使用時數(簽核中＋已核可)</returns>
        public int GetFamilyCareLeaveYearUsedHours(string employeeNumber, DateTime dt);

        /// <summary>
        /// 取得家庭照顧假年度已核可時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>年度已核可時數</returns>
        public int GetFamilyCareLeaveYearApprovedHours(string employeeNumber, DateTime dt);

        ///// <summary>
        ///// 取得員工生日假資料，包括查詢年度選擇的生日假方案、是否已經使用生日假B
        ///// </summary>
        ///// <param name="employeeNumber">員工編號</param>
        ///// <param name="year">查詢年度，以西元年表示</param>
        ///// <param name="birthdayWelfare">查詢年度的生日假方案，0-查無資料，1-生日假，2-生日禮券</param>
        ///// <param name="isUsed">查詢年度是否已經使用生日假</param>
        //public void GetBirthdayLeaveInfo(string employeeNumber, int year, 
        //    out int birthdayWelfare, out bool isUsed);

        /// <summary>
        /// 取得員工查詢年度選擇的生日假方案
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="year">查詢年度，以西元年表示</param>
        /// <returns>查詢年度的生日假方案，0-查無資料，1-生日假，2-生日禮券</returns>
        public int GetBirthdayWelfare(string employeeNumber, int year);

        /// <summary>
        /// 取得員工年度是否已經使用生日假
        /// </summary>
        /// <param name="employeeNumber"></param>
        /// <param name="year"></param>
        /// <returns>是否已經使用生日假</returns>
        public bool IsBirthdayLeaveTaken(string employeeNumber, int year);

        /// <summary>
        /// 取得育嬰假年度總可用時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>年度總可用時數</returns>
        public int GetParentalLeaveYearAvailableHours(string employeeNumber, DateTime dt);

        /// <summary>
        /// 取得育嬰假年度已使用時數(簽核中＋已核可)
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>年度已使用時數(簽核中＋已核可)</returns>
        public int GetParentalLeaveYearUsedHours(string employeeNumber, DateTime dt);

        /// <summary>
        /// 取得育嬰假年度已核可時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>年度已核可時數</returns>
        public int GetParentalLeaveYearApprovedHours(string employeeNumber, DateTime dt);

    }
}

