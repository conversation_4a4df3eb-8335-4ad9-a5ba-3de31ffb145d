﻿using Microsoft.Extensions.Configuration;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.Utilities.DataAccess.Interfaces;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Runtime.Caching;
using static Antlr4.Runtime.Atn.SemanticContext;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic
{
    /// <summary>
    /// SinoSign 商業物件
    /// </summary>
    public class SinoSignBo : ISinoSignBo
    {
        private readonly ISinoSignDao _sinoSignDao;
        private readonly IConfiguration _configuration;
        private readonly bool _IsMultipleVicePresidents = false;

        private static ObjectCache _cache = MemoryCache.Default;
        private readonly static object CacheLock = new object();
        private static CacheItemPolicy CachePolicy
        {
            get
            {
                CacheItemPolicy policy = new CacheItemPolicy();
                policy.AbsoluteExpiration = DateTimeOffset.Now.AddMinutes(2.0);
                return policy;
            }
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="SinoSignBo"/> class.
        /// </summary>
        /// <param name="sinoSignDao">The SinoSign DAO.</param>
        /// <param name="configuration"></param>
        public SinoSignBo(ISinoSignDao sinoSignDao, IConfiguration configuration)
        {
            _sinoSignDao = sinoSignDao;
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            // 取得設定檔 IsMultipleVicePresidents
            if (_configuration != null)
            {
                var k = _configuration["IsMultipleVicePresidents"];
                if (!string.IsNullOrWhiteSpace(k))
                {
                    _IsMultipleVicePresidents = _configuration.GetValue<bool>("IsMultipleVicePresidents");
                }
            }
        }

        /// <summary>取得代理人</summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>
        ///   <para>代理人List</para>
        /// </returns>
        public Dictionary<string, string> GetDeputies(string empNo)
        {
            Dictionary<string, string> agents = new Dictionary<string, string>();
            if (empNo != null)
            {
                DataTable dtDeputies = _sinoSignDao.GetDeputies(empNo);
                foreach (DataRow dr in dtDeputies.Rows)
                {
                    agents.Add((string)dr["deputyuid"], (string)dr["name"]);
                }
            }
            return agents;
        }

        /// <summary>
        /// 取得具備角色之預設員工(非代理)
        /// </summary>
        /// <param name="role">角色</param>
        /// <returns>員工編號 List</returns>
        public List<string> GetRoleDefaultUser(string role)
        {
            List<string> empNos = new List<string>();
            DataTable dt = _sinoSignDao.GetRoleDefaultUser(role);

            foreach (DataRow dr in dt.Rows)
            {
                string empNo = (string)dr["deputyuid"];
                empNos.Add(empNo.Trim());
            }

            if (_IsMultipleVicePresidents && role == "S03") // 僅在有2位副執行長時才加入 S04 和 S05
            {
                List<string> s04 = GetRoleUsers("S04");
                if (s04.Count > 0)
                {
                    empNos = empNos.Union(s04).ToList();
                }
                List<string> s05 = GetRoleUsers("S05");
                if (s05.Count > 0)
                {
                    empNos = empNos.Union(s05).ToList();
                }
            }

            return empNos;
        }

        /// <summary>
        /// 取得具備角色之員工
        /// </summary>
        /// <param name="role">角色</param>
        /// <returns>員工編號 List</returns>
        public List<string> GetRoleUsers(string role)
        {
            List<string> empNos = new List<string>();
            DataTable dt = _sinoSignDao.GetRoleUsers(role);
            foreach (DataRow dr in dt.Rows)
            {
                string empNo = (string)dr["deputyuid"];
                if (empNo == null)
                {
                    continue;
                }
                empNo = empNo.Trim();
                if (empNos.Contains(empNo))
                {
                    continue; // 避免重覆
                }
                empNos.Add(empNo);
            }
            return empNos;
        }

        /// <summary>
        /// 取得所有角色群組名稱
        /// </summary>
        /// <returns></returns>
        public Dictionary<string, string> GetRoleGroupNames()
        {
            string cacheName = "GetRoleGroupNames";
            if (_cache.Contains(cacheName))
            {
                return (Dictionary<string, string>)_cache[cacheName];
            }
            Dictionary<string, string> roleGroupNames = new Dictionary<string, string>();
            DataTable dt = _sinoSignDao.GetRoleGroupNames();
            foreach (DataRow dr in dt.Rows)
            {
                string id = (string)dr["id"];
                string name = (string)dr["name"];
                if (!roleGroupNames.Keys.Contains(id))
                {
                    roleGroupNames.Add(id, name);
                }
            }
            lock (CacheLock)
            {
                _cache.Set(cacheName, roleGroupNames, CachePolicy);
            }
            return roleGroupNames;
        }

        /// <summary>
        /// 取得角色名稱
        /// </summary>
        /// <param name="roleId">角色代碼</param>
        /// <returns>角色名稱</returns>
        public string GetRoleName(string roleId)
        {       
            string roleName = string.Empty;
            string cacheName = $"GetRoleName-{roleId}";
            if (_cache.Contains(cacheName))
            {
                return (string)_cache[cacheName];
            }
            Dictionary<string, string> roleNames = GetRoleGroupNames();
            foreach (var role in roleNames)
            {
                if(role.Key == roleId)
                {
                    roleName = role.Value;
                    break;
                }
            }

            lock (CacheLock)
            {
                _cache.Set(cacheName, roleName, CachePolicy);
            }
            return roleName;
        }

        /// <summary>
        /// 是否有此Role，若無則傳回null
        /// </summary>
        /// <param name="empNo"></param>
        /// <param name="roleId"></param>
        /// <returns></returns>
        public Role? UserInRole(string empNo, string roleId)
        {
            List<Role> roles = GetUserRoles(empNo);
            foreach (Role role in roles)
            {
                if (role.RoleId == roleId)
                {
                    return role;
                }
            }
            return null;
        }

        /// <summary>
        /// 取得使用者正式角色
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>角色List</returns>
        public List<Role> GetUserFormalRoles(string empNo)
        {
            string cacheName = $"GetUserFormalRoles-{empNo}";
            if (_cache.Contains(cacheName))
            {
                return (List<Role>)_cache[cacheName];
            }
            List<Role> allRoles = GetUserRoles(empNo);
            List<Role> roles = new List<Role>();
            foreach (Role role in allRoles)
            {
                if (role.RoleType != RoleType.Agent)
                {
                    roles.Add(role);
                }
            }

            lock (CacheLock)
            {
                _cache.Set(cacheName, roles, CachePolicy);
            }
            return roles;
        }


        /// <summary>
        /// 取得使用者角色
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>角色List</returns>
        public List<Role> GetUserRoles(string empNo)
        {
            string cacheName = $"GetUserRoles-{empNo}";
            if (_cache.Contains(cacheName))
            {
                return (List<Role>)_cache[cacheName];
            }
            List<Role> roles = new List<Role>();
            if (empNo != null)
            {
                DataTable dtRoles = _sinoSignDao.GetUserRoles(empNo);
                foreach (DataRow dr in dtRoles.Rows)
                {
                    string name = EmployeeBo.CorrectJobName((string)dr["name"]);
                    string uid = (string)dr["uid"];
                    if (uid.Length == 2)
                    {
                        name += "登記桌";
                    }
                    RoleType roleType = RoleType.Role;
                    if (uid == empNo)
                    {
                        roleType = RoleType.Self;
                    }
                    else if (uid.Length == 4)
                    {
                        roleType = RoleType.Agent;
                    }
                    //若 userdeputy.modifyuid != "DB" 則視為代理
                    string modifyuid = (string)dr["modifyuid"];
                    if (modifyuid != "DB" && uid.Length != 2) // 登記桌 為特例，全視為正式
                    {
                        roleType = RoleType.Agent;
                    }

                    Role role = new Role(uid, name, roleType);
                    // 用  LINQ 搜
                    var kk = roles.FirstOrDefault(r => r.RoleId == role.RoleId);
                    if (kk == null) // 避免重覆
                    {
                        roles.Add(role);
                    }
                }


                if (roles.Count == 0)
                {
                    Role role = new Role(empNo, empNo, RoleType.Self);
                    roles.Add(role);
                }

                lock (CacheLock)
                {
                    _cache.Set(cacheName, roles, CachePolicy);
                }
            }
            return roles;
        }

        /// <summary>
        /// 取得部門收發員工編號列表
        /// </summary>
        /// <param name="deptNo">部門編號</param>
        /// <returns>部門收發員工編號 List</returns>
        public List<string> GetDepartmentMailroom(int deptNo)
        {
            string cacheName = $"GetDepartmentMailroom-{deptNo}";
            if (_cache.Contains(cacheName))
            {
                return (List<string>)_cache[cacheName];
            }
            List<string> mailrooms = new List<string>();
            DataTable dt = _sinoSignDao.GetDepartmentMailroom(deptNo);
            if (dt.Rows.Count > 0)
            {
                foreach (DataRow dr in dt.Rows)
                {
                    mailrooms.Add((string)dr["empno"]);
                }
            }
            lock (CacheLock)
            {
                _cache.Set(cacheName, mailrooms, CachePolicy);
            }
            return mailrooms;
        }
    }
}
