﻿using System;
using System.Collections.Generic;
using System.Data;

namespace Sinotech.Mis.Common
{
    /// <summary>
    /// 員工基本資料
    /// </summary>
    public class Employee
    {
        /// <summary>
        /// 員工編號
        /// </summary>
        public string EmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 生日
        /// </summary>
        public DateTime? Birthday { get; set; }

        /// <summary>
        /// 中文姓名
        /// </summary>
        public string CName { get; set; } = string.Empty;

        /// <summary>
        /// 部門編號
        /// </summary>
        public int DeptNo { get; set; }

        /// <summary>
        /// 部門簡稱
        /// </summary>
        public string DeptSName { get; set; } = string.Empty;

        /// <summary>
        /// 員工在職狀態
        /// </summary>
        public char Status { get; set; } = '0';

        /// <summary>
        /// 性別
        /// </summary>
        public string Sex { get; set; } = string.Empty;

        /// <summary>
        /// 職級編號
        /// </summary>
        public string RankNo { get; set; } = string.Empty;

        /// <summary>
        /// 職級名稱
        /// </summary>
        public string RankName { get; set; } = string.Empty;

        /// <summary>
        /// 職務編號
        /// </summary>
        public string? JobNo { get; set; }

        /// <summary>
        /// 職務名稱
        /// </summary>
        public string? JobName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? TelExtension { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? SpecLine { get; set; }

        /// <summary>
        /// 電子郵件
        /// </summary>
        public string? Email { get; set; }

        /// <summary>
        /// 組別編號
        /// </summary>
        public int? TeamID { get; set; }

        /// <summary>
        /// 組別名稱
        /// </summary>
        public string? TeamCName { get; set; }

        /// <summary>
        /// 是否為組長
        /// </summary>
        public bool IsTeamLeader { get; set; }

        /// <summary>
        /// 是否為司機
        /// </summary>
        public bool IsDriver { get; set; }

        /// <summary>
        /// 是否為副理以上
        /// </summary>
        public bool IsAboveDeputyManager { get; set; } = false;

        /// <summary>
        /// 是否為部門主管
        /// </summary>
        public bool IsManager { get; set; } = false;

        /// <summary>
        /// 是否為部門副主管
        /// </summary>
        public bool IsDeputyManager { get; set; } = false;

        /// <summary>
        /// 將 Employee DataTable 轉換為 List EmployeeView 物件
        /// </summary>
        /// <param name="table"></param>
        /// <returns></returns>
        public static List<Employee> DataTableToList(DataTable table)
        {
            List<Employee> list = new List<Employee>();
            foreach (DataRow row in table.Rows)
            {
                list.Add(FromDataRow(row));
            }
            return list;
        }

        /// <summary>
        /// 將 DataRow 轉換為 Employee 物件
        /// </summary>
        /// <param name="row"></param>
        /// <returns></returns>
        public static Employee FromDataRow(DataRow row)
        {
            Employee employee = new Employee();

            if (row.Table.Columns.Contains("EmpNo") && row["EmpNo"] != DBNull.Value)
            {
                employee.EmpNo = (string)row["EmpNo"];
            }
            if (row.Table.Columns.Contains("Birthday") && row["Birthday"] != DBNull.Value)
            {
                employee.Birthday = (DateTime?)row["Birthday"];
            }
            if (row.Table.Columns.Contains("CName") && row["CName"] != DBNull.Value)
            {
                employee.CName = (string)row["CName"];
            }
            if (row.Table.Columns.Contains("DeptNo") && row["DeptNo"] != DBNull.Value)
            {
                employee.DeptNo = (int)row["DeptNo"];
            }
            if (row.Table.Columns.Contains("DeptSName") && row["DeptSName"] != DBNull.Value)
            {
                employee.DeptSName = (string)row["DeptSName"];
            }
            if (row.Table.Columns.Contains("Status") && row["Status"] != DBNull.Value)
            {
                employee.Status = char.Parse(row["Status"].ToString());
            }
            if (row.Table.Columns.Contains("Sex") && row["Sex"] != DBNull.Value)
            {
                employee.Sex = (string)row["Sex"];
            }
            if (row.Table.Columns.Contains("RankNo") && row["RankNo"] != DBNull.Value)
            {
                employee.RankNo = (string)row["RankNo"];
            }
            if (row.Table.Columns.Contains("RankName") && row["RankName"] != DBNull.Value)
            {
                employee.RankName = (string)row["RankName"];
            }
            if (row.Table.Columns.Contains("JobNo") && row["JobNo"] != DBNull.Value)
            {
                employee.JobNo = (string?)row["JobNo"];
            }
            if (row.Table.Columns.Contains("JobName") && row["JobName"] != DBNull.Value)
            {
                employee.JobName = (string?)row["JobName"];
            }
            if (row.Table.Columns.Contains("TelExtension") && row["TelExtension"] != DBNull.Value)
            {
                employee.TelExtension = (string?)row["TelExtension"];
            }
            if (row.Table.Columns.Contains("SpecLine") && row["SpecLine"] != DBNull.Value)
            {
                employee.SpecLine = (string?)row["SpecLine"];
            }
            if (row.Table.Columns.Contains("Email") && row["Email"] != DBNull.Value)
            {
                employee.Email = (string?)row["Email"];
            }
            if (row.Table.Columns.Contains("TeamID") && row["TeamID"] != DBNull.Value)
            {
                employee.TeamID = (int?)row["TeamID"];
            }
            if (row.Table.Columns.Contains("TeamCName") && row["TeamCName"] != DBNull.Value)
            {
                employee.TeamCName = (string?)row["TeamCName"];
            }
            if (row.Table.Columns.Contains("IsTeamLeader") && row["IsTeamLeader"] != DBNull.Value)
            {
                employee.IsTeamLeader = (bool)row["IsTeamLeader"];
            }
            if (row.Table.Columns.Contains("IsDriver") && row["IsDriver"] != DBNull.Value)
            {
                employee.IsDriver = (bool)row["IsDriver"];
            }
            if (row.Table.Columns.Contains("IsAboveDeputyManager") && row["IsAboveDeputyManager"] != DBNull.Value)
            {
                employee.IsAboveDeputyManager = (bool)row["IsAboveDeputyManager"];
            }
            if (row.Table.Columns.Contains("IsManager") && row["IsManager"] != DBNull.Value)
            {
                employee.IsManager = (bool)row["IsManager"];
            }
            if (row.Table.Columns.Contains("IsDeputyManager") && row["IsDeputyManager"] != DBNull.Value)
            {
                employee.IsDeputyManager = (bool)row["IsDeputyManager"];
            }

            return employee;
        }
    }
}

