﻿using System;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 加班倍率補休
    /// </summary>
    public class B1Rate_CompHol
    {
        /// <summary>
        /// 流水號
        /// </summary>
        public int? ID { get; set; }

        /// <summary>
        /// 員工編號
        /// </summary>
        public string B1_EMPNO { get; set; } = "";

        /// <summary>
        /// 計畫編號
        /// </summary>
        public string B1_PROJNO { get; set; } = "";

        /// <summary>
        /// 加班時數
        /// </summary>
        public int B1_HOURS { get; set; } = 1;

        /// <summary>
        /// 加班倍率代碼: 1：1倍、2：4/3 (1.33)倍、3：5/3(1.66)倍、4：補休假、6：8/3 (2.66)倍
        /// </summary>
        public int B1_RATE { get; set; }

        /// <summary>
        /// 是否納入加班上限累計範圍
        /// </summary>
        public bool IsOvertime { get; set; }

        /// <summary>
        /// 加班日期: 民國年月日 YYYMMDD 不足位數前補零
        /// </summary>
        public string B1_YYMMDD { get; set; } = "";

        /// <summary>
        /// 加班卡卡號（表單單號）
        /// </summary>
        public string B1_SHEETNO { get; set; } = "";

        /// <summary>
        /// 補休假加班每日每小時加班順序：記錄每張補休假加班卡當日每1小時加班順序，供年底結算補休假未休報酬用
        /// </summary>
        public int B1_DayHourOrder { get; set; }

        /// <summary>
        /// 資料來源
        /// </summary>
        public string B1_SOURCE { get; set; } = "Attendance";

        /// <summary>
        /// 最後修改者員工編號
        /// </summary>
        public string? UpdatedEmpNo { get; set; }

        /// <summary>
        /// 最後修改者姓名
        /// </summary>
        public string? UpdatedName { get; set; }

        /// <summary>
        /// 最後修改日期時間
        /// </summary>
        public DateTime? UpdatedTime { get; set; }

        /// <summary>
        /// 最後修改來源IP 位址
        /// </summary>
        public string? UpdatedIP { get; set; }

        /// <summary>
        /// 最後修改來源電腦名稱
        /// </summary>
        public string? UpdatedHost { get; set; }
    }
}
