﻿using System;
using System.Data;

namespace Sinotech.Mis.Utilities.DataAccess.Interfaces
{
    /// <summary>工作日資料存取介面</summary>
    public interface IWorkdayDao
    {
        /// <summary>
        /// 取得該日的屬性
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns>日期屬性</returns>
        public int GetDayType(DateTime date, int shiftId = 1);

        /// <summary>
        /// 取得員工該日的班別日期屬性
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號/param>
        /// <returns>日期屬性</returns>
        public int GetDayType(DateTime date, string empNo);

        /// <summary>取得某日期所屬旬的最後一天工作日之日期</summary>
        /// <param name="year">The year. 西元年</param>
        /// <param name="month">The month. 月分(數字從1開始)</param>
        /// <param name="day">The day. 日</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns>最後一天工作日之日期</returns>
        public DateTime GetLastWorkDayInTenDays(int year, int month, int day, int shiftId = 1);

        /// <summary>
        /// 整月工時
        /// </summary>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <param name="shiftId">班別ID，預設 1 正常班</param>
        /// <returns>整月工時合計</returns>
        public int GetMonthWorkHours(int year, int month, int shiftId = 1);
        /// <summary>
        /// 取得該日的詳細資料
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns></returns>
        public DataTable GetWorkday(DateTime date, int shiftId = 1);


        /// <summary>
        /// 取得所有工作日類型 Data Table
        /// </summary>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns>所有工作日類型 Data Table</returns>
        public DataTable GetWorkdayTypes(int shiftId = 1);

        /// <summary>查詢指定期間內之日曆天資料。</summary>
        /// <param name="startDate">起始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns>DataTable(日曆天資料)</returns>
        public DataTable GetWorkdaysDateRange(DateTime startDate, DateTime endDate, int shiftId = 1);

        /// <summary>查詢指定期間內之日曆天資料。</summary>
        /// <param name="startDate">起始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="empNo">員工編號/param>
        /// <returns>DataTable(日曆天資料)</returns>
        public DataTable GetWorkdaysDateRange(DateTime startDate, DateTime endDate, string empNo);

        /// <summary>查詢指定期間內之班別，此處只會傳回有設定特殊班別的資料。</summary>
        /// <param name="startDate">起始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="empNo">員工編號/param>
        /// <returns>DataTable(日曆天資料)</returns>
        public DataTable GetShiftsDateRange(DateTime startDate, DateTime endDate, string empNo);

        /// <summary>
        /// 取得某月份的日曆天資料
        /// </summary>
        /// <param name="year">西元年</param>
        /// <param name="month">月</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns>DataTable(正常工作卡資料)</returns>
        public DataTable GetWorkDaysInMonth(int year, int month, int shiftId = 1);

        /// <summary>取得某日期所屬旬的最後一天工作日之日期</summary>
        /// <param name="year">西元年</param>
        /// <param name="month">月</param>
        /// <param name="tenDays">旬別(1:上旬、2:中旬、3:下旬)</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns>DataTable(日曆天資料)</returns>
        public DataTable GetWorkdaysInTendays(int year, int month, int tenDays, int shiftId = 1);

        /// <summary>該月分最後一個工作日的日期</summary>
        /// <param name="year">The year. 西元年</param>
        /// <param name="month">The month. 月分</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns>最後一個工作日的日期</returns>
        public DateTime LastWorkDayInMonth(int year, int month, int shiftId = 1);
    }
}
