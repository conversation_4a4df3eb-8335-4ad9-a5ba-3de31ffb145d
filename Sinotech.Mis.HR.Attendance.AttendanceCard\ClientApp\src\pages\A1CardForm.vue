<template>
  <h6 class="my-2 fs-2 text-center">
    <i class="bi bi-sun me-1" />
    <span>{{ FORM_ID.A1Card }}</span>
  </h6>
  <div class="container px-0 text-center">
    <div class="border border-dark-subtle border-2 mx-2 mx-sm-0">
      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-a1card">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>申</span>
              <span class="mx-1">請</span>
              <span>人</span>
            </div>
          </div>
        </div>
        <div class="col-9 col-md-10 py-2 text-start">
          <div class="row">
            <div class="col col-sm-5 col-md-4 col-lg-3 pe-0 py-0">
              <ChooseUser
                :modelValue="{
                  userId: employee.userId,
                  userName: employee.userName,
                  deptNo: employee.deptNo,
                  deptSName: employee.deptSName
                }"
                :employeeData="applyEmployeeData"
                :filter="onSearchEmployeeData"
                :clearable="false"
                :mode="'apply'"
                :disabled="submitted === true"
                @update:modelValue="onChangeEmployee"
              />
            </div>
            <div class="col-auto">
              <template v-if="userStore.userId !== employee?.userId">
                <span class="badge rounded-pill bg-warning">代填</span>
              </template>
            </div>
          </div>
        </div>
      </div>

      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-a1card">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>所屬部門</span>
            </div>
          </div>
        </div>
        <div class="col-9 col-md-10 py-2 d-flex align-items-center">
          {{ employee.deptSName }}
        </div>
      </div>

      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-a1card">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>填報旬別</span>
            </div>
          </div>
        </div>
        <div class="col-9 col-md-10 py-2 d-flex align-items-center text-start">
          <div class="row">
            <div class="col-12">
              <span class="d-none d-md-inline me-1">民國</span>
              <div class="d-inline-block">
                <select
                  class="form-select"
                  :style="{ 'min-width': '5rem' }"
                  :value="year"
                  :disabled="submitted === true"
                  @input="onChangeYear"
                >
                  <template
                    v-for="(yearOption, yearOptionIndex) in getThreeRocYears(filledTime.getFullYear())"
                    :key="yearOptionIndex"
                  >
                    <option :value="yearOption.year">
                      {{ yearOption.rocYear }}
                    </option>
                  </template>
                </select>
              </div>
              <span class="mx-1">年</span>

              <div class="d-inline-block">
                <select
                  class="form-select"
                  :value="month"
                  :disabled="submitted === true"
                  @input="onChangeMonth"
                >
                  <template
                    v-for="(monthOption, monthOptionIndex) in MONTHS_OPTIONS"
                    :key="monthOptionIndex"
                  >
                    <option
                      v-if="monthOption.optionValue !== 0"
                      :value="monthOption.optionValue"
                    >
                      {{ monthOption.field }}
                    </option>
                  </template>
                </select>
              </div>
              <span class="mx-1">月</span>

              <div class="d-inline-block">
                <select
                  class="form-select"
                  :value="tenDays.optionValue"
                  :disabled="submitted === true"
                  @input="onChangeTenDays"
                >
                  <template
                    v-for="tenDaysOption in TEN_DAYS_OPTIONS"
                    :key="tenDaysOption.optionValue"
                  >
                    <template v-if="parseInt(tenDaysOption.optionValue, 10) >= 0">
                      <option :value="tenDaysOption.optionValue">
                        {{ tenDaysOption.field }}
                      </option>
                    </template>
                  </template>
                </select>
              </div>
              <span class="ms-1">旬</span>
            </div>

            <template v-if="!calendarCheck">
              <div class="col-12 mt-2 text-danger">
                <small>行政處尚未公告{{ yearRoc }}年度行事曆</small>
              </div>
            </template>
            <template v-if="cardCheck === true">
              <div class="col-12 mt-2 text-danger">
                <small>該旬已填報，每旬限填一張工作卡！</small>
              </div>
            </template>
          </div>
        </div>
      </div>

      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-a1card">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>加會人員</span>
            </div>
          </div>
        </div>
        <div :class="['col-sm-auto py-2', (signers.length > 0) ? 'col-2' : 'col-auto']">
          <button
            type="button"
            :class="[
              'btn me-1 p-2',
              (((calendarCheck === false) || (cardCheck === true) || (submitted === true)) ? 'btn-outline-secondary' : 'btn-primary')
            ]"
            :disabled="(calendarCheck === false) || (cardCheck === true) || (submitted === true)"
            @click="onAddSignerClick"
          >
            <span>新增加會人員</span>
          </button>
        </div>

        <div class="col py-2">
          <template
            v-for="(signer, index) in signers"
            :key="index"
          >
            <div class="row mb-2">
              <div class="col-auto p-0">
                <span class="badge bg-secondary">
                  {{ index + 1 }}
                </span>
              </div>
              <div class="col col-sm-8 col-md-6 col-lg-4 pt-1">
                <ChooseColleague
                  :modelValue="signer.userId ? {
                    userId: signer.userId,
                    userName: signer.userName
                  } : null"
                  :customClass="((shake.signer === true) && (signer.userId === null)) ? 'shake' : ''"
                  :employeeData="employeeData.filter((employeeEle) => ((signers.findIndex((signerEle) => signerEle?.userId ? (signerEle.userId === employeeEle.userId) : false) === -1) && (employee.userId !== employeeEle.userId)))"
                  :employeeFilter="employee.userId"
                  :signerFilter="employeeData.filter((employeeEle) => ((signers.findIndex((signerEle) => signerEle?.userId ? (signerEle.userId === employeeEle.userId) : false) !== -1)))"
                  :placeholder="'請輸入或選取加會人員'"
                  :filter="onSearchEmployeeData"
                  :clearable="true"
                  :alwaysShowClearButton="true"
                  @update:modelValue="onChangeSigner($event, index)"
                  @delete="onDeleteSigner(index)"
                />
              </div>
            </div>
          </template>
        </div>
      </div>

      <div class="row mx-0 text-start">
        <DataTable
          :value="projectWorkingHoursData"
          class="px-0"
        >
          <template #header>
            <div class="row border-bottom border-dark-subtle">
              <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-a1card">
                <div class="d-table h-100 w-100">
                  <div class="d-table-cell align-middle text-center">
                    <span>填報計畫</span>
                  </div>
                </div>
              </div>
              <div class="col py-2">
                <div class="row">
                  <div class="col pt-1">
                    <ProjectSelect
                      :modelValue="project"
                      :projectData="projectData.filter(projectEle => projectWorkingHoursData.findIndex((e: any) => e.projectId === projectEle.id) === -1)"
                      :projectFilter="projectData.filter(projectEle => projectWorkingHoursData.findIndex((e: any) => e.projectId === projectEle.id) !== -1)"
                      :placeholder="'請輸入或選取計畫後，按【填寫計畫工時】'"
                      :clearable="true"
                      :disabled="(calendarCheck === false) || (cardCheck === true) || (submitted === true)"
                      @update:modelValue="onSelectProject"
                      @delete="onDeleteProject"
                    />
                    <button
                      type="button"
                      :class="['btn mt-2 me-1 p-1 p-sm-2', (((project === null) || (submitted === true)) ? 'btn-outline-secondary' : 'btn-primary')]"
                      :disabled="(project === null) || (submitted === true)"
                      @click="onAddRow"
                    >
                      <span>填寫計畫工時</span>
                    </button>
                  </div>
                  <div class="col-12 fw-normal text-secondary">
                    <small>※ 計畫工時須扣除請假卡時數(公出及出差除外)</small>
                  </div>
                  <div class="col-12 fw-normal text-secondary">
                    <small>※ 計畫進度已達100%第3個月起不可填報</small>
                  </div>
                </div>
              </div>
            </div>
          </template>

          <ColumnGroup type="header">
            <Row>
              <Column
                header="計畫工時"
                :rowspan="2"
              />
              <template
                v-for="(item, index) in columnDataArray"
                :key="index"
              >
                <Column
                  :field="item.field"
                  :class="item.workHours === 0 ? 'bg-light' : 'bg-white'"
                >
                  <template #header>
                    <span :class="item.workHours === 0 ? 'text-black-50' : ''">
                      {{ item.dayFormat }}
                    </span>
                  </template>
                </Column>
              </template>
            </Row>
            <Row>
              <template
                v-for="(item, index) in columnDataArray"
                :key="index"
              >
                <Column
                  :field="item.field"
                  :class="item.workHours === 0 ? 'bg-light' : 'bg-white'"
                >
                  <template #header>
                    <span :class="item.workHours === 0 ? 'text-black-50' : ''">
                      {{ item.date.toString() }}
                    </span>
                  </template>
                </Column>
              </template>
            </Row>
          </ColumnGroup>

          <Column
            field="projectId"
            class="text-center p-0"
            :bodyStyle="{ 'min-width': '130px' }"
          >
            <template #body="{ index, data, field }">
              <div class="row align-items-center">
                <div class="col-8 col-lg-6 text-end ps-0 pe-2 pe-lg-1">
                  {{ data[field] }}
                </div>
                <div class="col-2 col-lg-4 p-0">
                  <div class="row">
                    <div class="col-12 p-0">
                      <button
                        v-tooltip="{ value: '刪除計畫', escape: true }"
                        type="button"
                        class="btn btn-danger btn-sm px-1 py-0"
                        @click="onDeleteRow(index)"
                      >
                        <i class="bi bi-x-lg d-inline d-xl-none" />
                        <span class="d-none d-xl-inline">刪除</span>
                      </button>
                    </div>
                    <div class="col-12 p-0">
                      <button
                        v-tooltip="{ value: '工時歸零', escape: true }"
                        type="button"
                        class="btn btn-warning btn-sm text-white mx-1 px-1 py-0"
                        @click="onResetRow(index)"
                      >
                        <i class="bi bi-arrow-clockwise d-inline d-xl-none" />
                        <span class="d-none d-xl-inline">歸零</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </Column>

          <template
            v-for="(columnData, index) in columnDataArray"
            :key="index"
          >
            <Column
              :field="columnData.field"
              :class="[
                'text-center p-0',
                (columnData.workHours === 0 ? 'bg-light' : '')
              ]"
            >
              <template #body="{ data, field }">
                <input
                  type="text"
                  maxlength="2"
                  :aria-label="columnData.field"
                  :style="{ width: '3rem' }"
                  :class="[
                    'form-control d-inline-block text-center py-2 px-0',
                    columnData.workHours === 0 ? 'text-black-50' : '',
                    data['shake-' + field] === true ? 'shake' : ''
                  ]"
                  :value="data[field]"
                  :disabled="onInputHourDisabled(columnData, data, field)"
                  @blur="onBlurHour($event, data, field)"
                >
              </template>
            </Column>
          </template>

          <ColumnGroup
            v-if="projectWorkingHoursData.length > 0"
            type="footer"
          >
            <Row>
              <Column
                class="bg-a1card bg-opacity-50"
                footer="計畫工時總計"
                footerClass="text-center"
              />
              <template
                v-for="(item, index) in columnDataArray"
                :key="index"
              >
                <Column class="bg-a1card bg-opacity-50 text-center">
                  <template #footer>
                    <span :class="(projectWorkingHoursData.reduce((accumulator: any, currentValue: any) => accumulator + currentValue[item.field], 0) > item.workHours) ? 'text-danger' : ''">
                      {{ projectWorkingHoursData.reduce((accumulator: any, currentValue: any) => accumulator + currentValue[item.field], 0) }}
                    </span>
                  </template>
                </Column>
              </template>
            </Row>
          </ColumnGroup>
        </DataTable>
      </div>
    </div>
  </div>

  <div class="row mt-2 mx-0">
    <div class="col text-center">
      <button
        type="button"
        :class="[
          'btn mx-2',
          (((projectWorkingHoursData.length === 0) || (submitted === true)) ? 'btn-outline-secondary' : 'btn-primary')
        ]"
        :disabled="(projectWorkingHoursData.length === 0) || (submitted === true)"
        @click="onSubmit"
      >
        <span>送出</span>
      </button>
      <button
        type="button"
        :class="[
          'btn mx-2',
          (((projectWorkingHoursData.length === 0) || (submitted === true)) ? 'btn-outline-secondary' : 'btn-secondary')
        ]"
        :disabled="(projectWorkingHoursData.length === 0) || (submitted === true)"
        @click="onResetForm"
      >
        <span>重填</span>
      </button>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
// import { useRoute } from 'vue-router'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'
import { useAuthUserStore } from '../store/index'
import { useMessageStore } from '../store/message'
import { useWorkday } from '../composable/workdays'
import { useProject } from '../composable/project'
import { useEmployeeData } from '../composable/employeeData'
import { useAddSigner } from '../composable/signer'
import { useAbortController } from '../composable/abortController'
import DataTable from 'primevue/datatable'
import Row from 'primevue/row'
import Column from 'primevue/column'
import ColumnGroup from 'primevue/columngroup'
import ChooseUser from '../components/ChooseUser.vue'
import ChooseColleague from '../components/ChooseColleague.vue'
import ProjectSelect from '../components/ProjectSelect.vue'
import { GET_A1CARDISFILLED_URL, POST_SUBMITA1CARD_URL } from '../api/appUrl'
import { SHAKE_RESIST_TIME, MONTHS_OPTIONS, TEN_DAYS_OPTIONS, SYSTEM_ERROR_MESSAGE, SIGNERS_MAX, FORM_ID } from '../api/appConst'
import type { EmployeeStoreBaseType, SignerType, ProjectType, WorkdayType, FormSubmitResponseApiType, TenDaysOptionsType, A1CardSubmitType } from '../api/appType'
import { getThreeRocYears, onSearchEmployeeData } from '../api/appFunction'
import { onBeforeRouteLeave } from 'vue-router'
import { routerExtend } from '../router'

// const route = useRoute()
const userStore = useAuthUserStore()
const confirm = useConfirm()
const toast = useToast()
const { onGetWorkdaysDateRange } = useWorkday()
const { projectData, onGetProjectsDateRange } = useProject()
const { employeeData, onSetEmployeeData } = useEmployeeData()
const { signers, signersString, onChangeSigner, onChangeSigners, onAddSigner, onDeleteSigner, onDeleteSigners, onCheckSigner } = useAddSigner()
const { abortController, fetchErrorHandler, abortListener } = useAbortController()

/**
 * 前端開始填表時間
 */
const filledTime: Date = new Date()

const defaultTenDays = (time: Date) => {
  if (time.getDate() <= 10) {
    return TEN_DAYS_OPTIONS[1]
  } else if (time.getDate() <= 20) {
    return TEN_DAYS_OPTIONS[2]
  } else if (time.getDate() <= 31) {
    return TEN_DAYS_OPTIONS[3]
  } else {
    return TEN_DAYS_OPTIONS[0]
  }
}

const toastMessage = ref<string>('')
const year = ref<number>(filledTime.getFullYear())
const month = ref<number>(filledTime.getMonth() + 1)
const tenDays = ref<TenDaysOptionsType>(defaultTenDays(filledTime))

const shake = ref<{
  column: Array<string>
  signer: boolean
}>({
  column: [],
  signer: false
})
const calendarCheck = ref<boolean>(false)
const cardCheck = ref<boolean>(false)

const employee = ref<EmployeeStoreBaseType>({
  userId: userStore.userId,
  userName: userStore.userName,
  deptNo: userStore.deptNo,
  deptSName: userStore.deptSName
})
const applyEmployeeData = ref<Array<EmployeeStoreBaseType>>([])
const projectWorkingHoursData = ref<Array<any>>([])
const columnDataArray = ref<Array<{
  field: string
  date: number
  day: number
  dayFormat: string
  workHours: number
}>>([])
const project = ref<ProjectType | null>(null)
const submitted = ref<boolean>(false)

const yearRoc = computed<number>((): number => year.value - 1911)
const monthJs = computed<number>((): number => month.value - 1)
const startDateNumber = computed<number>((): number => parseInt(tenDays.value.optionValue, 10) * 10 + 1)
const endDateNumber = computed<number>((): number => parseInt(tenDays.value.optionValue, 10) < 2 ? (parseInt(tenDays.value.optionValue, 10) + 1) * 10 : new Date(year.value, monthJs.value + 1, 0).getDate())
const startDate = computed<Date>((): Date => new Date(year.value, monthJs.value, startDateNumber.value))
const endDate = computed<Date>((): Date => new Date(year.value, monthJs.value, endDateNumber.value))
const divisor = computed<number>((): number => endDateNumber.value - startDateNumber.value + 1 + 1)

/**
 * 切換申請人
 * @param event 單一名員工的資料
 */
const onChangeEmployee = (event: EmployeeStoreBaseType): void => {
  const tempprojectWorkingHoursData = projectWorkingHoursData.value
  const tempProject = project.value
  const tempEmployee = employee.value

  employee.value = {
    userId: event.userId,
    userName: event.userName,
    deptNo: event.deptNo,
    deptSName: event.deptSName
  }
  projectWorkingHoursData.value = []
  project.value = null

  if (tempprojectWorkingHoursData.length > 0) {
    confirm.require({
      group: 'app',
      header: '提醒',
      message: '目前填報計畫的資料將被清空，是否繼續？',
      acceptProps: {
        severity: 'primary'
      },
      rejectProps: {
        severity: 'secondary'
      },
      accept: (): void => {
        onLoadData()
      },
      reject: (): void => {
        employee.value = tempEmployee
        projectWorkingHoursData.value = tempprojectWorkingHoursData
        project.value = tempProject
      }
    })
  } else if (signers.value.find((e: SignerType) => e.userId === event.userId) !== undefined) {
    const tempAddSigner = signers.value
    onDeleteSigners()

    confirm.require({
      group: 'app',
      header: '提醒',
      message: '目前加會人員的資料將被清空，是否繼續？',
      acceptProps: {
        severity: 'primary'
      },
      rejectProps: {
        severity: 'secondary'
      },
      accept: (): void => {
        onLoadData()
      },
      reject: (): void => {
        employee.value = tempEmployee
        projectWorkingHoursData.value = tempprojectWorkingHoursData
        project.value = tempProject
        onChangeSigners(tempAddSigner)
      }
    })
  } else if (onCheckSigner() === true) {
    onDeleteSigners()
  } else {
    onLoadData()
  }
}

const onChangeYear = (event: Event): void => {
  const tempprojectWorkingHoursData = projectWorkingHoursData.value
  const tempProject = project.value
  const tempYear = year.value

  year.value = parseInt((event.target as HTMLSelectElement).value, 10)
  projectWorkingHoursData.value = []
  project.value = null

  if (tempprojectWorkingHoursData.length > 0) {
    confirm.require({
      group: 'app',
      header: '提醒',
      message: '目前填報計畫的資料將被清空，是否繼續？',
      acceptProps: {
        severity: 'primary'
      },
      rejectProps: {
        severity: 'secondary'
      },
      accept: (): void => {
        onLoadData()
      },
      reject: (): void => {
        year.value = tempYear
        projectWorkingHoursData.value = tempprojectWorkingHoursData
        project.value = tempProject
      }
    })
  } else {
    onLoadData()
  }
}

const onChangeMonth = (event: Event): void => {
  const tempprojectWorkingHoursData = projectWorkingHoursData.value
  const tempProject = project.value
  const tempMonth = month.value

  month.value = parseInt((event.target as HTMLSelectElement).value, 10)
  projectWorkingHoursData.value = []
  project.value = null

  if (tempprojectWorkingHoursData.length > 0) {
    confirm.require({
      group: 'app',
      header: '提醒',
      message: '目前填報計畫的資料將被清空，是否繼續？',
      acceptProps: {
        severity: 'primary'
      },
      rejectProps: {
        severity: 'secondary'
      },
      accept: (): void => {
        onLoadData()
      },
      reject: (): void => {
        month.value = tempMonth
        projectWorkingHoursData.value = tempprojectWorkingHoursData
        project.value = tempProject
      }
    })
  } else {
    onLoadData()
  }
}

const onChangeTenDays = (event: Event): void => {
  const tempprojectWorkingHoursData = projectWorkingHoursData.value
  const tempProject = project.value
  const tempTenDays = tenDays.value

  tenDays.value = TEN_DAYS_OPTIONS.find((e: TenDaysOptionsType) => e.optionValue === (event.target as HTMLSelectElement).value) ?? TEN_DAYS_OPTIONS[0]
  projectWorkingHoursData.value = []
  project.value = null

  if (tempprojectWorkingHoursData.length > 0) {
    confirm.require({
      group: 'app',
      header: '提醒',
      message: '目前填報計畫的資料將被清空，是否繼續？',
      acceptProps: {
        severity: 'primary'
      },
      rejectProps: {
        severity: 'secondary'
      },
      accept: (): void => {
        onLoadData()
      },
      reject: (): void => {
        tenDays.value = tempTenDays
        projectWorkingHoursData.value = tempprojectWorkingHoursData
        project.value = tempProject
      }
    })
  } else {
    onLoadData()
  }
}

const onSelectProject = (projectData: ProjectType): void => {
  project.value = projectData
}

const onDeleteProject = (): void => {
  project.value = null
}

const onBlurHour = (event: Event, data: any, field: string): void => {
  const value: number = parseInt((event.target as HTMLInputElement).value, 10)
  if (isNaN(parseInt((event.target as HTMLInputElement).value, 10)) || Number.isNaN(value) || value < 0 || (value.toString() !== (event.target as HTMLInputElement).value)) {
    toastMessage.value = '請填報正確的時數'
    data['shake-' + field] = true
    shake.value.column.push(field)
    setTimeout((): void => {
      const indexColumn: number = shake.value.column.findIndex((e: string) => e === field)
      shake.value.column.splice(indexColumn)
      data['shake-' + field] = false
    }, SHAKE_RESIST_TIME)
  } else {
    data[field] = value
  }

  if (toastMessage.value.length > 0) {
    toast.add({
      severity: 'warn',
      summary: toastMessage.value,
      group: 'app'
    })
    toastMessage.value = ''
  }
}

const onAddRow = (): void => {
  const rowData: any = {}
  if (project.value !== null) {
    rowData['projectId'] = project.value.id
    rowData['projectStartDate'] = project.value.startDate
    rowData['projectEndDate'] = project.value.endDate
  }

  for (let dateNumber: number = startDateNumber.value; dateNumber <= endDateNumber.value; dateNumber++) {
    const field: string = 'd' + (dateNumber - parseInt(tenDays.value.optionValue, 10) * 10 - 1) % divisor.value // 該旬之第一天以d0表示，之後的以此類推
    if (projectWorkingHoursData.value.length === 0) {
      const columnData = columnDataArray.value.find((e: {
        field: string
        date: number
        day: number
        dayFormat: string
        workHours: number
      }) => e.field === field)

      if (columnData !== undefined) {
        rowData[field] = columnData.workHours

        const date = new Date(year.value, monthJs.value, columnData.date)
        if (rowData.projectStartDate !== null) {
          if (date < new Date(rowData.projectStartDate)) {
            rowData[field] = 0
          }
        }
        if (rowData.projectEndDate !== null) {
          if (date > new Date(rowData.projectEndDate)) {
            rowData[field] = 0
          }
        }
      } else {
        rowData[field] = 0
      }
    } else {
      rowData[field] = 0
    }
  }
  projectWorkingHoursData.value.push(rowData)

  project.value = null
}

const onResetRow = (index: number): void => {
  confirm.require({
    group: 'app',
    header: '提醒',
    message: '確認歸零？',
    acceptProps: {
      severity: 'primary'
    },
    rejectProps: {
      severity: 'secondary'
    },
    accept: (): void => {
      for (let dateNumber: number = startDateNumber.value; dateNumber <= endDateNumber.value; dateNumber++) {
        const field: string = 'd' + (dateNumber - parseInt(tenDays.value.optionValue, 10) * 10 - 1) % divisor.value // 該旬之第一天以d0表示，之後的以此類推
        projectWorkingHoursData.value[index][field] = 0
      }
    }
  })
}

const onDeleteRow = (index: number): void => {
  confirm.require({
    group: 'app',
    header: '提醒',
    message: '確認刪除？',
    acceptProps: {
      severity: 'primary'
    },
    rejectProps: {
      severity: 'secondary'
    },
    accept: (): void => {
      const removed: Array<any> = projectWorkingHoursData.value.splice(index, 1)
      if (removed.length === 0) {
        toast.add({
          severity: 'error',
          summary: '系統發生例外狀況，刪除失敗',
          group: 'app'
        })
      }
    }
  })
}

const onAddSignerClick = (): void => {
  if (signers.value.find((e: SignerType) => e.userId === null)) {
    toast.add({
      severity: 'warn',
      summary: '請先輸入加會人員',
      group: 'app'
    })
  } else if (signers.value.length >= SIGNERS_MAX) {
    toast.add({
      severity: 'warn',
      summary: '僅開放最多加會' + SIGNERS_MAX + '名人員',
      group: 'app'
    })
  } else {
    onAddSigner()
  }
}

const onInputHourDisabled = (
  columnData: {
    field: string
    date: number
    day: number
    dayFormat: string
    workHours: number
  },
  rowData: any,
  rowDataField: string
): boolean => {
  const date = new Date(year.value, monthJs.value, columnData.date)

  if (columnData.workHours === 0) {
    return true
  }

  if (cardCheck.value === true) {
    return true
  }

  if (shake.value.column.find(e => e === rowDataField) !== undefined) {
    return true
  }

  if (submitted.value === true) {
    return true
  }

  if (rowData.projectStartDate !== null) {
    if (date < new Date(rowData.projectStartDate)) {
      return true
    }
  }
  if (rowData.projectEndDate !== null) {
    if (date > new Date(rowData.projectEndDate)) {
      return true
    }
  }

  return false
}

const onSubmit = (): void => {
  let checkSubmitted: number = 0
  submitted.value = true
  confirm.require({
    group: 'app',
    header: '提醒',
    message: '確認送出表單？',
    acceptProps: {
      severity: 'primary'
    },
    rejectProps: {
      severity: 'secondary'
    },
    accept: (): void => {
      checkSubmitted++
      if (checkSubmitted === 1) {
        // 因為要傳一旬每天的時數a1_DDHH，需要以下時數的檢查
        const columnDataIndex: number = columnDataArray.value.findIndex((columnEach: {
          field: string
          date: number
          day: number
          dayFormat: string
          workHours: number
        }) => projectWorkingHoursData.value.reduce((accumulator: any, currentValue: any) => accumulator + currentValue[columnEach.field], 0) > columnEach.workHours)

        if (onCheckSigner() === true) {
          shake.value.signer = true
          toast.add({
            severity: 'warn',
            summary: '請輸入或選取加會人員',
            group: 'app'
          })
          submitted.value = false
          setTimeout((): void => {
            shake.value.signer = false
          }, SHAKE_RESIST_TIME)
        } else if (columnDataIndex >= 0) {
          toast.add({
            severity: 'warn',
            summary: yearRoc.value + '/' + month.value + '/' + columnDataArray.value[columnDataIndex].date + ' 填報的工時超過當日的正常工作時數，請修正',
            group: 'app'
          })
          submitted.value = false
        } else {
          const sentTime: Date = new Date() // 前端按送出的時間
          const a1_YYMM: string = yearRoc.value.toString() + month.value.toString().padStart(2, '0') // 民國年月
          const a1_NN: string = (parseInt(tenDays.value.optionValue, 10) + 1).toString() // 上旬：1，中旬：2，下旬：3
          const a1_WYYMMDD: string = (sentTime.getFullYear() - 1911).toString() + (sentTime.getMonth() + 1).toString().padStart(2, '0') + sentTime.getDate().toString().padStart(2, '0') // 填卡日期民國年月日
          const postData: Array<A1CardSubmitType> = []

          // 1個計畫編號需要1筆旬卡資料
          projectWorkingHoursData.value.forEach((row: any) => {
            let dayWorkHours: string = '' // 每旬每一天個別的時數
            let totalWorkHours: number = 0 // 每旬每計畫工作總時數
            for (let dateNumber: number = startDateNumber.value; dateNumber <= endDateNumber.value; dateNumber++) {
              const field: string = 'd' + (dateNumber - parseInt(tenDays.value.optionValue, 10) * 10 - 1) % divisor.value // 該旬之第一天以d0表示，之後的以此類推
              totalWorkHours += row[field]
              dayWorkHours += row[field].toString()
            }

            postData.push({
              a1_EMPNO: employee.value.userId,
              a1_YYMM: a1_YYMM,
              a1_NN: a1_NN,
              a1_PROJNO: row.projectId, // 計畫編號
              a1_DDHH: dayWorkHours,
              a1_HOUR: totalWorkHours,
              a1_WYYMMDD: a1_WYYMMDD,
              a1_SOURCE: 'Attendance', // 寫入資料來源
              addSigners: signersString.value,
              a1_WDate: sentTime,
              createdTime: sentTime,
              filledTime: filledTime
            })
          })

          fetch(POST_SUBMITA1CARD_URL, {
            method: 'POST',
            headers: {
              'content-type': 'application/json'
            },
            body: JSON.stringify(postData),
            signal: abortController.signal
          }).then((res: Response): Promise<FormSubmitResponseApiType> => {
            if (!res.ok) {
              throw new Error(res.status.toString())
            }
            return res.json()
          }).then((res: FormSubmitResponseApiType): void => {
            if (res.Status === 0) {
              const messageStore = useMessageStore()
              messageStore.setData('表單已送出')

              routerExtend.pushHandler('Message')
            } else {
              toast.add({
                severity: 'warn',
                summary: res.Message,
                group: 'app'
              })
            }
          }).catch((err: Error): void => {
            console.error(err)
            fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
          }).finally((): void => {
            submitted.value = false
          })
        }
      }
    },
    reject: (): void => {
      submitted.value = false
    }
  })
}

const onResetForm = (): void => {
  submitted.value = true
  confirm.require({
    group: 'app',
    header: '提醒',
    message: '確認重置表單？',
    acceptProps: {
      severity: 'primary'
    },
    rejectProps: {
      severity: 'secondary'
    },
    accept: (): void => {
      toast.add({
        severity: 'success',
        summary: '表單已重置',
        group: 'app'
      })

      const resetDate: Date = filledTime
      year.value = resetDate.getFullYear()
      month.value = resetDate.getMonth() + 1
      tenDays.value = defaultTenDays(resetDate)
      // onSetDateParameter()

      employee.value = {
        userId: userStore.userId,
        userName: userStore.userName,
        deptNo: userStore.deptNo,
        deptSName: userStore.deptSName
      }
      projectWorkingHoursData.value = []
      project.value = null

      onLoadData()
      submitted.value = false
    },
    reject: (): void => {
      submitted.value = false
    }
  })
}

const onSetColumnData = (dataSet: Array<WorkdayType>): void => {
  columnDataArray.value = []
  if (dataSet.length > 0) {
    calendarCheck.value = true
    for (let dateNumber: number = startDateNumber.value; dateNumber <= endDateNumber.value; dateNumber++) {
      const field: string = 'd' + (dateNumber - parseInt(tenDays.value.optionValue, 10) * 10 - 1) % divisor.value // 該旬之第一天以d0表示，之後的以此類推
      const found: WorkdayType | undefined = dataSet.find((e: WorkdayType) => new Date(e.workDate).getDate() === dateNumber)

      let workHours: number = 0
      if (found?.dayOff !== true) {
        workHours = found?.workHours ?? 0
      }
      columnDataArray.value.push({
        field: field,
        date: dateNumber,
        day: new Date(year.value, monthJs.value, dateNumber).getDay(),
        dayFormat: new Intl.DateTimeFormat('zh-Hant-TW', { weekday: 'short' }).format(new Date(year.value, monthJs.value, dateNumber)).slice(-1),
        workHours: workHours
      })
    }
  } else {
    calendarCheck.value = false
    for (let dateNumber: number = startDateNumber.value; dateNumber <= endDateNumber.value; dateNumber++) {
      const field: string = 'd' + (dateNumber - parseInt(tenDays.value.optionValue, 10) * 10 - 1) % divisor.value // 該旬之第一天以d0表示，之後的以此類推

      columnDataArray.value.push({
        field: field,
        date: dateNumber,
        day: new Date(year.value, monthJs.value, dateNumber).getDay(),
        dayFormat: new Intl.DateTimeFormat('zh-Hant-TW', { weekday: 'short' }).format(new Date(year.value, monthJs.value, dateNumber)).slice(-1),
        workHours: 0
      })
    }
  }
}

const onLoadDateCanFillCard = async (): Promise<void> => {
  const params = new URLSearchParams({
    empNo: employee.value.userId,
    chineseYearMonth: (year.value - 1911).toString() + (monthJs.value + 1).toString().padStart(2, '0'),
    tenDays: (parseInt(tenDays.value.optionValue, 10) + 1).toString()
  })
  await fetch(GET_A1CARDISFILLED_URL + '?' + params, {
    method: 'GET',
    signal: abortController.signal
  }).then((res: Response): Promise<any> => {
    if (!res.ok) {
      throw new Error(res.status.toString())
    }
    return res.json()
  }).then((res: any): void => {
    cardCheck.value = (res.FormNo !== null)
  }).catch((err: Error): void => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })
}

const onLoadData = async (): Promise<void> => {
  let workdayData: Array<WorkdayType> = []
  try {
    workdayData = await onGetWorkdaysDateRange(startDate.value, endDate.value, employee.value.userId, abortController.signal)
  } catch (err: unknown) {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  }

  onSetColumnData(workdayData)

  try {
    await onGetProjectsDateRange(startDate.value, endDate.value, employee.value.deptNo, abortController.signal)
  } catch (err: unknown) {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  }

  await onLoadDateCanFillCard()
  if (cardCheck.value === true) {
    signers.value = []
  }

  await onSetEmployeeData(userStore.deptNo, abortController.signal).catch((err: Error): void => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })
  applyEmployeeData.value = employeeData.value

  onSetEmployeeData(employee.value.deptNo, abortController.signal).catch((err: Error): void => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })
}

// const onSetDateParameter = () => {
//   let routeYearString = (route.params?.year === undefined) ? '' : route.params.year.toString()
//   let routeMonthString = (route.params?.month === undefined) ? '' : route.params.month.toString()
//   let routeTenDaysString = (route.params?.tenDays === undefined) ? '' : route.params.tenDays.toString()
//   if ((routeYearString.length > 0) && (routeMonthString.length > 0) && (routeTenDaysString.length > 0)) {
//     let routeYear = parseInt(routeYearString, 10)
//     let routeMonth = parseInt(routeMonthString, 10)
//     let routeTenDays = parseInt(routeTenDaysString, 10)

//     let check = false
//     let yearCheck = getThreeRocYears(filledTime.getFullYear())
//     if ((routeYear < yearCheck[0].year) || (routeYear > yearCheck[2].year)) {
//       check = true
//     } else if ((routeMonth < 1) || (routeMonth > 12)) {
//       check = true
//     } else if ((routeTenDays < parseInt(TEN_DAYS_OPTIONS[1].optionValue, 10)) || (routeTenDays > parseInt(TEN_DAYS_OPTIONS[3].optionValue, 10))) {
//       check = true
//     }

//     if (check === false) {
//       year.value = routeYear
//       month.value = routeMonth
//       tenDays.value = TEN_DAYS_OPTIONS[routeTenDays + 1]
//     }
//   }
// }

onBeforeRouteLeave((): void => {
  toast.removeGroup('app')
})

onMounted(async (): Promise<void> => {
  abortListener()

  // onSetDateParameter()
  onLoadData()
  await onSetEmployeeData(userStore.deptNo, abortController.signal).catch((err: Error): void => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })
  applyEmployeeData.value = employeeData.value
})
</script>
<style lang="scss" scoped>
:deep(.p-datatable-empty-message) {
  display: none;
}
:deep(.p-datatable .p-datatable-column-header-content) {
  justify-content: center;
  color: black;
}
:deep(.p-datatable .p-datatable-header) {
  padding: 0 12px;
  background: #ffffff;
  color: black;
  border: none;
}
:deep(.p-datatable .p-datatable-thead > tr > th) {
  background: #ffffff;
}
:deep(.p-datatable .p-datatable-tfoot > tr > td) {
  color: black;
}
</style>