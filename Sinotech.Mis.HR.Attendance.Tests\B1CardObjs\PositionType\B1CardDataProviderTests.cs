﻿using Sinotech.Mis.Common;
using Sinotech.Mis.HR.Attendance.Common;
using System.Collections.Generic;
using Xunit;
#nullable enable

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.PositionType.Tests
{
    public class B1CardDataProviderTests
    {
        [Theory]
        [InlineData("2000", "nouser", "2000")]
        [InlineData("RP19553", "0305", "RP19553")]
        [InlineData("fakeProjNo", "fakeuser", "fakeProjNo")]
        public void GetProjectInfoTest(string projectNumber, string empNo, string expected)
        {
            B1CardParameters parameters = new B1CardParameters
            {
                Projects = new List<Project>
                {
                    new Project
                    {
                        PrjNo = projectNumber,
                        PrjName = "Test Project"
                    }
                },
                B1Card = new B1Card
                {
                    EmpNo = empNo
                },
                EmployeeDetail = new Employee
                {
                    EmpNo = empNo
                }
            };
            B1CardDataProvider provider = new B1CardDataProvider(parameters);
            Project? actual = provider.GetProjectInfo(projectNumber);
            Assert.NotNull(actual);
            
            Assert.Equal(expected, actual.PrjNo);
        }

        [Fact]
        public void GetSpecialStaffAllowedMonthlyWeightedOvertimeHours_ShouldReturnCorrectValue()
        {
            // Arrange
            var parameters = new B1CardParameters
            {
                SpecialStaffAllowedMonthWeightedOvertimeHours = 10.5
            };
            var provider = new B1CardDataProvider(parameters);

            // Act
            var result = provider.GetSpecialStaffAllowedMonthlyWeightedOvertimeHours();

            // Assert
            Assert.Equal(10.5, result);
        }

        [Fact]
        public void GetCurrentMonthlyWeightedOvertimeHours_ShouldReturnCorrectValue()
        {
            // Arrange
            var parameters = new B1CardParameters
            {
                CurrentMonthWeightedOvertimeHours = 8.0
            };
            var provider = new B1CardDataProvider(parameters);

            // Act
            var result = provider.GetCurrentMonthlyWeightedOvertimeHours();

            // Assert
            Assert.Equal(8.0, result);
        }

        [Fact]
        public void GetWeightedOvertimeHours_ShouldReturnCorrectValue()
        {
            // Arrange
            var parameters = new B1CardParameters
            {
                DayOvertime = new DayOvertime
                {
                    PaidHours = 5.5
                }
            };
            var provider = new B1CardDataProvider(parameters);

            // Act
            var result = provider.GetWeightedOvertimeHours();

            // Assert
            Assert.Equal(5.5, result);
        }
    }
}

