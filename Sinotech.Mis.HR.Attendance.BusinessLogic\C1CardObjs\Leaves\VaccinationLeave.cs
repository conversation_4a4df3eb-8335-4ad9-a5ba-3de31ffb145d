﻿using Sinotech.Mis.HR.Attendance.Common;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves
{
    /// <summary>
    /// 疫苗接種假類別
    /// </summary>
    /// <seealso cref="Leaves.LeaveBase" />
    [LeaveKind(LeaveKindEnum.VaccinationLeave)]
    public class VaccinationLeave : C1CardLeaveNotProvided
    {

        /// <summary>
        /// 疫苗接種假建構子
        /// </summary>
        /// <param name="c1Card"></param>
        /// <param name="c1CardBo"></param>
        public VaccinationLeave(C1Card c1Card, IC1CardBo c1CardBo) : base(c1Card, c1CardBo)
        {
        }
    }
}