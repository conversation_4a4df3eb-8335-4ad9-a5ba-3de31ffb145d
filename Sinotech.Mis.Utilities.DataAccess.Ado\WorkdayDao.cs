﻿using Sinotech.Mis.Helpers;
using Sinotech.Mis.Utilities.DataAccess.Interfaces;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;

namespace Sinotech.Mis.Utilities.DataAccess.Ado
{
    /// <summary>
    /// 工作日資料存取元件
    /// </summary>
    public class WorkdayDao : IWorkdayDao
    {
        private readonly string _connectionString;

        /// <summary>Initializes a new instance of the <see cref="WorkdayDao" /> class.</summary>
        /// <param name="connectionString">The connection string.</param>
        public WorkdayDao(string connectionString)
        {
            _connectionString = connectionString;
        }

        /// <summary>
        /// 取得該日的詳細資料
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns></returns>
        public DataTable GetWorkday(DateTime date, int shiftId = 1)
        {
            string sqlstr = @"SELECT Workday.ShiftId, Workday.WorkDate, Workday.DayType, Workday.ArrivalTime, Workday.DepartureTime, 
 Workday.FlexibleArrivalBefore, Workday.FlexibleArrivalAfter, Workday.FlexibleDepartureBefore, 
 Workday.FlexibleDepartureAfter, Workday.MiddayBreakStart, Workday.MiddayBreakEnd, Workday.MorningRestStart, 
 Workday.MorningRestEnd, Workday.AfternoonRestStart, Workday.AfternoonRestEnd, Workday.WorkHours, 
 Workday.WeekDay, Workday.Comment, WorkdayType.TypeName, WorkdayType.DayOff
FROM Workday INNER JOIN WorkdayType ON Workday.DayType = WorkdayType.TypeId WHERE WorkDate=@WorkDate AND ShiftId=@ShiftId;";

            SqlParameter paramWorkDate = new SqlParameter("@WorkDate", SqlDbType.Date);
            paramWorkDate.Value = date;
            SqlParameter paramShiftId = new SqlParameter("@ShiftId", SqlDbType.SmallInt);
            paramShiftId.Value = shiftId;
            SqlParameter[] parameters = new SqlParameter[2];
            parameters[0] = paramWorkDate;
            parameters[1] = paramShiftId;
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sqlstr, parameters);
            return dt;
        }

        /// <summary>
        /// 取得所有工作日類型 Data Table
        /// </summary>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns>所有工作日類型 Data Table</returns>
        public DataTable GetWorkdayTypes(int shiftId = 1)
        {
            string sqlstr = @"SELECT * FROM WorkdayType;";

            DataTable dt = SqlHelper.GetDataTable(_connectionString, sqlstr);
            return dt;
        }

        /// <summary>
        /// 整月工時
        /// </summary>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <param name="shiftId">班別ID，預設 1 正常班</param>
        /// <returns>整月工時合計</returns>
        public int GetMonthWorkHours(int year, int month, int shiftId = 1)
        {
            int monthWorkHours = 0;
            string sqlstr = "Select SUM(WorkHours) AS MonthWorkHours FROM Workday WHERE WorkDate >= @StartDate AND WorkDate < @EndDate AND ShiftId=@ShiftId";
            DateTime startDate = new DateTime(year, month, 1, 0, 0, 0, DateTimeKind.Local);
            DateTime endDate = startDate.AddMonths(1);
            SqlParameter paramStartDate = new SqlParameter("@StartDate", SqlDbType.Date);
            paramStartDate.Value = startDate;
            SqlParameter paramEndDate = new SqlParameter("@EndDate", SqlDbType.Date);
            paramEndDate.Value = endDate;
            SqlParameter paramShiftId = new SqlParameter("@ShiftId", SqlDbType.SmallInt);
            paramShiftId.Value = shiftId;
            SqlParameter[] parameters = new SqlParameter[3];
            parameters[0] = paramStartDate;
            parameters[1] = paramEndDate;
            parameters[2] = paramShiftId;
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sqlstr, parameters);
            if (dt != null && dt.Rows.Count > 0)
            {
                double dMonthWorkHours = (double)dt.Rows[0]["MonthWorkHours"];
                monthWorkHours = (int)dMonthWorkHours;
            }

            return monthWorkHours;
        }

        /// <summary>
        /// 取得某日期的工作日類型班別代碼
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns></returns>
        public int GetDayType(DateTime date, int shiftId = 1)
        {
            int iDayType = -1;
            string sqlstr = "SELECT DayType FROM dbo.Workday WHERE WorkDate=@WorkDate AND ShiftId=@ShiftId";
            SqlParameter paramWorkDate = new SqlParameter("@WorkDate", SqlDbType.Date);
            paramWorkDate.Value = date;
            SqlParameter paramShiftId = new SqlParameter("@ShiftId", SqlDbType.SmallInt);
            paramShiftId.Value = shiftId;
            SqlParameter[] parameters = new SqlParameter[2];
            parameters[0] = paramWorkDate;
            parameters[1] = paramShiftId;
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sqlstr, parameters);
            if (dt != null && dt.Rows.Count > 0)
            {
                byte dayType = (byte)dt.Rows[0]["DayType"];
                iDayType = (int)dayType;
            }
            return iDayType;
        }

        /// <summary>
        /// 取得員工該日的日期屬性代碼
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號/param>
        /// <returns>日期屬性</returns>
        public int GetDayType(DateTime date, string empNo)
        {
            int shiftId = 1; // 預設班別 1，正常班
            string sqlstr = @"SELECT EmpNo,ShiftId,WorkDate FROM dbo.EmpWorkShift WHERE WorkDate=@Date AND EmpNo=@EmpNo;";

            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterDate = new SqlParameter("@Date", SqlDbType.Date);
            parameterDate.Value = date;
            parameters.Add(parameterDate);

            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            parameterEmpNo.Value = empNo;
            parameters.Add(parameterEmpNo);

            DataTable dataTable = SqlHelper.GetDataTable(_connectionString, sqlstr, parameters);
            if (dataTable.Rows.Count > 0)
            {
                shiftId = (int)dataTable.Rows[0]["ShiftId"];
            }
            int iDayType = GetDayType(date, shiftId);
            return iDayType;
        }

        /// <summary>
        /// 取得某月份的日曆天資料
        /// </summary>
        /// <param name="year">西元年</param>
        /// <param name="month">月</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns>DataTable(正常工作卡資料)</returns>
        public DataTable GetWorkDaysInMonth(int year, int month, int shiftId = 1)
        {
            string sqlstr = @"SELECT ShiftId, WorkDate, DAY(WorkDate) AS DayNumber, DayType, CASE DayType WHEN 1 THEN 1 WHEN 7 THEN 1 ELSE 0 END AS IsWorkDay, WorkHours, [WeekDay], CASE [WeekDay] WHEN 0 THEN '日' WHEN 1 THEN '一' WHEN 2 THEN '二' WHEN 3 THEN '三' WHEN 4 THEN '四' WHEN 5 THEN '五' WHEN 6 THEN '六' END AS ChineseWeekDay
 FROM Workday.dbo.Workday WHERE ShiftId=@shiftId AND YEAR(WorkDate) = @Year AND MONTH(WorkDate) = @Month;";
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterYear = new SqlParameter("@Year", SqlDbType.Int);
            parameterYear.Value = year;
            parameters.Add(parameterYear);

            SqlParameter parameterMonth = new SqlParameter("@Month", SqlDbType.Int);
            parameterMonth.Value = month;
            parameters.Add(parameterMonth);

            SqlParameter parameterShiftId = new SqlParameter("@ShiftId", SqlDbType.Int);
            parameterShiftId.Value = shiftId;
            parameters.Add(parameterShiftId);

            DataTable dataTable = SqlHelper.GetDataTable(_connectionString, sqlstr, parameters);
            return dataTable;
        }

        /// <summary>取得某日期所屬旬的最後一天工作日</summary>
        /// <param name="year">西元年</param>
        /// <param name="month">月</param>
        /// <param name="day">日</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns>最後一天工作日之日期</returns>
        public DateTime GetLastWorkDayInTenDays(int year, int month, int day, int shiftId = 1)
        {
            DateTime workday = DateTime.Now;
            string sqlstr;

            switch (day)
            {
                case > 20: // 下旬
                    return LastWorkDayInMonth(year, month, shiftId);
                case < 11: // 上旬
                    sqlstr = $"SELECT TOP 1 WorkDate FROM dbo.Workday WHERE WorkDate>= CAST('{year}/{month}/01' AS DATETIME) AND WorkDate < CAST('{year}/{month}/11' AS DATETIME) AND DayType in (1, 7) AND ShiftId={shiftId} ORDER BY WorkDate DESC;";
                    break;
                default: // 中旬
                    sqlstr = $"SELECT TOP 1 WorkDate FROM dbo.Workday WHERE WorkDate>= CAST('{year}/{month}/11' AS DATETIME) AND WorkDate < CAST('{year}/{month}/21' AS DATETIME) AND DayType in (1, 7) AND ShiftId={shiftId} ORDER BY WorkDate DESC;";
                    break;
            }

            DataTable dataTable = SqlHelper.GetDataTable(_connectionString, sqlstr);
            if (dataTable.Rows.Count == 1)
            {
                workday = (DateTime)dataTable.Rows[0]["WorkDate"];
            }

            return workday;
        }

        /// <summary>查詢指定期間內之日曆天資料。</summary>
        /// <param name="startDate">起始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="empNo">員工編號/param>
        /// <returns>DataTable(日曆天資料)</returns>
        public DataTable GetWorkdaysDateRange(DateTime startDate, DateTime endDate, string empNo)
        {
            string sqlstr = @"SELECT EmpWorkday.EmpNo, EmpWorkday.ShiftId, EmpWorkday.WorkDate, EmpWorkday.AfternoonRestEnd, 
 EmpWorkday.AfternoonRestStart, EmpWorkday.MorningRestEnd, EmpWorkday.MorningRestStart, 
 EmpWorkday.MiddayBreakEnd, EmpWorkday.MiddayBreakStart, EmpWorkday.FlexibleDepartureAfter, 
 EmpWorkday.FlexibleDepartureBefore, EmpWorkday.FlexibleArrivalAfter, EmpWorkday.DepartureTime, 
 EmpWorkday.ArrivalTime, EmpWorkday.FlexibleArrivalBefore, DAY(EmpWorkday.WorkDate) AS DayNumber, 
 EmpWorkday.DayType, CASE DayType WHEN 1 THEN 1 WHEN 7 THEN 1 ELSE 0 END AS IsWorkDay, 
 EmpWorkday.WorkHours, EmpWorkday.WeekDay, 
 CASE [WeekDay] WHEN 0 THEN '日' WHEN 1 THEN '一' WHEN 2 THEN '二' WHEN 3 THEN '三' WHEN 4 THEN '四' WHEN
 5 THEN '五' WHEN 6 THEN '六' END AS ChineseWeekDay, WorkdayType.TypeName, WorkdayType.DayOff
FROM EmpWorkday INNER JOIN
 WorkdayType ON EmpWorkday.DayType = WorkdayType.TypeId
WHERE (EmpWorkday.EmpNo = @EmpNo) AND (EmpWorkday.WorkDate >= @StartDate) AND 
 (EmpWorkday.WorkDate <= @EndDate);";

            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.Date);
            parameterStartDate.Value = startDate.Date;
            parameters.Add(parameterStartDate);

            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.Date);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);

            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            parameterEmpNo.Value = empNo;
            parameters.Add(parameterEmpNo);

            DataTable dataTable = SqlHelper.GetDataTable(_connectionString, sqlstr, parameters);
            return dataTable;
        }

        /// <summary>查詢指定期間內之日曆天資料。</summary>
        /// <param name="startDate">起始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns>DataTable(日曆天資料)</returns>
        public DataTable GetWorkdaysDateRange(DateTime startDate, DateTime endDate, int shiftId = 1)
        {
            string sqlstr = @"SELECT Workday.ShiftId, Workday.WorkDate, Workday.AfternoonRestEnd, Workday.AfternoonRestStart, 
 Workday.MorningRestEnd, Workday.MorningRestStart, Workday.MiddayBreakEnd, Workday.MiddayBreakStart, 
 Workday.FlexibleDepartureAfter, Workday.FlexibleDepartureBefore, Workday.FlexibleArrivalAfter, 
 Workday.DepartureTime, Workday.ArrivalTime, Workday.FlexibleArrivalBefore, DAY(Workday.WorkDate) 
 AS DayNumber, Workday.DayType, CASE DayType WHEN 1 THEN 1 WHEN 7 THEN 1 ELSE 0 END AS IsWorkDay, 
 Workday.WorkHours, Workday.WeekDay, 
 CASE [WeekDay] WHEN 0 THEN '日' WHEN 1 THEN '一' WHEN 2 THEN '二' WHEN 3 THEN '三' WHEN 4 THEN '四' WHEN
 5 THEN '五' WHEN 6 THEN '六' END AS ChineseWeekDay, WorkdayType.DayOff, WorkdayType.TypeName
FROM Workday INNER JOIN
 WorkdayType ON Workday.DayType = WorkdayType.TypeId
WHERE (Workday.ShiftId = @ShiftId) AND (Workday.WorkDate >= @StartDate) AND (Workday.WorkDate <= @EndDate)";

            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.Date);
            parameterStartDate.Value = startDate.Date;
            parameters.Add(parameterStartDate);

            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.Date);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);

            SqlParameter parameterShiftId = new SqlParameter("@ShiftId", SqlDbType.Int);
            parameterShiftId.Value = shiftId;
            parameters.Add(parameterShiftId);

            DataTable dataTable = SqlHelper.GetDataTable(_connectionString, sqlstr, parameters);
            return dataTable;
        }

        /// <summary>查詢指定期間內之班別，此處只會傳回有設定特殊班別的資料。</summary>
        /// <param name="startDate">起始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="empNo">員工編號/param>
        /// <returns>DataTable(日曆天資料)</returns>
        public DataTable GetShiftsDateRange(DateTime startDate, DateTime endDate, string empNo)
        {
            string sqlstr = @"SELECT EmpNo,ShiftId,WorkDate FROM dbo.EmpWorkShift WHERE WorkDate >= @StartDate AND WorkDate <= @EndDate AND EmpNo=@EmpNo;";

            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.Date);
            parameterStartDate.Value = startDate.Date;
            parameters.Add(parameterStartDate);

            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.Date);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);

            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            parameterEmpNo.Value = empNo;
            parameters.Add(parameterEmpNo);

            DataTable dataTable = SqlHelper.GetDataTable(_connectionString, sqlstr, parameters);
            return dataTable;
        }

        /// <summary>取得某日期所屬旬的最後一天工作日之日期</summary>
        /// <param name="year">西元年</param>
        /// <param name="month">月</param>
        /// <param name="tenDays">旬別(1:上旬、2:中旬、3:下旬)</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns>DataTable(日曆天資料)</returns>
        public DataTable GetWorkdaysInTendays(int year, int month, int tenDays, int shiftId = 1)
        {
            string sqlstr = @"SELECT s.ShiftId, s.WorkDate, DAY(s.WorkDate) AS DayNumber, s.DayType, 
 CASE DayType WHEN 1 THEN 1 WHEN 7 THEN 1 ELSE 0 END AS IsWorkDay, s.WorkHours, s.WeekDay, 
 CASE [WeekDay] WHEN 0 THEN '日' WHEN 1 THEN '一' WHEN 2 THEN '二' WHEN 3 THEN '三' WHEN 4 THEN '四' WHEN
 5 THEN '五' WHEN 6 THEN '六' END AS ChineseWeekDay, WorkdayType.TypeName, WorkdayType.DayOff
FROM Workday AS s INNER JOIN
 WorkdayType ON s.DayType = WorkdayType.TypeId
WHERE (s.ShiftId = @shiftId) AND (YEAR(s.WorkDate) = @Year) AND (MONTH(s.WorkDate) = @Month) AND 
 (CASE (DAY(WorkDate) - 1) / 10 + 1 WHEN 1 THEN 1 WHEN 2 THEN 2 ELSE 3 END = @TenDays);";

            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterYear = new SqlParameter("@Year", SqlDbType.Int);
            parameterYear.Value = year;
            parameters.Add(parameterYear);

            SqlParameter parameterMonth = new SqlParameter("@Month", SqlDbType.Int);
            parameterMonth.Value = month;
            parameters.Add(parameterMonth);

            SqlParameter parameterTenDays = new SqlParameter("@TenDays", SqlDbType.Int);
            parameterTenDays.Value = tenDays;
            parameters.Add(parameterTenDays);

            SqlParameter parameterShiftId = new SqlParameter("@ShiftId", SqlDbType.Int);
            parameterShiftId.Value = shiftId;
            parameters.Add(parameterShiftId);

            DataTable dataTable = SqlHelper.GetDataTable(_connectionString, sqlstr, parameters);
            return dataTable;
        }

        /// <summary>
        /// 該月分最後一個工作日的日期
        /// </summary>
        /// <param name="year">The year. 西元年</param>
        /// <param name="month">The month. 月分</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns>最後一天工作日之日期</returns>
        public DateTime LastWorkDayInMonth(int year, int month, int shiftId = 1)
        {
            DateTime workday = DateTime.Now;
            string sqlstr;
            if (month == 12)
            {
                sqlstr = $"SELECT TOP 1 WorkDate FROM dbo.Workday WHERE WorkDate < CAST('{year + 1}/01/01' AS DATETIME) AND DayType in (1, 7) AND ShiftId=1 ORDER BY WorkDate DESC;";
            }
            else
            {
                sqlstr = $"SELECT TOP 1 WorkDate FROM dbo.Workday WHERE WorkDate < CAST('{year}/{month + 1}/01' AS DATETIME) AND DayType in (1, 7) AND ShiftId=1 ORDER BY WorkDate DESC;";
            }

            DataTable dataTable = SqlHelper.GetDataTable(_connectionString, sqlstr);
            if (dataTable.Rows.Count == 1)
            {
                workday = (DateTime)dataTable.Rows[0]["WorkDate"];
            }
            return workday;
        }
    }
}
