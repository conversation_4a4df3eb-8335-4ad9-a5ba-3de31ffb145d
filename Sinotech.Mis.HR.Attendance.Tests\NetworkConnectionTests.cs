﻿using System;
using System.ComponentModel;
using System.Net;
using Xunit;
using Sinotech.Mis.Helpers;
using System.Diagnostics.CodeAnalysis;

namespace Sinotech.Mis.Helpers.Tests
{
    [ExcludeFromCodeCoverage]
    public class NetworkConnectionTests : IDisposable
    {
        private readonly NetworkConnection _networkConnection;
        private readonly string _networkName = @"\\127.0.0.1\share";
        //private readonly NetworkCredential _credentials = new NetworkCredential("username", "password", "domain");

        public NetworkConnectionTests()
        {
           // _networkConnection = new NetworkConnection(_networkName, _credentials);
        }

        [Fact]
        public void NetworkConnection_Constructor_ShouldThrowWin32Exception_OnInvalidCredentials()
        {
            var invalidCredentials = new NetworkCredential("invalidUser", "invalidPassword", "invalidDomain");
            Assert.Throws<Win32Exception>(() => new NetworkConnection(_networkName, invalidCredentials));
        }

        public void Dispose()
        {
            if (_networkConnection != null)
            {
                _networkConnection.Dispose();
            }
        }
    }
}
