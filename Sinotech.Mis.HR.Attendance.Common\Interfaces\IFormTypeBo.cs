﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Sinotech.Mis.HR.Attendance.Common
{
    public interface IFormTypeBo
    {
        /// <summary>
        /// 由表單ID取得表單中文名
        /// </summary>
        /// <param name="formId"></param>
        /// <returns></returns>
        public string? GetFormName(string formId);

        /// <summary>
        /// 取得所有 FormTypes
        /// </summary>
        /// <returns></returns>
        public List<FormType> GetFormTypes();


    }
}
