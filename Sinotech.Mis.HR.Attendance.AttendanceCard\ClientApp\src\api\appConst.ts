﻿import type { MonthsOptionsType, TenDaysOptionsType, CardOptionsType } from './appType'

/**
 * 卡別ID名稱
 * @property { string } A1Card 正常工作卡
 * @property { string } B1CardApp 加班申請卡
 * @property { string } B1Card 加班卡
 * @property { string } C1Card 請假卡
 */
export enum FORM_ID {
  A1Card = '正常工作卡',
  B1CardApp = '加班申請卡',
  B1Card = '加班卡',
  C1Card = '請假卡'
}

/**
 * 日期類型
 * @property { number } WeekWorkday 1 週間工作日
 * @property { number } SaturdayRestday 2 週六休息日
 * @property { number } SundayRegularHoliday 3 週日例假日
 * @property { number } WeekHoliday 4 週間國定假日
 * @property { number } SaturdayHoliday 5 週六國定假日
 * @property { number } SundayHoliday 6 週日國定假日
 * @property { number } MakeUpWorkday 7 補班日
 * @property { number } MakeUpHoliday 8 補假日
 * @property { number } FlexbleHoliday 9 彈性放假日
 * @property { number } WeekNaturalDisasterDay 10 週間天災日
 * @property { number } SaturdayNaturalDisasterDay 11 週六天災日
 * @property { number } WeekRestday 20 週間休息日
 */
export enum WORKDAY_CODE {
  WeekWorkday = 1,
  SaturdayRestday = 2,
  SundayRegularHoliday = 3,
  WeekHoliday = 4,
  SaturdayHoliday = 5,
  SundayHoliday = 6,
  MakeUpWorkday = 7,
  MakeUpHoliday = 8,
  FlexbleHoliday = 9,
  WeekNaturalDisasterDay = 10,
  SaturdayNaturalDisasterDay = 11,
  WeekRestday = 20
}

/**
 * 關卡狀態
 * @property { number } NotSend 0 未傳送
 * @property { number } Processing 1 進行中
 * @property { number } Agree 2 同意
 * @property { number } Deny 3 不同意
 * @property { number } Withdraw 4 已抽單
 * @property { number } Notified 5 已通知
 */
export enum FLOW_STATUS_CODE {
  NotSend = 0,
  Processing = 1,
  Agree = 2,
  Deny = 3,
  Withdraw = 4,
  Notified = 5
}

export const SESSION_STORAGE_SECRET = 'Sinotech Attendance 2023',
  NIL_UUID = '00000000-0000-0000-0000-000000000000',
  IDLE_TIMEOUT_TIME = 20 * (60 * 1000),
  WARN_DISPLAY_TIME = 5000,
  INFO_DISPLAY_TIME = 10000,
  SUCCESS_DISPLAY_TIME = 10000,
  FETCH_TIMEOUT_TIME = 30000,
  SHAKE_RESIST_TIME = 3000,
  REFRESH_PAGE_TOAST_TIME = 2000,
  REFRESH_PAGE_TIME = 3000,
  SYSTEM_ERROR_MESSAGE = '系統發生例外狀況，請與系統管理者聯絡！',
  SYSTEM_UPDATE_MESSAGE = '請重新載入頁面',
  DATERANGE_WARN_MESSAGE = '【截止日期】須大於【起始日期】',
  ROC_YEARS_OPTIONS = Array.from(Array((Math.ceil((new Date().getFullYear() - 1911) / 100)) * 100).keys(), (e) => e + 1),
  MONTHS_OPTIONS: Array<MonthsOptionsType> = [
    {
      field: null,
      optionValue: 0
    },
    {
      field: '1',
      optionValue: 1
    },
    {
      field: '2',
      optionValue: 2
    },
    {
      field: '3',
      optionValue: 3
    },
    {
      field: '4',
      optionValue: 4
    },
    {
      field: '5',
      optionValue: 5
    },
    {
      field: '6',
      optionValue: 6
    },
    {
      field: '7',
      optionValue: 7
    },
    {
      field: '8',
      optionValue: 8
    },
    {
      field: '9',
      optionValue: 9
    },
    {
      field: '10',
      optionValue: 10
    },
    {
      field: '11',
      optionValue: 11
    },
    {
      field: '12',
      optionValue: 12
    }
  ],
  TEN_DAYS_OPTIONS: Array<TenDaysOptionsType> = [
    {
      field: null,
      optionValue: '-1'
    },
    {
      field: '上',
      optionValue: '0'
    },
    {
      field: '中',
      optionValue: '1'
    },
    {
      field: '下',
      optionValue: '2'
    }
  ],
  CARDS_OPTIONS: Array<CardOptionsType> = [
    {
      label: '全部',
      value: null
    },
    {
      label: FORM_ID.A1Card,
      value: 'A1Card'
    },
    {
      label: FORM_ID.B1CardApp,
      value: 'B1CardApp'
    },
    {
      label: FORM_ID.B1Card,
      value: 'B1Card'
    },
    {
      label: FORM_ID.C1Card,
      value: 'C1Card'
    }
  ],
  UPLOADED_FILE_MIME = [
    'text/plain',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'image/*'
  ],
  UPLOADED_FILESIZE_MAX = 10000000,
  PLCAE_TEXTAREA_MAX = 50,
  REASON_TEXTAREA_MAX = 100,
  SIGNERS_MAX = 10,
  SMALL_DATE_TIME = {
    floor: new Date(1900, 1, 1),
    ceil: new Date(2079, 6, 6, 12)
  }