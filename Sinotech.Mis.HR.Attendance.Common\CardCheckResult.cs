﻿namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 請假卡檢查結果
    /// </summary>
    public class CardCheckResult
    {

        /// <summary>
        /// 建構元
        /// </summary>
        /// <param name="code"></param>
        /// <param name="status"></param>
        /// <param name="message"></param>
        public CardCheckResult(int code, CardStatusEnum status, string message)
        {
            Code = code;
            Status = status;
            Message = message;
        }

        public override bool Equals(object? obj)
        {
            if (obj is CardCheckResult result && 
                result.Code == Code && 
                result.Message == Message && 
                result.Status == Status)
            {
                return true;
            }
            return false;
        }

        public override int GetHashCode()
        {
            return Code;
        }

        /// <summary>
        /// 代碼
        /// </summary>
        public int Code { get; }

        /// <summary>
        /// 是否資料全部正確
        /// </summary>
        //public bool Ok { get { return Status == CardStatusEnum.Success; } }

        /// <summary>
        /// 錯誤訊息
        /// </summary>
        public string Message { get; } = string.Empty;

        /// <summary>
        /// 狀態
        /// </summary>
        public CardStatusEnum Status { get; }

    }
}
