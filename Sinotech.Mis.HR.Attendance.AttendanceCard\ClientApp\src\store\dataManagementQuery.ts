import { defineStore } from 'pinia'
import { MONTHS_OPTIONS, TEN_DAYS_OPTIONS } from '../api/appConst'
import type { MonthsOptionsType } from '../api/appType'

const now = new Date()
const startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 30)
const endTime = new Date(now.getFullYear(), now.getMonth(), now.getDate())

const defaultParameter = {
  formID: null as (string | null),
  contentStartTime: startTime,
  contentEndTime: endTime,
  startTime: startTime,
  endTime: endTime,
  overtimeCompensatoryKind: {
    id: 0,
    name: '全部'
  },
  leaveKind: 0,
  queryTimeType: '1',
  employee: {
    userId: '',
    userName: '',
    deptNo: 0,
    deptSName: ''
  },
  dept: {
    deptNo: 0,
    deptSName: ''
  },
  projectNumber: '',
  formStatus: ['0', '1', '2', '3', '4'],
  formNo: '',
  years: endTime.getFullYear() - 1911, // 若指定查詢正常工作卡，查詢旬別的民國年
  months: MONTHS_OPTIONS.find((e: MonthsOptionsType) => e.optionValue === (endTime.getMonth() + 1)) ?? MONTHS_OPTIONS[0], // 若指定查詢正常工作卡，查詢旬別的月
  tenDays: TEN_DAYS_OPTIONS[0] // 若指定查詢正常工作卡，查詢旬別的旬
}

export const useDataManagementQueryStore = defineStore('dataManagementQuery', {
  state: () => ({
    queryOnce: false, // 紀錄是否為第二次以上的查詢
    queryPage: 0,
    queryTotalRecords: 0,
    sortField: 'createdTime',
    sortOrder: -1,
    ...defaultParameter
  }),
  actions: {
    setParameter(newParameter: any) {
      this.formID = newParameter.formID
      this.contentStartTime = newParameter.contentStartTime
      this.contentEndTime = newParameter.contentEndTime
      this.startTime = newParameter.startTime
      this.endTime = newParameter.endTime
      this.overtimeCompensatoryKind = newParameter.overtimeCompensatoryKind
      this.leaveKind = newParameter.leaveKind
      this.queryTimeType = newParameter.queryTimeType
      this.employee = newParameter.employee
      this.dept = newParameter.dept
      this.projectNumber = newParameter.projectNumber
      this.formStatus = newParameter.formStatus
      this.formNo = newParameter.formNo
      this.years = newParameter.years
      this.months = newParameter.months
      this.tenDays = newParameter.tenDays
    }
  }
})