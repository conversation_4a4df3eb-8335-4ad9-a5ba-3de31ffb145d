﻿namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 工作日類型
    /// </summary>
    public enum WorkdayType
    {
        /// <summary>
        /// 工作日
        /// </summary>
        WeekWorkday = 1,
        /// <summary>
        /// 週六休息日
        /// </summary>
        SaturdayRestday = 2,
        /// <summary>
        /// 例假日
        /// </summary>
        SundayRegularHoliday = 3,
        /// <summary>
        /// 週間國定假日
        /// </summary>
        WeekHoliday = 4,
        /// <summary>
        /// 週六國定假日
        /// </summary>
        SaturdayHoliday = 5,
        /// <summary>
        /// 週日國定假日
        /// </summary>
        SundayHoliday = 6,
        /// <summary>
        /// 補班日
        /// </summary>
        MakeUpWorkday = 7,
        /// <summary>
        /// 補假日
        /// </summary>
        MakeUpHoliday = 8,
        /// <summary>
        /// 彈性放假日
        /// </summary>
        FlexbleHoliday = 9,
        /// <summary>
        /// 週間天災日
        /// </summary>
        WeekNaturalDisasterDay = 10,
        /// <summary>
        /// 週六天災日
        /// </summary>
        SaturdayNaturalDisasterDay = 11,
        /// <summary>
        /// 週間休息日
        /// </summary>
        WeekRestday = 20
    }
}
