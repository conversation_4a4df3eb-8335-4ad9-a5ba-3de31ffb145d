# **三卡電子表單 [API 說明文件](./api/index.html)**

<br />

## [API列表](./api/index.html)
* [Sinotech.Mis.HR.Attendance.BusinessLogic](./api/Sinotech.Mis.HR.Attendance.BusinessLogic.html)
* [Sinotech.Mis.HR.Attendance.Common](./api/Sinotech.Mis.HR.Attendance.Common.html)
* [Sinotech.Mis.Extensions.Configuration](./api/Sinotech.Mis.Extensions.Configuration.html)
* [Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers](./api/Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers.html)
* [Sinotech.Mis.HR.Attendance.DataAccess.Interfaces](./api/Sinotech.Mis.HR.Attendance.DataAccess.Interfaces.html)
* [Sinotech.Mis.Utilities.DataAccess.Interfaces](./api/Sinotech.Mis.Utilities.DataAccess.Interfaces.html)
* [Sinotech.Mis.Serilog.Utilities.Middlewares](./api/Sinotech.Mis.Serilog.Utilities.Middlewares.html)
* [Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.PositionType](./api/Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.PositionType.html)

### 本文件採用 [Docfx](https://dotnet.github.io/docfx/) 製作 .
## Docfx 安裝與更新:
1. 安裝 Docfx：
   > `dotnet tool update -g docfx` 
2. 更新說明檔：
    <br />
   在目前目錄執行 
   > `docfx docfx.json`
3. 在本地建立 port 8080的網站查看API說明文件：
   > `docfx docfx.json --serve`

***
詳細使用方式請參考 [Docfx說明文件](https://dotnet.github.io/docfx/)



