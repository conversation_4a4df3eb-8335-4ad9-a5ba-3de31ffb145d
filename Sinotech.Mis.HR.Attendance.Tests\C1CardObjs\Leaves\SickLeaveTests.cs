﻿using FakeItEasy;
using Xunit;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves;
using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.Tests;
using System.Diagnostics.CodeAnalysis;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves.Tests
{
    [ExcludeFromCodeCoverage]
    public class SickLeaveTests : TestC1CardBase
    {
        public SickLeaveTests()
        {
            #region Prepare Data

            _c1Card.LeaveNumber = LeaveKindEnum.SickLeave;

            #endregion
        }

        [Theory]
        [InlineData(false, SickLeave.CodeLeaveRecordNotCreateYet)]
        [InlineData(true, SickLeave.CodeOk)]
        public void TestCanTakeThisLeave(bool ifLeaveRecordExists, int returnCode)
        {
            A.CallTo(() => _c1CardBo.IfLeaveRecordExists(A<string>.Ignored,
                A<DateTime>.Ignored, A<DateTime>.Ignored)).Returns(ifLeaveRecordExists);

            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CanTakeThisLeave();

            Assert.Equal(returnCode, result.Code);
        }

        [Theory]
        [InlineData(240, 0, 0, 8, false, SickLeave.CodeOk)]
        [InlineData(240, 10, 0, 8, false, SickLeave.CodeOk)]
        [InlineData(240, 232, 0, 8, false, SickLeave.CodeOk)]
        [InlineData(240, 233, 0, 8, false, SickLeave.CodeExceedQuota)]
        [InlineData(240, 240, 0, 1, false, SickLeave.CodeExceedQuota)]

        [InlineData(240, 0, 0, 8, true, SickLeave.CodeOk)]
        [InlineData(240, 10, 0, 8, true, SickLeave.CodeOk)]
        [InlineData(240, 232, 0, 8, true, SickLeave.CodeOk)]
        [InlineData(240, 233, 0, 8, true, SickLeave.CodeOk)]
        [InlineData(240, 240, 0, 1, true, SickLeave.CodeOk)]

        [InlineData(240, 241, 0, 8, true, SickLeave.CodeOk)]
        [InlineData(240, 232, 3, 8, false, SickLeave.CodeOk)]
        [InlineData(240, 233, 3, 8, false, SickLeave.CodeExceedQuota)]
        [InlineData(240, 230, 6, 8, false, SickLeave.CodeOk)]

        [InlineData(240, 232, 3, 8, true, SickLeave.CodeOk)]
        [InlineData(240, 233, 3, 8, true, SickLeave.CodeOk)]
        [InlineData(240, 230, 6, 8, true, SickLeave.CodeOk)]
        public void TestExceedQuota(int available, int used, int menstrualLeaveUsed, int totalHours, bool confirmed, int returnCode)
        {
            A.CallTo(() => _c1CardBo.GetSickLeaveAvailableHours(A<string>.Ignored, A<DateTime>.Ignored)).Returns(available);
            A.CallTo(() => _c1CardBo.GetSickLeaveUsedHours(A<string>.Ignored, A<DateTime>.Ignored)).Returns(used);
            A.CallTo(() => _c1CardBo.GetMenstrualLeaveYearUsedHours(A<string>.Ignored, A<DateTime>.Ignored)).Returns(menstrualLeaveUsed);
            A.CallTo(() => _c1CardBo.CalculateEmpTakeLeaveWorkingHours(A<string>.Ignored, A<DateTime>.Ignored, A<DateTime>.Ignored)).Returns(totalHours);

            _c1Card.Confirmed = confirmed;

            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CheckOverPermittedLeaveHours();

            Assert.Equal(returnCode, result.Code);
        }

        [Fact]
        public void TestCheckRequiredFields()
        {
            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CheckRequiredFields();

            Assert.Equal(SickLeave.CodeOk, result.Code);
        }

        [Theory]
        [InlineData(Gender.Female, true)]
        [InlineData(Gender.Male, true)]
        public void TestGender(Gender gender, bool expected)
        {
            var result = SickLeave.IsAllowForThisGender(gender);
            Assert.Equal(expected, result);
        }
    }
}