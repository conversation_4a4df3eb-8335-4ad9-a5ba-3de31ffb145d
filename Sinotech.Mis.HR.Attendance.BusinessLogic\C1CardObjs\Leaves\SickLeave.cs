﻿﻿using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using System;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves
{
    /// <summary>
    /// 病假類別
    /// 病假為員工因疾病需要治療或休養時申請的假別
    /// 依勞基法規定，病假一年內未超過30日部分，工資折半發給
    /// </summary>
    /// <seealso cref="Leaves.LeaveBase" />
    [LeaveKind(LeaveKindEnum.SickLeave)]
    public class SickLeave : C1CardBase
    {
        /// <summary>
        /// 超出病假額度的錯誤代碼
        /// </summary>
        public const int CodeExceedQuota = 3010901;
        
        /// <summary>
        /// 超出額度時的卡片狀態（需要確認）
        /// </summary>
        private readonly CardStatusEnum _statusExceedQuota = CardStatusEnum.Conformation;
        
        /// <summary>
        /// 超假提醒訊息模板1（僅計算病假時數）
        /// 格式：本次請假時數、年度已請時數、總計時數、年度上限時數
        /// </summary>
        private readonly string _messagePatternExceedQuota1 = "您本次請假 {0} 小時，年度累計已請 {1} 小時，總計 {2} 小時，已超過年度上限（{3} 小時），是否確定超假？";
        
        /// <summary>
        /// 超假提醒訊息模板2（包含生理假計算）
        /// 格式：本次請假時數、年度已請時數、病假時數、生理假時數、總計時數、年度上限時數
        /// 注意：生理假前3日不併入病假累計
        /// </summary>
        private readonly string _messagePatternExceedQuota2 = "您本次請假 {0} 小時，年度累計已請 {1} 小時（病假 {2} 小時、生理假 {3} 小時，生理假前3日不併入累計），總計 {4} 小時，已超過年度上限（{5} 小時），是否確定超假？";

        /// <summary>
        /// 病假類別建構子
        /// 初始化病假申請卡片的相關資料和業務邏輯處理物件
        /// </summary>
        /// <param name="c1Card">C1卡片資料物件，包含請假的基本資訊</param>
        /// <param name="c1CardBo">C1卡片業務邏輯物件，用於執行資料庫操作和業務規則驗證</param>
        public SickLeave(C1Card c1Card, IC1CardBo c1CardBo) : base(c1Card, c1CardBo)
        {
        }

        /// <summary>
        /// 檢查員工是否能申請病假
        /// 驗證員工的病假資格和相關限制條件
        /// </summary>
        /// <returns>卡片檢查結果，包含是否通過驗證和相關訊息</returns>
        internal override CardCheckResult CanTakeThisLeave()
        {
            // 檢查是否已經建立該員工的休假基本資料
            // 如果尚未建立，表示系統中沒有該員工的假別設定
            if (!IfLeaveRecordExists())
            {
                return ResultLeaveRecordNotCreatedYet;
            }

            return ResultOk;
        }

        /// <summary>
        /// 檢查是否超過允許的病假時數
        /// 根據勞基法規定和公司政策，驗證申請的病假時數是否超過年度上限
        /// </summary>
        /// <returns>檢查結果，如果超假則回傳確認狀態和提醒訊息</returns>
        internal override CardCheckResult CheckOverPermittedLeaveHours()
        {
            return IfExceedQuota();
        }

        /// <summary>
        /// 檢查是否超出病假額度的私有方法
        /// 計算包含生理假在內的病假使用時數，並與年度上限比較
        /// </summary>
        /// <returns>超假檢查結果</returns>
        private CardCheckResult IfExceedQuota()
        {
            // 生理假門檻：3天（24小時），超過此門檻的部分併入病假計算
            const int menstrualLeaveHoursThreadHold = 3 * 8;

            // 取得該員工本年度病假可用總時數
            var available = GetSickLeaveYearAvailableHours();
            
            // 取得該員工本年度已使用的病假時數
            var used = GetSickLeaveYearUsedHours();
            
            // 取得該員工本年度已使用的生理假時數
            var menstrualLeaveHoursUsed = GetMenstrualLeaveYearUsedHours();

            // 計算合併使用時數：病假 + (生理假超過3天的部分)
            // 生理假前3天不併入病假累計，超過3天的部分才計入
            var aggregateUsed = used + Math.Max(menstrualLeaveHoursUsed - menstrualLeaveHoursThreadHold, 0);
            
            // 檢查此申請是否已經被確認（允許超假）
            var confirmed = _c1Card.Confirmed;

            // 如果本次申請加上已使用時數超過可用額度，且尚未確認，則需要提醒
            if (TotalHours + aggregateUsed > available && !confirmed)
            {
                return GetExceedQuotaCheckResult();
            }

            return _resultOk;

            /// <summary>
            /// 取得超假檢查結果的內部方法
            /// 根據是否有使用生理假來決定使用哪種訊息模板
            /// </summary>
            /// <returns>包含超假訊息的檢查結果</returns>
            CardCheckResult GetExceedQuotaCheckResult()
            {
                CardCheckResult result;
                
                // 如果生理假使用時數超過3天門檻，使用包含生理假說明的訊息模板
                if (menstrualLeaveHoursUsed > menstrualLeaveHoursThreadHold)
                {
                    result = new CardCheckResult(CodeExceedQuota, _statusExceedQuota,
                        string.Format(_messagePatternExceedQuota2,
                                      TotalHours,           // {0} 本次請假時數
                                      aggregateUsed,        // {1} 年度累計已請時數
                                      used,                 // {2} 病假時數
                                      menstrualLeaveHoursUsed, // {3} 生理假時數
                                      TotalHours + aggregateUsed, // {4} 總計時數
                                      available));          // {5} 年度上限時數
                }
                else
                {
                    // 如果沒有使用生理假或未超過門檻，使用簡化的訊息模板
                    result = new CardCheckResult(CodeExceedQuota, _statusExceedQuota,
                        string.Format(_messagePatternExceedQuota1,
                                      TotalHours,              // {0} 本次請假時數
                                      aggregateUsed,           // {1} 年度累計已請時數
                                      TotalHours + aggregateUsed, // {2} 總計時數
                                      available));             // {3} 年度上限時數
                }

                return result;
            }
        }

        /// <summary>
        /// 取得該員工本年度已使用的病假時數
        /// </summary>
        /// <returns>已使用的病假時數（小時）</returns>
        private int GetSickLeaveYearUsedHours()
        {
            return _c1CardBo.GetSickLeaveUsedHours(_c1Card.EmpNo, _c1Card.StartDate);
        }

        /// <summary>
        /// 取得該員工本年度可使用的病假總時數
        /// 根據勞基法規定和公司政策計算年度病假額度
        /// </summary>
        /// <returns>可使用的病假總時數（小時）</returns>
        private int GetSickLeaveYearAvailableHours()
        {
            return _c1CardBo.GetSickLeaveAvailableHours(_c1Card.EmpNo, _c1Card.StartDate);
        }

        /// <summary>
        /// 取得該員工本年度已使用的生理假時數
        /// 生理假前3天不併入病假計算，超過3天的部分才併入病假額度
        /// </summary>
        /// <returns>已使用的生理假時數（小時）</returns>
        private int GetMenstrualLeaveYearUsedHours()
        {
            return _c1CardBo.GetMenstrualLeaveYearUsedHours(_c1Card.EmpNo, _c1Card.StartDate);
        }

        /// <summary>
        /// 檢查病假申請的必要欄位是否完整填寫
        /// 驗證申請表單中的必填欄位，確保資料完整性
        /// </summary>
        /// <returns>欄位檢查結果，如有缺少必填欄位則回傳錯誤訊息</returns>
        internal override CardCheckResult CheckRequiredFields()
        {
            // 先執行基底類別的必要欄位檢查（如請假日期、時數等基本欄位）
            var result = base.CheckRequiredFields();
            if (result != ResultOk)
            {
                return result;
            }

            // 病假目前沒有額外的必填欄位要求
            // 如未來需要增加病假特有的必填欄位（如診斷書等），可在此處擴充
            return ResultOk;
        }

        /// <summary>
        /// 計算病假的最早可申請日期與最晚可申請日期
        /// 病假通常允許事後申請（因突發疾病），所以日期限制較為寬鬆
        /// </summary>
        /// <param name="date">基準請假日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>回傳包含最早日期和最晚日期的元組</returns>
        public override (DateTime, DateTime) CalculateLeavePermittedPeriod(DateTime date, string empNo) => DefaultCalculateLeavePermittedPeriod(date, empNo);

        /// <summary>
        /// 檢查指定性別是否可以申請病假
        /// 病假為所有性別都可申請的假別，無性別限制
        /// </summary>
        /// <param name="gender">員工性別</param>
        /// <returns>永遠回傳 true，因為病假對所有性別開放</returns>
        public static bool IsAllowForThisGender(Gender gender)
        {
            return true;
        }

    }

}