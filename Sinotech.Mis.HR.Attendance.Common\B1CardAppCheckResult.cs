﻿using Sinotech.Mis.Common;
using System;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 加班申請卡檢查結果
    /// </summary>
    public class B1CardAppCheckResult
    {
        /// <summary>
        /// 員工基本資料
        /// </summary>
        public EmployeeSimple? Employee { get; set; }

        /// <summary>
        /// 本日是否能報加班
        /// </summary>
        public bool IsOvertimeAllowed { get; set; } = false;

        /// <summary>
        /// 是否填過加班申請卡
        /// </summary>
        public bool IsFilled { get; set; } = false;

        /// <summary>
        /// 若已填報，該表單單號及名稱
        /// </summary>
        public FormFilled? FilledForm { get; set; }

        /// <summary>
        /// 是否逾期填報
        /// </summary>
        public bool IsOverdue { get; set; } = false;

        /// <summary>
        /// 是否資料全部正確
        /// </summary>
        public bool IsValid { get; set; } = false;

        /// <summary>
        /// 工作日詳細資料，包括日期類型
        /// </summary>
        public Workday? DayDetail { get; set; }

        /// <summary>
        /// 加班申請卡的最少當日可加班時數
        /// </summary>
        public int HoursLowerBound { get; set; } = 1;

        /// <summary>
        /// 最少當日加班支薪時數
        /// </summary>
        public int MinPaidOvertimeHours { get; set; } = 1;

        /// <summary>
        /// 最大當日可加班時數
        /// </summary>
        public int HoursUpperBound { get; set; } = 12;

        /// <summary>
        /// 日期警告訊息，無訊息時為 ""
        /// </summary>
        public string DateAlarmMessage { get; set; } = "";

        /// <summary>
        /// 日期錯誤訊息，無訊息時為 ""
        /// </summary>
        public string DateErrorMessage { get; set; } = "";

        /// <summary>
        /// 時數警告訊息，無訊息時為 ""
        /// </summary>
        public string HoursAlarmMessage { get; set; } = "";

        /// <summary>
        /// 時數錯誤訊息，無訊息時為 ""
        /// </summary>
        public string HoursErrorMessage { get; set; } = "";

        /// <summary>
        /// 申請人錯誤訊息，無訊息時為 ""
        /// </summary>
        public string UserErrorMessage { get; set; } = "";

        /// <summary>
        /// 錯誤訊息，無訊息時為 ""
        /// </summary>
        public string ErrorMessage { get; set; } = "";

        /// <summary>
        /// AddB1CardApp 時傳回 FormUID
        /// </summary>
        public Guid? FormUID { get; set; } = null;
    }
}
