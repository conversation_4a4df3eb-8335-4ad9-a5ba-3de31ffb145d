﻿using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Collections.Generic;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.Tests
{
    public class FormViewTests
    {
        [Fact]
        public void FormView_DefaultValues_ShouldBeSetCorrectly()
        {
            // Arrange & Act
            var formView = new FormView();

            // Assert
            Assert.Null(formView.ID);
            Assert.Equal(Guid.Empty, formView.FormUID);
            Assert.Equal(string.Empty, formView.FormID);
            Assert.Equal(string.Empty, formView.FormNo);
            Assert.Equal(string.Empty, formView.FormSubject);
            Assert.Equal(string.Empty, formView.ApplicationType);
            Assert.Equal(string.Empty, formView.FormInfo);
            Assert.Equal(string.Empty, formView.EmpNo);
            Assert.Equal(string.Empty, formView.EmpName);
            Assert.Equal(0, formView.DeptNo);
            Assert.Equal(string.Empty, formView.DeptSName);
            Assert.Null(formView.TeamID);
            Assert.Null(formView.TeamCName);
            Assert.Equal(string.Empty, formView.RankNo);
            Assert.Equal(string.Empty, formView.RankName);
            Assert.Null(formView.JobNo);
            Assert.Null(formView.JobName);
            Assert.Null(formView.ContentStartTime);
            Assert.Null(formView.ContentEndTime);
            Assert.Equal(string.Empty, formView.CreatedEmpNo);
            Assert.Equal(string.Empty, formView.CreatedName);
            Assert.Equal(default(DateTime), formView.FilledTime);
            Assert.Equal(default(DateTime), formView.CreatedTime);
            Assert.Equal(string.Empty, formView.CreatedIP);
            Assert.Null(formView.CreatedHost);
            Assert.Null(formView.AddedSigner);
            Assert.Equal(default(DateTime), formView.StartTime);
            Assert.Null(formView.EndTime);
            Assert.Equal(0, formView.TotalHours);
            Assert.Equal(0, formView.FormStatus);
            Assert.Equal(string.Empty, formView.FormStatusName);
            Assert.Equal(0, formView.TotalSteps);
            Assert.Equal(0, formView.CurrentStep);
            Assert.Null(formView.UpdatedEmpNo);
            Assert.Null(formView.UpdatedName);
            Assert.Null(formView.UpdatedTime);
            Assert.Null(formView.UpdatedIP);
            Assert.Null(formView.UpdatedHost);
            Assert.NotNull(formView.Flows);
            Assert.Empty(formView.Flows);
        }

        [Fact]
        public void FormView_SetProperties_ShouldBeSetCorrectly()
        {
            // Arrange
            var formView = new FormView();
            var now = DateTime.Now;
            var guid = Guid.NewGuid();
            var flows = new List<FormFlow> { new FormFlow() };

            // Act
            formView.ID = 1;
            formView.FormUID = guid;
            formView.FormID = "FormID";
            formView.FormNo = "FormNo";
            formView.FormSubject = "FormSubject";
            formView.ApplicationType = "ApplicationType";
            formView.FormInfo = "FormInfo";
            formView.EmpNo = "EmpNo";
            formView.EmpName = "EmpName";
            formView.DeptNo = 2;
            formView.DeptSName = "DeptSName";
            formView.TeamID = 3;
            formView.TeamCName = "TeamCName";
            formView.RankNo = "RankNo";
            formView.RankName = "RankName";
            formView.JobNo = "JobNo";
            formView.JobName = "JobName";
            formView.ContentStartTime = now;
            formView.ContentEndTime = now;
            formView.CreatedEmpNo = "CreatedEmpNo";
            formView.CreatedName = "CreatedName";
            formView.FilledTime = now;
            formView.CreatedTime = now;
            formView.CreatedIP = "CreatedIP";
            formView.CreatedHost = "CreatedHost";
            formView.AddedSigner = "AddedSigner";
            formView.StartTime = now;
            formView.EndTime = now;
            formView.TotalHours = 4;
            formView.FormStatus = 1;
            formView.FormStatusName = "FormStatusName";
            formView.TotalSteps = 5;
            formView.CurrentStep = 6;
            formView.UpdatedEmpNo = "UpdatedEmpNo";
            formView.UpdatedName = "UpdatedName";
            formView.UpdatedTime = now;
            formView.UpdatedIP = "UpdatedIP";
            formView.UpdatedHost = "UpdatedHost";
            formView.Flows = flows;

            // Assert
            Assert.Equal(1, formView.ID);
            Assert.Equal(guid, formView.FormUID);
            Assert.Equal("FormID", formView.FormID);
            Assert.Equal("FormNo", formView.FormNo);
            Assert.Equal("FormSubject", formView.FormSubject);
            Assert.Equal("ApplicationType", formView.ApplicationType);
            Assert.Equal("FormInfo", formView.FormInfo);
            Assert.Equal("EmpNo", formView.EmpNo);
            Assert.Equal("EmpName", formView.EmpName);
            Assert.Equal(2, formView.DeptNo);
            Assert.Equal("DeptSName", formView.DeptSName);
            Assert.Equal(3, formView.TeamID);
            Assert.Equal("TeamCName", formView.TeamCName);
            Assert.Equal("RankNo", formView.RankNo);
            Assert.Equal("RankName", formView.RankName);
            Assert.Equal("JobNo", formView.JobNo);
            Assert.Equal("JobName", formView.JobName);
            Assert.Equal(now, formView.ContentStartTime);
            Assert.Equal(now, formView.ContentEndTime);
            Assert.Equal("CreatedEmpNo", formView.CreatedEmpNo);
            Assert.Equal("CreatedName", formView.CreatedName);
            Assert.Equal(now, formView.FilledTime);
            Assert.Equal(now, formView.CreatedTime);
            Assert.Equal("CreatedIP", formView.CreatedIP);
            Assert.Equal("CreatedHost", formView.CreatedHost);
            Assert.Equal("AddedSigner", formView.AddedSigner);
            Assert.Equal(now, formView.StartTime);
            Assert.Equal(now, formView.EndTime);
            Assert.Equal(4, formView.TotalHours);
            Assert.Equal(1, formView.FormStatus);
            Assert.Equal("FormStatusName", formView.FormStatusName);
            Assert.Equal(5, formView.TotalSteps);
            Assert.Equal(6, formView.CurrentStep);
            Assert.Equal("UpdatedEmpNo", formView.UpdatedEmpNo);
            Assert.Equal("UpdatedName", formView.UpdatedName);
            Assert.Equal(now, formView.UpdatedTime);
            Assert.Equal("UpdatedIP", formView.UpdatedIP);
            Assert.Equal("UpdatedHost", formView.UpdatedHost);
            Assert.Equal(flows, formView.Flows);
        }
    }
}
