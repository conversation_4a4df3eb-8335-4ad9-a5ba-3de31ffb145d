﻿using Sinotech.Mis.Common;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 加班卡檢查結果
    /// </summary>
    public class B1CardCheckResult
    {
        /// <summary>
        /// 員工基本資料
        /// </summary>
        public EmployeeSimple? Employee { get; set; }

        /// <summary>
        /// 本日是否能報加班
        /// </summary>
        public bool CanOvertime { get; set; } = false;

        /// <summary>
        /// 是否填過加班申請卡
        /// </summary>
        public bool IsFilled { get; set; } = false;

        /// <summary>
        /// 若已填報，該表單單號及名稱
        /// </summary>
        public FormFilled? FilledForm { get; set; }

        /// <summary>
        /// 是否資料全部正確
        /// </summary>
        public bool IsValid { get; set; } = false;

        /// <summary>
        /// 此次加班總時數
        /// </summary>
        public int TotalHours { get; set; }

        /// <summary>
        /// 此次加班需累計加班總時數
        /// </summary>
        public int InOvertimeHours { get; set; }

        /// <summary>
        /// 工作日詳細資料，包括日期類型
        /// </summary>
        public Workday DayDetail { get; set; } = new Workday();

        /// <summary>
        /// 加班申請卡的最少當日可加班時數
        /// </summary>
        /// 
        public int HoursLowerBound { get; set; } = 1;
        /// <summary>
        /// 最少當日加班支薪時數
        /// </summary>
        public int MinPaidOvertimeHours { get; set; } = 1;

        /// <summary>
        /// 每日上班時數上限
        /// </summary>
        public int HoursUpperBound { get; set; } = 12;

        /// <summary>
        /// 日期警告訊息，無訊息時為 ""
        /// </summary>
        public string DateAlarmMessage { get; set; } = string.Empty;

        /// <summary>
        /// 日期的錯誤訊息
        /// </summary>
        /// <value>The date error message.</value>
        public string DateErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// 時數警告訊息，無訊息時為 ""
        /// </summary>
        public string HoursAlarmMessage { get; set; } = string.Empty;

        /// <summary>
        /// 時數錯誤訊息，無訊息時為 ""
        /// </summary>
        public string HoursErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// 申請人錯誤訊息，無訊息時為 ""
        /// </summary>
        public string UserErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// 警告訊息，無訊息時為 ""
        /// </summary>
        public string AlarmMessage { get; set; } = string.Empty;

        /// <summary>
        /// 錯誤訊息，無訊息時為 ""
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// 【加班截止時間】早於 【加班起始時間】 之訊息
        /// </summary>
        public string EndTimeMustLaterThanStartTime { get; } = AttendanceParameters.EndTimeMustLaterThanStartTime;

        /// <summary>
        /// 加班卡加班時段內包含上班時間 之訊息
        /// </summary>
        public string BadOvertimeSection { get; } = AttendanceParameters.BadOvertimeSection;

        /// <summary>
        /// 加班時間重疊 之訊息
        /// </summary>
        public string TimeOverlapped { get; } = AttendanceParameters.TimeOverlapped;

        /// <summary>
        /// 加班時數不足一小時 之訊息
        /// </summary>
        public string B1CardAtLeastOneHour { get; } = AttendanceParameters.B1CardAtLeastOneHour;

        /// <summary>
        /// 工作日加班總時數超過12小時 之訊息
        /// </summary>
        public string AboveDayOvertimeLimitError { get; } = AttendanceParameters.AboveDayOvertimeLimitError;
    }
}
