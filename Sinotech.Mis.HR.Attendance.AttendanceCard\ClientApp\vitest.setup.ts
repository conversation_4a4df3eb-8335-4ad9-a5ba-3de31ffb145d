import { config } from '@vue/test-utils'
import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import PrimeVue from 'primevue/config'
import ConfirmationService from 'primevue/confirmationservice'
import ToastService from 'primevue/toastservice'
import { createRouter, createWebHistory } from 'vue-router'

config.global.plugins = [
  createTestingPinia(),
  PrimeVue,
  ConfirmationService,
  ToastService,
  createRouter({
    history: createWebHistory(),
    routes: [{
      path: '/',
      component: { template: '<div/>' }
    }]
  })
]

config.global.stubs = {
  'router-link': { template: '<div/>' },
  'router-view': { template: '<div/>' }
}