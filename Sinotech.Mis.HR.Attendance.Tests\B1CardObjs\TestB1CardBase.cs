using FakeItEasy;
using Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.PositionType;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.Tests
{
    [ExcludeFromCodeCoverage]
    public class TestB1CardBase
    {
        protected readonly DateTime _now = DateTime.Now.Date + TimeSpan.FromSeconds(1);
        protected readonly string _employeeNumber = "0000";
        protected readonly string _reason = "test";
        protected readonly string _addSigner = "";
        protected readonly B1Card _b1Card = new B1Card();
        protected readonly Workday _workday = new Workday();
        protected readonly string _project1 = "TI00001";
        protected readonly string _project2 = "TI00002";

        public TestB1CardBase()
        {
            #region Prepare Data

            _b1Card.EmpNo = _employeeNumber;
            _b1Card.AddSigners = _addSigner;
            _b1Card.Reason = _reason;
            _b1Card.TotalHours = 3;
            _b1Card.Details = new List<B1CardDetail>()
            {
                new B1CardDetail()
                {
                    B1_CODE = 1,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now,
                    EndTime = _now + TimeSpan.FromHours(2),
                    Hour = 2,
                    Project = _project1,
                },
                new B1CardDetail()
                {
                    B1_CODE = 1,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now + TimeSpan.FromHours(2),
                    EndTime = _now + TimeSpan.FromHours(3),
                    Hour = 1,
                    Project = _project2,
                }
            };

            _workday.DayType = WorkdayType.FlexbleHoliday;

            #endregion
        }

        protected IB1CardDataProvider GetFakeObject(B1CardPositionEnum b1CardPosition)
        {
            var provider = A.Fake<IB1CardDataProvider>();
            A.CallTo(() => provider.EmployeeNumber).Returns(_employeeNumber);
            A.CallTo(() => provider.OvertimeDate).Returns(_now);
            A.CallTo(() => provider.GetPositionType())
                .Returns(B1CardPositionEnum.GeneralStaff);

            A.CallTo(() => provider.GetB1Card()).Returns(_b1Card);
            A.CallTo(() => provider.GetOverTimeDateInfo()).Returns(_workday);

            return provider;
        }
    }
}
