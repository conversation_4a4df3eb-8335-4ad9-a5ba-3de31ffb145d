﻿using System;
using System.Collections.Generic;

namespace Sinotech.Mis.HR.Attendance.Common
{

    /// <summary>
    /// 單日加班詳細資料
    /// </summary>
    public class DayOvertime
    {
        /// <summary>
        /// 本日加班的各項紀錄
        /// </summary>
        public List<OvertimeRecord> OvertimeRecords { get; set; } = new List<OvertimeRecord>();

        /// <summary>
        /// 本日加班依小時分配
        /// </summary>
        public List<OvertimeHour> HourDetails = new List<OvertimeHour>();

        /// <summary>
        /// 加班日期
        /// </summary>
        public DateTime OvertimeDate { get; set; }

        /// <summary>
        /// 日期類型 (工作日、週六休息日等)
        /// </summary>
        public WorkdayType WorkdayType { get; set; }

        /// <summary>
        /// 總加班時數，包括 InOvertime = false
        /// </summary>
        public int TotalHours { get; set; }

        /// <summary>
        /// 上限內總加班時數，只算 InOvertime = true
        /// </summary>
        public int InOvertimeHours { get; set; }

        /// <summary>
        /// 支薪加班倍率/補休折算加班倍率/加權時數  合計
        /// </summary>
        public double PaidHours { get; set; }
    }

    /// <summary>
    /// 加班紀錄，對應到B1Rate
    /// 每一天加班(一個DayOvertime物件instance)有 1~12 個OvertimeRecord，加班填一筆就有一個OvertimeRecord<br/>
    /// 
    /// </summary>
    public class OvertimeRecord
    {
        /// <summary>
        /// 加班類型
        /// </summary>
        public OvertimeType OvertimeType { get; set; }

        /// <summary>
        /// 顯示排序用，目前為本日加班第幾筆加班類型<br/>
        /// 對應到B1Card.B1_SERIALNO
        /// </summary>
        public int Order { get; set; } = 1;

        /// <summary>
        /// 計畫編號
        /// </summary>
        public string ProjectNumber { get; set; } = "";

        /// <summary>
        /// 開始加班時間
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 結束加班時間
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 總加班時數，包括 InOvertime = false
        /// </summary>
        public int Hours { get; set; }

        /// <summary>
        /// 支薪加班倍率/補休折算加班倍率/加權時數  合計
        /// </summary>
        public double PaidHours { get; set; }

        /// <summary>
        /// 上限內總加班時數，只算 InOvertime = true
        /// </summary>
        public int InOvertimeHours { get; set; }

        /// <summary>
        /// 本筆記錄的不同加班倍率，每筆倍率有 1~n小時
        /// </summary>
        public List<OvertimeRecordRate> RateDetails = new List<OvertimeRecordRate>();

        /// <summary>
        /// 本筆紀錄的每小時詳細資料
        /// </summary>
        public List<OvertimeHour> HourDetails = new List<OvertimeHour>();
    }

    /// <summary>
    /// 加班紀錄倍率，一筆加班紀錄的特定一種加班倍率，每種倍率有 1~n小時
    /// </summary>
    public class OvertimeRecordRate
    {
        /// <summary>
        /// 加班類型
        /// </summary>
        public OvertimeType OvertimeType { get; set; }

        /// <summary>
        /// 計畫編號
        /// </summary>
        public string? ProjectNumber { get; set; }

        /// <summary>
        /// 總加班時數，包括 InOvertime = false
        /// </summary>
        public int Hours { get; set; }

        /// <summary>
        /// 上限內總加班時數，只算 InOvertime = true
        /// </summary>
        public int InOvertimeLimitHours { get; set; }

        /// <summary>
        /// 是否納入加班上限累計範圍
        /// </summary>
        public bool InOvertime { get; set; } = false;

        /// <summary>
        ///  加班倍率代碼: 1：1倍、2：4/3 (1.33)倍、3：5/3(1.66)倍、4：補休假、6：8/3 (2.66)倍
        /// </summary>
        public OvertimeRateType RateType { get; set; }

        /// <summary>
        /// 支薪加班倍率/補休折算加班倍率/加權時數  合計，也就是 Rate*Hours
        /// </summary>
        public double PaidHours { get; set; }

        /// <summary>
        /// 本倍率的每小時詳細資料
        /// </summary>
        public List<OvertimeHour> DetailHours = new List<OvertimeHour>();
    }

    /// <summary>
    /// 每小時加班資訊
    /// </summary>
    public class OvertimeHour
    {
        /// <summary>
        /// 加班類型
        /// </summary>
        public OvertimeType OvertimeType { get; set; }

        /// <summary>
        /// 目前時數歸屬於哪一個 OvertimeType.Order
        /// </summary>
        public int TypeOrder { get; set; } = 1;

        /// <summary>
        /// 計畫編號
        /// </summary>
        public string ProjectNumber { get; set; } = "";

        /// <summary>
        /// 第幾小時
        /// </summary>
        public int NumberOfHours { get; set; }

        /// <summary>
        /// 是否納入加班上限累計範圍
        /// </summary>
        public bool InOvertime { get; set; } = false;

        /// <summary>
        /// 加班倍率代碼: 1：1倍、2：4/3 (1.33)倍、3：5/3(1.66)倍、4：補休假、6：8/3 (2.66)倍
        /// </summary>
        public OvertimeRateType RateType { get; set; }

        /// <summary>
        /// 換算$時加班倍率代碼: 1：1倍、2：4/3 (1.33)倍、3：5/3(1.66)倍、4：補休假、6：8/3 (2.66)倍
        /// </summary>
        public OvertimeRateType PaidRateType { get; set; }

        /// <summary>
        /// 加班倍率，如 4/3 (1.33)、5/3(1.66)等
        /// </summary>
        public double Rate { get; set; }

        /// <summary>
        /// 支薪加班倍率/補休折算加班倍率/加權時數，若 Rate 為 4 補休假時需要，例如 1/3 (1.33)倍2小時就是 2/3 (2.66)，在此同 Rate
        /// </summary>
        public double PaidHour { get; set; }
    }
}
