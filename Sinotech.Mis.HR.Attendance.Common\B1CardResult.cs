﻿namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// B1CardResult
    /// </summary>
    public class B1CardResult
    {
        /// <summary>
        /// 代碼
        /// </summary>
        public int Code { get; }

        /// <summary>
        /// 狀態
        /// </summary>
        public B1CardStatusEnum Status { get; }

        /// <summary>
        /// 錯誤訊息
        /// </summary>
        public string Message { get; } = string.Empty;

        /// <summary>
        /// 建構元
        /// </summary>
        /// <param name="code"></param>
        /// <param name="status"></param>
        /// <param name="message"></param>
        public B1CardResult(int code, B1CardStatusEnum status, string message)
        {
            Code = code;
            Status = status;
            Message = message;
        }
    }
}
