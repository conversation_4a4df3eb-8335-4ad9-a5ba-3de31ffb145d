﻿using Sinotech.Mis.HR.Attendance.Common;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.Tests
{
    public class EventRelationRecordTests
    {
        [Fact]
        public void EventRelationRecord_DefaultValues_ShouldBeInitialized()
        {
            // Arrange & Act
            var record = new EventRelationRecord();

            // Assert
            Assert.Equal(string.Empty, record.SheetNo);
            Assert.Equal(string.Empty, record.EmpName);
            Assert.Equal(string.Empty, record.LeaveName);
            Assert.Equal(string.Empty, record.LeaveSubName);
            Assert.Equal(string.Empty, record.FormInfo);
            Assert.Equal(0, record.LeaveHour);
        }

        [Fact]
        public void EventRelationRecord_SetValues_ShouldBeSetCorrectly()
        {
            // Arrange
            var record = new EventRelationRecord
            {
                SheetNo = "S123",
                EmpName = "John Doe",
                LeaveName = "Annual Leave",
                LeaveSubName = "Paid Leave",
                FormInfo = "Form Information",
                LeaveHour = 8
            };

            // Act & Assert
            Assert.Equal("S123", record.SheetNo);
            Assert.Equal("<PERSON> Doe", record.EmpName);
            Assert.Equal("Annual Leave", record.LeaveName);
            Assert.Equal("Paid Leave", record.LeaveSubName);
            Assert.Equal("Form Information", record.FormInfo);
            Assert.Equal(8, record.LeaveHour);
        }
    }
}
