import { SINODAMS_DEPUTY_URL } from './appUrl'
import { WORKDAY_CODE, FORM_ID } from './appConst'
import type { EmployeeType, SignerType, DayType, CalendarDayType, ProjectType } from './appType'

export const dateToRocString = (date: Date, withTime: boolean = true): string => {
  let result = (date.getFullYear() - 1911).toString() + '/' + (date.getMonth() + 1).toString().padStart(2, '0') + '/' + date.getDate().toString().padStart(2, '0')
  if (withTime) {
    result += ' ' + date.getHours().toString().padStart(2, '0') + ':' + date.getMinutes().toString().padStart(2, '0')
  }
  return result
}

export const getThreeRocYears = (year: number): Array<{
  rocYear: number
  year: number
}> => {
  const result = []
  for (let index = 0; index < 3; index++) {
    result.push({
      rocYear: year - 1911 + (index - 1),
      year: year + (index - 1)
    })
  }
  return result
}

export const getCalendarDayClass = (date: DayType, workdaysData: CalendarDayType): Array<string> => {
  const ret: Array<string> = []

  const workday = workdaysData['d' + date.year.toString() + (date.month + 1).toString().padStart(2, '0') + date.day.toString().padStart(2, '0')]
  if (workday !== undefined) {
    switch (workday.dayType) {
      case WORKDAY_CODE.WeekWorkday:
      case WORKDAY_CODE.MakeUpWorkday:
        ret.push('working-day-black')
        break
      case WORKDAY_CODE.SaturdayRestday:
      case WORKDAY_CODE.SundayRegularHoliday:
        ret.push('day-off')
        break
      case WORKDAY_CODE.WeekHoliday:
      case WORKDAY_CODE.SaturdayHoliday:
      case WORKDAY_CODE.SundayHoliday:
      case WORKDAY_CODE.MakeUpHoliday:
      case WORKDAY_CODE.FlexbleHoliday:
      case WORKDAY_CODE.WeekNaturalDisasterDay:
      case WORKDAY_CODE.SaturdayNaturalDisasterDay:
      case WORKDAY_CODE.WeekRestday:
        ret.push('holiday fw-bold')
        break
      default:
        break
    }
  }
  return ret
}

export const getFullCalendarDayClass = (date: DayType, workdaysData: CalendarDayType): Array<string> => {
  const ret: Array<string> = []

  const workday = workdaysData['d' + date.year.toString() + (date.month + 1).toString().padStart(2, '0') + date.day.toString().padStart(2, '0')]
  if (workday !== undefined) {
    switch (workday.dayType) {
      case WORKDAY_CODE.WeekWorkday:
      case WORKDAY_CODE.MakeUpWorkday:
        ret.push('working-day')
        break
      case WORKDAY_CODE.SaturdayRestday:
      case WORKDAY_CODE.SundayRegularHoliday:
      case WORKDAY_CODE.WeekHoliday:
      case WORKDAY_CODE.SaturdayHoliday:
      case WORKDAY_CODE.SundayHoliday:
      case WORKDAY_CODE.MakeUpHoliday:
      case WORKDAY_CODE.FlexbleHoliday:
      case WORKDAY_CODE.WeekNaturalDisasterDay:
      case WORKDAY_CODE.SaturdayNaturalDisasterDay:
      case WORKDAY_CODE.WeekRestday:
        ret.push('day-off')
        break
      default:
        break
    }
  }
  return ret
}

export const sortCard = (val1: { formID: string, formUID: string, formSubject: string, startTime: string }, val2: { formID: string, formUID: string, formSubject: string, startTime: string }): number => {
  if (val1.formID === val2.formID) {
    return 0
  }

  if (val1.formID === 'A1Card') {
    return -1
  } else if (val1.formID === 'B1CardApp') {
    if (val2.formID === 'A1Card') {
      return 1
    }
    return -1
  } else if (val1.formID === 'B1Card') {
    if (val2.formID === 'A1Card' || val2.formID === 'B1CardApp') {
      return 1
    }
    return -1
  } else if (val1.formID === 'C1Card') {
    if (val2.formID === 'A1Card' || val2.formID === 'B1CardApp' || val2.formID === 'B1Card') {
      return 1
    }
    return -1
  }
  return 1
}

export const formatFormID = (data: string): string => {
  let formName
  switch (data) {
    case 'A1Card':
      formName = FORM_ID.A1Card
      break
    case 'B1CardApp':
      formName = FORM_ID.B1CardApp
      break
    case 'B1Card':
      formName = FORM_ID.B1Card
      break
    case 'C1Card':
      formName = FORM_ID.C1Card
      break
    default:
      formName = ''
  }

  return formName
}

export const onSearchProjectData = (options: Array<ProjectType>, search: string): Array<ProjectType> => {
  return options.filter((option: ProjectType) => {
    return option.id.toString().toUpperCase().includes(search.toUpperCase()) ||
      option.name.toUpperCase().includes(search.toUpperCase()) ||
      (option.id.toString() + option.name).toUpperCase().includes(search.toUpperCase().replace(/\s/g, ''))
  })
}

export const onSearchEmployeeData = (options: Array<EmployeeType | SignerType>, search: string): Array<EmployeeType | SignerType> => {
  return options.filter((option: EmployeeType | SignerType) => {
    if (option.userId !== null && option.userName !== null) {
      return option.userId.toString().includes(search) ||
        option.userName.includes(search) ||
        (option.userId.toString() + option.userName).includes(search.replace(/\s/g, ''))
    } else {
      return option
    }
  })
}

export const onClickDownloadUrl = (url: string): void => {
  window.location.assign(url)
}

export const onSetDeputy = (): void => {
  window.open(SINODAMS_DEPUTY_URL, '_blank')
}