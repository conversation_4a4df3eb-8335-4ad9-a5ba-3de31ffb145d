﻿using Xunit;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Antlr4.Runtime.Atn.SemanticContext;

namespace Sinotech.Mis.HR.Attendance.Common.Tests
{
    public class NotificationFormTests
    {
        [Theory]
        [InlineData("0348")]
        [InlineData("0349")]
        [InlineData("0395")]
        [InlineData("2268")]
        [InlineData("2295")]
        public void NotificationFormTest(string empNo)
        {
            Notification notification = new Notification()
            {
                NotifyEmpNo = empNo
            };

            Form form = new Form();
            NotificationForm notificationForm=new NotificationForm(notification, form);
            Assert.NotEmpty(notificationForm.Notification.NotifyEmpNo);
            Assert.Equal(empNo, notificationForm.Notification.NotifyEmpNo);
        }
    }
}