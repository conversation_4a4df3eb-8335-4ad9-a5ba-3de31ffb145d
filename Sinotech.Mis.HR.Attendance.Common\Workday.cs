﻿using System;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 工作日
    /// </summary>
    public class Workday
    {
        /// <summary>
        /// 日曆別，目前只有一個，值為 1
        /// </summary>
        /// <value>
        /// 1
        /// </value>
        public int ShiftId { get; set; }

        /// <summary>
        /// 日期
        /// </summary>
        /// <value>
        /// The work date.
        /// </value>
        public DateTime WorkDate { get; set; }

        /// <summary>
        /// 工作日類型
        /// </summary>
        /// <value>
        /// The type of the day.
        /// </value>
        public WorkdayType DayType { get; set; }
        /// <summary>
        /// 工作日類型名稱
        /// </summary>
        /// <value>
        /// The name of the type.
        /// </value>
        public string TypeName { get; set; } = string.Empty;

        /// <summary>
        /// 是否為休假日
        /// </summary>
        /// <value>
        ///   <c>true</c> if [day off]; otherwise, <c>false</c>.
        /// </value>
        public bool DayOff { get; set; }

        /// <summary>
        /// 工作日正常上班時間
        /// </summary>
        public DateTime ArrivalTime { get; set; }

        /// <summary>
        /// 工作日正常下班時間
        /// </summary>
        public DateTime DepartureTime { get; set; }

        /// <summary>
        /// 工作日最早彈性上班時間
        /// </summary>

        public DateTime FlexibleArrivalBefore { get; set; }

        /// <summary>
        /// 工作日最晚彈性上班時間
        /// </summary>

        public DateTime FlexibleArrivalAfter { get; set; }

        /// <summary>
        /// 工作日最早彈性下班時間
        /// </summary>

        public DateTime FlexibleDepartureBefore { get; set; }

        /// <summary>
        /// 工作日最晚彈性下班時間
        /// </summary>
        public DateTime FlexibleDepartureAfter { get; set; }

        /// <summary>
        /// 工作日午休起始時間
        /// </summary>
        public DateTime MiddayBreakStart { get; set; }

        /// <summary>
        /// 工作日午休結束時間
        /// </summary>
        public DateTime MiddayBreakEnd { get; set; }

        /// <summary>
        /// 工作日上午休息開始時間
        /// </summary>
        public DateTime MorningRestStart { get; set; }

        /// <summary>
        /// 工作日上午休息結束時間
        /// </summary>
        public DateTime MorningRestEnd { get; set; }

        /// <summary>
        /// 工作日下午休息開始時間
        /// </summary>
        public DateTime AfternoonRestStart { get; set; }

        /// <summary>
        /// 工作日下午休息結束時間
        /// </summary>
        public DateTime AfternoonRestEnd { get; set; }

        /// <summary>
        /// 工作日正常工時(小時)
        /// </summary>
        public double WorkHours { get; set; }
        /// <summary>
        /// 星期數 0-星期日 1-星期一 2-星期二 3-星期三 4-星期四 5-星期五 6-星期六
        /// </summary>
        public int WeekDay { get; set; }

        /// <summary>
        /// 註釋
        /// </summary>
        public string? Comment { get; set; }
    }

}
