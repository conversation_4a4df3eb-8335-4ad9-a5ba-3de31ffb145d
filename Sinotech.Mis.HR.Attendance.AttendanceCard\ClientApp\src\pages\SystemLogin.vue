<template>
  <div :class="[prodModeClass, 'd-flex px-2 py-1']">
    <img
      :src="sinotechBanner"
      class="float-start"
      alt="財團法人中興工程顧問社"
    >
  </div>
  <div class="container mt-2">
    <div class="row d-flex align-items-center justify-content-center">
      <div class="col-6">
        <div class="card border-dark mb-3 text-center">
          <div class="card-header">
            三卡電子表單
          </div>
          <div class="card-body">
            <div class="row mx-0">
              <div class="col-12 my-2">
                <input
                  type="text"
                  class="form-control"
                  v-model="account"
                  placeholder="員工編號"
                  maxlength="50"
                  :disabled="submitted === true"
                  @keyup.enter="onLogin"
                />
              </div>
              <div class="col-12 my-2">
                <input
                  type="password"
                  class="form-control"
                  v-model="password"
                  placeholder="密碼"
                  maxlength="50"
                  :disabled="submitted === true"
                  @keyup.enter="onLogin"
                />
              </div>
            </div>
            <button
              type="button"
              :class="[
                'btn',
                (loginDisabled === true) ? 'btn-outline-secondary' : 'btn-primary'
              ]"
              :disabled="loginDisabled === true"
              @click="onLogin"
            >
              <span>
                <template v-if="submitted === true">
                  驗證中...
                </template>
                <template v-else>
                  登入
                </template>
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, onBeforeRouteLeave } from 'vue-router'
import { useToast } from 'primevue/usetoast'
import { useAuthUserStore } from '../store/index'
import { FETCH_TIMEOUT_TIME, WARN_DISPLAY_TIME } from '../api/appConst'
import { POST_LOGIN_URL } from '../api/appUrl'
import sinotechBanner from '../assets/Banner.png'
import publicKeyRaw from '../publickey.pem?raw'

let publicKey: CryptoKey
const prodModeClass = import.meta.env.VITE_PROD_MODE_CLASS
const userStore = useAuthUserStore()
const account = ref<string>('')
const password = ref<string>('')
const submitted = ref<boolean>(false)
const loginDisabled = computed<boolean>(() => (account.value.length === 0) || (password.value.length === 0) || (submitted.value === true))
const router = useRouter()
const toast = useToast()

const importPublicKey = async (key: string): Promise<CryptoKey> => {
  const binaryDerString = atob(key.replace(/-----BEGIN PUBLIC KEY-----|-----END PUBLIC KEY-----|\n/g, '').trim())
  const binaryDer = new Uint8Array(binaryDerString.split('').map(char => char.charCodeAt(0)))

  return crypto.subtle.importKey(
    'spki',
    binaryDer.buffer,
    {
      name: 'RSA-OAEP',
      hash: 'SHA-256'
    },
    false,
    ['encrypt']
  )
}

const arrayBufferToBase64 = (buffer: ArrayBuffer) => {
  let binary = ''
  const bytes = new Uint8Array(buffer)
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i])
  }
  return btoa(binary)
}

const onLogin = async (): Promise<void> => {
  if (loginDisabled.value === false) {
    submitted.value = true

    try {
      const res: Response = await fetch(POST_LOGIN_URL, {
        method: 'POST',
        headers: {
          'content-type': 'application/json'
        },
        body: JSON.stringify({
          username: arrayBufferToBase64(await crypto.subtle.encrypt(
            {
              name: 'RSA-OAEP'
            },
            publicKey,
            new TextEncoder().encode(account.value)
          )),
          password: arrayBufferToBase64(await crypto.subtle.encrypt(
            {
              name: 'RSA-OAEP'
            },
            publicKey,
            new TextEncoder().encode(password.value)
          ))
        }),
        signal: AbortSignal.timeout(FETCH_TIMEOUT_TIME)
      })
      if (res.ok) {
        const resJson = await res.json()
        if (resJson?.isAuthenticated === true) {
          await userStore.setLogonUser(resJson.userId).then(() => {
            submitted.value = false
            router.push({
              name: 'Home'
            })
          }).catch((err: Error): void => {
            console.error(err)
            submitted.value = false
            toast.add({
              severity: 'error',
              summary: '登入失敗',
              life: WARN_DISPLAY_TIME,
              group: 'app'
            })
          })
        } else {
          submitted.value = false
          toast.add({
            severity: 'error',
            summary: resJson.errorMessage,
            life: WARN_DISPLAY_TIME,
            group: 'app'
          })
        }
      } else {
        const err = await res.text()
        console.error(err)
        submitted.value = false
        toast.add({
          severity: 'error',
          summary: '登入失敗',
          life: WARN_DISPLAY_TIME,
          group: 'app'
        })
      }
    } catch (err: unknown) {
      console.error(err)
      submitted.value = false
      toast.add({
        severity: 'error',
        summary: '登入失敗',
        life: WARN_DISPLAY_TIME,
        group: 'app'
      })
    }
  }
}

onBeforeRouteLeave(() => {
  toast.removeGroup('app')
})

onMounted(async (): Promise<void> => {
  publicKey = await importPublicKey(publicKeyRaw)
})
</script>