﻿using FakeItEasy;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves;
using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves.Tests
{
    public class MenstrualLeaveTests : TestC1CardBase
    {
        public MenstrualLeaveTests()
        {
            #region Prepare Data

            _c1Card.LeaveNumber = LeaveKindEnum.MenstrualLeave;

            #endregion
        }

        [Theory]
        [InlineData(true, true, MenstrualLeave.CodeOk)]
        [InlineData(true, false, MenstrualLeave.CodeFemaleOnly)]
        [InlineData(false, true, MenstrualLeave.CodeLeaveRecordNotCreateYet)]
        [InlineData(false, false, MenstrualLeave.CodeFemaleOnly)]
        public void TestCanTakeThisLeave(bool ifLeaveRecordExists, bool isFemale, int expectedCode)
        {
            A.CallTo(() => _c1CardBo.IsFemale(A<string>.Ignored)).Returns(isFemale);

            A.CallTo(() => _c1CardBo.IfLeaveRecordExists(A<string>.Ignored,
                A<DateTime>.Ignored, A<DateTime>.Ignored)).Returns(ifLeaveRecordExists);

            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CanTakeThisLeave();

            Assert.Equal(expectedCode, result.Code);
        }

        [Theory]
        [InlineData(true, 8, MenstrualLeave.CodeExceedQuota)]
        [InlineData(false, 1, MenstrualLeave.CodeIllegalRange)]
        [InlineData(false, 9, MenstrualLeave.CodeIllegalRange)]
        [InlineData(false, 8, MenstrualLeave.CodeOk)]
        [InlineData(false, 16, MenstrualLeave.CodeIllegalRange)]
        public void TestExceedQuota(bool IsMenstrualLeaveAlreadyTaken, int totalHours, int returnCode)
        {
            A.CallTo(() => _c1CardBo.IsMenstrualLeaveAlreadyTaken(A<string>.Ignored,
                    A<int>.Ignored, A<int>.Ignored)).Returns(IsMenstrualLeaveAlreadyTaken);
            A.CallTo(() => _c1CardBo.CalculateEmpTakeLeaveWorkingHours(A<string>.Ignored, A<DateTime>.Ignored, A<DateTime>.Ignored)).Returns(totalHours);

            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CheckOverPermittedLeaveHours();

            Assert.Equal(returnCode, result.Code);
        }

        [Theory]
        [InlineData(1, MenstrualLeave.CodeIllegalRange)]
        [InlineData(9, MenstrualLeave.CodeIllegalRange)]
        [InlineData(8, MenstrualLeave.CodeOk)]
        public void TestRange(int totalHours, int returnCode)
        {
            A.CallTo(() => _c1CardBo.IsMenstrualLeaveAlreadyTaken(A<string>.Ignored, A<int>.Ignored, A<int>.Ignored)).Returns(false);
            A.CallTo(() => _c1CardBo.CalculateEmpTakeLeaveWorkingHours(A<string>.Ignored, A<DateTime>.Ignored, A<DateTime>.Ignored)).Returns(totalHours);

            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CheckOverPermittedLeaveHours();

            Assert.Equal(returnCode, result.Code);
        }

        [Fact]
        public void TestCheckRequiredFields()
        {
            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CheckRequiredFields();

            Assert.Equal(MenstrualLeave.CodeOk, result.Code);
        }

        [Theory]
        [InlineData(Gender.Female, true)]
        [InlineData(Gender.Male, false)]
        public void TestGender(Gender gender, bool expected)
        {
            var result = MenstrualLeave.IsAllowForThisGender(gender);
            Assert.Equal(expected, result);
        }
    }
}