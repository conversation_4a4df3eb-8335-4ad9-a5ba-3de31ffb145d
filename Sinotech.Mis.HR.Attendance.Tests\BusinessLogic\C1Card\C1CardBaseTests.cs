﻿using FakeItEasy;
using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.Utilities;
using System;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.Tests.BusinessLogic.C1CardObjs
{
    [ExcludeFromCodeCoverage]
    /// <summary>
    /// C1CardBase 的單元測試
    /// </summary>
    public class C1CardBaseTests
    {
        private readonly IC1CardBo _fakeC1CardBo;
        private readonly C1Card _c1Card;

        public C1CardBaseTests()
        {
            _fakeC1CardBo = A.Fake<IC1CardBo>();
            _c1Card = new C1Card
            {
                EmpNo = "2268",
                StartDate = new DateTime(2024, 10, 4, 9, 0, 0),
                EndDate = new DateTime(2024, 10, 4, 12, 0, 0),
                Hours = 3,
                LeaveSubNumber = 0,
                LeaveNumber = LeaveKindEnum.BusinessOutLeave
            };
        }

        /// <summary>
        /// 測試情境：請假時數未超過工作時數
        /// </summary>
        [Fact]
        public void CheckOverPermittedWorkingHours_WhenHoursNotExceeded_ShouldReturnOk()
        {
            // Arrange
            A.CallTo(() => _fakeC1CardBo.CheckAccumulatedOverPermittedWorkingHours(A<C1Card>._))
                .Returns((false, 0, null));
            var c1CardBase = C1CardFactory.CreateLeave(_c1Card, _fakeC1CardBo);

            // Act
            var result = c1CardBase.CheckAccumulatedOverPermittedWorkingHours();

            // Assert
            Assert.Equal(3000100, result.Code); // ResultOk
        }

        /// <summary>
        /// 測試情境：請假時數超過工作時數
        /// </summary>
        [Fact]
        public void CheckOverPermittedWorkingHours_WhenHoursExceeded_ShouldReturnTimeLargerThanWorkHours()
        {
            // Arrange
            DateTime date = new DateTime(2024, 10, 4, 0, 0, 0, DateTimeKind.Local);
            A.CallTo(() => _fakeC1CardBo.CheckAccumulatedOverPermittedWorkingHours(A<C1Card>._))
                .Returns((true, 10, date));
            var c1CardBase = C1CardFactory.CreateLeave(_c1Card, _fakeC1CardBo);
            string rocDate = CardUtility.RocChineseDateString(date);
            string strHours = 10.ToString();
            // Act
            var result = c1CardBase.CheckAccumulatedOverPermittedWorkingHours();
            
            // Assert
            Assert.Equal(3000321, result.Code); // ResultTimeLargerThanWorkHours
            Assert.Contains(strHours, result.Message);
            Assert.Contains(rocDate, result.Message);
        }
    }
}
