﻿using FakeItEasy;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sinotech.Mis.Extensions.Configuration;
using Sinotech.Mis.HR.Attendance.BusinessLogic;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.Tests;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers.Tests
{
    [ExcludeFromCodeCoverage]
    public class FormControllerTests
    {

        private readonly B1CardAppBo _b1CardAppBo;
        private readonly CardBoFactory _cardBoFactory;
        private readonly IConfiguration _configuration;
        private readonly FormController _controller;
        private readonly FormBo _formBo;
        private readonly string ConnectionStringAttendance;

        public FormControllerTests(AttendanceBo attendanceBo, B1CardAppBo b1CardAppBo, FormBo formBo, CardBoFactory cardBoFactory, ILogger<FormController> logger, FileExtensionContentTypeProvider fileExtensionContentTypeProvider)
        {
            _formBo = formBo;
            _b1CardAppBo = b1CardAppBo;
            _cardBoFactory = cardBoFactory;
            _configuration = new ConfigurationBuilder().
                AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).Build();
            ConnectionStringAttendance = _configuration.GetSecuredConnectionString("Attendance");
            TestHelper.ClearData(ConnectionStringAttendance);
            _controller = new FormController(attendanceBo, formBo, cardBoFactory,
                _configuration, logger, fileExtensionContentTypeProvider);
        }

        private void AddA1Card()
        {
            Form form = new Form();
            form.FormUID = new Guid("*************-7777-7777-************");
            form.FormSubject = "正常工作卡-白燕菁-111年04月中旬";
            form.FormNo = "旬111000104";
            form.FormID = "A1Card";
            form.FormInfo = "111年04月中旬";
            form.EmpNo = "0349";
            form.EmpName = "白燕菁";
            form.DeptNo = 4;
            form.DeptSName = "企劃處";
            form.TeamID = null;
            form.TeamCName = null;
            form.CreatedEmpNo = "0349";
            form.CreatedName = "白燕菁";
            form.CreatedTime = DateTime.Parse("2022-07-27T07:54:48.44").ToLocalTime();
            form.FilledTime = DateTime.Parse("2022-07-27T07:53:33.913").ToLocalTime();
            form.CreatedIP = "***********";
            form.AddedSigner = "0274";
            form.StartTime = DateTime.Parse("2022-07-27T07:53:33.913").ToLocalTime();
            form.FormStatus = 1;
            form.TotalSteps = 2;
            form.CurrentStep = 0;

            form.Flows = new List<FormFlow>();
            FormFlow flow = new FormFlow();
            flow.FlowUID = new Guid("*************-5555-5555-************");
            flow.FormUID = form.FormUID;
            flow.IsNotification = false;
            flow.FlowName = "加會人員";
            flow.RecipientEmpNo = "0274";
            flow.RecipientName = "薛強";
            flow.RecipientDeptNo = 10;
            flow.RecipientDeptSName = "土研中心";
            flow.Step = 1;
            flow.FlowStatus = 0;
            form.Flows.Add(flow);

            flow = new FormFlow();
            flow.FlowUID = new Guid("*************-6666-6666-************");
            flow.FormUID = form.FormUID;
            flow.IsNotification = false;
            flow.FlowName = "申請人部門主管";
            flow.RecipientEmpNo = "L04";
            flow.RecipientName = "企劃處經理";
            flow.RecipientDeptNo = 4;
            flow.RecipientDeptSName = "企劃處";
            flow.Step = 2;
            flow.FlowStatus = 0;
            form.Flows.Add(flow);

            ICardBaseBo cardBo = _cardBoFactory.GetCardBo("A1Card");
            A1Card card = new A1Card();
            card.FormUID = new Guid("*************-7777-7777-************");
            card.A1_EMPNO = "0349";
            card.A1_YYMM = "11104";
            card.A1_NN = '2';
            card.A1_SHEETNO = "111年04月中旬";
            card.A1_SOURCE = "Attendance";
            card.A1_WYYMMDD = "1110727";
            card.A1_AYYMMDD = "       ";
            card.A1_STATUS = 1;
            card.AddSigners = "0274";
            card.A1_WDate = DateTime.Parse("2022-07-26T23:53:33.913");
            card.CreatedTime = DateTime.Parse("2022-07-26T23:54:48.440");
            card.UpdatedEmpNo = "0349";
            card.UpdatedName = "白燕菁";
            card.UpdatedTime = DateTime.Parse("2022-07-26T23:54:48.440").ToLocalTime();
            card.UpdatedIP = "***********";
            card.UpdatedHost = "03-333.secinc";
            A1CardDetail detail = new A1CardDetail();
            detail.A1_PROJNO = "TI21508";
            detail.A1_DDHH = "8007081100";
            detail.A1_SERIALNO = "01";
            detail.A1_HOUR = 25;
            card.Details.Add(detail);
            detail = new A1CardDetail();
            detail.A1_PROJNO = "RP19553";
            detail.A1_DDHH = "0001007700";
            detail.A1_HOUR = 15;
            detail.A1_SERIALNO = "02";
            card.Details.Add(detail);

            _formBo.AddForm(form, cardBo, card);
        }

        private void AddA1CardSimple()
        {
            Form form = new Form();
            form.FormUID = new Guid("*************-2222-2222-************");
            form.FormSubject = "正常工作卡-岳美熹-111年04月中旬";
            form.FormNo = "旬111000104";
            form.FormID = "A1Card";
            form.FormInfo = "111年04月中旬";
            form.EmpNo = "2130";
            form.EmpName = "岳美熹";
            form.DeptNo = 16;
            form.DeptSName = "防災中心";
            form.TeamID = null;
            form.TeamCName = null;
            form.CreatedEmpNo = "2130";
            form.CreatedName = "岳美熹";
            form.CreatedTime = DateTime.Parse("2022-07-27T07:54:48.44").ToLocalTime();
            form.FilledTime = DateTime.Parse("2022-07-27T07:53:33.913").ToLocalTime();
            form.CreatedIP = "***********";
            form.AddedSigner = "";
            form.StartTime = DateTime.Parse("2022-07-27T07:53:33.913").ToLocalTime();
            form.FormStatus = 1;
            form.TotalSteps = 2;
            form.CurrentStep = 0;

            form.Flows = new List<FormFlow>();

            ICardBaseBo cardBo = _cardBoFactory.GetCardBo("A1Card");

            A1Card card = new A1Card();
            card.FormUID = new Guid("*************-2222-2222-************");
            card.A1_EMPNO = "2069";
            card.A1_YYMM = "11104";
            card.A1_SHEETNO = "111年04月中旬";
            card.A1_NN = '2';
            card.A1_WYYMMDD = "1110727";
            card.A1_STATUS = 1;
            card.A1_AYYMMDD = "       ";
            card.A1_SOURCE = "Attendance";
            card.AddSigners = "";
            card.A1_WDate = DateTime.Parse("2022-07-26T23:54:48.44").ToLocalTime();
            card.CreatedTime = card.A1_WDate;
            card.UpdatedEmpNo = "2069";
            card.UpdatedName = "林伯勳";
            card.UpdatedTime = DateTime.Parse("2022-07-26T23:54:48.440");
            card.UpdatedIP = "***********";
            card.UpdatedHost = "03-xxx.secinc";
            card.A1_STATUS = (int)FormStatus.Processing;
            A1CardDetail detail = new A1CardDetail();
            detail.A1_PROJNO = "TD22504";
            detail.A1_SERIALNO = "01";
            detail.A1_DDHH = "8007081100";
            detail.A1_HOUR = 25;
            card.Details.Add(detail);
            _formBo.AddForm(form, cardBo, card);
        }

        [Fact]
        public void AddA1Card_AddedSignerTooMany()
        {
            Form form = new Form();
            form.FormUID = new Guid("*************-7777-7777-************");
            form.FormSubject = "正常工作卡-白燕菁-111年04月中旬";
            form.FormNo = "旬111000104";
            form.FormID = "A1Card";
            form.FormInfo = "111年04月中旬";
            form.EmpNo = "0349";
            form.EmpName = "白燕菁";
            form.DeptNo = 4;
            form.DeptSName = "企劃處";
            form.TeamID = null;
            form.TeamCName = null;
            form.CreatedEmpNo = "0349";
            form.CreatedName = "白燕菁";
            form.CreatedTime = DateTime.Parse("2022-07-27T07:54:48.44").ToLocalTime();
            form.FilledTime = DateTime.Parse("2022-07-27T07:53:33.913").ToLocalTime();
            form.CreatedIP = "***********";
            form.AddedSigner = "0274,0178,0349,0305,0395,2268,2295,0276,0391,0760,2191";
            form.StartTime = DateTime.Parse("2022-07-27T07:53:33.913").ToLocalTime();
            form.FormStatus = 1;
            form.TotalSteps = 2;
            form.CurrentStep = 0;

            form.Flows = new List<FormFlow>();
            FormFlow flow = new FormFlow();
            flow.FlowUID = new Guid("*************-5555-5555-************");
            flow.FormUID = form.FormUID;
            flow.IsNotification = false;
            flow.FlowName = "加會人員";
            flow.RecipientEmpNo = "0274";
            flow.RecipientName = "薛強";
            flow.RecipientDeptNo = 10;
            flow.RecipientDeptSName = "土研中心";
            flow.Step = 1;
            flow.FlowStatus = 0;
            form.Flows.Add(flow);

            flow = new FormFlow();
            flow.FlowUID = new Guid("*************-6666-6666-************");
            flow.FormUID = form.FormUID;
            flow.IsNotification = false;
            flow.FlowName = "申請人部門主管";
            flow.RecipientEmpNo = "L04";
            flow.RecipientName = "企劃處經理";
            flow.RecipientDeptNo = 4;
            flow.RecipientDeptSName = "企劃處";
            flow.Step = 2;
            flow.FlowStatus = 0;
            form.Flows.Add(flow);

            ICardBaseBo cardBo = _cardBoFactory.GetCardBo("A1Card");
            A1Card card = new A1Card();
            card.FormUID = new Guid("*************-7777-7777-************");
            card.A1_EMPNO = "0349";
            card.A1_YYMM = "11104";
            card.A1_NN = '2';
            card.A1_SHEETNO = "111年04月中旬";
            card.A1_SOURCE = "Attendance";
            card.A1_WYYMMDD = "1110727";
            card.A1_AYYMMDD = "       ";
            card.A1_STATUS = 1;
            card.AddSigners = "0274";
            card.A1_WDate = DateTime.Parse("2022-07-26T23:53:33.913");
            card.CreatedTime = DateTime.Parse("2022-07-26T23:54:48.440");
            card.UpdatedEmpNo = "0349";
            card.UpdatedName = "白燕菁";
            card.UpdatedTime = DateTime.Parse("2022-07-26T23:54:48.440").ToLocalTime();
            card.UpdatedIP = "***********";
            card.UpdatedHost = "03-333.secinc";
            A1CardDetail detail = new A1CardDetail();
            detail.A1_PROJNO = "TI21508";
            detail.A1_DDHH = "8007081100";
            detail.A1_SERIALNO = "01";
            detail.A1_HOUR = 25;
            card.Details.Add(detail);
            detail = new A1CardDetail();
            detail.A1_PROJNO = "RP19553";
            detail.A1_DDHH = "0001007700";
            detail.A1_HOUR = 15;
            detail.A1_SERIALNO = "02";
            card.Details.Add(detail);

            string result = _formBo.AddForm(form, cardBo, card);
            Assert.Equal("僅開放最多加會10名人員", result);
        }

        [Fact]
        public void Approve_ArgumentOutOfRangeException_Test()
        {
            Approve approveDto = new Approve();
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo("0395");
            Assert.Throws<ArgumentOutOfRangeException>(() => _controller.Approve(approveDto));
        }

        [Fact]
        public async Task Approve_Deny_Test()
        {
            Approve approveDto = new Approve();
            string result = _controller.Approve(approveDto);
            Assert.Empty(result);
            string b1CardAppStr = "{\"B1_ADate\":null,\"B1_Code\":\"1\",\"B1_Date\":\"2022-01-04T00:00:00+08:00\",\"B1_DateTypeId\":1,\"B1_DeptNo\":4,\"B1_EmpNo\":\"0395\",\"B1_FormID\":null,\"B1_Hour\":2,\"B1_IsOverdue\":false,\"B1_OverdueReason\":\"\",\"B1_PaidHour\":0,\"B1_PrjNo\":\"RP19553\",\"B1_Reason\":\"test\",\"B1_ShouldSignEmpNo\":null,\"B1_SOURCE\":\"Attendance\",\"B1_Status\":1,\"B1_UDate\":null,\"B1_UpdatedEmpNo\":null,\"B1_WDate\":\"2024-10-25T10:22:59.621+08:00\",\"B1_WritedEmpNo\":\"0395\",\"FormUID\":\"00000000-0000-0000-0000-000000000000\",\"ID\":null,\"Name\":\"B1CardApp\",\"RequisitionID\":null,\"UpdatedEmpNo\":null,\"UpdatedName\":null,\"UpdatedTime\":null,\"UpdatedIP\":null,\"UpdatedHost\":null,\"AddSigners\":\"\",\"CreatedTime\":\"2024-10-25T10:22:59.621+08:00\",\"FilledTime\":\"2024-10-25T10:22:27.585+08:00\",\"ApplicationType\":\"\",\"UploadedFiles\":[],\"RemindSigner\":false,\"RemindMessageType\":0,\"RemindMessage\":null}";

            string creatorId = "0395";

            B1CardApp b1CardApp = JsonConvert.DeserializeObject<B1CardApp>(b1CardAppStr);
            var b1CardAppCheckResult = await _b1CardAppBo.AddB1CardApp(creatorId, b1CardApp, "127.0.0.1", "localhost");
            Assert.True(b1CardAppCheckResult.IsValid);

            Form form = _formBo.GetForm((Guid)b1CardAppCheckResult.FormUID);
            FormCard formCard = _b1CardAppBo.GetFormCard(form, "0395");
            Guid flowUID = formCard.Flows[0].FlowUID;

            Approve approve = new Approve();
            approve.ApproverDeptSName = "企劃處";
            approve.ApproveIP = "127.0.0.1";
            approve.ApproverName = "test";
            approve.ApproverEmpNo = "0391";
            approve.FlowUID = flowUID;
            approve.FormID = form.FormID;
            approve.FormUID = form.FormUID;
            approve.ApproveTime = DateTime.Now;
            approve.FlowStatus = (int)FlowStatus.Deny;
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo(approve.ApproverEmpNo);
            _controller.Approve(approve);
        }

        [Fact]
        public async Task ApproveTest()
        {
            Approve approveDto = new Approve();
            string result = _controller.Approve(approveDto);
            Assert.Empty(result);
            string b1CardAppStr = "{\"B1_ADate\":null,\"B1_Code\":\"1\",\"B1_Date\":\"2022-01-04T00:00:00+08:00\",\"B1_DateTypeId\":1,\"B1_DeptNo\":4,\"B1_EmpNo\":\"0395\",\"B1_FormID\":null,\"B1_Hour\":2,\"B1_IsOverdue\":false,\"B1_OverdueReason\":\"\",\"B1_PaidHour\":0,\"B1_PrjNo\":\"RP19553\",\"B1_Reason\":\"test\",\"B1_ShouldSignEmpNo\":null,\"B1_SOURCE\":\"Attendance\",\"B1_Status\":1,\"B1_UDate\":null,\"B1_UpdatedEmpNo\":null,\"B1_WDate\":\"2024-10-25T10:22:59.621+08:00\",\"B1_WritedEmpNo\":\"0395\",\"FormUID\":\"00000000-0000-0000-0000-000000000000\",\"ID\":null,\"Name\":\"B1CardApp\",\"RequisitionID\":null,\"UpdatedEmpNo\":null,\"UpdatedName\":null,\"UpdatedTime\":null,\"UpdatedIP\":null,\"UpdatedHost\":null,\"AddSigners\":\"\",\"CreatedTime\":\"2024-10-25T10:22:59.621+08:00\",\"FilledTime\":\"2024-10-25T10:22:27.585+08:00\",\"ApplicationType\":\"\",\"UploadedFiles\":[],\"RemindSigner\":false,\"RemindMessageType\":0,\"RemindMessage\":null}";

            string creatorId = "0395";

            B1CardApp b1CardApp = JsonConvert.DeserializeObject<B1CardApp>(b1CardAppStr);
            var b1CardAppCheckResult = await _b1CardAppBo.AddB1CardApp(creatorId, b1CardApp, "127.0.0.1", "localhost");
            Assert.True(b1CardAppCheckResult.IsValid);

            Form form = _formBo.GetForm((Guid)b1CardAppCheckResult.FormUID);
            FormCard formCard = _b1CardAppBo.GetFormCard(form, "0395");
            Guid flowUID = formCard.Flows[0].FlowUID;

            Approve approve = new Approve();
            approve.ApproverDeptSName = "企劃處";
            approve.ApproveIP = "127.0.0.1";
            approve.ApproverName = "test";
            approve.ApproverEmpNo = "0391";
            approve.FlowUID = flowUID;
            approve.FormID = form.FormID;
            approve.FormUID = form.FormUID;
            approve.ApproveTime = DateTime.Now;
            approve.FlowStatus = (int)FlowStatus.Agree;
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo(approve.ApproverEmpNo);
            _controller.Approve(approve);
            _controller.Approve(approve);
        }

        [Fact]
        public void CanCallGetSentFormCard()
        {
            // Arrange
            var formUID = new Guid("8bd4648e-c67a-428b-a9c0-a3f9f67c0eec");
            var empNo = "0395";
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo(empNo);

            // Act
            var result = _controller.GetSentFormCard(formUID, empNo);

            // Assert
            string expect = "[]";
            Assert.Equal(expect, result);
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("   ")]
        public void CannotCallGetSentFormCardWithInvalidEmpNo(string empNo)
        {
            string expect = "[]";
            var result = _controller.GetSentFormCard(new Guid("7a7c5fda-e55e-48a5-8816-430b53b8b786"), empNo);
            Assert.Equal(expect, result);
        }

        [Fact]
        public async Task ClosedWithdrawTest()
        {
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo("0395");
            string b1CardAppStr = "{\"B1_ADate\":null,\"B1_Code\":\"1\",\"B1_Date\":\"2022-01-04T00:00:00+08:00\",\"B1_DateTypeId\":1,\"B1_DeptNo\":4,\"B1_EmpNo\":\"0395\",\"B1_FormID\":null,\"B1_Hour\":2,\"B1_IsOverdue\":false,\"B1_OverdueReason\":\"\",\"B1_PaidHour\":0,\"B1_PrjNo\":\"RP19553\",\"B1_Reason\":\"test\",\"B1_ShouldSignEmpNo\":null,\"B1_SOURCE\":\"Attendance\",\"B1_Status\":1,\"B1_UDate\":null,\"B1_UpdatedEmpNo\":null,\"B1_WDate\":\"2024-10-25T10:22:59.621+08:00\",\"B1_WritedEmpNo\":\"0395\",\"FormUID\":\"00000000-0000-0000-0000-000000000000\",\"ID\":null,\"Name\":\"B1CardApp\",\"RequisitionID\":null,\"UpdatedEmpNo\":null,\"UpdatedName\":null,\"UpdatedTime\":null,\"UpdatedIP\":null,\"UpdatedHost\":null,\"AddSigners\":\"\",\"CreatedTime\":\"2024-10-25T10:22:59.621+08:00\",\"FilledTime\":\"2024-10-25T10:22:27.585+08:00\",\"ApplicationType\":\"\",\"UploadedFiles\":[],\"RemindSigner\":false,\"RemindMessageType\":0,\"RemindMessage\":null}";

            string creatorId = "0395";

            B1CardApp b1CardApp = JsonConvert.DeserializeObject<B1CardApp>(b1CardAppStr);
            var b1CardAppCheckResult = await _b1CardAppBo.AddB1CardApp(creatorId, b1CardApp, "127.0.0.1", "localhost");
            Assert.True(b1CardAppCheckResult.IsValid);

            Form form = _formBo.GetForm((Guid)b1CardAppCheckResult.FormUID);
            FormCard formCard = _b1CardAppBo.GetFormCard(form, "0395");
            Guid flowUID = formCard.Flows[0].FlowUID;

            Approve approve = new Approve();
            approve.ApproverDeptSName = "企劃處";
            approve.ApproveIP = "127.0.0.1";
            approve.ApproverName = "test";
            approve.ApproverEmpNo = "0391";
            approve.FlowUID = flowUID;
            approve.FormID = form.FormID;
            approve.FormUID = form.FormUID;
            approve.ApproveTime = DateTime.Now;
            approve.FlowStatus = (int)FlowStatus.Agree;

            _formBo.Approve(approve, _b1CardAppBo, "0391");

            Withdraw withdraw = new Withdraw();
            withdraw.FormUID = form.FormUID;
            withdraw.FormID = form.FormID;
            withdraw.WithdrawEmpNo = "0395";
            withdraw.WithdrawName = "曾騰毅";
            withdraw.WithdrawIP = "127.0.0.1";
            withdraw.WithdrawHost = "localhost";
            withdraw.WithdrawTime = DateTime.Now;
            string errorMessage = _controller.Withdraw(withdraw);
            Assert.NotEmpty(errorMessage);
            errorMessage = _controller.ClosedWithdraw(withdraw);
            Assert.Empty(errorMessage);
        }

        [Fact]
        public void DeliveredNotificationsTest()
        {
            DeliveredNotificationsRequest request = new DeliveredNotificationsRequest();
            request.Id = 0;
            var result = _controller.DeliveredNotifications(request);
            Assert.IsType<OkObjectResult>(result);
        }

        [Fact]
        public void DownloadAttachment_ReturnsFileContentResult_WhenAttachmentExists()
        {
            // Arrange
            var formUID = Guid.NewGuid();
            var id = 1;
            var fakeAttendanceBo = A.Fake<AttendanceBo>();
            var fakeCardBoFactory = A.Fake<CardBoFactory>();
            var fakeFileExtensionContentTypeProvider = A.Fake<FileExtensionContentTypeProvider>();
            var fakeLogger = A.Fake<ILogger<FormController>>();
            var controller = new FormController(fakeAttendanceBo, _formBo, fakeCardBoFactory, _configuration, fakeLogger, fakeFileExtensionContentTypeProvider);

            // Act
            var result = controller.DownloadAttachment(formUID, id);

            // Assert
            var fileResult = Assert.IsType<FileContentResult>(result);
            string fileDownloadName = "找不到檔案.txt";
            Assert.Equal(fileDownloadName, fileResult.FileDownloadName);

        }

        [Fact]
        public void DownloadAttachment_ReturnsNotFound_WhenAttachmentDoesNotExist()
        {
            // Arrange
            var formUID = Guid.NewGuid();
            var id = 1;
            var fakeAttendanceBo = A.Fake<AttendanceBo>();
            var fakeCardBoFactory = A.Fake<CardBoFactory>();
            var fakeFileExtensionContentTypeProvider = A.Fake<FileExtensionContentTypeProvider>();

            var fakeLogger = A.Fake<ILogger<FormController>>();
            var controller = new FormController(fakeAttendanceBo, _formBo, fakeCardBoFactory, _configuration, fakeLogger, fakeFileExtensionContentTypeProvider);

            // Act
            var result = controller.DownloadAttachment(formUID, id);

            // Assert
            Assert.IsType<FileContentResult>(result);
        }

        [Fact]
        public void GetSentBoxPeriodByContentDate_ValidRequest_ReturnsExpectedResult()
        {
            // Arrange
            var httpContext = A.Fake<HttpContext>();
            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = httpContext
            };

            var request = new GetFormCardsRequest
            {
                EmpNo = "0349",
                StartDate = new DateTime(2022, 7, 1),
                EndDate = new DateTime(2022, 7, 31),
                ProjNo = "P001",
                Status = 1
            };

            // Act
            var result = _controller.GetSentBoxPeriodByContentDate(request);

            // Assert
            Assert.Equal("[]", result);
        }

        [Fact]
        public void GetSentBoxPeriodByContentDate_InvalidDateRange_ReturnsEmptyArray()
        {
            // Arrange
            var formBo = A.Fake<FormBo>();
            var httpContext = A.Fake<HttpContext>();

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = httpContext
            };

            var request = new GetFormCardsRequest
            {
                EmpNo = "0349",
                StartDate = new DateTime(2022, 7, 31),
                EndDate = new DateTime(2022, 7, 1),
                ProjNo = "P001",
                Status = 1
            };


            // Act
            var result = _controller.GetSentBoxPeriodByContentDate(request);

            // Assert
            Assert.Equal("[]", result);
        }

        [Fact]
        public void GetSentBoxPeriodByContentDate_UserWithoutPermission_ReturnsEmptyArray()
        {
            // Arrange
            var formBo = A.Fake<FormBo>();
            var httpContext = A.Fake<HttpContext>();

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = httpContext
            };

            var request = new GetFormCardsRequest
            {
                EmpNo = "0349",
                StartDate = new DateTime(2022, 7, 1),
                EndDate = new DateTime(2022, 7, 31),
                ProjNo = "P001",
                Status = 1
            };

            // Act
            var result = _controller.GetSentBoxPeriodByContentDate(request);

            // Assert
            Assert.Equal("[]", result);
        }

        [Theory]
        [InlineData("0349", false)]
        [InlineData("0395", false)]
        [InlineData("2268", false)]
        [InlineData("2295", true)]
        [InlineData("0274", false)]
        public void GetApprovalFormCardsTest(string empNo, bool isEmpty)
        {
            Guid guid = new Guid("*************-7777-7777-************");
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo(empNo);
            string json = _controller.GetApprovalFormCard(guid, empNo);
            Assert.Equal("[]", json);
            AddA1Card();
            json = _controller.GetApprovalFormCard(guid, empNo);
            if (isEmpty)
            {
                Assert.Equal("[]", json);
            }
            else
            {
                Assert.NotEmpty(json);
            }
        }

        [Theory]
        [InlineData(4, "0000", "2022-07-01", "2022-07-31")]
        [InlineData(1, "0395", "2022-07-01", "2022-07-31")]
        [InlineData(2, "0395", "2022-07-01", "2022-07-31")]
        [InlineData(3, "0395", "2022-07-01", "2022-07-31")]
        [InlineData(4, "0395", "2022-07-01", "2022-07-31")]
        [InlineData(5, "0395", "2022-07-01", "2022-07-31")]
        [InlineData(12, "0395", "2022-07-01", "2022-07-31")]
        public void GetDepartmentSentBoxByContentDateTest(int deptNo, string empNo, DateTime startDate, DateTime endDate)
        {
            // Arrange
            var fakeAttendanceBo = A.Fake<AttendanceBo>();
            var fakeCardBoFactory = A.Fake<CardBoFactory>();
            var fakeFileExtensionContentTypeProvider = A.Fake<FileExtensionContentTypeProvider>();

            var fakeLogger = A.Fake<ILogger<FormController>>();
            var controller = new FormController(fakeAttendanceBo, _formBo, fakeCardBoFactory, _configuration, fakeLogger, fakeFileExtensionContentTypeProvider);
            GetFormCardsRequest request = new GetFormCardsRequest();
            request.DeptNo = deptNo;
            request.StartDate = startDate;
            request.EndDate = endDate;
            request.EmpNo = empNo;
            string result = controller.GetDepartmentSentBoxByContentDate(request);
            string emptyJson = "[]";
            Assert.Equal(emptyJson, result);
        }

        [Fact]
        public void GetDepartmentSentBoxPeriodTest()
        {
            int deptNo = 4;
            string empNo = "0349";
            DateTime startDate = new DateTime(2023, 4, 1, 0, 0, 0, DateTimeKind.Local);
            DateTime endDate = new DateTime(2023, 4, 30, 0, 0, 0, DateTimeKind.Local);
            GetFormCardsRequest request = new GetFormCardsRequest();
            request.EmpNo = empNo;
            request.DeptNo = deptNo;
            request.StartDate = startDate;
            request.EndDate = endDate;
            string result = _controller.GetDepartmentSentBoxPeriod(request);
            Assert.NotEmpty(result);
            Assert.Equal("[]", result);
        }

        [Fact()]
        public void GetFormsByContentDateTest()
        {
            int deptNo = 4;
            string empNo = "0349";
            DateTime startDate = new DateTime(2023, 4, 1, 0, 0, 0, DateTimeKind.Local);
            DateTime endDate = new DateTime(2023, 4, 30, 0, 0, 0, DateTimeKind.Local);
            GetFormCardsRequest request = new GetFormCardsRequest();
            request.EmpNo = empNo;
            request.DeptNo = deptNo;
            request.StartDate = startDate;
            request.EndDate = endDate;
            string result = _controller.GetFormsByContentDate(request);
            Assert.NotEmpty(result);
            Assert.Equal("[]", result);
        }

        [Theory]
        [InlineData("0349", "[]")]
        [InlineData("0395", "[]")]
        [InlineData("2130", "[]")]
        [InlineData("2259", "[]")]
        [InlineData("2268", "[]")]
        [InlineData("2295", "[]")]
        public void GetFormsTest(string empNo, string expected)
        {
            int deptNo = 4;
            DateTime startDate = new DateTime(2023, 4, 1, 0, 0, 0, DateTimeKind.Local);
            DateTime endDate = new DateTime(2023, 4, 30, 0, 0, 0, DateTimeKind.Local);
            GetFormCardsRequest request = new GetFormCardsRequest();
            request.EmpNo = empNo;
            request.DeptNo = deptNo;
            request.StartDate = startDate;
            request.EndDate = endDate;
            string result = _controller.GetForms(request);
            Assert.NotEmpty(result);
            Assert.Equal(expected, result);
        }

        [Theory]
        [InlineData("0349", "[]")]
        [InlineData("0395", "[]")]
        [InlineData("2130", "[]")]
        [InlineData("2259", "[]")]
        [InlineData("2268", "[]")]
        public void GetInbox_EmpNo_Empty_Test(string empNo, string expected)
        {
            string inbox = _controller.GetInbox(empNo);
            Assert.Equal(expected, inbox);
        }

        [Theory]
        [InlineData("0349")]
        [InlineData("0395")]
        [InlineData("2130")]
        [InlineData("2259")]
        [InlineData("2268")]
        public void GetInbox_EmpNo_Test(string empNo)
        {
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo(empNo);
            string strInbox = _controller.GetInbox(empNo);
            List<RoleInbox> list = JsonConvert.DeserializeObject<List<RoleInbox>>(strInbox);
            Assert.NotEmpty(list);
            RoleInbox roleInbox = list[0];
            Assert.Empty(roleInbox.Inboxes);
        }

        [Theory]
        [InlineData("0395", 1, 0)]
        [InlineData("0395", 2, 0)]
        [InlineData("0395", 3, 0)]
        [InlineData("0395", 4, 0)]
        [InlineData("2130", 1, 0)]
        [InlineData("2130", 2, 0)]
        [InlineData("2130", 3, 0)]
        [InlineData("2130", 4, 0)]
        [InlineData("2069", 1, 0)]
        [InlineData("2069", 2, 1)]
        [InlineData("2069", 3, 0)]
        [InlineData("2069", 4, 0)]
        public void GetMonthSentCardsTest(string empNo, int status, int expected)
        {
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo(empNo);
            AddA1CardSimple();
            DateTime date = new DateTime(2022, 4, 1, 0, 0, 0, DateTimeKind.Local);
            string retStr = _controller.GetMonthSentCards(date, empNo, status);
            var result = JsonConvert.DeserializeObject<List<object>>(retStr);
            int actual = result.Count;
            Assert.Equal(expected, actual);
        }

        [Theory]
        [InlineData("2130", "[]")]
        [InlineData("0391", "[]")]
        [InlineData("0274", "[]")]
        [InlineData("0395", "[]")]
        [InlineData("2268", "[]")]
        public void GetNotificationsTest(string empNo, string expected)
        {
            GetNotifyFormCardsRequest request = new GetNotifyFormCardsRequest();

            string json = _controller.GetNotifyFormCards(request);
            Assert.Equal(expected, json);
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo(empNo);
            request.StartDate = DateTime.Now;
            request.EndDate = DateTime.Now;
            request.EmpNo = empNo;
            request.UserId = empNo;
            request.DeptNo = 0;
            request.Status = [];
            json = _controller.GetNotifyFormCards(request);
            Assert.Equal(expected, json);
        }

        [Fact]
        public void GetNotifyFormCardTest()
        {
            Guid guid = new Guid("*************-2222-2222-************");
            string json = _controller.GetNotifyFormCard(guid, 1, "2268");
            Assert.Equal("[]", json);
        }


        [Fact]
        public void GetSentBoxPeriodTest()
        {
            DateTime startDate = new DateTime(2022, 9, 1, 0, 0, 0, DateTimeKind.Local);
            DateTime endDate = new DateTime(2022, 9, 30, 0, 0, 0, DateTimeKind.Local);
            GetFormCardsRequest request = new GetFormCardsRequest();
            request.StartDate = startDate;
            request.EndDate = endDate;
            request.EmpNo = "0349";
            string sentBox = _controller.GetSentBoxPeriod(request);
            Assert.Equal("[]", sentBox);
        }

        [Fact]
        public void GetSignedFormsPeriodTest()
        {
            AddA1CardSimple();
            string empNo = "2130";
            DateTime startDate = new DateTime(2022, 7, 25, 0, 0, 0, DateTimeKind.Local);
            DateTime endDate = new DateTime(2022, 7, 27, 0, 0, 0, DateTimeKind.Local);
            FormCardsYearMonthRequest input = new FormCardsYearMonthRequest();
            input.EmpNo = empNo;
            input.StartDate = startDate;
            input.EndDate = endDate;
            string json = _controller.GetSignedFormsPeriod(input);
            List<SignedForm> signedForms = JsonConvert.DeserializeObject<List<SignedForm>>(json);
            Assert.Empty(signedForms);
        }

        [Fact]
        public void MultipleApproveTest()
        {
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo("0395");
            List<Approve> list = new List<Approve>();
            string result = _controller.MultipleApprove(list);
            Assert.Equal("[]", result);
            result = _controller.MultipleApprove(list);
            Assert.Equal("[\"重覆送出群簽\"]", result);
        }


        [Fact]
        public void WithdrawTest()
        {
            AddA1CardSimple();

            Withdraw withdraw = new Withdraw();
            withdraw.FormUID = new Guid("*************-2222-2222-************");
            withdraw.FormID = "A1Card";
            withdraw.WithdrawEmpNo = "2130";
            withdraw.WithdrawName = "岳美熹";
            withdraw.WithdrawIP = "**********";
            withdraw.WithdrawHost = "03-333";
            withdraw.WithdrawTime = DateTime.Parse("2022-07-28 10:44:44+08:00");
            Form form = _formBo.GetForm(withdraw.FormUID);
            Assert.Equal((int)FormStatus.Agree, form.FormStatus);
            string errorMessage = _controller.Withdraw(withdraw);
            Assert.NotEmpty(errorMessage);

            AddA1Card();
            withdraw = new Withdraw();
            withdraw.FormUID = new Guid("*************-7777-7777-************");
            withdraw.FormID = "A1Card";
            withdraw.WithdrawEmpNo = "0349";
            withdraw.WithdrawName = "白燕菁";
            withdraw.WithdrawIP = "**********";
            withdraw.WithdrawHost = "03-388";
            withdraw.WithdrawTime = DateTime.Parse("2022-07-28 10:44:44+08:00");
            form = _formBo.GetForm(withdraw.FormUID);
            Assert.Equal((int)FormStatus.Processing, form.FormStatus);
            errorMessage = _controller.Withdraw(withdraw);
            Assert.Empty(errorMessage);
            form = _formBo.GetForm(withdraw.FormUID);
            Assert.Equal((int)FormStatus.Withdraw, form.FormStatus);
            ICardBaseBo cardBo = _cardBoFactory.GetCardBo(form.FormID);
            CardBase card = cardBo.GetCard(form.FormUID);

            A1Card a1Card = (A1Card)card;
            Assert.Equal((int)FormStatus.Withdraw, a1Card.A1_STATUS);

        }

        [Fact]
        public void GetSignedFormCardTest()
        {
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo("0395");
            Guid formUID = Guid.NewGuid();
            string result = _controller.GetSignedFormCard(formUID, "0395");
            Assert.Equal("[]", result);
        }

        [Theory]
        [InlineData("0395", 2022, 0, 0, "2022-01-01", "2022-01-31")]
        [InlineData("0395", 2022, 10, 0, "2022-11-01", "2022-11-30")]
        [InlineData("0395", 2022, 10, 3, "2022-11-03", "2022-11-30")]
        public void GetSignedFormsByYearMonthTest(string empNo, int year, int month, int tenDays, DateTime startDate, DateTime endDate)
        {
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo(empNo);
            Guid formUID = Guid.NewGuid();
            FormCardsYearMonthRequest input = new FormCardsYearMonthRequest();
            input.EmpNo = empNo;
            input.Year = year;
            input.Month = month;
            input.TenDays = tenDays;
            input.StartDate = startDate;
            input.EndDate = endDate;

            string result = _controller.GetSignedFormsByYearMonth(input);
            Assert.Equal("[]", result);
        }

        [Theory]
        [InlineData("0395", 2022, 0, 0)]
        [InlineData("0395", 2022, 10, 0)]
        [InlineData("0395", 2022, 10, 3)]
        public void GetSentBoxPeriodByContentYearMonthTest(string empNo, int year, int month, int tenDays)
        {
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo(empNo);
            Guid formUID = Guid.NewGuid();
            FormCardsYearMonthRequest request = new FormCardsYearMonthRequest();
            request.Year = year;
            request.Month = month;
            request.TenDays = tenDays;
            request.EmpNo = empNo;
            string result = _controller.GetSentBoxPeriodByContentYearMonth(request);
            Assert.Equal("[]", result);
        }

        [Theory]
        [InlineData("0395", 2022, 0, 0)]
        [InlineData("0395", 2022, 10, 0)]
        [InlineData("0395", 2022, 10, 3)]
        public void GetDepartmentSentBoxByYearMonthTest(string empNo, int year, int month, int tenDays)
        {
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo(empNo);
            Guid formUID = Guid.NewGuid();
            FormCardsYearMonthRequest request = new FormCardsYearMonthRequest();
            request.Year = year;
            request.Month = month;
            request.TenDays = tenDays;
            request.EmpNo = empNo;
            string result = _controller.GetDepartmentSentBoxByYearMonth(request);
            Assert.Equal("[]", result);
        }

        [Theory]
        [InlineData("0395", 2022, 0, 0)]
        [InlineData("0395", 2022, 10, 0)]
        [InlineData("0395", 2022, 10, 3)]
        public void GetFormsByContentYearMonthTest(string empNo, int year, int month, int tenDays)
        {
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo(empNo);
            Guid formUID = Guid.NewGuid();
            FormCardsYearMonthRequest request = new FormCardsYearMonthRequest();
            request.Year = year;
            request.Month = month;
            request.TenDays = tenDays;
            request.EmpNo = empNo;
            string result = _controller.GetFormsByContentYearMonth(request);
            Assert.Equal("[]", result);
        }
    }
}
