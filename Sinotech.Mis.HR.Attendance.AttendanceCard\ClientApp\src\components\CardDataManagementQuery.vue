<template>
  <router-link
    v-slot="{ navigate }"
    :to="{ name: 'DataManagement' }"
    custom
  >
    <button
      type="button"
      class="btn btn-outline-dark bi bi-box-arrow-left mt-2 mx-2"
      :disabled="submitted === true"
      @click="navigate"
    >
      <span>三卡管理</span>
    </button>
  </router-link>
  <template v-if="modelValue?.formStatus === 1">
    <button
      type="button"
      :class="[
        'btn mt-2 mx-2',
        submitted === true ? 'btn-outline-danger' : 'btn-danger'
      ]"
      :disabled="submitted === true"
      @click="onWithdraw"
    >
      <span>抽單</span>
    </button>
  </template>
  <template v-else-if="modelValue?.formStatus === 2">
    <button
      type="button"
      :class="[
        'btn mt-2 mx-2',
        submitted === true ? 'btn-outline-danger' : 'btn-danger'
      ]"
      :disabled="submitted === true"
      @click="onClosedCardWithdraw"
    >
      <span>結案抽單</span>
    </button>
  </template>
  <h6 class="my-2 fs-2 text-center">
    <slot name="header" />
  </h6>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'
import { REFRESH_PAGE_TOAST_TIME, REFRESH_PAGE_TIME, SYSTEM_ERROR_MESSAGE } from '../api/appConst'
import { useAuthUserStore } from '../store/index'
import { useCardStore } from '../store/card'
import { useAlarmDialogStore } from '../store/alarmDialog'
import { useUiStore } from '../store/ui'
import { routerExtend } from '../router'
import { useFormFlow } from '../composable/formFlow'
import { useAbortController } from '../composable/abortController'
import type { PropType } from 'vue'
import type { CardStoreType, FormFlowType, CardApiType, FormFlowApiType } from '../api/appType'

const userStore = useAuthUserStore()
const { flowRes, onFlowWithdraw, onFlowClosedWithdraw, onFlowWithdrawError } = useFormFlow()
const { abortController, fetchErrorHandler, abortListener } = useAbortController()

const submitted = ref<boolean>(false)

const confirm = useConfirm()
const toast = useToast()

const props = defineProps({
  modelValue: {
    type: Object as PropType<CardStoreType & FormFlowType>,
    default: () => {}
  }
})

const onWithdraw = () => {
  let checkSubmitted: number = 0
  submitted.value = true
  confirm.require({
    group: 'app',
    header: '提醒',
    message: '確認抽單？',
    acceptProps: {
      severity: 'primary'
    },
    rejectProps: {
      severity: 'secondary'
    },
    accept: (): void => {
      checkSubmitted++
      if (checkSubmitted === 1) {
        onFlowWithdraw(userStore, props.modelValue, abortController.signal).then(() => {
          if (flowRes.value.length === 0) {
            toast.add({
              severity: 'success',
              summary: '已抽單',
              life: REFRESH_PAGE_TOAST_TIME,
              group: 'app'
            })

            useUiStore().toggle(true)
            flowRes.value = ''

            setTimeout(() => {
              routerExtend.pushHandler('DataManagement')
            }, REFRESH_PAGE_TIME)
          } else {
            submitted.value = false
            onFlowWithdrawErrorHandler()
          }
        }).catch((err: Error) => {
          console.error(err)
          submitted.value = false
          fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
        })
      }
    },
    reject: (): void => {
      submitted.value = false
    }
  })
}

const onClosedCardWithdraw = () => {
  let checkSubmitted: number = 0
  submitted.value = true
  confirm.require({
    group: 'app',
    header: '提醒',
    message: '本卡已結案，仍確認抽單？',
    acceptProps: {
      severity: 'primary'
    },
    rejectProps: {
      severity: 'secondary'
    },
    accept: (): void => {
      checkSubmitted++
      if (checkSubmitted === 1) {
        onFlowClosedWithdraw(userStore, props.modelValue, abortController.signal).then(() => {
          if (flowRes.value.length === 0) {
            toast.add({
              severity: 'success',
              summary: '已抽單',
              life: REFRESH_PAGE_TOAST_TIME,
              group: 'app'
            })

            useUiStore().toggle(true)
            flowRes.value = ''

            setTimeout(() => {
              routerExtend.pushHandler('DataManagement')
            }, REFRESH_PAGE_TIME)
          } else {
            submitted.value = false
            onFlowWithdrawErrorHandler()
          }
        }).catch((err: Error) => {
          console.error(err)
          submitted.value = false
          fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
        })
      }
    },
    reject: (): void => {
      submitted.value = false
    }
  })
}

/**
 * 如果管理者的抽單發生錯誤，先嘗試重讀表單資料，仍讀不到資料的話，就導向'DataManagement'頁面
 */
 const onFlowWithdrawErrorHandler = (): void => {
  onFlowWithdrawError(userStore, props.modelValue, abortController.signal).then((res: CardApiType & FormFlowApiType) => {
    useCardStore().setData(res)
    useAlarmDialogStore().setMessage(flowRes.value)
    useAlarmDialogStore().setVisible(true)

    if ((res.FormUID as string).length > 0) {
      flowRes.value = ''
    } else {
      useAlarmDialogStore().setTargetRoute('DataManagement')
    }
  }).catch((err: Error) => {
    throw err
  })
}

onMounted((): void => {
  abortListener()
})
</script>