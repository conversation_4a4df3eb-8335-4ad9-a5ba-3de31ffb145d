<template>
  <table class="table table-sm table-bordered caption-top text-center mb-0">
    <caption>
      <span>加班時數統計(統計時間：</span>
      <span>{{ dateToRocString(modelValue?.overtimeDateStatic) }})</span>
    </caption>
    <thead class="table-light">
      <tr>
        <th />
        <th>已同意</th>
        <th>簽核中</th>
        <template v-if="modelValue?.card === FORM_ID.B1CardApp">
          <th>合計</th>
        </template>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>
          <span>{{ modelValue?.overtimeData?.monthOvertimeStatics?.MonthName }}</span>
          <span v-if="modelValue?.currentInfoNote === true">(當月)</span>
        </td>
        <td>{{ modelValue?.overtimeData?.monthOvertimeStatics?.ApprovedHours }}</td>
        <td>{{ modelValue?.overtimeData?.monthOvertimeStatics?.UnderApprovalHours }}</td>
        <td v-if="modelValue?.card === FORM_ID.B1CardApp">
          {{ modelValue?.overtimeData?.monthOvertimeStatics?.TotalHours }}
        </td>
      </tr>
      <tr>
        <td>
          <span>{{ modelValue?.overtimeData?.quarterlyOvertimeStatics?.QuarterName }}</span>
          <span v-if="modelValue?.currentInfoNote === true">(當季)</span>
        </td>
        <td>{{ modelValue?.overtimeData?.quarterlyOvertimeStatics?.ApprovedHours }}</td>
        <td>{{ modelValue?.overtimeData?.quarterlyOvertimeStatics?.UnderApprovalHours }}</td>
        <td v-if="modelValue?.card === FORM_ID.B1CardApp">
          {{ modelValue?.overtimeData?.quarterlyOvertimeStatics?.TotalHours }}
        </td>
      </tr>
    </tbody>
  </table>
</template>
<script setup lang="ts">
import { FORM_ID } from '../api/appConst'
import { dateToRocString } from '../api/appFunction'

defineProps<{
  modelValue: {
    card: string
    overtimeDateStatic: Date
    overtimeData: any
    currentInfoNote: boolean
  }
}>()
</script>