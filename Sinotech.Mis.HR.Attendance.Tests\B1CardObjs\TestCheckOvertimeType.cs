using Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.PositionType;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.Tests
{
    [ExcludeFromCodeCoverage]
    public class TestCheckOvertimeType : TestB1CardBase
    {
        [Theory]
        [InlineData(WorkdayType.SundayRegularHoliday, 0)]
        [InlineData(WorkdayType.SundayHoliday, 0)]
        [InlineData(WorkdayType.WeekNaturalDisasterDay, 0)]
        [InlineData(WorkdayType.SaturdayNaturalDisasterDay, 0)]
        [InlineData(WorkdayType.WeekWorkday, 0)]
        [InlineData(WorkdayType.SaturdayRestday, 0)]
        [InlineData(WorkdayType.WeekHoliday, 511)]
        [InlineData(WorkdayType.SaturdayHoliday, 0)]
        [InlineData(WorkdayType.MakeUpHoliday, 511)]
        [InlineData(WorkdayType.MakeUpWorkday, 0)]
        [InlineData(WorkdayType.FlexbleHoliday, 0)]
        [InlineData(WorkdayType.WeekRestday, 0)]
        public void CheckCompensatoryLeave(WorkdayType type, int code)
        {
            var provider = GetFakeObject(B1CardPositionEnum.GeneralStaff);
            var p = B1CardPositionFactory.GetB1CardPositionObject(provider);

            _workday.DayType = type;

            _b1Card.Details.Clear();
            _b1Card.Details.Add(
                new B1CardDetail()
                {
                    B1_CODE = 1,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now,
                    EndTime = _now + TimeSpan.FromHours(1),
                    Hour = 1,
                    Project = _project1,
                }
            );

            _b1Card.Details.Add(
                new B1CardDetail()
                {
                    B1_CODE = 3,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now + TimeSpan.FromHours(1),
                    EndTime = _now + TimeSpan.FromHours(6),
                    Hour = 5,
                    Project = _project1,
                }
            );

            _b1Card.Details.Add(
                new B1CardDetail()
                {
                    B1_CODE = 3,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now + TimeSpan.FromHours(6),
                    EndTime = _now + TimeSpan.FromHours(7),
                    Hour = 1,
                    Project = _project1,
                }
            );

            _b1Card.TotalHours = 7;

            var result = p.CheckOvertimeType();
            Assert.Equal(code, result.Code);
        }

        [Theory]
        [InlineData(WorkdayType.SundayRegularHoliday, 0)]
        [InlineData(WorkdayType.SundayHoliday, 0)]
        [InlineData(WorkdayType.WeekNaturalDisasterDay, 0)]
        [InlineData(WorkdayType.SaturdayNaturalDisasterDay, 0)]
        [InlineData(WorkdayType.WeekWorkday, 0)]
        [InlineData(WorkdayType.SaturdayRestday, 0)]
        [InlineData(WorkdayType.WeekHoliday, 0)]
        [InlineData(WorkdayType.SaturdayHoliday, 0)]
        [InlineData(WorkdayType.MakeUpHoliday, 0)]
        [InlineData(WorkdayType.MakeUpWorkday, 0)]
        [InlineData(WorkdayType.FlexbleHoliday, 0)]
        [InlineData(WorkdayType.WeekRestday, 0)]
        public void CheckCompensatoryLeave2(WorkdayType type, int code)
        {
            var provider = GetFakeObject(B1CardPositionEnum.GeneralStaff);
            var p = B1CardPositionFactory.GetB1CardPositionObject(provider);

            _workday.DayType = type;

            _b1Card.Details.Clear();
            _b1Card.Details.Add(
                new B1CardDetail()
                {
                    B1_CODE = 1,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now,
                    EndTime = _now + TimeSpan.FromHours(8),
                    Hour = 8,
                    Project = _project1,
                }
            );

            _b1Card.Details.Add(
                new B1CardDetail()
                {
                    B1_CODE = 3,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now + TimeSpan.FromHours(8),
                    EndTime = _now + TimeSpan.FromHours(11),
                    Hour = 3,
                    Project = _project1,
                }
            );

            _b1Card.TotalHours = 11;

            var result = p.CheckOvertimeType();
            Assert.Equal(code, result.Code);
        }
    }
}