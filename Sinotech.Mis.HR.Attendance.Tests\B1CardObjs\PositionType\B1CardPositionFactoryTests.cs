﻿using Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.PositionType;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using Xunit;
using Moq;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.Tests
{
    public class B1CardPositionFactoryTests
    {
        private readonly Mock<IB1CardDataProvider> _mockProvider;

        public B1CardPositionFactoryTests()
        {
            _mockProvider = new Mock<IB1CardDataProvider>();
        }

        [Fact]
        public void GetB1CardPositionObject_SpecialStaff_ReturnsSpecialStaff()
        {
            // Arrange
            _mockProvider.Setup(p => p.IsSpecialStaff()).Returns(true);
            _mockProvider.Setup(p => p.EmployeeNumber).Returns("1234");
            _mockProvider.Setup(p => p.OvertimeDate).Returns(DateTime.Now);

            // Act
            var result = B1CardPositionFactory.GetB1CardPositionObject(_mockProvider.Object);

            // Assert
            Assert.IsType<SpecialStaff>(result);
        }

        [Theory]
        [InlineData(B1CardPositionEnum.Chairman)]
        [InlineData(B1CardPositionEnum.President)]
        [InlineData(B1CardPositionEnum.VicePresident)]
        [InlineData(B1CardPositionEnum.Manager)]
        [InlineData(B1CardPositionEnum.DeputyManager)]
        [InlineData(B1CardPositionEnum.Director)]
        [InlineData(B1CardPositionEnum.DeputyDirector)]
        [InlineData(B1CardPositionEnum.ProjectDirector)]
        [InlineData(B1CardPositionEnum.ProjectDeputyDirector)]
        public void GetB1CardPositionObject_StaffOvertimeNotAllowed_ReturnsStaffOvertimeNotAllowed(B1CardPositionEnum position)
        {
            // Arrange
            _mockProvider.Setup(p => p.IsSpecialStaff()).Returns(false);
            _mockProvider.Setup(p => p.GetPositionType()).Returns(position);
            _mockProvider.Setup(p => p.EmployeeNumber).Returns("1234");
            _mockProvider.Setup(p => p.OvertimeDate).Returns(DateTime.Now);

            // Act
            var result = B1CardPositionFactory.GetB1CardPositionObject(_mockProvider.Object);

            // Assert
            Assert.IsType<StaffOvertimeNotAllowed>(result);
        }

        [Fact]
        public void GetB1CardPositionObject_Driver_ReturnsDriver()
        {
            // Arrange
            _mockProvider.Setup(p => p.IsSpecialStaff()).Returns(false);
            _mockProvider.Setup(p => p.GetPositionType()).Returns(B1CardPositionEnum.Driver);
            _mockProvider.Setup(p => p.EmployeeNumber).Returns("1234");
            _mockProvider.Setup(p => p.OvertimeDate).Returns(DateTime.Now);

            // Act
            var result = B1CardPositionFactory.GetB1CardPositionObject(_mockProvider.Object);

            // Assert
            Assert.IsType<Driver>(result);
        }

        [Theory]
        [InlineData(B1CardPositionEnum.ChiefAccountant)]
        [InlineData(B1CardPositionEnum.SectionChief)]
        [InlineData(B1CardPositionEnum.ProjectSectionChief)]
        [InlineData(B1CardPositionEnum.EnvLabChief)]
        [InlineData(B1CardPositionEnum.GeneralStaff)]
        public void GetB1CardPositionObject_GeneralStaff_ReturnsGeneralStaff(B1CardPositionEnum position)
        {
            // Arrange
            _mockProvider.Setup(p => p.IsSpecialStaff()).Returns(false);
            _mockProvider.Setup(p => p.GetPositionType()).Returns(position);
            _mockProvider.Setup(p => p.EmployeeNumber).Returns("1234");
            _mockProvider.Setup(p => p.OvertimeDate).Returns(DateTime.Now);

            // Act
            var result = B1CardPositionFactory.GetB1CardPositionObject(_mockProvider.Object);

            // Assert
            Assert.IsType<GeneralStaff>(result);
        }

        [Fact]
        public void GetB1CardPositionObject_NotInList_ThrowsArgumentException()
        {
            // Arrange
            _mockProvider.Setup(p => p.IsSpecialStaff()).Returns(false);
            _mockProvider.Setup(p => p.GetPositionType()).Returns(B1CardPositionEnum.NotInList);
            _mockProvider.Setup(p => p.EmployeeNumber).Returns("1234");
            _mockProvider.Setup(p => p.OvertimeDate).Returns(DateTime.Now);

            // Act & Assert
            var exception = Assert.Throws<ArgumentException>(() => B1CardPositionFactory.GetB1CardPositionObject(_mockProvider.Object));
            Assert.Equal("GetB1CardPositionObject: 找不到員工編號 1234 的資料!", exception.Message);
        }

        [Fact]
        public void GetB1CardPositionObject_UnsupportedPosition_ThrowsArgumentException()
        {
            // Arrange
            _mockProvider.Setup(p => p.IsSpecialStaff()).Returns(false);
            _mockProvider.Setup(p => p.GetPositionType()).Returns((B1CardPositionEnum)999);
            _mockProvider.Setup(p => p.EmployeeNumber).Returns("1234");
            _mockProvider.Setup(p => p.OvertimeDate).Returns(DateTime.Now);

            // Act & Assert
            var exception = Assert.Throws<ArgumentException>(() => B1CardPositionFactory.GetB1CardPositionObject(_mockProvider.Object));
            Assert.Equal("GetB1CardPositionObject: 員工 1234 不支援的職等 999!", exception.Message);
        }
    }
}
