<template>
  <h6 class="my-2 fs-2 text-center">
    <i class="bi bi-gear me-1" />
    <span>三卡管理</span>
  </h6>
  <div class="container px-0">
    <DataTable
      v-model:filters="filters"
      :value="cardData"
      :paginator="true"
      :alwaysShowPaginator="true"
      :first="queryPage"
      :rows="rows"
      :loading="cardDataLoading"
      :sortField="sortField"
      :sortOrder="sortOrder"
      class="border my-2"
      :paginatorTemplate="(queryTotalRecords > 0) ? 'FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport' : ''"
      :currentPageReportTemplate="(queryTotalRecords > 0) ? '{first}至{last}筆，共{totalRecords}筆' : ''"
      @update:sortField="onSortField"
      @update:sortOrder="onSortOrder($event as number)"
      @page="onChangePage"
      @filter="onFilter"
    >
      <template #header>
        <div
          id="accordionDiv"
          class="accordion"
        >
          <div class="accordion-item">
            <h2
              id="heading"
              class="accordion-header"
            >
              <button
                class="accordion-button"
                type="button"
                data-bs-toggle="collapse"
                data-bs-target="#collapseDiv"
                aria-expanded="true"
                aria-controls="collapseDiv"
              >
                <span>查詢條件</span>
              </button>
            </h2>
            <div
              id="collapseDiv"
              class="accordion-collapse collapse show"
              aria-labelledby="heading"
              data-bs-parent="#accordionDiv"
            >
              <div class="accordion-body py-1">
                <div class="row my-2">
                  <div class="col-3 col-md-2">
                    <span>卡別</span>
                  </div>
                  <div class="col-6 col-md-4">
                    <VueSelect
                      label="label"
                      :reduce="(e: CardOptionsType) => e.value"
                      :options="CARDS_OPTIONS"
                      :clearable="false"
                      :searchable="false"
                      :modelValue="formID"
                      @update:modelValue="onChangeFormID"
                    />
                  </div>
                </div>
                <template v-if="formID === 'A1Card'">
                  <div class="row my-2">
                    <div class="col-3 col-md-2">
                      <span>旬別</span>
                    </div>
                    <div class="col">
                      <div class="d-md-inline-block">
                        <span class="d-none d-md-inline-block me-1">民國</span>
                        <VueSelect
                          class="d-inline-block"
                          :options="ROC_YEARS_OPTIONS"
                          :clearable="false"
                          :searchable="false"
                          :modelValue="years"
                          @update:modelValue="onChangeYear"
                        />
                        <span class="mx-1">年</span>
                      </div>

                      <div class="d-md-inline-block">
                        <VueSelect
                          class="d-inline-block"
                          label="field"
                          :reduce="(e: MonthsOptionsType) => e.optionValue"
                          :options="MONTHS_OPTIONS"
                          :clearable="false"
                          :searchable="false"
                          :modelValue="months"
                          @update:modelValue="onChangeMonth"
                        />
                        <span class="mx-1">月</span>
                      </div>

                      <div class="d-md-inline-block">
                        <VueSelect
                          class="d-inline-block"
                          label="field"
                          :reduce="(e: TenDaysOptionsType) => e.optionValue"
                          :options="TEN_DAYS_OPTIONS"
                          :clearable="false"
                          :searchable="false"
                          :modelValue="tenDays"
                          :disabled="months.optionValue === 0"
                          @update:modelValue="onChangeTenDays"
                        />
                        <span class="ms-1">旬</span>
                      </div>
                    </div>
                  </div>
                </template>
                <template v-else-if="(formID === 'B1CardApp') || (formID === 'B1Card')">
                  <div class="row my-2">
                    <div class="col-3 col-md-2">
                      <span>加班別</span>
                    </div>
                    <div class="col-6 col-md-4">
                      <VueSelect
                        label="name"
                        :reduce="(e: OvertimeCompensatoryType) => e.id"
                        :options="overtimeCompensatory"
                        :clearable="false"
                        :searchable="false"
                        :modelValue="overtimeCompensatoryKind"
                        @update:modelValue="onChangeOvertimeCompensatory"
                      />
                    </div>
                  </div>
                </template>
                <template v-else-if="formID === 'C1Card'">
                  <div class="row my-2">
                    <div class="col-3 col-md-2">
                      <span>假別</span>
                    </div>
                    <div class="col-6 col-md-4">
                      <template v-if="leaveKindOptions.length > 0">
                        <VueSelect
                          label="name"
                          :reduce="(e: LeaveKindOptionsType) => e.number"
                          :options="leaveKindOptions"
                          :clearable="false"
                          :searchable="false"
                          :modelValue="leaveKind"
                          @update:modelValue="onChangeleaveKind"
                        />
                      </template>
                    </div>
                  </div>
                </template>
                <div class="row my-2">
                  <div class="col-3 col-md-2">
                    <span>部門</span>
                  </div>
                  <div class="col-6 col-md-4">
                    <VueSelect
                      label="deptSName"
                      :reduce="(e: DeptType) => e.deptNo"
                      :options="deptOptions"
                      :clearable="false"
                      :searchable="false"
                      :modelValue="dept"
                      @update:modelValue="onChangeDept"
                    />
                  </div>
                </div>
                <div class="row my-2">
                  <div class="col-3 col-md-2">
                    <span>申請人</span>
                  </div>
                  <div class="col-6 col-md-4">
                    <ChooseUser
                      :modelValue="{
                        userId: employee.userId,
                        userName: employee.userName,
                        deptNo: employee.deptNo,
                        deptSName: employee.deptSName
                      }"
                      :employeeData="dept.deptNo === -1 ? allEmployeeData : allEmployeeData.filter((employeeEle) => (employeeEle.deptNo === dept.deptNo))"
                      :filter="onSearchEmployeeData"
                      :clearable="employee.userId.length > 0"
                      @update:modelValue="onChangeEmployee"
                      @delete="onDeleteEmployee"
                    />
                  </div>
                </div>
                <div class="row my-2">
                  <div class="col-3 col-md-2">
                    <span>計畫編號</span>
                  </div>
                  <div class="col-6 col-md-4">
                    <input
                      v-model="projectNumber"
                      type="text"
                      class="form-control"
                      aria-label="projectNumber"
                    >
                  </div>
                </div>
                <div
                  v-if="formID !== 'A1Card'"
                  class="row my-2"
                >
                  <div class="col-3 col-md-2">
                    <span>日期範圍</span>
                  </div>
                  <div class="col-6 col-md-9">
                    <div class="row my-2">
                      <div class="col-12">
                        <div class="form-check form-check-inline">
                          <input
                            id="contentTime"
                            v-model="queryTimeType"
                            type="radio"
                            class="form-check-input"
                            value="1"
                          >
                          <label class="form-check-label" for="contentTime">
                            申請日期
                          </label>
                        </div>

                        <div class="form-check form-check-inline">
                          <input
                            id="createdTime"
                            v-model="queryTimeType"
                            type="radio"
                            class="form-check-input"
                            value="2"
                          >
                          <label
                            class="form-check-label"
                            for="createdTime"
                          >
                            填報日期
                          </label>
                        </div>
                      </div>

                      <div class="col-12">
                        <div class="row">
                          <div class="col-auto py-1">
                            <template v-if="queryTimeType === '1'">
                              <RocCalendarSelect
                                v-model="contentStartTime"
                                :inputClass="{
                                  'form-control': true
                                }"
                              />
                            </template>
                            <template v-else-if="queryTimeType === '2'">
                              <RocCalendarSelect
                                v-model="startTime"
                                :inputClass="{
                                  'form-control': true
                                }"
                              />
                            </template>
                          </div>
                          <div class="col-auto d-flex align-items-center">
                            <span>至</span>
                          </div>
                          <div class="col-auto py-1">
                            <template v-if="queryTimeType === '1'">
                              <RocCalendarSelect
                                v-model="contentEndTime"
                                :inputClass="{
                                  'form-control': true
                                }"
                              />
                            </template>
                            <template v-if="queryTimeType === '2'">
                              <RocCalendarSelect
                                v-model="endTime"
                                :inputClass="{
                                  'form-control': true
                                }"
                              />
                            </template>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="row my-2">
                  <div class="col-3 col-md-2">
                    <span>表單狀態</span>
                  </div>
                  <div class="col-6 col-md-9">
                    <div class="form-check form-check-inline">
                      <input
                        id="formStatus1"
                        v-model="formStatus"
                        class="form-check-input"
                        type="checkbox"
                        value="1"
                      >
                      <label
                        class="form-check-label"
                        for="formStatus1"
                      >
                        <span>進行中</span>
                      </label>
                    </div>
                    <div class="form-check form-check-inline">
                      <input
                        id="formStatus2"
                        v-model="formStatus"
                        class="form-check-input"
                        type="checkbox"
                        value="2"
                      >
                      <label
                        class="form-check-label"
                        for="formStatus2"
                      >
                        <span>同意</span>
                      </label>
                    </div>
                    <div class="form-check form-check-inline">
                      <input
                        id="formStatus3"
                        v-model="formStatus"
                        class="form-check-input"
                        type="checkbox"
                        value="3"
                      >
                      <label
                        class="form-check-label"
                        for="formStatus3"
                      >
                        <span>不同意</span>
                      </label>
                    </div>
                    <div class="form-check form-check-inline">
                      <input
                        id="formStatus4"
                        v-model="formStatus"
                        class="form-check-input"
                        type="checkbox"
                        value="4"
                      >
                      <label
                        class="form-check-label"
                        for="formStatus4"
                      >
                        <span>抽單</span>
                      </label>
                    </div>
                  </div>
                </div>
                <div class="row my-2">
                  <div class="col-3 col-md-2">
                    <span>卡號</span>
                  </div>
                  <div class="col-6 col-md-4">
                    <input
                      v-model="formNo"
                      type="text"
                      class="form-control"
                      aria-label="formNo"
                    >
                  </div>
                </div>
                <div class="row my-2">
                  <div class="col text-center">
                    <button
                      type="button"
                      class="btn btn-primary me-1 p-1 p-sm-2"
                      @click="onQueryData"
                    >
                      <span>查詢</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template #empty>
        <div class="text-center">
          <span>無</span>
        </div>
      </template>
      <template #paginatorfirstpagelinkicon>
        <i class="bi bi-chevron-double-left" />
      </template>
      <template #paginatorprevpagelinkicon>
        <i class="bi bi-chevron-left" />
      </template>
      <template #paginatornextpagelinkicon>
        <i class="bi bi-chevron-right" />
      </template>
      <template #paginatorlastpagelinkicon>
        <i class="bi bi-chevron-double-right" />
      </template>
      <Column
        field="formNo"
        header="卡號"
        :sortable="true"
      >
        <template #body="{ data, field }">
          <router-link :to="{
            name: data['formID'] + 'DataManagementQuery',
            params: { formUID: data['formUID'] }
          }">
            {{ data[field] }}
          </router-link>
          <router-link
            v-if="data['formStatus'] === 2 && data['formID'] === 'A1Card'"
            v-slot="{ href, navigate }"
            :to="{
              name: data['formID'] + 'DataManagementEdit',
              params: { formUID: data['formUID'] }
            }"
            custom
          >
            <a
              class="btn btn-danger btn-sm bi bi-pencil ms-1"
              role="button"
              tabindex="0"
              @keydown.enter="onKeydownEditLink(navigate)"
              @keydown.space="onKeydownEditLink(navigate)"
              @click="onClickEditLink($event, href, navigate)"
            />
          </router-link>
        </template>
        <template #sorticon="sort">
          <span
            v-if="sort.sorted === false"
            class="bi bi-arrow-down-up ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === 1)"
            class="bi bi-sort-up-alt ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === -1)"
            class="bi bi-sort-down-alt ms-1"
          />
        </template>
      </Column>
      <Column
        field="formID"
        header="卡別"
        :sortable="true"
      >
        <template #body="{ data, field }">
          {{ data[field] ? formatFormID(data[field]) : '' }}
        </template>
        <template #sorticon="sort">
          <span
            v-if="sort.sorted === false"
            class="bi bi-arrow-down-up ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === 1)"
            class="bi bi-sort-up-alt ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === -1)"
            class="bi bi-sort-down-alt ms-1"
          />
        </template>
      </Column>
      <Column
        field="empNo"
        header="申請人"
        :sortable="true"
      >
        <template #body="{ data }">
          <span class="me-1">
            {{ data['empNo'] ?? '' }}
          </span>
          <span>
            {{ data['empName'] ?? '' }}
          </span>
        </template>
        <template #sorticon="sort">
          <span
            v-if="sort.sorted === false"
            class="bi bi-arrow-down-up ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === 1)"
            class="bi bi-sort-up-alt ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === -1)"
            class="bi bi-sort-down-alt ms-1"
          />
        </template>
      </Column>
      <Column
        field="applicationType"
        header="申請別"
        :sortable="true"
      >
        <template #body="{ data, field }">
          <template v-if="data['formID'] !== 'A1Card'">
            {{ data[field] }}
          </template>
        </template>
        <template #sorticon="sort">
          <span
            v-if="sort.sorted === false"
            class="bi bi-arrow-down-up ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === 1)"
            class="bi bi-sort-up-alt ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === -1)"
            class="bi bi-sort-down-alt ms-1"
          />
        </template>
      </Column>
      <Column
        field="formInfo"
        header="申請資訊"
        :sortable="true"
      >
        <template #sorticon="sort">
          <span
            v-if="sort.sorted === false"
            class="bi bi-arrow-down-up ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === 1)"
            class="bi bi-sort-up-alt ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === -1)"
            class="bi bi-sort-down-alt ms-1"
          />
        </template>
      </Column>
      <Column
        field="totalHours"
        header="申請時數"
        :sortable="true"
      >
        <template #sorticon="sort">
          <span
            v-if="sort.sorted === false"
            class="bi bi-arrow-down-up ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === 1)"
            class="bi bi-sort-up-alt ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === -1)"
            class="bi bi-sort-down-alt ms-1"
          />
        </template>
      </Column>
      <Column
        field="formStatus"
        header="表單狀態"
        :sortable="true"
      >
        <template #body="{ data, field }">
          {{ data[field] ? data['formStatusName'] : '' }}
        </template>
        <template #sorticon="sort">
          <span
            v-if="sort.sorted === false"
            class="bi bi-arrow-down-up ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === 1)"
            class="bi bi-sort-up-alt ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === -1)"
            class="bi bi-sort-down-alt ms-1"
          />
        </template>
      </Column>
      <Column
        field="createdTime"
        header="填報時間"
        :sortable="true"
      >
        <template #body="{ data, field }">
          {{ data[field] ? dateToRocString(new Date(data[field])) : '' }}
        </template>
        <template #sorticon="sort">
          <span
            v-if="sort.sorted === false"
            class="bi bi-arrow-down-up ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === 1)"
            class="bi bi-sort-up-alt ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === -1)"
            class="bi bi-sort-down-alt ms-1"
          />
        </template>
      </Column>
      <Column
        field="createdName"
        header="填表人"
        :sortable="true"
      >
        <template #body="{ data }">
          <div>
            <span class="me-1">
              {{ data['createdEmpNo'] ?? '' }}
            </span>
            <span>
              {{ data['createdName'] ?? '' }}
            </span>
            <template v-if="data['deputy'] === 2 || data['deputy'] === 3">
              <span class="badge rounded-pill bg-warning">代</span>
            </template>
          </div>
        </template>
        <template #sorticon="sort">
          <span
            v-if="sort.sorted === false"
            class="bi bi-arrow-down-up ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === 1)"
            class="bi bi-sort-up-alt ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === -1)"
            class="bi bi-sort-down-alt ms-1"
          />
        </template>
      </Column>
      <Column
        field="addedSigner"
        header="加會人員"
      >
        <template #body="{ data, field }">
          <template v-if="data[field]?.split(',')?.length > 1">
            {{ data[field].split(',').slice(data[field].split(',').length - 1).toString() + '...' }}
          </template>
          <template v-else>
            {{ data[field] }}
          </template>
        </template>
        <template #sorticon="sort">
          <span
            v-if="sort.sorted === false"
            class="bi bi-arrow-down-up ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === 1)"
            class="bi bi-sort-up-alt ms-1"
          />
          <span
            v-else-if="(sort.sorted === true) && (parseInt(sort.sortOrder.toString(), 10) === -1)"
            class="bi bi-sort-down-alt ms-1"
          />
        </template>
      </Column>
    </DataTable>

    <template v-if="queryDataTime !== null">
      <div class="text-black-50 text-end">
        <span>
          查詢時間：
        </span>
        <span class="fst-italic">
          {{ dateToRocString(queryDataTime) }}
        </span>
      </div>
    </template>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { FilterMatchMode, FilterOperator } from '@primevue/core/api'
import { useToast } from 'primevue/usetoast'
import { useOvertimeCompensatory } from '../composable/overtimeCompensatory'
import { useDepartment } from '../composable/department'
import { useAbortController } from '../composable/abortController'
import { useDataManagementQueryStore } from '../store/dataManagementQuery'
import { dateToRocString, formatFormID, onSearchEmployeeData } from '../api/appFunction'
import { GET_LEAVEKINDS_URL, GET_ALLEMPLOYEES_URL, POST_FORMS_URL, POST_FORMS_CONTENTENTDATE_URL, POST_FORMS_CONTENTENTYEARMONTH_URL } from '../api/appUrl'
import { ROC_YEARS_OPTIONS, MONTHS_OPTIONS, TEN_DAYS_OPTIONS, CARDS_OPTIONS, SYSTEM_ERROR_MESSAGE, DATERANGE_WARN_MESSAGE } from '../api/appConst'
import { onBeforeRouteLeave } from 'vue-router'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import VueSelect from 'vue-select'
import ChooseUser from '../components/ChooseUser.vue'
import RocCalendarSelect from '../components/RocCalendarSelect.vue'
import isEqual from 'lodash.isequal'
import type { MonthsOptionsType, TenDaysOptionsType, CardOptionsType, OvertimeCompensatoryType, LeaveKindApiType, LeaveKindOptionsType, EmployeeStoreBaseType, DeptType, EmployeeApiBaseType } from '../api/appType'
import type { NavigationFailure } from 'vue-router'
import type { DataTablePageEvent, DataTableFilterEvent } from 'primevue/datatable'

const rows = 200

const toast = useToast()
const { overtimeCompensatory, onGetOvertimeCompensatory } = useOvertimeCompensatory()
const { deptOptions, onGetDeptOptions } = useDepartment()
const dataManagementQueryStore = useDataManagementQueryStore()
const { abortController, fetchErrorHandler, abortListener } = useAbortController()

const {
  queryOnce,
  queryPage,
  queryTotalRecords,
  sortField,
  sortOrder,
  formID,
  contentStartTime,
  contentEndTime,
  startTime,
  endTime,
  overtimeCompensatoryKind,
  leaveKind,
  queryTimeType,
  employee,
  dept,
  projectNumber,
  formStatus,
  formNo,
  years,
  months,
  tenDays
} = storeToRefs(dataManagementQueryStore)

const watchedState = ref<any>()

const allEmployeeData = ref<Array<EmployeeStoreBaseType>>([])

const queryDataTime = ref<Date | null>(null)
const cardData = ref<Array<any>>([])
const cardDataLoading = ref<boolean>(false)

const leaveKindOptions = ref<Array<LeaveKindOptionsType>>([])

const filters = ref<any>({
  formID: { value: null, matchMode: FilterMatchMode.EQUALS },
  empNo: { value: null, matchMode: FilterMatchMode.EQUALS },
  deptNo: { value: null, matchMode: FilterMatchMode.EQUALS },
  formStatus: {
    operator: FilterOperator.OR,
    constraints: [
      { value: '0', matchMode: FilterMatchMode.EQUALS }, // 表單狀態的選項全部沒有勾選的情形
      { value: '1', matchMode: FilterMatchMode.EQUALS },
      { value: '2', matchMode: FilterMatchMode.EQUALS },
      { value: '3', matchMode: FilterMatchMode.EQUALS },
      { value: '4', matchMode: FilterMatchMode.EQUALS }
    ]
  },
  applicationType: {
    operator: FilterOperator.AND,
    constraints: [
      { value: null, matchMode: FilterMatchMode.EQUALS }
    ],
  },
  formNo: { value: null, matchMode: FilterMatchMode.CONTAINS }
})

const onChangeFormID = (event: string): void => {
  formID.value = event
}

const onChangeYear = (year: number): void => {
  years.value = year
}

const onChangeMonth = (event: number): void => {
  months.value = MONTHS_OPTIONS.find((e: MonthsOptionsType) => e.optionValue === event) ?? MONTHS_OPTIONS[0]

  if (event === 0) {
    tenDays.value = TEN_DAYS_OPTIONS[0]
  }
}

const onChangeTenDays = (event: string): void => {
  tenDays.value = TEN_DAYS_OPTIONS.find((e: TenDaysOptionsType) => e.optionValue === event) ?? TEN_DAYS_OPTIONS[0]
}

const onSetWatchedState = (): void => {
  watchedState.value = {
    formID: formID.value,
    contentStartTime: contentStartTime.value,
    contentEndTime: contentEndTime.value,
    startTime: startTime.value,
    endTime: endTime.value,
    overtimeCompensatoryKind: overtimeCompensatoryKind.value,
    leaveKind: leaveKind.value,
    queryTimeType: queryTimeType.value,
    employee: employee.value,
    dept: dept.value,
    projectNumber: projectNumber.value,
    formStatus: formStatus.value,
    formNo: formNo.value
  }
}

const onChangeOvertimeCompensatory = (event: number): void => {
  overtimeCompensatoryKind.value = overtimeCompensatory.value.find((e: {
    id: number
    name: string
  }) => e.id === event) ?? overtimeCompensatoryKind.value
}

const onChangeleaveKind = (event: number): void => {
  leaveKind.value = event
}

const onChangeEmployee = (event: EmployeeStoreBaseType): void => {
  employee.value = {
    userId: event.userId,
    userName: event.userName,
    deptNo: event.deptNo,
    deptSName: event.deptSName
  }
}

const onDeleteEmployee = (): void => {
  employee.value = {
    userId: '',
    userName: '',
    deptNo: 0,
    deptSName: ''
  }
}

const onChangeDept = (event: number): void => {
  dept.value = deptOptions.value.find((e: DeptType) => e.deptNo === event) ?? dept.value
  if ((dept.value.deptNo !== -1) && (employee.value.deptNo !== dept.value.deptNo)) {
    onDeleteEmployee()
  }
}

const onGetLeaveKindOptions = async (): Promise<void> => {
  try {
    const res: Response = await fetch(GET_LEAVEKINDS_URL, {
      method: 'GET',
      signal: abortController.signal
    })
    if (!res.ok) {
      throw new Error(res.status.toString())
    } else {
      const jsonData = await res.json()
      leaveKindOptions.value = leaveKindOptions.value.concat(
        jsonData.filter((e: LeaveKindApiType) => e.DisplayOnFrontEnd === true).map((e: LeaveKindApiType) => {
          return {
            name: e.Name,
            number: e.Number
          }
        })
      )
    }
  } catch (err: unknown) {
    throw err
  }
}

const onSortField = (event: string): void => {
  sortField.value = event
  dataManagementQueryStore.sortField = event
}

const onSortOrder = (event: number): void => {
  sortOrder.value = event
  dataManagementQueryStore.sortOrder = event
}

const onChangePage = (event: DataTablePageEvent): void => {
  const page = event.page * rows
  queryPage.value = page
  dataManagementQueryStore.queryPage = page
}

const onFilter = (event: DataTableFilterEvent): void => {
  queryTotalRecords.value = event.filteredValue.length
}

const onSetFilter = (): void => {
  filters.value.formID.value = formID.value

  filters.value.empNo.value = null
  if (employee.value.userId !== '') {
    filters.value.empNo.value = employee.value.userId
  }

  filters.value.deptNo.value = null
  if (dept.value.deptNo !== -1) {
    filters.value.deptNo.value = dept.value.deptNo
  }

  filters.value.applicationType.constraints = [{
    value: null,
    matchMode: FilterMatchMode.EQUALS
  }]
  if (formID.value === 'B1CardApp' || formID.value === 'B1Card') {
    const found: OvertimeCompensatoryType | undefined = overtimeCompensatory.value.find((e: OvertimeCompensatoryType) => e.id === overtimeCompensatoryKind.value.id)
    if (found?.id !== 0) {
      filters.value.applicationType.constraints = [{
        value: found?.name,
        matchMode: FilterMatchMode.EQUALS
      }]
    }
  } else if (formID.value === 'C1Card') {
    const found: LeaveKindOptionsType | undefined = leaveKindOptions.value.find((e: LeaveKindOptionsType) => e.number === leaveKind.value)
    if (found?.number !== 0) {
      filters.value.applicationType.constraints = [{
        value: found?.name,
        matchMode: FilterMatchMode.EQUALS
      }]
    }
  }

  filters.value.formStatus.constraints = formStatus.value.map((e: string) => {
    return {
      value: e,
      matchMode: FilterMatchMode.EQUALS
    }
  })

  filters.value.formNo.value = formNo.value
}

const onKeydownEditLink = (navigate: (e?: MouseEvent) => Promise<void | NavigationFailure>): void => {
  navigate()
}

const onClickEditLink = (event: MouseEvent, href: string, navigate: (e?: MouseEvent) => Promise<void | NavigationFailure>): void => {
  if (event.ctrlKey === true || event.shiftKey === true) {
    window.open(href, '_blank')
  } else {
    navigate()
  }
}

const onSetAllEmployeeData = async (): Promise<void> => {
  try {
    const res: Response = await fetch(GET_ALLEMPLOYEES_URL, {
      method: 'GET',
      signal: abortController.signal
    })
    if (!res.ok) {
      throw new Error(res.status.toString())
    } else {
      const jsonData = await res.json()
      allEmployeeData.value = jsonData.map((e: EmployeeApiBaseType) => {
        return {
          userId: e.EmpNo,
          userName: e.CName,
          deptNo: e.DeptNo,
          deptSName: e.DeptSName
        }
      })
    }
  } catch (err: unknown) {
    throw err
  }
}

const onQueryData = async (): Promise<void> => {
  if (startTime.value > endTime.value) {
    toast.add({
      severity: 'warn',
      summary: DATERANGE_WARN_MESSAGE,
      group: 'app'
    })
  } else {
    let url, postData
    if (formID.value === 'A1Card') {
      url = POST_FORMS_CONTENTENTYEARMONTH_URL
      postData = {
        projNo: projectNumber.value,
        year: years.value + 1911,
        month: months.value.optionValue,
        tenDays: (parseInt(tenDays.value.optionValue, 10) + 1)
      }
    } else {
      url = POST_FORMS_URL
      postData = {
        projNo: projectNumber.value,
        startDate: startTime.value.toISOString(),
        endDate: endTime.value.toISOString()
      }
      if (queryTimeType.value === '1') {
        url = POST_FORMS_CONTENTENTDATE_URL
        postData = {
          projNo: projectNumber.value,
          startDate: contentStartTime.value.toISOString(),
          endDate: contentEndTime.value.toISOString()
        }
      }
    }
  
    queryDataTime.value = new Date()
    cardDataLoading.value = true

    try {
      const res: Response = await fetch(url, {
        method: 'POST',
        headers: {
          'content-type': 'application/json'
        },
        body: JSON.stringify(postData),
        signal: abortController.signal
      })
      if (!res.ok) {
        throw new Error(res.status.toString())
      } else {
        const resJson = await res.json()
        cardData.value = resJson.map((e: any) => {
          // 1:自填、2:他人代填、3:代他人填
          e.Deputy = 1
          if (e.IsAgentFilled === true) {
            e.Deputy = 2
          } else if (e.IsAgency === true) {
            e.Deputy = 3
          }

          return {
            formID: e.FormID,
            formUID: e.FormUID,
            formNo: e.FormNo,
            formInfo: e.FormInfo,
            formStatus: e.FormStatus,
            formStatusName: e.FormStatusName,
            totalHours: e.TotalHours,
            applicationType: e.ApplicationType,
            contentStartTime: e.ContentStartTime ? new Date(e.ContentStartTime) : null,
            contentEndTime: e.ContentEndTime ? new Date(e.ContentEndTime) : null,
            createdTime: e.CreatedTime ? new Date(e.CreatedTime) : null,
            createdEmpNo: e.CreatedEmpNo,
            createdName: e.CreatedName,
            empNo: e.EmpNo,
            empName: e.EmpName,
            deptNo: e.DeptNo,
            deputy: e.Deputy,
            addedSigner: e.AddedSigner
          }
        })
      }
    } catch (err: unknown) {
      console.error(err)
      fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
    }
    onSetFilter()
    cardDataLoading.value = false
    queryOnce.value = true
    onSetWatchedState()
  }
}

onBeforeRouteLeave(() => {
  toast.removeGroup('app')
  const checkEqual = isEqual(watchedState.value, {
    formID: formID.value,
    contentStartTime: contentStartTime.value,
    contentEndTime: contentEndTime.value,
    startTime: startTime.value,
    endTime: endTime.value,
    overtimeCompensatoryKind: overtimeCompensatoryKind.value,
    leaveKind: leaveKind.value,
    queryTimeType: queryTimeType.value,
    employee: employee.value,
    dept: dept.value,
    projectNumber: projectNumber.value,
    formStatus: formStatus.value,
    formNo: formNo.value
  })
  if (watchedState.value !== undefined && checkEqual === false) {
    dataManagementQueryStore.setParameter(watchedState.value)
  }
})

onMounted(async (): Promise<void> => {
  abortListener()
  deptOptions.value.push({
    deptNo: -1,
    deptSName: '全社'
  })
  await onGetDeptOptions(abortController.signal).catch((err: Error) => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })

  if (dept.value.deptNo === 0) {
    dept.value = {
      deptNo: -1,
      deptSName: '全社'
    }
  }

  overtimeCompensatory.value.push({
    id: 0,
    name: '全部'
  })
  await onGetOvertimeCompensatory(abortController.signal).catch((err: Error) => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })

  leaveKindOptions.value.push({
    number: 0,
    name: '全部'
  })
  await onGetLeaveKindOptions().catch((err: Error) => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })

  await onSetAllEmployeeData().catch((err: Error) => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })

  if (queryOnce.value === true) {
    await onQueryData()
  }
})
</script>