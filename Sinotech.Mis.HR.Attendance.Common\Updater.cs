﻿using System;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 更新者
    /// </summary>
    public class Updater
    {
        /// <summary>
        /// 最後修改者員工編號
        /// </summary>
        public string UpdatedEmpNo { get; set; } = string.Empty;
        /// <summary>
        /// 最後修改者姓名
        /// </summary>
        public string UpdatedName { get; set; } = string.Empty;

        /// <summary>
        /// 最後修改日期時間
        /// </summary>
        public DateTime UpdatedTime { get; set; }
        /// <summary>
        /// 最後修改來源IP 位址
        /// </summary>
        public string UpdatedIP { get; set; } = string.Empty;
        /// <summary>
        /// 最後修改來源電腦名稱
        /// </summary>
        public string UpdatedHost { get; set; } = string.Empty;
    }
}
