﻿﻿using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using System;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves
{
    /// <summary>
    /// 事假類別 - 處理員工事假的申請邏輯
    /// 包含超假檢查、必要欄位驗證、與家庭照顧假合併計算等功能
    /// </summary>
    /// <seealso cref="Leaves.LeaveBase" />
    [LeaveKind(LeaveKindEnum.PersonalLeave)]
    public class PersonalLeave : C1CardBase
    {
        #region CheckResult - 檢查結果相關常數與訊息定義

        /// <summary>
        /// 錯誤代碼：超過年度事假額度
        /// </summary>
        public const int CodeExceedQuota = 3004901;
        /// <summary>
        /// 超假狀態：需要確認（允許用戶選擇是否繼續超假）
        /// </summary>
        private readonly CardStatusEnum _statusExceedQuota = CardStatusEnum.Conformation;
        /// <summary>
        /// 超假訊息樣板1：僅計算事假時數的情況
        /// 參數：{0}本次請假時數, {1}年度累計時數, {2}總計時數, {3}年度上限時數
        /// </summary>
        private readonly string _messagePatternExceedQuota1 = "您本次請假 {0} 小時，年度累計已請 {1} 小時，總計 {2} 小時，已超過年度上限（{3} 小時），是否確定超假？";
        /// <summary>
        /// 超假訊息樣板2：合併計算事假與家庭照顧假時數的情況
        /// 參數：{0}本次請假時數, {1}年度累計時數, {2}事假時數, {3}家庭照顧假時數, {4}總計時數, {5}年度上限時數
        /// </summary>
        private readonly string _messagePatternExceedQuota2 = "您本次請假 {0} 小時，年度累計已請 {1} 小時（事假 {2} 小時、家庭照顧假 {3} 小時），總計 {4} 小時，已超過年度上限（{5} 小時），是否確定超假？";

        #endregion

        /// <summary>
        /// 事假類別建構子
        /// 初始化事假處理物件，傳入請假卡片資料與業務邏輯物件
        /// </summary>
        /// <param name="c1Card">C1請假卡片資料</param>
        /// <param name="c1CardBo">C1請假卡片業務邏輯物件</param>
        public PersonalLeave(C1Card c1Card, IC1CardBo c1CardBo) : base(c1Card, c1CardBo)
        {
        }

        /// <summary>
        /// 檢查是否能請此假別（事假）
        /// 事假無性別、年資等特殊限制，主要檢查休假記錄是否存在
        /// </summary>
        /// <returns>檢查結果，包含錯誤代碼、狀態與訊息</returns>
        internal override CardCheckResult CanTakeThisLeave()
        {
            // 檢查是否已經建立休假資料記錄
            if (!IfLeaveRecordExists())
            {
                return ResultLeaveRecordNotCreatedYet;
            }

            // 所有檢查通過
            return ResultOk;
        }

        /// <summary>
        /// 檢查是否超過允許的請假時數
        /// 事假與家庭照顧假合併計算年度額度，需檢查是否超過年度上限
        /// </summary>
        /// <returns>檢查結果，包含錯誤代碼、狀態與訊息</returns>
        internal override CardCheckResult CheckOverPermittedLeaveHours()
        {
            // 取得年度可用事假時數
            var available = GetPersonalLeaveYearAvailableHours();
            // 取得年度已使用事假時數
            var used = GetPersonalLeaveYearUsedHours();
            // 取得年度已使用家庭照顧假時數（與事假合併計算）
            var familyCareLeaveUsedHours = GetFamilyCareLeaveYearUsedHours();

            // 計算總已使用時數（事假 + 家庭照顧假）
            var aggregateUsedHours = used + familyCareLeaveUsedHours;
            // 檢查是否已確認超假
            var confirmed = _c1Card.Confirmed;

            // 如果本次請假 + 已使用時數 > 可用時數，且尚未確認超假
            if (TotalHours + aggregateUsedHours > available && !confirmed)
            {
                return GetExceedQuotaCheckResult();
            }
            return _resultOk;

            /// <summary>
            /// 產生超假檢查結果
            /// 根據是否有家庭照顧假使用記錄，選擇適當的訊息樣板
            /// </summary>
            /// <returns>超假確認的檢查結果</returns>
            CardCheckResult GetExceedQuotaCheckResult()
            {
                CardCheckResult result;
                // 如果有使用家庭照顧假，使用詳細訊息樣板
                if (familyCareLeaveUsedHours > 0)
                {
                    result = new CardCheckResult(CodeExceedQuota, _statusExceedQuota,
                                string.Format(_messagePatternExceedQuota2,
                                    TotalHours, aggregateUsedHours,
                                    used, familyCareLeaveUsedHours,
                                    TotalHours + aggregateUsedHours, available));
                }
                // 如果僅有事假，使用簡化訊息樣板
                else
                {
                    result = new CardCheckResult(CodeExceedQuota, _statusExceedQuota,
                                string.Format(_messagePatternExceedQuota1,
                                    TotalHours, aggregateUsedHours,
                                    TotalHours + aggregateUsedHours, available));
                }

                return result;
            }
        }

        /// <summary>
        /// 檢查必要欄位是否已填寫
        /// 繼承父類別的基本欄位檢查邏輯
        /// </summary>
        /// <returns>檢查結果，包含錯誤代碼、狀態與訊息</returns>
        internal override CardCheckResult CheckRequiredFields()
        {
            // 執行父類別的基本必要欄位檢查
            var result = base.CheckRequiredFields();
            if (result != ResultOk)
            {
                return result;
            }

            // 事假無額外的必要欄位檢查
            return ResultOk;
        }

        /// <summary>
        /// 計算最早可請假日期與最晚可請假日期
        /// 使用預設的計算邏輯，事假無特殊期間限制
        /// </summary>
        /// <param name="date">請假日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>Tuple包含 (最早可請假日期, 最晚可請假日期)</returns>
        public override (DateTime, DateTime) CalculateLeavePermittedPeriod(DateTime date, string empNo) => DefaultCalculateLeavePermittedPeriod(date, empNo);

        /// <summary>
        /// 檢查指定性別是否可請此假別（事假）
        /// 事假不限性別，男女皆可申請
        /// </summary>
        /// <param name="gender">員工性別</param>
        /// <returns>true：可申請；false：不可申請</returns>
        public static bool IsAllowForThisGender(Gender gender)
        {
            return true;
        }

        #region Tools - 輔助工具方法

        /// <summary>
        /// 取得員工年度可用事假時數
        /// 根據員工編號和請假日期計算該年度的事假額度
        /// </summary>
        /// <returns>年度可用事假時數</returns>
        private int GetPersonalLeaveYearAvailableHours()
        {
            return _c1CardBo.GetPersonalLeaveYearAvailableHours(_c1Card.EmpNo, _c1Card.StartDate);
        }

        /// <summary>
        /// 取得員工年度已使用事假時數
        /// 計算從年初至今已申請的事假總時數
        /// </summary>
        /// <returns>年度已使用事假時數</returns>
        private int GetPersonalLeaveYearUsedHours()
        {
            return _c1CardBo.GetPersonalLeaveYearUsedHours(_c1Card.EmpNo, _c1Card.StartDate);
        }

        /// <summary>
        /// 取得員工年度已使用家庭照顧假時數
        /// 家庭照顧假與事假合併計算年度額度，需一併考慮
        /// </summary>
        /// <returns>年度已使用家庭照顧假時數</returns>
        private int GetFamilyCareLeaveYearUsedHours()
        {
            return _c1CardBo.GetFamilyCareLeaveYearUsedHours(_c1Card.EmpNo, _c1Card.StartDate);
        }

        #endregion
    }
}
