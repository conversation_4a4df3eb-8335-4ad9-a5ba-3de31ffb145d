﻿using Sinotech.Mis.HR.Attendance.BusinessLogic;
using FakeItEasy;
using Microsoft.Extensions.Configuration;
using Sinotech.Mis.Extensions.Configuration;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.DataAccess.Interfaces;
using Sinotech.Mis.HR.Attendance.Tests;
using Sinotech.Mis.Utilities.DataAccess.Interfaces;
using System;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.Tests
{
    /// <summary>
    /// Test of AttendanceBo
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class AttendanceBoTests
    {

        private readonly AttendanceBo _attendanceBo;

        public AttendanceBoTests(AttendanceBo attendanceBo)
        {
            _attendanceBo = attendanceBo;
            IConfiguration configuration = new ConfigurationBuilder().
AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).Build();
            string ConnectionStringAttendance = configuration.GetSecuredConnectionString("Attendance");
            TestHelper.ClearData(ConnectionStringAttendance);
        }

        [Theory]
        [InlineData(2023, 3, 10, "2000", false)]
        [InlineData(2023, 3, 10, "2001", false)]
        [InlineData(2023, 3, 10, "2268", true)]
        [InlineData(2023, 3, 10, "2003", true)]
        [InlineData(2023, 3, 10, "0391", true)]
        [InlineData(2023, 3, 10, "2008", true)]
        [InlineData(2023, 3, 10, "0476", false)]
        [InlineData(2023, 3, 10, "0741", true)]
        [InlineData(2023, 3, 10, "2065", true)]
        [InlineData(2023, 3, 10, "2191", true)]
        public void CanCallGetMonthAttendance(int year, int month, int day, string empNo, bool isEmpty)
        {
            // Arrange
            var date = new DateTime(year, month, day, 0, 0, 0, DateTimeKind.Local);

            // Act
            var result = _attendanceBo.GetMonthAttendance(date, empNo);

            // Assert
            Assert.Equal(isEmpty, result.Count > 0);
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("   ")]
        public void CannotCallGetMonthAttendanceWithInvalidUserid(string empNo)
        {
            Assert.Throws<ArgumentNullException>(() => _attendanceBo.GetMonthAttendance(DateTime.UtcNow, empNo));
        }

        [Theory]
        [InlineData("0349", "0395", true)] // 管理員
        [InlineData("2008", "0000", false)] // 員工不存在
        [InlineData("0349", "0000", true)] // 管理員員工不存在也可查
        [InlineData("2008", "2029", false)] // 非同組員工
        [InlineData("2008", "2008", true)] // 員工本人
        [InlineData("2008", "2308", true)] // 同組員工
        [InlineData("2029", "2260", true)] // 同組員工
        [InlineData("2029", "2008", false)] // 非同組員工
        [InlineData("2029", "2029", true)] // 員工本人
        [InlineData("0274", "2029", true)] // 部門主管
        public void CanSeeAttendanceTest(string watcherId, string empNo, bool expected)
        {
            // Action
            bool actual = _attendanceBo.CanSeeAttendance(watcherId, empNo);
            // Assert
            Assert.Equal(expected, actual);
        }

        [Fact]
        public void DeliveredNotificationsTest()
        {
            IAttendanceDao attendanceDao = A.Fake<IAttendanceDao>();
            IEmployeeBo employeeBo = A.Fake<IEmployeeBo>();
            IDepartmentBo departmentBo = A.Fake<IDepartmentBo>();
            ISinoSignBo sinoSignBo = A.Fake<ISinoSignBo>();
            IWorkdayDao workdayDao = A.Fake<IWorkdayDao>();
            WorkdayBo workdayBo = new WorkdayBo(workdayDao);
            AttendanceBo attendanceBo = new AttendanceBo(attendanceDao, departmentBo, employeeBo, sinoSignBo, workdayBo);
            int id = 3;
            A.CallTo(() => attendanceDao.DeliveredNotifications(id)).Returns(true);
            bool result = attendanceBo.DeliveredNotifications(id);
            Assert.True(result);
        }

        [Theory]
        [InlineData(null, 3)]
        [InlineData(1, 1)]
        [InlineData(2, 1)]
        [InlineData(3, 1)]
        [InlineData(4, 0)]
        public void GetAllSentBoxByContentDateTest(int? status, int expected)
        {
            IAttendanceDao attendanceDao = A.Fake<IAttendanceDao>();
            IDepartmentBo departmentBo = A.Fake<IDepartmentBo>();
            IEmployeeBo employeeBo = A.Fake<IEmployeeBo>();
            ISinoSignBo sinoSignBo = A.Fake<ISinoSignBo>();
            IWorkdayDao workdayDao = A.Fake<IWorkdayDao>();
            //IWorkdayBo workdayBo = A.Fake<IWorkdayBo>();
            WorkdayBo workdayBo = new WorkdayBo(workdayDao);

            DataTable dt = new DataTable();
            A.CallTo(() => attendanceDao.GetFormsByContentDate(A<DateTime>.Ignored, A<DateTime>.Ignored)).Returns(dt);
            AttendanceBo attendanceBo = new AttendanceBo(attendanceDao, departmentBo, employeeBo, sinoSignBo, workdayBo);
            DataTable result = attendanceBo.GetAllSentBoxByContentDate(DateTime.Now, DateTime.Now);
            Assert.Empty(result.Rows);

            dt.Columns.Add("Status", typeof(int));
            dt.Columns.Add("ID", typeof(int));
            DataRow row = dt.NewRow();
            row["Status"] = 1;
            row["ID"] = 1;
            dt.Rows.Add(row);
            row = dt.NewRow();
            row["Status"] = 2;
            row["ID"] = 2;
            dt.Rows.Add(row);
            row = dt.NewRow();
            row["Status"] = 3;
            row["ID"] = 3;
            dt.Rows.Add(row);
            result = attendanceBo.GetAllSentBoxByContentDate(DateTime.Now, DateTime.Now, status);
            Assert.Equal(expected, result.Rows.Count);
        }

        [Theory]
        [InlineData(null, 3)]
        [InlineData(1, 1)]
        [InlineData(2, 1)]
        [InlineData(3, 1)]
        [InlineData(4, 0)]
        public void GetAllSentBoxTest(int? status, int expected)
        {
            IAttendanceDao attendanceDao = A.Fake<IAttendanceDao>();
            IDepartmentBo departmentBo = A.Fake<IDepartmentBo>();
            IEmployeeBo employeeBo = A.Fake<IEmployeeBo>();
            ISinoSignBo sinoSignBo = A.Fake<ISinoSignBo>();
            IWorkdayDao workdayDao = A.Fake<IWorkdayDao>();
            WorkdayBo workdayBo = new WorkdayBo(workdayDao);

            DataTable dt = new DataTable();
            A.CallTo(() => attendanceDao.GetForms(A<DateTime>.Ignored, A<DateTime>.Ignored)).Returns(dt);
            AttendanceBo attendanceBo = new AttendanceBo(attendanceDao, departmentBo, employeeBo, sinoSignBo, workdayBo);
            DataTable result = attendanceBo.GetAllSentBox(DateTime.Now, DateTime.Now);
            Assert.Empty(result.Rows);

            dt.Columns.Add("Status", typeof(int));
            dt.Columns.Add("ID", typeof(int));
            DataRow row = dt.NewRow();
            row["Status"] = 1;
            row["ID"] = 1;
            dt.Rows.Add(row);
            row = dt.NewRow();
            row["Status"] = 2;
            row["ID"] = 2;
            dt.Rows.Add(row);
            row = dt.NewRow();
            row["Status"] = 3;
            row["ID"] = 3;
            dt.Rows.Add(row);
            result = attendanceBo.GetAllSentBox(DateTime.Now, DateTime.Now, status);
            Assert.Equal(expected, result.Rows.Count);
        }

        /// <summary>Test of GetEligibleOvertimeEmployeesDataTableTest</summary>
        [Fact]
        public void GetEligibleOvertimeEmployeesDataTableTest()
        {
            DataTable dtCanOvertimeEmployee = _attendanceBo.GetEligibleOvertimeEmployeesDataTable();
            Assert.True(dtCanOvertimeEmployee.Rows.Count > 0);
        }

        [Fact]
        public void GetDayInTimeStringTest()
        {
            DateTime dateTime = DateTime.Parse("2023-03-29+08:00");
            string empNo = "0395";
            string str = _attendanceBo.GetDayInTimeString(dateTime, empNo);
            Assert.NotEmpty(str);
        }

        [Fact]
        public void GetDayInTimeTest()
        {
            DateTime dateTime = DateTime.Parse("2022/09/27");
            string empNo = "0395";
            DayInTime dayInTime = _attendanceBo.GetDayInTime(dateTime, empNo);
            Assert.NotNull(dayInTime);
            Assert.Equal(empNo, dayInTime.EmpNo);
        }

        [Fact]
        public void GetDepartmentSentBoxByContentDateTest()
        {
            DateTime startDate = new DateTime(2020, 1, 1, 0, 0, 0, DateTimeKind.Local);
            DateTime endDate = new DateTime(2020, 1, 2, 0, 0, 0, DateTimeKind.Local);
            int deptNo = 1;
            string prjNo = "2000";
            DataTable dt = _attendanceBo.GetDepartmentSentBoxByContentDate(deptNo, startDate, endDate, prjNo);
            Assert.Empty(dt.Rows);
        }

        [Theory]
        [InlineData(1, "2020-01-01", "2020-01-02", "2000")]
        [InlineData(10, "2020-01-01", "2020-01-02", "TI23013")]
        public void GetDepartmentSentBoxTest(int deptNo, DateTime startDate, DateTime endDate, string projNo)
        {
            DataTable dt = _attendanceBo.GetDepartmentSentBox(deptNo, startDate, endDate, projNo);
            Assert.Empty(dt.Rows);
        }


        [Fact]
        public void GetEmployeeCompensatoryRestHoursTest()
        {
            DateTime date = new DateTime(2020, 1, 1);
            string empNo = "0395";
            int hours = _attendanceBo.GetEmployeeCompensatoryLeaveHours(date, empNo);
            Assert.Equal(0, hours);
        }

        [Fact]
        public void GetMonthEmployeeLeavesTest()
        {
            DateTime date = new DateTime(2023, 4, 23);
            string empNo = "0349";
            EmployeeLeaves employeeLeaves = _attendanceBo.GetMonthEmployeeLeaves(date, empNo);
            Assert.NotNull(employeeLeaves);
            Assert.Equal(56, employeeLeaves.RemainAnnualLeaves);
            Assert.Equal(0, employeeLeaves.RemainReservedLeaves);
        }

        [Fact]
        public void GetMonthEmployeeOvertimeStaticsTest()
        {
            DateTime date = new DateTime(2020, 1, 1);
            string empNo = "0395";
            MonthOvertimeStatics statics = _attendanceBo.GetMonthEmployeeOvertimeStatics(date, empNo);
            Assert.Equal("1月", statics.MonthName);
            Assert.Equal(0, statics.ApprovedHours);
            Assert.Equal(0, statics.UnderApprovalHours);
        }

        [Theory]
        [InlineData(2022, 10, 10, 2, 8)] //國定假日
        [InlineData(2022, 10, 10, 7, 8)] //國定假日
        [InlineData(2022, 10, 10, 12, 12)] //國定假日
        [InlineData(2022, 10, 10, 13, 12)] //國定假日
        [InlineData(2022, 2, 26, 2, 2)] //週六休息日
        [InlineData(2022, 2, 26, 7, 7)] //週六休息日
        [InlineData(2022, 2, 26, 12, 12)] //週六休息日
        [InlineData(2022, 10, 11, 2, 2)]  //工作日
        [InlineData(2022, 10, 11, 8, 4)]  //工作日
        [InlineData(2022, 1, 22, 2, 2)]  //補班日
        [InlineData(2022, 1, 22, 8, 4)]  //補班日
        [InlineData(2022, 5, 2, 2, 8)]  //補假日
        [InlineData(2022, 5, 2, 7, 8)]  //補假日
        [InlineData(2022, 5, 2, 9, 9)]  //補假日
        [InlineData(2022, 5, 2, 13, 12)]  //補假日
        public void GetPaidHourTest(int year, int month, int day, int hours, int expect)
        {
            DateTime date = new DateTime(year, month, day, 0, 0, 0, DateTimeKind.Local);
            int actual = _attendanceBo.GetPaidHour(date, hours);
            Assert.Equal(expect, actual);
        }

        [Theory]
        [InlineData(2020, 1, 1, "0395", 0)]
        [InlineData(2020, 4, 1, "0395", 0)]
        [InlineData(2020, 7, 1, "0395", 0)]
        [InlineData(2020, 10, 1, "0395", 0)]
        public void GetQuarterlyEmployeeOvertimeHoursTest(int year, int month, int day, string empNo, int expect)
        {
            DateTime date = new DateTime(year, month, day, 0, 0, 0, DateTimeKind.Local);
            int hours = _attendanceBo.GetQuarterlyEmployeeOvertimeHours(date, empNo);
            Assert.Equal(expect, hours);
        }

        [Theory]
        [InlineData(2020, 2, 3, "0395", 0, 0)]
        [InlineData(2020, 4, 1, "0395", 0, 0)]
        [InlineData(2020, 5, 5, "0395", 0, 0)]
        [InlineData(2020, 7, 1, "0395", 0, 0)]
        [InlineData(2020, 9, 9, "0395", 0, 0)]
        [InlineData(2020, 10, 1, "0395", 0, 0)]
        [InlineData(2020, 11, 11, "0395", 0, 0)]
        public void GetQuarterlyEmployeeOvertimeStaticsTest(int year, int month, int day, string empNo, int approval, int underApproval)
        {
            DateTime date = new DateTime(year, month, day, 0, 0, 0, DateTimeKind.Local);
            QuarterlyOvertimeStatics statics = _attendanceBo.GetQuarterlyEmployeeOvertimeStatics(date, empNo);
            Assert.Equal(approval, statics.ApprovedHours);
            Assert.Equal(underApproval, statics.UnderApprovalHours);
        }

        [Theory]
        [InlineData(2020, 2, 3, "0395", 0)]
        [InlineData(2020, 4, 1, "0395", 0)]
        [InlineData(2020, 5, 5, "0395", 0)]
        [InlineData(2020, 7, 1, "0395", 0)]
        [InlineData(2020, 9, 9, "0395", 0)]
        [InlineData(2020, 10, 1, "0395", 0)]
        [InlineData(2020, 11, 11, "0395", 0)]
        public void GetYearSickHoursTest(int year, int month, int day, string empNo, int expect)
        {
            DateTime date = new DateTime(year, month, day, 0, 0, 0, DateTimeKind.Local);
            int hours = _attendanceBo.GetYearSickLeaveHours(date, empNo);
            Assert.Equal(expect, hours);
        }


        [Fact]
        public void IsSpecialStaffTest()
        {
            DateTime date = new DateTime(2022, 10, 11, 0, 0, 0, DateTimeKind.Local);
            string empNo = "2025";
            double allowableMonthWeightedOvertimeHour;
            double currentMonthWeightedOvertimeHour;
            bool isSpecial = _attendanceBo.IsSpecialStaff(empNo);
            Assert.False(isSpecial);
            isSpecial = _attendanceBo.IsSpecialStaff(empNo, date,
                out allowableMonthWeightedOvertimeHour,
                out currentMonthWeightedOvertimeHour);

            Assert.False(isSpecial);
            Assert.Equal(54.0, allowableMonthWeightedOvertimeHour);
            Assert.Equal(0, currentMonthWeightedOvertimeHour);
            empNo = "2008";
            //isSpecial = _c1CardBo.IsSpecialStaff(empNo);
            //Assert.True(isSpecial);
            isSpecial = _attendanceBo.IsSpecialStaff(empNo, new DateTime(2019, 10, 11, 0, 0, 0, DateTimeKind.Local));
            Assert.False(isSpecial);
            isSpecial = _attendanceBo.IsSpecialStaff(empNo, date,
                out allowableMonthWeightedOvertimeHour,
                out currentMonthWeightedOvertimeHour);

            Assert.True(isSpecial);
            Assert.Equal(39.71, allowableMonthWeightedOvertimeHour);
            Assert.Equal(0.0, currentMonthWeightedOvertimeHour);
        }

        [Fact]
        public void IsOvertimeAllowedTest_Invalid()
        {
            var result = _attendanceBo.IsOvertimeAllowed("", DateTime.Now);
            Assert.False(result.IsOvertimeAllowed);
        }

        /// <summary>
        /// 測試取得上旬日期範圍
        /// </summary>
        [Fact]
        public void GetTendays_FirstTenDays_ReturnsCorrectDateRange()
        {
            // Arrange
            int year = 2025;
            int month = 1;
            int tenDays = 1;

            // Act
            var (startDate, endDate) = _attendanceBo.GetTendays(year, month, tenDays);

            // Assert
            Assert.Equal(new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Local), startDate);
            Assert.Equal(new DateTime(2025, 1, 10, 0, 0, 0, DateTimeKind.Local), endDate);
        }

        /// <summary>
        /// 測試取得中旬日期範圍
        /// </summary>
        [Fact]
        public void GetTendays_MiddleTenDays_ReturnsCorrectDateRange()
        {
            // Arrange
            int year = 2025;
            int month = 1;
            int tenDays = 2;

            // Act
            var (startDate, endDate) = _attendanceBo.GetTendays(year, month, tenDays);

            // Assert
            Assert.Equal(new DateTime(2025, 1, 11, 0, 0, 0, DateTimeKind.Local), startDate);
            Assert.Equal(new DateTime(2025, 1, 20, 0, 0, 0, DateTimeKind.Local), endDate);
        }

        /// <summary>
        /// 測試取得下旬日期範圍
        /// </summary>
        [Fact]
        public void GetTendays_LastTenDays_ReturnsCorrectDateRange()
        {
            // Arrange
            int year = 2025;
            int month = 1;
            int tenDays = 3;

            // Act
            var (startDate, endDate) = _attendanceBo.GetTendays(year, month, tenDays);

            // Assert
            Assert.Equal(new DateTime(2025, 1, 21, 0, 0, 0, DateTimeKind.Local), startDate);
            Assert.Equal(new DateTime(2025, 1, 31, 0, 0, 0, DateTimeKind.Local), endDate);
        }

        /// <summary>
        /// 測試不同月份的下旬日期範圍（以2月為例）
        /// </summary>
        [Fact]
        public void GetTendays_LastTenDaysFebruary_ReturnsCorrectDateRange()
        {
            // Arrange
            int year = 2025;
            int month = 2;
            int tenDays = 3;

            // Act
            var (startDate, endDate) = _attendanceBo.GetTendays(year, month, tenDays);

            // Assert
            Assert.Equal(new DateTime(2025, 2, 21, 0, 0, 0, DateTimeKind.Local), startDate);
            Assert.Equal(new DateTime(2025, 2, 28, 0, 0, 0, DateTimeKind.Local), endDate);
        }

        /// <summary>
        /// 測試取得開始和結束日期
        /// </summary>
        [Theory]
        [InlineData(2025, 0, 0, 1, 1, 12, 31)]  // 全年度
        [InlineData(2025, 1, 0, 1, 1, 1, 31)]  // 一月份
        [InlineData(2025, 1, 1, 1, 1, 1, 10)]  // 一月上旬
        [InlineData(2025, 1, 2, 1, 11, 1, 20)]  // 一月中旬
        [InlineData(2025, 1, 3, 1, 21, 1, 31)]  // 一月下旬
        [InlineData(2025, 2, 3, 2, 21, 2, 28)]  // 二月下旬
        public void GetStartEndDays_ShouldReturnCorrectDateRange(int year, int month, int tenDays,
            int expectedStartMonth, int expectedStartDay, int expectedEndMonth, int expectedEndDay)
        {
            // Arrange
            DateTime expectedStartDate = new DateTime(year, expectedStartMonth, expectedStartDay, 0, 0, 0, DateTimeKind.Local);
            DateTime expectedEndDate = new DateTime(year, expectedEndMonth, expectedEndDay, 0, 0, 0, DateTimeKind.Local);
            //var expected = (expectedStartDate, expectedEndDate);

            // Act
            var (actualStart, actualEnd) = _attendanceBo.GetStartEndDays(year, month, tenDays);

            // Assert
            Assert.Equal(expectedStartDate, actualStart);
            Assert.Equal(expectedEndDate, actualEnd);
        }

        /// <summary>
        /// 測試無效的年度參數
        /// </summary>
        [Fact]
        public void GetStartEndDays_WithInvalidYear_ShouldThrowArgumentException()
        {
            // Act & Assert
            var exception = Assert.Throws<ArgumentException>(() => _attendanceBo.GetStartEndDays(0, 1, 0));
            Assert.Equal("年度不可為空", exception.Message);
        }

        [Theory]
        [InlineData("0391", true)]
        [InlineData("0392", false)]
        [InlineData("0395", false)]
        [InlineData("1048", true)]
        [InlineData("0804", true)]
        [InlineData("2135", true)]
        public void IsAuthorizedToQueryDepartmentSentBoxTest(string empNo, bool expected)
        {
            bool result = _attendanceBo.IsAuthorizedToQueryDepartmentSentBox(empNo);
            Assert.Equal(expected, result);
        }

        //private (DateTime startDate, DateTime endDate) GetExpectedDateRange(int year, int month, int tenDays)
        //{
        //    if (tenDays == 0)
        //    {
        //        if (month == 0)
        //        {
        //            return (
        //                new DateTime(year, 1, 1, 0, 0, 0, DateTimeKind.Local),
        //                new DateTime(year, 12, 31, 0, 0, 0, DateTimeKind.Local)
        //            );
        //        }
        //        else
        //        {
        //            var startDate = new DateTime(year, month, 1);
        //            return (startDate, startDate.AddMonths(1).AddDays(-1));
        //        }
        //    }
        //    else
        //    {
        //        var startDate = new DateTime(year, month, (tenDays - 1) * 10 + 1);
        //        var endDate = tenDays == 3
        //            ? startDate.AddMonths(1).AddDays(-1)
        //            : startDate.AddDays(9);
        //        return (startDate, endDate);
        //    }
        //}
    }
}
