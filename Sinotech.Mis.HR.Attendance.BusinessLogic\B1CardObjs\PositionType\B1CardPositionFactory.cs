﻿using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Diagnostics.CodeAnalysis;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.PositionType
{
    [ExcludeFromCodeCoverage]
    public class B1CardPositionFactory
    {
        //private IB1CardDataProvider _provider;

        //public B1CardPositionFactory(IB1CardDataProvider provider)
        //{
        //    _provider = provider;
        //}

        public static B1CardPositionBase GetB1CardPositionObject(IB1CardDataProvider _provider)
        {
            var overtimeDate = _provider.OvertimeDate;
            var employeeNumber = _provider.EmployeeNumber;

            if (_provider.IsSpecialStaff())
            {
                return new SpecialStaff(employeeNumber, overtimeDate, _provider);
            }

            var position = _provider.GetPositionType();
            B1CardPositionBase? p = null;
            switch (position)
            {
                case B1CardPositionEnum.NotInList:
                    throw new ArgumentException($"{nameof(GetB1CardPositionObject)}: 找不到員工編號 {employeeNumber} 的資料!");

                case B1CardPositionEnum.Chairman:
                case B1CardPositionEnum.President:
                case B1CardPositionEnum.VicePresident:
                case B1CardPositionEnum.Manager:
                case B1CardPositionEnum.DeputyManager:
                case B1CardPositionEnum.Director:
                case B1CardPositionEnum.DeputyDirector:
                case B1CardPositionEnum.ProjectDirector:
                case B1CardPositionEnum.ProjectDeputyDirector:
                    p = new StaffOvertimeNotAllowed(employeeNumber, overtimeDate, _provider);
                    break;

                case B1CardPositionEnum.Driver:
                    p = new Driver(employeeNumber, overtimeDate, _provider);
                    break;

                case B1CardPositionEnum.ChiefAccountant:
                case B1CardPositionEnum.SectionChief:
                case B1CardPositionEnum.ProjectSectionChief:
                case B1CardPositionEnum.EnvLabChief:
                case B1CardPositionEnum.GeneralStaff:
                    p = new GeneralStaff(employeeNumber, overtimeDate, _provider);
                    break;

                default:
                    throw new ArgumentException($"{nameof(GetB1CardPositionObject)}: 員工 {employeeNumber} 不支援的職等 {position}!");
            }
            return p;
        }
    }
}