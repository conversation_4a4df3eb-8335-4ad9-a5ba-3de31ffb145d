﻿using System.Diagnostics.CodeAnalysis;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.Tests
{
    [ExcludeFromCodeCoverage]
    public class ProjectBoTests
    {
        private readonly ProjectBo _projectBo;

        public ProjectBoTests(ProjectBo projectBo)
        {
            _projectBo = projectBo;
        }

        //[Theory]
        //[InlineData("", true)]
        //[InlineData("9602C", true)]
        //[InlineData("RP19553", true)]
        //[InlineData("TI22017", false)]
        //public void GetEndDateTest(string projectNo, bool expected)
        //{
        //    DateTime? date = _projectBo.GetEndDate(projectNo);
        //    if(expected)
        //    {
        //        Assert.True(date == null);

        //    }
        //    else
        //    {
        //        Assert.NotNull(date);
        //    }
        //}
    }
}