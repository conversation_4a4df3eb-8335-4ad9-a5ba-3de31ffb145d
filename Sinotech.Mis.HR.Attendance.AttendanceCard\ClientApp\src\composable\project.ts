import { ref } from 'vue'
import type { ProjectType, ProjectApiType } from '../api/appType'
import { GET_OPENPROJECTSDATERANGE_URL } from '../api/appUrl'

/**
 * 計畫
 * @returns 
 */
export function useProject() {
  const projectData = ref<Array<ProjectType>>([])

  const onGetProjectsDateRange = async (startDate: Date, endDate: Date, deptNo: number, signal: AbortSignal): Promise<void> => {
    const params = new URLSearchParams({
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      deptNo: deptNo.toString()
    })
    try {
      const res: Response = await fetch(GET_OPENPROJECTSDATERANGE_URL + '?' + params, {
        method: 'GET',
        signal: signal
      })
      if (!res.ok) {
        throw new Error(res.status.toString())
      }
      const jsonData = await res.json()
      projectData.value = jsonData.map((e: ProjectApiType) => {
        return {
          id: e.PrjNo,
          name: e.PrjN<PERSON>
        }
      })
    } catch (err: unknown) {
      throw err
    }
  }
  
  return { projectData, onGetProjectsDateRange }
}