{"$schema": "https://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": true, "anonymousAuthentication": false, "iisExpress": {"applicationUrl": "http://localhost:56180", "sslPort": 0}}, "profiles": {"Sinotech.Mis.HR.Attendance.HomeInbox": {"commandName": "Project", "launchBrowser": true, "launchUrl": "Inbox?empNo=2268", "applicationUrl": "http://localhost:5238", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "weatherforecast", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}