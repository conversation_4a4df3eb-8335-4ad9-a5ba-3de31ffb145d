﻿using Sinotech.Mis.Helpers;
using Sinotech.Mis.HR.Attendance.DataAccess.Interfaces;
using Sinotech.Mis.HR.Attendance.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;


namespace Sinotech.Mis.HR.Attendance.DataAccess.Ado
{
    public class B1CardDao : IB1CardDao
    {

        /// <summary>
        /// The connection string
        /// </summary>
        private readonly string _connectionString;

        /// <summary>Initializes a new instance of the <see cref="AttendanceDao" /> class.</summary>
        /// <param name="connectionString">The connection string.</param>
        public B1CardDao(string connectionString)
        {
            _connectionString = connectionString;
        }

        /// <summary>
        /// 取得加班單
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public DataTable GetB1Card(DateTime date, string empNo)
        {
            string sql = "SELECT * FROM B1Card WHERE B1_Date=@Date AND B1_EmpNo=@EmpNO;";
            SqlParameter[] parameters = new SqlParameter[2];
            parameters[0] = new SqlParameter("@Date", SqlDbType.SmallDateTime);
            parameters[0].Value = date;
            parameters[1] = new SqlParameter("@EmpNO", SqlDbType.NVarChar, 4);
            parameters[1].Value = empNo;
            return SqlHelper.GetDataTable(_connectionString, sql, parameters);
        }

        /// <summary>
        /// 取得 表單關係人 某月份加班單
        /// </summary>
        /// <param name="empNo">表單關係人 員工編號</param>
        /// <param name="date">日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns>已填表單</returns>
        public DataTable GetB1CardMonth(string empNo, DateTime date, int? status = null)
        {
            List<SqlParameter> parameters = new List<SqlParameter>();
            string strSql = @"SELECT * FROM B1CARD WHERE B1_YYMM=@YYYMM AND B1_EMPNO=@EmpNo";

            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar, 5);
            parameterEmpNo.Value = empNo;
            parameters.Add(parameterEmpNo);

            string yyyMM = CardUtility.RocChineseYYYMM(date);
            SqlParameter parameterYYYMM = new SqlParameter("@YYYMM", SqlDbType.VarChar, 50);
            parameterYYYMM.Value = yyyMM;
            parameters.Add(parameterYYYMM);
            if (status != null)
            {
                strSql += @" AND B1_STATUS=@Status";
                SqlParameter parameterStatus = new SqlParameter("@Status", SqlDbType.Int);
                parameterStatus.Value = status;
                parameters.Add(parameterStatus);
            }
            strSql += " ORDER BY B1_YYMM,B1_SDD,B1_SHH;";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql, parameters);
            return dt;
        }

        /// <summary>
        /// 取得B1Cards
        /// </summary>
        /// <param name="formUID"></param>
        /// <returns></returns>
        public DataTable GetB1Cards(Guid formUID)
        {
            string sql = @"SELECT * FROM B1Card WHERE FormUID=@FormUID";
            SqlParameter parameter = new SqlParameter("@FormUID", SqlDbType.UniqueIdentifier);
            parameter.Value = formUID;
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameter);
            return dt;
        }

        /// <summary>
        /// 依日期區間取得所有加班卡
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public DataTable GetB1Cards(DateTime startDate, DateTime endDate)
        {
            string sql = @"SELECT * FROM B1Card WHERE B1_WDate >= @StartDate AND B1_WDate <= @EndDate ORDER BY ID;";
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            return dt;
        }

        /// <summary>
        /// 依日期區間取得某計畫所有加班卡
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="projNo"></param>
        /// <returns></returns>
        public DataTable GetB1Cards(DateTime startDate, DateTime endDate, string projNo)
        {
            DataTable dt = new DataTable();
            string sql = @"SELECT * FROM B1Card WHERE B1_WDate >= @StartDate AND B1_WDate <= @EndDate 
            AND B1_PROJNO like @ProjNo;";
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);
            SqlParameter parameterProjNo = new SqlParameter("@ProjNo", SqlDbType.VarChar, 10); // 為做 Like查詢要放寬
            parameterProjNo.Value = $"%{projNo}%";
            parameters.Add(parameterProjNo);
            dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            return dt;
        }

        /// <summary>
        /// 依日期區間取得所有表單及加班卡
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public DataTable GetB1CardsForms(DateTime startDate, DateTime endDate)
        {
            DataTable dt = new DataTable();
            string sql = @"SELECT B1CARD.ID,B1CARD.FormUID,B1CARD.B1_EMPNO,B1CARD.B1_YYMM,B1CARD.B1_PROJNO,
 B1CARD.B1_ITEMNO,B1CARD.B1_EXPNO,B1CARD.B1_SDD,B1CARD.B1_SHH,B1CARD.B1_SMM,
 B1CARD.B1_EDD,B1CARD.B1_EHH,B1CARD.B1_EMM,B1CARD.B1_HOUR,B1CARD.B1_CODE,
 B1CARD.B1_Reason,B1CARD.B1_WYYMMDD,B1CARD.B1_AYYMMDD,B1CARD.B1_STATUS,
 B1CARD.B1_SHEETNO,B1CARD.B1_SERIALNO,B1CARD.B1_SOURCE,B1CARD.B1_HOURLEFT,
 B1CARD.B1_StartDate,B1CARD.B1_EndDate,B1CARD.B1_DateTypeId,B1CARD.B1_WDate,B1CARD.B1_ADate,
 B1CARD.UpdatedEmpNo,B1CARD.UpdatedName,B1CARD.UpdatedTime,B1CARD.UpdatedIP,B1CARD.UpdatedHost,
 Form.FormID,Form.FormNo,Form.ID AS FormIntID,Form.FormSubject,Form.FormInfo,Form.EmpNo,Form.EmpName,
 Form.DeptNo,Form.DeptSName,Form.TeamID,Form.TeamCName,Form.RankNo,Form.RankName,Form.JobNo,
 Form.JobName,Form.ContentStartTime,Form.ContentEndTime,Form.CreatedEmpNo,Form.CreatedName,
 Form.FilledTime,Form.CreatedTime,Form.CreatedIP,Form.CreatedHost,Form.AddedSigner,Form.StartTime,
 Form.EndTime,Form.FormStatus,Form.TotalSteps,Form.CurrentStep,Form.UpdatedEmpNo AS FormUpdatedEmpNo,
 Form.UpdatedName AS FormUpdatedName,Form.UpdatedTime AS FormUpdatedTime,FormFlow.ID AS FormFlowID,
 Form.UpdatedIP AS FormUpdatedIP,Form.UpdatedHost AS FormUpdatedHost,FormStatus.Name AS FormStatusName,
 FormFlowStatus.Name AS FlowStatusName,FormFlow.FlowUID,FormFlow.RecipientEmpNo,
 FormFlow.RecipientName,FormFlow.RecipientDeptNo,FormFlow.RecipientDeptSName,FormFlow.RecipientTeamID,
 FormFlow.RecipientTeamCName,FormFlow.FlowName,FormFlow.Step,FormFlow.ApproverEmpNo,
 FormFlow.ApproverName,FormFlow.ApproverDeptNo,FormFlow.ApproverDeptSName,FormFlow.ApproverTeamID,
 FormFlow.ApproverTeamCName,FormFlow.ApproveTime,FormFlow.ApproveIP,FormFlow.ApproveHost,
 FormFlow.IsAgentApprove,FormFlow.FlowStatus,FormFlow.IsNotification,FormAttachment.FileDirectory,
 FormAttachment.OriginalFileName,FormAttachment.EncodedFileName,FormAttachment.ID AS AttachmentID
 FROM B1CARD INNER JOIN Form ON B1CARD.FormUID = Form.FormUID INNER JOIN
 FormFlow ON Form.FormUID = FormFlow.FormUID INNER JOIN
 FormStatus ON B1CARD.B1_STATUS = FormStatus.ID AND Form.FormStatus = FormStatus.ID INNER JOIN
 FormFlowStatus ON FormFlow.FlowStatus = FormFlowStatus.ID LEFT JOIN
 FormAttachment ON Form.FormUID = FormAttachment.FormUID
 WHERE (CONVERT(DATE,B1CARD.B1_WDate) >= CONVERT(DATE,@StartDate)) AND (CONVERT(DATE,B1CARD.B1_WDate) <= CONVERT(DATE,@EndDate)) 
 ORDER BY B1CARD.ID,B1CARD.B1_SERIALNO,FormFlow.Step;";
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);
            dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            return dt;
        }

        /// <summary>
        /// 依日期區間取得某計畫所有表單及加班卡
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="projNo"></param>
        /// <returns></returns>
        public DataTable GetB1CardsForms(DateTime startDate, DateTime endDate, string projNo)
        {
            string sql = @"SELECT B1CARD.ID,B1CARD.FormUID,B1CARD.B1_EMPNO,B1CARD.B1_YYMM,B1CARD.B1_PROJNO,
 B1CARD.B1_ITEMNO,B1CARD.B1_EXPNO,B1CARD.B1_SDD,B1CARD.B1_SHH,B1CARD.B1_SMM,
 B1CARD.B1_EDD,B1CARD.B1_EHH,B1CARD.B1_EMM,B1CARD.B1_HOUR,B1CARD.B1_CODE,
 B1CARD.B1_Reason,B1CARD.B1_WYYMMDD,B1CARD.B1_AYYMMDD,B1CARD.B1_STATUS,
 B1CARD.B1_SHEETNO,B1CARD.B1_SERIALNO,B1CARD.B1_SOURCE,B1CARD.B1_HOURLEFT,
 B1CARD.B1_StartDate,B1CARD.B1_EndDate,B1CARD.B1_DateTypeId,B1CARD.B1_WDate,B1CARD.B1_ADate,
 B1CARD.UpdatedEmpNo,B1CARD.UpdatedName,B1CARD.UpdatedTime,B1CARD.UpdatedIP,B1CARD.UpdatedHost,
 Form.FormID,Form.FormNo,Form.ID AS FormIntID,Form.FormSubject,Form.FormInfo,Form.EmpNo,Form.EmpName,
 Form.DeptNo,Form.DeptSName,Form.TeamID,Form.TeamCName,Form.RankNo,Form.RankName,Form.JobNo,
 Form.JobName,Form.ContentStartTime,Form.ContentEndTime,Form.CreatedEmpNo,Form.CreatedName,
 Form.FilledTime,Form.CreatedTime,Form.CreatedIP,Form.CreatedHost,Form.AddedSigner,Form.StartTime,
 Form.EndTime,Form.FormStatus,Form.TotalSteps,Form.CurrentStep,Form.UpdatedEmpNo AS FormUpdatedEmpNo,
 Form.UpdatedName AS FormUpdatedName,Form.UpdatedTime AS FormUpdatedTime,FormFlow.ID AS FormFlowID,
 Form.UpdatedIP AS FormUpdatedIP,Form.UpdatedHost AS FormUpdatedHost,FormStatus.Name AS FormStatusName,
 FormFlowStatus.Name AS FlowStatusName,FormFlow.FlowUID,FormFlow.RecipientEmpNo,
 FormFlow.RecipientName,FormFlow.RecipientDeptNo,FormFlow.RecipientDeptSName,FormFlow.RecipientTeamID,
 FormFlow.RecipientTeamCName,FormFlow.FlowName,FormFlow.Step,FormFlow.ApproverEmpNo,
 FormFlow.ApproverName,FormFlow.ApproverDeptNo,FormFlow.ApproverDeptSName,FormFlow.ApproverTeamID,
 FormFlow.ApproverTeamCName,FormFlow.ApproveTime,FormFlow.ApproveIP,FormFlow.ApproveHost,
 FormFlow.IsAgentApprove,FormFlow.FlowStatus,FormFlow.IsNotification,FormAttachment.FileDirectory,
 FormAttachment.OriginalFileName,FormAttachment.EncodedFileName,FormAttachment.ID AS AttachmentID
 FROM B1CARD INNER JOIN Form ON B1CARD.FormUID = Form.FormUID INNER JOIN
 FormFlow ON Form.FormUID = FormFlow.FormUID INNER JOIN
 FormStatus ON B1CARD.B1_STATUS = FormStatus.ID AND Form.FormStatus = FormStatus.ID INNER JOIN
 FormFlowStatus ON FormFlow.FlowStatus = FormFlowStatus.ID LEFT JOIN
 FormAttachment ON Form.FormUID = FormAttachment.FormUID
 WHERE (CONVERT(DATE,B1CARD.B1_WDate) >= CONVERT(DATE,@StartDate)) AND (CONVERT(DATE,B1CARD.B1_WDate) <= CONVERT(DATE,@EndDate)) 
 AND (B1CARD.B1_PROJNO like @ProjNo)
 ORDER BY B1CARD.ID,B1CARD.B1_SERIALNO,FormFlow.Step;";
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);
            SqlParameter parameterProjNo = new SqlParameter("@ProjNo", SqlDbType.VarChar, 10);// 為做 Like查詢要放寬
            parameterProjNo.Value = $"%{projNo}%";
            parameters.Add(parameterProjNo);
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            return dt;
        }

        /// <summary>
        /// 取得加班類型列表 B1Code
        /// </summary>
        /// <returns></returns>
        public DataTable GetB1CardTypes()
        {
            string sql = @"SELECT ID,Name FROM B1CODE ORDER BY ID ASC;";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql);
            return dt;
        }

        /// <summary>
        /// 取得 填表人/表單關係人 某月份已填加班單
        /// </summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <param name="status">表單狀態</param>
        /// <returns>已填表單</returns>
        public DataTable GetSentB1CardYearMonth(string empNo, int year, int month, int? status = null)
        {
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar, 5);
            parameterEmpNo.Value = empNo;
            parameters.Add(parameterEmpNo);
            string strSql = @"SELECT B1CARD.ID,B1CARD.FormUID,B1CARD.B1_EMPNO,B1CARD.B1_YYMM,B1CARD.B1_PROJNO,
 B1CARD.B1_ITEMNO,B1CARD.B1_EXPNO,B1CARD.B1_SDD,B1CARD.B1_SHH,B1CARD.B1_SMM,
 B1CARD.B1_EDD,B1CARD.B1_EHH,B1CARD.B1_EMM,B1CARD.B1_HOUR,B1CARD.B1_CODE,
 B1CARD.B1_Reason,B1CARD.B1_WYYMMDD,B1CARD.B1_AYYMMDD,B1CARD.B1_STATUS,
 B1CARD.B1_SHEETNO,B1CARD.B1_SERIALNO,B1CARD.B1_SOURCE,B1CARD.B1_HOURLEFT,
 B1CARD.B1_StartDate,B1CARD.B1_EndDate,B1CARD.B1_DateTypeId,B1CARD.B1_WDate,B1CARD.B1_ADate,
 B1CARD.UpdatedEmpNo,B1CARD.UpdatedName,B1CARD.UpdatedTime,B1CARD.UpdatedIP,B1CARD.UpdatedHost,
 Form.FormID,Form.FormNo,Form.ID AS FormIntID,Form.FormSubject,Form.FormInfo,Form.EmpNo,Form.EmpName,
 Form.DeptNo,Form.DeptSName,Form.TeamID,Form.TeamCName,Form.RankNo,Form.RankName,Form.JobNo,
 Form.JobName,Form.ContentStartTime,Form.ContentEndTime,Form.CreatedEmpNo,Form.CreatedName,
 Form.FilledTime,Form.CreatedTime,Form.CreatedIP,Form.CreatedHost,Form.AddedSigner,Form.StartTime,
 Form.EndTime,Form.FormStatus,Form.TotalSteps,Form.CurrentStep,Form.UpdatedEmpNo AS FormUpdatedEmpNo,
 Form.UpdatedName AS FormUpdatedName,Form.UpdatedTime AS FormUpdatedTime,FormFlow.ID AS FormFlowID,
 Form.UpdatedIP AS FormUpdatedIP,Form.UpdatedHost AS FormUpdatedHost,FormStatus.Name AS FormStatusName,
 FormFlowStatus.Name AS FlowStatusName,FormFlow.FlowUID,FormFlow.RecipientEmpNo,
 FormFlow.RecipientName,FormFlow.RecipientDeptNo,FormFlow.RecipientDeptSName,FormFlow.RecipientTeamID,
 FormFlow.RecipientTeamCName,FormFlow.FlowName,FormFlow.Step,FormFlow.ApproverEmpNo,
 FormFlow.ApproverName,FormFlow.ApproverDeptNo,FormFlow.ApproverDeptSName,FormFlow.ApproverTeamID,
 FormFlow.ApproverTeamCName,FormFlow.ApproveTime,FormFlow.ApproveIP,FormFlow.ApproveHost,
 FormFlow.IsAgentApprove,FormFlow.FlowStatus,FormFlow.IsNotification,FormAttachment.FileDirectory,
 FormAttachment.OriginalFileName,FormAttachment.EncodedFileName,FormAttachment.ID AS AttachmentID
 FROM B1CARD INNER JOIN Form ON B1CARD.FormUID = Form.FormUID 
 LEFT JOIN FormFlow ON Form.FormUID = FormFlow.FormUID 
 INNER JOIN FormStatus ON B1CARD.B1_STATUS = FormStatus.ID AND Form.FormStatus = FormStatus.ID 
 LEFT JOIN FormFlowStatus ON FormFlow.FlowStatus = FormFlowStatus.ID 
 LEFT JOIN FormAttachment ON Form.FormUID = FormAttachment.FormUID
 WHERE ((Form.CreatedEmpNo = @EmpNo) OR (Form.EmpNo = @EmpNo)) AND (Form.FormInfo LIKE @FormInfo)";

            int rocYear = CardUtility.RocChineseYear(year);
            string formInfo = $"{rocYear}_{month.ToString("00")}%";
            SqlParameter parameterFormInfo = new SqlParameter("@FormInfo", SqlDbType.VarChar, 50);
            parameterFormInfo.Value = formInfo;
            parameters.Add(parameterFormInfo);
            if (status != null)
            {
                strSql += @" AND Form.FormStatus=@Status";
                SqlParameter parameterStatus = new SqlParameter("@Status", SqlDbType.Int);
                parameterStatus.Value = status;
                parameters.Add(parameterStatus);
            }
            strSql += " ORDER BY FormInfo,Step;";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql, parameters);
            return dt;
        }
        /// <summary>
        /// 是否已填該日的加班卡，也就是 B1_Status 為1或2
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public bool IsFilled(DateTime date, string empNo)
        {
            string sql = @"SELECT *FROM B1CARD WHERE (B1_YYMM = @B1_YYMM) AND (B1_SDD = @B1_DD) 
                            AND (B1_EMPNO = @EmpNo) AND (B1_STATUS IN (1,2));";
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parametersB1_YYMM = new SqlParameter("@B1_YYMM", SqlDbType.Char, 5);
            parametersB1_YYMM.Value = CardUtility.RocChineseYYYMM(date);
            parameters.Add(parametersB1_YYMM);
            SqlParameter parametersB1_DD = new SqlParameter("@B1_DD", SqlDbType.Char, 2);
            parametersB1_DD.Value = date.ToString("dd");
            parameters.Add(parametersB1_DD);
            SqlParameter parametersEmpNo = new SqlParameter("@EmpNO", SqlDbType.Char, 4);
            parametersEmpNo.Value = empNo;
            parameters.Add(parametersEmpNo);

            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            bool ret = dt.Rows.Count > 0;
            return ret;
        }

        /// <summary>
        /// 是否已填該日的加班單，也就是 B1_Status 為1或2
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public DataTable IsFilledB1Card(DateTime date, string empNo)
        {
            string sql = @"SELECT *FROM B1CARD WHERE (B1_YYMM = @B1_YYMM) AND (B1_SDD = @B1_DD) 
                            AND (B1_EMPNO = @EmpNo) AND (B1_STATUS IN (1,2));";
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parametersB1_YYMM = new SqlParameter("@B1_YYMM", SqlDbType.Char, 5);
            parametersB1_YYMM.Value = CardUtility.RocChineseYYYMM(date);
            parameters.Add(parametersB1_YYMM);
            SqlParameter parametersB1_DD = new SqlParameter("@B1_DD", SqlDbType.Char, 2);
            parametersB1_DD.Value = date.ToString("dd");
            parameters.Add(parametersB1_DD);
            SqlParameter parametersEmpNo = new SqlParameter("@EmpNO", SqlDbType.Char, 4);
            parametersEmpNo.Value = empNo;
            parameters.Add(parametersEmpNo);

            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            return dt;
        }

    }
}
