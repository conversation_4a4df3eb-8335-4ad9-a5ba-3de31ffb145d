﻿using System;
using System.Collections.Generic;
using System.Data;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>表單流程</summary>
    public class FormFlow
    {

        /// <summary>
        /// 產生空的 User Defined DataTable
        /// </summary>
        /// <returns></returns>
        private static DataTable UserDefinedDataTableSchema()
        {
            DataTable dtFormFlow = new DataTable("FormFlow");
            dtFormFlow.Columns.Add("FlowUID", typeof(Guid));
            dtFormFlow.Columns.Add("FormUID", typeof(Guid));
            dtFormFlow.Columns.Add("RecipientEmpNo", typeof(string));
            dtFormFlow.Columns.Add("RecipientName", typeof(string));
            dtFormFlow.Columns.Add("RecipientDeptNo", typeof(int));
            dtFormFlow.Columns.Add("RecipientDeptSName", typeof(string));
            dtFormFlow.Columns.Add("RecipientTeamID", typeof(int));
            dtFormFlow.Columns.Add("RecipientTeamCName", typeof(string));
            dtFormFlow.Columns.Add("FlowName", typeof(string));
            dtFormFlow.Columns.Add("Step", typeof(int));
            dtFormFlow.Columns.Add("ApproverEmpNo", typeof(string));
            dtFormFlow.Columns.Add("ApproverName", typeof(string));
            dtFormFlow.Columns.Add("ApproverDeptNo", typeof(int));
            dtFormFlow.Columns.Add("ApproverDeptSName", typeof(string));
            dtFormFlow.Columns.Add("ApproverTeamID", typeof(int));
            dtFormFlow.Columns.Add("ApproverTeamCName", typeof(string));
            dtFormFlow.Columns.Add("ApproveTime", typeof(DateTime));
            dtFormFlow.Columns.Add("ApproveIP", typeof(string));
            dtFormFlow.Columns.Add("ApproveHost", typeof(string));
            dtFormFlow.Columns.Add("IsAgentApprove", typeof(bool));
            dtFormFlow.Columns.Add("FlowStatus", typeof(int));
            dtFormFlow.Columns.Add("IsNotification", typeof(bool));
            dtFormFlow.Columns.Add("ApproveComments", typeof(string));
            return dtFormFlow;
        }

        /// <summary>DataRow轉為FormFlow</summary>
        /// <param name="row">The row.</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public static FormFlow DataRowToFormFlow(DataRow row)
        {
            FormFlow formFlow = new FormFlow();
            formFlow.FlowUID = (Guid)row["FlowUID"];
            formFlow.FormUID = (Guid)row["FormUID"];
            formFlow.RecipientEmpNo = (string)row["RecipientEmpNo"];
            formFlow.RecipientName = (string)row["RecipientName"];
            formFlow.RecipientDeptNo = (int)row["RecipientDeptNo"];
            formFlow.RecipientDeptSName = (string)row["RecipientDeptSName"];
            if (row["RecipientTeamID"] != null && row["RecipientTeamID"] != DBNull.Value)
            {
                formFlow.RecipientTeamID = (int?)row["RecipientTeamID"];
            }
            if (row["RecipientTeamCName"] != null && row["RecipientTeamCName"] != DBNull.Value)
            {
                formFlow.RecipientTeamCName = (string?)row["RecipientTeamCName"];
            }
            formFlow.FlowName = (string)row["FlowName"];
            formFlow.Step = (int)(byte)row["Step"];
            if (row["ApproverEmpNo"] != null && row["ApproverEmpNo"] != DBNull.Value)
            {
                formFlow.ApproverEmpNo = (string)row["ApproverEmpNo"];
            }
            if (row["ApproverName"] != null && row["ApproverName"] != DBNull.Value)
            {
                formFlow.ApproverName = (string)row["ApproverName"];
            }
            if (row["ApproverDeptNo"] != null && row["ApproverDeptNo"] != DBNull.Value)
            {
                formFlow.ApproverDeptNo = (int)row["ApproverDeptNo"];
            }
            if (row["ApproverDeptSName"] != null && row["ApproverDeptSName"] != DBNull.Value)
            {
                formFlow.ApproverDeptSName = (string)row["ApproverDeptSName"];
            }
            if (row["ApproverTeamID"] != null && row["ApproverTeamID"] != DBNull.Value)
            {
                formFlow.ApproverTeamID = (int?)row["ApproverTeamID"];
            }
            if (row["ApproverTeamCName"] != null && row["ApproverTeamCName"] != DBNull.Value)
            {
                formFlow.ApproverTeamCName = (string?)row["ApproverTeamCName"];
            }
            if (row["ApproveTime"] != null && row["ApproveTime"] != DBNull.Value)
            {
                formFlow.ApproveTime = (DateTime?)row["ApproveTime"];
            }
            if (row["ApproveIP"] != null && row["ApproveIP"] != DBNull.Value)
            {
                formFlow.ApproveIP = (string?)row["ApproveIP"];
            }
            if (row["ApproveHost"] != null && row["ApproveHost"] != DBNull.Value)
            {
                formFlow.ApproveHost = (string?)row["ApproveHost"];
            }
            if (row["IsAgentApprove"] != null && row["IsAgentApprove"] != DBNull.Value)
            {
                formFlow.IsAgentApprove = (bool)row["IsAgentApprove"];
            }
            formFlow.FlowStatus = (int)(byte)row["FlowStatus"];
            if (row["FlowStatusName"] != null && row["FlowStatusName"] != DBNull.Value)
            {
                formFlow.FlowStatusName = (string?)row["FlowStatusName"];
            }
            formFlow.IsNotification = (bool)row["IsNotification"];
            return formFlow;
        }

        /// <summary>
        /// 轉換為 FormFlowDto DataRow
        /// </summary>
        /// <returns></returns>
        public DataRow ToUserDefinedDataRow()
        {
            DataTable dtFormFlow = UserDefinedDataTableSchema();
            DataRow row = dtFormFlow.NewRow();
            row["FlowUID"] = FlowUID;
            row["FormUID"] = FormUID;
            row["RecipientEmpNo"] = RecipientEmpNo;
            row["RecipientName"] = RecipientName;
            row["RecipientDeptNo"] = RecipientDeptNo;
            row["RecipientDeptSName"] = RecipientDeptSName;
            if (RecipientTeamID != null)
            {
                row["RecipientTeamID"] = RecipientTeamID;
            }
            else
            {
                row["RecipientTeamID"] = DBNull.Value;
            }
            if (RecipientTeamCName != null)
            {
                row["RecipientTeamCName"] = RecipientTeamCName;
            }
            else
            {
                row["RecipientTeamCName"] = DBNull.Value;
            }
            row["FlowName"] = FlowName;
            row["Step"] = Step;
            row["ApproverEmpNo"] = ApproverEmpNo;
            row["ApproverName"] = ApproverName;
            row["ApproverDeptNo"] = ApproverDeptNo;
            row["ApproverDeptSName"] = ApproverDeptSName;
            if (ApproverTeamID != null)
            {
                row["ApproverTeamID"] = ApproverTeamID;
            }
            else
            {
                row["ApproverTeamID"] = DBNull.Value;
            }
            if (ApproverTeamCName != null)
            {
                row["ApproverTeamCName"] = ApproverTeamCName;
            }
            else
            {
                row["ApproverTeamCName"] = DBNull.Value;
            }
            if (ApproveTime != null)
            {
                row["ApproveTime"] = ApproveTime;
            }
            else
            {
                row["ApproveTime"] = DBNull.Value;
            }
            if (ApproveIP != null)
            {
                row["ApproveIP"] = ApproveIP;
            }
            else
            {
                row["ApproveIP"] = DBNull.Value;
            }
            if (ApproveHost != null)
            {
                row["ApproveHost"] = ApproveHost;
            }
            else
            {
                row["ApproveHost"] = DBNull.Value;
            }
            if (IsNotification)
            {
                row["IsAgentApprove"] = DBNull.Value;
                row["ApproverEmpNo"] = DBNull.Value;
                row["ApproverName"] = DBNull.Value;
                row["ApproverDeptNo"] = DBNull.Value;
                row["ApproverDeptSName"] = DBNull.Value;
                row["ApproverTeamID"] = DBNull.Value;
                row["ApproverTeamCName"] = DBNull.Value;
                row["ApproveTime"] = DBNull.Value;
                row["ApproveTime"] = DBNull.Value;
                row["ApproveIP"] = DBNull.Value;
                row["ApproveHost"] = DBNull.Value;
            }
            else
            {
                row["IsAgentApprove"] = IsAgentApprove;
            }
            row["FlowStatus"] = FlowStatus;
            row["IsNotification"] = IsNotification;
            row["ApproveComments"] = ApproveComments;
            return row;
        }

        /// <summary>
        /// 轉換為 FormFlowDto
        /// </summary>
        /// <returns></returns>
        public DataTable ToUserDefinedDataTable()
        {
            DataTable dtFormFlow = UserDefinedDataTableSchema();
            DataRow row = ToUserDefinedDataRow();
            DataRow newRow = dtFormFlow.NewRow();
            object?[]? cloned = row.ItemArray.Clone() as object?[];
            if (cloned != null)
            {
                newRow.ItemArray = cloned;
            }
            dtFormFlow.Rows.Add(newRow);
            return dtFormFlow;
        }


        /// <summary>
        /// 轉換為 FormFlowDto
        /// </summary>
        /// <returns></returns>
        public static DataTable ToUserDefinedDataTable(List<FormFlow> flows)
        {
            DataTable dtFormFlow = UserDefinedDataTableSchema();
            foreach (FormFlow flow in flows)
            {
                DataRow row = flow.ToUserDefinedDataRow();
                DataRow newRow = dtFormFlow.NewRow();
                object?[]? cloned = row.ItemArray.Clone() as object?[];
                if (cloned != null)
                {
                    newRow.ItemArray = cloned;
                }
                dtFormFlow.Rows.Add(newRow);
            }
            return dtFormFlow;
        }

        /// <summary>
        /// 簽核來源電腦名稱
        /// </summary>
        public string? ApproveHost { get; set; }

        /// <summary>
        /// 簽核來源IP位址
        /// </summary>
        public string? ApproveIP { get; set; }

        /// <summary>
        /// 簽核人部門編號
        /// </summary>
        public int ApproverDeptNo { get; set; }

        /// <summary>
        /// 簽核人部門簡稱
        /// </summary>
        public string ApproverDeptSName { get; set; } = string.Empty;

        /// <summary>
        /// 簽核人員工編號
        /// </summary>
        public string ApproverEmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 簽核人姓名
        /// </summary>
        public string ApproverName { get; set; } = string.Empty;

        /// <summary>
        /// 簽核人組別名稱
        /// </summary>
        public string? ApproverTeamCName { get; set; }

        /// <summary>
        /// 簽核人組別編號
        /// </summary>
        public int? ApproverTeamID { get; set; }

        /// <summary>
        /// 簽核日期時間
        /// </summary>
        public DateTime? ApproveTime { get; set; }

        /// <summary>
        /// 流程關卡名稱
        /// </summary>
        public string FlowName { get; set; } = string.Empty;

        /// <summary>
        /// 流程關卡狀態  0:未傳送,1:簽核中, 2:同意, 3:不同意, 4:已抽單, 5: 已通知
        /// </summary>
        public int FlowStatus { get; set; }

        /// <summary>
        /// 流程關卡狀態名稱
        /// </summary>
        public string? FlowStatusName { get; set; } = null;

        /// <summary>
        /// 流程識別碼
        /// </summary>
        public Guid FlowUID { get; set; }

        /// <summary>
        /// 表單識別碼
        /// </summary>
        public Guid FormUID { get; set; }
        /// <summary>
        /// 流水號
        /// </summary>
        public int? ID { get; set; }

        /// <summary>
        /// 是否為代理人簽核
        /// </summary>
        public bool IsAgentApprove { get; set; } = false;

        /// <summary>
        /// 是否為通知關卡
        /// </summary>
        public bool IsNotification { get; set; } = false;

        /// <summary>
        /// 預設收件人部門編號
        /// </summary>
        public int RecipientDeptNo { get; set; }

        /// <summary>
        /// 預設收件人部門簡稱
        /// </summary>
        public string RecipientDeptSName { get; set; } = string.Empty;

        /// <summary>
        /// 收件人員工編號或角色
        /// </summary>
        public string RecipientEmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 預設收件人或角色名稱
        /// </summary>
        public string RecipientName { get; set; } = string.Empty;

        /// <summary>
        /// 預設收件人組別名稱
        /// </summary>
        public string? RecipientTeamCName { get; set; }

        /// <summary>
        /// 預設收件人組別編號
        /// </summary>
        public int? RecipientTeamID { get; set; }

        /// <summary>
        /// 目前關卡數/第幾關
        /// </summary>
        public int Step { get; set; }

        /// <summary>
        /// 簽核意見
        /// </summary>
        public string ApproveComments { get; set; } = string.Empty;

    }
}
