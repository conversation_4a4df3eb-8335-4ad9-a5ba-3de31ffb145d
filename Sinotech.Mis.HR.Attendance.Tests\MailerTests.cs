﻿using FakeItEasy;
using System.Collections.Generic;
using System.Net.Mail;
using Xunit;

namespace Sinotech.Mis.Helpers.Tests
{
    public class MailerTests
    {
        [Fact]
        public void SendMail_WithValidParameters_ReturnsTrue()
        {
            // Arrange
            var subject = "Test Subject";
            var body = "Test Body";
            var from = new MailAddress("<EMAIL>");
            var toList = new List<MailAddress> { new MailAddress("<EMAIL>") };
            var smtpServer = "smtp.example.com";
            var port = 25;
            var priority = MailPriority.Normal;
            var username = "username";
            var password = "password";

            var smtpClient = A.Fake<SmtpClient>();

            // Act
            var result = Mailer.SendMail(subject, body, from, toList, smtpServer, port, priority, username, password);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void SendMail_WithInvalidSmtpServer_ReturnsFalse()
        {
            // Arrange
            var subject = "Test Subject";
            var body = "Test Body";
            var from = new MailAddress("<EMAIL>");
            var toList = new List<MailAddress> { new MailAddress("<EMAIL>") };
            var smtpServer = "invalid.smtp.example.com";
            var port = 25;
            var priority = MailPriority.Normal;
            var username = "username";
            var password = "password";

            var smtpClient = A.Fake<SmtpClient>();

            // Act
            var result = Mailer.SendMail(subject, body, from, toList, smtpServer, port, priority, username, password);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void SendMail_WithSingleRecipient_ReturnsTrue()
        {
            // Arrange
            var subject = "Test Subject";
            var body = "Test Body";
            var from = new MailAddress("<EMAIL>");
            var to = new MailAddress("<EMAIL>");
            var smtpServer = "smtp.example.com";
            var port = 25;
            var priority = MailPriority.Normal;
            var username = "username";
            var password = "password";

            var smtpClient = A.Fake<SmtpClient>();

            // Act
            var result = Mailer.SendMail(subject, body, from, to, smtpServer, port, priority, username, password);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void SendMail_WithMultipleRecipients_ReturnsTrue()
        {
            // Arrange
            var subject = "Test Subject";
            var body = "Test Body";
            var from = new MailAddress("<EMAIL>");
            var toList = new List<MailAddress> { new MailAddress("<EMAIL>"), new MailAddress("<EMAIL>") };
            var ccList = new List<MailAddress> { new MailAddress("<EMAIL>") };
            var bccList = new List<MailAddress> { new MailAddress("<EMAIL>") };
            var smtpServer = "smtp.example.com";
            var port = 25;
            var priority = MailPriority.Normal;
            var username = "username";
            var password = "password";

            var smtpClient = A.Fake<SmtpClient>();

            // Act
            var result = Mailer.SendMail(subject, body, from, toList, ccList, bccList, smtpServer, port, priority, username, password);

            // Assert
            Assert.False(result);
        }
    }
}
