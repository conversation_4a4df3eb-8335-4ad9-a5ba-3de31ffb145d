import { ref } from 'vue'
import { GET_DEPARTMENTS_URL } from '../api/appUrl'
import type { DeptType, DeptApiType } from '../api/appType'

export function useDepartment() {
  const deptOptions = ref<Array<DeptType>>([])

  const onGetDeptOptions = async (signal: AbortSignal): Promise<void> => {
    const res: Response = await fetch(GET_DEPARTMENTS_URL, {
      method: 'GET',
      signal: signal
    })
    if (!res.ok) {
      throw new Error(res.status.toString())
    }

    const jsonData = await res.json()
    deptOptions.value = deptOptions.value.concat(
      jsonData.map((e: DeptApiType) => {
        return {
          deptNo: e.DeptNo,
          deptSName: e.DeptSName
        }
      })
    )
  }
    
  return { deptOptions, onGetDeptOptions }
}