﻿using Microsoft.Extensions.Configuration;
using Sinotech.Mis.Extensions.Configuration;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.DataAccess.Interfaces;
using Sinotech.Mis.HR.Attendance.Tests;
using System;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using Xunit;
#nullable enable

namespace Sinotech.Mis.HR.Attendance.DataAccess.Ado.Tests
{
    [ExcludeFromCodeCoverage]
    public class AccountDaoTests
    {
        /// <summary>
        /// Inject IAccountDao
        /// </summary>
        private readonly IAccountDao _accountDao;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="accountDao"></param>
        public AccountDaoTests(IAccountDao accountDao)
        {
            _accountDao = accountDao;
            IConfiguration configuration = new ConfigurationBuilder().
                 AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).Build();
            string ConnectionStringAttendance = configuration.GetSecuredConnectionString("Attendance");
            TestHelper.ClearData(ConnectionStringAttendance);
        }

        /// <summary>
        /// 測試登入失敗次數
        /// </summary>
        /// <param name="account"></param>
        /// <param name="expect"></param>
        [Theory]
        [InlineData("test", 0)]
        [InlineData("0395", 0)]
        [InlineData("0349", 0)]
        [InlineData("2268", 0)]
        public void GetLoginFailCountTest(string account, int expect)
        {
            // Act
            int result = _accountDao.GetLoginFailCount(account);
            // Assert
            Assert.Equal(expect, result);
        }

        /// <summary>
        /// 測試登入失敗紀錄
        /// </summary>
        /// <param name="account"></param>
        /// <param name="expect"></param>
        [Theory]
        [InlineData("test", 0)]
        [InlineData("0395", 0)]
        [InlineData("0349", 0)]
        [InlineData("2268", 0)]
        public void GetLoginFailRecordTest(string account, int expect)
        {
            // Act
            DataRow? dataRow = _accountDao.GetLoginFailRecord(account);
            int actual = dataRow == null ? 0 : (int)dataRow["FailCount"];
            // Assert
            Assert.Equal(expect, actual);
        }

        /// <summary>
        /// 測試記錄登入失敗
        /// </summary>
        /// <param name="empNo"></param>
        /// <param name="expect"></param>
        [Theory]
        [InlineData("test", true)]
        [InlineData("0395", true)]
        [InlineData("0349", true)]
        [InlineData("2268", true)]
        public void LogAccountLoginFailTest(string empNo, bool expect)
        {
            AccountLogInOutDto dto = new AccountLogInOutDto();
            dto.EmpNo = empNo;
            dto.Action = AccountLogInOutAction.Logout;
            dto.IP = "127.0.0.1";
            dto.ActionTime = DateTime.Now;
            DateTime firstLoginTime = dto.ActionTime;
            bool actual = _accountDao.LogAccountLoginFail(dto, firstLoginTime, null, 1);
            Assert.Equal(expect, actual);
        }

        /// <summary>
        /// 測試記錄登入
        /// </summary>
        /// <param name="empNo"></param>
        /// <param name="expect"></param>
        [Theory]
        [InlineData("test", true)]
        [InlineData("0395", true)]
        [InlineData("0349", true)]
        [InlineData("2268", true)]
        public void LogAccountLoginTest(string empNo, bool expect)
        {
            AccountLogInOutDto dto = new AccountLogInOutDto();
            dto.EmpNo = empNo;
            dto.Action = AccountLogInOutAction.Logout;
            dto.IP = "127.0.0.1";
            dto.ActionTime = DateTime.Now;
            bool actual = _accountDao.LogAccountLogin(dto);
            Assert.Equal(expect, actual);
        }

        /// <summary>
        /// 測試記錄登出
        /// </summary>
        /// <param name="empNo"></param>
        /// <param name="expect"></param>
        [Theory]
        [InlineData("test", true)]
        [InlineData("0395", true)]
        [InlineData("0349", true)]
        [InlineData("2268", true)]
        public void LogAccountLogoutTest(string empNo, bool expect)
        {
            AccountLogInOutDto dto = new AccountLogInOutDto();
            dto.EmpNo = empNo;
            dto.Action = AccountLogInOutAction.Logout;
            dto.IP = "127.0.0.1";
            dto.ActionTime = DateTime.Now;

            bool actual = _accountDao.LogAccountLogout(dto);
            Assert.Equal(expect, actual);
        }

        /// <summary>
        /// 測試記錄帳號解鎖
        /// </summary>
        /// <param name="empNo"></param>
        /// <param name="expect"></param>
        [Theory]
        [InlineData("test", true)]
        [InlineData("0395", true)]
        [InlineData("0349", true)]
        [InlineData("2268", true)]
        public void LogAccountUnlockTest(string empNo, bool expect)
        {
            AccountLogInOutDto dto = new AccountLogInOutDto();
            dto.EmpNo = empNo;
            dto.Action = AccountLogInOutAction.Logout;
            dto.IP = "127.0.0.1";
            dto.ActionTime = DateTime.Now;
            bool result = _accountDao.LogAccountUnlock(dto);
            Assert.Equal(expect, result);
        }

    }
}