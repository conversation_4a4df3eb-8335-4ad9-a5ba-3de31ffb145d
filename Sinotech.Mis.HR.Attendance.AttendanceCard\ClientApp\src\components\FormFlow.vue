<template>
  <table class="table table-sm table-bordered caption-top text-center mb-0">
    <caption>簽核流程</caption>
    <thead class="table-secondary align-middle">
      <tr>
        <th :style="{ width: '10%' }">
          <span>關卡</span>
        </th>
        <th :style="{ width: '25%' }">
          <span>簽核關卡</span>
        </th>
        <th :style="{ width: '20%' }">
          <span>簽核人</span>
        </th>
        <th :style="{ width: '10%' }">
          <span>簽核時間</span>
        </th>
        <th :style="{ width: '10%' }">
          <span>簽核狀態</span>
        </th>
        <th :style="{ width: '25%' }">
          <span>簽核意見</span>
        </th>
      </tr>
    </thead>
    <tbody class="table-light align-middle">
      <template v-if="modelValue?.flows?.length === 0">
        <tr>
          <td
            colspan="6"
            class="text-center"
          >
            <span>無須簽核</span>
          </td>
        </tr>
      </template>
      <template v-else>
        <template
          v-for="(flow, index) in modelValue?.flows"
          :key="index"
        >
          <tr>
            <td
              :class="[
                'text-center',
                (modelValue?.formStatus === 1 && flow.step === modelValue?.currentStep) ? 'text-primary' : ''
              ]"
            >
              {{ flow.step }}
            </td>
            <td
              :class="[
                'text-start',
                (modelValue?.formStatus === 1 && flow.step === modelValue?.currentStep) ? 'text-primary' : ''
              ]"
            >
              <template v-if="flow.flowName === flow.recipientName">
                {{ flow.recipientName }}
              </template>
              <template v-else>
                {{ flow.flowName }}
                {{ '-' }}
                {{ flow.recipientName }}
              </template>
            </td>
            <td
              :class="[
                'text-center',
                (modelValue?.formStatus === 1 && flow.step === modelValue?.currentStep) ? 'text-primary' : ''
              ]"
            >
              <span>{{ flow.approverName ?? '' }}</span>
              <template v-if="flow.isAgentApprove === true">
                <span class="badge rounded-pill bg-warning">代</span>
              </template>
            </td>
            <td
              :class="[
                'text-center',
                (modelValue?.formStatus === 1 && flow.step === modelValue?.currentStep) ? 'text-primary' : ''
              ]"
            >
              {{ flow.approveTime ? dateToRocString(new Date(flow.approveTime)) : '' }}
            </td>
            <td
              :class="[
                'text-center',
                (modelValue?.formStatus === 1 && flow.step === modelValue?.currentStep) ? 'text-primary' : ''
              ]"
            >
              {{ flow.flowStatusName }}
            </td>
            <td class="text-start">
              <template v-if="(editComment === true && modelValue?.formStatus === 1 && flow.step === modelValue?.currentStep)">
                <button
                  type="button"
                  :class="['btn btn-sm mb-1', (selectApproveCommentOptions.length === 0) ? 'btn-outline-secondary' : 'btn-primary']"
                  :disabled="selectApproveCommentOptions.length === 0"
                  @click="onClickShowDialog"
                >
                  常用意見
                </button>
                <textarea
                  class="form-control text-primary"
                  placeholder="請輸入簽核意見"
                  rows="3"
                  :value="approveComment"
                  @input="onUpdateApproveComment"
                />
              </template>
              <template v-else>
                {{ flow.approveComments }}
              </template>
            </td>
          </tr>
        </template>
      </template>
    </tbody>
    <tfoot>
      <tr>
        <td
          colspan="6"
          class="text-center fw-bold"
        >
          <span>表單狀態：</span>
          <template v-if="modelValue?.formStatus === 4">
            <span>{{ modelValue?.formStatusName }}</span>
            <template v-if="modelValue?.updatedName || modelValue?.updatedTime">
              <span>(</span>
              <span class="me-1">{{ modelValue?.updatedName }}</span>
              <template v-if="modelValue?.updatedTime">
                <span>於</span>
                <span>{{ dateToRocString(new Date(modelValue.updatedTime)) }}</span>
                <span class="ms-1">抽單</span>
              </template>
              <template v-else>
                <span>{{ modelValue?.formStatusName }}</span>
              </template>
              <span>)</span>
            </template>
          </template>
          <template v-else>
            <span>{{ modelValue?.formStatusName }}</span>
          </template>
        </td>
      </tr>
    </tfoot>
  </table>
  <Dialog
    v-model:visible="showDialog"
    modal
    :style="{ width: '35vw' }"
    :breakpoints="{
      [bootstrapVariables.gridBreakpointsLg]: '60vw',
      [bootstrapVariables.gridBreakpointsSm]: '80vw'
    }"
    class="text-break"
  >
    <template #header>
      <span class="p-dialog-title">常用意見</span>
    </template>
    <div>
      <div
        v-for="(option, index) in selectApproveCommentOptions"
        :key="index"
        class="form-check mb-1"
      >
        <input
          :id="'approveCommentOptions' + (index + 1).toString()"
          v-model="selectApproveComment"
          class="form-check-input"
          type="radio"
          :value="option"
        >
        <label
          class="form-check-label text-decoration-underline"
          :for="'approveCommentOptions' + (index + 1).toString()"
        >
          {{ option }}
        </label>
      </div>
    </div>
    <template #footer>
      <button
        type="button"
        :class="['btn', (selectApproveComment.length === 0) ? 'btn-outline-secondary' : 'btn-primary']"
        :disabled="selectApproveComment.length === 0"
        @click="onSelectApproveComment"
      >
        <span>確定</span>
      </button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import Dialog from 'primevue/dialog'
import { useAuthUserStore } from '../store/index'
import { dateToRocString } from '../api/appFunction'
import { SYSTEM_ERROR_MESSAGE } from '../api/appConst'
import type { FormFlowType } from '../api/appType'
import { POST_TOPAPPROVECOMMENTS_URL} from '../api/appUrl'
import { useToast } from 'primevue/usetoast'
import { useAbortController } from '../composable/abortController'
import bootstrapVariables from '../assets/scss/_bootstrap_variables.module.scss'

const props = defineProps<{
  modelValue: FormFlowType | undefined,
  editComment?: boolean
}>()
const emits = defineEmits(['update:approveComment'])

const userStore = useAuthUserStore()
const toast = useToast()
const { abortController, fetchErrorHandler, abortListener } = useAbortController()
const approveComment = ref<string>('')
const selectApproveComment = ref<string>('')
const selectApproveCommentOptions = ref<Array<string>>([])
const showDialog = ref<boolean>(false)

const onUpdateApproveComment = (event: Event) => {
  approveComment.value = (event.target as HTMLInputElement).value
  emits('update:approveComment', approveComment.value)
}

const onSelectApproveComment = () => {
  approveComment.value = selectApproveComment.value
  emits('update:approveComment', approveComment.value)
  showDialog.value = false
  selectApproveComment.value = ''
}

const onClickShowDialog = (): void => {
  showDialog.value = true
}

onMounted(() => {
  abortListener()

  if (props.editComment === true && props.modelValue?.formStatus === 1) {
    const params = new URLSearchParams({
      empNo: userStore.userId,
      count: '10'
    })

    fetch(POST_TOPAPPROVECOMMENTS_URL + '?' + params, {
      method: 'POST',
      signal: abortController.signal
    }).then((res: Response): Promise<Array<string>> => {
      if (!res.ok) {
        throw new Error(res.status.toString())
      }
      return res.json()
    }).then((res: Array<string>): void => {
      selectApproveCommentOptions.value = res
    }).catch((err: Error): void => {
      console.error(err)
      fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
    })
  }
})
</script>
<style lang="scss" scoped>
@use '../assets/scss/bootstrap_variables' as bootstrap;
.form-control::placeholder {
  color: bootstrap.$primary !important;
}
</style>