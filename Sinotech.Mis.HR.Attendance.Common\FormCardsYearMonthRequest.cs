﻿using System;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 取得特定時間區間內所有表單的請求
    /// </summary>
    public class FormCardsYearMonthRequest
    {
        /// <summary>
        /// 西元年
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// 月 1~12, 0:無指定
        /// </summary>
        public int Month { get; set; } = 0;

        /// <summary>
        /// 旬, 1: 上旬 2: 中旬 3: 下旬 0:無指定
        /// </summary>
        public int TenDays { get; set; } = 0;

        /// <summary>
        /// 計畫編號，若不填則傳回所有
        /// </summary>
        public string ProjNo { get; set; } = string.Empty;

        /// <summary>
        /// 員工編號，若不填則傳回所有
        /// </summary>
        public string EmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 部門編號，若為0則是全部
        /// </summary>
        public int DeptNo { get; set; } = 0;

        /// <summary>
        /// 表單狀態，若為0則是全部
        /// </summary>
        public int Status { get; set; } = 0;

        /// <summary>
        /// 表單內容開始日期
        /// </summary>
        public DateTime StartDate { get; set; } = DateTime.MaxValue;

        /// <summary>
        /// 表單內容結束日期
        /// </summary>
        public DateTime EndDate { get; set; } = DateTime.MinValue;
    }
}
