﻿using FakeItEasy;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sinotech.Mis.Extensions.Configuration;
using Sinotech.Mis.HR.Attendance.BusinessLogic;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.DataAccess.Ado;
using Sinotech.Mis.HR.Attendance.Tests;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Threading.Tasks;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers.Tests
{
    public class B1CardControllerTests
    {

        private readonly B1CardAppBo _b1CardAppBo;
        private readonly IB1CardBo _b1CardBo;
        private readonly CardBoFactory _cardBoFactory;
        private readonly FormBo _formBo;
        private readonly ILogger<B1CardController> _logger;
        private readonly B1CardController _controller;
        private readonly B1CardAppController _b1CardAppController;
        private readonly IConfiguration _configuration;
        public B1CardControllerTests(AttendanceBo attendanceBo, B1CardAppBo b1CardAppBo, IB1CardBo b1CardBo, FormBo formBo, CardBoFactory cardBoFactory,
            WorkdayBo workdayBo, ILogger<B1CardController> logger)
        {
            _b1CardAppBo = b1CardAppBo;
            _formBo = formBo;
            _cardBoFactory = cardBoFactory;
            _b1CardBo = b1CardBo;
            _logger = logger;


            IConfiguration configuration = new ConfigurationBuilder().
                    AddJsonFile("appsettings.internet.json", optional: true, reloadOnChange: true).Build();
            _controller = new B1CardController(_b1CardBo, configuration, _logger);
            configuration = new ConfigurationBuilder().
                   AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).Build();
            _configuration = configuration;
            _controller = new B1CardController(_b1CardBo, configuration, _logger);
            var fakeB1CardAppLogger = A.Fake<ILogger<B1CardAppController>>();
            _b1CardAppController = new B1CardAppController(attendanceBo, b1CardBo, b1CardAppBo, workdayBo,
    configuration, fakeB1CardAppLogger);

            string ConnectionStringAttendance = configuration.GetSecuredConnectionString("Attendance");
            TestHelper.ClearData(ConnectionStringAttendance);
        }

        private void AddSimpleB1CardApp(DateTime date, string empNo)
        {
            string errorMessage;
            Guid formUid = Guid.NewGuid();
            string guidString = formUid.ToString();
            Guid flowUid = Guid.NewGuid();
            string jsonForm = "{\"ID\":null,\"FormUID\":\"" + guidString + "\",\"FormID\":\"B1CardApp\",\"FormNo\":null,\"FormSubject\":\"加班申請卡-" + empNo + "-111/08/18\",\"FormInfo\":\"111/08/18\",\"EmpNo\":\"" + empNo + "\",\"EmpName\":\"白燕菁\",\"DeptNo\":4,\"DeptSName\":\"企劃處\",\"TeamID\":null,\"TeamCName\":null,\"CreatedEmpNo\":\"" + empNo + "\",\"CreatedName\":\"曾騰毅\",\"FilledTime\":\"2022-08-19T08:35:42.582+08:00\",\"CreatedTime\":\"2022-08-19T08:36:07.111+08:00\",\"CreatedIP\":\"***********\",\"CreatedHost\":\"03-769\",\"AddedSigner\":\"0274\",\"StartTime\":\"2022-08-19T08:36:35.8711009+08:00\",\"EndTime\":null,\"FormStatus\":1,\"TotalSteps\":2,\"CurrentStep\":0,\"UpdatedEmpNo\":null,\"UpdatedName\":null,\"UpdatedTime\":null,\"UpdatedIP\":null,\"UpdatedHost\":null,\"Flows\":["
                            + "{\"ID\":null,\"FlowUID\":\"" + flowUid + "\",\"FormUID\":\"" + formUid + "\",\"RecipientEmpNo\":\"L04\",\"RecipientName\":\"企劃處經理\",\"RecipientDeptNo\":4,\"RecipientDeptSName\":\"企劃處\",\"RecipientTeamID\":null,\"RecipientTeamCName\":null,\"FlowName\":\"申請人部門主管\",\"Step\":1,\"ApproverEmpNo\":null,\"ApproverName\":null,\"ApproverDeptNo\":0,\"ApproverDeptSName\":null,\"ApproverTeamID\":null,\"ApproverTeamCName\":null,\"ApproverIP\":null,\"ApproverHost\":null,\"ApproveTime\":null,\"IsAgentApprove\":false,\"FlowStatus\":1,\"IsNotification\":false}]}";
            Form form = JsonConvert.DeserializeObject<Form>(jsonForm);
            form.FormStatus = (int)FormStatus.Processing;
            B1CardApp card = new B1CardApp();
            card.FormUID = formUid;
            card.B1_EmpNo = empNo;
            card.B1_DeptNo = 4;
            card.B1_Date = date;
            card.B1_Hour = 4;
            card.B1_Code = '2';
            card.B1_PrjNo = "RP19553";
            card.B1_Reason = "test";
            card.B1_WritedEmpNo = empNo;
            card.B1_WDate = date;
            card.B1_UpdatedEmpNo = null;
            card.B1_Status = (int)FormStatus.Processing;
            card.B1_SOURCE = "Attendance";
            card.UpdatedEmpNo = empNo;
            card.UpdatedName = "曾騰毅";
            card.UpdatedTime = date;
            card.UpdatedIP = "***********";
            card.UpdatedHost = "03-769";
            card.AddSigners = "0274";
            card.CreatedTime = date;

            ICardBaseBo cardBo = _cardBoFactory.GetCardBo("B1CardApp");
            errorMessage = _formBo.AddForm(form, cardBo, card);

            Approve approve = new Approve();
            approve.ApproverDeptNo = 4;
            approve.ApproveIP = "127.0.0.1";
            approve.ApproverName = "部門主管";
            approve.ApproveTime = DateTime.Now;
            approve.ApproverDeptSName = "企劃處";
            approve.ApproveHost = "localhost";
            approve.ApproverEmpNo = "0391";
            approve.FormUID = card.FormUID;
            approve.FormID = "B1CardApp";
            approve.FlowUID = flowUid;
            approve.FlowStatus = (int)FlowStatus.Agree;
            errorMessage = _formBo.Approve(approve, cardBo, "0391");
        }

        private class MyCheckResult
        {
            public string ErrorMessage { get; set; }
            public string EndTimeMustLaterThanStartTime { get; set; }
            public bool CanOvertime { get; set; }
        }

        [Theory]
        [InlineData(2023, 3, 8, "0349")]
        [InlineData(2023, 1, 2, "0395")]
        public void CanCallDateCanFillB1Card(int year, int month, int day, string empNo)
        {
            // Arrange
            DateTime date = new DateTime(year, month, day, 0, 0, 0, DateTimeKind.Local);
            // Add B1CardApp
            AddSimpleB1CardApp(date, empNo);

            // Act
            string result = _controller.DateCanFillB1Card(date, empNo);
            MyCheckResult checkResult = JsonConvert.DeserializeObject<MyCheckResult>(result);

            // Assert
            Assert.True(checkResult.CanOvertime);
        }

        [Fact]
        public async Task CanCallSubmit()
        {
            // Arrange
            var b1Card = new B1Card
            {
                EmpNo = "0349",
                Reason = "TestValue1570286398",
                B1_WDate = new DateTime(2023, 3, 2, 11, 32, 11, DateTimeKind.Local),
                TotalHours = 1,
                InOvertimeHours = 1,
                Status = 1,
                SheetNo = "Test436381",
                B1_ADate = null,
                Source = "Attendance",
                DateTypeId = (int)WorkdayType.WeekWorkday,
                Details = new List<B1CardDetail>()
            };

            B1CardDetail detail = new B1CardDetail
            {
                Project = "RP19553",
                StartTime = new DateTime(2023, 3, 1, 17, 3, 0, DateTimeKind.Local),
                EndTime = new DateTime(2023, 3, 1, 19, 3, 0, DateTimeKind.Local),
                Hour = 2,
                HourLeft = 0,
                B1_CODE = 2,
                SerialNo = 1
            };

            b1Card.Details.Add(detail);

            // Act
            var result = await _controller.Submit(b1Card);
            B1CardCheckResult checkResult = JsonConvert.DeserializeObject<B1CardCheckResult>(result);

            // Assert
            Assert.False(checkResult.CanOvertime);
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("   ")]
        public void CannotCallDateCanFillB1CardWithInvalidEmpNo(string empNo)
        {
            // Assert
            DateTime date = new DateTime(2023, 3, 8, 0, 0, 0, DateTimeKind.Local);
            string result = _controller.DateCanFillB1Card(date, empNo);

            // Act
            MyCheckResult checkResult = JsonConvert.DeserializeObject<MyCheckResult>(result);

            // Assert
            Assert.False(checkResult.CanOvertime);
            Assert.Equal(checkResult.ErrorMessage, checkResult.ErrorMessage);
        }

        [Fact]
        public async Task CannotCallSubmitWithNullB1Card()
        {
            // Act
            var result = await _controller.Submit(default);
            B1CardCheckResult checkResult = JsonConvert.DeserializeObject<B1CardCheckResult>(result);

            // Assert
            Assert.False(checkResult.CanOvertime);
        }

        [Fact]
        public async Task SubmitTest()
        {
            B1Card b1Card = new B1Card();
            string strResult = await _controller.Submit(b1Card);
            B1CardAppCheckResult b1CardAppCheckResult = JsonConvert.DeserializeObject<B1CardAppCheckResult>(strResult);
            Assert.False(b1CardAppCheckResult.IsValid);
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo("0395");
            await _controller.Submit(b1Card);
            b1CardAppCheckResult = JsonConvert.DeserializeObject<B1CardAppCheckResult>(strResult);
            Assert.False(b1CardAppCheckResult.IsValid);

            b1Card.EmpNo = "0395";
            b1Card.TotalHours = 7;
            b1Card.AddSigners = "0349";
            List<B1CardDetail> details = new List<B1CardDetail>();
            B1CardDetail detail = new B1CardDetail();
            detail.Project = "RP09553";
            detail.Hour = 7;
            detail.B1_CODE = 1;
            detail.StartTime = DateTime.Parse("2023-11-04 07:09+08:00", new CultureInfo("zh-TW"));
            detail.EndTime = DateTime.Parse("2023-11-04 14:13+08:00", new CultureInfo("zh-TW"));
            details.Add(detail);
            b1Card.Details = details;
            b1Card.CreatedTime = DateTime.Parse("2023-11-03 14:45:33+08:00", new CultureInfo("zh-TW"));
            b1Card.FilledTime = DateTime.Parse("2023-11-03 14:05:33+08:00", new CultureInfo("zh-TW"));
            b1Card.B1_WDate = b1Card.FilledTime;
            b1Card.Reason = "This is a test.";
            strResult = await _controller.Submit(b1Card);
            B1CardCheckResult result = JsonConvert.DeserializeObject<B1CardCheckResult>(strResult);
            Assert.False(result.IsValid);
            Assert.Equal("請先確認已填加班申請卡，且經部門主管核可，方可填報", result.ErrorMessage);


            B1CardApp b1CardApp = new B1CardApp();
            b1CardApp.B1_PrjNo = "RP19553";
            b1CardApp.B1_EmpNo = "0395";
            b1CardApp.B1_Hour = 3;
            b1CardApp.B1_PaidHour = 6;
            b1CardApp.B1_Code = '1';
            b1CardApp.B1_Date = DateTime.Parse("2023-11-03+08:00", new CultureInfo("zh-TW"));
            b1CardApp.CreatedTime = DateTime.Parse("2023-11-03 14:45:33+08:00", new CultureInfo("zh-TW"));
            b1CardApp.FilledTime = DateTime.Parse("2023-11-03 14:05:33+08:00", new CultureInfo("zh-TW")); ;
            b1CardApp.B1_WDate = b1CardApp.FilledTime;
            b1CardApp.B1_Reason = "This is a test.";
            _b1CardAppController.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo("0395");
            strResult = await _b1CardAppController.Submit(b1CardApp);
            b1CardAppCheckResult = JsonConvert.DeserializeObject<B1CardAppCheckResult>(strResult);
            Assert.True(b1CardAppCheckResult.IsValid);
            Form form= _formBo.GetForm((Guid)b1CardAppCheckResult.FormUID);

            Approve approve = new Approve();
            approve.ApproverDeptSName = "企劃處";
            approve.ApproveIP = "127.0.0.1";
            approve.ApproverName = "test";
            approve.ApproverEmpNo = "0391";
            approve.FlowUID = form.Flows[0].FlowUID;
            approve.FormID = form.FormID;
            approve.FormUID = form.FormUID;
            approve.ApproveTime = DateTime.Now;
            approve.FlowStatus = (int)FlowStatus.Agree;
            _formBo.Approve(approve, _b1CardAppBo, "0391");
            strResult = await _controller.Submit(b1Card);
            result = JsonConvert.DeserializeObject<B1CardCheckResult>(strResult);
            Assert.False(result.IsValid);

        }

        [Theory]
        [InlineData(2023, 3, 8, "0391")]
        public void WithB1CardAppCanNotCallDateCanFillB1Card(int year, int month, int day, string empNo)
        {
            // Arrange
            DateTime date = new DateTime(year, month, day, 0, 0, 0, DateTimeKind.Local);
            // Add B1CardApp
            AddSimpleB1CardApp(date, empNo);
            // Act
            string result = _controller.DateCanFillB1Card(date, empNo);
            B1CardCheckResult checkResult = JsonConvert.DeserializeObject<B1CardCheckResult>(result);
            // Assert
            Assert.False(checkResult.CanOvertime);
        }

        [Theory]
        [InlineData(2023, 3, 8, "0349")]
        [InlineData(2023, 1, 1, "0395")]
        public void WithoutB1CardAppCanNotCallDateCanFillB1Card(int year, int month, int day, string empNo)
        {
            // Arrange
            DateTime date = new DateTime(year, month, day, 0, 0, 0, DateTimeKind.Local);
            // Act
            string result = _controller.DateCanFillB1Card(date, empNo);
            B1CardCheckResult checkResult = JsonConvert.DeserializeObject<B1CardCheckResult>(result);
            // Assert
            Assert.False(checkResult.CanOvertime);
        }

        [Fact]
        public void B1CardControllerTest()
        {
            IConfiguration configuration = new ConfigurationBuilder().
                     AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).Build();
            var logger = A.Fake<ILogger<B1CardController>>();
            var b1CardBo = A.Fake<B1CardBo>();

            B1CardController controller = new B1CardController(b1CardBo, configuration, logger);
            Assert.NotNull(controller);
        }


    }
}
