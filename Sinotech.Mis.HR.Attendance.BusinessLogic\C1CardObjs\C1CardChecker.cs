﻿using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves;
using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs
{
    public class C1CardChecker
    {

        private readonly C1CardBase? _c1Card;

        /// <summary>
        /// 建構元
        /// </summary>
        /// <param name="c1Card">請假 DTO物件</param>
        public C1CardChecker(C1Card c1Card, IC1CardBo c1CardBo)
        {
            C1CardBase? cardbase = C1CardFactory.CreateLeave(c1Card, c1CardBo);
            _c1Card = cardbase;
            if (cardbase == null)
            {
                throw new LeaveNotFoundException("無法找到對應假別");
            }
        }

        /// <summary>
        /// 檢查所有規格
        /// </summary>
        /// <returns>C1CardCheckResult物件</returns>
        public CardCheckResult CheckData()
        {
            if (_c1Card != null)
            {
                return _c1Card.CheckData();
            }
            else
            {
                return new CardCheckResult(999, CardStatusEnum.Error, "無法找到對應假別");
            }
        }

    }
}
