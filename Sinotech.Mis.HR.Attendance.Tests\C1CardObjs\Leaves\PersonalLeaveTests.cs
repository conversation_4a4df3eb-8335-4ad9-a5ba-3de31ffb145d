﻿using FakeItEasy;
using Microsoft.AspNetCore.Http;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves;
using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves.Tests
{
    public class PersonalLeaveTests : TestC1CardBase
    {
        public PersonalLeaveTests()
        {
            #region Prepare Data

            _c1Card.LeaveNumber = LeaveKindEnum.PersonalLeave;

            #endregion
        }

        [Theory]
        [InlineData(false, PersonalLeave.CodeLeaveRecordNotCreateYet)]
        [InlineData(true, PersonalLeave.CodeOk)]
        public void TestCanTakeThisLeave(bool ifLeaveRecordExists, int returnCode)
        {
            A.<PERSON>o(() => _c1CardBo.IfLeaveRecordExists(A<string>.Ignored,
                A<DateTime>.Ignored, A<DateTime>.Ignored)).Returns(ifLeaveRecordExists);

            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CanTakeThisLeave();

            Assert.Equal(returnCode, result.Code);
        }

        [Theory]
        [InlineData(112, 100, 15, 8, false, PersonalLeave.CodeExceedQuota)]
        [InlineData(112, 112,  0, 1, false, PersonalLeave.CodeExceedQuota)]
        [InlineData(112, 112, 15, 1, false, PersonalLeave.CodeExceedQuota)]
        [InlineData(112, 100, 15, 8,  true, PersonalLeave.CodeOk)]
        [InlineData(112, 112,  0, 1,  true, PersonalLeave.CodeOk)]
        [InlineData(112, 112, 15, 1, true, PersonalLeave.CodeOk)]
        [InlineData(112, 100, 10, 8, true, PersonalLeave.CodeOk)]
        [InlineData(112, 100, 10, 8, false, PersonalLeave.CodeExceedQuota)]
        public void TestExceedQuota(int available, int used, int familyCareLeaveUsedHours, int totalHours, bool confirmed, int returnCode)
        {
            A.CallTo(() => _c1CardBo.GetPersonalLeaveYearAvailableHours(A<string>.Ignored, A<DateTime>.Ignored)).Returns(available);
            A.CallTo(() => _c1CardBo.GetPersonalLeaveYearUsedHours(A<string>.Ignored, A<DateTime>.Ignored)).Returns(used);
            A.CallTo(() => _c1CardBo.GetFamilyCareLeaveYearUsedHours(A<string>.Ignored, A<DateTime>.Ignored)).Returns(familyCareLeaveUsedHours);
            A.CallTo(() => _c1CardBo.CalculateEmpTakeLeaveWorkingHours(A<string>.Ignored, A<DateTime>.Ignored, A<DateTime>.Ignored)).Returns(totalHours);

            _c1Card.Confirmed = confirmed;

            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CheckOverPermittedLeaveHours();

            Assert.Equal(returnCode, result.Code);
        }

        [Fact]
        public void TestCheckRequiredFields()
        {
            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CheckRequiredFields();

            Assert.Equal(PersonalLeave.CodeOk, result.Code);
        }

        [Theory]
        [InlineData(Gender.Female, true)]
        [InlineData(Gender.Male, true)]
        public void TestGender(Gender gender, bool expected)
        {
            var result = PersonalLeave.IsAllowForThisGender(gender);
            Assert.Equal(expected, result);
        }
    }
}