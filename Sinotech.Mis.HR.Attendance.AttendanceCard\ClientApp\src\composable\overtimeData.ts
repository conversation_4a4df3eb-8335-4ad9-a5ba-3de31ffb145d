import { ref } from 'vue'
import { GET_OVERTIMEDATA_URL } from '../api/appUrl'
import type { OvertimeDataType } from '../api/appType'

/**
 * 加班時數統計
 * @returns 
 */
export function useOvertimeData() {
  const overtimeDateStatic = ref<Date>(new Date())
  const overtimeDate = ref<Date>(new Date())
  const overtimeData = ref<OvertimeDataType | null>(null)

  const onSetOvertimeDate = (date: Date) => {
    overtimeDate.value = (new Date(date.getFullYear(), date.getMonth(), date.getDate()))
  }

  const onGetOvertimeData = async (empNo: string, signal: AbortSignal): Promise<void> => {
    overtimeDateStatic.value = new Date()
  
    const params = new URLSearchParams({
      empNo: empNo,
      date: overtimeDate.value.toISOString()
    })
    try {
      const res: Response = await fetch(GET_OVERTIMEDATA_URL + '?' + params, {
        method: 'GET',
        signal: signal
      })
      if (!res.ok) {
        throw new Error(res.status.toString())
      }

      const jsonData = await res.json()
      overtimeData.value = {
        b1Card: jsonData.B1Card,
        b1CardApp: jsonData.B1CardApp,
        dayDetail: jsonData.DayDetail,
        hoursAreZeroMessage: jsonData.HoursAreZeroMessage,
        hoursLowerBound: jsonData.HoursLowerBound,
        hoursUpperBound: jsonData.HoursUpperBound,
        inTimeString: jsonData.InTimeString,
        isDriver: jsonData.IsDriver,
        isSpecialStaff: jsonData.IsSpecialStaff,
        monthCloseToHoursUpperBoundMessage: jsonData.MonthCloseToHoursUpperBoundMessage,
        monthCloseToHoursUpperBoundValue: jsonData.MonthCloseToHoursUpperBoundValue,
        monthOverHoursUpperBoundMessage: jsonData.MonthOverHoursUpperBoundMessage,
        monthOverHoursUpperBoundValue: jsonData.MonthOverHoursUpperBoundValue,
        monthOvertimeStatics: jsonData.MonthOvertimeStatics,
        overHoursLowerBoundMessage: jsonData.OverHoursLowerBoundMessage,
        overHoursUpperBoundMessage: jsonData.OverHoursUpperBoundMessage,
        quarterOverHoursUpperBoundMessage: jsonData.QuarterOverHoursUpperBoundMessage,
        quarterOverHoursUpperBoundValue: jsonData.QuarterOverHoursUpperBoundValue,
        quarterlyOvertimeStatics: jsonData.QuarterlyOvertimeStatics,
        specialStaffAllowedMonthWeightedOvertimeHours: jsonData.SpecialStaffAllowedMonthWeightedOvertimeHours,
        specialStaffCurrentMonthWeightedOvertimeHours: jsonData.SpecialStaffCurrentMonthWeightedOvertimeHours
      }
    } catch (err: unknown) {
      throw err
    }
  }

  return { overtimeDateStatic, overtimeDate, overtimeData, onSetOvertimeDate, onGetOvertimeData }
}