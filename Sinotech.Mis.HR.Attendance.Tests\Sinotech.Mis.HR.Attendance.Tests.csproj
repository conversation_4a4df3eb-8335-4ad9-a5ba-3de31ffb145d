<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>

    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="appsettings.Development.json" />
    <None Remove="appsettings.json" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="appsettings.Development.json">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Include="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="FakeItEasy" Version="8.3.0" />
    <PackageReference Include="HtmlSanitizer" Version="8.1.870" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.11" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.11.1" />
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="xunit" Version="2.9.2" />
    <PackageReference Include="xunit.console" Version="2.9.2" />
    <PackageReference Include="Xunit.DependencyInjection" Version="9.5.0" />
    <PackageReference Include="xunit.runner.console" Version="2.9.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="xunit.runner.visualstudio" Version="2.8.2">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="6.0.2">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="XunitXml.TestLogger" Version="4.1.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Sinotech.Mis.Extensions.Configuration\Sinotech.Mis.Extensions.Configuration.csproj" />
    <ProjectReference Include="..\Sinotech.Mis.Helpers\Sinotech.Mis.Helpers.csproj" />
    <ProjectReference Include="..\Sinotech.Mis.HR.Attendance.AttendanceCard\Sinotech.Mis.HR.Attendance.AttendanceCard.csproj" />
    <ProjectReference Include="..\Sinotech.Mis.HR.Attendance.BusinessLogic\Sinotech.Mis.HR.Attendance.BusinessLogic.csproj" />
    <ProjectReference Include="..\Sinotech.Mis.HR.Attendance.Common\Sinotech.Mis.HR.Attendance.Common.csproj" />
    <ProjectReference Include="..\Sinotech.Mis.HR.Attendance.DataAccess.Ado\Sinotech.Mis.HR.Attendance.DataAccess.Ado.csproj" />
    <ProjectReference Include="..\Sinotech.Mis.HR.Attendance.Extensions.ServiceCollection\Sinotech.Mis.HR.Attendance.Extensions.ServiceCollection.csproj" />
    <ProjectReference Include="..\Sinotech.Mis.HR.Attendance.Utilities\Sinotech.Mis.HR.Attendance.Utilities.csproj" />
    <ProjectReference Include="..\Sinotech.Mis.Utilities.DataAccess.Ado\Sinotech.Mis.Utilities.DataAccess.Ado.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.internet.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PrivateKey.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PublicKey.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
