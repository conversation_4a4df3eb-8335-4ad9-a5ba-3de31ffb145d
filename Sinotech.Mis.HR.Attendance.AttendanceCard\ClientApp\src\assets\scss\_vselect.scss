@use './bootstrap_variables' as *;
@import 'bootstrap/scss/buttons';

.v-select {
  font-weight: $font-weight-normal;
  border-width: $form-select-border-width;
  border-radius: $form-select-border-radius;
  vertical-align: middle;
}

.v-select > ul {
  min-width: inherit;
}

.vs__search,
.vs--searchable .vs__dropdown-toggle {
  cursor: pointer;
}

.vs__dropdown-toggle {
  border-width: $form-select-border-width;
  border-radius: $form-select-border-radius;
}

.vs--disabled * {
  cursor: default !important;
  background-color: $gray-200 !important;
}

.vs--open.vs--single {
  box-shadow: $form-select-focus-box-shadow;
}

/// 避免 vue-select 下拉時會自動縮小
.vs--open.vs--single .vs__selected {
  position: static;
}

.vs__selected {
  min-width: 1rem;
}

.vs__dropdown-option {
  min-height: 2rem;
  padding-left: 0.8rem;
}