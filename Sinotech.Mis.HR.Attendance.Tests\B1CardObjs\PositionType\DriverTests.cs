﻿using Sinotech.Mis.Common;
using Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.PositionType;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using Xunit;
#nullable enable
namespace Sinotech.Mis.HR.Attendance.Tests
{
    [ExcludeFromCodeCoverage]
    public class DriverTests
    {
        private readonly IB1CardDataProvider _provider;

        public DriverTests()
        {
            _provider = new MockB1CardDataProvider();
        }

        [Fact]
        public void IsOvertimeAllowed_ShouldReturnTrue()
        {
            var driver = new Driver("12345", DateTime.Now, _provider);
            Assert.True(driver.IsOvertimeAllowed);
        }

        //[Fact]
        //public void HasAppliedOvertimeWork_ShouldReturnTrue()
        //{
        //    var driver = new Driver("12345", DateTime.Now, _provider);
        //    Assert.True(driver.HasAppliedOvertimeWork());
        //}

        //[Fact]
        //public void CheckDailyOvertimeHours_ShouldReturnSuccessResult()
        //{
        //    var driver = new Driver("12345", DateTime.Now, _provider);
        //    var result = driver.CheckDailyOvertimeHours();
        //    Assert.Equal(driver.SuccessResult, result);
        //}

        //[Fact]
        //public void CheckMonthlyOvertimeHours_ShouldReturnErrorResult_WhenExceeds92Hours()
        //{
        //    var driver = new Driver("12345", DateTime.Now, _provider);
        //    var result = driver.CheckMonthlyOvertimeHours();
        //    Assert.Equal(538, result.Code);
        //    Assert.Equal(B1CardStatusEnum.Error, result.Status);
        //    Assert.Equal("超出彈性每月加班時數", result.Message);
        //}

        //[Fact]
        //public void CheckMonthlyOvertimeHours_ShouldReturnSuccessResult_WhenWithin92Hours()
        //{
        //    var driver = new Driver("12345", DateTime.Now, _provider);
        //    var result = driver.CheckMonthlyOvertimeHours();
        //    Assert.Equal(driver.SuccessResult, result);
        //}

        //[Fact]
        //public void CheckQuarterlyOvertimeHours_ShouldReturnSuccessResult()
        //{
        //    var driver = new Driver("12345", DateTime.Now, _provider);
        //    var result = driver.CheckQuarterlyOvertimeHours();
        //    Assert.Equal(driver.SuccessResult, result);
        //}

        //[Fact]
        //public void CheckAppliedHours_ShouldReturnSuccessResult()
        //{
        //    var driver = new Driver("12345", DateTime.Now, _provider);
        //    var result = driver.CheckAppliedHours();
        //    Assert.Equal(driver.SuccessResult, result);
        //}

        //[Fact]
        //public void GetWarningResult_ShouldReturnSuccessResult()
        //{
        //    var driver = new Driver("12345", DateTime.Now, _provider);
        //    var result = driver.GetWarningResult();
        //    Assert.Equal(driver.SuccessResult, result);
        //}
    }

    public class MockB1CardDataProvider : IB1CardDataProvider
    {
        public string EmployeeNumber => "12345";
        public DateTime OvertimeDate => DateTime.Now;

        public int GetAppliedOvertimeHourInQuota() => 0;
        public B1CardApp? GetB1CardApp() => null;
        public B1Card GetB1Card() => new B1Card();
        public double GetCurrentMonthlyWeightedOvertimeHours() => 0;
        public bool GetHasAppliedOvertimeWork() => true;
        public bool GetHasB1CardFilled() => true;
        public int GetMonthOvertimeHourInQuota() => 0;
        public Workday GetOverTimeDateInfo() => new Workday();
        public B1CardPositionEnum GetPositionType() => B1CardPositionEnum.Driver;
        public List<Project> GetProjectList() => new List<Project>();
        public Project? GetProjectInfo(string projectNumber) => null;
        public int GetQuarterOvertimeHourInQuota() => 0;
        public double GetSpecialStaffAllowedMonthlyWeightedOvertimeHours() => 0;
        public double GetWeightedOvertimeHours() => 0;
        public bool IsSpecialStaff() => false;
    }
}
