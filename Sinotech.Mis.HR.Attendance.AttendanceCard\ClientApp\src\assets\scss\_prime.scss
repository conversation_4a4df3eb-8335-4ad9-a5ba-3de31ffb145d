@use './font' as font;

@use './bootstrap_variables' as *;
@import 'bootstrap/scss/buttons';

.p-component,
.p-link,
.p-fileupload .p-button,
.p-autocomplete .p-autocomplete-multiple-container .p-autocomplete-input-token input,
.p-chips .p-chips-multiple-container .p-chips-input-token input,
.p-inputtext {
  font-family: font.$font-families;
}

.p-menubar-submenu {
  z-index: 10 !important;
}

.p-inputtext:enabled:hover {
  border-color: $input-border-color !important;
}

.p-inputtext:enabled:focus {
  box-shadow: $input-btn-focus-box-shadow !important;
}

.p-link:focus {
  box-shadow: $input-btn-focus-box-shadow !important;
}

.p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box.p-focus {
  box-shadow: none !important;
}

.p-toast .p-toast-message.p-toast-message-error {
  font-weight: bold;
  color: $gray-900;
}

.p-confirmdialog {
  min-width: 30vw;
}

.p-confirmdialog.p-dialog-danger {
  .p-dialog-header {
    background: $red;
  }
}

.p-dialog .p-dialog-header .p-dialog-header-icon:focus {
  box-shadow: $input-btn-focus-box-shadow;
}

.p-fileupload .p-button {
  @extend .btn;
}

.p-fileupload .p-button.p-fileupload-choose-button:not(.p-disabled) {
  @extend .btn-primary;
}

.p-fileupload .p-button.p-fileupload-choose-button.p-disabled {
  @extend .btn-outline-secondary;
}

.p-fileupload-content,
.p-fileupload.p-fileupload-advanced .p-message {
  padding: 0 !important;
  margin: 0;
  border-bottom: 0 !important;
}

.p-button:focus {
  box-shadow: none;
}

.p-button .p-button-icon-left {
  margin-right: 0;
}

.p-button .p-button-label {
  font-weight: normal;
}

// 縮小Calendar的日期間距
.p-datepicker-panel,
.p-datepicker-header,
.p-datepicker-day-view,
.p-datepicker-day-cell,
.p-datepicker-day {
  padding: 0 !important;
  margin: 0 !important;
}

// Datatable的表格內容強制不換行
.p-datatable .p-datatable-table-container .p-datatable-table {
  .p-datatable-thead tr th,
  .p-datatable-tbody tr td {
    white-space: nowrap;
  }
}

.p-datatable .p-datatable-header {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.p-datatable .p-datatable-thead > tr > th,
.p-datatable .p-datatable-tbody > tr > td {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.attendanceCard-develop {
  background: yellow !important;
}

.p-component-overlay {
  background-color: transparent;
}