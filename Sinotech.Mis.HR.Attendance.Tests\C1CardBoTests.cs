﻿using FakeItEasy;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.VisualStudio.TestPlatform.CommunicationUtilities.ObjectModel;
using Newtonsoft.Json;
using Sinotech.Mis.Common;
using Sinotech.Mis.Extensions.Configuration;
using Sinotech.Mis.HR.Attendance.BusinessLogic;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.DataAccess.Interfaces;
using Sinotech.Mis.HR.Attendance.Tests;
using Sinotech.Mis.Utilities.DataAccess.Interfaces;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using Xunit;


namespace Sinotech.Mis.HR.Attendance.BusinessLogic.Tests
{
    [ExcludeFromCodeCoverage]
    public class C1CardBoTests
    {

#nullable enable
        private readonly IAttendanceBo _attendanceBo;
        private readonly IConfiguration _configuration;
        private readonly IC1CardBo _c1CardBo;
        private readonly IC1CardDao _c1CardDao;
        private readonly IDepartmentBo _departmentBo;
        private readonly IEmployeeBo _employeeBo;
        private readonly IFormBo _formBo;
        private readonly ISinoSignBo _sinoSignBo;
        private readonly IFormFlowBo _formFlowBo;
        private readonly IWorkdayBo _workdayBo;
        private readonly bool _IsMultipleVicePresidents = false;

        public C1CardBoTests(IAttendanceBo attendanceBo, IC1CardDao c1CardDao,
            IDepartmentBo departmentBo,
            IEmployeeBo employeeBo,
            IFormTypeBo formTypeBo,
            IFormFlowBo formFlowBo,
            IWorkdayBo workdayBo,
            ISinoSignDao sinoSignDao,
            NotificationMail notificationMail)
        {
            _c1CardDao = c1CardDao;
            _employeeBo = employeeBo;
            _formFlowBo = formFlowBo;
            _attendanceBo = attendanceBo;

            _workdayBo = workdayBo;
            IConfiguration configuration = new ConfigurationBuilder().
                    AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).Build();
            _sinoSignBo = new SinoSignBo(sinoSignDao, configuration);
            _formBo = new FormBo(_attendanceBo,
                departmentBo,
                _employeeBo, formTypeBo, formFlowBo,
                _sinoSignBo, notificationMail,
                A.Fake<ILogger<FormBo>>(),
                configuration);

            _configuration = configuration;
            _c1CardBo = new C1CardBo(_attendanceBo, _c1CardDao, _employeeBo, _formBo, formFlowBo, A.Fake<ILogger<C1CardBo>>(), _workdayBo);

            string? ConnectionStringAttendance = configuration.GetSecuredConnectionString("Attendance");
            if (ConnectionStringAttendance != null)
            {
                TestHelper.ClearData(ConnectionStringAttendance);
            }
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            // 取得設定檔 IsMultipleVicePresidents
            if (_configuration != null)
            {
                var k = _configuration["IsMultipleVicePresidents"];
                if (!string.IsNullOrWhiteSpace(k))
                {
                    _IsMultipleVicePresidents = _configuration.GetValue<bool>("IsMultipleVicePresidents");
                }
            }
        }

        [Theory]
        [InlineData("0349", "{\"EmpNo\":\"0349\",\"StartTime\":\"2023-01-02T08:00:00\",\"EndTime\":\"2023-01-02T17:00:00\",\"EventDate\":\"0001-01-01T00:00:00\",\"ExpirationStartDate\":\"0001-01-01T00:00:00\",\"ExpirationEndDate\":\"0001-01-01T00:00:00\",\"OverPermittedHours\":false,\"Deputy\":\"\",\"LeaveNumber\":1,\"LeaveSubNumber\":0,\"RelatedFormNumber\":\"\",\"Reason\":\"\",\"ProjectNumber\":\"\",\"Location\":\"\",\"UpdatedEmpNo\":null,\"UpdatedName\":null,\"UpdatedTime\":null,\"UpdatedIP\":null,\"UpdatedHost\":null,\"AddSigners\":\"\",\"CreatedTime\":\"2022-12-29T08:34:03\",\"FilledTime\":\"2022-12-29T08:33:07.111+08:00\",\"UploadedFiles\":null}", "127.0.0.1", "DummyHost", "【代理人】不可空白")]
        [InlineData("0395", "{\"EmpNo\":\"0395\",\"StartTime\":\"2023-01-02T08:00:00\",\"EndTime\":\"2023-01-02T17:00:00\",\"EventDate\":\"0001-01-01T00:00:00\",\"ExpirationStartDate\":\"0001-01-01T00:00:00\",\"ExpirationEndDate\":\"0001-01-01T00:00:00\",\"OverPermittedHours\":false,\"Deputy\":\"\",\"LeaveNumber\":1,\"LeaveSubNumber\":0,\"RelatedFormNumber\":\"\",\"Reason\":\"\",\"ProjectNumber\":\"\",\"Location\":\"\",\"UpdatedEmpNo\":null,\"UpdatedName\":null,\"UpdatedTime\":null,\"UpdatedIP\":null,\"UpdatedHost\":null,\"AddSigners\":\"\",\"CreatedTime\":\"2022-12-29T08:34:03\",\"FilledTime\":\"2022-12-29T08:33:07.111+08:00\",\"UploadedFiles\":null}", "127.0.0.1", "DummyHost", "【代理人】不可空白")]
        [InlineData("0395", "{\"EmpNo\":\"0395\",\"StartTime\":\"2023-01-02T08:00:00\",\"EndTime\":\"2023-01-02T17:00:00\",\"EventDate\":\"0001-01-01T00:00:00\",\"ExpirationStartDate\":\"0001-01-01T00:00:00\",\"ExpirationEndDate\":\"0001-01-01T00:00:00\",\"OverPermittedHours\":false,\"Deputy\":\"0349\",\"LeaveNumber\":1,\"LeaveSubNumber\":0,\"RelatedFormNumber\":\"\",\"Reason\":\"\",\"ProjectNumber\":\"\",\"Location\":\"\",\"UpdatedEmpNo\":null,\"UpdatedName\":null,\"UpdatedTime\":null,\"UpdatedIP\":null,\"UpdatedHost\":null,\"AddSigners\":\"\",\"CreatedTime\":\"2022-12-29T08:34:03\",\"FilledTime\":\"2022-12-29T08:33:07.111+08:00\",\"UploadedFiles\":null}", "127.0.0.1", "DummyHost", "請假日期限填上班日")]
        [InlineData("0349", "{\"EmpNo\":\"0349\",\"StartTime\":\"2023-01-02T08:00:00\",\"EndTime\":\"2023-01-02T17:00:00\",\"EventDate\":\"0001-01-01T00:00:00\",\"ExpirationStartDate\":\"0001-01-01T00:00:00\",\"ExpirationEndDate\":\"0001-01-01T00:00:00\",\"OverPermittedHours\":false,\"Deputy\":\"0395\",\"LeaveNumber\":1,\"LeaveSubNumber\":0,\"RelatedFormNumber\":\"\",\"Reason\":\"\",\"ProjectNumber\":\"\",\"Location\":\"\",\"UpdatedEmpNo\":null,\"UpdatedName\":null,\"UpdatedTime\":null,\"UpdatedIP\":null,\"UpdatedHost\":null,\"AddSigners\":\"\",\"CreatedTime\":\"2022-12-29T08:34:03\",\"FilledTime\":\"2022-12-29T08:33:07.111+08:00\",\"UploadedFiles\":null}", "127.0.0.1", "DummyHost", "請假日期限填上班日")]
        [InlineData("0395", "{\"EmpNo\":\"0395\",\"StartTime\":\"2023-01-03T08:00:00\",\"EndTime\":\"2023-01-03T17:00:00\",\"EventDate\":\"0001-01-01T00:00:00\",\"ExpirationStartDate\":\"0001-01-01T00:00:00\",\"ExpirationEndDate\":\"0001-01-01T00:00:00\",\"OverPermittedHours\":false,\"Deputy\":\"\",\"LeaveNumber\":1,\"LeaveSubNumber\":0,\"RelatedFormNumber\":\"\",\"Reason\":\"\",\"ProjectNumber\":\"\",\"Location\":\"\",\"UpdatedEmpNo\":null,\"UpdatedName\":null,\"UpdatedTime\":null,\"UpdatedIP\":null,\"UpdatedHost\":null,\"AddSigners\":\"\",\"CreatedTime\":\"2022-12-29T08:34:03\",\"FilledTime\":\"2022-12-29T08:33:07.111+08:00\",\"UploadedFiles\":null}", "127.0.0.1", "DummyHost", "【代理人】不可空白")]
        [InlineData("0349", "{\"EmpNo\":\"0349\",\"StartTime\":\"2023-01-03T08:00:00\",\"EndTime\":\"2023-01-03T17:00:00\",\"EventDate\":\"0001-01-01T00:00:00\",\"ExpirationStartDate\":\"0001-01-01T00:00:00\",\"ExpirationEndDate\":\"0001-01-01T00:00:00\",\"OverPermittedHours\":false,\"Deputy\":\"\",\"LeaveNumber\":1,\"LeaveSubNumber\":0,\"RelatedFormNumber\":\"\",\"Reason\":\"\",\"ProjectNumber\":\"\",\"Location\":\"\",\"UpdatedEmpNo\":null,\"UpdatedName\":null,\"UpdatedTime\":null,\"UpdatedIP\":null,\"UpdatedHost\":null,\"AddSigners\":\"\",\"CreatedTime\":\"2022-12-29T08:34:03\",\"FilledTime\":\"2022-12-29T08:33:07.111+08:00\",\"UploadedFiles\":null}", "127.0.0.1", "DummyHost", "【代理人】不可空白")]
        [InlineData("0395", "{\"EmpNo\":\"0395\",\"StartTime\":\"2023-01-05T08:00:00\",\"EndTime\":\"2023-01-05T17:00:00\",\"EventDate\":\"0001-01-01T00:00:00\",\"ExpirationStartDate\":\"0001-01-01T00:00:00\",\"ExpirationEndDate\":\"0001-01-01T00:00:00\",\"OverPermittedHours\":false,\"Deputy\":\"0349\",\"LeaveNumber\":14,\"LeaveSubNumber\":0,\"RelatedFormNumber\":\"\",\"Reason\":\"\",\"ProjectNumber\":\"\",\"Location\":\"\",\"UpdatedEmpNo\":null,\"UpdatedName\":null,\"UpdatedTime\":null,\"UpdatedIP\":null,\"UpdatedHost\":null,\"AddSigners\":\"\",\"CreatedTime\":\"2022-12-29T08:34:03\",\"FilledTime\":\"2022-12-29T08:33:07.111+08:00\",\"UploadedFiles\":null}", "127.0.0.1", "DummyHost", "")]
        [InlineData("2268", "{\"EmpNo\":\"2268\",\"StartTime\":\"2023-01-05T08:00:00\",\"EndTime\":\"2023-01-05T17:00:00\",\"EventDate\":\"0001-01-01T00:00:00\",\"ExpirationStartDate\":\"0001-01-01T00:00:00\",\"ExpirationEndDate\":\"0001-01-01T00:00:00\",\"OverPermittedHours\":false,\"Deputy\":\"0395\",\"LeaveNumber\":14,\"LeaveSubNumber\":0,\"RelatedFormNumber\":\"\",\"Reason\":\"\",\"ProjectNumber\":\"\",\"Location\":\"\",\"UpdatedEmpNo\":null,\"UpdatedName\":null,\"UpdatedTime\":null,\"UpdatedIP\":null,\"UpdatedHost\":null,\"AddSigners\":\"\",\"CreatedTime\":\"2022-12-29T08:34:03\",\"FilledTime\":\"2022-12-29T08:33:07.111+08:00\",\"UploadedFiles\":null}", "127.0.0.1", "DummyHost", "")]
        public async Task AddC1CardTest(string creatorId, string leaveJson, string ipAddress, string hostname, string expected)
        {
            LeaveView? leave = JsonConvert.DeserializeObject<LeaveView>(leaveJson);

            Assert.NotNull(leave);
            CardCheckResult result = await _c1CardBo.AddC1Card(creatorId, leave, ipAddress, hostname);
            Assert.NotNull(result);
            Assert.Equal(expected, result.Message);
        }

        private async Task<CardCheckResult?> AddC1Card(string empNo, string deputy, string addSigners, int leaveNumber, int leaveDetailNumber, string startTime, string endTime, string eventDate)
        {
            DateTime createdTime = DateTime.Now;
            DateTime filledTime = createdTime.AddSeconds(-29);

            DateTime startDate = DateTime.Parse(startTime);
            DateTime endDate = DateTime.Parse(endTime);

            LeaveView leave = new LeaveView();
            leave.EmpNo = empNo;
            leave.StartTime = DateTime.Parse(startTime);
            leave.EndTime = DateTime.Parse(endTime);
            leave.Deputy = deputy;
            leave.LeaveKind = leaveNumber;
            leave.LeaveDetailNumber = leaveDetailNumber;
            leave.Reason = "test";
            leave.ProjectNumber = "RP19553";
            leave.Location = "地點";
            leave.UpdatedEmpNo = empNo;
            leave.UpdatedTime = DateTime.Parse("2023-11-07T09:36:55.684+08:00");
            leave.UpdatedIP = "**************";
            leave.UpdatedHost = "VS2017";
            leave.AddSigners = addSigners;
            leave.CreatedTime = createdTime;
            leave.FilledTime = filledTime;
            leave.Confirmed = true;
            if (!string.IsNullOrWhiteSpace(eventDate))
            {
                leave.EventDate = DateTime.Parse(eventDate);
            }

            CardCheckResult result = await _c1CardBo.AddC1Card(empNo, leave, "127.0.0.1", "DummyHost");
            return result;
        }

        // <summary>
        /// 測試執行長請假流程
        /// </summary>
        [Fact]
        public void AddFlows_CEO_Test()
        {
            List<string> listDeputyCEO = _sinoSignBo.GetRoleDefaultUser("S03");
            if (_IsMultipleVicePresidents)
            {
                listDeputyCEO = _sinoSignBo.GetRoleDefaultUser("S04");
            }
            string deputyCEO = listDeputyCEO[0];
            List<string> listCEO = _sinoSignBo.GetRoleDefaultUser("S02");
            if (listCEO.Count == 0)
            {
                listCEO = _sinoSignBo.GetRoleUsers("S02");
            }
            if (listCEO.Count == 0)
            {
                return;
            }
            string empNoCEO = listCEO[0];

            Employee? employee = _employeeBo.GetEmployeeDetail(empNoCEO);
            Assert.NotNull(employee);
            string chariman = _sinoSignBo.GetRoleDefaultUser("S01")[0];
            C1Card c1Card = new C1Card();
            c1Card.EmpNo = chariman;
            c1Card.StartDate = new DateTime(2023, 4, 24, 8, 0, 0, DateTimeKind.Local);
            c1Card.EndDate = new DateTime(2023, 4, 24, 17, 0, 0, DateTimeKind.Local);
            c1Card.LeaveNumber = LeaveKindEnum.AnnualLeave;

            Form form = new Form();
            _c1CardBo.SetFlows(c1Card, form, employee);

            Assert.Single(form.Flows); // 單日假單無代理人只到董事長
            if (empNoCEO == deputyCEO)
            {
                // 如果執行長與副執行長相同
                Assert.Equal("執行長", form.Flows[0].FlowName);
                return;
            }

            Assert.Equal("董事長", form.Flows[0].FlowName);
            Assert.False(form.Flows[0].IsNotification);

            c1Card.Deputy = deputyCEO;
            form = new Form();
            _c1CardBo.SetFlows(c1Card, form, employee);
            Assert.Equal(2, form.Flows.Count); // 單日假單先通知代理人再到董事長
            Assert.Equal("代理人", form.Flows[0].FlowName);
            Assert.True(form.Flows[0].IsNotification);
            Assert.Equal("董事長", form.Flows[1].FlowName);
            Assert.False(form.Flows[1].IsNotification);

            c1Card.StartDate = new DateTime(2023, 4, 24, 8, 0, 0);
            c1Card.EndDate = new DateTime(2023, 4, 25, 17, 0, 0);
            c1Card.LeaveNumber = LeaveKindEnum.AnnualLeave;
            //c1Card.Hour = 16;
            form = new Form();
            _c1CardBo.SetFlows(c1Card, form, employee);
            Assert.Equal(2, form.Flows.Count); // 2日假單先通知代理人再到董事長
            Assert.Equal("代理人", form.Flows[0].FlowName);
            Assert.True(form.Flows[0].IsNotification);
            Assert.Equal("董事長", form.Flows[1].FlowName);
            Assert.False(form.Flows[1].IsNotification);
        }

        [Fact]
        public void AddFlows_DepartmentManager_Test()
        {
            C1Card c1Card = new C1Card();
            c1Card.EmpNo = "2096";
            Employee employee = _employeeBo.GetEmployeeDetail(c1Card.EmpNo);
            c1Card.StartDate = new DateTime(2023, 4, 24, 8, 0, 0);
            c1Card.EndDate = new DateTime(2023, 4, 24, 17, 0, 0);
            c1Card.LeaveNumber = LeaveKindEnum.AnnualLeave;
            //c1Card.Hour = 8;
            Form form = new Form();
            _c1CardBo.SetFlows(c1Card, form, employee);
            Assert.Equal(2, form.Flows.Count); // 單日假單無代理人到副執行長及通知部門登記桌
            Assert.Equal("副執行長", form.Flows[0].FlowName);
            Assert.Equal("部門登記桌", form.Flows[1].FlowName);
            Assert.False(form.Flows[0].IsNotification);
            Assert.True(form.Flows[1].IsNotification);

            form = new Form();
            c1Card.Deputy = "2016";
            _c1CardBo.SetFlows(c1Card, form, employee);
            Assert.Equal(3, form.Flows.Count);   // 單日假單只通知代理人、到副執行長及通知部門登記桌
            Assert.Equal("代理人", form.Flows[0].FlowName);
            Assert.Equal("副執行長", form.Flows[1].FlowName);
            Assert.Equal("部門登記桌", form.Flows[2].FlowName);

            Assert.True(form.Flows[0].IsNotification);
            Assert.False(form.Flows[1].IsNotification);
            Assert.True(form.Flows[2].IsNotification);

            form = new Form();
            c1Card.AddSigners = "2092";
            _c1CardBo.SetFlows(c1Card, form, employee);
            Assert.Equal(4, form.Flows.Count); // 單日假單通知代理人再加會人員與副執行長及通知部門登記桌
            Assert.Equal("代理人", form.Flows[0].FlowName);
            Assert.True(form.Flows[0].IsNotification);
            Assert.Equal("加會人員", form.Flows[1].FlowName);
            Assert.Equal("副執行長", form.Flows[2].FlowName);
            Assert.False(form.Flows[2].IsNotification);
            Assert.Equal("部門登記桌", form.Flows[3].FlowName);
            Assert.True(form.Flows[3].IsNotification);


            c1Card.StartDate = new DateTime(2023, 4, 24, 8, 0, 0);
            c1Card.EndDate = new DateTime(2023, 4, 25, 17, 0, 0);
            //c1Card.Hour = 16; //2天到執行長            
            c1Card.AddSigners = string.Empty; // 無加會人員
            form = new Form();
            _c1CardBo.SetFlows(c1Card, form, employee);
            Assert.Equal(4, form.Flows.Count);
            Assert.Equal("代理人", form.Flows[0].FlowName);
            Assert.Equal("副執行長", form.Flows[1].FlowName);
            Assert.Equal("執行長", form.Flows[2].FlowName);
            Assert.Equal("部門登記桌", form.Flows[3].FlowName);
            Assert.True(form.Flows[3].IsNotification);
            // 1. 通知代理人 2.副執行長 3.執行長 4.通知部門登記桌

            c1Card.AddSigners = "2092"; // 加會人員
            form = new Form();
            _c1CardBo.SetFlows(c1Card, form, employee);
            Assert.Equal(5, form.Flows.Count);
            Assert.Equal("代理人", form.Flows[0].FlowName);
            Assert.Equal("加會人員", form.Flows[1].FlowName);
            Assert.Equal("副執行長", form.Flows[2].FlowName);
            Assert.Equal("執行長", form.Flows[3].FlowName);
            Assert.Equal("部門登記桌", form.Flows[4].FlowName);
            // 1. 通知代理人 2.加會人員 3.副執行長 4.執行長 5.通知部門登記桌

            c1Card.StartDate = new DateTime(2023, 4, 24, 8, 0, 0);
            c1Card.EndDate = new DateTime(2023, 5, 3, 17, 0, 0);
            //c1Card.Hour = 56; //7天到董事長
            form = new Form();
            _c1CardBo.SetFlows(c1Card, form, employee);
            Assert.Equal(6, form.Flows.Count);
            // 1. 通知代理人 2.加會人員 3.副執行長 4.執行長 5.董事長 6.通知部門登記桌
            Assert.Equal("代理人", form.Flows[0].FlowName);
            Assert.Equal("加會人員", form.Flows[1].FlowName);
            Assert.Equal("副執行長", form.Flows[2].FlowName);
            Assert.Equal("執行長", form.Flows[3].FlowName);
            Assert.Equal("董事長", form.Flows[4].FlowName);
            Assert.Equal("部門登記桌", form.Flows[5].FlowName);

            c1Card.AddSigners = string.Empty; // 無加會人員
            form = new Form();
            _c1CardBo.SetFlows(c1Card, form, employee);
            Assert.Equal(5, form.Flows.Count);
            // 1. 通知代理人 2.副執行長 3.執行長 4.董事長 5.通知部門登記桌
            Assert.Equal("代理人", form.Flows[0].FlowName);
            Assert.True(form.Flows[0].IsNotification);
            Assert.Equal("副執行長", form.Flows[1].FlowName);
            Assert.Equal("執行長", form.Flows[2].FlowName);
            Assert.Equal("董事長", form.Flows[3].FlowName);
            Assert.Equal("部門登記桌", form.Flows[4].FlowName);
            Assert.True(form.Flows[4].IsNotification);
        }


        /// <summary>
        /// 測試副執行長請假流程
        /// </summary>
        [Fact]
        public void AddFlows_DeputyCEO_Test()
        {
            List<string> listDeputyCEO = _sinoSignBo.GetRoleDefaultUser("S03");
            if (listDeputyCEO.Count == 0)
            {
                listDeputyCEO = _sinoSignBo.GetRoleUsers("S03");
            }

            if (_IsMultipleVicePresidents && listDeputyCEO.Count == 0)
            {
                listDeputyCEO = _sinoSignBo.GetRoleUsers("S04");
            }

            string empNo = listDeputyCEO[0];
            if (string.IsNullOrWhiteSpace(empNo))
            {
                return;
            }
#pragma warning disable CS8600 // Converting null literal or possible null value to non-nullable type.
            Employee employee = _employeeBo.GetEmployeeDetail(empNo);
#pragma warning restore CS8600 // Converting null literal or possible null value to non-nullable type.

            var defaultCEOList = _sinoSignBo.GetRoleDefaultUser("S02");
            if (defaultCEOList.Count == 0)
            {
                defaultCEOList = _sinoSignBo.GetRoleUsers("S02");
            }
            string empNoCEO = defaultCEOList[0];
            string chariman = _sinoSignBo.GetRoleUsers("S01")[0];
            C1Card c1Card = new C1Card();
            c1Card.EmpNo = empNo;
            c1Card.StartDate = new DateTime(2023, 4, 24, 8, 0, 0);
            c1Card.EndDate = new DateTime(2023, 4, 24, 17, 0, 0);
            c1Card.LeaveNumber = LeaveKindEnum.AnnualLeave;
            //c1Card.Hour = 8;
            Form form = new Form();
            _c1CardBo.SetFlows(c1Card, form, employee);
            Assert.Single(form.Flows); // 單日假單無代理人只到執行長
            Assert.Equal("執行長", form.Flows[0].FlowName);
            Assert.False(form.Flows[0].IsNotification);

            c1Card.Deputy = empNoCEO;
            form = new Form();
            _c1CardBo.SetFlows(c1Card, form, employee);
            Assert.Equal(2, form.Flows.Count); // 單日假單先通知代理人再到執行長
            Assert.Equal("代理人", form.Flows[0].FlowName);
            Assert.True(form.Flows[0].IsNotification);
            Assert.Equal("執行長", form.Flows[1].FlowName);
            Assert.False(form.Flows[1].IsNotification);

            c1Card.StartDate = new DateTime(2023, 4, 24, 8, 0, 0);
            c1Card.EndDate = new DateTime(2023, 4, 25, 17, 0, 0);
            c1Card.LeaveNumber = LeaveKindEnum.AnnualLeave;
            //c1Card.Hour = 16;
            form = new Form();
            _c1CardBo.SetFlows(c1Card, form, employee);
            Assert.Equal(2, form.Flows.Count); // 2日假單先通知代理人再到執行長
            Assert.Equal("代理人", form.Flows[0].FlowName);
            Assert.True(form.Flows[0].IsNotification);
            Assert.Equal("執行長", form.Flows[1].FlowName);
            Assert.False(form.Flows[1].IsNotification);

            c1Card.StartDate = new DateTime(2023, 4, 24, 8, 0, 0);
            c1Card.EndDate = new DateTime(2023, 4, 25, 17, 0, 0);
            c1Card.LeaveNumber = LeaveKindEnum.AnnualLeave;
            //c1Card.Hour = 16;
            form = new Form();
            _c1CardBo.SetFlows(c1Card, form, employee);
            Assert.Equal(2, form.Flows.Count); // 2日假單先通知代理人再到執行長，最後到董事長
            Assert.Equal("代理人", form.Flows[0].FlowName);
            Assert.True(form.Flows[0].IsNotification);
            Assert.Equal("執行長", form.Flows[1].FlowName);
            Assert.False(form.Flows[1].IsNotification);

            c1Card.StartDate = new DateTime(2023, 4, 24, 8, 0, 0);
            c1Card.EndDate = new DateTime(2023, 4, 26, 17, 0, 0);
            c1Card.LeaveNumber = LeaveKindEnum.AnnualLeave;
            //c1Card.Hour = 24;
            form = new Form();
            _c1CardBo.SetFlows(c1Card, form, employee);
            Assert.Equal(3, form.Flows.Count); // 3日假單先通知代理人再到執行長，最後到董事長
            Assert.Equal("代理人", form.Flows[0].FlowName);
            Assert.True(form.Flows[0].IsNotification);
            Assert.Equal("執行長", form.Flows[1].FlowName);
            Assert.False(form.Flows[1].IsNotification);
            Assert.Equal("董事長", form.Flows[2].FlowName);
            Assert.False(form.Flows[2].IsNotification);

            c1Card.Deputy = empNoCEO;
            c1Card.AddSigners = chariman;// 加會董事長
            form = new Form();
            _c1CardBo.SetFlows(c1Card, form, employee);
            Assert.Equal(4, form.Flows.Count); // 3日假單先通知代理人再到加會人員，執行長，最後到董事長
            Assert.Equal("代理人", form.Flows[0].FlowName);
            Assert.True(form.Flows[0].IsNotification);
            Assert.Equal("加會人員", form.Flows[1].FlowName);
            Assert.False(form.Flows[1].IsNotification);
            Assert.Equal("執行長", form.Flows[2].FlowName);
            Assert.False(form.Flows[2].IsNotification);
            Assert.Equal("董事長", form.Flows[3].FlowName);
            Assert.False(form.Flows[3].IsNotification);

            c1Card.StartDate = new DateTime(2023, 4, 24, 8, 0, 0);
            c1Card.EndDate = new DateTime(2023, 4, 28, 17, 0, 0);
            c1Card.LeaveNumber = LeaveKindEnum.AnnualLeave;
            //c1Card.Hour = 40;
            c1Card.Deputy = empNoCEO;
            c1Card.AddSigners = chariman;// 加會董事長
            form = new Form();
            _c1CardBo.SetFlows(c1Card, form, employee);
            Assert.Equal(4, form.Flows.Count); // 5日假單先通知代理人再到加會人員，執行長，最後到董事長
            Assert.Equal("代理人", form.Flows[0].FlowName);
            Assert.True(form.Flows[0].IsNotification);
            Assert.Equal("加會人員", form.Flows[1].FlowName);
            Assert.False(form.Flows[1].IsNotification);
            Assert.Equal("執行長", form.Flows[2].FlowName);
            Assert.False(form.Flows[2].IsNotification);
            Assert.Equal("董事長", form.Flows[3].FlowName);
            Assert.False(form.Flows[3].IsNotification);

            c1Card.StartDate = new DateTime(2023, 4, 24, 8, 0, 0);
            c1Card.EndDate = new DateTime(2023, 5, 3, 17, 0, 0);
            c1Card.LeaveNumber = LeaveKindEnum.AnnualLeave;
            //c1Card.Hour = 56;
            c1Card.Deputy = empNoCEO;
            c1Card.AddSigners = string.Empty;// 無加會
            form = new Form();
            _c1CardBo.SetFlows(c1Card, form, employee);
            Assert.Equal(3, form.Flows.Count); // 7日假單先通知代理人再到執行長，最後到董事長
            Assert.Equal("代理人", form.Flows[0].FlowName);
            Assert.True(form.Flows[0].IsNotification);
            Assert.Equal("執行長", form.Flows[1].FlowName);
            Assert.False(form.Flows[1].IsNotification);
            Assert.Equal("董事長", form.Flows[2].FlowName);
            Assert.False(form.Flows[2].IsNotification);
        }

        [Fact]
        public void AddFlows_GeneralStaff_Test()
        {
            C1Card c1Card = new C1Card();
            c1Card.EmpNo = "0349";
            Employee? employee = _employeeBo.GetEmployeeDetail(c1Card.EmpNo);
            Assert.NotNull(employee);
            c1Card.StartDate = new DateTime(2023, 4, 24, 8, 0, 0);
            c1Card.EndDate = new DateTime(2023, 4, 24, 17, 0, 0);
            c1Card.LeaveNumber = LeaveKindEnum.AnnualLeave;
            //c1Card.Hour = 8;
            Form form = new Form();
            _c1CardBo.SetFlows(c1Card, form, employee);
            Assert.Equal(2, form.Flows.Count); // 單日假單無代理人只到部門主管及通知部門登記桌
            Assert.False(form.Flows[0].IsNotification);
            Assert.True(form.Flows[1].IsNotification);

            form = new Form();
            c1Card.Deputy = "0305";
            _c1CardBo.SetFlows(c1Card, form, employee);
            Assert.Equal(3, form.Flows.Count);   // 單日假單只通知代理人與部門主管及通知部門登記桌

            form = new Form();
            c1Card.AddSigners = "0395";
            _c1CardBo.SetFlows(c1Card, form, employee);
            Assert.Equal(4, form.Flows.Count); // 單日假單通知代理人再加會人員與部門主管及通知部門登記桌

            c1Card.StartDate = new DateTime(2023, 4, 24, 8, 0, 0);
            c1Card.EndDate = new DateTime(2023, 4, 26, 17, 0, 0);
            //c1Card.Hour = 24; //3天到副執行長            
            form = new Form();
            _c1CardBo.SetFlows(c1Card, form, employee);
            Assert.Equal(5, form.Flows.Count);
            Assert.Equal("代理人", form.Flows[0].FlowName);
            Assert.Equal("加會人員", form.Flows[1].FlowName);
            Assert.Equal("部門主管", form.Flows[2].FlowName);
            Assert.Equal("副執行長", form.Flows[3].FlowName);
            Assert.Equal("部門登記桌", form.Flows[4].FlowName);
            // 1. 通知代理人 2.加會人員 3.部門主管 4.副執行長 5.通知部門登記桌

            c1Card.StartDate = new DateTime(2023, 4, 24, 8, 0, 0);
            c1Card.EndDate = new DateTime(2023, 5, 3, 17, 0, 0);
            //c1Card.Hour = 56; //7天到執行長
            form = new Form();
            _c1CardBo.SetFlows(c1Card, form, employee);
            Assert.Equal(6, form.Flows.Count);
            // 1. 通知代理人 2.加會人員 3.部門主管 4.副執行長 5.執行長 6.通知部門登記桌
            Assert.Equal("代理人", form.Flows[0].FlowName);
            Assert.Equal("加會人員", form.Flows[1].FlowName);
            Assert.Equal("部門主管", form.Flows[2].FlowName);
            Assert.Equal("副執行長", form.Flows[3].FlowName);
            Assert.Equal("執行長", form.Flows[4].FlowName);
            Assert.Equal("部門登記桌", form.Flows[5].FlowName);

            c1Card.AddSigners = "0305,0494"; // 加會2人
            form = new Form();
            _c1CardBo.SetFlows(c1Card, form, employee);
            Assert.Equal(7, form.Flows.Count);
            // 1. 通知代理人 2.加會人員 3.部門主管 4.副執行長 5.執行長 6.通知部門登記桌
            Assert.Equal("代理人", form.Flows[0].FlowName);
            Assert.Equal("加會人員", form.Flows[1].FlowName);
            Assert.Equal("0305", form.Flows[1].RecipientEmpNo);
            Assert.Equal("加會人員", form.Flows[2].FlowName);
            Assert.Equal("0494", form.Flows[2].RecipientEmpNo);
            Assert.Equal("部門主管", form.Flows[3].FlowName);
            Assert.Equal("副執行長", form.Flows[4].FlowName);
            Assert.Equal("執行長", form.Flows[5].FlowName);
            Assert.Equal("部門登記桌", form.Flows[6].FlowName);

            c1Card.AddSigners = "0494,0305,0494"; // 重覆加會，要能自動 dedup
            form = new Form();
            _c1CardBo.SetFlows(c1Card, form, employee);
            Assert.Equal(7, form.Flows.Count);
            // 1. 通知代理人 2.加會人員 3.部門主管 4.副執行長 5.執行長 6.通知部門登記桌
            Assert.Equal("代理人", form.Flows[0].FlowName);
            Assert.Equal("加會人員", form.Flows[1].FlowName);
            Assert.Equal("0494", form.Flows[1].RecipientEmpNo);
            Assert.Equal("加會人員", form.Flows[2].FlowName);
            Assert.Equal("0305", form.Flows[2].RecipientEmpNo);
            Assert.Equal("部門主管", form.Flows[3].FlowName);
            Assert.Equal("副執行長", form.Flows[4].FlowName);
            Assert.Equal("執行長", form.Flows[5].FlowName);
            Assert.Equal("部門登記桌", form.Flows[6].FlowName);
        }

        [Theory]
        [InlineData("01", "", 2, 16)]
        [InlineData("02", "", 2, 16)]
        [InlineData("03", "", 2, 16)]
        [InlineData("04", "", 2, 16)]
        [InlineData("05", "", 2, 16)]
        [InlineData("06", "", 2, 16)]
        [InlineData("07", "01", 5, 40)]
        [InlineData("07", "02", 5, 40)]
        [InlineData("07", "03", 5, 40)]
        [InlineData("07", "04", 5, 40)]
        [InlineData("08", "", 2, 16)]
        [InlineData("09", "", 2, 16)]
        [InlineData("10", "", 2, 16)]
        [InlineData("11", "01", 2, 16)]
        [InlineData("11", "02", 2, 16)]
        [InlineData("11", "03", 2, 16)]
        [InlineData("11", "04", 2, 16)]
        [InlineData("11", "05", 2, 16)]
        [InlineData("11", "06", 2, 16)]
        [InlineData("11", "07", 2, 16)]
        [InlineData("11", "21", 2, 16)]
        [InlineData("11", "22", 2, 16)]
        [InlineData("11", "23", 2, 16)]
        [InlineData("11", "24", 2, 16)]
        [InlineData("11", "25", 2, 16)]
        [InlineData("11", "26", 2, 16)]
        [InlineData("11", "27", 2, 16)]
        [InlineData("11", "28", 2, 16)]
        [InlineData("11", "29", 2, 16)]
        [InlineData("11", "30", 2, 16)]
        [InlineData("11", "31", 2, 16)]
        [InlineData("11", "32", 2, 16)]
        [InlineData("11", "33", 2, 16)]
        [InlineData("11", "34", 2, 16)]
        [InlineData("11", "35", 2, 16)]
        [InlineData("11", "36", 2, 16)]
        [InlineData("11", "51", 2, 16)]
        [InlineData("11", "52", 2, 16)]
        [InlineData("11", "53", 2, 16)]
        [InlineData("11", "54", 2, 16)]
        [InlineData("11", "55", 2, 16)]
        [InlineData("12", "", 2, 16)]
        [InlineData("14", "", 2, 16)]
        [InlineData("15", "", 2, 16)]
        [InlineData("16", "", 2, 16)]
        [InlineData("17", "", 2, 16)]
        [InlineData("19", "", 2, 16)]
        [InlineData("21", "", 2, 16)]
        [InlineData("22", "", 2, 16)]
        public void C1CardDto2C1CardTest(string c1Code, string c1Code2, int expectedDays, int expectedC1Hours)
        {
            C1CardDto c1CardDto = new C1CardDto();
            c1CardDto.C1_CODE = c1Code.PadLeft(2, ' ');
            c1CardDto.C1_CODE2 = c1Code2.PadLeft(2, ' ');
            c1CardDto.C1_StartDate = new DateTime(2023, 4, 28, 8, 0, 0, DateTimeKind.Local);
            c1CardDto.C1_EndDate = new DateTime(2023, 4, 28, 17, 0, 0, DateTimeKind.Local);
            c1CardDto.C1_HOUR = 8;
            if (c1Code == "07")
            {
                c1CardDto.C1_HOUR = 24;
            }
            List<C1CardDto> c1CardDtos = new List<C1CardDto> { c1CardDto };
            C1CardDto c1CardDto2 = new C1CardDto();
            c1CardDto2.C1_CODE = c1Code;
            c1CardDto2.C1_CODE2 = c1Code2;
            c1CardDto2.C1_StartDate = new DateTime(2023, 5, 2, 8, 0, 0, DateTimeKind.Local);
            c1CardDto2.C1_EndDate = new DateTime(2023, 5, 2, 17, 0, 0, DateTimeKind.Local);
            c1CardDto2.C1_HOUR = 8;
            if (c1Code == "07")
            {
                c1CardDto2.C1_HOUR = 16;
            }
            c1CardDtos.Add(c1CardDto2);
            C1Card c1Card = _c1CardBo.C1CardDto2C1Card(c1CardDtos);
            c1Card.LeaveUnit = _c1CardBo.GetLeaveUnit(c1Card);
            c1Card.LeaveMinimumUnit = _c1CardBo.GetLeaveMinimumUnit(c1Card);
            Assert.NotNull(c1Card);
            Assert.Equal(expectedC1Hours, c1Card.Hours);
            Assert.Equal(expectedDays, c1Card.Days);

            c1CardDtos = new List<C1CardDto> { c1CardDto2, c1CardDto };

            c1Card = _c1CardBo.C1CardDto2C1Card(c1CardDtos);
            Assert.NotNull(c1Card);
            Assert.Equal(expectedC1Hours, c1Card.Hours);
            Assert.Equal(expectedDays, c1Card.Days);
        }

        [Fact]
        public void C1CardDto2ListC1CardDtos_CalendarDay_Test()
        {
            string json = @"{""ID"":null,""FormUID"":""98b86fce-e7bc-4494-a7fa-7bcf20b9ac88"",""C1_EMPNO"":""2130"",""C1_YYMM"":""11210"",""C1_SDD"":""27"",""C1_SHH"":""08"",""C1_SMM"":""00"",""C1_EDD"":""21"",""C1_EHH"":""17"",""C1_EMM"":""00"",""C1_HOUR"":320,""C1_CODE"":""07"",""C1_CODE2"":""01"",""C1_EventDate"":""2023-10-26T00:00:00+08:00"",""C1_DeadlineStartDate"":""0001-01-01T00:00:00"",""C1_DeadlineEndDate"":""0001-01-01T00:00:00"",""C1_RelationSheetNo"":"""",""C1_LeaveMaximum"":0,""C1_LeaveUnit"":"""",""C1_PrjNo"":"""",""C1_Location"":"""",""C1_Reason"":""事由XXX"",""C1_Agent"":""0178"",""C1_WYYMMDD"":""1121020"",""C1_AYYMMDD"":null,""C1_STATUS"":1,""C1_OVER"":""N"",""C1_SHEETNO"":""假112000006"",""C1_SERIALNO"":""1"",""C1_SOURCE"":""Attendance"",""C1_PREMON"":0,""C1_CHECK"":""0"",""ExpDate"":null,""C1_StartDate"":""2023-10-27T08:00:00+08:00"",""C1_EndDate"":""2023-12-21T17:00:00+08:00"",""C1_WDate"":""2023-10-20T17:46:27.747+08:00"",""C1_ADate"":null,""UpdatedEmpNo"":""0395"",""UpdatedName"":""曾騰毅"",""UpdatedTime"":""2023-10-20T17:46:27.747+08:00"",""UpdatedIP"":""**************"",""UpdatedHost"":""VS2017""}";
            C1CardDto? c1CardDto = JsonConvert.DeserializeObject<C1CardDto>(json);
            if (c1CardDto == null)
            {
                return;
            }
            List<C1CardDto> list = _c1CardBo.C1CardDto2ListC1CardDtos(c1CardDto);
            Assert.Equal(3, list.Count);
            Assert.Equal("27", list[0].C1_SDD);
            Assert.Equal("01", list[1].C1_SDD);
            Assert.Equal("01", list[2].C1_SDD);
            Assert.Equal("31", list[0].C1_EDD);
            Assert.Equal("30", list[1].C1_EDD);
            Assert.Equal("21", list[2].C1_EDD);
            Assert.Equal("08", list[0].C1_SHH);
            Assert.Equal("08", list[1].C1_SHH);
            Assert.Equal("08", list[2].C1_SHH);
            Assert.Equal("17", list[0].C1_EHH);
            Assert.Equal("17", list[1].C1_EHH);
            Assert.Equal("17", list[2].C1_EHH);

            DateTime start = new DateTime(2023, 10, 27, 8, 0, 0, DateTimeKind.Local);
            Assert.Equal(start, list[0].C1_StartDate);
            start = new DateTime(2023, 11, 1, 8, 0, 0, DateTimeKind.Local);
            Assert.Equal(start, list[1].C1_StartDate);
            start = new DateTime(2023, 12, 1, 8, 0, 0, DateTimeKind.Local);
            Assert.Equal(start, list[2].C1_StartDate);
            DateTime end = new DateTime(2023, 10, 31, 17, 0, 0, DateTimeKind.Local);
            Assert.Equal(end, list[0].C1_EndDate);
            end = new DateTime(2023, 11, 30, 17, 0, 0, DateTimeKind.Local);
            Assert.Equal(end, list[1].C1_EndDate);
            end = new DateTime(2023, 12, 21, 17, 0, 0, DateTimeKind.Local);
            Assert.Equal(end, list[2].C1_EndDate);

            int hours = 40;
            Assert.Equal(hours, list[0].C1_HOUR);
            hours = 240;
            Assert.Equal(hours, list[1].C1_HOUR);
            hours = 168;
            Assert.Equal(hours, list[2].C1_HOUR);
        }

        [Fact]
        public void C1CardDto2ListC1CardDtos_CalendarDay_Test2()
        {
            string json = @"{""ID"":null,""FormUID"":""ef8df36c-484c-402b-85d3-2fb40aa1fef4"",""C1_EMPNO"":""2130"",""C1_YYMM"":""11210"",""C1_SDD"":""05"",""C1_SHH"":""08"",""C1_SMM"":""00"",""C1_EDD"":""01"",""C1_EHH"":""17"",""C1_EMM"":""00"",""C1_HOUR"":144,""C1_CODE"":""07"",""C1_CODE2"":""02"",""C1_EventDate"":""2023-10-05T00:00:00+08:00"",""C1_DeadlineStartDate"":""0001-01-01T00:00:00"",""C1_DeadlineEndDate"":""0001-01-01T00:00:00"",""C1_RelationSheetNo"":"""",""C1_LeaveMaximum"":0,""C1_LeaveUnit"":"""",""C1_PrjNo"":"""",""C1_Location"":"""",""C1_Reason"":""測試流產假4星期"",""C1_Agent"":""0178"",""C1_WYYMMDD"":""1121023"",""C1_AYYMMDD"":null,""C1_STATUS"":1,""C1_OVER"":""N"",""C1_SHEETNO"":""假112000008"",""C1_SERIALNO"":""1"",""C1_SOURCE"":""Attendance"",""C1_PREMON"":0,""C1_CHECK"":""0"",""ExpDate"":null,""C1_StartDate"":""2023-10-05T08:00:00+08:00"",""C1_EndDate"":""2023-11-01T17:00:00+08:00"",""C1_WDate"":""2023-10-23T16:13:45.625+08:00"",""C1_ADate"":null,""UpdatedEmpNo"":""0395"",""UpdatedName"":""曾騰毅"",""UpdatedTime"":""2023-10-23T16:13:45.625+08:00"",""UpdatedIP"":""**************"",""UpdatedHost"":""VS2017""}";
            C1CardDto? c1CardDto = JsonConvert.DeserializeObject<C1CardDto>(json);
            if (c1CardDto == null)
            {
                return;
            }
            List<C1CardDto> list = _c1CardBo.C1CardDto2ListC1CardDtos(c1CardDto);
            Assert.Equal(2, list.Count);
            Assert.Equal("05", list[0].C1_SDD);
            Assert.Equal("01", list[1].C1_SDD);

            Assert.Equal("31", list[0].C1_EDD);
            Assert.Equal("01", list[1].C1_EDD);

            Assert.Equal("08", list[0].C1_SHH);
            Assert.Equal("08", list[1].C1_SHH);

            Assert.Equal("17", list[0].C1_EHH);
            Assert.Equal("17", list[1].C1_EHH);


            DateTime start = new DateTime(2023, 10, 5, 8, 0, 0, DateTimeKind.Local);
            Assert.Equal(start, list[0].C1_StartDate);
            start = new DateTime(2023, 11, 1, 8, 0, 0, DateTimeKind.Local);
            Assert.Equal(start, list[1].C1_StartDate);

            DateTime end = new DateTime(2023, 10, 31, 17, 0, 0, DateTimeKind.Local);
            Assert.Equal(end, list[0].C1_EndDate);
            end = new DateTime(2023, 11, 1, 17, 0, 0, DateTimeKind.Local);
            Assert.Equal(end, list[1].C1_EndDate);


            int hours = 216;
            Assert.Equal(hours, list[0].C1_HOUR);
            hours = 8;
            Assert.Equal(hours, list[1].C1_HOUR);

        }

        [Fact]
        public void C1CardDto2ListC1CardDtos_CalendarDay_Test3()
        {
            string json = @"{""ID"":null,""FormUID"":""ef8df36c-484c-402b-85d3-2fb40aa1fef4"",""C1_EMPNO"":""2130"",""C1_YYMM"":""11210"",""C1_SDD"":""06"",""C1_SHH"":""08"",""C1_SMM"":""00"",""C1_EDD"":""02"",""C1_EHH"":""17"",""C1_EMM"":""00"",""C1_HOUR"":144,""C1_CODE"":""07"",""C1_CODE2"":""02"",""C1_EventDate"":""2023-10-05T00:00:00+08:00"",""C1_DeadlineStartDate"":""0001-01-01T00:00:00"",""C1_DeadlineEndDate"":""0001-01-01T00:00:00"",""C1_RelationSheetNo"":"""",""C1_LeaveMaximum"":0,""C1_LeaveUnit"":"""",""C1_PrjNo"":"""",""C1_Location"":"""",""C1_Reason"":""測試流產假4星期"",""C1_Agent"":""0178"",""C1_WYYMMDD"":""1121023"",""C1_AYYMMDD"":null,""C1_STATUS"":1,""C1_OVER"":""N"",""C1_SHEETNO"":""假112000008"",""C1_SERIALNO"":""1"",""C1_SOURCE"":""Attendance"",""C1_PREMON"":0,""C1_CHECK"":""0"",""ExpDate"":null,""C1_StartDate"":""2023-10-06T08:00:00+08:00"",""C1_EndDate"":""2023-11-02T17:00:00+08:00"",""C1_WDate"":""2023-10-23T16:13:45.625+08:00"",""C1_ADate"":null,""UpdatedEmpNo"":""0395"",""UpdatedName"":""曾騰毅"",""UpdatedTime"":""2023-10-23T16:13:45.625+08:00"",""UpdatedIP"":""**************"",""UpdatedHost"":""VS2017""}";
            C1CardDto? c1CardDto = JsonConvert.DeserializeObject<C1CardDto>(json);
            if (c1CardDto == null)
            {
                return;
            }
            List<C1CardDto> list = _c1CardBo.C1CardDto2ListC1CardDtos(c1CardDto);
            Assert.Equal(2, list.Count);
            Assert.Equal("06", list[0].C1_SDD);
            Assert.Equal("01", list[1].C1_SDD);

            Assert.Equal("31", list[0].C1_EDD);
            Assert.Equal("02", list[1].C1_EDD);

            Assert.Equal("08", list[0].C1_SHH);
            Assert.Equal("08", list[1].C1_SHH);

            Assert.Equal("17", list[0].C1_EHH);
            Assert.Equal("17", list[1].C1_EHH);


            DateTime start = new DateTime(2023, 10, 6, 8, 0, 0, DateTimeKind.Local);
            Assert.Equal(start, list[0].C1_StartDate);
            start = new DateTime(2023, 11, 1, 8, 0, 0, DateTimeKind.Local);
            Assert.Equal(start, list[1].C1_StartDate);

            DateTime end = new DateTime(2023, 10, 31, 17, 0, 0, DateTimeKind.Local);
            Assert.Equal(end, list[0].C1_EndDate);
            end = new DateTime(2023, 11, 2, 17, 0, 0, DateTimeKind.Local);
            Assert.Equal(end, list[1].C1_EndDate);
            int hours = 208;
            Assert.Equal(hours, list[0].C1_HOUR);
            hours = 16;
            Assert.Equal(hours, list[1].C1_HOUR);
        }

        [Fact]
        public void C1CardDto2ListC1CardDtos_Workday_Test()
        {
            string json = @"{""ID"":null,""Name"":""C1Card"",""FormUID"":""e076dd62-dcc0-462c-a499-cbd49542f941"",""EmpNo"":""2130"",""StartDate"":""2021-05-31T08:00:00+08:00"",""EndDate"":""2021-06-02T17:00:00+08:00"",""Days"":3,""Hours"":24,""LeaveNumber"":1,""LeaveSubNumber"":0,""Reason"":""test1100531~1100602"",""ProjectNumber"":"""",""Location"":"""",""Deputy"":""0217"",""WDate"":""2023-10-23T10:33:39.988+08:00"",""EventDate"":null,""ExpirationStartDate"":""0001-01-01T00:00:00"",""ExpirationEndDate"":""0001-01-01T00:00:00"",""ADate"":null,""Status"":1,""OverPermittedHours"":false,""FormNumber"":"""",""LeaveMaximum"":0,""LeaveUnit"":"""",""RelatedFormNumber"":"""",""Confirmed"":true,""UpdatedEmpNo"":""0395"",""UpdatedName"":""曾騰毅"",""UpdatedTime"":""2023-10-23T10:33:39.988+08:00"",""UpdatedIP"":""**************"",""UpdatedHost"":""VS2017"",""AddSigners"":"""",""CreatedTime"":""2023-10-23T10:35:31.984+08:00"",""FilledTime"":""2023-10-23T10:33:39.988+08:00"",""UploadedFiles"":[],""RemindSigner"":false,""RemindMessageType"":0,""RemindMessage"":null}";
            C1Card? c1Card = JsonConvert.DeserializeObject<C1Card>(json);
            if (c1Card == null)
            {
                return;
            }
            _c1CardBo.GenerateC1CardDetail(c1Card);
            List<C1CardDto> list = _c1CardBo.C1CardToC1CardDtos(c1Card);
            Assert.Equal(2, list.Count);
            Assert.Equal("31", list[0].C1_SDD);
            Assert.Equal("01", list[1].C1_SDD);
            Assert.Equal("31", list[0].C1_EDD);
            Assert.Equal("02", list[1].C1_EDD);
            Assert.Equal("08", list[0].C1_SHH);
            Assert.Equal("08", list[1].C1_SHH);
            Assert.Equal("17", list[0].C1_EHH);
            Assert.Equal("17", list[1].C1_EHH);


            DateTime start = new DateTime(2021, 5, 31, 8, 0, 0, DateTimeKind.Local);
            Assert.Equal(start, list[0].C1_StartDate);
            start = new DateTime(2021, 6, 1, 8, 0, 0, DateTimeKind.Local);
            Assert.Equal(start, list[1].C1_StartDate);
            DateTime end = new DateTime(2021, 5, 31, 17, 0, 0, DateTimeKind.Local);
            Assert.Equal(end, list[0].C1_EndDate);
            end = new DateTime(2021, 6, 2, 17, 0, 0, DateTimeKind.Local);
            Assert.Equal(end, list[1].C1_EndDate);

            int hours = 8;
            Assert.Equal(hours, list[0].C1_HOUR);
            hours = 16;
            Assert.Equal(hours, list[1].C1_HOUR);
        }

        [Fact]
        public void C1CardDto2ListC1CardDtos_Workday_Test2()
        {
            string json = @"{""ID"":null,""Name"":""C1Card"",""FormUID"":""5c0f7f9e-1638-47b9-aa30-1418cb6934cc"",""EmpNo"":""2130"",""StartDate"":""2023-08-31T14:00:00+08:00"",""EndDate"":""2023-09-01T11:00:00+08:00"",""Days"":0,""Hours"":6,""LeaveNumber"":1,""LeaveSubNumber"":0,""Reason"":""109-08-31 ~ 109-09-01"",""ProjectNumber"":"""",""Location"":"""",""Deputy"":""1803"",""WDate"":""2023-10-23T10:42:51.664+08:00"",""EventDate"":null,""ExpirationStartDate"":""0001-01-01T00:00:00"",""ExpirationEndDate"":""0001-01-01T00:00:00"",""ADate"":null,""Status"":1,""OverPermittedHours"":false,""FormNumber"":"""",""LeaveMaximum"":0,""LeaveUnit"":"""",""RelatedFormNumber"":"""",""Confirmed"":true,""UpdatedEmpNo"":""0395"",""UpdatedName"":""曾騰毅"",""UpdatedTime"":""2023-10-23T10:42:51.664+08:00"",""UpdatedIP"":""**************"",""UpdatedHost"":""VS2017"",""AddSigners"":"""",""CreatedTime"":""2023-10-23T10:44:03.96+08:00"",""FilledTime"":""2023-10-23T10:42:51.664+08:00"",""UploadedFiles"":[],""RemindSigner"":false,""RemindMessageType"":0,""RemindMessage"":null}";
            C1Card? c1Card = JsonConvert.DeserializeObject<C1Card>(json);
            Assert.NotNull(c1Card);
            _c1CardBo.GenerateC1CardDetail(c1Card);
            List<C1CardDto> list = _c1CardBo.C1CardToC1CardDtos(c1Card);
            Assert.Equal(2, list.Count);
            Assert.Equal("31", list[0].C1_SDD);
            Assert.Equal("01", list[1].C1_SDD);
            Assert.Equal("31", list[0].C1_EDD);
            Assert.Equal("01", list[1].C1_EDD);
            Assert.Equal("14", list[0].C1_SHH);
            Assert.Equal("08", list[1].C1_SHH);
            Assert.Equal("17", list[0].C1_EHH);
            Assert.Equal("11", list[1].C1_EHH);
            DateTime start = new DateTime(2023, 8, 31, 14, 0, 0, DateTimeKind.Local);
            Assert.Equal(start, list[0].C1_StartDate);
            start = new DateTime(2023, 9, 1, 8, 0, 0, DateTimeKind.Local);
            Assert.Equal(start, list[1].C1_StartDate);
            DateTime end = new DateTime(2023, 8, 31, 17, 0, 0, DateTimeKind.Local);
            Assert.Equal(end, list[0].C1_EndDate);
            end = new DateTime(2023, 9, 1, 11, 0, 0, DateTimeKind.Local);
            Assert.Equal(end, list[1].C1_EndDate);
            int hours = 3;
            Assert.Equal(hours, list[0].C1_HOUR);
            hours = 3;
            Assert.Equal(hours, list[1].C1_HOUR);
        }

        [Fact]
        public void C1CardToC1CardDtosOldTest()
        {
            C1CardBo c1CardBo = (C1CardBo)_c1CardBo;
            string json = @"{""ID"":null,""Name"":""C1Card"",""FormUID"":""5c0f7f9e-1638-47b9-aa30-1418cb6934cc"",""EmpNo"":""2130"",""StartDate"":""2023-08-31T14:00:00+08:00"",""EndDate"":""2023-09-01T11:00:00+08:00"",""Days"":0,""Hours"":6,""LeaveNumber"":1,""LeaveSubNumber"":0,""Reason"":""109-08-31 ~ 109-09-01"",""ProjectNumber"":"""",""Location"":"""",""Deputy"":""1803"",""WDate"":""2023-10-23T10:42:51.664+08:00"",""EventDate"":null,""ExpirationStartDate"":""0001-01-01T00:00:00"",""ExpirationEndDate"":""0001-01-01T00:00:00"",""ADate"":null,""Status"":1,""OverPermittedHours"":false,""FormNumber"":"""",""LeaveMaximum"":0,""LeaveUnit"":"""",""RelatedFormNumber"":"""",""Confirmed"":true,""UpdatedEmpNo"":""0395"",""UpdatedName"":""曾騰毅"",""UpdatedTime"":""2023-10-23T10:42:51.664+08:00"",""UpdatedIP"":""**************"",""UpdatedHost"":""VS2017"",""AddSigners"":"""",""CreatedTime"":""2023-10-23T10:44:03.96+08:00"",""FilledTime"":""2023-10-23T10:42:51.664+08:00"",""UploadedFiles"":[],""RemindSigner"":false,""RemindMessageType"":0,""RemindMessage"":null}";
            C1Card? c1Card = JsonConvert.DeserializeObject<C1Card>(json);
            Assert.NotNull(c1Card);
            _c1CardBo.GenerateC1CardDetail(c1Card);
            List<C1CardDto> list = c1CardBo.C1CardToC1CardDtosOld(c1Card);
            Assert.NotNull(list);
        }

        [Theory]
        [InlineData(1, 0, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(2, 0, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(3, 0, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(4, 0, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(5, 0, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(6, 0, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(7, 1, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(7, 2, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(7, 3, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(7, 4, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(8, 0, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(9, 0, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(10, 0, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(11, 1, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(11, 2, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(11, 3, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(11, 4, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(11, 5, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(11, 6, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(11, 7, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(11, 21, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(11, 22, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(11, 23, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(11, 24, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(11, 25, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(11, 26, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(11, 27, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(11, 28, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(11, 29, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(11, 30, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(11, 31, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(11, 32, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(11, 33, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(11, 34, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(11, 35, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(11, 36, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(11, 51, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(11, 52, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(11, 53, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(11, 54, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(11, 55, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(12, 0, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(14, 0, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(15, 0, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(16, 0, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(17, 0, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(19, 0, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(21, 0, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        [InlineData(22, 0, "2023-4-28 8:0:0", "2023-5-2 15:55:0")]
        public void C1CardToC1CardDtosTest(int leaveNumber, int leaveSubNumber, DateTime startDate, DateTime endDate)
        {
            C1Card c1Card = new C1Card();
            c1Card.EmpNo = "0349";
            c1Card.CreatedTime = DateTime.Now;
            c1Card.WDate = ((DateTime)c1Card.CreatedTime).AddMinutes(2);
            c1Card.LeaveNumber = (LeaveKindEnum)leaveNumber;
            c1Card.LeaveSubNumber = leaveSubNumber;
            c1Card.StartDate = startDate;
            c1Card.EndDate = endDate;
            c1Card.Hours = _c1CardBo.CalculateTakeLeaveWorkingHours(c1Card.StartDate, c1Card.EndDate);
            _c1CardBo.GenerateC1CardDetail(c1Card);

            List<C1CardDto> c1CardDtos = _c1CardBo.C1CardToC1CardDtos(c1Card);
            Assert.NotNull(c1CardDtos);
            Assert.NotEmpty(c1CardDtos);
            string c1Code = leaveNumber.ToString("00");
            Assert.Equal(c1Code, c1CardDtos[0].C1_CODE);
            if (leaveSubNumber > 0)
            {
                string c1Code2 = leaveSubNumber.ToString("00");
                Assert.Equal(c1Code2, c1CardDtos[0].C1_CODE2);
            }
        }

        [Theory]
        [InlineData("2023-09-23", (int)LeaveKindEnum.AnnualLeave, 0, 1, "2023-09-23T17:00+08:00")]
        [InlineData("2023-09-23", (int)LeaveKindEnum.PostponedLeave, 0, 1, "2023-09-23T17:00+08:00")]
        [InlineData("2023-09-23", (int)LeaveKindEnum.CompensatoryLeave, 0, 1, "2023-09-23T17:00+08:00")]
        [InlineData("2023-09-23", (int)LeaveKindEnum.BusinessOutLeave, 0, 1, "2023-09-23T17:00+08:00")]
        [InlineData("2023-09-23", (int)LeaveKindEnum.BusinessTripLeave, 0, 1, "2023-09-23T17:00+08:00")]
        [InlineData("2023-09-23", (int)LeaveKindEnum.SickLeave, 0, 1, "2023-09-23T17:00+08:00")]
        [InlineData("2023-09-23", (int)LeaveKindEnum.PersonalLeave, 0, 1, "2023-09-23T17:00+08:00")]
        [InlineData("2023-09-23", (int)LeaveKindEnum.MenstrualLeave, 0, 1, "2023-09-23T17:00+08:00")]
        [InlineData("2023-09-23", (int)LeaveKindEnum.BirthdayLeave, 0, 1, "2023-09-23T17:00+08:00")]
        [InlineData("2023-09-23", (int)LeaveKindEnum.ObstetricInspectionLeave, 0, 1, "2023-09-23T17:00+08:00")]
        [InlineData("2023-09-23", (int)LeaveKindEnum.MarriageLeave, 0, 1, "2023-10-04T17:00+08:00")] // 8 working days
        [InlineData("2023-09-23", (int)LeaveKindEnum.PaternityLeave, 0, 1, "2023-09-23T17:00+08:00")]
        [InlineData("2023-09-23", (int)LeaveKindEnum.MaternityLeave, 1, 1, "2023-11-17T17:00+08:00")]
        [InlineData("2023-09-23", (int)LeaveKindEnum.MaternityLeave, 2, 1, "2023-10-20T17:00+08:00")]
        [InlineData("2023-09-23", (int)LeaveKindEnum.MaternityLeave, 3, 1, "2023-09-29T17:00+08:00")]
        [InlineData("2023-09-23", (int)LeaveKindEnum.MaternityLeave, 4, 1, "2023-09-27T17:00+08:00")]
        [InlineData("2023-09-15", (int)LeaveKindEnum.FuneralLeave, 1, 1, "2023-09-25T17:00+08:00")] // 8 working days
        [InlineData("2023-09-15", (int)LeaveKindEnum.FuneralLeave, 2, 1, "2023-09-25T17:00+08:00")] // 8 working days
        [InlineData("2023-09-15", (int)LeaveKindEnum.FuneralLeave, 3, 1, "2023-09-25T17:00+08:00")] // 8 working days
        [InlineData("2023-09-15", (int)LeaveKindEnum.FuneralLeave, 4, 1, "2023-09-25T17:00+08:00")] // 8 working days
        [InlineData("2023-09-15", (int)LeaveKindEnum.FuneralLeave, 5, 1, "2023-09-25T17:00+08:00")] // 8 working days
        [InlineData("2023-09-15", (int)LeaveKindEnum.FuneralLeave, 6, 1, "2023-09-25T17:00+08:00")] // 8 working days
        [InlineData("2023-09-15", (int)LeaveKindEnum.FuneralLeave, 7, 1, "2023-09-25T17:00+08:00")] // 8 working days
        [InlineData("2023-09-15", (int)LeaveKindEnum.FuneralLeave, 21, 1, "2023-09-22T17:00+08:00")] // 6 working days
        [InlineData("2023-09-15", (int)LeaveKindEnum.FuneralLeave, 22, 1, "2023-09-22T17:00+08:00")] // 6 working days
        [InlineData("2023-09-15", (int)LeaveKindEnum.FuneralLeave, 23, 1, "2023-09-22T17:00+08:00")] // 6 working days
        [InlineData("2023-09-15", (int)LeaveKindEnum.FuneralLeave, 24, 1, "2023-09-22T17:00+08:00")] // 6 working days
        [InlineData("2023-09-15", (int)LeaveKindEnum.FuneralLeave, 25, 1, "2023-09-22T17:00+08:00")] // 6 working days
        [InlineData("2023-09-15", (int)LeaveKindEnum.FuneralLeave, 26, 1, "2023-09-22T17:00+08:00")] // 6 working days
        [InlineData("2023-09-22", (int)LeaveKindEnum.FuneralLeave, 27, 1, "2023-09-25T17:00+08:00")] // 3 working days
        [InlineData("2023-09-22", (int)LeaveKindEnum.FuneralLeave, 28, 1, "2023-09-25T17:00+08:00")] // 3 working days
        [InlineData("2023-09-15", (int)LeaveKindEnum.FuneralLeave, 29, 1, "2023-09-22T17:00+08:00")] // 6 working days
        [InlineData("2023-09-15", (int)LeaveKindEnum.FuneralLeave, 30, 1, "2023-09-22T17:00+08:00")] // 6 working days
        [InlineData("2023-09-15", (int)LeaveKindEnum.FuneralLeave, 31, 1, "2023-09-22T17:00+08:00")] // 6 working days
        [InlineData("2023-09-15", (int)LeaveKindEnum.FuneralLeave, 32, 1, "2023-09-22T17:00+08:00")] // 6 working days
        [InlineData("2023-09-15", (int)LeaveKindEnum.FuneralLeave, 33, 1, "2023-09-22T17:00+08:00")] // 6 working days
        [InlineData("2023-09-15", (int)LeaveKindEnum.FuneralLeave, 34, 1, "2023-09-22T17:00+08:00")] // 6 working days
        [InlineData("2023-09-15", (int)LeaveKindEnum.FuneralLeave, 35, 1, "2023-09-19T17:00+08:00")] // 3 working days
        [InlineData("2023-09-15", (int)LeaveKindEnum.FuneralLeave, 36, 1, "2023-09-19T17:00+08:00")] // 3 working days
        [InlineData("2023-09-15", (int)LeaveKindEnum.FuneralLeave, 51, 1, "2023-09-19T17:00+08:00")] // 3 working days
        [InlineData("2023-09-15", (int)LeaveKindEnum.FuneralLeave, 52, 1, "2023-09-19T17:00+08:00")] // 3 working days
        [InlineData("2023-09-15", (int)LeaveKindEnum.FuneralLeave, 53, 1, "2023-09-19T17:00+08:00")] // 3 working days
        [InlineData("2023-09-15", (int)LeaveKindEnum.FuneralLeave, 54, 1, "2023-09-19T17:00+08:00")] // 3 working days
        [InlineData("2023-09-15", (int)LeaveKindEnum.FuneralLeave, 55, 1, "2023-09-19T17:00+08:00")] // 3 working days
        public void CalculateLeaveEndDateTest(DateTime startDate, int leaveNumber, int subKindNumber, int shiftId, DateTime expect)
        {
            DateTime date = _c1CardBo.CalculateLeaveEndDate(startDate, leaveNumber, subKindNumber, shiftId);
            Assert.Equal(expect, date);
        }

        [Theory]
        [InlineData(2, "2023-4-25 15:0:0", "2023-4-27 15:0:0")]
        [InlineData(1, "2023-4-25 16:0:0", "2023-4-27 15:0:0")]
        [InlineData(2, "2023-4-25 16:0:0", "2023-4-27 17:0:0")]
        [InlineData(3, "2023-4-25 16:0:0", "2023-4-28 17:0:0")]
        [InlineData(1, "2023-4-27 16:0:0", "2023-4-28 17:0:0")]
        [InlineData(1, "2023-4-27 16:0:0", "2023-4-29 9:0:0")]
        [InlineData(1, "2023-4-28 7:0:0", "2023-4-28 17:0:0")]
        [InlineData(1, "2023-4-28 8:0:0", "2023-4-28 17:0:0")]
        [InlineData(1, "2023-4-28 8:0:0", "2023-4-29 17:0:0")]
        [InlineData(1, "2023-4-28 8:0:0", "2023-4-30 17:0:0")]
        [InlineData(1, "2023-4-28 8:0:0", "2023-5-1 17:0:0")]
        [InlineData(2, "2023-4-28 8:0:0", "2023-5-2 17:0:0")]
        [InlineData(3, "2023-4-28 8:0:0", "2023-5-3 17:0:0")]
        [InlineData(4, "2023-4-28 8:0:0", "2023-5-4 17:0:0")]
        public void CalculateTakeLeaveWorkingDaysTest(int days, DateTime startDate, DateTime endDate)
        {
            int ret = _c1CardBo.CalculateTakeLeaveWorkingDays(startDate, endDate);
            Assert.Equal(days, ret);
        }

        [Theory]
        [InlineData(1, "2023-4-26 7:0:0", "2023-4-26 9:0:0")]
        [InlineData(1, "2023-4-26 7:30:0", "2023-4-26 9:0:0")]
        [InlineData(2, "2023-4-26 7:30:0", "2023-4-26 9:30:0")]
        [InlineData(10, "2023-4-26 16:0:0", "2023-4-28 9:0:0")]
        [InlineData(9, "2023-4-27 16:0:0", "2023-4-29 9:0:0")]
        [InlineData(8, "2023-4-27 2:0:0", "2023-4-27 17:30:0")]
        [InlineData(8, "2023-4-27 7:0:0", "2023-4-27 17:0:0")]
        [InlineData(8, "2023-4-27 7:0:0", "2023-4-27 18:0:0")]
        [InlineData(8, "2023-4-27 8:0:0", "2023-4-27 17:0:0")]
        [InlineData(8, "2023-4-27 8:0:0", "2023-4-27 18:0:0")]
        [InlineData(24, "2023-4-25 7:30:0", "2023-4-27 17:30:0")]
        public void CalculateTakeLeaveWorkingHoursTest(int expected, DateTime startDate, DateTime endDate)
        {
            int hours = _c1CardBo.CalculateTakeLeaveWorkingHours(startDate, endDate);
            Assert.Equal(expected, hours);
        }

        [Theory]
        [InlineData("2023-09-15", "2023-09-28")]
        public void CheckLeaveDaysTest(DateTime startDate, DateTime endDate)
        {
            string json = @"{""ID"":null,""Name"":""C1Card"",""FormUID"":""5c0f7f9e-1638-47b9-aa30-1418cb6934cc"",""EmpNo"":""2130"",""StartDate"":""2023-08-31T14:00:00+08:00"",""EndDate"":""2023-09-01T11:00:00+08:00"",""Days"":0,""Hours"":6,""LeaveNumber"":1,""LeaveSubNumber"":0,""Reason"":""109-08-31 ~ 109-09-01"",""ProjectNumber"":"""",""Location"":"""",""Deputy"":""1803"",""WDate"":""2023-10-23T10:42:51.664+08:00"",""EventDate"":null,""ExpirationStartDate"":""0001-01-01T00:00:00"",""ExpirationEndDate"":""0001-01-01T00:00:00"",""ADate"":null,""Status"":1,""OverPermittedHours"":false,""FormNumber"":"""",""LeaveMaximum"":0,""LeaveUnit"":"""",""RelatedFormNumber"":"""",""Confirmed"":true,""UpdatedEmpNo"":""0395"",""UpdatedName"":""曾騰毅"",""UpdatedTime"":""2023-10-23T10:42:51.664+08:00"",""UpdatedIP"":""**************"",""UpdatedHost"":""VS2017"",""AddSigners"":"""",""CreatedTime"":""2023-10-23T10:44:03.96+08:00"",""FilledTime"":""2023-10-23T10:42:51.664+08:00"",""UploadedFiles"":[],""RemindSigner"":false,""RemindMessageType"":0,""RemindMessage"":null}";
            C1Card? c1Card = JsonConvert.DeserializeObject<C1Card>(json);
            Assert.NotNull(c1Card);
            string result = _c1CardBo.CheckLeaveDays(startDate, endDate);
            Assert.Empty(result);
        }

        [Fact]
        public void FinishTest()
        {
            C1Card c1Card = new C1Card();
            FormStatus formStatus = new FormStatus();
            Updater updateDto = new Updater();
            Assert.Empty(c1Card.EmpNo);
            _c1CardBo.Finish(c1Card, formStatus, updateDto);
        }

        [Theory]
        [InlineData("2023/11/23 08:00+08:00", "2023/11/23 17:00+08:00", 1, 0, 8, "0349", "D", "112/11/23")]
        [InlineData("2023/11/23 08:00+08:00", "2023/11/23 17:00+08:00", 1, 0, 8, "0349", "H", "112/11/23")]
        [InlineData("2023/11/23 09:05+08:00", "2023/11/23 11:04+08:00", 1, 0, 2, "0349", "H", "112/11/23 09:05 ~ 11:04")]
        [InlineData("2023/11/23 08:00+08:00", "2023/11/24 17:00+08:00", 1, 0, 16, "0349", "D", "112/11/23 ~ 112/11/24")]
        [InlineData("2023/11/23 08:00+08:00", "2023/11/24 17:00+08:00", 1, 0, 16, "0349", "H", "112/11/23 ~ 112/11/24")]
        [InlineData("2023/11/23 08:00+08:00", "2023/11/24 16:00+08:00", 1, 0, 15, "0349", "H", "112/11/23 08:00 ~ 112/11/24 16:00")]
        public void GenerateFormInfoTest1Day(DateTime startDate, DateTime endDate, int leaveNumber, int leaveSubNumber, int hours, string empNo, string leaveUnit, string expect)
        {
            C1Card card = new C1Card();
            card.LeaveNumber = (LeaveKindEnum)leaveNumber;
            card.LeaveSubNumber = leaveSubNumber;
            card.LeaveUnit = leaveUnit;
            card.EmpNo = empNo;
            card.StartDate = startDate;
            card.EndDate = endDate;
            card.Hours = hours;
            string formInfo = _c1CardBo.GenerateFormInfo(card);
            Assert.Equal(expect, formInfo);
        }

        [Theory]
        [InlineData("2268", "1989-04-21")]
        [InlineData("2295", "1987-05-05")]
        public void GetBirthdayTest(string employeeNumber, DateTime birthday)
        {
            DateTime date = _c1CardBo.GetBirthday(employeeNumber);

            // Assert
            Assert.Equal(birthday, date);
        }

        [Fact]
        public void GetBirthdayWelfareTest()
        {
            DataTable dt = _c1CardBo.GetBirthdayWelfare(2022);
            Assert.NotEmpty(dt.Rows);
        }

        [Fact]
        public void GetCardTest()
        {
            CardBase? c1Card = _c1CardBo.GetCard(Guid.Empty);
            Assert.Null(c1Card);
        }

        [Theory]
        [InlineData("2268", "2022-01-28")]
        [InlineData("2295", "2022-02-22")]
        public void GetCompensatoryLeaveRemainingHoursTest(string employeeNumber, DateTime dt)
        {
            int actualHours = _c1CardBo.GetCompensatoryLeaveRemainingHours(employeeNumber, dt);

            // Assert
            Assert.True(actualHours == 0);
        }

        [Theory]
        [InlineData("0395", 2, 0, 0)]
        [InlineData("0349", 2, 0, 0)]
        [InlineData("2268", 2, 0, 0)]
        public void GetEventRelatedFormsTest(string empNo, int leaveNumber, int leaveSubNumber, int expected)
        {
            DataTable dt = _c1CardBo.GetEventRelatedSheets(empNo, leaveNumber, leaveSubNumber);
            Assert.Equal(expected, dt.Rows.Count);
        }

        [Fact]
        public void GetEventRelatedRecordTest()
        {
            string formNo = string.Empty;
            var result = _c1CardBo.GetEventRelatedRecord(formNo);
            Assert.Empty(result);
        }

        [Theory]
        [InlineData("0349", "2022-01-28")]
        [InlineData("0395", "2022-02-22")]
        public void GetFamilyCareLeaveYearApprovedHoursTest(string employeeNumber, DateTime dt)
        {
            int actualHours = _c1CardBo.GetFamilyCareLeaveYearApprovedHours(employeeNumber, dt);

            // Assert
            Assert.True(actualHours == 0);
        }

        [Theory]
        [InlineData("0349", "2022-01-28")]
        [InlineData("0395", "2022-02-22")]
        public void GetFamilyCareLeaveYearAvailableHoursTest(string employeeNumber, DateTime dt)
        {
            int actualHours = _c1CardBo.GetFamilyCareLeaveYearAvailableHours(employeeNumber, dt);

            // Assert
            Assert.True(actualHours == 0);
        }

        [Fact]
        public void GetFormCards_ProjNoTest()
        {
            DateTime startDate = new DateTime(2023, 5, 1, 0, 0, 0, DateTimeKind.Local);
            DateTime endDate = new DateTime(2023, 5, 31, 23, 59, 59, DateTimeKind.Local);
            string projNo = "RP19553";
            List<FormCard> formCards = _c1CardBo.GetFormCards(startDate, endDate, projNo);
            Assert.Empty(formCards);
        }

        [Fact]
        public void GetFormCardsTest()
        {
            DateTime startDate = new DateTime(2023, 5, 1, 0, 0, 0, DateTimeKind.Local);
            DateTime endDate = new DateTime(2023, 5, 31, 23, 59, 59, DateTimeKind.Local);
            List<FormCard> formCards = _c1CardBo.GetFormCards(startDate, endDate);
            Assert.Empty(formCards);
        }

        [Fact]
        public void GetFormCardTest()
        {
            string empNo = "0395";
            Guid guid = Guid.Empty;
            Form form = _formBo.GetForm(guid);
            FormCard formCard = _c1CardBo.GetFormCard(form, empNo);
            Assert.Empty(formCard.EmpNo);
        }

        [Fact]
        public void GetLeaveAttachmentPromptTest()
        {
            LeaveKindEnum kindenum = LeaveKindEnum.MarriageLeave;
            string ret = _c1CardBo.GetLeaveAttachmentPrompt(kindenum);
            Assert.Contains("檢附相關證明文件", ret);
        }

        [Theory]
        [InlineData(2, 0, "2023-4-25 15:0:0", "2023-4-27 15:0:0", 1)]
        [InlineData(1, 7, "2023-4-25 16:0:0", "2023-4-27 15:0:0", 1)]
        [InlineData(2, 1, "2023-4-25 16:0:0", "2023-4-27 17:0:0", 1)]
        [InlineData(3, 0, "2023-4-25 7:0:0", "2023-4-27 17:0:0", 1)]
        [InlineData(3, 1, "2023-4-25 16:0:0", "2023-4-28 17:0:0", 1)]
        [InlineData(3, 0, "2023-4-25 7:30:0", "2023-4-27 17:0:0", 1)]
        [InlineData(3, 0, "2023-4-25 7:30:0", "2023-4-27 17:30:0", 1)]
        [InlineData(1, 1, "2023-4-27 16:0:0", "2023-4-28 17:0:0", 1)]
        [InlineData(0, 2, "2023-4-26 8:0:0", "2023-4-26 10:0:0", 1)]
        [InlineData(1, 2, "2023-4-26 16:0:0", "2023-4-28 9:0:0", 1)]
        [InlineData(1, 1, "2023-4-27 16:0:0", "2023-4-29 9:0:0", 1)]
        [InlineData(1, 0, "2023-4-28 7:0:0", "2023-4-28 17:0:0", 1)]
        [InlineData(1, 0, "2023-4-28 8:0:0", "2023-4-28 17:0:0", 1)]
        [InlineData(1, 0, "2023-4-28 8:0:0", "2023-4-29 17:0:0", 1)]
        [InlineData(1, 0, "2023-4-28 8:0:0", "2023-4-30 17:0:0", 1)]
        [InlineData(1, 0, "2023-4-28 8:0:0", "2023-5-1 17:0:0", 1)]
        [InlineData(2, 0, "2023-4-28 8:0:0", "2023-5-2 17:0:0", 1)]
        [InlineData(3, 0, "2023-4-28 8:0:0", "2023-5-3 17:0:0", 1)]
        [InlineData(4, 0, "2023-4-28 8:0:0", "2023-5-4 17:0:0", 1)]
        [InlineData(2, 0, "2023-4-25 7:0:0", "2023-4-26 17:0:0", 7)]
        [InlineData(3, 0, "2023-4-25 7:0:0", "2023-4-27 17:0:0", 7)]
        [InlineData(2, 0, "2023-4-25 8:0:0", "2023-4-26 17:0:0", 7)]
        [InlineData(3, 0, "2023-4-25 8:0:0", "2023-4-27 17:0:0", 7)]
        public void GetLeaveDayHoursTest(int days, int hours, DateTime startDate, DateTime endDate, int leaveNumber)
        {
            string ret = _c1CardBo.GetLeaveDayHours(leaveNumber, startDate, endDate);
            string expect = string.Empty;
            if (days > 0)
            {
                expect += $"{days}天";
            }
            if (hours > 0)
            {
                expect += $"{hours}小時";
            }
            Assert.Equal(expect, ret);
        }

        [Theory]
        [InlineData(1, "特別休息假")]
        [InlineData(2, "婚假")]
        public void GetLeaveKindTest(int leaveNumber, string leaveName)
        {
            LeaveKind? leaveKind = _c1CardBo.GetLeaveKind((LeaveKindEnum)leaveNumber);
            Assert.NotNull(leaveKind);
            Assert.Equal(leaveName, leaveKind.Name);
        }

        [Theory]
        [InlineData(LeaveKindEnum.AnnualLeave, "H")]
        [InlineData(LeaveKindEnum.MarriageLeave, "D")]
        [InlineData(LeaveKindEnum.FuneralLeave, "F")]
        public void GetLeaveUnitTest(LeaveKindEnum kindEnum, string expect)
        {
            C1Card leave = new C1Card();
            leave.LeaveNumber = kindEnum;
            string unit = _c1CardBo.GetLeaveMinimumUnit(leave);
            Assert.Equal(expect, unit);
        }


        [Fact]
        public void GetMenstrualLeaveYearUsedHours_ShouldReturnCorrectHours()
        {
            // Arrange
            string employeeNumber = "0349";
            DateTime dt = new DateTime(2022, 10, 1, 0, 0, 0, DateTimeKind.Local);

            // Act
            int actualHours = _c1CardBo.GetMenstrualLeaveYearUsedHours(employeeNumber, dt);

            // Assert
            Assert.True(actualHours > 0);
        }

        [Theory]
        [InlineData("0349", "2022-01-28")]
        [InlineData("0395", "2022-02-22")]
        public void GetPersonalLeaveYearApprovedHoursTest(string employeeNumber, DateTime dt)
        {
            int actualHours = _c1CardBo.GetPersonalLeaveYearApprovedHours(employeeNumber, dt);

            // Assert
            Assert.True(actualHours == 0);
        }

        [Fact]
        public void GetRestKindsTest()
        {
            List<LeaveKind> restKinds = _c1CardBo.GetLeaveKinds();
            Assert.NotEmpty(restKinds);
        }

        [Theory]
        [InlineData("0349", "2022-01-28")]
        [InlineData("0395", "2022-02-22")]
        public void GetSickLeaveApprovedHoursTest(string employeeNumber, DateTime dt)
        {
            int actualHours = _c1CardBo.GetSickLeaveApprovedHours(employeeNumber, dt);

            // Assert
            Assert.True(actualHours == 0);
        }

        [Theory]
        [InlineData("0349", "2022-01-28")]
        [InlineData("0395", "2022-02-22")]
        public void GetSickLeaveAvailableHoursTest(string employeeNumber, DateTime dt)
        {
            int actualHours = _c1CardBo.GetSickLeaveAvailableHours(employeeNumber, dt);

            // Assert
            Assert.True(actualHours > 0);
        }

        [Theory]
        [InlineData("0349", "2022-01-28")]
        [InlineData("0395", "2022-02-22")]
        public void GetSickLeaveUsedHoursTest(string employeeNumber, DateTime dt)
        {
            int actualHours = _c1CardBo.GetSickLeaveUsedHours(employeeNumber, dt);

            // Assert
            Assert.True(actualHours == 0);
        }

        [Theory]
        [InlineData(2022, "0349", false)]
        [InlineData(2023, "0395", false)]
        [InlineData(2022, "0395", false)]
        [InlineData(2022, "2025", true)]
        public void HasChosenBirthdayLeaveTest(int year, string empNo, bool hasChosen)
        {
            bool result = _c1CardBo.HasChosenBirthdayLeave(year, empNo);
            Assert.Equal(hasChosen, result);
        }

        [Theory]
        [InlineData("2268", "2022-02-02", "2022-02-02", true)]
        [InlineData("2268", "2022-02-01", "2022-01-01", false)]
        [InlineData("2295", "2022-02-22", "2022-02-22", false)]
        [InlineData("2295", "2022-03-22", "2022-02-22", false)]
        public void IfLeaveRecordExistsTes(string empNumber, DateTime from, DateTime to, bool expected)
        {
            bool actual = _c1CardBo.IfLeaveRecordExists(empNumber, from, to);
            Assert.Equal(expected, actual);
        }

        [Theory]
        [InlineData("0349", 2022, false)]
        [InlineData("0395", 2022, false)]
        public void IsBirthdayLeaveTakenTest(string employeeNumber, int year, bool expected)
        {
            bool actual = _c1CardBo.IsBirthdayLeaveTaken(employeeNumber, year);

            // Assert
            Assert.Equal(expected, actual);
        }

        [Fact]
        public void WithdrawTest()
        {
            C1Card c1Card = new C1Card();
            Withdraw withdraw = new Withdraw();
            bool result = _c1CardBo.Withdraw(c1Card, withdraw);
            Assert.False(result);
        }


        [Fact]
        public void GetAppliedHoursBetween_ShouldReturnEmptyDictionary_WhenStartDateIsGreaterThanEndDate()
        {
            // Arrange
            var empNo = "E123";
            var startDate = new DateTime(2023, 10, 10);
            var endDate = new DateTime(2023, 10, 1);

            // Act
            var result = _c1CardBo.GetAppliedHoursBetween(empNo, startDate, endDate);

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetAppliedHoursBetween_ShouldReturnCorrectHours_WhenDatesAreValid()
        {
            //IWorkdayBo workdayBo;
            //IC1CardBo c1CardBo;
            //IAttendanceBo attendanceBo = A.Fake<IAttendanceBo>();
            //IEmployeeBo employeeBo = A.Fake<IEmployeeBo>();
            //IFormBo formBo = A.Fake<IFormBo>();
            //IFormFlowBo formFlowBo = A.Fake<IFormFlowBo>();
            ILogger<IC1CardBo> logger = A.Fake<ILogger<IC1CardBo>>();
            IWorkdayBo workdayBo = A.Fake<IWorkdayBo>();
            //c1CardBo = A.Fake<IC1CardBo>();
            IC1CardBo c1CardBo = new C1CardBo(
                _attendanceBo,
                _c1CardDao,
                _employeeBo,
                _formBo,
                _formFlowBo,
                logger,
                _workdayBo
            );

            // Arrange
            var empNo = "2268";
            var startDate = new DateTime(2024, 10, 1);
            var endDate = new DateTime(2024, 10, 10);
            var result = await AddC1Card(empNo, "0349", "", 1, 0, "2024-10-01 08:03:00", "2024-10-01 08:05:00", "");
            Assert.NotNull(result);
            Assert.Equal(CardStatusEnum.Success, result.Status);
            result = await AddC1Card(empNo, "0349", "", 1, 0, "2024-10-01 08:07:00", "2024-10-01 08:09:00", "");
            Assert.NotNull(result);
            Assert.Equal(CardStatusEnum.Success, result.Status);
            result = await AddC1Card(empNo, "0349", "", 1, 0, "2024-10-01 08:10:00", "2024-10-01 08:13:00", "");
            Assert.NotNull(result);
            Assert.Equal(CardStatusEnum.Success, result.Status);
            result = await AddC1Card(empNo, "0349", "", 1, 0, "2024-10-01 08:15:00", "2024-10-01 08:17:00", "");
            Assert.NotNull(result);
            Assert.Equal(CardStatusEnum.Success, result.Status);
            result = await AddC1Card(empNo, "0349", "", 1, 0, "2024-10-01 09:00:00", "2024-10-09 17:00:00", "");
            Assert.NotNull(result);
            Assert.Equal(CardStatusEnum.Error, result.Status);
            Assert.Equal(3000321, result.Code);
        }

        [Fact]
        public void CanClosedWithdrawTest()
        {
            // Arrange
            var card = new C1Card { Status = (int)FormStatus.Withdraw };

            // Act
            var result = _c1CardBo.CanClosedWithdraw(card);

            // Assert
            Assert.Equal("表單已抽單", result);
        }

        [Fact]
        public void CanClosedWithdrawTest_ReturnProcessing()
        {
            // Arrange
            var card = new C1Card { Status = (int)FormStatus.Processing };

            // Act
            var result = _c1CardBo.CanClosedWithdraw(card);

            // Assert
            Assert.Equal("表單進行中", result);
        }

        [Fact]
        public void CanClosedWithdraw_Deny_Test()
        {
            // Arrange
            var card = new C1Card { Status = (int)FormStatus.Deny };

            // Act
            var result = _c1CardBo.CanClosedWithdraw(card);

            // Assert
            Assert.Equal(string.Empty, result);
        }


        [Fact]
        public async Task CheckOverPermittedWorkingHour_ShouldReturnSuccess()
        {
            // Arrange
            IAttendanceBo attendanceBo;
            IC1CardDao c1CardDao;
            IEmployeeBo employeeBo;
            IFormBo formBo;
            IFormFlowBo formFlowBo;
            ILogger<IC1CardBo> logger;
            IWorkdayBo workdayBo;
            IC1CardBo c1CardBo;
            attendanceBo = A.Fake<IAttendanceBo>();
            c1CardDao = A.Fake<IC1CardDao>();
            employeeBo = A.Fake<IEmployeeBo>();
            formBo = A.Fake<IFormBo>();
            formFlowBo = A.Fake<IFormFlowBo>();
            logger = A.Fake<ILogger<IC1CardBo>>();
            workdayBo = A.Fake<IWorkdayBo>();
            //c1CardBo = A.Fake<IC1CardBo>();
            c1CardBo = new C1CardBo(
                _attendanceBo,
                _c1CardDao,
                _employeeBo,
                _formBo,
                _formFlowBo,
                logger,
                _workdayBo
            );

            // Arrange
            var empNo = "2268";
            var startDate = new DateTime(2024, 10, 1);
            var endDate = new DateTime(2024, 10, 10);
            var result = await AddC1Card(empNo, "0349", "", 1, 0, "2024-10-01 08:03:00", "2024-10-01 08:05:00", "");
            Assert.NotNull(result);
            Assert.Equal(CardStatusEnum.Success, result.Status);
            result = await AddC1Card(empNo, "0349", "", 1, 0, "2024-10-01 08:20:00", "2024-10-01 08:25:00", "");
            Assert.NotNull(result);
            Assert.Equal(CardStatusEnum.Success, result.Status);
            result = await AddC1Card(empNo, "0349", "", 1, 0, "2024-10-01 13:10:00", "2024-10-01 13:25:00", "");
            Assert.NotNull(result);
            Assert.Equal(CardStatusEnum.Success, result.Status);
            result = await AddC1Card(empNo, "0349", "", 1, 0, "2024-10-01 09:00:00", "2024-10-01 16:00:00", "");
            Assert.NotNull(result);
            Assert.Equal(CardStatusEnum.Error, result.Status);

            result = await AddC1Card(empNo, "0349", "", 1, 0, "2024-10-07 09:00:00", "2024-10-08 16:00:00", "");
            Assert.NotNull(result);
            Assert.Equal(CardStatusEnum.Success, result.Status);

            result = await AddC1Card(empNo, "0349", "", 1, 0, "2024-10-09 08:00:00", "2024-10-09 17:00:00", "");
            Assert.NotNull(result);
            Assert.Equal(CardStatusEnum.Success, result.Status);

            result = await AddC1Card(empNo, "0349", "", 1, 0, "2024-10-08 08:20:00", "2024-10-08 08:25:00", "");
            Assert.NotNull(result);
            Assert.Equal(CardStatusEnum.Error, result.Status);

            var expectedHours = new Dictionary<DateTime, int>
            {
                { new DateTime(2024, 10, 1), 3 },
                { new DateTime(2024, 10, 2), 0 },
                { new DateTime(2024, 10, 3), 0 },
                { new DateTime(2024, 10, 4), 0 },
                { new DateTime(2024, 10, 5), 0 },
                { new DateTime(2024, 10, 6), 0 },
                { new DateTime(2024, 10, 7), 7 },
                { new DateTime(2024, 10, 8), 7 },
                { new DateTime(2024, 10, 9), 8 },
                { new DateTime(2024, 10, 10), 0 }
            };

            var workdays = new List<Workday>
            {
                new Workday { WorkDate = new DateTime(2024, 10, 1), WorkHours = 8 },
                new Workday { WorkDate = new DateTime(2024, 10, 2), WorkHours = 0 },
                new Workday { WorkDate = new DateTime(2024, 10, 3), WorkHours = 0 },
                new Workday { WorkDate = new DateTime(2024, 10, 4), WorkHours = 8 },
                new Workday { WorkDate = new DateTime(2024, 10, 5), WorkHours = 0 },
                new Workday { WorkDate = new DateTime(2024, 10, 6), WorkHours = 0 },
                new Workday { WorkDate = new DateTime(2024, 10, 7), WorkHours = 8 },
                new Workday { WorkDate = new DateTime(2024, 10, 8), WorkHours = 8 },
                new Workday { WorkDate = new DateTime(2024, 10, 9), WorkHours = 8 },
                new Workday { WorkDate = new DateTime(2024, 10, 10), WorkHours = 0 },
                new Workday { WorkDate = new DateTime(2024, 10, 11), WorkHours = 8 }
            };

            A.CallTo(() => workdayBo.GetEmpWorkdaysDateRange(startDate, endDate, empNo)).Returns(workdays);

            // Act
            var hours = c1CardBo.GetAppliedHoursBetween(empNo, startDate, endDate);

            // Assert
            Assert.Equal(expectedHours, hours);



            // Arrange
            startDate = new DateTime(2024, 10, 8, 0, 0, 0, DateTimeKind.Local);
            endDate = new DateTime(2024, 10, 8, 0, 0, 0, DateTimeKind.Local);


            expectedHours = new Dictionary<DateTime, int>
            {
                { new DateTime(2024, 10, 8), 7 }
            };
            workdays = new List<Workday>
            {
                new Workday { WorkDate = new DateTime(2024, 10, 8), WorkHours = 8 }
            };
            A.CallTo(() => workdayBo.GetEmpWorkdaysDateRange(startDate, endDate, empNo)).Returns(workdays);

            // Act
            hours = c1CardBo.GetAppliedHoursBetween(empNo, startDate, endDate);

            // Assert
            Assert.Equal(expectedHours, hours);


            result = await AddC1Card(empNo, "0349", "", 1, 0, "2024-10-07 08:20:00", "2024-10-07 08:25:00", "");
            Assert.NotNull(result);
            Assert.Equal(CardStatusEnum.Success, result.Status);

            result = await AddC1Card(empNo, "0349", "", 1, 0, "2024-10-07 08:10:00", "2024-10-07 08:12:00", "");
            Assert.NotNull(result);
            Assert.Equal(CardStatusEnum.Error, result.Status);
        }


        [Fact]
        public async Task CheckOverPermittedWorkingHour_ShouldReturnError()
        {
            // Arrange
            IAttendanceBo attendanceBo;
            IC1CardDao c1CardDao;
            IEmployeeBo employeeBo;
            IFormBo formBo;
            IFormFlowBo formFlowBo;
            ILogger<IC1CardBo> logger;
            IWorkdayBo workdayBo;
            IC1CardBo c1CardBo;
            attendanceBo = A.Fake<IAttendanceBo>();
            c1CardDao = A.Fake<IC1CardDao>();
            employeeBo = A.Fake<IEmployeeBo>();
            formBo = A.Fake<IFormBo>();
            formFlowBo = A.Fake<IFormFlowBo>();
            logger = A.Fake<ILogger<IC1CardBo>>();
            workdayBo = A.Fake<IWorkdayBo>();
            //c1CardBo = A.Fake<IC1CardBo>();
            c1CardBo = new C1CardBo(
                _attendanceBo,
                _c1CardDao,
                _employeeBo,
                _formBo,
                _formFlowBo,
                logger,
                _workdayBo
            );

            // Arrange
            var empNo = "2268";
            var startDate = new DateTime(2024, 10, 1);
            var endDate = new DateTime(2024, 10, 10);
            var result = await AddC1Card(empNo, "0349", "", 1, 0, "2024-10-01 08:03:00", "2024-10-01 08:05:00", "");
            Assert.NotNull(result);
            Assert.Equal(CardStatusEnum.Success, result.Status);
            result = await AddC1Card(empNo, "0349", "", 1, 0, "2024-10-01 08:20:00", "2024-10-01 08:25:00", "");
            Assert.NotNull(result);
            Assert.Equal(CardStatusEnum.Success, result.Status);
            result = await AddC1Card(empNo, "0349", "", 1, 0, "2024-10-01 13:10:00", "2024-10-01 13:25:00", "");
            Assert.NotNull(result);
            Assert.Equal(CardStatusEnum.Success, result.Status);
            result = await AddC1Card(empNo, "0349", "", 1, 0, "2024-10-04 09:00:00", "2024-10-08 14:00:00", "");
            Assert.NotNull(result);
            Assert.Equal(CardStatusEnum.Success, result.Status);

            result = await AddC1Card(empNo, "0349", "", 1, 0, "2024-10-09 08:00:00", "2024-10-09 17:00:00", "");
            Assert.NotNull(result);
            Assert.Equal(CardStatusEnum.Success, result.Status);

            var expectedHours = new Dictionary<DateTime, int>
            {
                { new DateTime(2024, 10, 1), 3 },
                { new DateTime(2024, 10, 2), 0 },
                { new DateTime(2024, 10, 3), 0 },
                { new DateTime(2024, 10, 4), 7 },
                { new DateTime(2024, 10, 5), 0 },
                { new DateTime(2024, 10, 6), 0 },
                { new DateTime(2024, 10, 7), 8 },
                { new DateTime(2024, 10, 8), 5 },
                { new DateTime(2024, 10, 9), 8 },
                { new DateTime(2024, 10, 10), 0 }
            };

            var workdays = new List<Workday>
            {
                new Workday { WorkDate = new DateTime(2024, 10, 1), WorkHours = 8 },
                new Workday { WorkDate = new DateTime(2024, 10, 2), WorkHours = 0 },
                new Workday { WorkDate = new DateTime(2024, 10, 3), WorkHours = 0 },
                new Workday { WorkDate = new DateTime(2024, 10, 4), WorkHours = 8 },
                new Workday { WorkDate = new DateTime(2024, 10, 5), WorkHours = 0 },
                new Workday { WorkDate = new DateTime(2024, 10, 6), WorkHours = 0 },
                new Workday { WorkDate = new DateTime(2024, 10, 7), WorkHours = 8 },
                new Workday { WorkDate = new DateTime(2024, 10, 8), WorkHours = 8 },
                new Workday { WorkDate = new DateTime(2024, 10, 9), WorkHours = 8 },
                new Workday { WorkDate = new DateTime(2024, 10, 10), WorkHours = 0 },
                new Workday { WorkDate = new DateTime(2024, 10, 11), WorkHours = 8 }
            };

            A.CallTo(() => workdayBo.GetEmpWorkdaysDateRange(startDate, endDate, empNo)).Returns(workdays);


            // Act
            var hours = c1CardBo.GetAppliedHoursBetween(empNo, startDate, endDate);

            // Assert
            Assert.Equal(expectedHours, hours);
        }

        [Theory]
        [InlineData("2001", LeaveKindEnum.AnnualLeave, 99, "申請人非在職")]
        [InlineData("0395", LeaveKindEnum.AnnualLeave, 0, null)]
        [InlineData("0395", LeaveKindEnum.DisasterRecoveryLeave, 1, AttendanceParameters.DisasterRecoveryLeaveCertificateRequired)]
        public void GetListRemindMessageTest(string empNo, LeaveKindEnum leaveKind, int expactedType, string? expectedMessage)
        {
            C1Card c1Card = new ();
            c1Card.EmpNo = empNo;
            c1Card.LeaveNumber = leaveKind;
           (int messageType, string? message) = _c1CardBo.GetListRemindMessage(c1Card);
            Assert.Equal(expactedType, messageType);
            Assert.Equal(expectedMessage, message);
        }
    }
}
