<template>
  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span class="mx-1">卡</span>
          <span class="mx-1">號</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ modelValue?.formNo }}
    </div>
  </div>
  
  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>申</span>
          <span class="mx-1">請</span>
          <span>人</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ (modelValue?.empNo ?? '') + ((modelValue?.empNo && modelValue?.empName) ? ' ' : '') + (modelValue?.empName ?? '') }}
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>所屬部門</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ modelValue?.deptSName }}
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span class="mx-1">假</span>
          <span class="mx-1">別</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ leaveKind }}
    </div>
  </div>

  <div
    v-if="leaveSubKind"
    class="row mx-0"
  >
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>假別細項</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ leaveSubKind }}
    </div>
  </div>

  <div
    v-if="modelValue?.card.RelatedFormNumber"
    class="row mx-0"
  >
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>相關卡號</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ modelValue?.card.RelatedFormNumber }}
    </div>
  </div>

  <template v-if="modelValue?.card.EventDate">
    <div class="row mx-0">
      <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card text-center">
        <div class="d-table h-100 w-100">
          <div class="d-table-cell align-middle">
            <span>事件發生日</span>
          </div>
        </div>
      </div>
      <div class="col-9 col-md-10 py-2">
        {{ modelValue?.card.EventDate ? dateToRocString(new Date(modelValue?.card.EventDate), false): '' }}
      </div>
    </div>

    <template v-if="(props.modelValue?.card.LeaveNumber !== 7 && props.modelValue?.card.LeaveNumber !== 9)">
      <div class="row mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card text-center">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>請假期限</span>
            </div>
          </div>
        </div>
        <div class="col-9 col-md-10 py-2">
          {{ dateToRocString(new Date(modelValue?.card.ExpirationStartDate), false) + ' ~ ' + dateToRocString(new Date(modelValue?.card.ExpirationEndDate), false) }}
        </div>
      </div>
    </template>
  </template>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>請假時間</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ modelValue?.formInfo }}
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>本次請假合計</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ modelValue?.card.DayHours }}
    </div>
  </div>

  <div
    v-if="needProject"
    class="row mx-0"
  >
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>填報計畫</span>
        </div>
      </div>
    </div>
    <div class="col py-2">
      <a
        class="align-items-center text-primary text-decoration-underline"
        role="button"
        @click="onProjectLinkClick(modelValue?.card.ProjectNumber)"
      >
        {{ modelValue?.card.ProjectNumber }}
      </a>
    </div>
  </div>

  <div
    v-if="needLocation"
    class="row mx-0"
  >
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>出差地點</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ modelValue?.card.Location }}
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>請假事由</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      <template
        v-for="(rowReason, rowIndex) in reason"
        :key="rowIndex"
      >
        <span>{{ rowReason }}</span>
        <br v-if="rowIndex < (reason.length - 1)">
      </template>
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>代</span>
          <span class="mx-1">理</span>
          <span>人</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      <span class="me-1">{{ modelValue?.card.Deputy }}</span>
      <span>{{ modelValue?.card.DeputyName }}</span>
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>加會人員</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ modelValue?.addedSigner }}
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span class="mx-1">附</span>
          <span class="mx-1">件</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      <template
        v-for="(file, index) in modelValue?.attachments"
        :key="index"
      >
        <div class="mb-1">
          <button
            type="button"
            class="btn btn-link p-0"
            @click="onClickDownloadUrl(GET_DOWNLOADATTACHMENT_URL + '/' + file.formUID + '/' + file.id)"
          >
            {{ file.originalFileName }}
          </button>
        </div>
      </template>
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>填</span>
          <span class="mx-1">表</span>
          <span>人</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ (modelValue?.createdEmpNo ?? '') + ((modelValue?.createdEmpNo && modelValue?.createdName) ? ' ' : '') + (modelValue?.createdName ?? '') }}
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-c1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>填報時間</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ modelValue?.createdTime ? dateToRocString(new Date(modelValue?.createdTime)) : '' }}
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { dateToRocString, onClickDownloadUrl } from '../api/appFunction'
import { GET_LEAVEKINDS_URL, GET_DOWNLOADATTACHMENT_URL, GET_PROJECTNAME_URL } from '../api/appUrl'
import { INFO_DISPLAY_TIME, SYSTEM_ERROR_MESSAGE } from '../api/appConst'
import { useAbortController } from '../composable/abortController'
import { useToast } from 'primevue/usetoast'
import type { PropType } from 'vue'
import type { CardStoreType, LeaveKindApiType, LeaveKindDetailApiType } from '../api/appType'

const leaveKind = ref<string>()
const leaveSubKind = ref<string>()
const needLocation = ref<boolean>()
const needProject = ref<boolean>()

const toast = useToast()
const { abortController, fetchErrorHandler, abortListener } = useAbortController()

const props = defineProps({
  modelValue: {
    type: Object as PropType<CardStoreType | undefined>,
    default: () => {}
  }
})

const reason = computed<Array<string>>((): Array<string> => {
  let result: Array<string> = []
  if (props.modelValue?.card?.Reason) {
    result = props.modelValue?.card?.Reason.split('\n')
  }
  return result
})

const onProjectLinkClick = async (projNo: string): Promise<void> => {
  const params = new URLSearchParams({
    projNo: projNo
  })
  await fetch(GET_PROJECTNAME_URL + '?' + params, {
    method: 'GET',
    signal: abortController.signal
  }).then((res: Response) => {
    if (!res.ok) {
      throw new Error(res.status.toString())
    }
    return res.text()
  }).then(res => {
    toast.add({
      severity: 'info',
      summary: '計畫名稱： ' + res,
      life: INFO_DISPLAY_TIME,
      group: 'app'
    })
  }).catch((err: Error): void => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })
}

onMounted(async (): Promise<void> => {
  abortListener()
  if (props.modelValue?.empNo) {
    const params = new URLSearchParams({
      empNo: props.modelValue.empNo
    })
    await fetch(GET_LEAVEKINDS_URL + '?' + params, {
      method: 'GET',
      signal: abortController.signal
    }).then((res: Response) => {
      if (!res.ok) {
        throw new Error(res.status.toString())
      }
      return res.json()
    }).then(res => {
      const leaveKindFound = res.find((e: LeaveKindApiType) => e.Number === props.modelValue?.card.LeaveNumber)
      leaveKind.value = leaveKindFound?.Name
      leaveSubKind.value = leaveKindFound?.Detail.find((e: LeaveKindDetailApiType) => e.LeaveSubNumber === props.modelValue?.card.LeaveSubNumber)?.LeaveSubName
      needLocation.value = leaveKindFound?.NeedLocation
      needProject.value = leaveKindFound?.NeedProject
    }).catch((err: Error): void => {
      console.error(err)
      fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
    })
  }
})
</script>