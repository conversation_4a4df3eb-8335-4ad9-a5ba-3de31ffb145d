﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.Utilities;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers
{
    /// <summary>
    /// 請假卡 Web API
    /// </summary>
    [Authorize]
    [ApiController]
    [Route("/api/[controller]/[action]")]
    public class C1CardController : ControllerBase
    {
        // 避免重覆送出鎖定物件
        private static readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);
        private readonly IAttendanceBo _attendanceBo;
        private readonly IC1CardBo _c1CardBo;
        private readonly IConfiguration _Configuration;
        private readonly ILogger<C1CardController> _logger;
        private readonly bool _useNegotiate = true;

        /// <summary>
        /// 建構子
        /// </summary>
        /// <param name="attendanceBo">出勤業務物件</param>
        /// <param name="c1CardBo">請假卡業務物件</param>
        /// <param name="configuration">設定檔</param>
        /// <param name="logger">日誌記錄器</param>
        public C1CardController(IAttendanceBo attendanceBo, IC1CardBo c1CardBo, 
            IConfiguration configuration, ILogger<C1CardController> logger)
        {
            _logger = logger;
            _attendanceBo = attendanceBo;
            _c1CardBo = c1CardBo;
            _useNegotiate = configuration.GetValue<bool>("UseNegotiate");
            _Configuration = configuration;
        }

        private static LeaveView _LastCard { get; set; } = new LeaveView();

        /// <summary>
        /// 計算請假日數與時數
        /// </summary>
        /// <param name="leaveNumber">假別編號</param>
        /// <param name="startDate">請假起始日期</param>
        /// <param name="endDate">請假結束日期</param>
        /// <returns>不可使用假別時顯示訊息，否則傳回空字串</returns>
        [Authorize]
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/")]
        public string CalculateLeaveDayHours(int leaveNumber, DateTime startDate, DateTime endDate)
        {
            string ret = "-1";
            try
            {
                startDate = startDate.ToLocalTime();
                endDate = endDate.ToLocalTime();
                ret = _c1CardBo.GetLeaveDayHours(leaveNumber, startDate, endDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "/api/C1Card/CalculateLeaveDayHours({LeaveNumber},{StartDate},{EndDate}) 發生錯誤：{Message} {StackTrace}", leaveNumber, startDate, endDate, ex.Message, ex.StackTrace);
            }
            return ret;
        }

        /// <summary>
        /// 計算預設請假截止日
        /// </summary>
        /// <param name="startDate">請假開始日期</param>
        /// <param name="leaveNumber">假別代碼</param>
        /// <param name="subKindNumber">假別細項代碼</param>
        /// <param name="shiftId">班別</param>
        /// <returns>請假截止日</returns>
        [Authorize]
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public DateTime CalculateLeaveEndDate(DateTime startDate, int leaveNumber, int subKindNumber, int shiftId = 1)
        {
            DateTime date = DateTime.MaxValue;
            try
            {
                date = _c1CardBo.CalculateLeaveEndDate(startDate, leaveNumber, subKindNumber, shiftId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "/api/C1Card/CalculateLeaveEndDate({StartDate},{LeaveNumber},{SubKindNumber},{ShiftId}) 發生錯誤：{Message} {StackTrace}", startDate, leaveNumber, subKindNumber, shiftId, ex.Message, ex.StackTrace);
            }
            return date;
        }

        /// <summary>
        /// 檢查請假卡
        /// </summary>
        /// <param name="leave">請假物件</param>
        /// <returns>CardCheckResult JSON {"Code":訊息代碼, "Message":"錯誤訊息", "Status": CardStatusEnum}</returns>
        [Authorize]
        [HttpPost]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/")]
        public string CheckData(LeaveView leave)
        {
            CardCheckResult checkResult = AttendanceParameters.ResultGeneralError;
            string ret = JsonConvert.SerializeObject(checkResult);
            try
            {
                string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
                if (!string.IsNullOrWhiteSpace(userId))
                {
                    // DateTime 時間本地化
                    leave.StartTime = leave.StartTime.ToLocalTime();
                    leave.EndTime = leave.EndTime.ToLocalTime();
                    if (leave.EventDate != null)
                    {
                        leave.EventDate = ((DateTime)leave.EventDate).ToLocalTime();
                    }

                    leave.CreatedTime = leave.CreatedTime.ToLocalTime();
                    leave.FilledTime = leave.FilledTime.ToLocalTime();

                    if (leave.UpdatedTime == null)
                    {
                        leave.UpdatedTime = leave.CreatedTime;
                    }
                    else
                    {
                        leave.UpdatedTime = ((DateTime)leave.UpdatedTime).ToLocalTime();
                    }

                    leave.UpdatedEmpNo = userId;
                    var (ipAddress, hostname) = CardUtility.GetIP_Hostname(Request.HttpContext);
                    leave.UpdatedIP = ipAddress.ToString();
                    leave.UpdatedHost = hostname;

                    checkResult = _c1CardBo.CheckC1Card(userId, leave, ipAddress.ToString(), hostname);
                    ret = JsonConvert.SerializeObject(checkResult);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "/api/C1Card/Submit({Leave}) 發生錯誤：{Message} {StackTrace}", JsonConvert.SerializeObject(leave), ex.Message, ex.StackTrace);
            }
            return ret;
        }

        /// <summary>
        /// 讀取員工指定月份休假統計時數
        /// </summary>
        /// <param name="date">查詢日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>休假統計時數 JSON</returns>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public string GetEmpLeaveInfo(DateTime date, string empNo)
        {
            string ret = "[]";
            date = date.ToLocalTime();
            try
            {
                string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
                if (!string.IsNullOrWhiteSpace(userId)
                    && userId != string.Empty && empNo != null
                    && (userId == empNo || _attendanceBo.CanSeeAttendance(userId, empNo)))
                {
                    List<EmpLeaveInfo> leaveInfos = _c1CardBo.GetEmpLeaveInfo(date, empNo);
                    ret = JsonConvert.SerializeObject(leaveInfos, Formatting.None);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "/api/Attendance/GetMonthEmployeeLeaves({Date},{EmpNo}) 發生錯誤：{Message} {StackTrace}", date, empNo, ex.Message, ex.StackTrace);
            }

            return ret;
        }

        /// <summary>
        /// 取得指定員工事件假有效期限內的相關卡號資料
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="leaveNumber">假別編號</param>
        /// <param name="leaveSubNumber">假別細項編號</param>
        /// <returns>相關卡之 JSON</returns>
        [Authorize]
        [HttpPost]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/")]
        public string GetEventRelatedSheets(string empNo, int leaveNumber, int leaveSubNumber)
        {
            string json = "{}";
            try
            {
                json = _c1CardBo.GetEventRelatedSheetsJson(empNo, leaveNumber, leaveSubNumber);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "/api/C1Card/GetEventRelatedSheets({EmpNo},{LeaveNumber},{LeaveSubNumber}) 發生錯誤：{Message} {StackTrace}", empNo, leaveNumber, leaveSubNumber, ex.Message, ex.StackTrace);
            }
            return json;
        }

        /// <summary>
        /// 取得所有休假別 JSON
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>所有休假別 JSON</returns>
        [Authorize]
        [HttpGet]
        [Route("/api/[controller]/[action]")]
        public string GetLeaveKinds(string empNo = null)
        {
            string ret = "[]";
            try
            {
                if (string.IsNullOrWhiteSpace(empNo))
                {
                    string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
                    if (!string.IsNullOrWhiteSpace(userId))
                    {
                        empNo = userId;
                    }
                }
                ret = _c1CardBo.GetLeaveKindsString(empNo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "/api/C1Card/GetLeaveKinds({EmpNo}) 發生錯誤：{Message} {StackTrace}", empNo, ex.Message, ex.StackTrace);
            }
            return ret;
        }

        /// <summary>
        /// 找出可請假期限
        /// </summary>
        /// <param name="eventDate">事件發生日</param>
        /// <param name="leaveNumber">假別代碼</param>
        /// <param name="subKindNumber">假別細項代碼</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>最早可請假日期與最晚可請假日期</returns>
        [Authorize]
        [HttpPost]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/")]
        public string LeavePermittedPeriod(DateTime eventDate, int leaveNumber, int subKindNumber, string empNo)
        {
            DualDateTime a = new();
            eventDate = eventDate.ToLocalTime();
            (a.ExpirationStartDate, a.ExpirationEndDate) = _c1CardBo.CalculateLeavePermittedPeriod(eventDate, leaveNumber, subKindNumber, empNo);
            string ret = JsonConvert.SerializeObject(a);
            return ret;
        }

        /// <summary>
        /// 送出請假卡
        /// </summary>
        /// <param name="leave">請假物件</param>
        /// <returns>CardCheckResult JSON {"Code":訊息代碼, "Message":"錯誤訊息", "Status": CardStatusEnum}</returns>
        [Authorize]
        [HttpPost]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/")]
        public async Task<string> Submit(LeaveView leave)
        {
            CardCheckResult checkResult = AttendanceParameters.ResultGeneralError;
            await _semaphore.WaitAsync();
            try
            {
                string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
                if (string.IsNullOrWhiteSpace(userId))
                {
                    checkResult = AttendanceParameters.ResultBadEmployee;
                    return JsonConvert.SerializeObject(checkResult);
                }

                if (leave.EasyEquals(_LastCard))
                {
                    checkResult = new CardCheckResult(1000311, CardStatusEnum.Error, AttendanceParameters.RepeatSubmitForm);
                    return JsonConvert.SerializeObject(checkResult);
                }

                // 處理附件
                string uploadFolder = _Configuration.GetValue<string>("UploadDirectory");
                //若有 attachments
                if (leave.UploadedFiles != null && leave.UploadedFiles.Count > 0)
                {
                    CardUtility.PostProcessUploadedFiles(leave.UploadedFiles, uploadFolder);
                }

                // DateTime 時間本地化
                leave.StartTime = leave.StartTime.ToLocalTime();
                leave.EndTime = leave.EndTime.ToLocalTime();
                if (leave.EventDate != null)
                {
                    leave.EventDate = ((DateTime)leave.EventDate).ToLocalTime();
                }

                leave.CreatedTime = leave.CreatedTime.ToLocalTime();
                leave.FilledTime = leave.FilledTime.ToLocalTime();
                leave.UpdatedTime = leave.CreatedTime;

                leave.UpdatedEmpNo = userId;
                var (ipAddress, hostname) = CardUtility.GetIP_Hostname(Request.HttpContext);
                leave.UpdatedIP = ipAddress.ToString();
                leave.UpdatedHost = hostname;

                checkResult = await _c1CardBo.AddC1Card(userId, leave, ipAddress.ToString(), hostname);
                if (checkResult.Status == CardStatusEnum.Success)
                {
                    _LastCard = leave.EasyClone();
                }

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "/api/C1Card/Submit({Leave}) 發生錯誤：{Message} {StackTrace}", JsonConvert.SerializeObject(leave), ex.Message, ex.StackTrace);
            }
            finally
            {
                _semaphore.Release();
            }
            string ret = JsonConvert.SerializeObject(checkResult);
            return ret;
        }

        /// <summary>
        /// 假別有效期限
        /// </summary>
        private sealed class DualDateTime
        {
            public DateTime ExpirationEndDate;
            public DateTime ExpirationStartDate;
        }
    }
}
