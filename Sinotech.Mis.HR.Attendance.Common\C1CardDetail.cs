﻿using System;

namespace Sinotech.Mis.HR.Attendance.Common
{
    public class C1CardDetail
    {
        /// <summary>
        /// 流水號
        /// </summary>
        public int? ID { get; set; }

        /// <summary>
        /// 民國年月
        /// </summary>
        public string C1_YYMM { get; set; } = string.Empty;
        /// <summary>
        /// 起始日
        /// </summary>
        public string C1_SDD { get; set; } = string.Empty;
        /// <summary>
        /// 起始時
        /// </summary>
        public string C1_SHH { get; set; } = string.Empty;
        /// <summary>
        /// 起始分
        /// </summary>
        public string C1_SMM { get; set; } = string.Empty;

        /// <summary>
        /// 截止日
        /// </summary>
        public string C1_EDD { get; set; } = string.Empty;

        /// <summary>
        /// 截止時
        /// </summary>
        public string C1_EHH { get; set; } = string.Empty;

        /// <summary>
        /// 截止分
        /// </summary>
        public string C1_EMM { get; set; } = string.Empty;

        /// <summary>
        /// 本次請假總時數
        /// </summary>
        public int C1_HOUR { get; set; }

        /// <summary>
        /// 請假卡表單序號 （跨月請假時使用）
        /// </summary>
        public string C1_SERIALNO { get; set; } = "1";

        /// <summary>
        /// 請假起始日期時間
        /// </summary>
        public DateTime C1_StartDate { get; set; }

        /// <summary>
        /// 請假截止日期時間
        /// </summary>
        public DateTime C1_EndDate { get; set; }

    }
}
