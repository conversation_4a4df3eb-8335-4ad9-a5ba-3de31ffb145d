﻿namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 角色
    /// </summary>
    public class Role
    {
        /// <summary>
        /// 角色ID
        /// </summary>
        public string RoleId { get; set; }
        /// <summary>
        /// 角色名稱
        /// </summary>
        public string RoleName { get; set; }

        /// <summary>
        /// 角色類型代碼
        /// </summary>
        public RoleType RoleType { get; set; }

        /// <summary>
        /// 建構元
        /// </summary>
        /// <param name="roleId"></param>
        /// <param name="roleName"></param>
        /// <param name="roleType"></param>
        public Role(string roleId, string roleName, RoleType roleType)
        {
            RoleId = roleId;
            RoleName = roleName;
            RoleType = roleType;
        }
    }

    /// <summary>
    /// 角色類型
    /// </summary>
    public enum RoleType
    {
        /// <summary>
        ///  角色
        /// </summary>
        Role = 1,
        /// <summary>         
        /// 本人
        /// </summary>
        Self = 2,
        /// <summary>
        /// 代理
        /// </summary>
        Agent = 3
    }
}
