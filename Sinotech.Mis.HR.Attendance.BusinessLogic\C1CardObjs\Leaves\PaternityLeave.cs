﻿﻿using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Collections.Generic;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves
{
    /// <summary>
    /// 陪產檢及陪產假類別
    /// </summary>
    [LeaveKind(LeaveKindEnum.PaternityLeave)]
    public class PaternityLeave : LeaveWithEvent
    {

        /// <summary>
        /// 建構子
        /// </summary>
        /// <param name="c1Card"></param>
        /// <param name="c1CardBo"></param>
        public PaternityLeave(C1Card c1Card, IC1CardBo c1CardBo) : base(c1Card, c1CardBo)
        {
        }

        /// <summary>
        /// 檢查是否能請此假
        /// </summary>
        /// <returns></returns>
        internal override CardCheckResult CanTakeThisLeave()
        {
            return ResultOk;
        }

        /// <summary>
        /// 檢查剩餘可休  
        /// </summary>
        /// <returns></returns>
        internal override CardCheckResult CheckRemainHours()
        {
            CardCheckResult result = ResultOk;
            if (_c1Card.EventDate != null && _c1Card.EventDate != DateTime.MinValue && _c1Card.EventDate != DateTime.MaxValue)
            {
                //若本次請假超過可請
                if (TotalHours > PermittedHours)
                {
                    result = GenerateOverLeaveHoursError(PermittedHours,
                        3008301, AttendanceParameters.PaternityLeaveOneTimeOverAvailableHours);
                    return result;
                }

                // 讀取DB，找出同一事件日剩餘可休
                //List<C1Card> c1Cards = _c1CardBo.GetC1CardByEventDate(_c1Card.EmpNo, _c1Card.LeaveNumber, _c1Card.LeaveSubNumber, (DateTime)_c1Card.EventDate);
                //if (c1Cards.Count > 0)
                //{
                //    int usedHours = 0;
                //    foreach (var c1Card in c1Cards)
                //    {
                //        usedHours += c1Card.Hours;
                //    }
                //    int remainHours = PermittedHours - usedHours;
                //    //若本次請假超過剩餘可休
                //    if (TotalHours > remainHours)
                //    {
                //        result = GenerateOverLeaveHoursError(remainHours,
                //            3015303, AttendanceParameters.PaternityLeaveOverAvailableHours);
                //        return result;
                //    }
                //}

                // 若有相關單號
                if (!string.IsNullOrWhiteSpace(_c1Card.RelatedFormNumber))
                {
                    int usedHours = 0;
                    List<EventRelationRecord> records = _c1CardBo.GetEventRelatedRecord(_c1Card.RelatedFormNumber);
                    // 錯誤判斷
                    foreach (var record in records)
                    {
                        usedHours += record.LeaveHour;
                    }
                    int remainHours = PermittedHours - usedHours;
                    //若本次請假超過剩餘可休
                    if (TotalHours > remainHours)
                    {
                        result = GenerateOverLeaveHoursError(remainHours,
                            3015303, AttendanceParameters.PaternityLeaveOverAvailableHours);
                        return result;
                    }
                }
            }
            else
            {
                result = ResultEventDateFieldRequired;
            }
            return result;
        }

        /// <summary>
        /// 計算最早可請假日期與最晚可請假日期
        /// </summary>
        /// <param name="date">請假日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public override (DateTime, DateTime) CalculateLeavePermittedPeriod(DateTime date, string empNo)
        {
            if (_c1Card.EventDate != null && _c1Card.EventDate != DateTime.MinValue && _c1Card.EventDate != DateTime.MaxValue) // 事件發生日
            {
                date = (DateTime)_c1Card.EventDate;
            }
            DateTime startDate = date.AddMonths(-9); // 預產期9個月前
            DateTime endDate = date.AddMonths(1);    // 預產期1個月後
            return (startDate, endDate);
        }

        /// <summary>
        /// 此性別是否可請此假別
        /// </summary>
        /// <param name="gender"></param>
        /// <returns></returns>
        public static bool IsAllowForThisGender(Gender gender)
        {
            return true;
        }

    }
}
