﻿using Sinotech.Mis.Helpers;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.DataAccess.Interfaces;
using Sinotech.Mis.HR.Attendance.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;

namespace Sinotech.Mis.HR.Attendance.DataAccess.Ado
{
    /// <summary>
    /// 出勤資料存取元件
    /// </summary>
    /// <seealso cref="Sinotech.Mis.HR.Attendance.DataAccess.Interfaces.IAttendanceDao" />
    public class AttendanceDao : IAttendanceDao
    {
        /// <summary>
        /// The connection string
        /// </summary>
        private readonly string _connectionString;

        /// <summary>Initializes a new instance of the <see cref="AttendanceDao" /> class.</summary>
        /// <param name="connectionString">The connection string.</param>
        public AttendanceDao(string connectionString)
        {
            _connectionString = connectionString;
        }

        /// <summary>
        /// 從 DataRow 中讀取時數與倍率轉換為浮點數的 加班(費)倍率時數(aka 加權加班時數)
        /// </summary>
        /// <param name="hours"></param>
        /// <param name="dr"></param>
        /// <returns>加班(費)倍率時數</returns>
        private static double GetWeightedHoursFromDataRow(double hours, DataRow dr)
        {
            char charRate = Convert.ToChar((string)dr["B1_RATE"]);
            if (dr["B1_HOURS"] != null && dr["B1_HOURS"] != DBNull.Value)
            {
                byte strB1Hours = (byte)dr["B1_HOURS"];

                int hour = int.Parse(strB1Hours.ToString());
                double rate = GetRateFromB1Hours(charRate);
                hours += hour * rate;
            }
            return hours;
        }

        /// <summary>
        /// 新增表單
        /// </summary>
        /// <param name="formID">表單名稱</param>
        /// <param name="chineseYear">ROC年度</param>
        /// <param name="dataSet">包含所有待新增資料表的 Data Set</param>
        /// <returns>(單號，錯誤訊息)</returns>
        public (string?, string) AddForm(string formID, int chineseYear, DataSet dataSet)
        {
            string errorMessage = string.Empty;
            string storedProcedure = "sp_FormCreate";
            
            List<SqlParameter> parameters = new List<SqlParameter>();

            SqlParameter parameterFormID = new SqlParameter("@FormID", SqlDbType.VarChar, 10)
            {
                Value = formID
            };
            parameters.Add(parameterFormID);

            SqlParameter parameterChineseYear = new SqlParameter("@ChineseYear", SqlDbType.VarChar, 3);
            parameterChineseYear.Value = $"{chineseYear}";
            parameters.Add(parameterChineseYear);

            foreach (DataTable dataTable in dataSet.Tables)
            {
                string parameterName = $"@{dataTable.TableName}";
                SqlParameter parameter = new SqlParameter(parameterName, SqlDbType.Structured);
                parameter.Value = dataTable;
                parameters.Add(parameter);
            }

            SqlParameter parameterFormNo = new SqlParameter("@FormNo", SqlDbType.NVarChar, 10);
            parameterFormNo.Direction = ParameterDirection.Output;
            parameters.Add(parameterFormNo);

            SqlParameter parameterFormInsertedRowCount = new SqlParameter("@FormInsertedRowCount", SqlDbType.Int);
            parameterFormInsertedRowCount.Direction = ParameterDirection.Output;
            parameters.Add(parameterFormInsertedRowCount);

            SqlParameter parameterFormFlowInsertedRowCount = new SqlParameter("@FormFlowInsertedRowCount", SqlDbType.Int);
            parameterFormFlowInsertedRowCount.Direction = ParameterDirection.Output;
            parameters.Add(parameterFormFlowInsertedRowCount);

            SqlParameter parameterNotificationInsertedRowCount = new SqlParameter("@NotificationInsertedRowCount", SqlDbType.Int);
            parameterNotificationInsertedRowCount.Direction = ParameterDirection.Output;
            parameters.Add(parameterNotificationInsertedRowCount);

            SqlParameter parameterCardInsertedRowCount = new SqlParameter("@CardInsertedRowCount", SqlDbType.Int);
            parameterCardInsertedRowCount.Direction = ParameterDirection.Output;
            parameters.Add(parameterCardInsertedRowCount);

            SqlParameter parameterFormAttachmentInsertedRowCount = new SqlParameter("@FormAttachmentInsertedRowCount", SqlDbType.Int);
            parameterFormAttachmentInsertedRowCount.Direction = ParameterDirection.Output;
            parameters.Add(parameterFormAttachmentInsertedRowCount);

            SqlParameter parameterB1RateInsertedRowCount = new SqlParameter("@B1RateInsertedRowCount", SqlDbType.Int);
            parameterB1RateInsertedRowCount.Direction = ParameterDirection.Output;
            parameters.Add(parameterB1RateInsertedRowCount);

            SqlParameter parameterB1Rate_CompHolInsertedRowCount = new SqlParameter("@B1Rate_CompHolInsertedRowCount", SqlDbType.Int);
            parameterB1Rate_CompHolInsertedRowCount.Direction = ParameterDirection.Output;
            parameters.Add(parameterB1Rate_CompHolInsertedRowCount);

            try
            {
                _ = SqlHelper.ExecuteStoredProcedureNonQuery(_connectionString, storedProcedure, parameters);
            }
            catch (SqlException ex)
            {
                Console.WriteLine($"{ex.Message} {ex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"{ex.Message} {ex.StackTrace}");
                throw;
            }
            string? formNo = null;
            object o = parameterFormNo.Value;
            if (o != null)
            {                
                formNo = o.ToString();
            }
            return (formNo, errorMessage);
        }

        /// <summary>
        /// 結案抽單
        /// </summary>
        /// <param name="withdraw">抽單物件</param>
        /// <returns></returns>
        public string ClosedWithdraw(Withdraw withdraw)
        {
            string ret = string.Empty;
            string storedProcedure = "sp_FormWithdraw";

            List<SqlParameter> parameters = new List<SqlParameter>();

            SqlParameter parameterFormID = new SqlParameter("@FormID", SqlDbType.VarChar, 10)
            {
                Value = withdraw.FormID
            };
            parameters.Add(parameterFormID);

            SqlParameter parametersFormUID = new SqlParameter("@FormUID", SqlDbType.UniqueIdentifier);
            parametersFormUID.Value = withdraw.FormUID;
            parameters.Add(parametersFormUID);

            SqlParameter parameterUpdatedEmpNo = new SqlParameter("@UpdatedEmpNo", SqlDbType.VarChar, 4)
            {
                Value = withdraw.WithdrawEmpNo
            };
            parameters.Add(parameterUpdatedEmpNo);

            SqlParameter parameterUpdatedName = new SqlParameter("@UpdatedName", SqlDbType.NVarChar, 20)
            {
                Value = withdraw.WithdrawName
            };
            parameters.Add(parameterUpdatedName);

            SqlParameter parameterUpdatedTime = new SqlParameter("@UpdatedTime", SqlDbType.DateTime)
            {
                Value = withdraw.WithdrawTime
            };
            parameters.Add(parameterUpdatedTime);

            SqlParameter parameterUpdatedIP = new SqlParameter("@UpdatedIP", SqlDbType.VarChar, 50)
            {
                Value = withdraw.WithdrawIP
            };
            parameters.Add(parameterUpdatedIP);

            SqlParameter parameterUpdatedHost = new SqlParameter("@UpdatedHost", SqlDbType.NVarChar, 50);
            if (string.IsNullOrWhiteSpace(withdraw.WithdrawHost))
            {
                parameterUpdatedHost.Value = DBNull.Value;
            }
            else
            {
                parameterUpdatedHost.Value = withdraw.WithdrawHost;
            }
            parameters.Add(parameterUpdatedHost);

            SqlHelper.ExecuteStoredProcedure(_connectionString, storedProcedure, parameters);
            return ret;
        }

        /// <summary>
        /// 紀錄第一次讀取通知的時間。第二次後讀取通知不會更新時間，視為失敗
        /// </summary>
        /// <param name="id">通知編號</param>
        /// <returns>紀錄第一次讀取通知的結果是成功或失敗</returns>
        public bool DeliveredNotifications(int id)
        {
            bool ret = true;
            string strSql = @"UPDATE Notification SET ViewTime = @ViewTime WHERE (ID = @ID AND ViewTime IS NULL)";

            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterViewTime = new SqlParameter("@ViewTime", SqlDbType.DateTime);
            parameterViewTime.Value = DateTime.Now.ToLocalTime();
            parameters.Add(parameterViewTime);

            SqlParameter parameterId = new SqlParameter("@ID", SqlDbType.Int);
            parameterId.Value = id;
            parameters.Add(parameterId);

            try
            {
                int rows = SqlHelper.ExecuteSqlNonQuery(_connectionString, strSql, parameters);
                if (rows < 1)
                {
                    ret = false;
                }
            }
            catch (SqlException ex)
            {
                Console.WriteLine($"{ex.Message} {ex.StackTrace}");
                ret = false;
            }
            return ret;
        }

        /// <summary>
        /// 取得流程狀態名稱對應列表
        /// </summary>
        /// <returns></returns>
        public DataTable FormFlowStatusNames()
        {
            string sqlstr = @"SELECT ID,Name FROM FormFlowStatus ORDER BY ID;";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sqlstr);
            return dt;
        }

        /// <summary>
        /// 取得表單狀態名稱列表
        /// </summary>
        /// <returns></returns>
        public DataTable FormStatusNames()
        {
            string sqlstr = @"SELECT ID,Name FROM FormStatus ORDER BY ID;";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sqlstr);
            return dt;
        }

        /// <summary>
        /// 取得所有管理員名單
        /// </summary>
        /// <returns></returns>
        public DataTable GetAdministrators()
        {
            string sqlstr = "SELECT EmpNo FROM dbo.Manager;";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sqlstr);
            return dt;
        }

        /// <summary>
        /// 取得所有的通知
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>通知 DataTable</returns>
        public DataTable GetAllNotifications(string empNo)
        {
            SqlParameter parameterNotifyEmpNo = new SqlParameter("@NotifyEmpNo", SqlDbType.VarChar);
            parameterNotifyEmpNo.Value = empNo;

            string strSql = @"SELECT Notification.NotifyEmpNo, Notification.NotifyName, Notification.NotifyTime, Notification.ViewTime, Form.FormUID, 
                            Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, Form.EmpName, Form.DeptNo, 
                            Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, Form.CreatedName, Form.FilledTime, 
                            Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, Form.StartTime, Form.EndTime, 
                            Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, Form.UpdatedName, 
                            Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost
                            FROM Form INNER JOIN
                            Notification ON Form.FormUID = Notification.FormUID
                            WHERE (Notification.NotifyEmpNo = @NotifyEmpNo);";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql, parameterNotifyEmpNo);
            return dt;
        }

        /// <summary>
        /// 取得全部已填表單，以填表日期為準
        /// </summary>
        /// <param name="startDate">最早填卡日期</param>
        /// <param name="endDate">最晚填卡日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns>已填表單</returns>
        public DataTable GetAllSentBox(DateTime startDate, DateTime endDate, int? status = null)
        {
            List<SqlParameter> parameters = new List<SqlParameter>();

            string strSql = @"SELECT Form.ID, A1Card.ID AS CardID, Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 CASE A1_NN WHEN '1' THEN '上旬' WHEN '2' THEN '中旬' WHEN '3' THEN '下旬' END AS ApplicationType, 
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime, Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost
FROM Form INNER JOIN
 A1CARD ON A1CARD.FormUID = Form.FormUID
WHERE (CONVERT(DATE, Form.CreatedTime) >= CONVERT(DATE, @StartDate)) AND (CONVERT(DATE, Form.CreatedTime) 
 <= CONVERT(DATE, @EndDate))
UNION ALL
SELECT Form.ID, B1CARDAPP.ID AS CardID,
 Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 CASE B1_Code WHEN '1' THEN '加班' WHEN '2' THEN '社外加班' WHEN '3' THEN '補休假' END AS ApplicationType, 
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime, Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost
FROM Form INNER JOIN
 B1CARDAPP ON B1CARDAPP.FormUID = Form.FormUID
WHERE (CONVERT(DATE, Form.CreatedTime) >= CONVERT(DATE, @StartDate)) AND (CONVERT(DATE, Form.CreatedTime) 
 <= CONVERT(DATE, @EndDate))
UNION ALL
SELECT Form.ID, B1Card.ID AS CardID,
 Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 CASE B1_CODE WHEN '1' THEN '加班' WHEN '2' THEN '社外加班' WHEN '3' THEN '補休假' END AS ApplicationType,
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime, Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost
FROM Form INNER JOIN
 B1CARD ON B1CARD.FormUID = Form.FormUID
WHERE (CONVERT(DATE, Form.CreatedTime) >= CONVERT(DATE, @StartDate)) AND (CONVERT(DATE, Form.CreatedTime) 
 <= CONVERT(DATE, @EndDate))
UNION ALL
SELECT Form.ID, C1CARD.ID AS CardID,
 Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 RESTKIND.R1_NAME AS ApplicationType, Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, 
 Form.TeamCName, Form.CreatedEmpNo, Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, 
 Form.CreatedHost, Form.AddedSigner, Form.StartTime, Form.EndTime, Form.FormStatus, Form.TotalSteps, 
 Form.CurrentStep, Form.UpdatedEmpNo, Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, 
 Form.UpdatedHost
FROM Form INNER JOIN
 C1CARD ON C1CARD.FormUID = Form.FormUID INNER JOIN
 RESTKIND ON ( C1CARD.C1_CODE = RESTKIND.R1_NO AND C1CARD.C1_CODE2=RESTKIND.R2_NO )
WHERE (CONVERT(DATE, Form.CreatedTime) >= CONVERT(DATE, @StartDate)) AND (CONVERT(DATE, Form.CreatedTime) 
 <= CONVERT(DATE, @EndDate))
 ORDER BY Form.ID,CardID;";
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);
            if (status != null)
            {
                strSql += @"SELECT Form.ID, A1Card.ID AS CardID, Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 CASE A1_NN WHEN '1' THEN '上旬' WHEN '2' THEN '中旬' WHEN '3' THEN '下旬' END AS ApplicationType, 
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime, Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost
FROM Form INNER JOIN
 A1CARD ON A1CARD.FormUID = Form.FormUID
WHERE (CONVERT(DATE, Form.CreatedTime) >= CONVERT(DATE, @StartDate)) AND (CONVERT(DATE, Form.CreatedTime) 
 <= CONVERT(DATE, @EndDate)) AND FormStatus=@Status
UNION ALL
SELECT Form.ID, B1CARDAPP.ID AS CardID,
 Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 CASE B1_Code WHEN '1' THEN '加班' WHEN '2' THEN '社外加班' WHEN '3' THEN '補休假' END AS ApplicationType, 
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime, Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost
FROM Form INNER JOIN
 B1CARDAPP ON B1CARDAPP.FormUID = Form.FormUID
WHERE (CONVERT(DATE, Form.CreatedTime) >= CONVERT(DATE, @StartDate)) AND (CONVERT(DATE, Form.CreatedTime) 
 <= CONVERT(DATE, @EndDate)) AND FormStatus=@Status
UNION ALL
SELECT Form.ID, B1Card.ID AS CardID,
 Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 CASE B1_CODE WHEN '1' THEN '加班' WHEN '2' THEN '社外加班' WHEN '3' THEN '補休假' END AS ApplicationType,
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime, Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost
FROM Form INNER JOIN
 B1CARD ON B1CARD.FormUID = Form.FormUID
WHERE (CONVERT(DATE, Form.CreatedTime) >= CONVERT(DATE, @StartDate)) AND (CONVERT(DATE, Form.CreatedTime) 
 <= CONVERT(DATE, @EndDate)) AND FormStatus=@Status
UNION ALL
SELECT Form.ID, C1CARD.ID AS CardID,
 Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 RESTKIND.R1_NAME AS ApplicationType, Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, 
 Form.TeamCName, Form.CreatedEmpNo, Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, 
 Form.CreatedHost, Form.AddedSigner, Form.StartTime, Form.EndTime, Form.FormStatus, Form.TotalSteps, 
 Form.CurrentStep, Form.UpdatedEmpNo, Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, 
 Form.UpdatedHost
FROM Form INNER JOIN
 C1CARD ON C1CARD.FormUID = Form.FormUID INNER JOIN
 RESTKIND ON ( C1CARD.C1_CODE = RESTKIND.R1_NO AND C1CARD.C1_CODE2=RESTKIND.R2_NO )
WHERE (CONVERT(DATE, Form.CreatedTime) >= CONVERT(DATE, @StartDate)) AND (CONVERT(DATE, Form.CreatedTime) 
 <= CONVERT(DATE, @EndDate)) AND FormStatus=@Status
 ORDER BY Form.ID,CardID;";
                SqlParameter parameterStatus = new SqlParameter("@Status", SqlDbType.Int);
                parameterStatus.Value = status;
                parameters.Add(parameterStatus);
            }

            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql, parameters);
            return dt;
        }

        /// <summary>
        /// 取得表單附件
        /// </summary>
        /// <param name="formUID">表單Guid</param>
        /// <returns></returns>
        public DataTable GetAttachments(Guid formUID)
        {
            string sql = @"SELECT ID, FormUID, FileDirectory, OriginalFileName, EncodedFileName
                FROM FormAttachment WHERE FormUID=@FormUID;";
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parametersFormUID = new SqlParameter("@FormUID", SqlDbType.UniqueIdentifier);
            parametersFormUID.Value = formUID;
            parameters.Add(parametersFormUID);
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            return dt;
        }

        /// <summary>
        /// 讀取員工刷卡時間(依據查詢日期 )
        /// </summary>
        /// <param name="theDate">查詢日期.</param>
        /// <param name="empNo">員工編號.</param>
        public DataTable GetDayInTime(DateTime theDate, string empNo)
        {
            string sqlStr = "SELECT IN_EMPNO, IN_YYYYMMDD, IN_HHMM FROM INTIME_DAY WHERE IN_YYYYMMDD=@YYYYMMDD AND IN_EMPNO=@EmpNo;";
            SqlParameter[] paramArray = new SqlParameter[2];
            paramArray[0] = new SqlParameter("@YYYYMMDD", SqlDbType.Char, 10);
            paramArray[0].Value = theDate.ToString("yyyy-MM-dd");
            paramArray[1] = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            paramArray[1].Value = empNo;
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sqlStr, paramArray);
            return dt;
        }

        /// <summary>
        /// 取得部門已填表單，預設一個月內表單
        /// </summary>
        /// <param name="deptNo">部門員工編號</param>
        /// <returns>已填表單</returns>
        public DataTable GetDepartmentSentBox(int deptNo)
        {
            DateTime endDate = DateTime.Now;
            DateTime startDate = endDate.Date.AddDays(-31);
            return GetDepartmentSentBox(deptNo, startDate, endDate);
        }

        /// <summary>
        /// 取得部門已填表單，以填表日期為主
        /// </summary>
        /// <param name="deptNo">部門員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <returns>已填表單</returns>
        public DataTable GetDepartmentSentBox(int deptNo, DateTime startDate, DateTime endDate)
        {
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterDeptNo = new SqlParameter("@DeptNo", SqlDbType.Int);
            parameterDeptNo.Value = deptNo;
            parameters.Add(parameterDeptNo);
            string strSql = @"SELECT Form.FormUID, Form.ID,
 ApplicationType = CASE A1_NN WHEN '1' THEN '上旬' WHEN '2' THEN '中旬'  WHEN '3' THEN '下旬' END,
 Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, Form.EmpName, 
 Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, Form.CreatedName, 
 Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, Form.StartTime, 
 Form.EndTime, Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, Form.UpdatedIP, 
 Form.UpdatedHost, Form.UpdatedName, Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime
FROM Form INNER JOIN
 FormStatus ON Form.FormStatus = FormStatus.ID INNER JOIN
 A1CARD ON A1CARD.FormUID = Form.FormUID
WHERE (DeptNo = @DeptNo) AND (CONVERT(DATE,Form.CreatedTime) >= CONVERT(DATE,@StartDate)) 
AND (CONVERT(DATE,Form.CreatedTime) <= CONVERT(DATE,@EndDate)) 
UNION ALL 
SELECT Form.FormUID,Form.ID,
ApplicationType = CASE B1_Code WHEN '1' THEN '加班' WHEN '2' THEN '社外加班'  WHEN '3' THEN '補休假' END,
FormID,FormNo,FormSubject,FormInfo,EmpNo,EmpName,DeptNo,DeptSName,TeamID,TeamCName,
CreatedEmpNo,CreatedName,FilledTime,CreatedTime,CreatedIP,CreatedHost,AddedSigner,StartTime,EndTime,
FormStatus,TotalSteps,CurrentStep,Form.UpdatedEmpNo,Form.UpdatedIP,Form.UpdatedHost,Form.UpdatedName,
Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime 
FROM Form INNER JOIN  FormStatus ON Form.FormStatus = FormStatus.ID 
INNER JOIN B1CARDAPP ON B1CARDAPP.FormUID=Form.FormUID
WHERE (DeptNo = @DeptNo) AND (CONVERT(DATE,Form.CreatedTime) >= CONVERT(DATE,@StartDate)) 
AND (CONVERT(DATE,Form.CreatedTime) <= CONVERT(DATE,@EndDate)) 
UNION ALL 
SELECT Form.FormUID,Form.ID,
ApplicationType = CASE B1_CODE WHEN '1' THEN '加班' WHEN '2' THEN '社外加班'  WHEN '3' THEN '補休假' END,
FormID,FormNo,FormSubject,FormInfo,EmpNo,EmpName,DeptNo,DeptSName,TeamID,TeamCName,
CreatedEmpNo,CreatedName,FilledTime,CreatedTime,CreatedIP,CreatedHost,AddedSigner,StartTime,EndTime,
FormStatus,TotalSteps,CurrentStep,Form.UpdatedEmpNo,Form.UpdatedIP,Form.UpdatedHost,Form.UpdatedName,
Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime 
FROM Form INNER JOIN  FormStatus ON Form.FormStatus = FormStatus.ID 
INNER JOIN B1CARD ON B1CARD.FormUID=Form.FormUID
WHERE (DeptNo = @DeptNo) AND (CONVERT(DATE,Form.CreatedTime) >= CONVERT(DATE,@StartDate)) 
AND (CONVERT(DATE,Form.CreatedTime) <= CONVERT(DATE,@EndDate)) 
UNION ALL 
SELECT Form.FormUID, Form.ID, R1_NAME AS ApplicationType,
FormID,FormNo,FormSubject,FormInfo,EmpNo,EmpName,DeptNo,DeptSName,TeamID,TeamCName,
CreatedEmpNo,CreatedName,FilledTime,CreatedTime,CreatedIP,CreatedHost,AddedSigner,StartTime,EndTime,
FormStatus,TotalSteps,CurrentStep,Form.UpdatedEmpNo,Form.UpdatedIP,Form.UpdatedHost,Form.UpdatedName,
Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime 
FROM Form INNER JOIN  FormStatus ON Form.FormStatus = FormStatus.ID 
INNER JOIN C1CARD ON C1CARD.FormUID=Form.FormUID
INNER JOIN RESTKIND ON C1CARD.C1_CODE=RESTKIND.R1_NO AND C1CARD.C1_CODE2=RESTKIND.R2_NO
WHERE (DeptNo = @DeptNo) AND (CONVERT(DATE,Form.CreatedTime) >= CONVERT(DATE,@StartDate)) 
AND (CONVERT(DATE,Form.CreatedTime) <= CONVERT(DATE,@EndDate)) 
ORDER BY CreatedTime DESC;";
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);

            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql, parameters);
            return dt;
        }

        /// <summary>
        /// 取得部門已填表單，以填表日期為主
        /// </summary>
        /// <param name="deptNo">部門員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>已填表單</returns>
        public DataTable GetDepartmentSentBox(int deptNo, DateTime startDate, DateTime endDate, string projNo)
        {
            List<SqlParameter> parameters = new List<SqlParameter>();

            SqlParameter parameterDeptNo = new SqlParameter("@DeptNo", SqlDbType.Int);
            parameterDeptNo.Value = deptNo;
            parameters.Add(parameterDeptNo);

            SqlParameter parameterPrjNo = new SqlParameter("@PrjNo", SqlDbType.VarChar);
            parameterPrjNo.Value = $"%{projNo}%";
            parameters.Add(parameterPrjNo);

            string strSql = @"SELECT Form.FormUID, Form.ID,
 ApplicationType = CASE A1_NN WHEN '1' THEN '上旬' WHEN '2' THEN '中旬'  WHEN '3' THEN '下旬' END,
 Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, Form.EmpName, 
 Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, Form.CreatedName, 
 Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, Form.StartTime, 
 Form.EndTime, Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, Form.UpdatedIP, 
 Form.UpdatedHost, Form.UpdatedName, Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime
FROM Form INNER JOIN
 FormStatus ON Form.FormStatus = FormStatus.ID INNER JOIN
 A1CARD ON A1CARD.FormUID = Form.FormUID
WHERE (A1CARD.A1_PROJNO like @PrjNo) AND (DeptNo = @DeptNo) 
AND (CONVERT(DATE,Form.CreatedTime) >= CONVERT(DATE,@StartDate)) 
AND (CONVERT(DATE,Form.CreatedTime) <= CONVERT(DATE,@EndDate)) 
UNION ALL 
SELECT Form.FormUID,Form.ID,
ApplicationType = CASE B1_Code WHEN '1' THEN '加班' WHEN '2' THEN '社外加班'  WHEN '3' THEN '補休假' END,
FormID,FormNo,FormSubject,FormInfo,EmpNo,EmpName,DeptNo,DeptSName,TeamID,TeamCName,
CreatedEmpNo,CreatedName,FilledTime,CreatedTime,CreatedIP,CreatedHost,AddedSigner,StartTime,EndTime,
FormStatus,TotalSteps,CurrentStep,Form.UpdatedEmpNo,Form.UpdatedIP,Form.UpdatedHost,Form.UpdatedName,
Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime 
FROM Form INNER JOIN  FormStatus ON Form.FormStatus = FormStatus.ID 
INNER JOIN B1CARDAPP ON B1CARDAPP.FormUID=Form.FormUID
WHERE (B1CARDAPP.B1_PrjNo like @PrjNo) AND (DeptNo = @DeptNo) 
AND (CONVERT(DATE,Form.CreatedTime) >= CONVERT(DATE,@StartDate)) 
AND (CONVERT(DATE,Form.CreatedTime) <= CONVERT(DATE,@EndDate)) 
UNION ALL 
SELECT Form.FormUID,Form.ID,
ApplicationType = CASE B1_CODE WHEN '1' THEN '加班' WHEN '2' THEN '社外加班'  WHEN '3' THEN '補休假' END,
FormID,FormNo,FormSubject,FormInfo,EmpNo,EmpName,DeptNo,DeptSName,TeamID,TeamCName,
CreatedEmpNo,CreatedName,FilledTime,CreatedTime,CreatedIP,CreatedHost,AddedSigner,StartTime,EndTime,
FormStatus,TotalSteps,CurrentStep,Form.UpdatedEmpNo,Form.UpdatedIP,Form.UpdatedHost,Form.UpdatedName,
Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime 
FROM Form INNER JOIN  FormStatus ON Form.FormStatus = FormStatus.ID 
INNER JOIN B1CARD ON B1CARD.FormUID=Form.FormUID
WHERE (B1CARD.B1_PROJNO like @PrjNo) AND (DeptNo = @DeptNo) 
AND (CONVERT(DATE,Form.CreatedTime) >= CONVERT(DATE,@StartDate)) 
AND (CONVERT(DATE,Form.CreatedTime) <= CONVERT(DATE,@EndDate)) 
UNION ALL 
SELECT Form.FormUID, Form.ID, R1_NAME AS ApplicationType,
FormID,FormNo,FormSubject,FormInfo,EmpNo,EmpName,DeptNo,DeptSName,TeamID,TeamCName,
CreatedEmpNo,CreatedName,FilledTime,CreatedTime,CreatedIP,CreatedHost,AddedSigner,StartTime,EndTime,
FormStatus,TotalSteps,CurrentStep,Form.UpdatedEmpNo,Form.UpdatedIP,Form.UpdatedHost,Form.UpdatedName,
Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime 
FROM Form INNER JOIN  FormStatus ON Form.FormStatus = FormStatus.ID 
INNER JOIN C1CARD ON C1CARD.FormUID=Form.FormUID
INNER JOIN RESTKIND ON C1CARD.C1_CODE=RESTKIND.R1_NO AND C1CARD.C1_CODE2=RESTKIND.R2_NO
WHERE (C1CARD.C1_PrjNo like @PrjNo) AND (DeptNo = @DeptNo) 
AND (CONVERT(DATE,Form.CreatedTime) >= CONVERT(DATE,@StartDate)) 
AND (CONVERT(DATE,Form.CreatedTime) <= CONVERT(DATE,@EndDate)) 
ORDER BY CreatedTime DESC;";
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);

            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql, parameters);
            return dt;
        }

        /// <summary>
        /// 取得部門已填表單，以內容日期為準
        /// </summary>
        /// <param name="deptNo">部門編號</param>
        /// <param name="startDate">最早日期</param>
        /// <param name="endDate">最晚日期</param>
        /// <returns>已填表單</returns>
        public DataTable GetDepartmentSentBoxByContentDate(int deptNo, DateTime startDate, DateTime endDate)
        {
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterDeptNo = new SqlParameter("@DeptNo", SqlDbType.Int);
            parameterDeptNo.Value = deptNo;
            parameters.Add(parameterDeptNo);

            string strSql = @"SELECT Form.FormUID, Form.ID,
 ApplicationType = CASE A1_NN WHEN '1' THEN '上旬' WHEN '2' THEN '中旬'  WHEN '3' THEN '下旬' END,
 Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, Form.EmpName, 
 Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, Form.CreatedName, 
 Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, Form.StartTime, 
 Form.EndTime, Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, Form.UpdatedIP, 
 Form.UpdatedHost, Form.UpdatedName, Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime
FROM Form INNER JOIN
 FormStatus ON Form.FormStatus = FormStatus.ID INNER JOIN
 A1CARD ON A1CARD.FormUID = Form.FormUID
WHERE  (DeptNo = @DeptNo) 
AND (CONVERT(DATE,Form.ContentStartTime) <= CONVERT(DATE,@EndDate)) 
AND (CONVERT(DATE,Form.ContentEndTime) >= CONVERT(DATE,@StartDate)) 
UNION ALL 
SELECT Form.FormUID,Form.ID,
ApplicationType = CASE B1_Code WHEN '1' THEN '加班' WHEN '2' THEN '社外加班'  WHEN '3' THEN '補休假' END,
FormID,FormNo,FormSubject,FormInfo,EmpNo,EmpName,DeptNo,DeptSName,TeamID,TeamCName,
CreatedEmpNo,CreatedName,FilledTime,CreatedTime,CreatedIP,CreatedHost,AddedSigner,StartTime,EndTime,
FormStatus,TotalSteps,CurrentStep,Form.UpdatedEmpNo,Form.UpdatedIP,Form.UpdatedHost,Form.UpdatedName,
Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime 
FROM Form INNER JOIN  FormStatus ON Form.FormStatus = FormStatus.ID 
INNER JOIN B1CARDAPP ON B1CARDAPP.FormUID=Form.FormUID
WHERE  (DeptNo = @DeptNo) AND (CONVERT(DATE,B1_Date) >= CONVERT(DATE,@StartDate)) 
AND (CONVERT(DATE,B1_Date) <= CONVERT(DATE,@EndDate)) 
UNION ALL 
SELECT Form.FormUID,Form.ID,
ApplicationType = CASE B1_CODE WHEN '1' THEN '加班' WHEN '2' THEN '社外加班'  WHEN '3' THEN '補休假' END,
FormID,FormNo,FormSubject,FormInfo,EmpNo,EmpName,DeptNo,DeptSName,TeamID,TeamCName,
CreatedEmpNo,CreatedName,FilledTime,CreatedTime,CreatedIP,CreatedHost,AddedSigner,StartTime,EndTime,
FormStatus,TotalSteps,CurrentStep,Form.UpdatedEmpNo,Form.UpdatedIP,Form.UpdatedHost,Form.UpdatedName,
Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime 
FROM Form INNER JOIN  FormStatus ON Form.FormStatus = FormStatus.ID 
INNER JOIN B1CARD ON B1CARD.FormUID=Form.FormUID
WHERE  (DeptNo = @DeptNo) AND (CONVERT(DATE,B1_EndDate) >= CONVERT(DATE,@StartDate)) 
AND (CONVERT(DATE,B1_StartDate) <= CONVERT(DATE,@EndDate)) 
UNION ALL 
SELECT Form.FormUID, Form.ID, R1_NAME AS ApplicationType,
FormID,FormNo,FormSubject,FormInfo,EmpNo,EmpName,DeptNo,DeptSName,TeamID,TeamCName,
CreatedEmpNo,CreatedName,FilledTime,CreatedTime,CreatedIP,CreatedHost,AddedSigner,StartTime,EndTime,
FormStatus,TotalSteps,CurrentStep,Form.UpdatedEmpNo,Form.UpdatedIP,Form.UpdatedHost,Form.UpdatedName,
Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime 
FROM Form INNER JOIN  FormStatus ON Form.FormStatus = FormStatus.ID 
INNER JOIN C1CARD ON C1CARD.FormUID=Form.FormUID
INNER JOIN RESTKIND ON C1CARD.C1_CODE=RESTKIND.R1_NO AND C1CARD.C1_CODE2=RESTKIND.R2_NO
WHERE  (DeptNo = @DeptNo) AND (CONVERT(DATE,C1_StartDate) <= CONVERT(DATE,@EndDate)) 
AND (CONVERT(DATE,C1_EndDate) >= CONVERT(DATE,@StartDate)) 
ORDER BY CreatedTime DESC;";
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);
            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql, parameters);
            return dt;
        }

        /// <summary>
        /// 取得部門已填表單，以內容日期為準
        /// </summary>
        /// <param name="deptNo">部門編號</param>
        /// <param name="startDate">最早日期</param>
        /// <param name="endDate">最晚日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>已填表單</returns>
        public DataTable GetDepartmentSentBoxByContentDate(int deptNo, DateTime startDate, DateTime endDate, string projNo)
        {
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterDeptNo = new SqlParameter("@DeptNo", SqlDbType.Int);
            parameterDeptNo.Value = deptNo;
            parameters.Add(parameterDeptNo);

            SqlParameter parameterPrjNo = new SqlParameter("@PrjNo", SqlDbType.VarChar);
            parameterPrjNo.Value = $"%{projNo}%";
            parameters.Add(parameterPrjNo);

            string strSql = @"SELECT Form.FormUID, Form.ID,
 ApplicationType = CASE A1_NN WHEN '1' THEN '上旬' WHEN '2' THEN '中旬'  WHEN '3' THEN '下旬' END,
 Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, Form.EmpName, 
 Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, Form.CreatedName, 
 Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, Form.StartTime, 
 Form.EndTime, Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, Form.UpdatedIP, 
 Form.UpdatedHost, Form.UpdatedName, Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime
FROM Form INNER JOIN
 FormStatus ON Form.FormStatus = FormStatus.ID INNER JOIN
 A1CARD ON A1CARD.FormUID = Form.FormUID
WHERE (A1CARD.A1_PROJNO like @PrjNo) AND (DeptNo = @DeptNo) 
AND (CONVERT(DATE,Form.ContentStartTime) <= CONVERT(DATE,@EndDate)) 
AND (CONVERT(DATE,Form.ContentEndTime) >= CONVERT(DATE,@StartDate)) 
UNION ALL 
SELECT Form.FormUID,Form.ID,
ApplicationType = CASE B1_Code WHEN '1' THEN '加班' WHEN '2' THEN '社外加班'  WHEN '3' THEN '補休假' END,
FormID,FormNo,FormSubject,FormInfo,EmpNo,EmpName,DeptNo,DeptSName,TeamID,TeamCName,
CreatedEmpNo,CreatedName,FilledTime,CreatedTime,CreatedIP,CreatedHost,AddedSigner,StartTime,EndTime,
FormStatus,TotalSteps,CurrentStep,Form.UpdatedEmpNo,Form.UpdatedIP,Form.UpdatedHost,Form.UpdatedName,
Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime 
FROM Form INNER JOIN  FormStatus ON Form.FormStatus = FormStatus.ID 
INNER JOIN B1CARDAPP ON B1CARDAPP.FormUID=Form.FormUID
WHERE (B1CARDAPP.B1_PrjNo like @PrjNo) AND (DeptNo = @DeptNo) 
AND (CONVERT(DATE,B1_Date) >= CONVERT(DATE,@StartDate)) 
AND (CONVERT(DATE,B1_Date) <= CONVERT(DATE,@EndDate)) 
UNION ALL 
SELECT Form.FormUID,Form.ID,
ApplicationType = CASE B1_CODE WHEN '1' THEN '加班' WHEN '2' THEN '社外加班'  WHEN '3' THEN '補休假' END,
FormID,FormNo,FormSubject,FormInfo,EmpNo,EmpName,DeptNo,DeptSName,TeamID,TeamCName,
CreatedEmpNo,CreatedName,FilledTime,CreatedTime,CreatedIP,CreatedHost,AddedSigner,StartTime,EndTime,
FormStatus,TotalSteps,CurrentStep,Form.UpdatedEmpNo,Form.UpdatedIP,Form.UpdatedHost,Form.UpdatedName,
Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime 
FROM Form INNER JOIN  FormStatus ON Form.FormStatus = FormStatus.ID 
INNER JOIN B1CARD ON B1CARD.FormUID=Form.FormUID
WHERE (B1CARD.B1_PROJNO like @PrjNo) AND (DeptNo = @DeptNo) 
AND (CONVERT(DATE,B1_EndDate) >= CONVERT(DATE,@StartDate)) 
AND (CONVERT(DATE,B1_StartDate) <= CONVERT(DATE,@EndDate)) 
UNION ALL 
SELECT Form.FormUID, Form.ID, R1_NAME AS ApplicationType,
FormID,FormNo,FormSubject,FormInfo,EmpNo,EmpName,DeptNo,DeptSName,TeamID,TeamCName,
CreatedEmpNo,CreatedName,FilledTime,CreatedTime,CreatedIP,CreatedHost,AddedSigner,StartTime,EndTime,
FormStatus,TotalSteps,CurrentStep,Form.UpdatedEmpNo,Form.UpdatedIP,Form.UpdatedHost,Form.UpdatedName,
Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime 
FROM Form INNER JOIN  FormStatus ON Form.FormStatus = FormStatus.ID 
INNER JOIN C1CARD ON C1CARD.FormUID=Form.FormUID
INNER JOIN RESTKIND ON C1CARD.C1_CODE=RESTKIND.R1_NO AND C1CARD.C1_CODE2=RESTKIND.R2_NO
WHERE (C1CARD.C1_PrjNo like @PrjNo) AND (DeptNo = @DeptNo) 
AND (CONVERT(DATE,C1_StartDate) <= CONVERT(DATE,@EndDate)) 
AND (CONVERT(DATE,C1_EndDate) >= CONVERT(DATE,@StartDate)) 
ORDER BY CreatedTime DESC;";
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);
            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql, parameters);
            return dt;
        }

        /// <summary>
        /// 取得 部門  某月份已填表單 ，限部門登記桌或管理員
        /// </summary>
        /// <param name="deptNo">部門編號</param>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <returns>已填表單</returns>
        public DataTable GetDepartmentSentBoxYearMonth(int deptNo, int year, int month)
        {
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterDeptNo = new SqlParameter("@DeptNo", SqlDbType.Int);
            parameterDeptNo.Value = deptNo;
            parameters.Add(parameterDeptNo);
            string strSql = @" SELECT Form.ID, A1Card.ID AS CardID, Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 CASE A1_NN WHEN '1' THEN '上旬' WHEN '2' THEN '中旬' WHEN '3' THEN '下旬' END AS ApplicationType, 
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime, Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost
FROM Form INNER JOIN
 A1CARD ON A1CARD.FormUID = Form.FormUID
WHERE (FormInfo LIKE @FormInfo ) AND (DeptNo = @DeptNo) 
UNION ALL 
SELECT Form.ID, B1CARDAPP.ID AS CardID,
 Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 CASE B1_Code WHEN '1' THEN '加班' WHEN '2' THEN '社外加班' WHEN '3' THEN '補休假' END AS ApplicationType, 
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime, Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost
FROM Form INNER JOIN
 B1CARDAPP ON B1CARDAPP.FormUID = Form.FormUID
WHERE (FormInfo LIKE @FormInfo ) AND (DeptNo = @DeptNo) 
UNION ALL
SELECT Form.ID, B1Card.ID AS CardID,
 Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 CASE B1_CODE WHEN '1' THEN '加班' WHEN '2' THEN '社外加班' WHEN '3' THEN '補休假' END AS ApplicationType,
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime, Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost
FROM Form INNER JOIN
 B1CARD ON B1CARD.FormUID = Form.FormUID
WHERE (FormInfo LIKE @FormInfo ) AND (DeptNo = @DeptNo) 
UNION ALL
SELECT Form.ID, C1CARD.ID AS CardID,
 Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 RESTKIND.R1_NAME AS ApplicationType, Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, 
 Form.TeamCName, Form.CreatedEmpNo, Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, 
 Form.CreatedHost, Form.AddedSigner, Form.StartTime, Form.EndTime, Form.FormStatus, Form.TotalSteps, 
 Form.CurrentStep, Form.UpdatedEmpNo, Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, 
 Form.UpdatedHost
FROM Form INNER JOIN
 C1CARD ON C1CARD.FormUID = Form.FormUID INNER JOIN
 RESTKIND ON C1CARD.C1_CODE = RESTKIND.R1_NO AND C1CARD.C1_CODE2=RESTKIND.R2_NO
WHERE (FormInfo LIKE @FormInfo ) AND (DeptNo = @DeptNo) 
 ORDER BY Form.ID,CardID;";
            int rocYear = CardUtility.RocChineseYear(year);
            string formInfo = $"{rocYear}_{month.ToString("00")}%";
            SqlParameter parameterFormInfo = new SqlParameter("@FormInfo", SqlDbType.VarChar, 52);
            parameterFormInfo.Value = formInfo;
            parameters.Add(parameterFormInfo);

            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql, parameters);
            return dt;
        }

        /// <summary>
        /// 讀取員工指定月份(依據查詢日期 )休假統計時數，
        /// 包括年度總可用時數、年度已使用時數(簽核中＋已核可)、累至查詢月份已使用時數(簽核中＋已核可)、
        /// 查詢月份已使用時數(簽核中＋已核可)、年度已核可時數(已核可)、
        /// 累至查詢月份年度已核可時數(已核可)、查詢月份已核可時數(已核可)
        /// 提供
        /// 01：特別休息假
        /// 12：補休假
        /// 14：延休假
        /// </summary>
        /// <param name="date">查詢日期</param>
        /// <param name="employeeNumber">員工編號</param>
        public List<EmpLeaveInfo> GetEmpLeaveInfo(DateTime theDate, string employeeNumber)
        {
            List<EmpLeaveInfo> infos = new List<EmpLeaveInfo>();
            int[] leaveNumbers = { 1, 12, 14 };
            foreach (int leaveNumber in leaveNumbers)
            {
                EmpLeaveInfo empLeave = new EmpLeaveInfo();
                empLeave.LeaveNumber = leaveNumber;
                empLeave.LeaveSubNumber = 0;
                SqlParameter[] parameters = GetEmpLeaveInfo(theDate, employeeNumber, leaveNumber, empLeave.LeaveSubNumber);

                if (parameters[4].Value != DBNull.Value && parameters[4].Value != null)
                {
                    empLeave.YearAvailableHours = (int)parameters[4].Value;
                }
                if (parameters[5].Value != DBNull.Value && parameters[5].Value != null)
                {
                    empLeave.YearUsedHours = (int)parameters[5].Value;
                }
                if (parameters[6].Value != DBNull.Value && parameters[6].Value != null)
                {
                    empLeave.SumToMonthUsedHours = (int)parameters[6].Value;
                }
                if (parameters[7].Value != DBNull.Value && parameters[7].Value != null)
                {
                    empLeave.MonthUsedHours = (int)parameters[7].Value;
                }
                if (parameters[8].Value != DBNull.Value && parameters[8].Value != null)
                {
                    empLeave.YearApprovedHours = (int)parameters[8].Value;
                }
                if (parameters[9].Value != DBNull.Value && parameters[9].Value != null)
                {
                    empLeave.SumToMonthApprovedHours = (int)parameters[9].Value;
                }
                if (parameters[10].Value != DBNull.Value && parameters[10].Value != null)
                {
                    empLeave.MonthApprovedHours = (int)parameters[10].Value;
                }
                infos.Add(empLeave);
            }
            return infos;
        }


        /// <summary>
        /// 讀取員工指定月份(依據查詢日期 )休假統計時數，
        /// 包括年度總可用時數、年度已使用時數(簽核中＋已核可)、累至查詢月份已使用時數(簽核中＋已核可)、
        /// 查詢月份已使用時數(簽核中＋已核可)、年度已核可時數(已核可)、
        /// 累至查詢月份年度已核可時數(已核可)、查詢月份已核可時數(已核可)
        /// 提供 01：特別休息假
        /// 04：事假
        /// 10：病假
        /// 12：補休假
        /// 14：延休假
        /// 16：生理假
        /// 19：家庭照顧假
        /// </summary>
        /// <param name="date">查詢日期</param>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="leaveNumber">假別代號</param>
        /// <param name="leaveSubNumber">假別細項代號</param>
        /// <returns></returns>
        public SqlParameter[] GetEmpLeaveInfo(DateTime theDate, string employeeNumber, int leaveNumber, int leaveSubNumber)
        {
            SqlParameter[] paramArray = new SqlParameter[11];
            paramArray[0] = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            paramArray[0].Value = employeeNumber;
            paramArray[1] = new SqlParameter("@QDate", SqlDbType.DateTime);
            paramArray[1].Value = theDate;
            paramArray[2] = new SqlParameter("@R1_NO", SqlDbType.Char, 2);
            paramArray[2].Value = leaveNumber.ToString("00");
            paramArray[3] = new SqlParameter("@R2_NO", SqlDbType.Char, 2);
            if (leaveSubNumber == 0)
            {
                //paramArray[3].Value = DBNull.Value;
                paramArray[3].Value = string.Empty;
            }
            else
            {
                paramArray[3].Value = leaveSubNumber.ToString("00");
            }
            paramArray[4] = new SqlParameter("@YearAvailableHours", SqlDbType.Int);
            paramArray[4].Direction = ParameterDirection.Output;
            paramArray[5] = new SqlParameter("@YearUsedHours", SqlDbType.Int);
            paramArray[5].Direction = ParameterDirection.Output;
            paramArray[6] = new SqlParameter("@SumToMonthUsedHours", SqlDbType.Int);
            paramArray[6].Direction = ParameterDirection.Output;
            paramArray[7] = new SqlParameter("@MonthUsedHours", SqlDbType.Int);
            paramArray[7].Direction = ParameterDirection.Output;
            paramArray[8] = new SqlParameter("@YearApprovedHours", SqlDbType.Int);
            paramArray[8].Direction = ParameterDirection.Output;
            paramArray[9] = new SqlParameter("@SumToMonthApprovedHours", SqlDbType.Int);
            paramArray[9].Direction = ParameterDirection.Output;
            paramArray[10] = new SqlParameter("@MonthApprovedHours", SqlDbType.Int);
            paramArray[10].Direction = ParameterDirection.Output;
            SqlHelper.GetDataTableByStoredProcedure(_connectionString, storedProcedureName: @"[dbo].[sp_GetEmpLeaveInfo]", paramArray);
            return paramArray;
        }

        /// <summary>
        /// 取得當年度1月至該月分之剩餘補休假時數
        /// </summary>
        /// <param name="date"></param>
        /// <param name="empNo"></param>
        /// <returns>剩餘補休假時數</returns>
        public int GetEmployeeCompensatoryLeaveHours(DateTime date, string empNo)
        {
            int hours = 0;
            string firstMonth = (1911 - date.Year).ToString() + "01";  //民國年...
            string thisMonth = (1911 - date.Year).ToString() + date.ToString("MM");
            string sql = @"SELECT (SUM(RESTOK.[R_CompensatoryHolidays]) - SUM(REST.[R_USECompensatoryHolidays])) AS hours FROM REST
 INNER JOIN RESTOK ON REST.R_YYMM=RESTOK.R_YYMM and REST.R_EMPNO=RESTOK.R_EMPNO
 WHERE (REST.R_EMPNO = @EmpNo) AND REST.R_YYMM BETWEEN @FirstMonth AND @ThisMonth;";
            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            parameterEmpNo.Value = empNo;
            SqlParameter parameterFirstMonth = new SqlParameter("@FirstMonth", SqlDbType.VarChar, 5);
            parameterFirstMonth.Value = firstMonth;
            SqlParameter parameterThisMonth = new SqlParameter("@ThisMonth", SqlDbType.VarChar, 5);
            parameterThisMonth.Value = thisMonth;
            List<SqlParameter> parameters = new List<SqlParameter>
            {
                parameterEmpNo,
                parameterFirstMonth,
                parameterThisMonth
            };
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            if (dt != null && dt.Rows.Count > 0)
            {
                object obj = dt.Rows[0]["hours"];
                if (obj != null && obj != DBNull.Value)
                {
                    hours = (int)obj;
                }
            }
            return hours;
        }

        /// <summary>
        /// 取得員工指定假別休假資料，包括年度總可用時數、年度已使用時數(簽核中＋已核可)、
        /// 累至查詢月份已使用時數(簽核中＋已核可)、查詢月份已使用時數(簽核中＋已核可)、
        /// 年度已核可時數(已核可)、累至查詢月份年度已核可時數(已核可)、
        /// 查詢月份已核可時數(已核可)
        /// 提供以下假別代碼查詢：
        /// 01：特別休息假
        /// 04：事假
        /// 10：病假
        /// 12：補休假(未開發)
        /// 14：延休假
        /// 16：生理假
        /// 19：家庭照顧假
        /// </summary>
        /// <param name="date">查詢日期.</param>
        /// <param name="empNo">員工編號.</param>
        /// <param name="leaveNumber">假別</param>
        /// <param name="leaveSubNumber">假別細項</param>
        public List<SqlParameter> GetEmployeeLeaveInfo(DateTime date, string empNo, LeaveKindEnum leaveNumber, int leaveSubNumber)
        {

            List<SqlParameter> parameters = new List<SqlParameter>();

            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            parameterEmpNo.Value = empNo;
            parameters.Add(parameterEmpNo);

            SqlParameter parameterDate = new SqlParameter("@QDate", SqlDbType.DateTime);
            parameterDate.Value = date;
            parameters.Add(parameterDate);

            SqlParameter parameterLeaveNumber = new SqlParameter("@R1_NO", SqlDbType.Char, 2);
            string leaveNo = ((int)leaveNumber).ToString("00");
            parameterLeaveNumber.Value = leaveNo;
            parameters.Add(parameterLeaveNumber);

            SqlParameter parameterLeaveSubNumber = new SqlParameter("@R2_NO", SqlDbType.Char, 2);
            string leaveSubNo;
            if (leaveSubNumber == 0)
            {
                leaveSubNo = "  ";
            }
            else
            {
                leaveSubNo = leaveSubNumber.ToString("00");
            }
            parameterLeaveSubNumber.Value = leaveSubNo;
            parameters.Add(parameterLeaveSubNumber);

            SqlParameter parameterYearAvailableHours = new SqlParameter("@YearAvailableHours", SqlDbType.Int);
            parameterYearAvailableHours.Direction = ParameterDirection.Output;
            parameters.Add(parameterYearAvailableHours);

            SqlParameter parameterYearUsedHours = new SqlParameter("@YearUsedHours", SqlDbType.Int);
            parameterYearUsedHours.Direction = ParameterDirection.Output;
            parameters.Add(parameterYearUsedHours);

            SqlParameter parameterSumToMonthUsedHours = new SqlParameter("@SumToMonthUsedHours", SqlDbType.Int);
            parameterSumToMonthUsedHours.Direction = ParameterDirection.Output;
            parameters.Add(parameterSumToMonthUsedHours);

            SqlParameter parameterMonthUsedHours = new SqlParameter("@MonthUsedHours", SqlDbType.Int);
            parameterMonthUsedHours.Direction = ParameterDirection.Output;
            parameters.Add(parameterMonthUsedHours);

            SqlParameter parameterYearApprovedHours = new SqlParameter("@YearApprovedHours", SqlDbType.Int);
            parameterYearApprovedHours.Direction = ParameterDirection.Output;
            parameters.Add(parameterYearApprovedHours);

            SqlParameter parameterSumToMonthApprovedHours = new SqlParameter("@SumToMonthApprovedHours", SqlDbType.Int);
            parameterSumToMonthApprovedHours.Direction = ParameterDirection.Output;
            parameters.Add(parameterSumToMonthApprovedHours);

            SqlParameter parameterMonthApprovedHours = new SqlParameter("@MonthApprovedHours", SqlDbType.Int);
            parameterMonthApprovedHours.Direction = ParameterDirection.Output;
            parameters.Add(parameterMonthApprovedHours);

            SqlHelper.GetDataTableByStoredProcedure(_connectionString, storedProcedureName: @"[dbo].[sp_GetEmpLeaveInfo]", parameters);
            List<SqlParameter> ret = new List<SqlParameter>();
            ret.Add(parameterYearAvailableHours);
            ret.Add(parameterYearUsedHours);
            ret.Add(parameterSumToMonthUsedHours);
            ret.Add(parameterMonthUsedHours);
            ret.Add(parameterYearApprovedHours);
            ret.Add(parameterSumToMonthApprovedHours);
            ret.Add(parameterMonthApprovedHours);
            return ret;
        }

        /// <summary>
        /// 讀取員工指定日期已填報有效(簽核中與同意)且為加班上限範圍內之補休假換算加權後加班總時數
        /// </summary>
        /// <param name="theDate">查詢日期.</param>
        /// <param name="empNo">員工編號.</param>
        public double GetEmployeeWeightedCompensatoryOvertimeHours(DateTime theDate, string empNo)
        {
            double hours = 0.0;
            string sqlstr = "SELECT B1_HOURS,B1_RATE,B1_YYMMDD FROM B1RATE WHERE (IsOverTime = 1) AND (B1_YYMMDD = @YYYMMDD) AND (B1_EMPNO = @EmpNo);";
            string yyymmdd = $"{CardUtility.RocChineseYYYMMDD(theDate)}";
            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            parameterEmpNo.Value = empNo;
            SqlParameter parameterYYYMMDD = new SqlParameter("@YYYMMDD", SqlDbType.VarChar, 10);
            parameterYYYMMDD.Value = yyymmdd;
            List<SqlParameter> parameters = new List<SqlParameter>
            {
                parameterEmpNo,
                parameterYYYMMDD
            };
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sqlstr, parameters);
            if (dt != null && dt.Rows.Count > 0)
            {
                hours = GetWeightedHoursFromDataTable(hours, dt);
                //檢查加班時數中是否有保留代休
                sqlstr = "SELECT B1_HOURS,B1_RATE,B1_YYMMDD FROM B1RATE_CompHol WHERE (B1_YYMMDD = @YYYMMDD) AND (B1_EMPNO = @EmpNo);";
                parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
                parameterEmpNo.Value = empNo;
                parameterYYYMMDD = new SqlParameter("@YYYMMDD", SqlDbType.VarChar, 10);
                parameterYYYMMDD.Value = yyymmdd;
                parameters = new List<SqlParameter>
                {
                    parameterEmpNo,
                    parameterYYYMMDD
                };
                DataTable dtComp = SqlHelper.GetDataTable(_connectionString, sqlstr, parameters);
                if (dtComp != null && dtComp.Rows.Count > 0)
                {
                    hours = GetWeightedHoursFromDataTable(hours, dtComp);
                }
            }
            return hours;
        }

        /// <summary>取得 Form</summary>
        /// <param name="formUID">The form guid.</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public DataTable GetForm(Guid formUID)
        {
            string sql = @"SELECT Form.ID, Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, 
Form.FormInfo, Form.EmpNo, Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, 
Form.RankNo, Form.RankName,Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime,
Form.CreatedEmpNo, Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost,
Form.AddedSigner,Form.StartTime, Form.EndTime, Form.FormStatus, Form.TotalSteps, Form.CurrentStep, 
Form.UpdatedEmpNo, Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost,
FormStatus.Name AS FormStatusName FROM Form INNER JOIN
 FormStatus ON Form.FormStatus = FormStatus.ID WHERE FormUID=@FormUID";
            SqlParameter parameter = new SqlParameter("@FormUID", SqlDbType.UniqueIdentifier);
            parameter.Value = formUID;
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameter);
            return dt;
        }

        /// <summary>取得 FormFLow</summary>
        /// <param name="flowUID">The flow guid.</param>
        /// <returns>FormFlow DataTable</returns>
        public DataTable GetFormFlow(Guid flowUID)
        {
            string sql = @"SELECT FormFlow.ID, FormFlow.FlowUID, FormFlow.FormUID, FormFlow.RecipientEmpNo, 
FormFlow.RecipientName, FormFlow.RecipientDeptNo, FormFlow.RecipientDeptSName, FormFlow.RecipientTeamID, 
 FormFlow.RecipientTeamCName, FormFlow.FlowName, FormFlow.Step, FormFlow.ApproverEmpNo, FormFlow.ApproverName, 
FormFlow.ApproverDeptNo, FormFlow.ApproverDeptSName, FormFlow.ApproverTeamID, 
 FormFlow.ApproverTeamCName, FormFlow.ApproveTime, FormFlow.ApproveIP, FormFlow.ApproveComments,
FormFlow.ApproveHost, FormFlow.IsAgentApprove, FormFlow.FlowStatus, FormFlow.IsNotification,
FormFlowStatus.Name AS FlowStatusName 
FROM FormFlow INNER JOIN FormFlowStatus ON FormFlow.FlowStatus = FormFlowStatus.ID WHERE FlowUID=@FlowUID";
            SqlParameter parameter = new SqlParameter("@FlowUID", SqlDbType.UniqueIdentifier);
            parameter.Value = flowUID;
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameter);
            return dt;
        }

        /// <summary>取得 FormFLow</summary>
        /// <param name="formUID">The form guid.</param>
        /// <returns>DataTable FormFlow</returns>
        public DataTable GetFormFlows(Guid formUID)
        {
            string sql = @"SELECT FormFlow.ID, FormFlow.FlowUID, FormFlow.FormUID, FormFlow.RecipientEmpNo,
 FormFlow.RecipientName, FormFlow.RecipientDeptNo, FormFlow.RecipientDeptSName, FormFlow.RecipientTeamID, 
 FormFlow.RecipientTeamCName, FormFlow.FlowName, FormFlow.Step, FormFlow.ApproverEmpNo, FormFlow.ApproverName, 
 FormFlow.ApproverDeptNo, FormFlow.ApproverDeptSName, FormFlow.ApproverTeamID,FormFlow.ApproverTeamCName, 
 FormFlow.ApproveTime, FormFlow.ApproveIP, FormFlow.ApproveHost, FormFlow.IsAgentApprove, FormFlow.ApproveComments,
 FormFlow.FlowStatus, FormFlow.IsNotification, FormFlowStatus.Name AS FlowStatusName 
 FROM FormFlow INNER JOIN FormFlowStatus ON FormFlow.FlowStatus = FormFlowStatus.ID WHERE FormUID=@FormUID";
            SqlParameter parameter = new SqlParameter("@FormUID", SqlDbType.UniqueIdentifier);
            parameter.Value = formUID;
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameter);
            return dt;
        }

        /// <summary>
        /// 依日期區間取得所有表單 by 填報日期，限管理員使用
        /// </summary>
        /// <param name="startDate">開始日</param>
        /// <param name="endDate">結束日</param>
        /// <returns>表單資料表</returns>
        public DataTable GetForms(DateTime startDate, DateTime endDate)
        {
            List<SqlParameter> parameters = new List<SqlParameter>();
            string strSql = @"SELECT Form.FormUID, 
 ApplicationType = CASE A1_NN WHEN '1' THEN '上旬' WHEN '2' THEN '中旬'  WHEN '3' THEN '下旬' END,
 Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, Form.EmpName, 
 Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, Form.CreatedName, 
 Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, Form.StartTime, 
 Form.EndTime, A1CARD.A1_HOUR AS TotalHours, Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, Form.UpdatedIP, 
 Form.UpdatedHost, Form.UpdatedName, Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime
FROM Form INNER JOIN
 FormStatus ON Form.FormStatus = FormStatus.ID INNER JOIN
 A1CARD ON A1CARD.FormUID = Form.FormUID
WHERE (CONVERT(DATE, Form.CreatedTime) >= CONVERT(DATE, @StartDate)) AND 
 (CONVERT(DATE, Form.CreatedTime) <= CONVERT(DATE, @EndDate))
UNION ALL 
SELECT Form.FormUID,
ApplicationType = CASE B1_Code WHEN '1' THEN '加班' WHEN '2' THEN '社外加班'  WHEN '3' THEN '補休假' END,
FormID,FormNo,FormSubject,FormInfo,EmpNo,EmpName,DeptNo,DeptSName,TeamID,TeamCName,
CreatedEmpNo,CreatedName,FilledTime,CreatedTime,CreatedIP,CreatedHost,AddedSigner,StartTime,EndTime,
B1CARDAPP.B1_Hour AS TotalHours, 
FormStatus,TotalSteps,CurrentStep,Form.UpdatedEmpNo,Form.UpdatedIP,Form.UpdatedHost,Form.UpdatedName,
Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime 
FROM Form INNER JOIN  FormStatus ON Form.FormStatus = FormStatus.ID 
INNER JOIN B1CARDAPP ON B1CARDAPP.FormUID=Form.FormUID
WHERE (CONVERT(DATE,Form.CreatedTime) >= CONVERT(DATE,@StartDate)) 
AND (CONVERT(DATE,Form.CreatedTime) <= CONVERT(DATE,@EndDate)) 
UNION ALL 
SELECT Form.FormUID,
ApplicationType = CASE B1_CODE WHEN '1' THEN '加班' WHEN '2' THEN '社外加班'  WHEN '3' THEN '補休假' END,
FormID,FormNo,FormSubject,FormInfo,EmpNo,EmpName,DeptNo,DeptSName,TeamID,TeamCName,
CreatedEmpNo,CreatedName,FilledTime,CreatedTime,CreatedIP,CreatedHost,AddedSigner,StartTime,EndTime,
B1CARD.B1_Hour AS TotalHours, 
FormStatus,TotalSteps,CurrentStep,Form.UpdatedEmpNo,Form.UpdatedIP,Form.UpdatedHost,Form.UpdatedName,
Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime 
FROM Form INNER JOIN  FormStatus ON Form.FormStatus = FormStatus.ID 
INNER JOIN B1CARD ON B1CARD.FormUID=Form.FormUID
WHERE (CONVERT(DATE,Form.CreatedTime) >= CONVERT(DATE,@StartDate)) 
AND (CONVERT(DATE,Form.CreatedTime) <= CONVERT(DATE,@EndDate)) 
UNION ALL 
SELECT Form.FormUID, R1_NAME AS ApplicationType,
FormID,FormNo,FormSubject,FormInfo,EmpNo,EmpName,DeptNo,DeptSName,TeamID,TeamCName,
CreatedEmpNo,CreatedName,FilledTime,CreatedTime,CreatedIP,CreatedHost,AddedSigner,StartTime,EndTime,
C1CARD.C1_Hour AS TotalHours, 
FormStatus,TotalSteps,CurrentStep,Form.UpdatedEmpNo,Form.UpdatedIP,Form.UpdatedHost,Form.UpdatedName,
Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime 
FROM Form INNER JOIN  FormStatus ON Form.FormStatus = FormStatus.ID 
INNER JOIN C1CARD ON C1CARD.FormUID=Form.FormUID
INNER JOIN RESTKIND ON C1CARD.C1_CODE=RESTKIND.R1_NO AND C1CARD.C1_CODE2=RESTKIND.R2_NO
WHERE (CONVERT(DATE,Form.CreatedTime) >= CONVERT(DATE,@StartDate)) 
AND (CONVERT(DATE,Form.CreatedTime) <= CONVERT(DATE,@EndDate)) 
ORDER BY CreatedTime DESC;";
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);

            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql, parameters);
            return dt;
        }

        /// <summary>
        /// 依日期區間取得所有表單 by 填報日期，限管理員使用
        /// </summary>
        /// <param name="startDate">開始日</param>
        /// <param name="endDate">結束日</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單資料表</returns>
        public DataTable GetForms(DateTime startDate, DateTime endDate, string projNo)
        {
            List<SqlParameter> parameters = new List<SqlParameter>();
            string strSql = @"SELECT Form.FormUID, Form.ID,
 ApplicationType = CASE A1_NN WHEN '1' THEN '上旬' WHEN '2' THEN '中旬'  WHEN '3' THEN '下旬' END,
 Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, Form.EmpName, 
 Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, Form.CreatedName, 
 Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, Form.StartTime, 
 Form.EndTime,  A1CARD.A1_HOUR AS TotalHours,  Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, Form.UpdatedIP, 
 Form.UpdatedHost, Form.UpdatedName, Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime
FROM Form INNER JOIN
 FormStatus ON Form.FormStatus = FormStatus.ID INNER JOIN
 A1CARD ON A1CARD.FormUID = Form.FormUID
WHERE (A1CARD.A1_PROJNO like @PrjNo) AND (CONVERT(DATE, Form.CreatedTime) >= CONVERT(DATE, @StartDate)) AND 
 (CONVERT(DATE, Form.CreatedTime) <= CONVERT(DATE, @EndDate))
UNION ALL 
SELECT Form.FormUID,Form.ID,
ApplicationType = CASE B1_Code WHEN '1' THEN '加班' WHEN '2' THEN '社外加班'  WHEN '3' THEN '補休假' END,
FormID,FormNo,FormSubject,FormInfo,EmpNo,EmpName,DeptNo,DeptSName,TeamID,TeamCName,
CreatedEmpNo,CreatedName,FilledTime,CreatedTime,CreatedIP,CreatedHost,AddedSigner,StartTime,EndTime,
B1CARDAPP.B1_Hour AS TotalHours, 
FormStatus,TotalSteps,CurrentStep,Form.UpdatedEmpNo,Form.UpdatedIP,Form.UpdatedHost,Form.UpdatedName,
Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime 
FROM Form INNER JOIN  FormStatus ON Form.FormStatus = FormStatus.ID 
INNER JOIN B1CARDAPP ON B1CARDAPP.FormUID=Form.FormUID
WHERE (B1CARDAPP.B1_PrjNo like @PrjNo) AND
 (CONVERT(DATE,Form.CreatedTime) >= CONVERT(DATE,@StartDate)) 
AND (CONVERT(DATE,Form.CreatedTime) <= CONVERT(DATE,@EndDate)) 
UNION ALL 
SELECT Form.FormUID,Form.ID,
ApplicationType = CASE B1_CODE WHEN '1' THEN '加班' WHEN '2' THEN '社外加班'  WHEN '3' THEN '補休假' END,
FormID,FormNo,FormSubject,FormInfo,EmpNo,EmpName,DeptNo,DeptSName,TeamID,TeamCName,
CreatedEmpNo,CreatedName,FilledTime,CreatedTime,CreatedIP,CreatedHost,AddedSigner,StartTime,EndTime,
B1CARD.B1_Hour AS TotalHours, 
FormStatus,TotalSteps,CurrentStep,Form.UpdatedEmpNo,Form.UpdatedIP,Form.UpdatedHost,Form.UpdatedName,
Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime 
FROM Form INNER JOIN  FormStatus ON Form.FormStatus = FormStatus.ID 
INNER JOIN B1CARD ON B1CARD.FormUID=Form.FormUID
WHERE (B1CARD.B1_PROJNO like @PrjNo) AND
 (CONVERT(DATE,Form.CreatedTime) >= CONVERT(DATE,@StartDate)) 
AND (CONVERT(DATE,Form.CreatedTime) <= CONVERT(DATE,@EndDate)) 
UNION ALL 
SELECT Form.FormUID, Form.ID,R1_NAME AS ApplicationType,
FormID,FormNo,FormSubject,FormInfo,EmpNo,EmpName,DeptNo,DeptSName,TeamID,TeamCName,
CreatedEmpNo,CreatedName,FilledTime,CreatedTime,CreatedIP,CreatedHost,AddedSigner,StartTime,EndTime,
C1CARD.C1_Hour AS TotalHours, 
FormStatus,TotalSteps,CurrentStep,Form.UpdatedEmpNo,Form.UpdatedIP,Form.UpdatedHost,Form.UpdatedName,
Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime 
FROM Form INNER JOIN  FormStatus ON Form.FormStatus = FormStatus.ID 
INNER JOIN C1CARD ON C1CARD.FormUID=Form.FormUID
INNER JOIN RESTKIND ON C1CARD.C1_CODE=RESTKIND.R1_NO AND C1CARD.C1_CODE2=RESTKIND.R2_NO
WHERE (C1CARD.C1_PrjNo like @PrjNo) AND
(CONVERT(DATE,Form.CreatedTime) >= CONVERT(DATE,@StartDate)) 
AND (CONVERT(DATE,Form.CreatedTime) <= CONVERT(DATE,@EndDate)) 
ORDER BY CreatedTime DESC;";
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);
            SqlParameter parameterPrjNo = new SqlParameter("@PrjNo", SqlDbType.VarChar);
            parameterPrjNo.Value = $"%{projNo}%";
            parameters.Add(parameterPrjNo);
            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql, parameters);
            return dt;
        }

        /// <summary>
        /// 依日期區間取得所有表單 by 內容日期，限管理員使用
        /// </summary>
        /// <param name="startDate">最早填卡日期</param>
        /// <param name="endDate">最晚填卡日期</param>
        /// <returns>表單資料表</returns>
        public DataTable GetFormsByContentDate(DateTime startDate, DateTime endDate)
        {
            startDate = startDate.Date;
            endDate = endDate.Date;
            List<SqlParameter> parameters = new List<SqlParameter>();
            /* string strSql = @"SELECT Form.FormUID, 
 ApplicationType = CASE A1_NN WHEN '1' THEN '上旬' WHEN '2' THEN '中旬'  WHEN '3' THEN '下旬' END,
 Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, Form.EmpName, 
 Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, Form.CreatedName, 
 Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, Form.StartTime, 
 Form.EndTime, 
 A1CARD.A1_HOUR AS TotalHours, 
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, Form.UpdatedIP, 
 Form.UpdatedHost, Form.UpdatedName, Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime
FROM Form INNER JOIN
 FormStatus ON Form.FormStatus = FormStatus.ID INNER JOIN
 A1CARD ON A1CARD.FormUID = Form.FormUID
WHERE CreatedTime > DATEADD(day, -31, GETDATE())
UNION ALL 
SELECT Form.FormUID,
ApplicationType = CASE B1_Code WHEN '1' THEN '加班' WHEN '2' THEN '社外加班'  WHEN '3' THEN '補休假' END,
FormID,FormNo,FormSubject,FormInfo,EmpNo,EmpName,DeptNo,DeptSName,TeamID,TeamCName,
CreatedEmpNo,CreatedName,FilledTime,CreatedTime,CreatedIP,CreatedHost,AddedSigner,StartTime,EndTime,
B1CARDAPP.B1_Hour AS TotalHours, 
FormStatus,TotalSteps,CurrentStep,Form.UpdatedEmpNo,Form.UpdatedIP,Form.UpdatedHost,Form.UpdatedName,
Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime 
FROM Form INNER JOIN  FormStatus ON Form.FormStatus = FormStatus.ID 
INNER JOIN B1CARDAPP ON B1CARDAPP.FormUID=Form.FormUID
WHERE CreatedTime > DATEADD(day, -31, GETDATE())
UNION ALL 
SELECT Form.FormUID,
ApplicationType = CASE B1_CODE WHEN '1' THEN '加班' WHEN '2' THEN '社外加班'  WHEN '3' THEN '補休假' END,
FormID,FormNo,FormSubject,FormInfo,EmpNo,EmpName,DeptNo,DeptSName,TeamID,TeamCName,
CreatedEmpNo,CreatedName,FilledTime,CreatedTime,CreatedIP,CreatedHost,AddedSigner,StartTime,EndTime,
B1CARD.B1_Hour AS TotalHours, 
FormStatus,TotalSteps,CurrentStep,Form.UpdatedEmpNo,Form.UpdatedIP,Form.UpdatedHost,Form.UpdatedName,
Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime 
FROM Form INNER JOIN  FormStatus ON Form.FormStatus = FormStatus.ID 
INNER JOIN B1CARD ON B1CARD.FormUID=Form.FormUID
WHERE CreatedTime > DATEADD(day, -31, GETDATE())
UNION ALL 
SELECT Form.FormUID, R1_NAME AS ApplicationType,
FormID,FormNo,FormSubject,FormInfo,EmpNo,EmpName,DeptNo,DeptSName,TeamID,TeamCName,
CreatedEmpNo,CreatedName,FilledTime,CreatedTime,CreatedIP,CreatedHost,AddedSigner,StartTime,EndTime,
C1CARD.C1_Hour AS TotalHours, 
FormStatus,TotalSteps,CurrentStep,Form.UpdatedEmpNo,Form.UpdatedIP,Form.UpdatedHost,Form.UpdatedName,
Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime 
FROM Form INNER JOIN  FormStatus ON Form.FormStatus = FormStatus.ID 
INNER JOIN C1CARD ON C1CARD.FormUID=Form.FormUID
INNER JOIN RESTKIND ON C1CARD.C1_CODE=RESTKIND.R1_NO AND C1CARD.C1_CODE2=RESTKIND.R2_NO
WHERE CreatedTime > DATEADD(day, -31, GETDATE())
ORDER BY CreatedTime DESC;"; */
            // if (startDate <= endDate)
            // {
            // 使用Form.ContentStartTime 與 Form.ContentEndTime
            string strSql = @"SELECT Form.FormUID, Form.ID,
 ApplicationType = CASE A1_NN WHEN '1' THEN '上旬' WHEN '2' THEN '中旬'  WHEN '3' THEN '下旬' END,
 Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, Form.EmpName,Form.DeptNo, Form.DeptSName,
 Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP,
 Form.CreatedHost, Form.AddedSigner, Form.StartTime, Form.EndTime,
 A1CARD.A1_HOUR AS TotalHours, 
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, Form.UpdatedIP, 
 Form.UpdatedHost, Form.UpdatedName, Form.UpdatedTime, FormStatus.Name AS FormStatusName,
 Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime
 FROM Form INNER JOIN
 FormStatus ON Form.FormStatus = FormStatus.ID INNER JOIN
 A1CARD ON A1CARD.FormUID = Form.FormUID
 WHERE CAST(Form.ContentStartTime AS DATE) <=@EndDate AND CAST(Form.ContentEndTime AS DATE) >= @StartDate
UNION ALL 
SELECT Form.FormUID,Form.ID,
ApplicationType = CASE B1_Code WHEN '1' THEN '加班' WHEN '2' THEN '社外加班'  WHEN '3' THEN '補休假' END,
FormID,FormNo,FormSubject,FormInfo,EmpNo,EmpName,DeptNo,DeptSName,TeamID,TeamCName,
CreatedEmpNo,CreatedName,FilledTime,CreatedTime,CreatedIP,CreatedHost,AddedSigner,StartTime,EndTime,
B1CARDAPP.B1_Hour AS TotalHours, 
FormStatus,TotalSteps,CurrentStep,Form.UpdatedEmpNo,Form.UpdatedIP,Form.UpdatedHost,Form.UpdatedName,
Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime 
FROM Form INNER JOIN  FormStatus ON Form.FormStatus = FormStatus.ID 
INNER JOIN B1CARDAPP ON B1CARDAPP.FormUID=Form.FormUID
 WHERE CAST(Form.ContentStartTime AS DATE) <=@EndDate AND CAST(Form.ContentEndTime AS DATE) >= @StartDate
UNION ALL 
SELECT Form.FormUID,Form.ID,
ApplicationType = CASE B1_CODE WHEN '1' THEN '加班' WHEN '2' THEN '社外加班'  WHEN '3' THEN '補休假' END,
FormID,FormNo,FormSubject,FormInfo,EmpNo,EmpName,DeptNo,DeptSName,TeamID,TeamCName,
CreatedEmpNo,CreatedName,FilledTime,CreatedTime,CreatedIP,CreatedHost,AddedSigner,StartTime,EndTime,
B1CARD.B1_Hour AS TotalHours, 
FormStatus,TotalSteps,CurrentStep,Form.UpdatedEmpNo,Form.UpdatedIP,Form.UpdatedHost,Form.UpdatedName,
Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime 
FROM Form INNER JOIN  FormStatus ON Form.FormStatus = FormStatus.ID 
INNER JOIN B1CARD ON B1CARD.FormUID=Form.FormUID
 WHERE CAST(Form.ContentStartTime AS DATE) <=@EndDate AND CAST(Form.ContentEndTime AS DATE) >= @StartDate
UNION ALL 
SELECT Form.FormUID, Form.ID, R1_NAME AS ApplicationType,
FormID,FormNo,FormSubject,FormInfo,EmpNo,EmpName,DeptNo,DeptSName,TeamID,TeamCName,
CreatedEmpNo,CreatedName,FilledTime,CreatedTime,CreatedIP,CreatedHost,AddedSigner,StartTime,EndTime,
C1CARD.C1_Hour AS TotalHours, 
FormStatus,TotalSteps,CurrentStep,Form.UpdatedEmpNo,Form.UpdatedIP,Form.UpdatedHost,Form.UpdatedName,
Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime 
FROM Form INNER JOIN  FormStatus ON Form.FormStatus = FormStatus.ID 
INNER JOIN C1CARD ON C1CARD.FormUID=Form.FormUID
INNER JOIN RESTKIND ON C1CARD.C1_CODE=RESTKIND.R1_NO AND C1CARD.C1_CODE2=RESTKIND.R2_NO
 WHERE CAST(Form.ContentStartTime AS DATE) <=@EndDate AND CAST(Form.ContentEndTime AS DATE) >= @StartDate
ORDER BY CreatedTime DESC;";
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);
            // }
            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql, parameters);
            return dt;
        }

        /// <summary>
        /// 依日期區間取得所有表單 by 內容日期，限管理員使用
        /// </summary>
        /// <param name="startDate">開始日</param>
        /// <param name="endDate">結束日</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單資料表</returns>
        public DataTable GetFormsByContentDate(DateTime startDate, DateTime endDate, string projNo)
        {
            List<SqlParameter> parameters = new List<SqlParameter>();
            string strSql = @"SELECT Form.FormUID, Form.ID,
 ApplicationType = CASE A1_NN WHEN '1' THEN '上旬' WHEN '2' THEN '中旬' WHEN '3' THEN '下旬' END,
 Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, Form.EmpName, Form.DeptNo, Form.DeptSName, 
 Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, Form.CreatedName, Form.FilledTime, Form.CreatedTime,
 Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, Form.StartTime, Form.EndTime, 
 A1CARD.A1_HOUR AS TotalHours, 
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, Form.UpdatedIP, 
 Form.UpdatedHost, Form.UpdatedName, Form.UpdatedTime, FormStatus.Name AS FormStatusName,
 Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime
 FROM Form INNER JOIN
 FormStatus ON Form.FormStatus = FormStatus.ID INNER JOIN
 A1CARD ON A1CARD.FormUID = Form.FormUID
 WHERE  (A1_PROJNO like @PrjNo) AND CAST(Form.ContentStartTime AS DATE) <=@EndDate AND CAST(Form.ContentEndTime AS DATE) >= @StartDate
 UNION ALL 
SELECT Form.FormUID,Form.ID,
ApplicationType = CASE B1_Code WHEN '1' THEN '加班' WHEN '2' THEN '社外加班'  WHEN '3' THEN '補休假' END,
FormID,FormNo,FormSubject,FormInfo,EmpNo,EmpName,DeptNo,DeptSName,TeamID,TeamCName,
CreatedEmpNo,CreatedName,FilledTime,CreatedTime,CreatedIP,CreatedHost,AddedSigner,StartTime,EndTime,
B1CARDAPP.B1_Hour AS TotalHours, 
FormStatus,TotalSteps,CurrentStep,Form.UpdatedEmpNo,Form.UpdatedIP,Form.UpdatedHost,Form.UpdatedName,
Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime 
FROM Form INNER JOIN  FormStatus ON Form.FormStatus = FormStatus.ID 
INNER JOIN B1CARDAPP ON B1CARDAPP.FormUID=Form.FormUID
WHERE (B1_PrjNo like @PrjNo) AND CAST(Form.ContentStartTime AS DATE) <=@EndDate AND CAST(Form.ContentEndTime AS DATE) >= @StartDate
UNION ALL 
SELECT Form.FormUID,Form.ID,
ApplicationType = CASE B1_CODE WHEN '1' THEN '加班' WHEN '2' THEN '社外加班'  WHEN '3' THEN '補休假' END,
FormID,FormNo,FormSubject,FormInfo,EmpNo,EmpName,DeptNo,DeptSName,TeamID,TeamCName,
CreatedEmpNo,CreatedName,FilledTime,CreatedTime,CreatedIP,CreatedHost,AddedSigner,StartTime,EndTime,
B1CARD.B1_Hour AS TotalHours, 
FormStatus,TotalSteps,CurrentStep,Form.UpdatedEmpNo,Form.UpdatedIP,Form.UpdatedHost,Form.UpdatedName,
Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime 
FROM Form INNER JOIN  FormStatus ON Form.FormStatus = FormStatus.ID 
INNER JOIN B1CARD ON B1CARD.FormUID=Form.FormUID
WHERE (B1_PROJNO like @PrjNo) AND CAST(Form.ContentStartTime AS DATE) <=@EndDate AND CAST(Form.ContentEndTime AS DATE) >= @StartDate
UNION ALL 
SELECT Form.FormUID, Form.ID, R1_NAME AS ApplicationType,
FormID,FormNo,FormSubject,FormInfo,EmpNo,EmpName,DeptNo,DeptSName,TeamID,TeamCName,
CreatedEmpNo,CreatedName,FilledTime,CreatedTime,CreatedIP,CreatedHost,AddedSigner,StartTime,EndTime,
C1CARD.C1_Hour AS TotalHours, 
FormStatus,TotalSteps,CurrentStep,Form.UpdatedEmpNo,Form.UpdatedIP,Form.UpdatedHost,Form.UpdatedName,
Form.UpdatedTime, FormStatus.Name AS FormStatusName,
Form.RankName, Form.RankNo, Form.JobNo, Form.JobName, Form.ContentStartTime, Form.ContentEndTime 
FROM Form INNER JOIN  FormStatus ON Form.FormStatus = FormStatus.ID 
INNER JOIN C1CARD ON C1CARD.FormUID=Form.FormUID
INNER JOIN RESTKIND ON C1CARD.C1_CODE=RESTKIND.R1_NO AND C1CARD.C1_CODE2=RESTKIND.R2_NO
WHERE (C1_PrjNo like @PrjNo) AND CAST(Form.ContentStartTime AS DATE) <=@EndDate AND CAST(Form.ContentEndTime AS DATE) >= @StartDate
ORDER BY CreatedTime DESC;";
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);
            SqlParameter parameterPrjNo = new SqlParameter("@PrjNo", SqlDbType.VarChar);
            parameterPrjNo.Value = $"%{projNo}%";
            parameters.Add(parameterPrjNo);
            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql, parameters);
            return dt;
        }

        /// <summary>
        /// Get Forms by FormStatus
        /// </summary>
        /// <param name="status">FormStatus</param>
        /// <returns>Forms</returns>
        public DataTable GetFormsByStatus(FormStatus status)
        {
            int iStatus = (int)status;
            string sql = $"SELECT * FROM Form WHERE FormStatus=@FormStatus";
            SqlParameter parameter = new SqlParameter("@FormStatus", SqlDbType.TinyInt);
            parameter.Value = iStatus;
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameter);
            return dt;
        }

        /// <summary>
        /// Get Forms by FormStatus
        /// </summary>
        /// <param name="status">FormStatus</param>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <returns>Forms</returns>
        public DataTable GetFormsByStatus(FormStatus status, DateTime startDate, DateTime endDate)
        {
            int iStatus = (int)status;
            string sql = @"SELECT * FROM Form WHERE FormStatus=@FormStatus
            AND (CONVERT(DATE,CreatedTime) >= CONVERT(DATE,@StartDate)) 
            AND (CONVERT(DATE,CreatedTime) <= CONVERT(DATE,@EndDate));";
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);
            SqlParameter parameterFormStatus = new SqlParameter("@FormStatus", SqlDbType.TinyInt);
            parameterFormStatus.Value = iStatus;
            parameters.Add(parameterFormStatus);
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            return dt;
        }

        /// <summary>
        /// 取得FormType
        /// </summary>
        /// <returns></returns>
        public DataTable GetFormTypes()
        {
            string sqlstr = "SELECT * FROM dbo.FormType;";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sqlstr);
            return dt;
        }

        /// <summary>取得收件匣 DataTable</summary>
        /// <param name="roles">角色的List</param>
        /// <returns>收件匣 DataTable</returns>
        public DataTable GetInboxes(List<Role> roles)
        {
            string strRoles = CardUtility.RolesToQuotedString(roles);

            string strSql = @$"SELECT Form.FormUID,Form.FormID,Form.FormNo,Form.FormSubject,Form.FormInfo, 
            Form.EmpNo,Form.EmpName,Form.DeptNo,Form.DeptSName,Form.TeamID,Form.TeamCName,Form.CreatedEmpNo, 
            Form.CreatedName,Form.FilledTime,Form.CreatedTime,Form.CreatedIP,Form.CreatedHost,Form.AddedSigner, 
            Form.StartTime,Form.EndTime,Form.FormStatus,Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo,
            Form.UpdatedIP,Form.UpdatedHost,FormFlow.FlowUID,FormFlow.RecipientEmpNo, FormFlow.RecipientName,
            FormFlow.ApproveComments,
            FormFlow.FlowName,FormFlow.Step, FormFlow.ID,FormFlow.RecipientDeptNo, FormFlow.RecipientDeptSName 
            FROM Form INNER JOIN FormFlow ON Form.FormUID = FormFlow.FormUID AND Form.CurrentStep = FormFlow.Step 
            WHERE FormFlow.RecipientEmpNo in ({strRoles}) AND (Form.FormStatus = 1) AND(FormFlow.IsNotification=0) 
            ORDER BY Form.ID, FormFlow.ID;";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql);
            return dt;
        }

        /// <summary>取得收件匣 DataTable</summary>
        /// <param name="role">角色</param>
        /// <returns>收件匣 DataTable</returns>
        public DataTable GetInboxes(string role)
        {
            string strSql = @"SELECT Form.FormUID,Form.FormID,Form.FormNo,Form.FormSubject,Form.FormInfo, 
            Form.EmpNo,Form.EmpName,Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName,Form.CreatedEmpNo, 
            Form.CreatedName,Form.FilledTime,Form.CreatedTime,Form.CreatedIP,Form.CreatedHost,Form.AddedSigner, 
            Form.StartTime,Form.EndTime,Form.FormStatus,Form.TotalSteps,Form.CurrentStep,Form.UpdatedEmpNo, 
            Form.UpdatedIP,Form.UpdatedHost,FormFlow.FlowUID,FormFlow.RecipientEmpNo,FormFlow.RecipientName,
            FormFlow.ApproveComments,
            FormFlow.FlowName,FormFlow.Step,FormFlow.ID,FormFlow.RecipientDeptNo,FormFlow.RecipientDeptSName 
            FROM Form INNER JOIN FormFlow ON Form.FormUID=FormFlow.FormUID AND Form.CurrentStep=FormFlow.Step
            WHERE FormFlow.RecipientEmpNo=@Role AND (Form.FormStatus = 1) AND(FormFlow.IsNotification = 0)
            ORDER BY Form.ID, FormFlow.ID";
            SqlParameter parameterRole = new SqlParameter("@Role", SqlDbType.VarChar, 5);
            parameterRole.Value = role;
            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql, parameterRole);
            return dt;
        }

        /// <summary>
        /// 讀取員工指定月份出勤時間
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public DataTable GetMonthAttendance(DateTime date, string empNo)
        {
            DataTable dt = new DataTable();
            if (empNo != null)
            {
                string temp = string.Format("{0, 3:G}{1:D2}%", CardUtility.RocChineseYear(date), date.Month);
                string strsql = "SELECT IN_YYMMDD, IN_HHMM, IN_TYPE FROM dbo.INTIME WHERE IN_YYMMDD like @YYYMM AND IN_EMPNO=@EmpNo ORDER BY IN_YYMMDD, IN_HHMM, IN_TYPE";

                List<SqlParameter> parameters = new List<SqlParameter>();
                SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
                parameterEmpNo.Value = empNo;
                parameters.Add(parameterEmpNo);

                SqlParameter parameterYYYMM = new SqlParameter("@YYYMM", SqlDbType.VarChar);
                parameterYYYMM.Value = temp;
                parameters.Add(parameterYYYMM);

                dt = SqlHelper.GetDataTable(_connectionString, strsql, parameters);
            }
            return dt;
        }

        /// <summary>
        /// 讀取員工指定月份(依據查詢日期 )休假統計時數
        /// </summary>
        /// <param name="theDate">查詢日期.</param>
        /// <param name="empno">員工編號.</param>
        public SqlParameter[] GetMonthEmployeeLeaves(DateTime theDate, string empno)
        {
            SqlParameter[] paramArray = new SqlParameter[28];
            paramArray[0] = new SqlParameter("@qdate", SqlDbType.DateTime);
            paramArray[0].Value = theDate;
            paramArray[1] = new SqlParameter("@empno", SqlDbType.VarChar);
            paramArray[1].Value = empno;
            paramArray[2] = new SqlParameter("@vac", SqlDbType.Int);
            paramArray[3] = new SqlParameter("@usevac", SqlDbType.Int);
            paramArray[4] = new SqlParameter("@usething", SqlDbType.Int);
            paramArray[5] = new SqlParameter("@usesick", SqlDbType.Int);
            paramArray[6] = new SqlParameter("@usevac_month", SqlDbType.Int);
            paramArray[7] = new SqlParameter("@usething_month", SqlDbType.Int);
            paramArray[8] = new SqlParameter("@usesick_month", SqlDbType.Int);
            paramArray[9] = new SqlParameter("@vac_surplus", SqlDbType.Int);
            paramArray[10] = new SqlParameter("@reserve_premonth", SqlDbType.Int);
            paramArray[11] = new SqlParameter("@reserve_new", SqlDbType.Int);
            paramArray[12] = new SqlParameter("@reserve", SqlDbType.Int);
            //2010.12.13新增延休假
            paramArray[13] = new SqlParameter("@ext", SqlDbType.Int);
            paramArray[14] = new SqlParameter("@useext", SqlDbType.Int);
            paramArray[15] = new SqlParameter("@useext_month", SqlDbType.Int);
            paramArray[16] = new SqlParameter("@ext_surplus", SqlDbType.Int);
            paramArray[17] = new SqlParameter("@ext_display", SqlDbType.Bit);
            paramArray[18] = new SqlParameter("@ext_import", SqlDbType.Bit);
            // 2018/05/23 新增補休假
            paramArray[19] = new SqlParameter("@usemenstruation", SqlDbType.Int);
            paramArray[20] = new SqlParameter("@usemenstruation_month", SqlDbType.Int);
            paramArray[21] = new SqlParameter("@emp_gender", SqlDbType.NChar, 1);
            paramArray[22] = new SqlParameter("@CompensatoryHolidays_new", SqlDbType.Int);
            paramArray[23] = new SqlParameter("@CompensatoryHolidays_use", SqlDbType.Int);
            paramArray[24] = new SqlParameter("@CompensatoryHolidays_new_sum", SqlDbType.Int);
            paramArray[25] = new SqlParameter("@CompensatoryHolidays_use_sum", SqlDbType.Int);
            paramArray[26] = new SqlParameter("@CompensatoryHolidays", SqlDbType.Int);
            for (int i = 2; i <= 26; i++)
            {
                paramArray[i].Direction = ParameterDirection.Output;
            }
            paramArray[27] = new SqlParameter("@rvalue", SqlDbType.Int);
            paramArray[27].Direction = ParameterDirection.ReturnValue;
            //DataTable dt = 
            SqlHelper.GetDataTableByStoredProcedure(_connectionString, storedProcedureName: @"[dbo].[sp_GetEmpRest]", paramArray);
            return paramArray;
        }

        /// <summary>
        /// 讀取員工指定月(依據查詢日期 ) 已填報有效(簽核中與同意)且為加班上限範圍內之加班總時數
        /// </summary>
        /// <param name="theDate">查詢日期.</param>
        /// <param name="empno">員工編號.</param>
        public int GetMonthEmployeeOvertimeHours(DateTime theDate, string empno)
        {
            int hours = 0;
            string sqlstr = "SELECT SUM(B1_HOURS) AS MonthlyHours FROM B1RATE WHERE (IsOverTime = 1) AND (B1_YYMMDD like @YYYMM) AND (B1_EMPNO = @EmpNo);";
            string yyymm = $"{CardUtility.RocChineseYYYMM(theDate)}%";
            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            parameterEmpNo.Value = empno;
            SqlParameter parameterYYYMM = new SqlParameter("@YYYMM", SqlDbType.VarChar);
            parameterYYYMM.Value = yyymm;
            List<SqlParameter> parameters = new List<SqlParameter>
            {
                parameterEmpNo,
                parameterYYYMM
            };
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sqlstr, parameters);
            if (dt != null && dt.Rows.Count == 1
                && dt.Rows[0]["MonthlyHours"] != null && dt.Rows[0]["MonthlyHours"] != DBNull.Value)
            {
                hours = (int)dt.Rows[0]["MonthlyHours"];
            }
            return hours;
        }

        /// <summary>
        /// 讀取員工指定月(依據查詢日期 ) 已填報有效(簽核中與同意)且為加班上限範圍內之加班總時數
        /// </summary>
        /// <param name="date">查詢日期.</param>
        /// <param name="empno">員工編號.</param>
        public DataTable GetMonthEmployeeOvertimeStatics(DateTime date, string empno)
        {
            string sqlstr = @"WITH B1CardTemp (B1_EMPNO, B1_SHEETNO, B1_STATUS) AS
            ( SELECT B1_EMPNO, B1_SHEETNO, B1_STATUS FROM B1CARD 
             WHERE (B1_YYMM = @YYYMM) AND (B1_EMPNO = @EmpNo) AND B1CARD.B1_STATUS IN (1,2)
             GROUP BY B1_EMPNO, B1_SHEETNO, B1_STATUS )
            SELECT [1] AS UnderApprovalHours, [2] AS ApprovedHours
            FROM  (  SELECT B1CardTemp.B1_STATUS, B1RATE.B1_HOURS FROM B1RATE
             INNER JOIN B1CardTemp ON B1RATE.B1_SHEETNO = B1CardTemp.B1_SHEETNO WHERE IsOvertime=1
            ) AS SourceTable  
            PIVOT  ( SUM(B1_HOURS)  FOR B1_STATUS IN ([1], [2]) ) AS PivotTable;";
            string yyymm = CardUtility.RocChineseYYYMM(date);
            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            parameterEmpNo.Value = empno;
            SqlParameter parameterYYYMM = new SqlParameter("@YYYMM", SqlDbType.VarChar);
            parameterYYYMM.Value = yyymm;
            List<SqlParameter> parameters = new List<SqlParameter>
            {
                parameterEmpNo,
                parameterYYYMM
            };
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sqlstr, parameters);
            return dt;
        }

        /// <summary>
        /// 讀取員工指定月(依據查詢日期 ) 已填報有效(簽核中與同意)且為加班上限範圍內之加權後加班總時數
        /// </summary>
        /// <param name="theDate">查詢日期.</param>
        /// <param name="empno">員工編號.</param>
        public double GetMonthEmployeeWeightedOvertimeHours(DateTime theDate, string empno)
        {
            double hours = 0.0;
            ////檢查加班時數，包含保留代休
            string sqlstr = "SELECT B1_HOURS,B1_RATE,B1_YYMMDD FROM B1RATE WHERE (IsOverTime = 1) AND (B1_YYMMDD like @YYYMM) AND (B1_EMPNO = @EmpNo);";
            string yyymm = $"{CardUtility.RocChineseYYYMM(theDate)}%";
            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            parameterEmpNo.Value = empno;
            SqlParameter parameterYYYMM = new SqlParameter("@YYYMM", SqlDbType.VarChar);
            parameterYYYMM.Value = yyymm;
            List<SqlParameter> parameters = new List<SqlParameter>
            {
                parameterEmpNo,
                parameterYYYMM
            };
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sqlstr, parameters);
            if (dt != null && dt.Rows.Count > 0)
            {
                hours = GetWeightedHoursFromDataTable(hours, dt);

                //檢查加班時數中是否有保留代休
                sqlstr = "SELECT B1_HOURS,B1_RATE,B1_YYMMDD FROM B1RATE_CompHol WHERE (B1_YYMMDD like @YYYMM) AND (B1_EMPNO = @EmpNo);";
                parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
                parameterEmpNo.Value = empno;
                parameterYYYMM = new SqlParameter("@YYYMM", SqlDbType.VarChar);
                parameterYYYMM.Value = yyymm;
                parameters = new List<SqlParameter>
                {
                    parameterEmpNo,
                    parameterYYYMM
                };
                DataTable dtComp = SqlHelper.GetDataTable(_connectionString, sqlstr, parameters);
                if (dtComp != null && dtComp.Rows.Count > 0)
                {
                    hours = GetWeightedHoursFromDataTable(hours, dtComp);
                }
            }
            return hours;
        }

        /// <summary>
        /// 讀取員工指定月份出勤時間字串
        /// </summary>
        /// <param name="date">The date.</param>
        /// <param name="userid">The userId.</param>
        /// <returns></returns>
        public DataTable GetMonthInTimeString(DateTime date, string userid)
        {
            DataTable dt = new DataTable();
            if (userid != null)
            {
                string temp = string.Format("{0, 4:G}-{1:D2}-%", date.Year, date.Month);
                string strsql = "SELECT IN_EMPNO, IN_YYYYMMDD, IN_HHMM FROM INTIME_DAY WHERE IN_YYYYMMDD like @YYYYMMDD AND IN_EMPNO=@EmpNo;";

                List<SqlParameter> parameters = new List<SqlParameter>();
                SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
                parameterEmpNo.Value = userid;
                parameters.Add(parameterEmpNo);

                SqlParameter parameterYYYYMMDD = new SqlParameter("@YYYYMMDD", SqlDbType.VarChar);
                parameterYYYYMMDD.Value = temp;
                parameters.Add(parameterYYYYMMDD);

                dt = SqlHelper.GetDataTable(_connectionString, strsql, parameters);
            }
            return dt;
        }

        /// <summary>取得指定表單新單號 (最大表單單號+1)</summary>
        /// <param name="formID">表單編號</param>
        /// <param name="chineseYear">民國年</param>
        public string GetNewFormNo(string formID, string chineseYear)
        {
            string ret = string.Empty;
            string sql = "SELECT dbo.fn_GetNewFormNo(@FormID, @ChineseYear);";
            SqlParameter[] parameters = new SqlParameter[2];
            parameters[0] = new SqlParameter("@FormID", SqlDbType.VarChar, 10);
            parameters[0].Value = formID;
            parameters[1] = new SqlParameter("@ChineseYear", SqlDbType.VarChar, 3);
            parameters[1].Value = chineseYear;
            object? obj = SqlHelper.GetFieldValue(_connectionString, sql, parameters);
            if (obj != null)
            {
                ret = (string)obj;
            }
            return ret;
        }

        /// <summary>
        /// 取得所有通知
        /// </summary>
        /// <returns>通知 DataTable</returns>
        public DataTable GetNotifications()
        {
            string strSql = "SELECT ID, NotifyEmpNo, NotifyName, NotifyTime, ViewTime FROM Notification;";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql);
            return dt;
        }

        /// <summary>
        /// 取得特定時間區間的通知表單卡別資料
        /// </summary>
        /// <param name="empNo">申請人員工編號</param>
        /// <param name="deptNo">申請人部門編號</param>
        /// <param name="roles">被通知者的角色</param>
        /// <param name="isRead">通知狀態，1代表未讀，2代表已讀</param>
        /// <param name="formStatus">表單狀態</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <returns>通知 DataTable</returns>
        public DataTable GetNotifyFormCards(string empNo, int deptNo, List<Role> roles, List<int> isRead, List<int> formStatus, DateTime startDate, DateTime endDate)
        {
            string strSqlEmpNo = string.Empty;
            string strSqlDeptNo = string.Empty;
            string strSqlIsRead = string.Empty;
            string strSqlFormStatus = string.Empty;
            List<SqlParameter> parameters = new List<SqlParameter>();

            if (!string.IsNullOrWhiteSpace(empNo))
            {
                strSqlEmpNo = " AND Form.EmpNo = @EmpNo ";
                SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.NVarChar);
                parameterEmpNo.Value = empNo;
                parameters.Add(parameterEmpNo);
            }

            if (deptNo > 0)
            {
                strSqlDeptNo = " AND Form.DeptNo = @DeptNo ";
                SqlParameter parameterDeptNo = new SqlParameter("@DeptNo", SqlDbType.Int);
                parameterDeptNo.Value = deptNo;
                parameters.Add(parameterDeptNo);
            }

            string strSqlRoleId = string.Join(",", roles.Select(item => $"'{item.RoleId}'"));

            if (isRead.Count == 0)
            {
                strSqlIsRead = " AND 1 = 0 ";
            }
            else
            {
                strSqlIsRead = " AND (";
                for (int index = 0; index < isRead.Count; index++)
                {
                    if (index > 0)
                    {
                        strSqlIsRead += " OR ";
                    }

                    if (isRead[index] == 1) // 未讀的通知
                    {
                        strSqlIsRead += " Notification.ViewTime IS NULL ";
                    }
                    else if (isRead[index] == 2) // 已讀的通知
                    {
                        strSqlIsRead += " Notification.ViewTime IS NOT NULL ";
                    }
                }
                strSqlIsRead += ")";
            }

            if (formStatus.Count == 0)
            {
                strSqlFormStatus = " AND 1 = 0 ";
            }
            else
            {
                for (int index = 0; index < formStatus.Count; index++)
                {
                    SqlParameter parameterFormStatus = new SqlParameter($"@FormStatus{index}", SqlDbType.TinyInt);
                    parameterFormStatus.Value = formStatus[index];
                    parameters.Add(parameterFormStatus);
                }
                string strSqlFormStatusParams = string.Join(",", formStatus);
                strSqlFormStatus = $" AND Form.FormStatus IN ({strSqlFormStatusParams})";

            }

            string strSql = @$"
                -- Select Form Which FormID is 'A1Card'
                SELECT
                    Notification.ID,
                    Notification.NotifyEmpNo,
                    Notification.NotifyName,
                    Notification.NotifyTime,
                    Notification.ViewTime,
                    Form.FormUID, 
                    Form.FormID,
                    Form.FormNo,
                    Form.FormSubject,
                    Form.FormInfo,
                    Form.EmpNo,
                    Form.EmpName,
                    Form.DeptNo,
                    Form.FormStatus,
                    FormStatus.Name AS FormStatusName,
                    A1CARD.ID AS CardID,
                    CASE A1CARD.A1_NN WHEN '1' THEN '上旬' WHEN '2' THEN '中旬' WHEN '3' THEN '下旬' END AS ApplicationType 
                FROM Form
                INNER JOIN FormStatus ON FormStatus.ID = Form.FormStatus
                INNER JOIN Notification ON Notification.FormUID = Form.FormUID
                INNER JOIN A1CARD ON A1CARD.FormUID = Form.FormUID
                WHERE TRIM(Notification.NotifyEmpNo) IN ({strSqlRoleId})
                {strSqlEmpNo}
                {strSqlDeptNo}
                {strSqlIsRead}
                {strSqlFormStatus}
                AND CONVERT(DATE, Notification.NotifyTime) >= @StartDate
                AND CONVERT(DATE, Notification.NotifyTime) <= @EndDate
                -- Select Form Which FormID is 'B1CardApp' 
                UNION ALL
                SELECT
                    Notification.ID,
                    Notification.NotifyEmpNo,
                    Notification.NotifyName,
                    Notification.NotifyTime,
                    Notification.ViewTime,
                    Form.FormUID, 
                    Form.FormID,
                    Form.FormNo,
                    Form.FormSubject,
                    Form.FormInfo,
                    Form.EmpNo,
                    Form.EmpName,
                    Form.DeptNo,
                    Form.FormStatus,
                    FormStatus.Name AS FormStatusName,
                    B1CARDAPP.ID AS CardID,
                    CASE B1CARDAPP.B1_Code WHEN '1' THEN '加班' WHEN '2' THEN '社外加班' WHEN '3' THEN '補休假' END AS ApplicationType 
                FROM Form
                INNER JOIN FormStatus ON FormStatus.ID = Form.FormStatus
                INNER JOIN Notification ON Notification.FormUID = Form.FormUID
                INNER JOIN B1CARDAPP ON B1CARDAPP.FormUID = Form.FormUID
                WHERE TRIM(Notification.NotifyEmpNo) IN ({strSqlRoleId})
                {strSqlEmpNo} 
                {strSqlDeptNo}
                {strSqlIsRead}
                {strSqlFormStatus}
                AND CONVERT(DATE, Notification.NotifyTime) >= @StartDate
                AND CONVERT(DATE, Notification.NotifyTime) <= @EndDate
                -- Select Form Which FormID is 'B1Card' 
                UNION ALL
                SELECT
                    Notification.ID,
                    Notification.NotifyEmpNo,
                    Notification.NotifyName,
                    Notification.NotifyTime,
                    Notification.ViewTime,
                    Form.FormUID, 
                    Form.FormID,
                    Form.FormNo,
                    Form.FormSubject,
                    Form.FormInfo,
                    Form.EmpNo,
                    Form.EmpName,
                    Form.DeptNo,
                    Form.FormStatus,
                    FormStatus.Name AS FormStatusName,
                    B1CARD.ID AS CardID,
                    CASE B1CARD.B1_Code WHEN '1' THEN '加班' WHEN '2' THEN '社外加班' WHEN '3' THEN '補休假' END AS ApplicationType
                FROM Form
                INNER JOIN FormStatus ON FormStatus.ID = Form.FormStatus
                INNER JOIN Notification ON Notification.FormUID = Form.FormUID 
                INNER JOIN B1CARD ON B1CARD.FormUID = Form.FormUID
                WHERE TRIM(Notification.NotifyEmpNo) IN ({strSqlRoleId})
                {strSqlEmpNo} 
                {strSqlDeptNo}
                {strSqlIsRead}
                {strSqlFormStatus}
                AND CONVERT(DATE, Notification.NotifyTime) >= @StartDate
                AND CONVERT(DATE, Notification.NotifyTime) <= @EndDate
                -- Select Form Which FormID is 'C1Card' 
                UNION ALL
                SELECT
                    Notification.ID,
                    Notification.NotifyEmpNo,
                    Notification.NotifyName,
                    Notification.NotifyTime,
                    Notification.ViewTime,
                    Form.FormUID, 
                    Form.FormID,
                    Form.FormNo,
                    Form.FormSubject,
                    Form.FormInfo,
                    Form.EmpNo,
                    Form.EmpName,
                    Form.DeptNo,
                    Form.FormStatus,
                    FormStatus.Name AS FormStatusName,
                    C1CARD.ID AS CardID,
                    RESTKIND.R1_NAME AS ApplicationType 
                FROM Form
                INNER JOIN FormStatus ON FormStatus.ID = Form.FormStatus
                INNER JOIN Notification ON Notification.FormUID = Form.FormUID 
                INNER JOIN C1CARD ON C1CARD.FormUID = Form.FormUID AND C1CARD.C1_SERIALNO = '1'
                INNER JOIN RESTKIND ON (C1CARD.C1_CODE=RESTKIND.R1_NO AND C1CARD.C1_CODE2=RESTKIND.R2_NO)
                WHERE TRIM(Notification.NotifyEmpNo) IN ({strSqlRoleId})
                {strSqlEmpNo} 
                {strSqlDeptNo}
                {strSqlIsRead}
                {strSqlFormStatus}
                AND CONVERT(DATE, Notification.NotifyTime) >= @StartDate
                AND CONVERT(DATE, Notification.NotifyTime) <= @EndDate;";

            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);

            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql, parameters);
            return dt;
        }

        /// <summary>
        /// 讀取員工指定季(依據查詢日期 ) 已填報有效(簽核中與同意)且為加班上限範圍內之加班總時數
        /// </summary>
        /// <param name="theDate">查詢日期.</param>
        /// <param name="empno">員工編號.</param>
        public int GetQuarterlyEmployeeOvertimeHours(DateTime theDate, string empno)
        {
            int hours = 0;
            string sqlstr = "SELECT SUM(B1_HOURS) AS Hours FROM B1RATE WHERE (IsOverTime = 1) AND (B1_EMPNO = @EmpNo)";
            string yyy = CardUtility.RocChineseYear(theDate).ToString();
            int quarterly = (theDate.Month - 1) / 3 + 1;
            switch (quarterly)
            {
                case 1:
                    sqlstr += $" AND ( (B1_YYMMDD like '{yyy}01%') OR (B1_YYMMDD like '{yyy}02%') OR (B1_YYMMDD like '{yyy}03%') );";
                    break;
                case 2:
                    sqlstr += $" AND ( (B1_YYMMDD like '{yyy}04%') OR (B1_YYMMDD like '{yyy}05%') OR (B1_YYMMDD like '{yyy}06%') );";
                    break;
                case 3:
                    sqlstr += $" AND ( (B1_YYMMDD like '{yyy}07%') OR (B1_YYMMDD like '{yyy}08%') OR (B1_YYMMDD like '{yyy}09%') );";
                    break;
                case 4:
                    sqlstr += $" AND ( (B1_YYMMDD like '{yyy}10%') OR (B1_YYMMDD like '{yyy}11%') OR (B1_YYMMDD like '{yyy}12%') );";
                    break;
            }

            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            parameterEmpNo.Value = empno;
            List<SqlParameter> parameters = new List<SqlParameter>();
            parameters.Add(parameterEmpNo);
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sqlstr, parameters);
            if (dt != null && dt.Rows.Count == 1)
            {
                if (dt.Rows[0]["Hours"] != null && dt.Rows[0]["Hours"] != DBNull.Value)
                {
                    hours = (int)dt.Rows[0]["Hours"];
                }
            }
            return hours;
        }

        public static double GetRateFromB1Hours(char charRate)
        {
            double rate = 1.0;
            switch (charRate)
            {
                case '1':
                    rate = 1.0;
                    break;
                case '2':
                    rate = 4.0 / 3.0;
                    break;
                case '3':
                    rate = 5.0 / 3.0;
                    break;
                case '4':
                    rate = 0.0; //保留代休假 先不算
                    break;
                case '5':
                    rate = 1.0;
                    break;
                case '6':
                    rate = 8.0 / 3.0;
                    break;
            }

            return rate;
        }

        /// <summary>取得 填表人/表單關係人 已填表單</summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="startDate">填表啟始日期</param>
        /// <param name="endDate">填表結束日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns>寄件匣 DataTable</returns>
        public DataTable GetSentBox(string empNo, DateTime? startDate = null, DateTime? endDate = null, int? status = null)
        {
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            parameterEmpNo.Value = empNo;
            parameters.Add(parameterEmpNo);
            string dateCondition = string.Empty;
            string statusCondition = string.Empty;

            if (startDate != null && endDate != null /*&& startDate <= endDate*/)
            {
                dateCondition = @" AND (CONVERT(DATE,CreatedTime) >= CONVERT(DATE,@StartDate)) AND 
                (CONVERT(DATE,CreatedTime) <= CONVERT(DATE,@EndDate)) ";

                SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
                parameterStartDate.Value = startDate;
                parameters.Add(parameterStartDate);
                SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
                parameterEndDate.Value = endDate;
                parameters.Add(parameterEndDate);
            }

            if (status != null && status != 0)
            {
                statusCondition = @" AND FormStatus=@Status";
                SqlParameter parameterStatus = new SqlParameter("@Status", SqlDbType.Int);
                parameterStatus.Value = status;
                parameters.Add(parameterStatus);
            }

            string strSql = @$"SELECT Form.ID, A1Card.ID AS CardID, Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 CASE A1_NN WHEN '1' THEN '上旬' WHEN '2' THEN '中旬' WHEN '3' THEN '下旬' END AS ApplicationType, 
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime,
 A1CARD.A1_HOUR AS TotalHours, 
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost
FROM Form INNER JOIN
 A1CARD ON A1CARD.FormUID = Form.FormUID
WHERE ( (CreatedEmpNo=@EmpNo) OR (EmpNo=@EmpNo) ) {dateCondition} {statusCondition}
UNION ALL 
SELECT Form.ID, B1CARDAPP.ID AS CardID,
 Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 CASE B1_Code WHEN '1' THEN '加班' WHEN '2' THEN '社外加班' WHEN '3' THEN '補休假' END AS ApplicationType, 
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime,
 B1CARDAPP.B1_Hour AS TotalHours,
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost
FROM Form INNER JOIN
 B1CARDAPP ON B1CARDAPP.FormUID = Form.FormUID
WHERE ( (CreatedEmpNo=@EmpNo) OR (EmpNo=@EmpNo) ) {dateCondition} {statusCondition}
UNION ALL 
SELECT Form.ID, B1Card.ID AS CardID,
 Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 CASE B1_CODE WHEN '1' THEN '加班' WHEN '2' THEN '社外加班' WHEN '3' THEN '補休假' END AS ApplicationType,
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime,
 B1CARD.B1_Hour AS TotalHours,
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost
FROM Form INNER JOIN
 B1CARD ON B1CARD.FormUID = Form.FormUID
WHERE ( (CreatedEmpNo=@EmpNo) OR (EmpNo=@EmpNo) ) {dateCondition} {statusCondition}
UNION ALL 
SELECT Form.ID, C1CARD.ID AS CardID,
 Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 RESTKIND.R1_NAME AS ApplicationType, Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, 
 Form.TeamCName, Form.CreatedEmpNo, Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, 
 Form.CreatedHost, Form.AddedSigner, Form.StartTime, Form.EndTime,
 C1CARD.C1_Hour AS TotalHours, 
 Form.FormStatus, Form.TotalSteps, 
 Form.CurrentStep, Form.UpdatedEmpNo, Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, 
 Form.UpdatedHost
FROM Form INNER JOIN
 C1CARD ON C1CARD.FormUID = Form.FormUID INNER JOIN
 RESTKIND ON C1CARD.C1_CODE = RESTKIND.R1_NO AND C1CARD.C1_CODE2=RESTKIND.R2_NO
WHERE ( (CreatedEmpNo=@EmpNo) OR (EmpNo=@EmpNo) ) {dateCondition} {statusCondition}
ORDER BY Form.ID,CardID;";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql, parameters);
            return dt;
        }

        /// <summary>取得 填表人/表單關係人 已填表單</summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="startDate">填表啟始日期</param>
        /// <param name="endDate">填表結束日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <param name="status">表單狀態</param>
        /// <returns>寄件匣 DataTable</returns>
        public DataTable GetSentBox(string empNo, DateTime startDate, DateTime endDate, string projNo, int? status = null)
        {
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            parameterEmpNo.Value = empNo;
            parameters.Add(parameterEmpNo);
            string dateCondition = string.Empty;
            string statusCondition = string.Empty;

            SqlParameter parameterPrjNo = new SqlParameter("@PrjNo", SqlDbType.VarChar);
            parameterPrjNo.Value = $"%{projNo}%";
            parameters.Add(parameterPrjNo);

            // if (startDate <= endDate)
            // {
            dateCondition = @" AND (CONVERT(DATE,CreatedTime) >= CONVERT(DATE,@StartDate)) AND 
                (CONVERT(DATE,CreatedTime) <= CONVERT(DATE,@EndDate)) ";

            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);
            // }

            if (status != null && status != 0)
            {
                statusCondition = @" AND FormStatus=@Status";
                SqlParameter parameterStatus = new SqlParameter("@Status", SqlDbType.Int);
                parameterStatus.Value = status;
                parameters.Add(parameterStatus);
            }

            string strSql = @$"SELECT Form.ID, A1Card.ID AS CardID, Form.FormUID, Form.FormID, Form.FormNo, 
 Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 CASE A1_NN WHEN '1' THEN '上旬' WHEN '2' THEN '中旬' WHEN '3' THEN '下旬' END AS ApplicationType, 
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime,
 A1CARD.A1_HOUR AS TotalHours, 
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost
FROM Form INNER JOIN
 A1CARD ON A1CARD.FormUID = Form.FormUID
WHERE (A1CARD.A1_PROJNO like @PrjNo) AND ( (CreatedEmpNo=@EmpNo) OR (EmpNo=@EmpNo) )
{dateCondition} {statusCondition} 
UNION ALL 
SELECT Form.ID, B1CARDAPP.ID AS CardID,
 Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 CASE B1_Code WHEN '1' THEN '加班' WHEN '2' THEN '社外加班' WHEN '3' THEN '補休假' END AS ApplicationType, 
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime,
 B1CARDAPP.B1_Hour AS TotalHours, 
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost
FROM Form INNER JOIN
 B1CARDAPP ON B1CARDAPP.FormUID = Form.FormUID
WHERE (B1CARDAPP.B1_PrjNo like @PrjNo) AND ( (CreatedEmpNo=@EmpNo) OR (EmpNo=@EmpNo) )
{dateCondition} {statusCondition} 
UNION ALL 
SELECT Form.ID, B1Card.ID AS CardID,
 Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 CASE B1_CODE WHEN '1' THEN '加班' WHEN '2' THEN '社外加班' WHEN '3' THEN '補休假' END AS ApplicationType,
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime,
 B1CARD.B1_Hour AS TotalHours,
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost
FROM Form INNER JOIN
 B1CARD ON B1CARD.FormUID = Form.FormUID
WHERE (B1CARD.B1_PROJNO like @PrjNo) AND ( (CreatedEmpNo=@EmpNo) OR (EmpNo=@EmpNo) )
{dateCondition} {statusCondition} 
UNION ALL 
SELECT Form.ID, C1CARD.ID AS CardID,
 Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 RESTKIND.R1_NAME AS ApplicationType, Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, 
 Form.TeamCName, Form.CreatedEmpNo, Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, 
 Form.CreatedHost, Form.AddedSigner, Form.StartTime, Form.EndTime,
 C1CARD.C1_Hour AS TotalHours, 
 Form.FormStatus, Form.TotalSteps, 
 Form.CurrentStep, Form.UpdatedEmpNo, Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, 
 Form.UpdatedHost
 FROM Form INNER JOIN
 C1CARD ON C1CARD.FormUID = Form.FormUID INNER JOIN
 RESTKIND ON C1CARD.C1_CODE = RESTKIND.R1_NO AND C1CARD.C1_CODE2=RESTKIND.R2_NO
 WHERE (C1CARD.C1_PrjNo like @PrjNo) AND ( (CreatedEmpNo=@EmpNo) OR (EmpNo=@EmpNo) ) 
{dateCondition} {statusCondition} 
ORDER BY Form.ID,CardID;";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql, parameters);
            return dt;
        }

        /// <summary>取得 填表人/表單關係人 已填表單，以內容日期為準</summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns>寄件匣 DataTable</returns>
        public DataTable GetSentBoxByContentDate(string empNo, DateTime startDate, DateTime endDate, int? status = null)
        {
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            parameterEmpNo.Value = empNo;
            parameters.Add(parameterEmpNo);

            string statusCondition = string.Empty;

            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);
            if (status != null && status != 0)
            {
                statusCondition = @" AND FormStatus=@Status";
                SqlParameter parameterStatus = new SqlParameter("@Status", SqlDbType.Int);
                parameterStatus.Value = status;
                parameters.Add(parameterStatus);
            }

            string strSql = @$"SELECT Form.ID, A1Card.ID AS CardID, Form.FormUID, Form.FormID, Form.FormNo, 
 Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 CASE A1_NN WHEN '1' THEN '上旬' WHEN '2' THEN '中旬' WHEN '3' THEN '下旬' END AS ApplicationType, 
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime, 
 A1CARD.A1_HOUR AS TotalHours,
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost
FROM Form INNER JOIN
 A1CARD ON A1CARD.FormUID = Form.FormUID
WHERE ( (CreatedEmpNo=@EmpNo) OR (EmpNo=@EmpNo) )
AND (CONVERT(DATE,Form.ContentStartTime) <= CONVERT(DATE,@EndDate))
AND (CONVERT(DATE,Form.ContentEndTime) >= CONVERT(DATE,@StartDate))
{statusCondition} 
UNION ALL 
SELECT Form.ID, B1CARDAPP.ID AS CardID,
 Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 CASE B1_Code WHEN '1' THEN '加班' WHEN '2' THEN '社外加班' WHEN '3' THEN '補休假' END AS ApplicationType, 
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime,
 B1CARDAPP.B1_Hour AS TotalHours,
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost
FROM Form INNER JOIN
 B1CARDAPP ON B1CARDAPP.FormUID = Form.FormUID
WHERE ( (CreatedEmpNo=@EmpNo) OR (EmpNo=@EmpNo) )
AND (CONVERT(DATE,B1_Date) >= CONVERT(DATE,@StartDate)) 
AND (CONVERT(DATE,B1_Date) <= CONVERT(DATE,@EndDate)) 
{statusCondition} 
UNION ALL 
SELECT Form.ID, B1Card.ID AS CardID,
 Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 CASE B1_CODE WHEN '1' THEN '加班' WHEN '2' THEN '社外加班' WHEN '3' THEN '補休假' END AS ApplicationType,
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime,
 B1CARD.B1_Hour AS TotalHours, 
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost
 FROM Form INNER JOIN
 B1CARD ON B1CARD.FormUID = Form.FormUID
WHERE ( (CreatedEmpNo=@EmpNo) OR (EmpNo=@EmpNo) )
AND (CONVERT(DATE,B1_EndDate) >= CONVERT(DATE,@StartDate)) 
AND (CONVERT(DATE,B1_StartDate) <= CONVERT(DATE,@EndDate)) 
{statusCondition} 
UNION ALL 
SELECT Form.ID, C1CARD.ID AS CardID,
 Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 RESTKIND.R1_NAME AS ApplicationType, Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, 
 Form.TeamCName, Form.CreatedEmpNo, Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, 
 Form.CreatedHost, Form.AddedSigner, Form.StartTime, Form.EndTime,
 C1CARD.C1_Hour AS TotalHours, 
 Form.FormStatus, Form.TotalSteps, 
 Form.CurrentStep, Form.UpdatedEmpNo, Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, 
 Form.UpdatedHost
FROM Form INNER JOIN
 C1CARD ON C1CARD.FormUID = Form.FormUID INNER JOIN
 RESTKIND ON C1CARD.C1_CODE = RESTKIND.R1_NO AND C1CARD.C1_CODE2=RESTKIND.R2_NO
WHERE ( (CreatedEmpNo=@EmpNo) OR (EmpNo=@EmpNo) ) 
AND (CONVERT(DATE,C1_StartDate) <= CONVERT(DATE,@EndDate)) 
AND (CONVERT(DATE,C1_EndDate) >= CONVERT(DATE,@StartDate)) 
{statusCondition} 
ORDER BY Form.ID,CardID;";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql, parameters);
            return dt;
        }

        /// <summary>取得 填表人/表單關係人 已填表單，以內容日期為準</summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <param name="status">表單狀態</param>
        /// <returns>寄件匣 DataTable</returns>
        public DataTable GetSentBoxByContentDate(string empNo, DateTime startDate, DateTime endDate, string projNo, int? status = null)
        {
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            parameterEmpNo.Value = empNo;
            parameters.Add(parameterEmpNo);
            string statusCondition = string.Empty;

            SqlParameter parameterPrjNo = new SqlParameter("@PrjNo", SqlDbType.VarChar);
            parameterPrjNo.Value = $"%{projNo}%";
            parameters.Add(parameterPrjNo);
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);
            if (status != null && status != 0)
            {
                statusCondition = @" AND FormStatus=@Status";
                SqlParameter parameterStatus = new SqlParameter("@Status", SqlDbType.Int);
                parameterStatus.Value = status;
                parameters.Add(parameterStatus);
            }
            string strSql = @$"SELECT Form.ID, A1Card.ID AS CardID, Form.FormUID, Form.FormID, Form.FormNo, 
 Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 CASE A1_NN WHEN '1' THEN '上旬' WHEN '2' THEN '中旬' WHEN '3' THEN '下旬' END AS ApplicationType, 
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime,
 A1CARD.A1_HOUR AS TotalHours,
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost
 FROM Form INNER JOIN
 A1CARD ON A1CARD.FormUID = Form.FormUID
 WHERE (A1CARD.A1_PROJNO like @PrjNo) AND ( (CreatedEmpNo=@EmpNo) OR (EmpNo=@EmpNo) )
 AND CAST(Form.ContentStartTime AS DATE) <=@EndDate AND CAST(Form.ContentEndTime AS DATE) >= @StartDate
{statusCondition} 
UNION ALL 
SELECT Form.ID, B1CARDAPP.ID AS CardID,
 Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 CASE B1_Code WHEN '1' THEN '加班' WHEN '2' THEN '社外加班' WHEN '3' THEN '補休假' END AS ApplicationType, 
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime, 
 B1CARDAPP.B1_Hour AS TotalHours, 
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost
FROM Form INNER JOIN
 B1CARDAPP ON B1CARDAPP.FormUID = Form.FormUID
WHERE (B1CARDAPP.B1_PrjNo like @PrjNo) AND ( (CreatedEmpNo=@EmpNo) OR (EmpNo=@EmpNo) )
AND CAST(Form.ContentStartTime AS DATE) <=@EndDate AND CAST(Form.ContentEndTime AS DATE) >= @StartDate
{statusCondition} 
UNION ALL 
SELECT Form.ID, B1Card.ID AS CardID,
 Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 CASE B1_CODE WHEN '1' THEN '加班' WHEN '2' THEN '社外加班' WHEN '3' THEN '補休假' END AS ApplicationType,
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime,
 B1CARD.B1_Hour AS TotalHours, 
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost
FROM Form INNER JOIN
 B1CARD ON B1CARD.FormUID = Form.FormUID
WHERE (B1CARD.B1_PROJNO like @PrjNo) AND ( (CreatedEmpNo=@EmpNo) OR (EmpNo=@EmpNo) )
AND CAST(Form.ContentStartTime AS DATE) <=@EndDate AND CAST(Form.ContentEndTime AS DATE) >= @StartDate
{statusCondition} 
UNION ALL 
SELECT Form.ID, C1CARD.ID AS CardID,
 Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 RESTKIND.R1_NAME AS ApplicationType, Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, 
 Form.TeamCName, Form.CreatedEmpNo, Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, 
 Form.CreatedHost, Form.AddedSigner, Form.StartTime, Form.EndTime, 
 C1CARD.C1_Hour AS TotalHours, 
 Form.FormStatus, Form.TotalSteps, 
 Form.CurrentStep, Form.UpdatedEmpNo, Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, 
 Form.UpdatedHost
FROM Form INNER JOIN
 C1CARD ON C1CARD.FormUID = Form.FormUID INNER JOIN
 RESTKIND ON C1CARD.C1_CODE = RESTKIND.R1_NO AND C1CARD.C1_CODE2=RESTKIND.R2_NO
WHERE (C1CARD.C1_PrjNo like @PrjNo) AND ( (CreatedEmpNo=@EmpNo) OR (EmpNo=@EmpNo) ) 
AND CAST(Form.ContentStartTime AS DATE) <=@EndDate AND CAST(Form.ContentEndTime AS DATE) >= @StartDate
{statusCondition} 
ORDER BY Form.ID,CardID;";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql, parameters);
            return dt;
        }

        /// <summary>
        /// 取得 填表人/表單關係人 某月份已填表單
        /// </summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <param name="status">表單狀態</param>
        /// <returns>已填表單</returns>
        public DataTable GetSentBoxYearMonth(string empNo, int year, int month, int? status = null)
        {
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            parameterEmpNo.Value = empNo;
            parameters.Add(parameterEmpNo);
            string strSql = @" SELECT Form.ID, A1Card.ID AS CardID, Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 CASE A1_NN WHEN '1' THEN '上旬' WHEN '2' THEN '中旬' WHEN '3' THEN '下旬' END AS ApplicationType, 
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime,
 A1CARD.A1_HOUR AS TotalHours, 
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost
FROM Form INNER JOIN
 A1CARD ON A1CARD.FormUID = Form.FormUID
WHERE ((CreatedEmpNo = @EmpNo) OR (EmpNo = @EmpNo)) AND (FormInfo LIKE @FormInfo) 
UNION ALL
SELECT Form.ID, B1CARDAPP.ID AS CardID,
 Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 CASE B1_Code WHEN '1' THEN '加班' WHEN '2' THEN '社外加班' WHEN '3' THEN '補休假' END AS ApplicationType, 
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime,
 B1CARDAPP.B1_Hour AS TotalHours, 
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost
FROM Form INNER JOIN
 B1CARDAPP ON B1CARDAPP.FormUID = Form.FormUID
WHERE ((CreatedEmpNo = @EmpNo) OR (EmpNo = @EmpNo)) AND (FormInfo LIKE @FormInfo) 
UNION ALL
SELECT Form.ID, B1Card.ID AS CardID,
 Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 CASE B1_CODE WHEN '1' THEN '加班' WHEN '2' THEN '社外加班' WHEN '3' THEN '補休假' END AS ApplicationType,
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime,
 B1CARD.B1_Hour AS TotalHours, 
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost
FROM Form INNER JOIN
 B1CARD ON B1CARD.FormUID = Form.FormUID
WHERE ((CreatedEmpNo = @EmpNo) OR (EmpNo = @EmpNo)) AND (FormInfo LIKE @FormInfo) 
UNION ALL
SELECT Form.ID, C1CARD.ID AS CardID,
 Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 RESTKIND.R1_NAME AS ApplicationType, Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, 
 Form.TeamCName, Form.CreatedEmpNo, Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, 
 Form.CreatedHost, Form.AddedSigner, Form.StartTime, Form.EndTime,
 C1CARD.C1_Hour AS TotalHours, 
 Form.FormStatus, Form.TotalSteps, 
 Form.CurrentStep, Form.UpdatedEmpNo, Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, 
 Form.UpdatedHost
FROM Form INNER JOIN
 C1CARD ON C1CARD.FormUID = Form.FormUID INNER JOIN
 RESTKIND ON C1CARD.C1_CODE = RESTKIND.R1_NO AND C1CARD.C1_CODE2=RESTKIND.R2_NO
WHERE ((CreatedEmpNo = @EmpNo) OR (EmpNo = @EmpNo)) AND (FormInfo LIKE @FormInfo) 
 ORDER BY Form.ID,CardID;";

            int rocYear = CardUtility.RocChineseYear(year);
            string formInfo = $"{rocYear}_{month.ToString("00")}%";
            SqlParameter parameterFormInfo = new SqlParameter("@FormInfo", SqlDbType.VarChar, 50);
            parameterFormInfo.Value = formInfo;
            parameters.Add(parameterFormInfo);
            if (status != null)
            {
                strSql = @"SELECT Form.ID, A1Card.ID AS CardID, Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 CASE A1_NN WHEN '1' THEN '上旬' WHEN '2' THEN '中旬' WHEN '3' THEN '下旬' END AS ApplicationType, 
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime,
 A1CARD.A1_HOUR AS TotalHours, 
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost
FROM Form INNER JOIN
 A1CARD ON A1CARD.FormUID = Form.FormUID
WHERE ((CreatedEmpNo = @EmpNo) OR (EmpNo = @EmpNo)) AND (FormInfo LIKE @FormInfo) AND FormStatus=@Status
UNION ALL
SELECT Form.ID, B1CARDAPP.ID AS CardID,
 Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 CASE B1_Code WHEN '1' THEN '加班' WHEN '2' THEN '社外加班' WHEN '3' THEN '補休假' END AS ApplicationType, 
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime, 
 B1CARDAPP.B1_Hour AS TotalHours, 
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost
FROM Form INNER JOIN
 B1CARDAPP ON B1CARDAPP.FormUID = Form.FormUID
WHERE ((CreatedEmpNo = @EmpNo) OR (EmpNo = @EmpNo)) AND (FormInfo LIKE @FormInfo) AND FormStatus=@Status
UNION ALL
SELECT Form.ID, B1Card.ID AS CardID,
 Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 CASE B1_CODE WHEN '1' THEN '加班' WHEN '2' THEN '社外加班' WHEN '3' THEN '補休假' END AS ApplicationType,
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime,
 B1CARD.B1_Hour AS TotalHours,
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost
FROM Form INNER JOIN
 B1CARD ON B1CARD.FormUID = Form.FormUID
WHERE ((CreatedEmpNo = @EmpNo) OR (EmpNo = @EmpNo)) AND (FormInfo LIKE @FormInfo) AND FormStatus=@Status
UNION ALL
SELECT Form.ID, C1CARD.ID AS CardID,
 Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 RESTKIND.R1_NAME AS ApplicationType, Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, 
 Form.TeamCName, Form.CreatedEmpNo, Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, 
 Form.CreatedHost, Form.AddedSigner, Form.StartTime, Form.EndTime,
 C1CARD.C1_Hour AS TotalHours, 
 Form.FormStatus, Form.TotalSteps, 
 Form.CurrentStep, Form.UpdatedEmpNo, Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, 
 Form.UpdatedHost
FROM Form INNER JOIN
 C1CARD ON C1CARD.FormUID = Form.FormUID INNER JOIN
 RESTKIND ON C1CARD.C1_CODE = RESTKIND.R1_NO AND C1CARD.C1_CODE2=RESTKIND.R2_NO
WHERE ((CreatedEmpNo = @EmpNo) OR (EmpNo = @EmpNo)) AND (FormInfo LIKE @FormInfo) AND FormStatus=@Status
ORDER BY Form.ID,CardID;";
                SqlParameter parameterStatus = new SqlParameter("@Status", SqlDbType.Int);
                parameterStatus.Value = status;
                parameters.Add(parameterStatus);
            }
            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql, parameters);
            return dt;
        }

        /// <summary>
        /// 取得 已簽核表單，包括本人簽核或代理人簽核，及預設本人角色(非代理)簽核
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="roles">角色</param>
        /// <returns>已簽核表單</returns>
        public DataTable GetSignedForms(string empNo, DateTime startDate, DateTime endDate, List<Role> roles)
        {
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            parameterEmpNo.Value = empNo;
            parameters.Add(parameterEmpNo);
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);
            string strRoles = CardUtility.RolesToQuotedString(roles);

            string strSql = @$"SELECT Form.ID, Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 ApplicationType = CASE A1_NN WHEN '1' THEN '上旬' WHEN '2' THEN '中旬' WHEN '3' THEN '下旬' END,
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime, 
 A1CARD.A1_HOUR AS TotalHours, 
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost, FormFlow.FlowUID, 
 FormFlow.RecipientEmpNo, FormFlow.RecipientName, FormFlow.RecipientDeptNo, FormFlow.RecipientDeptSName, 
 FormFlow.RecipientTeamID, FormFlow.RecipientTeamCName, FormFlow.FlowName, FormFlow.Step, 
 FormFlow.ApproverEmpNo, FormFlow.ApproverName, FormFlow.ApproverDeptNo, FormFlow.ApproverDeptSName, 
 FormFlow.ApproverTeamID, FormFlow.ApproverTeamCName, FormFlow.ApproveTime, FormFlow.ApproveIP,FormFlow.ApproveHost, 
 FormFlow.IsAgentApprove, FormFlow.IsNotification,FormFlow.ApproveComments,
 FormFlow.FlowStatus FROM Form INNER JOIN FormFlow ON Form.FormUID = FormFlow.FormUID
 INNER JOIN
 A1CARD ON A1CARD.FormUID = Form.FormUID
 WHERE (FormFlow.ApproverEmpNo = @EmpNo OR FormFlow.RecipientEmpNo in ({strRoles})) 
 AND (CONVERT(DATE, FormFlow.ApproveTime) >= CONVERT(DATE, @StartDate))
 AND (CONVERT(DATE, FormFlow.ApproveTime) <= CONVERT(DATE, @EndDate))
UNION ALL
SELECT Form.ID, Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
ApplicationType = CASE B1_Code WHEN '1' THEN '加班' WHEN '2' THEN '社外加班' WHEN '3' THEN '補休假' END,
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime,
 B1CARDAPP.B1_Hour AS TotalHours, 
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost, FormFlow.FlowUID, 
 FormFlow.RecipientEmpNo, FormFlow.RecipientName, FormFlow.RecipientDeptNo, FormFlow.RecipientDeptSName, 
 FormFlow.RecipientTeamID, FormFlow.RecipientTeamCName, FormFlow.FlowName, FormFlow.Step, 
 FormFlow.ApproverEmpNo, FormFlow.ApproverName, FormFlow.ApproverDeptNo, FormFlow.ApproverDeptSName, 
 FormFlow.ApproverTeamID, FormFlow.ApproverTeamCName, FormFlow.ApproveTime, FormFlow.ApproveIP,FormFlow.ApproveHost, 
 FormFlow.IsAgentApprove, FormFlow.IsNotification,FormFlow.ApproveComments,
 FormFlow.FlowStatus FROM Form INNER JOIN FormFlow ON Form.FormUID = FormFlow.FormUID
INNER JOIN B1CARDAPP ON B1CARDAPP.FormUID=Form.FormUID
 WHERE (FormFlow.ApproverEmpNo = @EmpNo OR FormFlow.RecipientEmpNo in ({strRoles})) 
 AND (CONVERT(DATE, FormFlow.ApproveTime) >= CONVERT(DATE, @StartDate))
 AND (CONVERT(DATE, FormFlow.ApproveTime) <= CONVERT(DATE, @EndDate))
UNION ALL
SELECT Form.ID, Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
ApplicationType = CASE B1_CODE WHEN '1' THEN '加班' WHEN '2' THEN '社外加班' WHEN '3' THEN '補休假' END,
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime, 
 B1CARD.B1_Hour AS TotalHours, 
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost, FormFlow.FlowUID, 
 FormFlow.RecipientEmpNo, FormFlow.RecipientName, FormFlow.RecipientDeptNo, FormFlow.RecipientDeptSName, 
 FormFlow.RecipientTeamID, FormFlow.RecipientTeamCName, FormFlow.FlowName, FormFlow.Step, 
 FormFlow.ApproverEmpNo, FormFlow.ApproverName, FormFlow.ApproverDeptNo, FormFlow.ApproverDeptSName, 
 FormFlow.ApproverTeamID, FormFlow.ApproverTeamCName, FormFlow.ApproveTime, FormFlow.ApproveIP,FormFlow.ApproveHost, 
 FormFlow.IsAgentApprove, FormFlow.IsNotification,FormFlow.ApproveComments,
 FormFlow.FlowStatus FROM Form INNER JOIN FormFlow ON Form.FormUID = FormFlow.FormUID
INNER JOIN B1CARD ON B1CARD.FormUID=Form.FormUID
 WHERE (FormFlow.ApproverEmpNo = @EmpNo OR FormFlow.RecipientEmpNo in ({strRoles})) 
 AND (CONVERT(DATE, FormFlow.ApproveTime) >= CONVERT(DATE, @StartDate))
 AND (CONVERT(DATE, FormFlow.ApproveTime) <= CONVERT(DATE, @EndDate))
UNION ALL
SELECT Form.ID, Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
R1_NAME AS ApplicationType,
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime,
 C1CARD.C1_Hour AS TotalHours, 
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost, FormFlow.FlowUID, 
 FormFlow.RecipientEmpNo, FormFlow.RecipientName, FormFlow.RecipientDeptNo, FormFlow.RecipientDeptSName, 
 FormFlow.RecipientTeamID, FormFlow.RecipientTeamCName, FormFlow.FlowName, FormFlow.Step, 
 FormFlow.ApproverEmpNo, FormFlow.ApproverName, FormFlow.ApproverDeptNo, FormFlow.ApproverDeptSName, 
 FormFlow.ApproverTeamID, FormFlow.ApproverTeamCName, FormFlow.ApproveTime, FormFlow.ApproveIP,FormFlow.ApproveHost, 
 FormFlow.IsAgentApprove, FormFlow.IsNotification,FormFlow.ApproveComments,
 FormFlow.FlowStatus FROM Form INNER JOIN FormFlow ON Form.FormUID = FormFlow.FormUID
 INNER JOIN C1CARD ON C1CARD.FormUID=Form.FormUID 
 INNER JOIN RESTKIND ON C1CARD.C1_CODE=RESTKIND.R1_NO AND C1CARD.C1_CODE2=RESTKIND.R2_NO
 WHERE (FormFlow.ApproverEmpNo = @EmpNo OR FormFlow.RecipientEmpNo in ({strRoles})) 
 AND (CONVERT(DATE, FormFlow.ApproveTime) >= CONVERT(DATE, @StartDate))
 AND (CONVERT(DATE, FormFlow.ApproveTime) <= CONVERT(DATE, @EndDate))
 ORDER BY Form.ID;";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql, parameters);
            return dt;
        }

        /// <summary>
        /// 取得 已簽核表單，包括本人簽核或代理人簽核，及預設本人角色(非代理)簽核，以申請內容日期為準
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="contentStartDate">申請內容啟始日期</param>
        /// <param name="contentEndDate">申請內容結束日期</param>
        /// <param name="roles">角色</param>
        /// <returns>已簽核表單</returns>
        public DataTable GetSignedFormsByContentDate(string empNo, DateTime startDate, DateTime endDate, DateTime contentStartDate, DateTime contentEndDate, List<Role> roles)
        {
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            parameterEmpNo.Value = empNo;
            parameters.Add(parameterEmpNo);
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);
            SqlParameter parameterContentStartDate = new SqlParameter("@ContentStartDate", SqlDbType.DateTime);
            parameterContentStartDate.Value = contentStartDate;
            parameters.Add(parameterContentStartDate);
            SqlParameter parameterContentEndDate = new SqlParameter("@ContentEndDate", SqlDbType.DateTime);
            parameterContentEndDate.Value = contentEndDate;
            parameters.Add(parameterContentEndDate);
            string strRoles = CardUtility.RolesToQuotedString(roles);

            string strSql = @$"SELECT Form.ID, Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
 ApplicationType = CASE A1_NN WHEN '1' THEN '上旬' WHEN '2' THEN '中旬' WHEN '3' THEN '下旬' END,
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime, 
 A1CARD.A1_HOUR AS TotalHours, 
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost, FormFlow.FlowUID, 
 FormFlow.RecipientEmpNo, FormFlow.RecipientName, FormFlow.RecipientDeptNo, FormFlow.RecipientDeptSName, 
 FormFlow.RecipientTeamID, FormFlow.RecipientTeamCName, FormFlow.FlowName, FormFlow.Step, 
 FormFlow.ApproverEmpNo, FormFlow.ApproverName, FormFlow.ApproverDeptNo, FormFlow.ApproverDeptSName, 
 FormFlow.ApproverTeamID, FormFlow.ApproverTeamCName, FormFlow.ApproveTime, FormFlow.ApproveIP,FormFlow.ApproveHost, 
 FormFlow.IsAgentApprove, FormFlow.IsNotification,FormFlow.ApproveComments,
 FormFlow.FlowStatus FROM Form INNER JOIN FormFlow ON Form.FormUID = FormFlow.FormUID
 INNER JOIN
 A1CARD ON A1CARD.FormUID = Form.FormUID
 WHERE (FormFlow.ApproverEmpNo = @EmpNo OR FormFlow.RecipientEmpNo in ({strRoles}))
 AND (CONVERT(DATE, FormFlow.ApproveTime) >= CONVERT(DATE, @StartDate))
 AND (CONVERT(DATE, FormFlow.ApproveTime) <= CONVERT(DATE, @EndDate))
 AND (CONVERT(DATE, Form.ContentStartTime) <= CONVERT(DATE, @ContentEndDate))
 AND (CONVERT(DATE, Form.ContentEndTime) >= CONVERT(DATE, @ContentStartDate))
UNION ALL
SELECT Form.ID, Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
ApplicationType = CASE B1_Code WHEN '1' THEN '加班' WHEN '2' THEN '社外加班' WHEN '3' THEN '補休假' END,
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime,
 B1CARDAPP.B1_Hour AS TotalHours, 
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost, FormFlow.FlowUID, 
 FormFlow.RecipientEmpNo, FormFlow.RecipientName, FormFlow.RecipientDeptNo, FormFlow.RecipientDeptSName, 
 FormFlow.RecipientTeamID, FormFlow.RecipientTeamCName, FormFlow.FlowName, FormFlow.Step, 
 FormFlow.ApproverEmpNo, FormFlow.ApproverName, FormFlow.ApproverDeptNo, FormFlow.ApproverDeptSName, 
 FormFlow.ApproverTeamID, FormFlow.ApproverTeamCName, FormFlow.ApproveTime, FormFlow.ApproveIP,FormFlow.ApproveHost, 
 FormFlow.IsAgentApprove, FormFlow.IsNotification,FormFlow.ApproveComments,
 FormFlow.FlowStatus FROM Form INNER JOIN FormFlow ON Form.FormUID = FormFlow.FormUID
INNER JOIN B1CARDAPP ON B1CARDAPP.FormUID=Form.FormUID
 WHERE (FormFlow.ApproverEmpNo = @EmpNo OR FormFlow.RecipientEmpNo in ({strRoles})) 
 AND (CONVERT(DATE, FormFlow.ApproveTime) >= CONVERT(DATE, @StartDate))
 AND (CONVERT(DATE, FormFlow.ApproveTime) <= CONVERT(DATE, @EndDate))
UNION ALL
SELECT Form.ID, Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
ApplicationType = CASE B1_CODE WHEN '1' THEN '加班' WHEN '2' THEN '社外加班' WHEN '3' THEN '補休假' END,
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime, 
 B1CARD.B1_Hour AS TotalHours, 
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost, FormFlow.FlowUID, 
 FormFlow.RecipientEmpNo, FormFlow.RecipientName, FormFlow.RecipientDeptNo, FormFlow.RecipientDeptSName, 
 FormFlow.RecipientTeamID, FormFlow.RecipientTeamCName, FormFlow.FlowName, FormFlow.Step, 
 FormFlow.ApproverEmpNo, FormFlow.ApproverName, FormFlow.ApproverDeptNo, FormFlow.ApproverDeptSName, 
 FormFlow.ApproverTeamID, FormFlow.ApproverTeamCName, FormFlow.ApproveTime, FormFlow.ApproveIP,FormFlow.ApproveHost, 
 FormFlow.IsAgentApprove, FormFlow.IsNotification,FormFlow.ApproveComments,
 FormFlow.FlowStatus FROM Form INNER JOIN FormFlow ON Form.FormUID = FormFlow.FormUID
INNER JOIN B1CARD ON B1CARD.FormUID=Form.FormUID
 WHERE (FormFlow.ApproverEmpNo = @EmpNo OR FormFlow.RecipientEmpNo in ({strRoles})) 
 AND (CONVERT(DATE, FormFlow.ApproveTime) >= CONVERT(DATE, @StartDate))
 AND (CONVERT(DATE, FormFlow.ApproveTime) <= CONVERT(DATE, @EndDate))
UNION ALL
SELECT Form.ID, Form.FormUID, Form.FormID, Form.FormNo, Form.FormSubject, Form.FormInfo, Form.EmpNo, 
R1_NAME AS ApplicationType,
 Form.EmpName, Form.DeptNo, Form.DeptSName, Form.TeamID, Form.TeamCName, Form.CreatedEmpNo, 
 Form.CreatedName, Form.FilledTime, Form.CreatedTime, Form.CreatedIP, Form.CreatedHost, Form.AddedSigner, 
 Form.StartTime, Form.EndTime,
 C1CARD.C1_Hour AS TotalHours, 
 Form.FormStatus, Form.TotalSteps, Form.CurrentStep, Form.UpdatedEmpNo, 
 Form.UpdatedName, Form.UpdatedTime, Form.UpdatedIP, Form.UpdatedHost, FormFlow.FlowUID, 
 FormFlow.RecipientEmpNo, FormFlow.RecipientName, FormFlow.RecipientDeptNo, FormFlow.RecipientDeptSName, 
 FormFlow.RecipientTeamID, FormFlow.RecipientTeamCName, FormFlow.FlowName, FormFlow.Step, 
 FormFlow.ApproverEmpNo, FormFlow.ApproverName, FormFlow.ApproverDeptNo, FormFlow.ApproverDeptSName, 
 FormFlow.ApproverTeamID, FormFlow.ApproverTeamCName, FormFlow.ApproveTime, FormFlow.ApproveIP,FormFlow.ApproveHost, 
 FormFlow.IsAgentApprove, FormFlow.IsNotification,FormFlow.ApproveComments,
 FormFlow.FlowStatus FROM Form INNER JOIN FormFlow ON Form.FormUID = FormFlow.FormUID
 INNER JOIN C1CARD ON C1CARD.FormUID=Form.FormUID 
 INNER JOIN RESTKIND ON C1CARD.C1_CODE=RESTKIND.R1_NO AND C1CARD.C1_CODE2=RESTKIND.R2_NO
 WHERE (FormFlow.ApproverEmpNo = @EmpNo OR FormFlow.RecipientEmpNo in ({strRoles})) 
 AND (CONVERT(DATE, FormFlow.ApproveTime) >= CONVERT(DATE, @StartDate))
 AND (CONVERT(DATE, FormFlow.ApproveTime) <= CONVERT(DATE, @EndDate))
 ORDER BY Form.ID;";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql, parameters);
            return dt;
        }

        /// <summary>
        /// 取得使用者前N筆簽核意見
        /// </summary>
        /// <param name="empNo"></param>
        /// <param name="count"></param>
        /// <returns>簽核意見</returns>
        public DataTable GetTopApproveComments(string empNo, int count)
        {
            string sql = @"SELECT DISTINCT TOP(@Number) ApproveComments, COUNT(ApproveComments) AS frequency, MAX(ApproveTime) AS ApproveTime
                           FROM FormFlow
                           WHERE ApproverEmpNo=@EmpNo AND ApproveComments <> '' AND ApproveComments is NOT NULL
                           GROUP BY ApproveComments
                           ORDER BY frequency DESC, ApproveTime DESC;";
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterNumber = new SqlParameter("@Number", SqlDbType.Int);
            parameterNumber.Value = count;
            parameters.Add(parameterNumber);
            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar, 5)
            {
                Value = empNo
            };
            parameters.Add(parameterEmpNo);
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            return dt;
        }

        /// <summary>
        /// 取得 transaction.
        /// </summary>
        /// <param name="connection">out connection</param>
        /// <returns>transaction</returns>
        public SqlTransaction GetTransaction(out SqlConnection connection)
        {
            connection = new SqlConnection(_connectionString);
            connection.Open();
            SqlTransaction transaction = connection.BeginTransaction();
            return transaction;
        }

        /// <summary>
        /// 取得有效的加班卡 B1_Status in (1, 2)
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public DataTable GetValidB1Card(DateTime date, string empNo)
        {
            string sql = "SELECT * FROM B1CARD WHERE B1_YYMM=@B1_YYMM AND B1_SDD=@B1_DD AND B1_EmpNo=@EmpNo AND B1_Status in (1, 2);";
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parametersB1_YYMM = new SqlParameter("@B1_YYMM", SqlDbType.Char, 5);
            parametersB1_YYMM.Value = CardUtility.RocChineseYYYMM(date);
            parameters.Add(parametersB1_YYMM);
            SqlParameter parametersB1_DD = new SqlParameter("@B1_DD", SqlDbType.Char, 2);
            parametersB1_DD.Value = date.ToString("dd");
            parameters.Add(parametersB1_DD);
            SqlParameter parametersEmpNo = new SqlParameter("@EmpNO", SqlDbType.VarChar);
            parametersEmpNo.Value = empNo;
            parameters.Add(parametersEmpNo);
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            return dt;
        }

        /// <summary>
        /// 取得有效的加班申請單，也就是 B1_Status 為1或2
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public DataTable GetValidB1CardApp(DateTime date, string empNo)
        {
            string sql = "SELECT * FROM B1CARDAPP WHERE B1_Date=@Date AND B1_EmpNo=@EmpNo AND B1_Status in (1,2);";
            SqlParameter[] parameters = new SqlParameter[2];
            parameters[0] = new SqlParameter("@Date", SqlDbType.SmallDateTime);
            parameters[0].Value = new DateTime(date.Year, date.Month, date.Day);
            parameters[1] = new SqlParameter("@EmpNO", SqlDbType.VarChar);
            parameters[1].Value = empNo;
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            return dt;
        }

        /// <summary>
        /// 依照資料表，依照整數的加班時數轉換為浮點數的加班(費)倍率時數
        /// </summary>
        /// <param name="hours">The hours.</param>
        /// <param name="dt">資料表</param>
        /// <returns>加班(費)倍率時數</returns>
        public static double GetWeightedHoursFromDataTable(double hours, DataTable dt)
        {
            foreach (DataRow dr in dt.Rows)
            {
                hours = GetWeightedHoursFromDataRow(hours, dr);
            }
            return hours;
        }

        /// <summary>
        /// 取得本月份的全年病假可休總時數
        /// </summary>
        /// <param name="date"></param>
        /// <param name="empNo"></param>
        /// <returns></returns>
        public int GetYearSickLeaveHours(DateTime date, string empNo)
        {
            int hours = 0;
            string thisMonth = (1911 - date.Year).ToString() + date.ToString("MM");
            string sql = @"SELECT R_SICK,R_USESICK2 FROM REST WHERE (R_EMPNO = @EmpNo) AND (R_YYMM = @ThisMonth);";
            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            parameterEmpNo.Value = empNo;
            SqlParameter parameterThisMonth = new SqlParameter("@ThisMonth", SqlDbType.VarChar, 5);
            parameterThisMonth.Value = thisMonth;
            List<SqlParameter> parameters = new List<SqlParameter>
            {
                parameterEmpNo,
                parameterThisMonth
            };
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            if (dt != null && dt.Rows.Count > 0)
            {
                object obj = dt.Rows[0]["hours"];
                if (obj != null && obj != DBNull.Value)
                {
                    hours = (int)obj;
                }
            }
            return hours;
        }

        ///// <summary>
        ///// 是否為管理員
        ///// </summary>
        ///// <param name="empNo">員工編號</param>
        ///// <returns></returns>
        //public bool IsAdmin(string empNo)
        //{
        //    bool isAdmin = false;
        //    string sqlstr = "SELECT EmpNo FROM dbo.Manager WHERE EmpNo=@EmpNo;";
        //    SqlParameter parameter = new SqlParameter("@EmpNo", SqlDbType.VarChar);
        //    parameter.Value = empNo;
        //    DataTable dt = SqlHelper.GetDataTable(_connectionString, sqlstr, parameter);
        //    if (dt.Rows.Count == 1)
        //    {
        //        isAdmin = true;
        //    }
        //    return isAdmin;
        //}

        /// <summary>檢查是否有指定表單編號和通知編號的通知</summary>
        /// <param name="formUID">The form guid.</param>
        /// <param name="notifyId">The notify id.</param>
        /// <returns>是否有指定的通知</returns>
        public bool IsNotificationExist(Guid formUID, int notifyId)
        {
            bool ret = false;

            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterID = new SqlParameter("@ID", SqlDbType.Int);
            parameterID.Value = notifyId;
            parameters.Add(parameterID);

            SqlParameter parameterFormUID = new SqlParameter("@FormUID", SqlDbType.UniqueIdentifier);
            parameterFormUID.Value = formUID;
            parameters.Add(parameterFormUID);

            string sql = @"SELECT 1 FROM Notification WHERE (ID=@ID AND FormUID=@FormUID)";

            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            if (dt != null && dt.Rows.Count == 1)
            {
                ret = true;
            }
            return ret;
        }

        /// <summary>檢查是否有指定表單編號、通知編號和員工編號的通知</summary>
        /// <param name="roles">角色list</param>
        /// <param name="formUID">The form guid.</param>
        /// <param name="notifyId">The notify id.</param>
        /// <returns>是否有指定的通知</returns>
        public bool IsNotificationExist(List<Role> roles, Guid formUID, int notifyId)
        {
            bool ret = false;

            string strRoles = CardUtility.RolesToQuotedString(roles);

            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterID = new SqlParameter("@ID", SqlDbType.Int);
            parameterID.Value = notifyId;
            parameters.Add(parameterID);

            SqlParameter parameterFormUID = new SqlParameter("@FormUID", SqlDbType.UniqueIdentifier);
            parameterFormUID.Value = formUID;
            parameters.Add(parameterFormUID);

            string sql = @$"SELECT 1 FROM Notification WHERE (ID=@ID AND FormUID=@FormUID AND NotifyEmpNo in ({strRoles}))";

            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            if (dt != null && dt.Rows.Count == 1)
            {
                ret = true;
            }
            return ret;
        }
        /// <summary>
        /// 判斷是不是可加班特殊員工
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="date">加班日</param>
        /// <returns>若為特殊員工，傳回true 以及特殊員工每月可加班總時數</returns>
        public (bool, double) IsSpecialStaff(string employeeNumber, DateTime date)
        {
            bool ret = false;
            double hoursLimit = 0.0;
            string sqlstr = @"SELECT * FROM SpecialOvertimeHoursLimit 
                WHERE EmpNO=@EmpNo and StartDate <= @Date 
                AND EndDate >= @Date AND IsActive = 1;";
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameter = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            parameter.Value = employeeNumber;
            parameters.Add(parameter);
            SqlParameter parameterDate = new SqlParameter("@Date", SqlDbType.DateTime);
            parameterDate.Value = date;
            parameters.Add(parameterDate);
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sqlstr, parameters);
            if (dt != null && dt.Rows.Count == 1)
            {
                hoursLimit = Decimal.ToDouble((decimal)dt.Rows[0]["B1HoursLimit"]);
                ret = true;
            }
            return (ret, hoursLimit);
        }

        /// <summary>
        /// 標註某員工及其對應之角色的所有未讀通知為已讀
        /// </summary>
        /// <param name="roles">員工角色</param>
        /// <returns></returns>
        public bool MarkDeliveredNotifications(List<Role> roles)
        {
            bool ret = true;

            string strSqlRoleId = string.Join(",", roles.Select(item => $"'{item.RoleId}'"));
            string strSql = @$"UPDATE Notification SET ViewTime = @ViewTime WHERE (TRIM(Notification.NotifyEmpNo) IN ({strSqlRoleId}) AND ViewTime IS NULL)";

            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterViewTime = new SqlParameter("@ViewTime", SqlDbType.DateTime);
            parameterViewTime.Value = DateTime.Now.ToLocalTime();
            parameters.Add(parameterViewTime);

            try
            {
                int rows = SqlHelper.ExecuteSqlNonQuery(_connectionString, strSql, parameters);
                if (rows < 1)
                {
                    ret = false;
                }
            }
            catch (SqlException ex)
            {
                Console.WriteLine($"{ex.Message} {ex.StackTrace}");
                ret = false;
            }
            return ret;
        }

        /// <summary>
        /// 更新表單
        /// </summary>
        /// <param name="formID">表單名稱</param>
        /// <param name="dataSet">包含所有待更新資料表的 Data Set</param>
        /// <returns>成功為空值，失敗時為錯誤訊息 </returns>
        public string UpdateForm(string formID, DataSet dataSet)
        {
            string ret = string.Empty;
            string storedProcedure = "sp_FormAction";
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterFormID = new SqlParameter("@FormID", SqlDbType.VarChar, 10)
            {
                Value = formID
            };
            parameters.Add(parameterFormID);
            foreach (DataTable dataTable in dataSet.Tables)
            {
                string parameterName = $"@{dataTable.TableName}";
                SqlParameter parameter = new SqlParameter(parameterName, SqlDbType.Structured);
                parameter.Value = dataTable;
                parameters.Add(parameter);
            }

            SqlParameter parameterFormUpdatedRowCount = new SqlParameter("@FormUpdatedRowCount", SqlDbType.Int);
            parameterFormUpdatedRowCount.Direction = ParameterDirection.Output;
            parameters.Add(parameterFormUpdatedRowCount);

            SqlParameter parameterFormFlowUpdatedRowCount = new SqlParameter("@FormFlowUpdatedRowCount", SqlDbType.Int);
            parameterFormFlowUpdatedRowCount.Direction = ParameterDirection.Output;
            parameters.Add(parameterFormFlowUpdatedRowCount);

            SqlParameter parameterNotificationInsertedRowCount = new SqlParameter("@NotificationInsertedRowCount", SqlDbType.Int);
            parameterNotificationInsertedRowCount.Direction = ParameterDirection.Output;
            parameters.Add(parameterNotificationInsertedRowCount);

            SqlParameter parameterCardUpdatedRowCount = new SqlParameter("@CardUpdatedRowCount", SqlDbType.Int);
            parameterCardUpdatedRowCount.Direction = ParameterDirection.Output;
            parameters.Add(parameterCardUpdatedRowCount);
            //try
            //{
            SqlHelper.ExecuteStoredProcedure(_connectionString, storedProcedure, parameters);
            //}
            //catch (Exception ex)
            //{
            //    Console.WriteLine($"{ex.Message} {ex.StackTrace}");
            //    ret = ex.Message;
            //    throw;
            //}
            return ret;
        }

    }
}
