using FakeItEasy;
using Xunit;
using Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.PositionType;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Diagnostics.CodeAnalysis;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.Tests
{
    [ExcludeFromCodeCoverage]
    public class TestB1CardCheckIsOvertimeAllowed : TestB1CardBase
    {
        // 檢查是否正確設定可加班屬性
        [Theory]
        [InlineData(B1CardPositionEnum.Chairman, false)]
        [InlineData(B1CardPositionEnum.President, false)]
        [InlineData(B1CardPositionEnum.VicePresident, false)]
        [InlineData(B1CardPositionEnum.Manager, false)]
        [InlineData(B1CardPositionEnum.DeputyManager, false)]
        [InlineData(B1CardPositionEnum.Director, false)]
        [InlineData(B1CardPositionEnum.DeputyDirector, false)]
        [InlineData(B1CardPositionEnum.ProjectDirector, false)]
        [InlineData(B1CardPositionEnum.ProjectDeputyDirector, false)]
        [InlineData(B1CardPositionEnum.Driver, true)]
        [InlineData(B1CardPositionEnum.ChiefAccountant, true)]
        [InlineData(B1CardPositionEnum.SectionChief, true)]
        [InlineData(B1CardPositionEnum.EnvLabChief, true)]
        [InlineData(B1CardPositionEnum.GeneralStaff, true)]
        public void Test_CheckIsOvertimeAllowed(
            B1CardPositionEnum positionToBeChecked, bool canWorkOvertime)
        {
            var provider = A.Fake<IB1CardDataProvider>();
            A.CallTo(() => provider.EmployeeNumber).Returns(_employeeNumber);
            A.CallTo(() => provider.OvertimeDate).Returns(_now);
            A.CallTo(() => provider.GetPositionType())
                .Returns(positionToBeChecked);

            var position = B1CardPositionFactory.GetB1CardPositionObject(provider);

            Assert.Equal(position.IsOvertimeAllowed, canWorkOvertime);
        }
        // 檢查是否正確設定可加班屬性，例外狀況
        [Fact]
        public void Test_CheckNotInTheListIsOvertimeAllowed()
        {
            var provider = A.Fake<IB1CardDataProvider>();
            A.CallTo(() => provider.EmployeeNumber).Returns(_employeeNumber);
            A.CallTo(() => provider.OvertimeDate).Returns(_now);
            A.CallTo(() => provider.GetPositionType())
                .Returns(B1CardPositionEnum.NotInList);

            Assert.Throws<ArgumentException>(
                () => B1CardPositionFactory.GetB1CardPositionObject(provider));
        }
    }
}