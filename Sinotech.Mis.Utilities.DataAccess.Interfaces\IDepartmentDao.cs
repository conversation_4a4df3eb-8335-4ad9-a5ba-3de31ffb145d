﻿using System.Data;

namespace Sinotech.Mis.Utilities.DataAccess.Interfaces
{
    /// <summary>
    ///  部門資料存取介面
    /// </summary>
    public interface IDepartmentDao
    {

        /// <summary>
        /// 取得副理以上資料表
        /// </summary>
        /// <returns></returns>
        public DataTable GetAboveDeputyManagerDataTable();

        /// <summary>
        /// 取得部門內所有員工
        /// </summary>
        /// <param name="departmentNumber">部門編號</param>
        /// <returns>部門內所有員工</returns>
        public DataTable GetDepartmentEmployees(int departmentNumber);

        /// <summary>
        /// 取得部門名稱
        /// </summary>
        /// <param name="departmentNumber">部門編號</param>
        /// <returns>部門名稱</returns>
        public string GetDepartmentName(int departmentNumber);
        /// <summary>
        /// 取得所有部門
        /// </summary>
        /// <returns>所有部門</returns>
        public DataTable GetDepartments();

        /// <summary>
        /// 取得部門簡稱
        /// </summary>
        /// <param name="departmentNumber">部門編號</param>
        /// <returns>部門簡稱</returns>
        public string GetDepartmentShortName(int departmentNumber);

        /// <summary>
        /// 取得部門主管
        /// </summary>
        /// <param name="departmentNumber">部門編號</param>
        /// <returns>部門主管</returns>
        public DataTable GetDepartmentsManager(int departmentNumber);

        /// <summary>
        /// 取得各部門各級主管
        /// </summary>
        /// <returns>各部門各級主管</returns>
        public DataTable GetDepartmentsManagers();

        /// <summary>
        /// 取得部門各級主管
        /// </summary>
        /// <param name="departmentNumber">部門編號</param>
        /// <returns>部門各級主管</returns>
        public DataTable GetDepartmentsManagers(int departmentNumber);

        /// <summary>
        /// 取得行政部門
        /// </summary>
        /// <returns></returns>
        public DataTable GetAdministrativedepartments();

        /// <summary>
        /// 取得研究中心
        /// </summary>
        /// <returns></returns>
        public DataTable GetResearchCenters();


        /// <summary>取得部門內所有組及員工</summary>
        /// <returns></returns>
        public DataTable GetDepartmentsTeamsEmployees();

        /// <summary>取得部門內所有組</summary>
        /// <param name="departmentNumber">The department number.</param>
        /// <returns>組的DataTable</returns>
        public DataTable GetDepartmentTeamsEmployees(int departmentNumber);

        /// <summary>
        /// 取得所有現役組長
        /// </summary>
        /// <returns></returns>
        public DataTable GetTeamLeaders();

    }
}
