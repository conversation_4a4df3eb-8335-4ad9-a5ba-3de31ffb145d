﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.Negotiate;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Server.IISIntegration;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using Serilog.Context;
using Sinotech.Mis.HR.Attendance.Extensions.ServiceCollection;
using Sinotech.Mis.Serilog.Utilities;
using Sinotech.Mis.Serilog.Utilities.Filters;
using Sinotech.Mis.Serilog.Utilities.Middlewares;
using System;
using System.Diagnostics.CodeAnalysis;
using System.Security.Authentication;
using System.Threading.Tasks;

namespace Sinotech.Mis.HR.Attendance.AttendanceCard
{
    [ExcludeFromCodeCoverage]
    public static class Program
    {
        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureLogging((hostingContext, config) =>
                {
                    config.ClearProviders();
                })
                .UseSerilog()
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    //若要避免使用 TLS 1.0/1.1 與 SSL 3.0/3.1 等
                    webBuilder.UseKestrel(kestrelOptions =>
                    {
                        kestrelOptions.ConfigureHttpsDefaults(httpsOptions =>
                        {
                            httpsOptions.SslProtocols = SslProtocols.Tls13;
                        });
                    });
                });

        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(new WebApplicationOptions
            {
                Args = args,
                WebRootPath = "ClientApp/dist" // 設定 WebRootPath
            });
            IConfiguration Configuration = builder.Configuration;
            builder.Host.UseSerilog((context, configuration) =>
                configuration.ReadFrom.Configuration(context.Configuration));
            IHost host = CreateHostBuilder(args).Build();

            SerilogHelpers.CreateSeriLog(host);
            // 使用 Serilog 進行記錄
            builder.Host.UseSerilog();

            // Add services to the container.
            //// By 林志偉，2022.07.05, for Serilog
            builder.Services.AddLogging(loggingBuilder => loggingBuilder.AddSerilog());
            builder.Services.AddHttpContextAccessor();

            // By 林志偉，2022.07.05, for Serilog
            builder.Services.AddControllers(options =>
                {
                    options.Filters.Add<SerilogAsyncExceptionFilter>();
                }
            );

            builder.Services.AddAttendanceBusinessObjects(Configuration);  // By 林志偉，2022.05.25

            // 是否使用整合認證
            bool useNegotiate = Configuration.GetValue<bool>("UseNegotiate");
            // 使用整合認證時必須設定 IIS 與 Negotiate 認證
            if (useNegotiate)
            {
                builder.Services.AddAuthentication(IISDefaults.AuthenticationScheme);
                builder.Services.AddAuthentication(NegotiateDefaults.AuthenticationScheme).AddNegotiate();
            }
            // 從附檔名取得 ContentType
            builder.Services.AddSingleton<FileExtensionContentTypeProvider>();

            // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
            builder.Services.AddEndpointsApiExplorer();

            builder.Services.AddSwaggerGen();

            builder.Services.AddAuthorization(options =>
            {
                if (useNegotiate)
                {
                    // By default, all incoming requests will be authorized according to the default policy.
                    options.FallbackPolicy = options.DefaultPolicy;
                }
            });

            if (!useNegotiate)
            {
                // 處於非網域環境時使用 Cookie 認證
                builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
                    .AddCookie(options =>
                    {
                        options.Events.OnRedirectToLogin = context =>
                        {
                            if (context.Request.Path.StartsWithSegments("/api"))
                            {
                                context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                            }
                            else
                            {
                                context.RedirectUri = "/"; // 前端的登入頁面
                                context.Response.Redirect(context.RedirectUri);
                            }
                            return Task.CompletedTask;
                        };
                    });
            }

            // In production, the Vite files will be served from this directory
            builder.Services.AddSpaStaticFiles(configuration =>
            {
                configuration.RootPath = "ClientApp/dist";
            });

            var app = builder.Build();

            // Configure the HTTP request pipeline.
            if (app.Environment.IsDevelopment())
            {
                // 在開發環境中，使用 Swagger 生成 API 文檔
                app.UseSwagger();
                app.UseSwaggerUI();
                // 在開發環境中發生未捕捉的錯誤時，系統錯誤處理
                app.UseExceptionHandler("/api/Error");
                // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
            }
            else
            {
                // API發生未捕捉的錯誤時，系統錯誤處理
                app.UseExceptionHandler("/api/Error");
                // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
            }
            app.UseHsts();
            app.UseHttpsRedirection();
            app.UseStaticFiles();
            app.UseSpaStaticFiles();
            app.UseRouting();
            if (useNegotiate)
            {
                app.UseAuthentication();
            }
            app.UseAuthorization();

            // By 林志偉，2022.07.05, for Serilog
            app.UseMiddleware<SerilogRequestLogger>();
            app.UseMiddleware<SerilogExceptionMiddleware>();
            app.UseSerilogRequestLogging();

#pragma warning disable ASP0014
            app.UseEndpoints(endpoints => endpoints.MapControllers());
#pragma warning restore ASP0014

            app.UseSpa(spa =>
            {
                if (app.Environment.IsDevelopment())
                    spa.UseViteDevelopmentServer(sourcePath: "ClientApp");
            });

            try
            {
                LogContext.PushProperty("SourceContext", nameof(Program));
                Log.Information("AttendanceCard Starting up");
                app.Run();
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "AttendanceCard Application start-up failed");
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }
    }
}
