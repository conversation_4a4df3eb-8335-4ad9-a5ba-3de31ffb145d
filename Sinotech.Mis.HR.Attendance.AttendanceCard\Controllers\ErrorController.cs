﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers
{
    /// <summary>
    /// 錯誤處理控制器，負責處理應用程式中的例外狀況。
    /// 此控制器已套用 AllowAnonymous 特性，允許未經身份驗證的使用者存取錯誤頁面。
    /// </summary>
    [AllowAnonymous]
    [Route("/api/[controller]")]
    public class ErrorController : Controller
    {
        private readonly ILogger<ErrorController> _logger;

        /// <summary>
        /// 初始化 <see cref="ErrorController"/> 類別的新執行個體。
        /// </summary>
        /// <param name="logger">用於記錄例外狀況與錯誤訊息的記錄器介面。</param>
        public ErrorController(ILogger<ErrorController> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 處理 API 呼叫時發生的例外狀況的端點。
        /// 此方法會記錄例外狀況並回傳適當的 JSON 回應。
        /// </summary>
        /// <returns>包含錯誤訊息的 500 狀態碼 JSON 回應。</returns>
        [HttpGet]
        [AllowAnonymous]
        [Route("/api/Error")]
        public IActionResult ApiError()
        {
            var exceptionHandlerPathFeature = HttpContext.Features.Get<IExceptionHandlerPathFeature>();
            var exception = exceptionHandlerPathFeature?.Error;

            if (exception != null)
            {
                _logger.LogError(exception, "API 例外狀況處理: {Message} {Stack}", exception.Message, exception.StackTrace);
            }

            // API 錯誤傳回適當的 JSON 回應
            return StatusCode(500, new { error = "系統發生錯誤，請通知系統管理者" });
        }
    }
}