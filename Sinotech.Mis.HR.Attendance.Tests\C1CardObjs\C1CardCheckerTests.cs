﻿using FakeItEasy;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Tests
{
    [ExcludeFromCodeCoverage]
    public class C1CardCheckerTests
    {

        private IC1CardBo _c1CardBo;

        public C1CardCheckerTests(IC1CardBo c1CardBo)
        {
            _c1CardBo = c1CardBo;
        }

        //[Fact]
        public void C1CardChecker_ShouldAssertException_Test()
        {
            C1Card c1Card = new C1Card();
            c1Card.LeaveNumber = (LeaveKindEnum)54;
            Assert.Throws<LeaveNotFoundException>(() => new C1CardChecker(c1Card, _c1CardBo));
        }

        [Fact]
        public void C1CardChecker_Exception_Test()
        {
            var c1Card = new C1Card();
            c1Card.LeaveNumber = (LeaveKindEnum) (-100);
            var c1CardBo = A.Fake<IC1CardBo>();
            Assert.Throws<LeaveNotFoundException>(() => new C1CardChecker(c1Card, c1CardBo));
        }
    }
}