﻿using Sinotech.Mis.HR.Attendance.Common;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.Tests
{
    [ExcludeFromCodeCoverage]
    public class CardCheckResultTests
    {
        [Fact]
        public void Constructor_ShouldInitializeProperties()
        {
            // Arrange
            int code = 1;
            CardStatusEnum status = CardStatusEnum.Success;
            string message = "Test message";

            // Act
            var result = new CardCheckResult(code, status, message);

            // Assert
            Assert.Equal(code, result.Code);
            Assert.Equal(status, result.Status);
            Assert.Equal(message, result.Message);
        }

        [Fact]
        public void Equals_ShouldReturnTrueForEqualObjects()
        {
            // Arrange
            var result1 = new CardCheckResult(1, CardStatusEnum.Success, "Test message");
            var result2 = new CardCheckResult(1, CardStatusEnum.Success, "Test message");

            // Act
            bool isEqual = result1.Equals(result2);

            // Assert
            Assert.True(isEqual);
        }

        [Fact]
        public void Equals_ShouldReturnFalseForDifferentObjects()
        {
            // Arrange
            var result1 = new CardCheckResult(1, CardStatusEnum.Success, "Test message");
            var result2 = new CardCheckResult(2, CardStatusEnum.Error, "Different message");

            // Act
            bool isEqual = result1.Equals(result2);

            // Assert
            Assert.False(isEqual);
        }

        [Fact]
        public void GetHashCode_ShouldReturnCode()
        {
            // Arrange
            int code = 1;
            var result = new CardCheckResult(code, CardStatusEnum.Success, "Test message");

            // Act
            int hashCode = result.GetHashCode();

            // Assert
            Assert.Equal(code, hashCode);
        }
    }
}