import { ref } from 'vue'
import { GET_MONTHEMPLOYEELEAVES_URL } from '../api/appUrl'
import type { EmployeeLeavesType } from '../api/appType'

const emptyLeaves: EmployeeLeavesType = {
  remainAnnualLeaves: 0,
  remainReservedLeaves: 0,
  remainReservedLeavesLastMonth: 0,
  newReservedLeavesMonth: 0,
  usedAnnualLeavesMonth: 0,
  totalAnnualLeaves: 0,
  usedAnnualLeaves: 0,
  usedPersonalLeaves: 0,
  usedPersonalLeavesMonth: 0,
  usedSickLeaves: 0,
  usedSickLeavesMonth: 0,
  displayExtendedLeaves: true,
  extendedLeaves: 0,
  usedExtendedLeavesMonth: 0,
  usedExtendedLeaves: 0,
  remainedExtendedLeaves: 0,
  extendedLeavesIsImported: true,
  usedMenstruationLeaves: 0,
  usedMenstruationLeavesMonth: 0,
  gender: '',
  newRemainCompensatoryLeavesMonth: 0,
  usedCompensatoryLeavesMonth: 0,
  totalNewRemainCompensatoryLeaves: 0,
  totalUsedCompensatoryLeaves: 0,
  totalCompensatoryLeaves: 0
}

/**
 * 指定月份的年度休假統計時數資料
 * @returns 
 */
export function useLeaveData() {
  const leaveDateStatic = ref<Date>(new Date())
  const leaves = ref<EmployeeLeavesType>(emptyLeaves)

  const onGetLeaves = async (date: Date, userId: string, signal: AbortSignal): Promise<void> => {
    leaveDateStatic.value = new Date()

    const params = new URLSearchParams({
      date: date.toISOString(),
      empNo: userId
    })
    try {
      const res: Response = await fetch(GET_MONTHEMPLOYEELEAVES_URL + '?' + params, {
        method: 'GET',
        signal: signal
      })
      if (!res.ok) {
        throw new Error(res.status.toString())
      }
      const jsonData = await res.json()
      leaves.value = {
        remainAnnualLeaves: jsonData.RemainAnnualLeaves,
        remainReservedLeaves: jsonData.RemainReservedLeaves,
        remainReservedLeavesLastMonth: jsonData.RemainReservedLeavesLastMonth,
        newReservedLeavesMonth: jsonData.NewReservedLeavesMonth,
        usedAnnualLeavesMonth: jsonData.UsedAnnualLeavesMonth,
        totalAnnualLeaves: jsonData.TotalAnnualLeaves,
        usedAnnualLeaves: jsonData.UsedAnnualLeaves,
        usedPersonalLeaves: jsonData.UsedPersonalLeaves,
        usedPersonalLeavesMonth: jsonData.UsedPersonalLeavesMonth,
        usedSickLeaves: jsonData.UsedSickLeaves,
        usedSickLeavesMonth: jsonData.UsedSickLeavesMonth,
        displayExtendedLeaves: jsonData.DisplayExtendedLeaves,
        extendedLeaves: jsonData.ExtendedLeaves,
        usedExtendedLeavesMonth: jsonData.UsedExtendedLeavesMonth,
        usedExtendedLeaves: jsonData.UsedExtendedLeaves,
        remainedExtendedLeaves: jsonData.RemainedExtendedLeaves,
        extendedLeavesIsImported: jsonData.ExtendedLeavesIsImported,
        usedMenstruationLeaves: jsonData.UsedMenstruationLeaves,
        usedMenstruationLeavesMonth: jsonData.UsedMenstruationLeavesMonth,
        gender: jsonData.Gender,
        newRemainCompensatoryLeavesMonth: jsonData.NewRemainCompensatoryLeavesMonth,
        usedCompensatoryLeavesMonth: jsonData.UsedCompensatoryLeavesMonth,
        totalNewRemainCompensatoryLeaves: jsonData.TotalNewRemainCompensatoryLeaves,
        totalUsedCompensatoryLeaves: jsonData.TotalUsedCompensatoryLeaves,
        totalCompensatoryLeaves: jsonData.TotalCompensatoryLeaves
      }
    } catch (err: unknown) {
      throw err
    }
  }

  const onSetLeaves = (leaveData: EmployeeLeavesType | null): void => {
    if (leaveData === null) {
      leaves.value = emptyLeaves
    } else {
      leaves.value = leaveData
    }
  }

  return { leaveDateStatic, leaves, onGetLeaves, onSetLeaves }
}