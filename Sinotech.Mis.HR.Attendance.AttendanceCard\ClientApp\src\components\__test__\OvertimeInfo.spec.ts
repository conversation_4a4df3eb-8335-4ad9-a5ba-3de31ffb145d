import { mount } from '@vue/test-utils'
import OvertimeInfo from '../OvertimeInfo.vue'

describe('OvertimeInfo', () => {
  it('check context', () => {
    const wrapper = mount(OvertimeInfo, {
      props: {
        modelValue:{
          card: '',
          overtimeDateStatic: new Date(),
          overtimeData: null,
          currentInfoNote: false
        }
      }
    })
    expect(wrapper.html()).toContain('加班時數統計')
  })
})