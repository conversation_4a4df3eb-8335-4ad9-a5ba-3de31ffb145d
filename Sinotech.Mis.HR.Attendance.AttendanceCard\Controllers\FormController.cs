﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.Utilities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

#nullable enable
namespace Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers
{
    /// <summary>
    /// 表單API
    /// </summary>
    [Authorize]
    [ApiController]
    [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
    [Route("/api/[controller]/[action]")]
    public class FormController : ControllerBase
    {
        private readonly bool _useNegotiate = true;
        private readonly IAttendanceBo _attendanceBo;
        private readonly ICardBoFactory _cardBoFactory;
        private readonly FileExtensionContentTypeProvider _fileExtensionContentTypeProvider;
        private readonly IFormBo _formBo;
        private readonly ILogger<FormController> _logger;

        /// <summary>
        ///   <see cref="FormController" /> 的建構函式</summary>
        /// <param name="attendanceBo">出勤商業物件</param>
        /// <param name="formBo">The form 商業物件.</param>
        /// <param name="cardBoFactory">CardBo Factory</param>
        /// <param name="configuration"></param>
        /// <param name="logger">記錄者</param>
        /// <param name="fileExtensionContentTypeProvider">副檔名檔案類型提供者</param>
        public FormController(IAttendanceBo attendanceBo, IFormBo formBo, ICardBoFactory cardBoFactory,
            IConfiguration configuration, ILogger<FormController> logger,
            FileExtensionContentTypeProvider fileExtensionContentTypeProvider)
        {
            _attendanceBo = attendanceBo;
            _formBo = formBo;
            _useNegotiate = configuration.GetValue<bool>("UseNegotiate");
            _logger = logger;
            _cardBoFactory = cardBoFactory;
            _fileExtensionContentTypeProvider = fileExtensionContentTypeProvider;
        }

        /// <summary>
        /// 單筆簽核
        /// </summary>
        /// <param name="approveDto">簽核物件</param>
        /// <returns>簽核成功傳回空值，失敗則傳回原因</returns>
        [Authorize]
        [HttpPost]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/")]
        public string Approve(Approve approveDto)
        {
            string errorMessage = string.Empty;
            string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            if (!string.IsNullOrWhiteSpace(userId))
            {
                ICardBaseBo? cardBo = _cardBoFactory.GetCardBo(approveDto.FormID);
                if (cardBo == null)
                {
                    throw new ArgumentOutOfRangeException(nameof(approveDto), "建立 CardBo 失敗");
                }

                var (ip, hostname) = CardUtility.GetIP_Hostname(Request.HttpContext);
                approveDto.IsAgentApprove = false;
                approveDto.IsNotification = false;
                System.Net.IPAddress? ipAddress = ip;
                approveDto.ApproveIP = ipAddress?.ToString();
                approveDto.ApproveHost = hostname;
                approveDto.ApproveTime = approveDto.ApproveTime.ToLocalTime();
                errorMessage = _formBo.Approve(approveDto, cardBo, userId);
                string opinion = "";
                switch (approveDto.FlowStatus)
                {
                    case 2:
                        opinion = "同意";
                        break;
                    case 3:
                        opinion = "不同意";
                        break;
                }

                _logger.LogInformation($"{approveDto.ApproveTime.ToString("yyyy/MM/dd HH:mm:ss")} {approveDto.ApproverEmpNo} {approveDto.ApproverName} 簽核 {approveDto.FormID} {approveDto.FormUID} {opinion} result {errorMessage}");
            }
            return errorMessage;
        }

        /// <summary>
        /// 結案抽單
        /// </summary>
        /// <param name="withdraw">抽單物件</param>
        /// <returns>成功傳回空值，失敗傳回錯誤訊息</returns>
        [Authorize]
        [HttpDelete]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/")]
        public string ClosedWithdraw(Withdraw withdraw)
        {
            withdraw.WithdrawTime = withdraw.WithdrawTime.ToLocalTime();
            if (Request != null && Request.HttpContext != null)
            {
                var (ip, hostname) = CardUtility.GetIP_Hostname(Request.HttpContext);
                System.Net.IPAddress? ipAddress = ip;
                withdraw.WithdrawIP = ipAddress?.ToString();
                withdraw.WithdrawHost = hostname;
            }

            string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            if (!string.IsNullOrWhiteSpace(userId))
            {
                withdraw.WithdrawEmpNo = userId;
            }
            string errorMessage = string.Empty;
            Form form = _formBo.GetForm(withdraw.FormUID);
            ICardBaseBo? cardBo = _cardBoFactory.GetCardBo(withdraw.FormID);
            if (cardBo != null)
            {
                CardBase? card = cardBo.GetCard(withdraw.FormUID);
                if (card != null)
                {
                    errorMessage = _formBo.ClosedWithdraw(form, cardBo, card, withdraw);
                }
            }
            return errorMessage;
        }

        /// <summary>
        /// 紀錄已讀通知
        /// </summary>
        /// <param name="request"></param>
        /// <returns>執行結果的JSON訊息</returns>
        [Authorize]
        [HttpPost]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [ProducesResponseType<bool>(StatusCodes.Status200OK)]
        [Route("/api/[controller]/[action]/")]
        public IActionResult DeliveredNotifications(DeliveredNotificationsRequest request)
        {
            string logonUserId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            // 限本人讀取才視為已讀通知
            bool result = _formBo.DeliveredNotifications(logonUserId, request.Id);
            return Ok(new { result });
        }

        /// <summary>
        /// 下載附件
        /// </summary>
        /// <param name="formUID">表單 Guid</param>
        /// <param name="id">附件 id</param>
        /// <returns>附件</returns>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/{formUID}/{id}")]
        public FileContentResult DownloadAttachment(Guid formUID, int id)
        {
#nullable enable
            const string DefaultContentType = "application/octet-stream";
            FormAttachment? attachment = _formBo.GetAttachment(formUID, id);
            byte[] fileContent;
            const string notFound = "找不到檔案";
            if (attachment == null)
            {
                attachment = new FormAttachment();
                attachment.EncodedFileName = "找不到檔案.txt";
                attachment.OriginalFileName = attachment.EncodedFileName;
                fileContent = Encoding.UTF8.GetBytes(notFound);
            }
            else
            {
                fileContent = _formBo.GetRemoteFile(attachment);
            }

            if (!_fileExtensionContentTypeProvider.TryGetContentType(attachment.EncodedFileName, out var contentType))
            {
                contentType = DefaultContentType;
            }

            var file = File(fileContent, contentType, attachment.OriginalFileName);
            return file;
        }

        /// <summary>取得特定待簽核表單及卡</summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="formUID">The form uid.</param>
        /// <returns>表單及卡 JSON</returns>
        [Authorize]
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/")]
        public string GetApprovalFormCard(Guid formUID, string empNo)
        {
            string ret = "[]";
            string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            if (!string.IsNullOrWhiteSpace(userId))
            {
                Form form = _formBo.GetForm(formUID);
                ICardBaseBo? cardBo = _cardBoFactory.GetCardBo(form.FormID);
                if (cardBo != null)
                {
                    ret = _formBo.GetApprovalFormCard(formUID, cardBo, empNo, userId);
                }
            }
            return ret;
        }

        /// <summary>
        /// 取得某段時間內部門已填表單，以內容日期為準
        /// </summary>
        /// <param name="request"></param>
        /// <returns>表單</returns>
        [Authorize]
        [HttpPost]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/")]
        public string GetDepartmentSentBoxByContentDate(GetFormCardsRequest request)
        {
            string ret = "[]";
            string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            request.StartDate = request.StartDate.ToLocalTime();
            request.EndDate = request.EndDate.ToLocalTime();
            bool isAdmin = _attendanceBo.IsAdmin(userId);
            if (!string.IsNullOrWhiteSpace(userId) &&
                (isAdmin || _formBo.CanSeeDepartment(request.DeptNo, request.EmpNo))
                && (request.StartDate <= request.EndDate)
                )
            {
                // 已填表單限部門登記桌或部門主管或治理可看
                ret = _formBo.GetDepartmentSentBoxByContentDateJson(request);
            }
            return ret;
        }

        /// <summary>
        /// 取得年月旬時間內部門已填表單，以內容日期為準
        /// </summary>
        /// <param name="request"></param>
        /// <returns>表單</returns>
        [Authorize]
        [HttpPost]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/")]
        public string GetDepartmentSentBoxByYearMonth(FormCardsYearMonthRequest request)
        {
            string ret = "[]";
            string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            var (startDate, endDate) = _attendanceBo.GetStartEndDays(request.Year, request.Month, request.TenDays);

            bool isAdmin = _attendanceBo.IsAdmin(userId);
            if (!string.IsNullOrWhiteSpace(userId) &&
                (isAdmin || _formBo.CanSeeDepartment(request.DeptNo, request.EmpNo))
                && (startDate <= endDate)
                )
            {
                GetFormCardsRequest getFormCardsRequest = new GetFormCardsRequest();
                getFormCardsRequest.StartDate = startDate;
                getFormCardsRequest.EndDate = endDate;
                getFormCardsRequest.EmpNo = request.EmpNo;
                getFormCardsRequest.DeptNo = request.DeptNo;
                getFormCardsRequest.ProjNo = request.ProjNo;
                getFormCardsRequest.Status = request.Status;

                // 已填表單限部門登記桌或部門主管或治理可看
                ret = _formBo.GetDepartmentSentBoxByContentDateJson(getFormCardsRequest);
            }
            return ret;
        }

        /// <summary>
        /// 取得某段時間內部門已填表單，以填表日期為準
        /// </summary>
        /// <param name="request"></param>
        /// <returns>表單</returns>
        [Authorize]
        [HttpPost]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/")]
        public string GetDepartmentSentBoxPeriod(GetFormCardsRequest request)
        {
            string ret = "[]";
            string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            request.StartDate = request.StartDate.ToLocalTime();
            request.EndDate = request.EndDate.ToLocalTime();
            bool isAdmin = _attendanceBo.IsAdmin(userId);
            if (!string.IsNullOrWhiteSpace(userId) &&
                (isAdmin || _formBo.CanSeeDepartment(request.DeptNo, request.EmpNo))
                && (request.StartDate <= request.EndDate)
                )
            {
                // 已填表單限部門登記桌或部門主管或治理可看
                ret = _formBo.GetDepartmentSentBoxJson(request);
            }
            return ret;
        }

        /// <summary>
        /// 取得特定時間區間內所有表單 by 填報日期，限管理者使用
        /// </summary>
        /// <param name="request"></param>
        /// <returns>表單</returns>
        [Authorize]
        [HttpPost]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/")]
        public string GetForms(GetFormCardsRequest request)
        {
            string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            DateTime startDate = request.StartDate.ToLocalTime();
            DateTime endDate = request.EndDate.ToLocalTime();
            // 所有表單限管理員可看
            string ret = "[]";
            bool isAdmin = _attendanceBo.IsAdmin(userId);
            if (isAdmin && startDate <= endDate)
            {
                ret = _formBo.GetFormsJson(userId, startDate, endDate, request.ProjNo);
            }
            return ret;
        }

        /// <summary>
        /// 取得特定時間區間內所有表單 by 內容日期，限管理者使用
        /// </summary>
        /// <param name="request"></param>
        /// <returns>表單</returns>
        [Authorize]
        [HttpPost]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/")]
        public string GetFormsByContentDate(GetFormCardsRequest request)
        {
            string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            DateTime startDate = request.StartDate.ToLocalTime();
            DateTime endDate = request.EndDate.ToLocalTime();
            // 所有表單限管理員可看
            string ret = "[]";
            bool isAdmin = _attendanceBo.IsAdmin(userId);
            if (isAdmin && startDate <= endDate)
            {
                ret = _formBo.GetFormsByContentDateJson(userId, startDate, endDate, request.ProjNo);
            }
            return ret;
        }

        /// <summary>
        /// 取得特定年月旬區間內所有表單 by 內容日期，限管理者使用
        /// </summary>
        /// <param name="request"></param>
        /// <returns>表單</returns>
        [Authorize]
        [HttpPost]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/")]
        public string GetFormsByContentYearMonth(FormCardsYearMonthRequest request)
        {
            string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            var (startDate, endDate) = _attendanceBo.GetStartEndDays(request.Year, request.Month, request.TenDays);

            // 所有表單限管理員可看
            string ret = "[]";
            bool isAdmin = _attendanceBo.IsAdmin(userId);
            if (isAdmin && startDate <= endDate)
            {
                ret = _formBo.GetFormsByContentDateJson(userId, startDate, endDate, request.ProjNo);
            }
            return ret;
        }

        /// <summary>
        /// 取得特定員工的待審表單
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>待審表單</returns>
        [Authorize]
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/{empNo}")]
        public string GetInbox(string empNo)
        {
            string ret = "[]";
            string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);

            if (!string.IsNullOrWhiteSpace(userId))
            {
                // 待審表單(收件匣)限本人或管理員可看
                ret = _formBo.GetInboxJson(empNo, userId, _cardBoFactory);
            }
            return ret;
        }

        /// <summary>取得單月已填卡</summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <param name="status">表單狀態，預設 null</param>
        /// <returns>單月已填卡 JSON</returns>
        [Authorize]
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/")]
        public string GetMonthSentCards(DateTime date, string empNo, int? status = null)
        {
            string ret = "[]";
            string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            date = date.ToLocalTime();
            if (!string.IsNullOrWhiteSpace(userId) && _formBo.CanSeeInbox(empNo, userId))
            {
                ret = _formBo.GetMonthSentCardsJson(_cardBoFactory, empNo, date, status);
            }
            return ret;
        }

        /// <summary>取得特定通知表單卡別資料</summary>
        /// <param name="formUID">The form uid.</param>
        /// <param name="notifyId">The notify id.</param>
        /// <param name="userId">查看員工編號</param>
        /// <returns>通知表單卡別資料 JSON</returns>
        [Authorize]
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/")]
        public string GetNotifyFormCard(Guid formUID, int notifyId, string userId)
        {
            string ret = "[]";
            string logonUserId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            if (!string.IsNullOrWhiteSpace(userId))
            {
                Form form = _formBo.GetForm(formUID);
                ICardBaseBo? cardBo = _cardBoFactory.GetCardBo(form.FormID);
                if (cardBo != null)
                {
                    ret = _formBo.GetNotifyFormCard(formUID, cardBo, notifyId, logonUserId, userId);
                }
            }
            return ret;
        }

        /// <summary>
        /// 取得特定員工在特定時間區間的通知表單卡別資料
        /// </summary>
        /// <param name="request"></param>
        /// <returns>通知表單卡別資料 JSON</returns>
        [Authorize]
        [HttpPost]
        [Route("/api/[controller]/[action]/")]
        public string GetNotifyFormCards(GetNotifyFormCardsRequest request)
        {
            string ret = "[]";
            string logonUserId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            List<int> isRead = request.IsRead.ToList();
            List<int> formStatus = request.Status.ToList();
            DateTime startDate = request.StartDate.ToLocalTime();
            DateTime endDate = request.EndDate.ToLocalTime();
            if (!string.IsNullOrWhiteSpace(logonUserId) && (startDate <= endDate))
            {
                ret = _formBo.GetNotifyFormCards(request.EmpNo, request.UserId, logonUserId, request.DeptNo, isRead, formStatus, startDate, endDate);
            }
            return ret;
        }

        /// <summary>
        /// 取得特定時間區間內已填表單，包括他人代填與本人填表，以填表時間為準
        /// </summary>
        /// <param name="request"></param>
        /// <returns>表單</returns>
        [Authorize]
        [HttpPost]
        [Route("/api/[controller]/[action]/")]
        public string GetSentBoxPeriod([FromBody] GetFormCardsRequest request)
        {
            string ret = "[]";
            string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            DateTime startDate = request.StartDate = request.StartDate.ToLocalTime();
            DateTime endDate = request.EndDate = request.EndDate.ToLocalTime();

            // 已填表單限本人或管理員可看
            if (_formBo.CanSeeInbox(request.EmpNo, userId) && (startDate <= endDate))
            {
                ret = _formBo.GetUserSentBoxJson(request.EmpNo, userId, startDate, endDate, request.ProjNo, request.Status);
            }
            return ret;
        }

        /// <summary>
        /// 取得特定時間區間內已填表單，包括他人代填與本人填表，以內容時間為準
        /// </summary>
        /// <param name="request"></param>
        /// <returns>表單 JSON</returns>
        [Authorize]
        [HttpPost]
        [Route("/api/[controller]/[action]/")]
        public string GetSentBoxPeriodByContentDate([FromBody] GetFormCardsRequest request)
        {
            string ret = "[]";
            string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            DateTime startDate = request.StartDate = request.StartDate.ToLocalTime();
            DateTime endDate = request.EndDate = request.EndDate.ToLocalTime();

            // 已填表單限本人或管理員可看
            if (_formBo.CanSeeInbox(request.EmpNo, userId) && (startDate <= endDate))
            {
                ret = _formBo.GetUserSentBoxJsonByContentDate(request.EmpNo, userId, startDate, endDate, request.ProjNo, request.Status);
            }
            return ret;
        }
        /// <summary>
        /// 取得特定年月旬時間區間內已填表單，包括他人代填與本人填表，以內容時間為準
        /// </summary>
        /// <param name="request"></param>
        /// <returns>表單 JSON</returns>
        [Authorize]
        [HttpPost]
        [Route("/api/[controller]/[action]/")]
        public string GetSentBoxPeriodByContentYearMonth([FromBody] FormCardsYearMonthRequest request)
        {
            string ret = "[]";
            string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);

            var (startDate, endDate) = _attendanceBo.GetStartEndDays(request.Year, request.Month, request.TenDays);


            // 已填表單限本人或管理員可看
            if (_formBo.CanSeeInbox(request.EmpNo, userId) && (startDate <= endDate))
            {
                ret = _formBo.GetUserSentBoxJsonByContentDate(request.EmpNo, userId, startDate, endDate, request.ProjNo, request.Status);
            }
            return ret;
        }

        /// <summary>取得已填表單及卡</summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="formUID">The form uid.</param>
        /// <returns>表單及卡 JSON</returns>
        [Authorize]
        [HttpGet]
        public string GetSentFormCard(Guid formUID, string empNo)
        {
            string ret = "[]";
            string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            if (!string.IsNullOrWhiteSpace(userId))
            {
                Form form = _formBo.GetForm(formUID);
                ICardBaseBo? cardBo = _cardBoFactory.GetCardBo(form.FormID);
                if (cardBo != null)
                {
                    ret = _formBo.GetSentFormCard(formUID, cardBo, empNo, userId);
                }
            }
            return ret;
        }

        /// <summary>取得特定已簽核表單及卡</summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="formUID">The form uid.</param>
        /// <returns>表單及卡 JSON</returns>
        [Authorize]
        [HttpGet]
        public string GetSignedFormCard(Guid formUID, string empNo)
        {
            string ret = "[]";
            string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            if (!string.IsNullOrWhiteSpace(userId))
            {
                Form form = _formBo.GetForm(formUID);
                ICardBaseBo? cardBo = _cardBoFactory.GetCardBo(form.FormID);
                if (cardBo != null)
                {
                    ret = _formBo.GetSignedFormCard(formUID, cardBo, empNo, userId);
                }
            }
            return ret;
        }

        /// <summary>
        /// 取得 指定員工簽核 表單，自 啟始日期 至 結束日期
        /// </summary>
        /// <param name="input">FormCardsYearMonthRequest，讀取員工編號、啟始日期、結束日期</param>
        /// <returns>表單 JSON</returns>
        [Authorize]
        [HttpPost]
        [Route("/api/[controller]/[action]/")]
        public string GetSignedFormsPeriod([FromBody] FormCardsYearMonthRequest input)
        {
            string ret = "[]";
            string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            DateTime startDate = input.StartDate.ToLocalTime();
            DateTime endDate = input.EndDate.ToLocalTime();
            if (!string.IsNullOrWhiteSpace(userId) && (startDate <= endDate))
            {
                ret = _formBo.GetSignedFormsJson(input.EmpNo, userId, startDate, endDate);
            }
            return ret;
        }

        /// <summary>
        /// 取得特定年月旬時間區間內 指定員工簽核表單
        /// </summary>
        /// <param name="input">FormCardsYearMonthRequest，讀取員工編號、年、月、旬</param>
        /// <returns>表單 JSON</returns>
        [Authorize]
        [HttpPost]
        [Route("/api/[controller]/[action]/")]
        public string GetSignedFormsByYearMonth([FromBody] FormCardsYearMonthRequest input)
        {
            string ret = "[]";
            string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            (DateTime contentStartDate, DateTime contentEndDate) = _attendanceBo.GetStartEndDays(input.Year, input.Month, input.TenDays);

            ret = _formBo.GetSignedFormsJsonByContentDate(input.EmpNo, userId, input.StartDate.ToLocalTime(), input.EndDate.ToLocalTime(), contentStartDate, contentEndDate);
            return ret;
        }

        /// <summary>
        /// 取得使用者前N筆簽核意見
        /// </summary>
        /// <param name="empNo"></param>
        /// <param name="count"></param>
        /// <returns>簽核意見</returns>
        [Authorize]
        [HttpPost]
        [Route("/api/[controller]/[action]/")]
        public List<string> GetTopApproveComments(string empNo, int count = 10)
        {
            string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            // 所有表單限管理員可看
            List<string> ret = _formBo.GetTopApproveComments(userId, empNo, count);
            return ret;
        }

        /// <summary>
        /// 標註某員工及其對應之角色的所有未讀通知為已讀
        /// </summary>
        /// <param name="request"></param>
        /// <returns>有一筆以上的通知被標註已讀則回傳true，否則回傳false</returns>
        [Authorize]
        [HttpPost]
        [Route("/api/[controller]/[action]/")]
        public IActionResult MarkDeliveredNotifications(GetNotifyFormCardsRequest request)
        {
            string logonUserId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            // 限本人讀取才視為已讀通知
            bool result = _formBo.MarkDeliveredNotifications(logonUserId, request.UserId);
            return Ok(new { result });
        }

        /// <summary>
        /// 取得Badge統計訊息數量
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>Badge統計訊息數量</returns>
        [Authorize]
        [HttpGet]
        [Route("/api/[controller]/[action]/{empNo}")]
        public IActionResult MenuBadge(string empNo)
        {
            int inbox = _formBo.GetInboxCount(empNo);
            int notification = _formBo.GetNotificationCount(empNo);

            return Ok(new { inbox, notification });
        }

        /// <summary>
        /// 群簽
        /// </summary>
        /// <param name="approveDtoList">簽核物件 集合</param>
        /// <returns>若成功回傳空字串""，若失敗傳回失敗單號列表</returns>
        [Authorize]
        [HttpPost]
        [Route("/api/[controller]/[action]/")]
        public string MultipleApprove(List<Approve> approveDtoList)
        {
            string ret = "Not Authorized";
            string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            if (!string.IsNullOrWhiteSpace(userId))
            {
                var (ip, hostname) = CardUtility.GetIP_Hostname(Request.HttpContext);

                foreach (Approve approveDto in approveDtoList)
                {
                    approveDto.IsAgentApprove = false;
                    approveDto.IsNotification = false;
                    System.Net.IPAddress? ipAddress = ip;
                    approveDto.ApproveIP = ipAddress?.ToString();
                    approveDto.ApproveHost = hostname;
                    approveDto.ApproveTime = approveDto.ApproveTime.ToLocalTime();
                }
                List<string> list = _formBo.MultipleApprove(approveDtoList, _cardBoFactory, userId);
                ret = JsonConvert.SerializeObject(list);
            }
            return ret;
        }

        /// <summary>
        /// 抽單
        /// </summary>
        /// <param name="withdraw">抽單物件</param>
        /// <returns>成功傳回空值，失敗傳回錯誤訊息</returns>
        [Authorize]
        [HttpDelete]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/")]
        public string Withdraw(Withdraw withdraw)
        {
            string errorMessage = string.Empty;
            withdraw.WithdrawTime = withdraw.WithdrawTime.ToLocalTime();
            if (Request != null && Request.HttpContext != null)
            {
                var (ip, hostname) = CardUtility.GetIP_Hostname(Request.HttpContext);
                System.Net.IPAddress? ipAddress = ip;
                withdraw.WithdrawIP = ipAddress?.ToString();
                withdraw.WithdrawHost = hostname;
            }

            string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            if (!string.IsNullOrWhiteSpace(userId))
            {
                withdraw.WithdrawEmpNo = userId;
            }

            Form form = _formBo.GetForm(withdraw.FormUID);
            ICardBaseBo? cardBo = _cardBoFactory.GetCardBo(withdraw.FormID);
            if (cardBo != null)
            {
                CardBase? card = cardBo.GetCard(withdraw.FormUID);
                if (card != null)
                {
                    errorMessage = _formBo.Withdraw(form, cardBo, card, withdraw);
                }
            }
            return errorMessage;
        }
    }
}
