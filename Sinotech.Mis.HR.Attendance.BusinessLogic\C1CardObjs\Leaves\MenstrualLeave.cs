﻿using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Diagnostics.CodeAnalysis;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves
{
    /// <summary>
    /// 生理假類別 - 處理女性員工生理假的申請邏輯
    /// 包含檢查申請資格、超假驗證、必要欄位驗證等功能
    /// </summary>
    /// <seealso cref="Leaves.LeaveBase" />
    [LeaveKind(LeaveKindEnum.MenstrualLeave)]
    public class MenstrualLeave : C1CardBase
    {
        #region CheckResult - 檢查結果相關常數與訊息定義

        /// <summary>
        /// 錯誤代碼：生理假限女性申請
        /// </summary>
        public const int CodeFemaleOnly = 3016301;
        /// <summary>
        /// 錯誤訊息：生理假限女性申請
        /// </summary>
        private readonly string _messageFemaleOnly = "生理假限女性申請";
        /// <summary>
        /// 錯誤狀態：生理假限女性申請
        /// </summary>
        private readonly CardStatusEnum _statusFemaleOnly = CardStatusEnum.Error;

        /// <summary>
        /// 快取的檢查結果：生理假限女性申請
        /// </summary>
        private CardCheckResult? _resultFemaleOnly;
        /// <summary>
        /// 取得檢查結果：生理假限女性申請（延遲初始化）
        /// </summary>
        private CardCheckResult ResultFemaleOnly =>
            _resultFemaleOnly ??=
            new CardCheckResult(CodeFemaleOnly, _statusFemaleOnly, _messageFemaleOnly);

        /// <summary>
        /// 錯誤代碼：本月已請生理假，不得超假
        /// </summary>
        public const int CodeExceedQuota = 3016302;
        /// <summary>
        /// 錯誤訊息：本月已請生理假，不得超假
        /// </summary>
        private readonly string _messageExceedQuota = "本月已請生理假，不得超假";
        /// <summary>
        /// 錯誤狀態：本月已請生理假，不得超假
        /// </summary>
        private readonly CardStatusEnum _statusExceedQuota = CardStatusEnum.Error;

        /// <summary>
        /// 快取的檢查結果：本月已請生理假，不得超假
        /// </summary>
        private CardCheckResult? _resultExceedQuota;
        /// <summary>
        /// 取得檢查結果：本月已請生理假，不得超假（延遲初始化）
        /// </summary>
        private CardCheckResult ResultExceedQuota =>
            _resultExceedQuota ??=
            new CardCheckResult(CodeExceedQuota, _statusExceedQuota, _messageExceedQuota);

        /// <summary>
        /// 錯誤代碼：請假時數不合規定
        /// </summary>
        public const int CodeIllegalRange = 3016303;
        /// <summary>
        /// 錯誤訊息：請假時數超過每日正常工時，請修改請假時間
        /// </summary>
        private readonly string _messageIllegalRange = "請假時數超過每日正常工時，請修改請假時間";
        /// <summary>
        /// 錯誤狀態：請假時數不合規定
        /// </summary>
        private readonly CardStatusEnum _statusIllegalRange = CardStatusEnum.Error;

        /// <summary>
        /// 快取的檢查結果：請假時數不合規定
        /// </summary>
        private CardCheckResult? _resultIllegalRange;
        /// <summary>
        /// 取得檢查結果：請假時數不合規定（延遲初始化）
        /// </summary>
        private CardCheckResult ResultIllegalRange =>
            _resultIllegalRange ??=
            new CardCheckResult(CodeIllegalRange, _statusIllegalRange, _messageIllegalRange);

        #endregion

        /// <summary>
        /// 生理假類別建構子
        /// 初始化生理假處理物件，傳入請假卡片資料與業務邏輯物件
        /// </summary>
        /// <param name="c1Card">C1請假卡片資料</param>
        /// <param name="c1CardBo">C1請假卡片業務邏輯物件</param>
        public MenstrualLeave(C1Card c1Card, IC1CardBo c1CardBo) : base(c1Card, c1CardBo)
        {
        }

        /// <summary>
        /// 檢查是否能請此假別（生理假）
        /// 驗證申請人性別與休假紀錄是否存在
        /// </summary>
        /// <returns>檢查結果，包含錯誤代碼、狀態與訊息</returns>
        internal override CardCheckResult CanTakeThisLeave()
        {
            // 檢查申請人性別：女性才能請生理假
            if (!IsFemale())
            {
                return ResultFemaleOnly;
            }

            // 檢查是否已經建立休假資料記錄
            if (!IfLeaveRecordExists())
            {
                return ResultLeaveRecordNotCreatedYet;
            }

            // 所有檢查通過
            return ResultOk;
        }

        /// <summary>
        /// 檢查是否超過允許的請假時數
        /// 生理假每月限請一次，且必須為8小時（一整天）
        /// </summary>
        /// <returns>檢查結果，包含錯誤代碼、狀態與訊息</returns>
        internal override CardCheckResult CheckOverPermittedLeaveHours()
        {
            // 檢查本月是否已請過生理假（每月限一次）
            if (IsMenstrualLeaveAlreadyTaken())
            {
                return ResultExceedQuota;
            }

            // 檢查請假時數是否為8小時（生理假必須請整天）
            if (TotalHours != 8)
            {
                return ResultIllegalRange;
            }

            // 時數檢查通過
            return ResultOk;
        }

        /// <summary>
        /// 檢查必要欄位是否已填寫
        /// 繼承父類別的基本欄位檢查邏輯
        /// </summary>
        /// <returns>檢查結果，包含錯誤代碼、狀態與訊息</returns>
        internal override CardCheckResult CheckRequiredFields()
        {
            // 執行父類別的基本必要欄位檢查
            var result = base.CheckRequiredFields();
            if (result != ResultOk)
            {
                return result;
            }

            // 生理假無額外的必要欄位檢查
            return ResultOk;
        }

        /// <summary>
        /// 計算最早可請假日期與最晚可請假日期
        /// 使用預設的計算邏輯，生理假無特殊期間限制
        /// </summary>
        /// <param name="date">請假日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>Tuple包含 (最早可請假日期, 最晚可請假日期)</returns>
        public override (DateTime, DateTime) CalculateLeavePermittedPeriod(DateTime date, string empNo) => DefaultCalculateLeavePermittedPeriod(date, empNo);

        /// <summary>
        /// 檢查指定性別是否可請此假別（生理假）
        /// 生理假限女性申請
        /// </summary>
        /// <param name="gender">員工性別</param>
        /// <returns>true：可申請；false：不可申請</returns>
        public static bool IsAllowForThisGender(Gender gender)
        {
            return gender == Gender.Female;
        }

        #region Tools - 輔助工具方法

        /// <summary>
        /// 檢查申請人是否為女性
        /// </summary>
        /// <returns>true：女性；false：男性</returns>
        private bool IsFemale()
        {
            return _c1CardBo.IsFemale(_c1Card.EmpNo);
        }

        /// <summary>
        /// 檢查申請人在指定年月是否已請過生理假
        /// 生理假每月限請一次
        /// </summary>
        /// <returns>true：已請過；false：尚未請過</returns>
        private bool IsMenstrualLeaveAlreadyTaken()
        {
            return _c1CardBo.IsMenstrualLeaveAlreadyTaken(_c1Card.EmpNo, _c1Card.StartDate.Year, _c1Card.StartDate.Month);
        }

        #endregion
    }
}
