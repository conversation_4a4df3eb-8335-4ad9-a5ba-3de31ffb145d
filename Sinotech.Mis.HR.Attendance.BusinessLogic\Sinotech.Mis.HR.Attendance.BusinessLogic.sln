﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.32510.428
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Sinotech.Mis.HR.Attendance.BusinessLogic", "Sinotech.Mis.HR.Attendance.BusinessLogic.csproj", "{7FE6C2A7-145E-4596-ABEB-F5FE770AFD35}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Sinotech.Mis.HR.Attendance.Common", "..\Sinotech.Mis.HR.Attendance.Common\Sinotech.Mis.HR.Attendance.Common.csproj", "{44F0534E-3952-48B8-B5C2-3ECE866729B8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Sinotech.Mis.Common", "..\Sinotech.Mis.Common\Sinotech.Mis.Common.csproj", "{2EA5272B-5A3A-41E7-822A-BF600C87EAC2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Sinotech.Mis.Utilities", "..\Sinotech.Mis.Utilities\Sinotech.Mis.Utilities.csproj", "{9C38F4C0-EED4-4E21-81F2-BDF0582AE658}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Sinotech.Mis.HR.Attendance.DataAccess.Interfaces", "..\Sinotech.Mis.HR.Attendance.DataAccess.Interfaces\Sinotech.Mis.HR.Attendance.DataAccess.Interfaces.csproj", "{F31C4939-18C4-4986-A814-EFDB2CDBE1C1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Sinotech.Mis.HR.Attendance.DataAccess.Ado", "..\Sinotech.Mis.HR.Attendance.DataAccess.Ado\Sinotech.Mis.HR.Attendance.DataAccess.Ado.csproj", "{37319B05-EC12-454C-93F0-7D213BADAA41}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Sinotech.Mis.HR.Attendance.Utilities", "..\Sinotech.Mis.HR.Attendance.Utilities\Sinotech.Mis.HR.Attendance.Utilities.csproj", "{F82BF026-AEDB-4035-9DFD-FC7B1AB8CBC7}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{7FE6C2A7-145E-4596-ABEB-F5FE770AFD35}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7FE6C2A7-145E-4596-ABEB-F5FE770AFD35}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7FE6C2A7-145E-4596-ABEB-F5FE770AFD35}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7FE6C2A7-145E-4596-ABEB-F5FE770AFD35}.Release|Any CPU.Build.0 = Release|Any CPU
		{44F0534E-3952-48B8-B5C2-3ECE866729B8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{44F0534E-3952-48B8-B5C2-3ECE866729B8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{44F0534E-3952-48B8-B5C2-3ECE866729B8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{44F0534E-3952-48B8-B5C2-3ECE866729B8}.Release|Any CPU.Build.0 = Release|Any CPU
		{2EA5272B-5A3A-41E7-822A-BF600C87EAC2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2EA5272B-5A3A-41E7-822A-BF600C87EAC2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2EA5272B-5A3A-41E7-822A-BF600C87EAC2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2EA5272B-5A3A-41E7-822A-BF600C87EAC2}.Release|Any CPU.Build.0 = Release|Any CPU
		{9C38F4C0-EED4-4E21-81F2-BDF0582AE658}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9C38F4C0-EED4-4E21-81F2-BDF0582AE658}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9C38F4C0-EED4-4E21-81F2-BDF0582AE658}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9C38F4C0-EED4-4E21-81F2-BDF0582AE658}.Release|Any CPU.Build.0 = Release|Any CPU
		{F31C4939-18C4-4986-A814-EFDB2CDBE1C1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F31C4939-18C4-4986-A814-EFDB2CDBE1C1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F31C4939-18C4-4986-A814-EFDB2CDBE1C1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F31C4939-18C4-4986-A814-EFDB2CDBE1C1}.Release|Any CPU.Build.0 = Release|Any CPU
		{37319B05-EC12-454C-93F0-7D213BADAA41}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{37319B05-EC12-454C-93F0-7D213BADAA41}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{37319B05-EC12-454C-93F0-7D213BADAA41}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{37319B05-EC12-454C-93F0-7D213BADAA41}.Release|Any CPU.Build.0 = Release|Any CPU
		{F82BF026-AEDB-4035-9DFD-FC7B1AB8CBC7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F82BF026-AEDB-4035-9DFD-FC7B1AB8CBC7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F82BF026-AEDB-4035-9DFD-FC7B1AB8CBC7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F82BF026-AEDB-4035-9DFD-FC7B1AB8CBC7}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {9413B172-633C-4DA9-A71C-D726D0490A88}
	EndGlobalSection
EndGlobal
