{"compilerOptions": {"forceConsistentCasingInFileNames": true, "target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "resolveJsonModule": true, "isolatedModules": true, "skipLibCheck": true, "baseUrl": ".", "lib": ["esnext", "dom", "dom.iterable", "scripthost"], "types": ["vitest/globals"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "tests/**/*.ts", "tests/**/*.tsx"], "exclude": ["node_modules"]}