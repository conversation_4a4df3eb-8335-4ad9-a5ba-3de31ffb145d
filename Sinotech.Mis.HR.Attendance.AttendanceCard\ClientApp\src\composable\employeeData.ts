import { ref } from 'vue'
import { GET_EMPLOYEES_URL } from '../api/appUrl'
import type { EmployeeStoreBaseType, EmployeeApiBaseType } from '../api/appType'

export function useEmployeeData() {
  const employeeData = ref<Array<EmployeeStoreBaseType>>([])

  const onSetEmployeeData = async (deptNo: number, signal: AbortSignal): Promise<void> => {
    const params = new URLSearchParams({
      deptNo: deptNo.toString()
    })
    const res: Response = await fetch(GET_EMPLOYEES_URL + '?' + params, {
      method: 'GET',
      signal: signal
    })
    if (!res.ok) {
      throw new Error(res.status.toString())
    }
    const jsonData = await res.json()
    employeeData.value = jsonData.map((e: EmployeeApiBaseType) => {
      return {
        userId: e.EmpNo,
        userName: e.CName,
        deptNo: e.Dept<PERSON>o,
        deptSName: e.DeptSName
      }
    })
  }

  return { employeeData, onSetEmployeeData }
}