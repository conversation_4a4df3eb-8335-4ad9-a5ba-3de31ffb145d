﻿using Sinotech.Mis.Common;
using System;

namespace Sinotech.Mis.HR.Attendance.Common
{
    public interface IProjectBo
    {
        /// <summary>Gets the end date.</summary>
        /// <param name="projectNumber">The project number.</param>
        /// <returns></returns>
        string GetOpenProjectsDateRange(DateTime startDate, DateTime endDate, int deptNo = -1);

        /// <summary>Gets the project.</summary>
        /// <param name="projectNumber">The project number.</param>
        /// <returns></returns>
        Project GetProject(string projectNumber);

        /// <summary>Gets the name of the project.</summary>
        /// <param name="projectNumber">The project number.</param>
        /// <returns></returns>
        string? GetProjectName(string projectNumber);

    }
}
