﻿using System;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 加班卡細項
    /// </summary>
    public class B1CardDetail
    {
        /// <summary>
        /// 流水號
        /// </summary>
        public int? ID { get; set; }

        /// <summary>
        /// 計畫編號
        /// </summary>
        public string Project { get; set; } = string.Empty;

        /// <summary>
        /// 起始時間
        /// </summary>
        public DateTime StartTime { get; set; }


        /// <summary>
        /// 截止時間
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 加班時數
        /// </summary>
        public int Hour { get; set; }

        /// <summary>
        /// 加班補休假剩餘時數
        /// </summary>
        public int HourLeft { get; set; }

        /// <summary>
        /// 加班申請別代號  1：加班 2：社外加班 3：補休假
        /// </summary>
        public int B1_CODE { get; set; }

        /// <summary>
        /// 申請別名稱 / 加班申請別 名稱
        /// </summary>
        public string ApplicationType { get; set; } = string.Empty;

        /// <summary>
        /// 加班卡填報序號 從1開始編號
        /// </summary>
        public int SerialNo { get; set; }


    }
}
