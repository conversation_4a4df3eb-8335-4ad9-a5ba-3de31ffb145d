import { ref, computed } from 'vue'
import type { EmployeeType, SignerType } from '../api/appType'

/**
 * 加會人員
 * @returns 
 */
export function useAddSigner() {
  const signers = ref<Array<SignerType>>([])

  const signersString = computed<string>(() =>
    signers.value.reduce((accumulator: string | null, currentValue: SignerType) => {
      if (accumulator === '' || accumulator === null) {
        return currentValue.userId ?? ''
      } else {
        return accumulator + ',' + (currentValue.userId ?? '')
      }
    }, '')
  )

  const onChangeSigner = (user: EmployeeType, index: number) => {
    signers.value[index] = {
      userId: user.userId,
      userName: user.userName
    }
  }

  const onChangeSigners = (signersData: Array<SignerType>) => {
    signers.value = signersData
  }

  const onAddSigner = () => {
    signers.value.push({
      userId: null,
      userName: null
    })
  }
  
  const onDeleteSigner = (index: number) => {
    signers.value.splice(index, 1)
  }

  const onDeleteSigners = () => {
    signers.value = []
  }

  const onCheckSigner = () => signers.value.some((e: SignerType) => e.userId === null)

  return { signers, signersString, onChangeSigner, onChangeSigners, onAddSigner, onDeleteSigner, onDeleteSigners, onCheckSigner }
}