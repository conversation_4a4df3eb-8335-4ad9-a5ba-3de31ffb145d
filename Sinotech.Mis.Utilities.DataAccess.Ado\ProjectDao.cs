﻿using Sinotech.Mis.Helpers;
using Sinotech.Mis.Utilities.DataAccess.Interfaces;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;

namespace Sinotech.Mis.Utilities.DataAccess.Ado
{
    /// <summary>計畫資料存取元件</summary>
    public class ProjectDao : IProjectDao
    {

        private readonly string _connectionString;

        /// <summary>Initializes a new instance of the <see cref="ProjectDao" /> class.</summary>
        /// <param name="connectionString">The connection string.</param>
        public ProjectDao(string connectionString)
        {
            _connectionString = connectionString;
        }

        /// <summary>
        /// 取得現有部門的所有計畫
        /// </summary>
        /// <returns></returns>
        public DataTable GetAllDepartmentsProjects()
        {
            string sql = """
SELECT vwPrjData.PrjNo, vwPrjData.PrjName, vwPrjData.BDate, vwPrjData.EDate, vwPrjData.MainDeptNo, 
vwDeptData.DeptSName, vwDeptData.DeptName, vwPrjData.PrjFName, vwPrjData.PrjFName, vwPrjData.YM_BanFillManMonth, 
vwPrjData.YM_Finish
FROM vwPrjData_WithFinish AS vwPrjData INNER JOIN
vwDeptData ON vwPrjData.MainDeptNo = vwDeptData.DeptNo
WHERE (vwPrjData.MainDeptNo IN
(SELECT DeptNo
FROM vwDeptData AS vwDeptData_1
WHERE (StNo = 1))) 
ORDER BY vwPrjData.MainDeptNo, vwPrjData.PrjNo;
""";
            return SqlHelper.GetDataTable(_connectionString, sql);
        }

        /// <summary>
        /// 取得各部門所有計畫
        /// 會將優先部門放在前面
        /// </summary>
        ///<param name="deptNo">優先部門代號，0 表示不將優先部門置前</param>
        /// <returns></returns>
        public DataTable GetAllDepartmentsProjects(int deptNo)
        {
            string sql = """
SELECT vwPrjData.PrjNo,vwPrjData.PrjName,vwPrjData.PrjFName,vwPrjData.BDate,vwPrjData.EDate,vwPrjData.MainDeptNo, vwPrjData.YM_BanFillManMonth, vwPrjData.YM_Finish,
case WHEN MainDeptNo=@PriorDeptNo Then 1 else 10 END as DisplayOrder,
vwDeptData.DeptSName,vwDeptData.DeptName FROM vwPrjData_WithFinish AS vwPrjData  INNER JOIN vwDeptData ON vwPrjData.MainDeptNo = vwDeptData.DeptNo 
WHERE vwDeptData.StNo = 1  -- AND LEN(PrjNo) = 7
Order by DisplayOrder, PrjNo;
""";
            SqlParameter paramDeptNo = new SqlParameter("@PriorDeptNo", SqlDbType.Int);
            paramDeptNo.Value = deptNo;
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, paramDeptNo);
            return dt;
        }

        /// <summary>
        /// 取得所有計畫
        /// </summary>
        /// <returns></returns>
        public DataTable GetAllProjects()
        {
            string sql = """
SELECT vwPrjData.PrjNo, vwPrjData.PrjName, vwPrjData.BDate, vwPrjData.EDate, vwPrjData.MainDeptNo, 
 vwDeptData.DeptSName, vwDeptData.DeptName, vwPrjData.YM_Finish, vwPrjData.YM_BanFillManMonth, 
 vwPrjData.PrjFName
FROM vwPrjData_WithFinish AS vwPrjData INNER JOIN
 vwDeptData ON vwPrjData.MainDeptNo = vwDeptData.DeptNo
ORDER BY vwPrjData.PrjNo;
""";
            return SqlHelper.GetDataTable(_connectionString, sql);
        }

        /// <summary>取得專案結案日期</summary>
        /// <param name="projectNumber">The project number.</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public DateTime? GetEndDate(string projectNumber)
        {
            DateTime? endDate = null;
            string sql = @"SELECT EDate FROM vwPrjData_WithFinish WHERE PrjNo=@PrjNo;";
            SqlParameter parameter = new SqlParameter("@PrjNo", projectNumber);
            object? obj = SqlHelper.GetFieldValue(_connectionString, sql, parameter);
            if (obj != null && obj != DBNull.Value)
            {
                endDate = (DateTime)obj;
            }
            return endDate;
        }

        /// <summary>取得計畫填報截止日</summary>
        /// <param name="projectNumber">The project number.</param>
        /// <returns></returns>
        public DateTime? GetSubmitDueDate(string projectNumber)
        {
            DateTime? submitDueDate = null;
            string sql = @"SELECT YM_BanFillManMonth FROM vwPrjData_WithFinish WHERE PrjNo=@PrjNo;";
            SqlParameter parameter = new SqlParameter("@PrjNo", projectNumber);
            object? obj = SqlHelper.GetFieldValue(_connectionString, sql, parameter);
            if (obj != null && obj != DBNull.Value)
            {
                submitDueDate = (DateTime)obj;
            }
            return submitDueDate;
        }

        ///<summary>查詢指定期間內已成立及未結案計畫。(查詢結束日期(不含)前成立 且 起始日期後結案或未結案 之計畫)</summary>
        ///<param name="startDate">起始日期</param>
        ///<param name="endDate">結束日期</param>
        ///<returns>DataTable(PrjNo：計畫編號、PrjName：計畫名稱、BDate：成立日期、EDate：結案日期、MainDeptNo：主辦部門代號、DeptSName：主辦部門名稱)</returns>
        public DataTable GetOpenProjectsDateRange(DateTime startDate, DateTime endDate)
        {
            DataTable dt = new DataTable();
            if (endDate >= startDate)
            {
                // 2022/11/09更新：如計畫的主辦部門為目前不存在之部門，則不顯示
                string sql = """
SELECT vwPrjData.PrjNo, vwPrjData.PrjName, vwPrjData.PrjFName,vwPrjData.BDate, vwPrjData.EDate, 
vwPrjData.MainDeptNo, vwPrjData.YM_BanFillManMonth, 
vwPrjData.YM_Finish, vwDeptData.DeptSName 
FROM vwPrjData_WithFinish AS vwPrjData 
INNER JOIN vwDeptData ON vwPrjData.MainDeptNo = vwDeptData.DeptNo
WHERE 
 ((vwPrjData.EDate IS NOT NULL AND vwPrjData.BDate >= @startDate AND vwPrjData.EDate <= @endDate)
 OR (vwPrjData.EDate IS NULL AND vwPrjData.BDate <= @endDate) )
 AND (MainDeptNo in (SELECT DeptNo FROM vwDeptData WHERE (StNo = 1)))
AND (@StartDate < YM_BanFillManMonth OR YM_BanFillManMonth IS NULL)
 ORDER BY PrjNo ASC;
""";
                List<SqlParameter> parameters = new List<SqlParameter>();
                SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
                parameterStartDate.Value = startDate;
                parameters.Add(parameterStartDate);
                SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
                parameterEndDate.Value = endDate;
                parameters.Add(parameterEndDate);
                dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            }
            return dt;
        }

        ///<summary>
        /// 查詢指定期間內已成立及未結案計畫。(查詢結束日期(不含)前成立 且 起始日期後結案或未結案 之計畫)
        /// 會將優先部門放在前面
        ///</summary>
        ///<param name="startDate">起始日期</param>
        ///<param name="endDate">結束日期</param>
        ///<param name="deptNo">優先部門代號，0 表示不將優先部門置前</param>
        ///<returns>DataTable(PrjNo：計畫編號、PrjName：計畫名稱、BDate：成立日期、EDate：結案日期、MainDeptNo：主辦部門代號、DeptSName：主辦部門名稱)</returns>
        public DataTable GetOpenProjectsDateRange(DateTime startDate, DateTime endDate, int deptNo)
        {
            // 2022/11/09更新：如計畫的主辦部門為目前不存在之部門，則不顯示
            // 2023/12/21更新：優先部門置前
            string sql = """
SELECT vwPrjData.PrjNo,vwPrjData.PrjName,vwPrjData.PrjFName,vwPrjData.BDate,vwPrjData.EDate,vwPrjData.MainDeptNo, 
vwPrjData.YM_BanFillManMonth, vwPrjData.YM_Finish,case WHEN MainDeptNo=@PriorDeptNo Then 1 else 10 END as DisplayOrder,
vwDeptData.DeptSName,vwDeptData.DeptName FROM vwPrjData_WithFinish AS vwPrjData INNER JOIN vwDeptData ON vwPrjData.MainDeptNo = vwDeptData.DeptNo 
WHERE vwDeptData.StNo = 1  -- AND LEN(PrjNo) = 7
AND ( (BDate <= @EndDate) AND (EDate IS NULL) OR (EDate > @StartDate) )  
AND (@StartDate < YM_BanFillManMonth OR YM_BanFillManMonth IS NULL)
Order by DisplayOrder, PrjNo;
""";
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter paramDeptNo = new SqlParameter("@PriorDeptNo", SqlDbType.Int);
            paramDeptNo.Value = deptNo;
            parameters.Add(paramDeptNo);
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            return dt;
        }

        /// <summary>取得計畫</summary>
        /// <param name="projectNumber">The project number.</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public DataTable GetProject(string projectNumber)
        {
            string sql = """
SELECT vwPrjData.PrjNo, vwPrjData.PrjName, vwPrjData.MainDeptNo, vwDeptData.DeptSName, vwPrjData.BDate,
vwPrjData.EDate,vwPrjData.PrjFName, vwPrjData.YM_BanFillManMonth, vwPrjData.YM_Finish
FROM vwPrjData_WithFinish AS vwPrjData INNER JOIN vwDeptData ON vwPrjData.MainDeptNo = vwDeptData.DeptNo 
WHERE (vwPrjData.PrjNo = @PrjNo);
""";
            SqlParameter parameter = new SqlParameter("@PrjNo", SqlDbType.VarChar);
            parameter.Value = projectNumber;
            return SqlHelper.GetDataTable(_connectionString, sql, parameter);
        }

        /// <summary>取得計畫名稱</summary>
        /// <param name="projectNumber">The project number.</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public string GetProjectName(string projectNumber)
        {
            string projectName = "";
            string sql = @"SELECT PrjName,PrjFName FROM vwPrjData_WithFinish WHERE PrjNo=@PrjNo;";
            SqlParameter parameter = new SqlParameter("@PrjNo", SqlDbType.VarChar);
            parameter.Value = projectNumber;
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameter);
            if (dt != null && dt.Rows.Count > 0)
            {
                DataRow dr = dt.Rows[0];
                projectName = (string)dt.Rows[0]["PrjName"];
                if (dr["PrjName"] != null && dr["PrjName"] != DBNull.Value)
                {
                    projectName = (string)dr["PrjName"];
                    if (string.IsNullOrWhiteSpace(projectName) && dr["PrjFName"] != null && dr["PrjFName"] != DBNull.Value)
                    {
                        projectName = (string)dr["PrjFName"];
                    }
                }
                else if (dr["PrjFName"] != null && dr["PrjFName"] != DBNull.Value)
                {
                    projectName = (string)dr["PrjFName"];
                }
            }
            return projectName;
        }
    }
}
