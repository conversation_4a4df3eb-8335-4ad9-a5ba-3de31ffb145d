﻿using System;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 通知表單卡別資料
    /// </summary>
    public class NotifyFormCards
    {
        /// <summary>
        /// 流水號
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public int ID { get; set; }

        /// <summary>
        /// 被通知者員工編號
        /// </summary>
        /// <value>
        /// The notify emp no.
        /// </value>
        public string NotifyEmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 被通知者姓名
        /// </summary>
        /// <value>
        /// The name of the notify.
        /// </value>
        public string NotifyName { get; set; } = string.Empty;

        /// <summary>
        /// 通知日期時間
        /// </summary>
        /// <value>
        /// The notify time.
        /// </value>
        public DateTime NotifyTime { get; set; }

        /// <summary>
        /// 通知讀取日期時間
        /// </summary>
        /// <value>
        /// The view time.
        /// </value>
        public DateTime? ViewTime { get; set; }

        /// <summary>
        /// 表單識別號
        /// </summary>
        /// <value>
        /// The form uuid.
        /// </value>
        public Guid FormUID { get; set; }

        /// <summary>
        /// 表單編號
        /// </summary>
        public string FormID { get; set; } = string.Empty;

        /// <summary>
        /// 表單單號
        /// </summary>
        public string FormNo { get; set; } = string.Empty;

        /// <summary>
        /// 表單主旨
        /// </summary>
        public string FormSubject { get; set; } = string.Empty;

        /// <summary>
        /// 表單資訊
        /// </summary>
        public string FormInfo { get; set; } = string.Empty;

        /// <summary>
        /// 申請人員工編號
        /// </summary>
        public string EmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 申請人姓名
        /// </summary>
        public string EmpName { get; set; } = string.Empty;

        /// <summary>
        /// 申請人部門編號
        /// </summary>
        public int DeptNo { get; set; }

        /// <summary>
        /// 表單簽核狀態(資料庫是 tinyint)
        /// </summary>
        public int FormStatus { get; set; }

        /// <summary>
        /// 表單簽核狀態名稱
        /// </summary>
        public string FormStatusName { get; set; } = string.Empty;

        /// <summary>
        /// 卡的ID
        /// </summary>
        public int CardID { get; set; }

        /// <summary>
        /// 申請別
        /// </summary>
        public string ApplicationType { get; set; } = string.Empty;
    }
}
