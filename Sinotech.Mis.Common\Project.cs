﻿using System;

namespace Sinotech.Mis.Common
{
    /// <summary>
    /// 計畫基本資料
    /// </summary>
    public class Project
    {
        /// <summary>
        /// 計畫編號
        /// </summary>
        public string PrjNo { get; set; } = string.Empty;

        /// <summary>
        /// 計畫主要部門編號
        /// </summary>
        public int MainDeptNo { get; set; }

        /// <summary>
        /// 計畫主要部門簡稱
        /// </summary>
        public string DeptSName { get; set; } = string.Empty;

        /// <summary>
        /// 計畫名稱
        /// </summary>
        public string PrjName { get; set; } = string.Empty;

        /// <summary>
        /// 計畫啟始日
        /// </summary>
        public DateTime? BDate { get; set; }

        /// <summary>
        /// 計畫結案日
        /// </summary>
        public DateTime? EDate { get; set; }

        /// <summary>
        /// 計畫填報截止日
        /// </summary>
        public DateTime? SubmitDueDate { get; set; } = null;


    }
}
