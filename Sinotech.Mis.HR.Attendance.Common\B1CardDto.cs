﻿using System;
using System.Collections.Generic;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 加班卡DTO，欄位順序不可更改
    /// </summary>
    public class B1CardDto
    {
        /// <summary>
        /// 流水號
        /// </summary>
        public int? ID { get; set; }

        /// <summary>
        /// 表單識別碼
        /// </summary>
        public Guid FormUID { get; set; } = Guid.Empty;

        /// <summary>
        /// 員工編號
        /// </summary>
        public string B1_EMPNO { get; set; } = string.Empty;

        /// <summary>
        /// 民國年月
        /// </summary>
        public string B1_YYMM { get; set; } = string.Empty;

        /// <summary>
        /// 計畫編號
        /// </summary>
        public string B1_PROJNO { get; set; } = string.Empty;

        /// <summary>
        /// 起始日
        /// </summary>
        public string B1_SDD { get; set; } = string.Empty;

        /// <summary>
        /// 起始時
        /// </summary>
        public string B1_SHH { get; set; } = string.Empty;

        /// <summary>
        /// 起始分
        /// </summary>

        public string B1_SMM { get; set; } = string.Empty;

        /// <summary>
        /// 截止日(同起始日)
        /// </summary>
        public string B1_EDD { get; set; } = string.Empty;

        /// <summary>
        /// 截止時
        /// </summary>
        public string B1_EHH { get; set; } = string.Empty;

        /// <summary>
        /// 截止分
        /// </summary>

        public string B1_EMM { get; set; } = string.Empty;

        /// <summary>
        /// 加班時數
        /// </summary>
        public int B1_HOUR { get; set; }

        /// <summary>
        /// 加班申請別代號  1：加班 2：社外加班 3：補休假
        /// </summary>
        public int B1_CODE { get; set; }

        /// <summary>
        /// 加班理由
        /// </summary>
        public string B1_Reason { get; set; } = string.Empty;

        /// <summary>
        /// YYYMMDD    填卡日期民國年月日
        /// </summary>
        public string B1_WYYMMDD { get; set; } = string.Empty;

        /// <summary>
        /// YYYMMDD    結案核卡日期民國年月日
        /// </summary>
        public string? B1_AYYMMDD { get; set; }

        /// <summary>
        /// 狀態
        /// </summary>
        public int B1_STATUS { get; set; }

        /// <summary>
        /// 單號
        /// </summary>
        public string B1_SHEETNO { get; set; } = string.Empty;

        /// <summary>
        /// 序號
        /// </summary>
        public string B1_SERIALNO { get; set; } = string.Empty;

        /// <summary>
        /// 資料來源：
        /// FlowMaster：FlowMaster系統
        /// Attendance：本系統
        /// SECINC：系統承辦人
        /// </summary>
        public string B1_SOURCE { get; set; } = "Attendance";


        /// <summary>
        ///  加班補休假剩餘時數 <br />
        ///  加班資料新增時預設填入加班卡申請時數 <br/>
        ///  請假卡使用時扣除此欄位時數
        /// </summary>
        public int B1_HOURLEFT { get; set; }

        /// <summary>
        /// 起始時間
        /// </summary>
        public DateTime B1_StartDate { get; set; }


        /// <summary>
        /// 截止時間
        /// </summary>
        public DateTime B1_EndDate { get; set; }

        /// <summary>
        /// 加班日期類型編號
        /// </summary>
        public int B1_DateTypeId { get; set; }

        /// <summary>
        /// 填卡日期時間
        /// </summary>
        public DateTime B1_WDate { get; set; }

        /// <summary>
        /// 核卡日期時間
        /// </summary>
        public DateTime? B1_ADate { get; set; }

        /// <summary>
        /// 最後修改者員工編號
        /// </summary>
        public string? UpdatedEmpNo { get; set; }

        /// <summary>
        /// 最後修改者姓名
        /// </summary>
        public string? UpdatedName { get; set; }

        /// <summary>
        /// 最後修改日期時間
        /// </summary>
        public DateTime? UpdatedTime { get; set; }

        /// <summary>
        /// 最後修改來源IP 位址
        /// </summary>
        public string? UpdatedIP { get; set; }

        /// <summary>
        /// 最後修改來源電腦名稱
        /// </summary>
        public string? UpdatedHost { get; set; }

        /// <summary>
        /// List &lt;B1CardDto&gt;轉為B1Card，只能是相關的B1CardDto，aka 同一B1Card
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public static B1Card B1CardDtoToB1Card(List<B1CardDto> list)
        {
            B1Card b1Card = new B1Card();
            B1CardDto card = list[0];
            b1Card.FormUID = card.FormUID;
            b1Card.EmpNo = card.B1_EMPNO;
            b1Card.Reason = card.B1_Reason;
            b1Card.SheetNo = card.B1_SHEETNO;
            b1Card.B1_WDate = card.B1_WDate;
            b1Card.B1_ADate = card.B1_ADate;
            b1Card.UpdatedEmpNo = card.UpdatedEmpNo;
            b1Card.UpdatedName = card.UpdatedName;
            b1Card.UpdatedTime = card.UpdatedTime;
            b1Card.UpdatedIP = card.UpdatedIP;
            b1Card.UpdatedHost = card.UpdatedHost;
            b1Card.Source = card.B1_SOURCE;
            b1Card.Status = card.B1_STATUS;

            foreach (B1CardDto dto in list)
            {
                B1CardDetail detail = new B1CardDetail();
                detail.ID = dto.ID;
                detail.Project = dto.B1_PROJNO;
                detail.B1_CODE = dto.B1_CODE;
                int year = dto.B1_StartDate.Year;
                int month = dto.B1_StartDate.Month;
                int day = dto.B1_StartDate.Day;
                int hour = dto.B1_StartDate.Hour;
                int minute = dto.B1_StartDate.Minute;
                detail.StartTime = new DateTime(year, month, day, hour, minute, 0, DateTimeKind.Local);
                day = dto.B1_EndDate.Day;
                hour = dto.B1_EndDate.Hour;
                minute = dto.B1_EndDate.Minute;
                detail.EndTime = new DateTime(year, month, day, hour, minute, 0, DateTimeKind.Local);
                detail.Hour = dto.B1_HOUR;
                detail.SerialNo = int.Parse(dto.B1_SERIALNO);
                b1Card.TotalHours += detail.Hour;
                b1Card.Details.Add(detail);
            }
            return b1Card;
        }
    }
}
