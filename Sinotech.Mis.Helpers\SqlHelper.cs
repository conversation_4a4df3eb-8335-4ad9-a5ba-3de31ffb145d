﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Reflection;

namespace Sinotech.Mis.Helpers
{
    /// <summary>
    ///     SqlHelper 的摘要描述
    /// </summary>
    public static class SqlHelper
    {
        /// <summary>
        /// 將DataRow陣列轉為List
        /// </summary>
        /// <typeparam name="T">型別</typeparam>
        /// <param name="dataRows">資料列陣列</param>
        /// <returns>T型別的List</returns>
        public static List<T> ConvertDataTable<T>(DataRow[] dataRows)
        {
            List<T> data = new List<T>();
            foreach (DataRow row in dataRows)
            {
                T item = GetItem<T>(row);
                data.Add(item);
            }
            return data;
        }

        /// <summary>
        /// 將 Data Table 轉為 List
        /// </summary>
        /// <typeparam name="T">型別</typeparam>
        /// <param name="dataTable">資料表</param>
        /// <returns>T型別的List</returns>
        public static List<T> ConvertDataTable<T>(DataTable dataTable)
        {
            List<T> data = new List<T>();
            foreach (DataRow row in dataTable.Rows)
            {
                T item = GetItem<T>(row);
                data.Add(item);
            }
            return data;
        }

        /// <summary>
        /// 將 List 轉為 Data Table
        /// </summary>
        /// <typeparam name="T">型別</typeparam>
        /// <param name="enumerable"></param>
        /// <returns>DataTable</returns>
        public static DataTable CreateDataTable<T>(IEnumerable<T> enumerable)
        {
            Type type = typeof(T);
            var properties = type.GetProperties();

            DataTable dataTable = new DataTable();
            dataTable.TableName = typeof(T).FullName;
            foreach (PropertyInfo info in properties)
            {
                dataTable.Columns.Add(new DataColumn(info.Name, Nullable.GetUnderlyingType(info.PropertyType) ?? info.PropertyType));
            }

            foreach (T entity in enumerable)
            {
                object[] values = new object[properties.Length];
                for (int i = 0; i < properties.Length; i++)
                {
                    if (properties[i] != null)
                    {
                        object? obj = properties[i].GetValue(entity);
                        if (obj != null)
                        {
                            values[i] = obj;
                        }
                    }
                }
                dataTable.Rows.Add(values);
            }
            return dataTable;
        }


        /// <summary>
        /// Creates an object from the given data row
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="row"></param>
        /// <returns></returns>
        public static T CreateItemFromRow<T>(DataRow row) where T : new()
        {
            // create a new object
            T item = new T();
            // set the item
            SetItemFromRow(item, row);
            // return 
            return item;
        }


        /// <summary>
        /// Creates a enumerable of an object from the given data dataTable
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="tbl"></param>
        /// <returns></returns>
        public static List<T> CreateListFromTable<T>(DataTable tbl) where T : new()
        {
            // define return enumerable
            List<T> lst = new List<T>();

            // go through each row
            foreach (DataRow r in tbl.Rows)
            {
                // add to the enumerable
                lst.Add(CreateItemFromRow<T>(r));
            }

            // return the enumerable
            return lst;
        }

        /// <summary>
        /// DataTable 轉為 Dictionary
        /// </summary>
        /// <param name="dataTable">資料表</param>
        /// <returns>Dictionary</returns>
        public static Dictionary<object, IList<dynamic>> DataTable2Dictionary(DataTable? dataTable)
        {
            Dictionary<object, IList<dynamic>> dict = new Dictionary<dynamic, IList<dynamic>>();
            if (dataTable != null)
            {
                foreach (DataColumn column in dataTable.Columns)
                {
                    IList<dynamic?> ts = dataTable.AsEnumerable()
                                          .Select(r => r.Field<dynamic>(column.ToString()))
                                          .ToList();
                    if (ts != null)
                    {
                        dict.Add(column, (IList<dynamic>)ts);
                    }
                }
            }
            return dict;
        }

        /// <summary>
        /// Data Table 轉為 型別 T 的 List
        /// </summary>
        /// <typeparam name="T">型別</typeparam>
        /// <param name="dataTable">資料表</param>
        /// <returns></returns>
        public static List<T> DataTableToList<T>(this DataTable dataTable) where T : class, new()
        {
            List<T> list = new List<T>();
            try
            {
                foreach (var row in dataTable.AsEnumerable())
                {
                    T obj = new T();
                    foreach (PropertyInfo prop in obj.GetType().GetProperties())
                    {
                        try
                        {
                            if (row[prop.Name] != null && row[prop.Name] != DBNull.Value)
                            {
                                PropertyInfo? propertyInfo = obj.GetType().GetProperty(prop.Name);
                                if (propertyInfo != null)
                                {
                                    if (propertyInfo.PropertyType.IsEnum)
                                    {
                                        string? strTemp = row[prop.Name].ToString();
                                        if (strTemp != null)
                                        {
                                            propertyInfo.SetValue(obj, Enum.Parse(propertyInfo.PropertyType, strTemp));
                                        }
                                    }
                                    else
                                    {
                                        propertyInfo.SetValue(obj, Convert.ChangeType(row[prop.Name], propertyInfo.PropertyType), null);
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"{ex.Message} {ex.StackTrace}");
                        }
                    }
                    list.Add(obj);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"{ex.Message} {ex.StackTrace}");
            }
            return list;
        }

        /// <summary>
        /// 執行SQL Command，回傳影響筆數(Insert、Update、Delete, -1 代表失敗)<br />
        /// </summary>
        /// <param name="connection"></param>
        /// <param name="transaction"></param>
        /// <param name="sql">SQL語法字串</param>
        /// <param name="parameter">參數</param>
        /// <returns>回傳影響筆數</returns>
        public static int ExecuteNonQuery(SqlConnection connection, SqlTransaction transaction, string sql, SqlParameter parameter)
        {
            int affectedRows = -1;
            using (SqlCommand command = new SqlCommand(sql, connection))
            {
                command.Transaction = transaction;
                command.Parameters.Add(parameter);
                affectedRows = command.ExecuteNonQuery();
            }
            return affectedRows;
        }

        /// <summary>
        /// 執行SQL Command，回傳影響筆數(Insert、Update、Delete, -1 代表失敗)<br />
        /// </summary>
        /// <param name="connection"></param>
        /// <param name="transaction"></param>
        /// <param name="sql">SQL語法字串</param>
        /// <param name="parameters">參數List</param>
        /// <returns>回傳影響筆數</returns>
        public static int ExecuteNonQuery(SqlConnection connection, SqlTransaction transaction, string sql, List<SqlParameter> parameters)
        {
            int affectedRows = -1;

            using (SqlCommand command = new SqlCommand(sql, connection))
            {
                command.Transaction = transaction;
                if (parameters != null)
                {
                    foreach (SqlParameter param in parameters)
                    {
                        command.Parameters.Add(param);
                    }
                }
                affectedRows = command.ExecuteNonQuery();
            }
            return affectedRows;
        }

        /// <summary>
        ///     執行SQL Command(Insert、Update、Delete, -1代表失敗)
        /// </summary>
        /// <param name="connectionString">The connection string.</param>
        /// <param name="sql">SQL語法字串</param>
        /// <returns>回傳影響筆數</returns>
        public static int ExecuteSqlCommand(string connectionString, string sql)
        {
            int returnValue = -1;

            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                using (SqlCommand command = new SqlCommand(sql, conn))
                {
                    command.Connection.Open();
                    returnValue = command.ExecuteNonQuery();
                }
            }
            return returnValue;
        }

        /// <summary>
        ///     執行SQL Command(Insert、Update、Delete, -1代表失敗)，帶參數
        /// </summary>
        /// <param name="connectionString">The connection string.</param>
        /// <param name="sql">SQL語法字串</param>
        /// <param name="sp">Parameters</param>
        /// <returns>
        ///     回傳影響筆數
        /// </returns>
        public static int ExecuteSqlCommand(string connectionString, string sql, SqlParameter[] sp)
        {
            int returnValue = -1;

            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                using (SqlCommand command = new SqlCommand(sql, conn))
                {
                    if (sp != null)
                    {
                        foreach (SqlParameter param in sp)
                        {
                            command.Parameters.Add(param);
                        }
                    }

                    try
                    {
                        command.Connection.Open();
                        returnValue = command.ExecuteNonQuery();
                    }

                    catch (Exception ex)
                    {
                        Console.WriteLine($"{ex.Message} {ex.StackTrace}");
                        return returnValue;
                    }
                }
            }
            return returnValue;
        }

        /// <summary>
        /// <summary>執行SQL Command，回傳影響筆數(Insert、Update、Delete, -1 代表失敗)<br /></summary>
        /// </summary>
        /// <param name="connectionString">Connection String</param>
        /// <param name="sql">SQL語法字串</param>
        /// <returns>回傳影響筆數</returns>
        public static int ExecuteSqlNonQuery(string connectionString, string sql)
        {
            int returnValue = -1;

            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                using (SqlCommand command = new SqlCommand(sql, conn))
                {
                    command.Connection.Open();
                    returnValue = command.ExecuteNonQuery();
                }
            }
            return returnValue;
        }

        /// <summary>
        /// <summary>執行SQL Command，回傳影響筆數(Insert、Update、Delete, -1 代表失敗)<br /></summary>
        /// </summary>
        /// <param name="connectionString">Connection String</param>
        /// <param name="sql">SQL語法字串</param>
        /// <param name="parameter">Parameter</param>
        /// <returns>回傳影響筆數</returns>
        public static int ExecuteSqlNonQuery(string connectionString, string sql, SqlParameter parameter)
        {
            SqlParameter[] parameters = new SqlParameter[1];
            parameters[0] = parameter;
            return ExecuteSqlNonQuery(connectionString, sql, parameters);
        }

        /// <summary>執行SQL Command，回傳影響筆數(Insert、Update、Delete, -1 代表失敗)<br /></summary>
        /// <param name="conn"></param>
        /// <param name="transaction"></param>
        /// <param name="sql">SQL語法字串</param>
        /// <param name="parameter">Parameter</param>
        /// <returns>回傳影響筆數</returns>
        public static int ExecuteSqlNonQuery(SqlConnection conn, SqlTransaction transaction, string sql, SqlParameter parameter)
        {
            SqlParameter[] parameters = new SqlParameter[1];
            parameters[0] = parameter;
            return ExecuteSqlNonQuery(conn, transaction, sql, parameters);
        }

        /// <summary>
        /// <summary>執行SQL Command，回傳影響筆數(Insert、Update、Delete, -1 代表失敗)<br /></summary>
        /// </summary>
        /// <param name="connectionString">Connection String</param>
        /// <param name="sql">SQL語法字串</param>
        /// <param name="parameters">Parammeters</param>
        /// <returns>回傳影響筆數</returns>
        public static int ExecuteSqlNonQuery(string connectionString, string sql, SqlParameter[] parameters)
        {
            int returnValue = -1;

            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                using (SqlCommand command = new SqlCommand(sql, conn))
                {
                    if (parameters != null)
                    {
                        foreach (SqlParameter param in parameters)
                        {
                            command.Parameters.Add(param);
                        }
                    }
                    command.Connection.Open();
                    returnValue = command.ExecuteNonQuery();
                }
            }
            return returnValue;
        }

        /// <summary>
        /// <summary>執行SQL Command，回傳影響筆數(Insert、Update、Delete, -1 代表失敗)<br /></summary>
        /// </summary>
        /// <param name="connectionString">Connection String</param>
        /// <param name="sql">SQL語法字串</param>
        /// <param name="parameters">Parammeters</param>
        /// <returns>回傳影響筆數</returns>
        public static int ExecuteSqlNonQuery(string connectionString, string sql, List<SqlParameter> parameters)
        {
            int returnValue = -1;

            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                using (SqlCommand command = new SqlCommand(sql, conn))
                {
                    if (parameters != null)
                    {
                        foreach (SqlParameter param in parameters)
                        {
                            command.Parameters.Add(param);
                        }
                    }
                    command.Connection.Open();
                    returnValue = command.ExecuteNonQuery();
                }
            }
            return returnValue;
        }

        /// <summary>執行SQL Command，回傳影響筆數(Insert、Update、Delete, -1 代表失敗)<br /></summary>
        /// <param name="connection"></param>
        /// <param name="transaction"></param>
        /// <param name="sql">SQL語法字串</param>
        /// <param name="sp">Parammeters</param>
        /// <returns>回傳影響筆數</returns>
        public static int ExecuteSqlNonQuery(SqlConnection connection, SqlTransaction transaction, string sql, SqlParameter[] sp)
        {
            int returnValue = -1;

            using (SqlCommand command = new SqlCommand(sql, connection))
            {
                command.Transaction = transaction;
                if (sp != null)
                {
                    foreach (SqlParameter param in sp)
                    {
                        command.Parameters.Add(param);
                    }
                }
                returnValue = command.ExecuteNonQuery();
            }
            return returnValue;
        }

        /// <summary>執行SQL Command，回傳第一個資料值(Insert、Update、Delete, null代表失敗)</summary>
        /// <param name="connection"></param>
        /// <param name="transaction"></param>
        /// <param name="sql">SQL語法字串</param>
        /// <returns>回傳影響筆數</returns>
        public static object ExecuteSqlScalar(SqlConnection connection, SqlTransaction transaction, string sql)
        {
            object returnValue;

            using (SqlCommand command = new SqlCommand(sql, connection))
            {
                command.Transaction = transaction;
                returnValue = command.ExecuteScalar();
            }

            return returnValue;
        }

        /// <summary>執行SQL Command，回傳第一個資料值(Insert、Update、Delete, null代表失敗)，帶參數<br /></summary>
        /// <param name="connection"></param>
        /// <param name="transaction"></param>
        /// <param name="sql">SQL語法字串</param>
        /// <param name="sp">Parammeters</param>
        /// <returns>回傳影響筆數</returns>
        public static object? ExecuteSqlScalar(SqlConnection connection, SqlTransaction transaction, string sql, SqlParameter sp)
        {
            SqlParameter[] parameters = new SqlParameter[1];
            parameters[0] = sp;
            return ExecuteSqlScalar(connection, transaction, sql, parameters);
        }

        /// <summary>執行SQL Command，回傳影響筆數(Insert、Update、Delete, -1 代表失敗)<br /></summary>
        /// <param name="connection"></param>
        /// <param name="transaction"></param>
        /// <param name="sql">SQL語法字串</param>
        /// <param name="parameter">Parammeters</param>
        /// <returns>回傳影響筆數</returns>
        public static object? ExecuteSqlScalar(SqlConnection connection, SqlTransaction transaction, string sql, SqlParameter[] parameter)
        {
            object?
             returnValue = null;
            using (SqlCommand command = new SqlCommand(sql, connection))
            {
                command.Transaction = transaction;
                if (parameter != null)
                {
                    foreach (SqlParameter param in parameter)
                    {
                        command.Parameters.Add(param);
                    }
                }
                returnValue = command.ExecuteScalar();
            }
            return returnValue;
        }

        /// <summary>
        /// 執行 Stored Procedure
        /// </summary>
        /// <param name="connectionString">連線字串</param>
        /// <param name="storedProcedure">預存程序</param>
        /// <param name="parameters">參數陣列</param>
        /// <returns></returns>
        public static object ExecuteStoredProcedure(string connectionString, string storedProcedure, SqlParameter[] parameters)
        {
            object returnValue;
            SqlConnection connection = new SqlConnection(connectionString);
            connection.Open();
            using (SqlCommand cmd = new(storedProcedure, connection))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 36000;
                foreach (SqlParameter parameter in parameters)
                {
                    cmd.Parameters.Add(parameter);
                }
                returnValue = cmd.ExecuteScalar();
            }
            connection.Close();
            return returnValue;
        }

        /// <summary>
        /// 執行 Stored Procedure
        /// </summary>
        /// <param name="connectionString">連線字串</param>
        /// <param name="storedProcedure">預存程序</param>
        /// <param name="parameterList">參數List</param>
        /// <returns></returns>
        public static object ExecuteStoredProcedure(string connectionString, string storedProcedure, List<SqlParameter> parameterList)
        {
            object returnValue;
            SqlConnection connection = new SqlConnection(connectionString);
            connection.Open();
            using (SqlCommand cmd = new SqlCommand(storedProcedure, connection))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 36000;
                foreach (SqlParameter parameter in parameterList)
                {
                    cmd.Parameters.Add(parameter);
                }
                returnValue = cmd.ExecuteScalar();
            }
            connection.Close();
            return returnValue;
        }

        /// <summary>
        /// <summary>執行SQL Command，回傳影響筆數(Insert、Update、Delete, -1 代表失敗)<br /></summary>
        /// </summary>
        /// <param name="connectionString">Connection String</param>
        /// <param name="sql">SQL語法字串</param>
        /// <param name="parameters">參數List</param>
        /// <returns>回傳影響筆數</returns>
        public static int ExecuteStoredProcedureNonQuery(string connectionString, string sql, List<SqlParameter> parameters)
        {
            int returnValue = -1;

            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                using (SqlCommand command = new SqlCommand(sql, conn))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    if (parameters != null)
                    {
                        foreach (SqlParameter param in parameters)
                        {
                            command.Parameters.Add(param);
                        }
                    }
                    command.Connection.Open();
                    returnValue = command.ExecuteNonQuery();
                }
            }
            return returnValue;
        }

        /// <summary>Executes the update.</summary>
        /// <param name="connectionString">連線字串</param>
        /// <param name="sqlString">SQL字串</param>
        /// <param name="parameters"></param>
        /// <returns>
        ///   <br />
        /// </returns>
        public static int ExecuteUpdate(string connectionString, string sqlString, List<SqlParameter> parameters)
        {
            int numRowsAffected = -1;
            using (SqlConnection sqlConnection = new SqlConnection(connectionString))
            {
                using (SqlCommand mySqlCommand = new SqlCommand())
                {

                    mySqlCommand.Connection = sqlConnection;
                    mySqlCommand.Connection.Open();
                    mySqlCommand.CommandText = sqlString;
                    foreach (SqlParameter param in parameters)
                    {
                        mySqlCommand.Parameters.Add(param);
                    }

                    numRowsAffected = mySqlCommand.ExecuteNonQuery();
                }
            }
            return numRowsAffected;
        }

        /// <summary>
        ///     Executes the update.
        /// </summary>
        /// <param name="connectionString">連線字串</param>
        /// <param name="sqlString">SQL字串</param>
        /// <returns></returns>
        public static int ExecuteUpdate(string connectionString, string sqlString)
        {
            int numRowsAffected = -1;
            using (SqlConnection sqlConnection = new SqlConnection(connectionString))
            {
                using (SqlCommand mySqlCommand = new SqlCommand())
                {
                    mySqlCommand.Connection = sqlConnection;
                    mySqlCommand.Connection.Open();
                    mySqlCommand.CommandText = sqlString;
                    numRowsAffected = mySqlCommand.ExecuteNonQuery();
                }
            }
            return numRowsAffected;
        }

        /// <summary>
        /// Get Column string value from Row
        /// </summary>
        /// <param name="row">DataRow</param>
        /// <param name="columnName">欄位名稱</param>
        /// <returns></returns>
        public static DateTime? GetColumnDateTimeValue(DataRow row, string columnName)
        {
            DateTime? ret = null;
            if (row[columnName] != DBNull.Value)
            {
                ret = (DateTime)row[columnName];
            }
            return ret;
        }

        /// <summary>
        /// Get Column string value from Row
        /// </summary>
        /// <param name="row">DataRow</param>
        /// <param name="columnName">欄位名稱</param>
        /// <returns></returns>
        public static int? GetColumnIntValue(DataRow row, string columnName)
        {
            int? ret = null;
            if (row[columnName] != DBNull.Value)
            {
                ret = (int)row[columnName];
            }
            return ret;
        }

        /// <summary>
        /// Get Column string value from Row
        /// </summary>
        /// <param name="row">DataRow</param>
        /// <param name="columnName">欄位名稱</param>
        /// <returns></returns>
        public static string? GetColumnStringValue(DataRow row, string columnName)
        {
            string? ret = null;
            if (row[columnName] != DBNull.Value)
            {
                ret = (string)row[columnName];
            }
            return ret;
        }

        /// <summary>
        /// Return DataTable by StoredProcedure With Parameters
        /// </summary>
        /// <param name="connectionString">The connection string.</param>
        /// <param name="storedProcedureName">Stored Procedure's Name</param>
        /// <param name="sqlParameters">Parameters</param>
        /// <returns>
        /// DataSet
        /// </returns>
        public static DataSet GetDataSetByStoredProcedure(string connectionString, string storedProcedureName,
            List<SqlParameter> sqlParameters)
        {
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                using (SqlCommand cmd = new SqlCommand(storedProcedureName, conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandTimeout = 36000;

                    if (sqlParameters != null)
                    {
                        foreach (SqlParameter param in sqlParameters)
                        {
                            cmd.Parameters.Add(param);
                        }
                    }
                    SqlDataAdapter da = new SqlDataAdapter(cmd);
                    DataSet ds = new DataSet();
                    da.Fill(ds);
                    return ds;
                }
            }
        }

        /// <summary>The DataReaders.</summary>
        /// <param name="connectionString">The connection string.</param>
        /// <param name="sqlString">The SQL query string</param>
        /// <returns></returns>
        public static string GetDataString(string connectionString, string sqlString)
        {
            string ret = "";

            using (SqlConnection sqlConnection = new SqlConnection(connectionString))
            {

                using (SqlCommand command = new SqlCommand(sqlString))
                {
                    command.Connection = sqlConnection;
                    command.CommandTimeout = 36000;
                    sqlConnection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        for (int i = 0; i < reader.FieldCount - 1; i++)
                        {
                            ret = string.Concat(ret, reader.GetName(i), "`");
                        }
                        ret = string.Concat(ret, reader.GetName(reader.FieldCount - 1), "#");
                        while (reader.Read())
                        {
                            for (int i = 0; i < reader.FieldCount - 1; i++)
                            {
                                ret = string.Concat(ret, Convert.IsDBNull(reader.GetValue(i)) ? "" : reader.GetValue(i), "`");
                            }
                            ret = string.Concat(ret,
                                Convert.IsDBNull(reader.GetValue(reader.FieldCount - 1))
                                    ? ""
                                    : reader.GetValue(reader.FieldCount - 1), "#");
                        }
                    }
                }
            }
            return ret;
        }

        /// <summary>Gets the data dataTable.</summary>
        /// <param name="connectionString">連線字串</param>
        /// <param name="sqlString">SQL字串</param>
        /// <returns>資料表</returns>
        public static DataTable GetDataTable(string connectionString, string sqlString)
        {
            DataTable dt = new DataTable();
            using (SqlConnection sqlConnection = new SqlConnection(connectionString))
            {
                using (SqlCommand cmd = new SqlCommand(sqlString, sqlConnection))
                {
                    cmd.CommandTimeout = 36000;
                    sqlConnection.Open();
                    using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                    {
                        da.Fill(dt);
                    }
                }
            }
            return dt;
        }

        /// <summary>
        /// Return DataTable through DataBase with Parameters
        /// </summary>
        /// <param name="connectionString">連線字串</param>
        /// <param name="sqlString">SQL字串</param>
        /// <param name="parameters">參數List</param>
        /// <returns>
        ///     DataTable
        /// </returns>
        public static DataTable GetDataTable(string connectionString, string sqlString, List<SqlParameter> parameters)
        {
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                using (SqlCommand cmd = new SqlCommand(sqlString, conn))
                {
                    cmd.CommandTimeout = 36000;

                    if (parameters != null)
                    {
                        foreach (SqlParameter param in parameters)
                        {
                            cmd.Parameters.Add(param);
                        }
                    }

                    DataTable dt = new DataTable();
                    conn.Open();
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        dt.Load(reader);
                    }
                    return dt;
                }
            }
        }

        /// <summary>Gets the data dataTable.</summary>
        /// <param name="connection">連線物件</param>
        /// <param name="transaction">交易物件</param>
        /// <param name="sqlString">SQL字串</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public static DataTable GetDataTable(SqlConnection connection, SqlTransaction transaction, string sqlString)
        {
            DataTable dt = new DataTable();
            using (SqlCommand cmd = new SqlCommand(sqlString, connection))
            {
                cmd.CommandTimeout = 36000;
                using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                {
                    da.Fill(dt);
                }
            }
            return dt;
        }

        /// <summary>Return DataTable through DataBase with Parameter</summary>
        /// <param name="connection">連線物件</param>
        /// <param name="transaction">交易物件</param>
        /// <param name="sqlString">SQL字串</param>
        /// <param name="parameter">參數</param>
        /// <returns>DataTable</returns>
        public static DataTable GetDataTable(SqlConnection connection, SqlTransaction transaction, string sqlString, SqlParameter parameter)
        {
            SqlParameter[] parameters = new SqlParameter[1];
            parameters[0] = parameter;
            return GetDataTable(connection, transaction, sqlString, parameters);
        }

        /// <summary>
        /// 取得資料表
        /// </summary>
        /// <param name="connectionString">連線字串</param>
        /// <param name="sqlString">SQL字串</param>
        /// <param name="parameter">參數</param>
        /// <returns></returns>
        public static DataTable GetDataTable(string connectionString, string sqlString, SqlParameter parameter)
        {
            SqlParameter[] parameters = new SqlParameter[1];
            parameters[0] = parameter;
            return GetDataTable(connectionString, sqlString, parameters);
        }


        /// <summary>Gets the data dataTable.</summary>
        /// <param name="connection">The connection.</param>
        /// <param name="transaction"></param>
        /// <param name="sqlString">The SQL command.</param>
        /// <param name="parameters">The SQL Parameters.</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public static DataTable GetDataTable(SqlConnection connection, SqlTransaction transaction, string sqlString, SqlParameter[] parameters)
        {
            using (SqlCommand cmd = new SqlCommand(sqlString, connection))
            {
                cmd.Transaction = transaction;
                cmd.CommandTimeout = 36000;
                if (parameters != null)
                {
                    foreach (SqlParameter parameter in parameters)
                    {
                        cmd.Parameters.Add(parameter);
                    }
                }
                DataTable dt = new DataTable();
                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    dt.Load(reader);
                }
                return dt;
            }
        }


        /// <summary>
        /// Return DataTable through DataBase with Parameters
        /// </summary>
        /// <param name="connectionString">The connection string.</param>
        /// <param name="sqlString">SQL command</param>
        /// <param name="parameters">Parameters</param>
        /// <returns>
        ///     DataTable
        /// </returns>
        public static DataTable GetDataTable(string connectionString, string sqlString, SqlParameter[] parameters)
        {
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                using (SqlCommand cmd = new SqlCommand(sqlString, conn))
                {
                    cmd.CommandTimeout = 36000;

                    if (parameters != null)
                    {
                        foreach (SqlParameter param in parameters)
                        {
                            cmd.Parameters.Add(param);
                        }
                    }

                    DataTable dt = new DataTable();
                    conn.Open();
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        dt.Load(reader);
                    }
                    return dt;
                }
            }
        }

        /// <summary>Gets the data dataTable.</summary>
        /// <param name="connection">The connection.</param>
        /// <param name="transaction"></param>
        /// <param name="sqlString">The SQL command.</param>
        /// <param name="parameterList">The SQL Parameters</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public static DataTable GetDataTable(SqlConnection connection, SqlTransaction transaction, string sqlString, List<SqlParameter> parameterList)
        {
            using (SqlCommand cmd = new SqlCommand(sqlString, connection))
            {
                cmd.Transaction = transaction;
                cmd.CommandTimeout = 36000;

                if (parameterList != null)
                {
                    foreach (SqlParameter param in parameterList)
                    {
                        cmd.Parameters.Add(param);
                    }
                }

                DataTable dt = new DataTable();
                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    dt.Load(reader);
                }
                return dt;
            }
        }

        /// <summary>
        /// Return DataTable by StoredProcedure With Param
        /// </summary>
        /// <param name="connectionString">The connection string.</param>
        /// <param name="storedProcedureName">StoredProcedure's Name</param>
        /// <param name="sqlParam">The SQL parameter.</param>
        /// <returns>
        /// DataTable
        /// </returns>
        public static DataTable GetDataTableByStoredProcedure(string connectionString, string storedProcedureName,
            SqlParameter sqlParam)
        {
            SqlParameter[] sqlParameters = new SqlParameter[1];
            sqlParameters[0] = sqlParam;
            return GetDataTableByStoredProcedure(connectionString, storedProcedureName, sqlParameters);
        }

        /// <summary>
        /// Return DataTable by StoredProcedure With Parameters
        /// </summary>
        /// <param name="connectionString">The connection string.</param>
        /// <param name="storedProcedureName">StoredProcedure's Name</param>
        /// <param name="sqlParameters">Parameters</param>
        /// <returns>
        /// DataTable
        /// </returns>
        public static DataTable GetDataTableByStoredProcedure(string connectionString, string storedProcedureName,
            SqlParameter[] sqlParameters)
        {
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                using (SqlCommand cmd = new SqlCommand(storedProcedureName, conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandTimeout = 36000;

                    if (sqlParameters != null)
                    {
                        foreach (SqlParameter param in sqlParameters)
                        {
                            cmd.Parameters.Add(param);
                        }
                    }
                    DataTable dt = new DataTable();
                    conn.Open();
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        dt.Load(reader);
                    }
                    return dt;
                }
            }
        }

        /// <summary>
        /// Return DataTable by StoredProcedure With Parameters
        /// </summary>
        /// <param name="connectionString">The connection string.</param>
        /// <param name="storedProcedureName">StoredProcedure's Name</param>
        /// <param name="sqlParameters">Parameters</param>
        /// <returns>
        /// DataTable
        /// </returns>
        public static DataTable GetDataTableByStoredProcedure(string connectionString, string storedProcedureName,
            List<SqlParameter> sqlParameters)
        {
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                using (SqlCommand cmd = new SqlCommand(storedProcedureName, conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandTimeout = 36000;

                    if (sqlParameters != null)
                    {
                        foreach (SqlParameter param in sqlParameters)
                        {
                            cmd.Parameters.Add(param);
                        }
                    }
                    DataTable dt = new DataTable();
                    conn.Open();
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        dt.Load(reader);
                    }
                    return dt;
                }
            }
        }

        /// <summary>
        /// Return DataTable by StoredProcedure
        /// </summary>
        /// <param name="connectionString">The connection string.</param>
        /// <param name="storedProcedureName">Name of the stored procedure.</param>
        /// <returns>
        /// DataTable
        /// </returns>
        public static DataTable GetDataTableByStoredProcedure(string connectionString, string storedProcedureName)
        {
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                SqlCommand cmd = new SqlCommand(storedProcedureName, conn);
                cmd.CommandTimeout = 36000;
                cmd.CommandType = CommandType.StoredProcedure;

                DataTable dt = new DataTable();
                conn.Open();
                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    dt.Load(reader);
                }
                return dt;
            }
        }

        /// <summary>Gets the fields.</summary>
        /// <param name="connectionString">The connection string.</param>
        /// <param name="sqlString">The SQL command string.</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public static DataSet GetFields(string connectionString, string sqlString)
        {
            DataSet myDataSet = new DataSet();
            using (SqlConnection sqlConnection = new SqlConnection(connectionString))
            {
                SqlDataAdapter mySqlDataAdapter;
                mySqlDataAdapter = new SqlDataAdapter(sqlString, sqlConnection);
                mySqlDataAdapter.SelectCommand.CommandTimeout = 36000;
                mySqlDataAdapter.Fill(myDataSet, "Result");
            }
            return myDataSet;
        }

        /// <summary>
        ///     執行SQL Command，回傳第一個資料值(Insert、Update、Delete, null代表失敗)
        /// </summary>
        /// <param name="connectionString"></param>
        /// <param name="sql">SQL語法字串</param>
        /// <returns>回傳影響筆數</returns>
        public static object GetFieldValue(string connectionString, string sql)
        {
            object returnValue;

            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                using (SqlCommand command = new SqlCommand(sql, conn))
                {
                    command.Connection.Open();
                    returnValue = command.ExecuteScalar();
                }
            }
            return returnValue;
        }

        /// <summary>
        /// 執行SQL Command，回傳第一個資料值(Insert、Update、Delete, null代表失敗)，帶參數
        /// </summary>
        /// <param name="connectionString"></param>
        /// <param name="sql">SQL語法字串</param>
        /// <param name="prarameter">Parameter</param>
        /// <returns>回傳影響筆數</returns>
        public static object? GetFieldValue(string connectionString, string sql, SqlParameter prarameter)
        {
            SqlParameter[] parameters = new SqlParameter[1];
            parameters[0] = prarameter;
            return GetFieldValue(connectionString, sql, parameters);
        }

        /// <summary>執行SQL Command，回傳第一個資料值(Insert、Update、Delete, null代表失敗)，帶參數<br /></summary>
        /// <param name="connectionString">Connection String</param>
        /// <param name="sql">SQL語法字串</param>
        /// <param name="parameters">Parammeters</param>
        /// <returns>回傳影響筆數</returns>
        public static object? GetFieldValue(string connectionString, string sql, SqlParameter[] parameters)
        {
            object? returnValue = null;

            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                using (SqlCommand command = new SqlCommand(sql, conn))
                {
                    if (parameters != null)
                    {
                        foreach (SqlParameter param in parameters)
                        {
                            command.Parameters.Add(param);
                        }
                    }
                    command.Connection.Open();
                    returnValue = command.ExecuteScalar();
                }
            }
            return returnValue;
        }

        /// <summary>執行SQL Command，回傳第一個資料值</summary>
        /// <param name="connection">The connection.</param>
        /// <param name="transaction"></param>
        /// <param name="sqlString">The SQL command.</param>
        /// <param name="parameters">The SQL Parameter.</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public static object? GetFieldValue(SqlConnection connection, SqlTransaction transaction, string sqlString, SqlParameter[] parameters)
        {
            object? returnValue = null;
            using (SqlCommand command = new SqlCommand(sqlString, connection, transaction))
            {
                if (parameters != null)
                {
                    foreach (SqlParameter param in parameters)
                    {
                        command.Parameters.Add(param);
                    }
                }
                returnValue = command.ExecuteScalar();
            }
            return returnValue;
        }

        /// <summary>
        /// 取得字串型別欄位值
        /// </summary>
        /// <param name="connectionString">連線字串</param>
        /// <param name="sqlString">SQL字串</param>
        /// <returns>字串</returns>
        public static string? GetValueString(string connectionString, string sqlString)
        {
            string? ret = "";
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                using (SqlCommand command = new SqlCommand(sqlString))
                {
                    command.CommandTimeout = 36000;
                    command.Connection = connection;
                    connection.Open();
                    using (SqlDataReader dataReader = command.ExecuteReader())
                    {
                        if (dataReader.Read())
                        {
                            ret = dataReader.GetValue(0).ToString();
                        }
                    }
                }
            }
            return ret;
        }

        /// <summary>
        /// Set Item value From DataRow
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="item"></param>
        /// <param name="row"></param>
        public static void SetItemFromRow<T>(T item, DataRow row) where T : new()
        {
            // go through each column
            foreach (DataColumn c in row.Table.Columns)
            {
                if (item != null)
                {
                    // find the property for the column
                    PropertyInfo? p = item.GetType().GetProperty(c.ColumnName);

                    // if exists, set the value
                    if (p != null && row[c] != DBNull.Value)
                    {
                        p.SetValue(item, row[c], null);
                    }
                }
            }
        }

        /// <summary>
        /// Return DataTable by StoredProcedure With Parameters
        /// </summary>
        /// <param name="connectionString">The connection string.</param>
        /// <param name="storedProcedureName">Stored Procedure's Name</param>
        /// <param name="sqlParameters">Parameters</param>
        /// <returns>
        /// DataSet
        /// </returns>
        public static DataSet StoredProcedureToDataSet(string connectionString, string storedProcedureName,
            SqlParameter[] sqlParameters)
        {
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                using (SqlCommand cmd = new SqlCommand(storedProcedureName, conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandTimeout = 36000;

                    if (sqlParameters != null)
                    {
                        foreach (SqlParameter param in sqlParameters)
                        {
                            cmd.Parameters.Add(param);
                        }
                    }
                    SqlDataAdapter da = new SqlDataAdapter(cmd);
                    DataSet ds = new DataSet();
                    da.Fill(ds);
                    return ds;
                }
            }
        }

        /// <summary>
        /// 由預存程序取出返回狀態值與影響列數
        /// </summary>
        /// <param name="connectionString"></param>
        /// <param name="storedProcedureName">預存程序名稱</param>
        /// <param name="sqlParameters">預存程序參數，不必指定回傳參數</param>
        /// <param name="affectedRecords">回傳的影響列數</param>
        /// <returns></returns>
        public static int GetStatusFromStoredProcedure(string connectionString, string storedProcedureName,
            List<SqlParameter> sqlParameters, out int affectedRecords)
        {
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                using (SqlCommand cmd = new SqlCommand(storedProcedureName, conn))
                {
                    try
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandTimeout = 36000;

                        if (sqlParameters != null)
                        {
                            foreach (SqlParameter param in sqlParameters)
                            {
                                cmd.Parameters.Add(param);
                            }
                        }

                        SqlParameter returnValue = cmd.Parameters.Add("@" + Guid.NewGuid().ToString(), SqlDbType.Int);
                        returnValue.Direction = ParameterDirection.ReturnValue;

                        conn.Open();

                        affectedRecords = cmd.ExecuteNonQuery();
                        return (int)returnValue.Value;
                    }
                    catch
                    {
                        throw;
                    }
                    finally
                    {
                        conn.Close();
                    }
                }
            }
        }

        ///// <summary>
        ///// 由預存程序取出返回狀態值與影響列數
        ///// </summary>
        ///// <param name="connectionString"></param>
        ///// <param name="storedProcedureName">預存程序名稱</param>
        ///// <param name="sqlParameters">預存程序參數，不必指定回傳參數</param>
        ///// <param name="affectedRecords">回傳的影響列數</param>
        ///// <returns></returns>
        //public static int ExecuteNonQuery(string connectionString, string storedProcedureName,
        //    List<SqlParameter> sqlParameters, out int affectedRecords)
        //{
        //    using (SqlConnection conn = new SqlConnection(connectionString))
        //    {
        //        using (SqlCommand cmd = new SqlCommand(storedProcedureName, conn))
        //        {
        //            try
        //            {
        //                cmd.CommandType = CommandType.StoredProcedure;
        //                cmd.CommandTimeout = 36000;

        //                if (sqlParameters != null)
        //                {
        //                    foreach (SqlParameter param in sqlParameters)
        //                    {
        //                        cmd.Parameters.Add(param);
        //                    }
        //                }

        //                SqlParameter returnValue = cmd.Parameters.Add("@" + Guid.NewGuid().ToString(), SqlDbType.Int);
        //                returnValue.Direction = ParameterDirection.ReturnValue;

        //                conn.Open();

        //                affectedRecords = cmd.ExecuteNonQuery();
        //                return (int)returnValue.Value;
        //            }
        //            catch
        //            {
        //                throw;
        //            }
        //            finally
        //            {
        //                conn.Close();
        //            }
        //        }
        //    }
        //}

        /// <summary>
        /// Get Item from DataRow
        /// </summary>
        /// <typeparam name="T">型別</typeparam>
        /// <param name="dr">DataRow</param>
        /// <returns>型別T物件</returns>
        private static T GetItem<T>(DataRow dr)
        {
            Type temp = typeof(T);
            T obj = Activator.CreateInstance<T>();

            foreach (DataColumn column in dr.Table.Columns)
            {
                foreach (PropertyInfo propertyInfo in temp.GetProperties())
                {
                    if (propertyInfo.Name == column.ColumnName)
                    {
                        if (dr[column.ColumnName] == DBNull.Value)
                        {
                            propertyInfo.SetValue(obj, null);
                        }
                        else
                        {
                            if (column.ColumnName != null && dr[column.ColumnName] != null)
                            {
                                object col = dr[column.ColumnName];
                                string? columeValue = col.ToString();
                                if (columeValue != null)
                                {
                                    if (propertyInfo.PropertyType.Name == "Char")
                                    {
                                        propertyInfo.SetValue(obj, char.Parse(columeValue), null);
                                    }
                                    else if (propertyInfo.PropertyType.Name == "Int32")
                                    {
                                        propertyInfo.SetValue(obj, int.Parse(columeValue), null);
                                    }
                                    else
                                    {
                                        propertyInfo.SetValue(obj, dr[column.ColumnName], null);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return obj;
        }
    }
}
