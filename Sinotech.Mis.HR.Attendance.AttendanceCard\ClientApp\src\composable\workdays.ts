import { ref } from 'vue'
import { GET_EMPLOYEEWORKDAYSDATERANGE_URL } from '../api/appUrl'
import type { WorkdayType, CalendarDayType } from '../api/appType'

/**
 * 日曆天資料
 * @returns 
 */
export function useWorkday() {
  const workdays = ref<CalendarDayType>({})
  const calendarLoaded = ref<boolean>(false)

  const onGetWorkdaysDateRange = async (startDate: Date, endDate: Date, empNo: string, signal: AbortSignal): Promise<Array<WorkdayType>> => {
    const params = new URLSearchParams({
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      empNo: empNo
    })
    try {
      const res: Response = await fetch(GET_EMPLOYEEWORKDAYSDATERANGE_URL + '?' + params, {
        method: 'GET',
        signal: signal
      })
      if (!res.ok) {
        throw new Error(res.status.toString())
      }
      const jsonData = await res.json()
      return jsonData.map((e: any) => {
        return {
          shiftId: e.ShiftId,
          workDate: e.WorkDate,
          dayType: e.DayType,
          typeName: e.TypeName,
          dayOff: e.DayOff,
          arrivalTime: e.ArrivalTime,
          departureTime: e.DepartureTime,
          flexibleArrivalBefore: e.FlexibleArrivalBefore,
          flexibleArrivalAfter: e.FlexibleArrivalAfter,
          flexibleDepartureBefore: e.FlexibleDepartureBefore,
          flexibleDepartureAfter: e.FlexibleDepartureAfter,
          middayBreakStart: e.MiddayBreakStart,
          middayBreakEnd: e.MiddayBreakEnd,
          morningRestStart: e.MorningRestStart,
          morningRestEnd: e.MorningRestEnd,
          afternoonRestStart: e.AfternoonRestStart,
          afternoonRestEnd: e.AfternoonRestEnd,
          workHours: e.WorkHours,
          weekDay: e.WeekDay,
          comment: e.Comment
        }
      })
    } catch (err: unknown) {
      throw err
    }
  }

  const onLoadCalendarData = async (startDate: Date, endDate: Date, empNo: string, signal: AbortSignal): Promise<void> => {
    calendarLoaded.value = false
  
    const result: CalendarDayType = {}
    workdays.value = {}
    const workdaysDateRange = await onGetWorkdaysDateRange(startDate, endDate, empNo, signal)
    if (Array.isArray(workdaysDateRange)) {
      workdaysDateRange.forEach((e: WorkdayType) => {
        const date = new Date(e.workDate)
        result['d' + date.getFullYear() + (date.getMonth() + 1).toString().padStart(2, '0') + date.getDate().toString().padStart(2, '0')] = {
          dayType: e.dayType
        }
      })
    } else {
      throw new Error(workdaysDateRange)
    }

    workdays.value = result
    calendarLoaded.value = true
  }
  
  return { workdays, calendarLoaded, onGetWorkdaysDateRange, onLoadCalendarData }
}