{"metadata": [{"src": [{"files": ["Sinotech.Mis.Common/**.csproj", "Sinotech.Mis.Utilities.DataAccess.Interfaces/**.csproj", "Sinotech.Mis.Extensions.Configuration/**.csproj", "Sinotech.Mis.Helpers/**.csproj", "Sinotech.Mis.Utilities/**.csproj", "Sinotech.Mis.Serilog.Utilities/**.csproj", "Sinotech.Mis.HR.Attendance.Common/**.csproj", "Sinotech.Mis.HR.Attendance.Utilities/**.csproj", "Sinotech.Mis.HR.Attendance.DataAccess.Interfaces/**.csproj", "Sinotech.Mis.Utilities.DataAccess.Ado/**.csproj", "Sinotech.Mis.HR.Attendance.DataAccess.Ado/**.csproj", "Sinotech.Mis.HR.Attendance.BusinessLogic/**.csproj", "Sinotech.Mis.HR.Attendance.Extensions.ServiceCollection/**.csproj", "Sinotech.Mis.HR.Attendance.AttendanceCard/**.csproj"], "filter": "filterConfig.yml", "src": "../", "exclude": ["**/ViteHelper.cs", "**/Error.cshtml", "**/Error.cshtml.cs"]}], "dest": "api", "shouldSkipMarkup": true, "includePrivateMembers": false, "disableGitFeatures": false, "disableDefaultFilter": false, "namespaceLayout": "flattened"}], "build": {"content": [{"files": ["api/**.yml", "api/index.md"]}, {"files": ["articles/**.md", "articles/**/toc.yml", "toc.yml", "*.md"]}], "resource": [{"files": ["images/**"]}], "overwrite": [{"files": ["apidoc/**.md"], "exclude": ["obj/**", "_site/**"]}], "dest": "_site", "globalMetadataFiles": [], "fileMetadataFiles": [], "template": ["default", "templates/material"], "postProcessors": [], "noLangKeyword": false, "keepFileLink": false, "disableGitFeatures": false}}