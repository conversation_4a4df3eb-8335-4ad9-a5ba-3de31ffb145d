<template>
  <div class="row border mx-0">
    <div class="col-auto py-2">
      <FileUpload
        name="files"
        mode="basic"
        invalidFileTypeMessage="{0}：僅供上傳文字、圖片或PDF檔"
        invalidFileSizeMessage="{0}：僅供上傳10MB以下的檔案"
        :class="disabled ? 'p-disabled' : ''"
        :accept="UPLOADED_FILE_MIME.toString()"
        :maxFileSize="UPLOADED_FILESIZE_MAX"
        :url="POST_FILEUPLOAD_URL"
        :multiple="true"
        :customUpload="true"
        :auto="true"
        :showUploadButton="false"
        :showCancelButton="false"
        :uploadIcon="'bi bi-plus-lg me-1'"
        :disabled="disabled"
        @uploader="fileUpload"
      />
    </div>
    <div class="col-8 col-md-9 col-xl-10 py-2">
      <slot name="message" />
    </div>

    <div
      v-if="fileSizeIsZeroMessage.length > 0"
      class="alert alert-danger alert-dismissible fade show my-0"
      role="alert"
    >
      {{ fileSizeIsZeroMessage + '：僅供上傳超過0位元以上的檔案' }}
      <button
        type="button"
        class="btn-close"
        data-bs-dismiss="alert"
        aria-label="Close"
      />
    </div>
    <div
      v-if="fileSizeUpperBoundMessage.length > 0"
      class="alert alert-danger alert-dismissible fade show my-0"
      role="alert"
    >
      {{ fileSizeUpperBoundMessage + '：僅供上傳總量50MB以下的檔案' }}
      <button
        type="button"
        class="btn-close"
        data-bs-dismiss="alert"
        aria-label="Close"
      />
    </div>
  </div>

  <template v-if="filesArray.length > 0">
    <ol class="border my-0">
      <li
        v-for="(file, index) in filesArray"
        :key="index"
      >
        {{ file.fileName }}
        <i
          class="bi bi-x-circle"
          role="button"
          @click="fileRemove(file, index)"
        />
      </li>
    </ol>
  </template>

  <div class="text-secondary px-1">
    <small>附件格式：限PDF、Word、Excel、文字檔或影像檔(png、jpg、gif)</small>
    <br>
    <small>檔案大小：單一檔案上限10MB，總量上限50MB</small>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useToast } from 'primevue/usetoast'
import { useAbortController } from '../composable/abortController'
import FileUpload from 'primevue/fileupload'
import { POST_FILEUPLOAD_URL } from '../api/appUrl'
import { UPLOADED_FILE_MIME, UPLOADED_FILESIZE_MAX, SYSTEM_ERROR_MESSAGE } from '../api/appConst'
import type { Ref } from 'vue'
import type { FileUploadUploaderEvent } from 'primevue/fileupload'
import type { FileApiType } from '../api/appType'

const toast = useToast()
const { abortController, fetchErrorHandler, abortListener } = useAbortController()

defineProps({
  disabled: {
    type: Boolean,
    default: false
  }
})
const emits = defineEmits(['update:modelValue'])

const filesArray = ref<Array<FileApiType>>([])
const fileSizeUpperBound = ref<number>(0)
const fileSizeUpperBoundMessage = ref<string>('')
const fileSizeIsZeroMessage = ref<string>('')

const fileUpload = (event: FileUploadUploaderEvent): void => {
  const files: Array<File> = event.files as Array<File>
  const formData: FormData = new FormData()
  fileSizeIsZeroMessage.value = ''

  files.forEach((file: File) => {
    if (file.size === 0) {
      formatMessage(fileSizeIsZeroMessage, file)
    } else if (fileSizeUpperBound.value > 5 * UPLOADED_FILESIZE_MAX) {
      // 檔案大小總量上限50MB
      formatMessage(fileSizeUpperBoundMessage, file)
    } else {
      fileSizeUpperBound.value += file.size
      formData.append('files', file)
    }
  })

  fetch(POST_FILEUPLOAD_URL, {
    method: 'POST',
    body: formData,
    signal: abortController.signal
  }).then((res: Response): Promise<{ list: Array<FileApiType> }> => {
    if (!res.ok) {
      throw new Error(res.status.toString())
    }
    return res.json()
  }).then((res: { list: Array<FileApiType> | undefined }): void => {
    if (res?.list) {
      filesArray.value = filesArray.value.concat(res.list)
      emits('update:modelValue', filesArray.value)
    } else {
      throw new Error('上傳附件失敗')
    }
  }).catch((err: Error): void => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })
}

const fileRemove = (file: FileApiType, index: number): void => {
  filesArray.value.splice(index, 1)
  fileSizeUpperBound.value -= file.size
  emits('update:modelValue', filesArray.value)
}

const formatMessage = (msgRef: Ref<string>, file: File): void => {
  if (msgRef.value.length > 0) {
    msgRef.value += ', ' + file.name
  } else {
    msgRef.value += file.name
  }
}

onMounted((): void => {
  abortListener()
})
</script>