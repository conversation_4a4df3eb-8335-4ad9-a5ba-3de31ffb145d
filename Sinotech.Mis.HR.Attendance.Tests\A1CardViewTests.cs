﻿using Xunit;

namespace Sinotech.Mis.HR.Attendance.Common.Tests
{
    public class A1CardViewTests
    {
        [Fact]
        public void SetApplicationType_ShouldSetApplicationTypeBasedOnA1_NN()
        {
            // Arrange
            var cardView = new A1CardView();

            // Act & Assert
            cardView.A1_NN = '1';
            cardView.SetApplicationType();
            Assert.Equal("上旬", cardView.ApplicationType);

            cardView.A1_NN = '2';
            cardView.SetApplicationType();
            Assert.Equal("中旬", cardView.ApplicationType);

            cardView.A1_NN = '3';
            cardView.SetApplicationType();
            Assert.Equal("下旬", cardView.ApplicationType);
        }
    }
}
