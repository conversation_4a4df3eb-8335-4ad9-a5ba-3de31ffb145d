using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.Utilities;
using System;
using System.Collections.Generic;

namespace Sinotech.Mis.HR.Attendance.C1CardObjs
{
    /// <summary>
    /// 假別基礎類別
    /// </summary>
    public abstract class C1CardBase
    {
        /// <summary>
        /// 檢查結果: 重覆請假
        /// </summary>
        protected readonly CardCheckResult ResultOverlappedLeaves = new CardCheckResult(3000306, CardStatusEnum.Error, AttendanceParameters.LeaveTimeOverlay);

        /// <summary>
        /// 檢查結果: 請假截止時間有誤
        /// </summary>
        protected readonly CardCheckResult ResultEndTimeError = new CardCheckResult(3000316, CardStatusEnum.Error, AttendanceParameters.LeaveEndTimeError);

        /// <summary>
        /// 檢查結果: 請假時數為0
        /// </summary>
        protected readonly CardCheckResult ResultTimeZeroError = new CardCheckResult(3000319, CardStatusEnum.Error, AttendanceParameters.Leave_Time_Zero);

        /// <summary>
        /// 檢查結果: 請假時間超過正常工時
        /// </summary>
        protected readonly CardCheckResult ResultTimeLargerThanWorkHours = new CardCheckResult(3000320, CardStatusEnum.Error, AttendanceParameters.Leave_Time_Larger_Than_Working_Hours);

        /// <summary>
        /// 檢查結果: 累計請假時間超過正常工時
        /// </summary>
        protected readonly CardCheckResult ResultAccumulatedTimeLargerThanWorkHours = new CardCheckResult(3000321, CardStatusEnum.Error, AttendanceParameters.Accumulated_Leave_Time_Larger_Than_Working_Hours);


        /// <summary>
        /// 請假 物件
        /// </summary>
        protected C1Card _c1Card;

        protected readonly IC1CardBo _c1CardBo;

        internal const int CodeOk = 3000100;
        internal CardCheckResult ResultOk { get => _resultOk; }

        protected readonly CardCheckResult _resultOk = new CardCheckResult(CodeOk, CardStatusEnum.Success, string.Empty);

        /// <summary>
        ///   <see cref="C1CardBase" /> 建構子
        /// </summary>
        /// <param name="c1Card">請假 DTO物件</param>
        /// <param name="c1CardBo">請假商業物件</param>
        internal C1CardBase(C1Card c1Card, IC1CardBo c1CardBo)
        {
            _c1Card = c1Card;
            _c1CardBo = c1CardBo;

            List<LeaveKind> leaveKinds = c1CardBo.GetLeaveKinds();
            LeaveKind? kind = leaveKinds.Find(e => e.Number == c1Card.LeaveNumber);
            if (kind != null)
            {
                this.Kind = kind;
            }

            c1CardBo.GetCertificateRequired(c1Card);
            CalculateHours();
        }

        /// <summary>
        /// 檢查是否能請此假
        /// </summary>
        /// <returns></returns>
        internal abstract CardCheckResult CanTakeThisLeave();

        /// <summary>
        /// 檢查基本資料
        /// </summary>
        /// <returns></returns>
        internal CardCheckResult CheckGeneralRules()
        {
            // 基本檢查
            CardCheckResult result = ResultOk;

            // 檢查是否為在職員工
            if (!_c1CardBo.IsEmployee(_c1Card.EmpNo))
            {
                result = AttendanceParameters.ResultBadEmployee;
                return result;
            }

            // 檢查加會人員是否均為在職員工
            if (!string.IsNullOrWhiteSpace(_c1Card.AddSigners))
            {
                string[] users = _c1Card.AddSigners.Split(',');
                foreach (string user in users)
                {
                    if (!_c1CardBo.IsEmployee(user))
                    {
                        return AttendanceParameters.ResultBadEmployee;
                    }
                }
            }

            return result;
        }


        /// <summary>
        /// 檢查請假啟始截止日否為工作日、是否在上班時間請假。 產假自行 override
        /// </summary>
        /// <returns></returns>
        internal virtual CardCheckResult CheckLeaveStartEndDates()
        {
            CardCheckResult result = ResultOk;

            #region 【請假截止日期時間】須大於【請假起始日期時間】
            if (_c1Card.StartDate > _c1Card.EndDate)
            {
                result = new CardCheckResult(3000313, CardStatusEnum.Error, AttendanceParameters.Leave_End_Date_Must_Greater_Than_Start_Date);
                return result;
            }
            #endregion

            #region 不得跨年請假，請分年度填報請假卡
            if (_c1Card.StartDate.Year != _c1Card.EndDate.Year)
            {
                result = new CardCheckResult(3000318, CardStatusEnum.Error, AttendanceParameters.Leave_Date_Across_Years);
                return result;
            }
            #endregion

            #region 檢查是否為工作日
            if (!_c1CardBo.IsWorkday(_c1Card.StartDate, _c1Card.EmpNo) || !_c1CardBo.IsWorkday(_c1Card.EndDate, _c1Card.EmpNo))
            {
                result = new CardCheckResult(3000302, CardStatusEnum.Error, AttendanceParameters.Leave_Date_Only_Allow_WorkDays);
                return result;
            }
            #endregion

            #region  是否在上班時間請假
            List<Workday> workdays = _c1CardBo.GetEmpWorkdaysDateRange(_c1Card.StartDate, _c1Card.EndDate, _c1Card.EmpNo);
            Workday workdayStart = workdays[0];
            Workday workdayEnd = workdays[workdays.Count - 1];
            if (workdays.Count > 0)
            {
                if (_c1Card.StartDate < workdayStart.ArrivalTime || _c1Card.EndDate > workdayEnd.FlexibleDepartureAfter)
                {
                    result = new CardCheckResult(3000305, CardStatusEnum.Error, AttendanceParameters.Leave_Time_Error);
                    return result;
                }
            }
            #endregion

            #region 若 截入時間小於 正常上班時間 
            if (_c1Card.EndDate < workdayEnd.ArrivalTime)
            {
                result = ResultEndTimeError;
                return result;
            }
            #endregion

            #region 若 截入日與啟始日不同，且 截入時間大於 正常下班時間 
            if (_c1Card.EndDate.Date > _c1Card.StartDate.Date && _c1Card.EndDate > workdayEnd.DepartureTime)
            {
                result = ResultEndTimeError;
                return result;
            }
            #endregion

            #region   是否在上午休息時間請假，目前允許
            //if (_c1Card.StartDate > workdayStart.MorningRestStart && _c1Card.StartDate < workdayStart.MorningRestEnd)
            //{
            //    result = new CardCheckResult(3000317, CardStatusEnum.Error, AttendanceParameters.LeaveAtRestTimeError);
            //    return result;
            //}
            //if (_c1Card.EndDate > workdayEnd.MorningRestStart && _c1Card.EndDate < workdayEnd.MorningRestEnd)
            //{
            //    result = new CardCheckResult(3000317, CardStatusEnum.Error, AttendanceParameters.LeaveAtRestTimeError);
            //    return result;
            //}
            #endregion

            #region  是否在午休時間請假
            if (_c1Card.StartDate > workdayStart.MiddayBreakStart && _c1Card.StartDate < workdayStart.MiddayBreakEnd)
            {
                result = new CardCheckResult(3000317, CardStatusEnum.Error, AttendanceParameters.LeaveAtRestTimeError);
                return result;
            }
            if (_c1Card.EndDate > workdayEnd.MiddayBreakStart && _c1Card.EndDate < workdayEnd.MiddayBreakEnd)
            {
                result = new CardCheckResult(3000317, CardStatusEnum.Error, AttendanceParameters.LeaveAtRestTimeError);
                return result;
            }
            #endregion

            #region   是否在下午休息時間請假，目前允許
            //if (_c1Card.StartDate > workdayStart.AfternoonRestStart && _c1Card.StartDate < workdayStart.AfternoonRestEnd)
            //{
            //    result = new CardCheckResult(3000317, CardStatusEnum.Error, AttendanceParameters.LeaveAtRestTimeError);
            //    return result;
            //}
            //if (_c1Card.EndDate > workdayEnd.AfternoonRestStart && _c1Card.EndDate < workdayEnd.AfternoonRestEnd)
            //{
            //    result = new CardCheckResult(3000317, CardStatusEnum.Error, AttendanceParameters.LeaveAtRestTimeError);
            //    return result;
            //}
            #endregion

            #region 檢查是否超過每日最大工時
            //// 若 截止日與啟始日相同，且 時數 大於正常上班時數

            if (_c1Card.EndDate.Date == _c1Card.StartDate.Date)
            {
                TimeSpan span = _c1Card.EndDate - _c1Card.StartDate;
                int spanMinutes = (span.Hours - 1) * 60 + span.Minutes;
                int workingMinutes = (int)workdayStart.WorkHours * 60;
                if (spanMinutes > workingMinutes)
                {
                    result = ResultTimeLargerThanWorkHours;
                    return result;
                }
            }
            #endregion

            #region 請假時數為零
            CalculateHours();
            if (TotalHours <= 0)
            {
                result = ResultTimeZeroError;
                return result;
            }
            #endregion

            return result;
        }

        /// <summary>
        /// 依照最小請假單位 檢查請假時段有效性
        /// </summary>
        /// <returns></returns>
        internal CardCheckResult CheckLeaveUnit()
        {
            // 本來是 _c1CardBo.GetLeaveMinimumUnit(_c1Card)，直接傳進來比較快
            string unit = Kind.MinimumUnit;
            CardCheckResult result;
            switch (unit)
            {
                case "H":
                    result = CheckLeaveUnitHour();
                    break;
                case "D":
                    result = CheckLeaveUnitDay();
                    break;
                case "F":
                    result = CheckLeaveUnitHalfDay();
                    break;
                default:
                    throw new LeaveNotFoundException($"查無假別單位：{unit}");
            }

            return result;
        }

        /// <summary>
        /// 請假單位為日時，檢查時段有效性
        /// </summary>
        /// <returns></returns>
        internal CardCheckResult CheckLeaveUnitDay()
        {
            bool notOk = false;
            List<Workday> workdays = _c1CardBo.GetEmpWorkdaysDateRange(_c1Card.StartDate, _c1Card.EndDate, _c1Card.EmpNo);
            if (workdays.Count == 0)
            {
                throw new LeaveNotFoundException("無工作日資訊");
            }
            Workday workdayStart = workdays[0];
            Workday workdayEnd = workdays[workdays.Count - 1];
            // 請全日假只允許 Workday.ArrivalTime ~ Workday.DepartureTime (08:00 ~ 17:00)
            if (_c1Card.StartDate != workdayStart.ArrivalTime || _c1Card.EndDate != workdayEnd.DepartureTime)
            {
                notOk = true;
            }
            CardCheckResult result = ResultOk;

            if (notOk)
            {
                result = new CardCheckResult(3000303, CardStatusEnum.Error, AttendanceParameters.Leave_Day_Time_Error);
            }
            return result;
        }

        /// <summary>
        /// 請假單位為半日時，檢查時段有效性
        /// </summary>
        /// <returns></returns>
        internal CardCheckResult CheckLeaveUnitHalfDay()
        {
            bool notOk = false;
            List<Workday> workdays = _c1CardBo.GetEmpWorkdaysDateRange(_c1Card.StartDate, _c1Card.EndDate, _c1Card.EmpNo);
            if (workdays.Count == 0)
            {
                throw new LeaveNotFoundException("無工作日資訊");
            }
            Workday workdayStart = workdays[0];
            Workday workdayEnd = workdays[workdays.Count - 1];

            // 請半日假只允許 Workday.ArrivalTime ~ Workday.MiddayBreakStart (08:00 ~ 12:00)
            // 或 Workday.MiddayBreakEnd ~ Workday.DepartureTime (13:00 ~ 17:00)
            // 或 Workday.ArrivalTime ~ Workday.DepartureTime (08:00 ~ 17:00)

            #region 檢查開始時間 08 ArrivalTime or 13 MiddayBreakEnd  
            if (_c1Card.StartDate != workdayStart.ArrivalTime && _c1Card.StartDate != workdayStart.MiddayBreakEnd)
            {
                notOk = true;
            }
            #endregion

            #region 檢查結束時間 12 MiddayBreakStart or 17  DepartureTime
            if (_c1Card.EndDate != workdayEnd.MiddayBreakStart && _c1Card.EndDate != workdayEnd.DepartureTime)
            {
                notOk = true;
            }
            #endregion

            CardCheckResult result = ResultOk;

            if (notOk)
            {
                string sTemp = AttendanceParameters.Leave_HalfDay_Time_Error.Replace("{X}", Kind.Name);
                result = new CardCheckResult(3000304, CardStatusEnum.Error, sTemp);
            }
            return result;
        }

        /// <summary>
        /// 請假單位為時，檢查請假時段有效性 (目前不檢查)
        /// </summary>
        /// <returns></returns>
        internal CardCheckResult CheckLeaveUnitHour()
        {
            CardCheckResult result = ResultOk;
            return result;
        }

        /// <summary>
        /// 檢查是否重覆請假
        /// </summary>
        /// <returns></returns>
        internal CardCheckResult CheckOverlappedLeave()
        {
            CardCheckResult result = ResultOk;
            bool found = _c1CardBo.CheckAlreadyTakeLeave(_c1Card.StartDate, _c1Card.EndDate, _c1Card.EmpNo);
            if (found)
            {
                result = ResultOverlappedLeaves;
            }
            return result;
        }

        /// <summary>
        /// 檢查是否超假
        /// </summary>
        /// <returns></returns>
        internal abstract CardCheckResult CheckOverPermittedLeaveHours();

        /// <summary>
        /// 檢查必要欄位
        /// </summary>
        /// <returns></returns>
        internal virtual CardCheckResult CheckRequiredFields()
        {
            CardCheckResult result = ResultOk;

            if (_c1Card.StartDate == DateTime.MinValue || _c1Card.StartDate == DateTime.MaxValue)
            {
                result = new CardCheckResult(3000309, CardStatusEnum.Error, AttendanceParameters.No_StartDate_Error);
                return result;
            }

            if (_c1Card.EndDate == DateTime.MinValue || _c1Card.EndDate == DateTime.MaxValue)
            {
                result = new CardCheckResult(3000310, CardStatusEnum.Error, AttendanceParameters.No_EndDate_Error);
                return result;
            }

            if (string.IsNullOrWhiteSpace(_c1Card.Deputy))
            {
                result = new CardCheckResult(3000311, CardStatusEnum.Error, AttendanceParameters.No_Deputy_Error);
                return result;
            }

            if (!_c1CardBo.IsEmployee(_c1Card.Deputy))
            {
                result = new CardCheckResult(3000312, CardStatusEnum.Error, AttendanceParameters.Bad_Deputy_Error);
                return result;
            }

            return result;
        }

        /// <summary>
        /// 檢查本次請假與先前請假時數累計是否超過每日正常上班時數
        /// </summary>
        /// <returns></returns>
        internal CardCheckResult CheckAccumulatedOverPermittedWorkingHours()
        {
            CardCheckResult result = ResultOk;
            var (overPermittedWorkingHours, hours, date) = _c1CardBo.CheckAccumulatedOverPermittedWorkingHours(_c1Card);
            if (overPermittedWorkingHours && date != null)
            {
                string message = AttendanceParameters.Accumulated_Leave_Time_Larger_Than_Working_Hours
                    .Replace("{date}", CardUtility.RocChineseDateString((DateTime)date))
                    .Replace("{hours}", hours.ToString());
                result = new CardCheckResult(3000321, CardStatusEnum.Error, message);
            }
            return result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="date">事件發生日</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        internal (DateTime, DateTime) DefaultCalculateLeavePermittedPeriod(DateTime date, string empNo)
        {
            DateTime earliestStartDate = _c1CardBo.GetFirstWorkdayOfYear(empNo, date.Year);
            DateTime latestEndDate = _c1CardBo.GetLastWorkdayOfYear(empNo, date.Year).AddDays(1).AddSeconds(-1);
            return (earliestStartDate, latestEndDate);
        }

        /// <summary>
        /// 計算請假時數
        /// </summary>
        /// <returns></returns>
        public virtual void CalculateHours()
        {
            if (_c1Card.EndDate > _c1Card.StartDate)
            {
                // 非個人化 TotalHours = _c1CardBo.CalculateTakeLeaveWorkingHours(_c1Card.StartDate, _c1Card.EndDate)
                // 以下改為個人化行事曆
                TotalHours = _c1CardBo.CalculateEmpTakeLeaveWorkingHours(_c1Card.EmpNo, _c1Card.StartDate, _c1Card.EndDate);
                Days = _c1CardBo.CalculateTakeLeaveWorkingDays(TotalHours);
            }
        }

        /// <summary>
        /// 計算最早可請假日期與最晚可請假日期
        /// </summary>
        /// <param name="eventDate">事件發生日</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public abstract (DateTime, DateTime) CalculateLeavePermittedPeriod(DateTime eventDate, string empNo);

        /// <summary>
        /// 檢查所有規格
        /// </summary>
        /// <returns>B1CardResult物件</returns>
        public CardCheckResult CheckData()
        {
            // 1. 檢查必要欄位
            CardCheckResult result = CheckRequiredFields();
            if (result.Status != CardStatusEnum.Success) { return result; }

            // 2. 檢查是否能請此假
            result = CanTakeThisLeave();
            if (result.Status != CardStatusEnum.Success) { return result; }

            // 3. 檢查一般性規則
            result = CheckGeneralRules();
            if (result.Status != CardStatusEnum.Success) { return result; }

            // 4. 檢查請假啟始截止日
            result = CheckLeaveStartEndDates();
            if (result.Status != CardStatusEnum.Success) { return result; }

            // 5. 檢查是否重覆請假
            result = CheckOverlappedLeave();
            if (result.Status != CardStatusEnum.Success) { return result; }

            // 6. 檢查請假單位
            result = CheckLeaveUnit();
            if (result.Status != CardStatusEnum.Success) { return result; }

            // 7. 檢查是否超假
            result = CheckOverPermittedLeaveHours();
            if (result.Status != CardStatusEnum.Success) { return result; }

            // 8. 檢查本次請假與先前請假時數累計是否超過每日正常上班時數
            result = CheckAccumulatedOverPermittedWorkingHours();
            if (result.Status != CardStatusEnum.Success) { return result; }

            return result;
        }

        /// <summary>
        /// 性別是否可請此假別
        /// </summary>
        /// <param name="type">假別</param>
        /// <param name="gender">性別</param>
        /// <returns></returns>
        /// <exception cref="LeaveNotFoundException"></exception>
        public static bool IsAllowForThisGender(LeaveKindEnum type, Gender gender)
        {
            switch (type)
            {
                case LeaveKindEnum.AnnualLeave:
                    return AnnualLeave.IsAllowForThisGender(gender);
                case LeaveKindEnum.BirthdayLeave:
                    return BirthdayLeave.IsAllowForThisGender(gender);
                case LeaveKindEnum.BusinessInjuryLeave:
                    return BusinessInjuryLeave.IsAllowForThisGender(gender);
                case LeaveKindEnum.BusinessOutLeave:
                    return BusinessOutLeave.IsAllowForThisGender(gender);
                case LeaveKindEnum.BusinessTripLeave:
                    return BusinessTripLeave.IsAllowForThisGender(gender);
                case LeaveKindEnum.CompensatoryLeave:
                    return CompensatoryLeave.IsAllowForThisGender(gender);
                case LeaveKindEnum.DisasterLeave:
                    return DisasterLeave.IsAllowForThisGender(gender);
                case LeaveKindEnum.FamilyCareLeave:
                    return FamilyCareLeave.IsAllowForThisGender(gender);
                case LeaveKindEnum.ForeignLeave:
                    return ForeignLeave.IsAllowForThisGender(gender);
                case LeaveKindEnum.FuneralLeave:
                    return FuneralLeave.IsAllowForThisGender(gender);
                case LeaveKindEnum.MarriageLeave:
                    return MarriageLeave.IsAllowForThisGender(gender);
                case LeaveKindEnum.MaternityLeave:
                    return MaternityLeave.IsAllowForThisGender(gender);
                case LeaveKindEnum.MenstrualLeave:
                    return MenstrualLeave.IsAllowForThisGender(gender);
                case LeaveKindEnum.ObstetricInspectionLeave:
                    return ObstetricInspectionLeave.IsAllowForThisGender(gender);
                case LeaveKindEnum.OfficialLeave:
                    return OfficialLeave.IsAllowForThisGender(gender);
                case LeaveKindEnum.PaternityLeave:
                    return PaternityLeave.IsAllowForThisGender(gender);
                case LeaveKindEnum.PersonalLeave:
                    return PersonalLeave.IsAllowForThisGender(gender);
                case LeaveKindEnum.PostponedLeave:
                    return PostponedLeave.IsAllowForThisGender(gender);
                case LeaveKindEnum.QuarantineCareLeave:
                    return QuarantineCareLeave.IsAllowForThisGender(gender);
                case LeaveKindEnum.QuarantineIsolationLeave:
                    return QuarantineIsolationLeave.IsAllowForThisGender(gender);
                case LeaveKindEnum.SickLeave:
                    return SickLeave.IsAllowForThisGender(gender);
                case LeaveKindEnum.VaccinationLeave:
                    return VaccinationLeave.IsAllowForThisGender(gender);
                case LeaveKindEnum.AdjustmentLeave:
                    return AdjustmentLeave.IsAllowForThisGender(gender);
                default:
                    throw new LeaveNotFoundException($"未找到此假別");
            }
        }

        /// <summary>
        /// 本次請假的天數，無條件捨去法
        /// </summary>
        /// <value>
        /// 天數
        /// </value>
        public int Days { get; set; }

        /// <summary>
        /// 假別類型
        /// </summary>
        public LeaveKind Kind { get; set; } = new LeaveKind();

        /// <summary>
        /// 請假總時數
        /// </summary>
        public int TotalHours { get; set; }
        public const int CodeLeaveRecordNotCreateYet = 3000300;
        private readonly string _messageLeaveRecordNotCreateYet = "您的年度休假資料尚未建立，請通知行政處";
        private readonly CardStatusEnum _statusLeaveRecordNotCreateYet = CardStatusEnum.Error;

        private CardCheckResult? _resultLeaveDataNotCreatedYet;
        protected CardCheckResult ResultLeaveRecordNotCreatedYet =>
            _resultLeaveDataNotCreatedYet ??=
            new CardCheckResult(CodeLeaveRecordNotCreateYet, _statusLeaveRecordNotCreateYet, _messageLeaveRecordNotCreateYet);

        #region  Delegate to another gateway

        protected bool IfLeaveRecordExists()
        {
            return _c1CardBo.IfLeaveRecordExists(_c1Card.EmpNo, _c1Card.StartDate, _c1Card.EndDate);
        }

        protected int GetPostponedLeaveRemainingHours()
        {
            return _c1CardBo.GetPostponedLeaveRemainingHours(_c1Card.EmpNo, _c1Card.StartDate);
        }

        #endregion
    }
}
