﻿using System;
using System.Collections.Generic;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 單日刷卡時間
    /// </summary>
    public class DayInTime
    {
        /// <summary>
        /// 員工編號
        /// </summary>
        public string EmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 刷卡時間字串 List
        /// </summary>
        public List<string> InTimeString { get; set; } = new List<string>();

        /// <summary>
        /// 刷卡時間 List
        /// </summary>
        public List<DateTime> InTime { get; set; } = new List<DateTime>();
    }
}
