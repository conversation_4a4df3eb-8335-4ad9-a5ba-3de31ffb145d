<template>
  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span class="mx-1">卡</span>
          <span class="mx-1">號</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ modelValue?.formNo }}
    </div>
  </div>
  
  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>申</span>
          <span class="mx-1">請</span>
          <span>人</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ (modelValue?.empNo ?? '') + ((modelValue?.empNo && modelValue?.empName) ? ' ' : '') + (modelValue?.empName ?? '') }}
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>所屬部門</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ modelValue?.deptSName }}
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>加班日期</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ modelValue?.formInfo }}
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-12 px-0">
      <table class="table table-sm table-bordered text-center mb-0">
        <caption class="d-none">
          <span>填報計畫</span>
        </caption>
        <thead>
          <tr class="table-light">
            <th>計畫編號</th>
            <th>申請別</th>
            <th>起始時間</th>
            <th>截止時間</th>
            <th>加班時數</th>
          </tr>
        </thead>
        <tbody>
          <template
            v-for="(detail, index) in cardDetails"
            :key="index"
          >
            <tr>
              <td>
                <a
                  class="align-items-center text-primary text-decoration-underline"
                  role="button"
                  @click="onProjectLinkClick(detail.project)"
                >
                  {{ detail.project }}
                </a>
              </td>
              <td>{{ detail.b1_CODE }}</td>
              <td>{{ detail.startTime }}</td>
              <td>{{ detail.endTime }}</td>
              <td>{{ detail.hour }}</td>
            </tr>
          </template>
        </tbody>
      </table>
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>加班總時數</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ modelValue?.card?.TotalHours }}
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>加班事由</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      <template
        v-for="(rowReason, rowIndex) in reason"
        :key="rowIndex"
      >
        <span>{{ rowReason }}</span>
        <br v-if="rowIndex < (reason.length - 1)">
      </template>
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>加會人員</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ modelValue?.addedSigner }}
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span class="mx-1">附</span>
          <span class="mx-1">件</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      <template
        v-for="(file, index) in modelValue?.attachments"
        :key="index"
      >
        <div class="mb-1">
          <button
            type="button"
            class="btn btn-link p-0"
            @click="onClickDownloadUrl(GET_DOWNLOADATTACHMENT_URL + '/' + file.formUID + '/' + file.id)"
          >
            {{ file.originalFileName }}
          </button>
        </div>
      </template>
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>填</span>
          <span class="mx-1">表</span>
          <span>人</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ (modelValue?.createdEmpNo ?? '') + ((modelValue?.createdEmpNo && modelValue?.createdName) ? ' ' : '') + (modelValue?.createdName ?? '') }}
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>填報時間</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ modelValue?.createdTime ? dateToRocString(new Date(modelValue?.createdTime)) : '' }}
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useOvertimeCompensatory } from '../composable/overtimeCompensatory'
import { useAbortController } from '../composable/abortController'
import { dateToRocString, onClickDownloadUrl } from '../api/appFunction'
import { SYSTEM_ERROR_MESSAGE, INFO_DISPLAY_TIME } from '../api/appConst'
import { GET_DOWNLOADATTACHMENT_URL, GET_PROJECTNAME_URL } from '../api/appUrl'
import { useToast } from 'primevue/usetoast'
import type { PropType } from 'vue'
import type { CardStoreType, OvertimeCompensatoryType } from '../api/appType'

const cardDetails = ref<Array<{
  project: string
  b1_CODE: string
  startTime: string
  endTime: string
  hour: number
}>>([])

const toast = useToast()
const { overtimeCompensatory, onGetOvertimeCompensatory } = useOvertimeCompensatory()
const { abortController, fetchErrorHandler, abortListener } = useAbortController()

const props = defineProps({
  modelValue: {
    type: Object as PropType<CardStoreType | undefined>,
    default: () => {}
  }
})

const reason = computed<Array<string>>((): Array<string> => {
  let result: Array<string> = []
  if (props.modelValue?.card?.Reason) {
    result = props.modelValue?.card?.Reason.split('\n')
  }
  return result
})

const onProjectLinkClick = async (projNo: string): Promise<void> => {
  const params = new URLSearchParams({
    projNo: projNo
  })
  await fetch(GET_PROJECTNAME_URL + '?' + params, {
    method: 'GET',
    signal: abortController.signal
  }).then((res: Response) => {
    if (!res.ok) {
      throw new Error(res.status.toString())
    }
    return res.text()
  }).then(res => {
    toast.add({
      severity: 'info',
      summary: '計畫名稱： ' + res,
      life: INFO_DISPLAY_TIME,
      group: 'app'
    })
  }).catch((err: Error): void => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })
}

onMounted(async (): Promise<void> => {
  abortListener()
  if (props.modelValue?.card) {
    try {
      await onGetOvertimeCompensatory(abortController.signal)
    } catch (err: unknown) {
      console.error(err)
      fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
    }

    props.modelValue?.card.Details.forEach((e: any) => {
      const startTime = new Date(e.StartTime)
      const endTime = new Date(e.EndTime)
      const startTimeNextDate = new Date(startTime)
      startTimeNextDate.setDate(startTimeNextDate.getDate() + 1)

      const startTimeString = new Date(e.StartTime).getHours().toString().padStart(2, '0') + ':' + new Date(e.StartTime).getMinutes().toString().padStart(2, '0')
      let endTimeString = ''

      if (new Date(startTimeNextDate.getFullYear() + '/' + (startTimeNextDate.getMonth() + 1) + '/' + startTimeNextDate.getDate()).getTime() === new Date(endTime.getFullYear() + '/' + (endTime.getMonth() + 1) + '/' + endTime.getDate()).getTime()) {
        endTimeString = '24:00'
      } else {
        endTimeString = endTime.getHours().toString().padStart(2, '0') + ':' + endTime.getMinutes().toString().padStart(2, '0')
      }

      cardDetails.value.push({
        project: e.Project,
        b1_CODE: overtimeCompensatory.value.find((found: OvertimeCompensatoryType) => found.id.toString() === e.B1_CODE.toString())?.name ?? '',
        startTime: startTimeString,
        endTime: endTimeString,
        hour: e.Hour
      })
    })
  }
})
</script>