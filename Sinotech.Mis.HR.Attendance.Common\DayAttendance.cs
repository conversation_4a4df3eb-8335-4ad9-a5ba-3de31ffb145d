﻿namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 單日出勤
    /// </summary>
    public class DayAttendance
    {
        /// <summary>
        /// 年
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// 月
        /// </summary>
        public int Month { get; set; }

        /// <summary>
        /// 日
        /// </summary>
        public int Day { get; set; }

        /// <summary>
        /// 刷卡時間字串陣列
        /// </summary>
        public string[] Attendances { get; set; } = new string[0];

        /// <summary>
        /// 與 Attendances 資料相同，但以逗號【,】分隔
        /// </summary>

        public string InTimeString { get; set; } = string.Empty;
    }
}
