﻿using Sinotech.Mis.HR.Attendance.Common;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.Tests
{
    public class DepartmentTests
    {
        [Fact]
        public void Department_DefaultValues_ShouldBeInitialized()
        {
            // Arrange & Act
            var department = new Department();

            // Assert
            Assert.Equal(0, department.DeptNo);
            Assert.Equal(string.Empty, department.DeptESSName);
            Assert.Equal(string.Empty, department.DeptESName);
            Assert.Equal(string.Empty, department.DeptSSName);
            Assert.Equal(string.Empty, department.DeptSName);
            Assert.Equal(string.Empty, department.DeptName);
            Assert.Equal(string.Empty, department.DeptEName);
            Assert.Equal(0, department.StNo);
        }

        [Fact]
        public void Department_SetValues_ShouldBeSetCorrectly()
        {
            // Arrange
            var department = new Department
            {
                DeptNo = 1,
                DeptESSName = "ESS",
                DeptESName = "ES",
                DeptSSName = "SS",
                DeptSName = "S",
                DeptName = "Name",
                DeptEName = "EName",
                StNo = 1
            };

            // Act & Assert
            Assert.Equal(1, department.DeptNo);
            Assert.Equal("ESS", department.DeptESSName);
            Assert.Equal("ES", department.DeptESName);
            Assert.Equal("SS", department.DeptSSName);
            Assert.Equal("S", department.DeptSName);
            Assert.Equal("Name", department.DeptName);
            Assert.Equal("EName", department.DeptEName);
            Assert.Equal(1, department.StNo);
        }
    }
}
