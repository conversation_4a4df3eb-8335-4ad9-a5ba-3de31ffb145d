<template>
  <VueSelectCustom
    ref="selectRef"
    :class="customClass"
    :options="selectOptions"
    :filter="filter"
    :clearable="clearable"
    :modelValue="modelValue"
    :placeholder="placeholder"
    :alwaysShowClearButton="alwaysShowClearButton"
    @option:selected="onChange"
    @search="onSearch"
  >
    <template #no-options>
      <span class="text-danger">
        {{ noOptions }}
      </span>
    </template>
    <template #option="option">
      {{ option.userId + ' ' + option.userName }}
    </template>
    <template #selected-option="option">
      {{ (option?.userId && option?.userName) ? (option.userId + ' ' + option.userName) : '' }}
    </template>
  </VueSelectCustom>
</template>
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import VueSelectCustom from './VueSelectCustom.vue'
import type { VueSelectInstance } from 'vue-select'
import type { EmployeeType, SignerType } from '../api/appType'
import { onSearchEmployeeData } from '../api/appFunction'

const props = withDefaults(
  defineProps<{
    customClass?: string
    clearable?: boolean
    modelValue: SignerType | null
    employeeData: Array<SignerType>
    employeeFilter?: string
    signerFilter?: Array<SignerType>
    placeholder: string
    alwaysShowClearButton?: boolean
    filter: (options: Array<EmployeeType | SignerType>, search: string) => Array<EmployeeType | SignerType>
  }>(),
  {
    customClass: '',
    clearable: true,
    modelValue: () => {
      return {
        userId: '',
        userName: ''
      }
    },
    employeeData: () => [],
    employeeFilter: '',
    signerFilter: () => [],
    placeholder: '',
    alwaysShowClearButton: false
  }
)
const emits = defineEmits(['delete', 'update:modelValue'])

const selectRef = ref<VueSelectInstance | undefined>()
const noOptions = ref<string>('')
const selectOptions = computed<Array<SignerType>>(() => props.employeeData.map((e: SignerType) => {
  return {
    userId: e.userId,
    userName: e.userName
  }
}))

const onChange = (event: SignerType): void => {
  emits('update:modelValue', {
    userId: event.userId,
    userName: event.userName
  })
}

/**
 * 只會變更noOptions的值，不影響搜尋的結果
 * @param search
 */
const onSearch = (search: string): void => {
  const searchResult = onSearchEmployeeData(props.signerFilter, search)
  if (props.employeeFilter === search) {
    noOptions.value = '不可選取本人'
  } else if (searchResult.length > 0) {
    noOptions.value = '不可重複'
  } else {
    noOptions.value = '查無資料'
  }
}

onMounted((): void => {
  selectRef.value!.$refs.clearButton.title = '刪除'
  selectRef.value!.$refs.clearButton.addEventListener('click', () => emits('delete'))
})
</script>
<style lang="scss" scoped>
@use '../assets/scss/bootstrap_variables' as bootstrap;
:deep(input::placeholder) {
  color: bootstrap.$gray-400;
}
</style>