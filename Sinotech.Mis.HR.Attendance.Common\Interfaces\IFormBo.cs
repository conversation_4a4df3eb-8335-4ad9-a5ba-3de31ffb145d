﻿using Sinotech.Mis.Common;
using System;
using System.Collections.Generic;
using System.Data;

namespace Sinotech.Mis.HR.Attendance.Common
{
    public interface IFormBo
    {
        /// <summary>
        /// 加入附件
        /// </summary>
        /// <param name="dtCardForms">The dt card notificationForms.</param>
        /// <param name="formCard">The forms card.</param>
        /// <param name="drs">The DRS.</param>
        void AddAttachments(DataTable dtCardForms, FormCard formCard, DataRow[] drs);

        /// <summary>
        /// 依計畫判斷，非本部門計畫加會該部門主管
        /// </summary>
        /// <param name="form">表單物件</param>
        /// <param name="project">計畫物件</param>
        /// <returns>增加流程數， 0 或 1</returns>
        int AddFlowByProject(Form form, Project project);

        /// <summary>新增 Form</summary>
        /// <param name="form">表單</param>
        /// <param name="cardBo">三卡商業邏輯元件</param>
        /// <param name="card">三卡</param>
        /// <returns></returns>
        string AddForm(Form form, ICardBaseBo cardBo, CardBase card);

        /// <summary>
        /// 替加會人員加上姓名
        /// </summary>
        /// <param name="addSigners">加會人員</param>
        /// <returns></returns>
        string? AddSignersAddName(string? addSigners);

        /// <summary>簽核</summary>
        /// <param name="approveDto">簽核資料物件</param>
        /// <param name="cardBo">三卡商業物件</param>
        /// <param name="empNo">簽核員工編號</param>
        /// <returns>簽核成功與否，成功為空值，失敗時為錯誤訊息 </returns>
        string Approve(Approve approveDto, ICardBaseBo cardBo, string empNo);

        /// <summary>
        /// 是否能看特定流程的待審表單
        /// </summary>
        /// <param name="formUID">表單編號</param>
        /// <param name="empNo">觀看者員工編號</param>
        /// <returns></returns>
        bool CanSeeApprovalFormCard(Guid formUID, string empNo);

        /// <summary>
        /// 能看部門資料
        /// </summary>
        /// <param name="deptNo"></param>
        /// <param name="empNo"></param>
        /// <returns></returns>
        bool CanSeeDepartment(int deptNo, string empNo);

        /// <summary>
        /// 該 empNo 是否有權限看 empNo 的收件匣
        /// </summary>
        /// <param name="empNo">收件匣擁有者員工編號</param>
        /// <param name="userId">查看者員工編號</param>
        /// <returns>
        ///   若有權限查看則傳回 <c>true</c>，否則傳回 <c>false</c>.
        /// </returns>
        bool CanSeeInbox(string empNo, string userId);

        /// <summary>
        /// 是否能看特定通知表單
        /// </summary>
        /// <param name="formUID">表單編號</param>
        /// <param name="notifyId">The notify id.</param>
        /// <param name="logonUserId">登入者帳號</param>
        /// <param name="userId">查看員工編號</param>
        /// <returns></returns>
        bool CanSeeNotifyFormCard(Guid formUID, int notifyId, string logonUserId, string userId);

        /// <summary>
        /// 是否能看特定已填表單
        /// </summary>
        /// <param name="formUID">表單編號</param>
        /// <param name="empNo">觀看者員工編號</param>
        /// <returns></returns>
        bool CanSeeSentFormCard(Guid formUID, string empNo);

        /// <summary>
        /// 是否能看特定已簽核表單
        /// </summary>
        /// <param name="formUID">表單編號</param>
        /// <param name="empNo">觀看者員工編號</param>
        /// <returns></returns>
        bool CanSeeSignedFormCard(Guid formUID, string empNo);

        /// <summary>
        /// 是否可抽單
        /// </summary>
        /// <param name="form">The forms.</param>
        /// <param name="cardBo">The card 商業物件</param>
        /// <param name="card">The card.</param>
        /// <param name="withdraw">抽單</param>
        /// <returns>
        ///   <c>true</c> if this instance can withdraw the specified forms; otherwise, <c>false</c>.
        /// </returns>
        bool CanWithdraw(Form form, ICardBaseBo cardBo, CardBase card, Withdraw withdraw);

        /// <summary>結案抽單</summary>
        /// <param name="form">表單</param>
        /// <param name="cardBo">三卡商業物件</param>
        /// <param name="card">三卡</param>
        /// <param name="withdraw">抽單物件</param>
        /// <returns>抽單成功傳回空值，失敗傳回錯誤訊息</returns>
        string ClosedWithdraw(Form form, ICardBaseBo cardBo, CardBase card, Withdraw withdraw);

        /// <summary>
        /// 紀錄員工本人或本人角色第一次讀取通知的時間
        /// </summary>
        /// <param name="userId">查看員工編號</param>
        /// <param name="id">通知編號</param>
        /// <returns>紀錄第一次讀取通知的結果是成功或失敗</returns>
        bool DeliveredNotifications(string userId, int id);

        /// <summary>
        /// Encodes the name of the file for attachment
        /// </summary>
        /// <param name="formNo">The form number</param>
        /// <param name="serial">The attachment serial</param>
        /// <param name="fileName">Name of the file.</param>
        /// <returns></returns>
        string EncodeAttachmentName(string formNo, int serial, string fileName);

        /// <summary>
        /// 取得特定時間區間內已同意已填表單 JSON，包括他人代填與本人填表，以填表時間為準
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="userId">登入者</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <returns></returns>
        string GetAgreedSentBoxJson(string empNo, string userId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// 取得特定月份已同意已填表單，包括他人代填與本人填表，依表單年月為準，非填表時間
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="userId">登入者</param>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <returns></returns>
        string GetAgreedSentBoxYearMonthJson(string empNo, string userId, int year, int month);

        /// <summary>
        /// 取得日期區間內全社的已填表單，以填表日期為準
        /// </summary>
        /// <param name="startDate">The start date.</param>
        /// <param name="endDate">The end date.</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns></returns>
        List<SentBox> GetAllSentBox(DateTime startDate, DateTime endDate, string? projNo);

        /// <summary>
        /// 取得日期區間內全社的已填表單，以內容日期為準
        /// </summary>
        /// <param name="startDate">The start date.</param>
        /// <param name="endDate">The end date.</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns></returns>
        List<SentBox> GetAllSentBoxByContentDate(DateTime startDate, DateTime endDate, string? projNo = null);

        /// <summary>
        /// 取得特定待簽核表單及卡
        /// </summary>
        /// <param name="formUID">Form Unique ID</param>
        /// <param name="cardBo">三卡商業元件</param>
        /// <param name="empNo">員工編號</param>
        /// <param name="userId">登入者帳號</param>
        /// <returns></returns>
        string GetApprovalFormCard(Guid formUID, ICardBaseBo cardBo, string empNo, string userId);

        /// <summary>
        /// 取得表單附件
        /// </summary>
        /// <param name="formUID">表單 Guid</param>
        /// <param name="id">附件 id</param>
        /// <returns>FormAttachment 物件 或 null</returns>
        FormAttachment? GetAttachment(Guid formUID, int id);

        /// <summary>
        /// 取得表單附件
        /// </summary>
        /// <param name="formUID">表單Guid</param>
        /// <returns></returns>
        List<FormAttachment>? GetAttachments(Guid formUID);

        /// <summary>
        /// 取得特定月份某員工所有卡，依表單年月為準，非填表時間
        /// </summary>
        /// <param name="cardBoFactory"></param>
        /// <param name="empNo">員工編號</param>
        /// <param name="date">日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns></returns>
        List<CardsList> GetCardsMonth(ICardBoFactory cardBoFactory, string empNo, DateTime date, int? status = null);

        /// <summary>
        /// 取得部門已填表單，限部門登記桌或管理員
        /// </summary>
        /// <param name="deptNo">部門編號</param>
        /// <returns>已填表單</returns>
        List<SentBox> GetDepartmentSentBox(int deptNo);

        /// <summary>
        /// 取得部門已填表單，限部門登記桌或管理員
        /// </summary>
        /// <param name="request"></param>
        /// <returns>已填表單</returns>
        List<SentBox> GetDepartmentSentBox(GetFormCardsRequest request);

        /// <summary>
        /// 取得部門已填表單，限部門登記桌或管理員
        /// </summary>
        /// <param name="deptNo">部門編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>已填表單</returns>
        List<SentBox> GetDepartmentSentBoxByContentDate(int deptNo, DateTime startDate, DateTime endDate, string? projNo = null);

        /// <summary>
        /// 取得部門已填表單，限部門登記桌或管理員，以內容日期為準
        /// </summary>
        /// <param name="request"></param>
        /// <returns>已填表單 JSON</returns>
        string GetDepartmentSentBoxByContentDateJson(GetFormCardsRequest request);

        /// <summary>
        /// 取得部門已填表單，限部門登記桌或管理員
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>已填表單 JSON</returns>
        string GetDepartmentSentBoxJson(string empNo);

        /// <summary>
        /// 取得部門已填表單，限部門登記桌或管理員，以填表日期為準
        /// </summary>
        /// <param name="request"></param>
        /// <returns>已填表單 JSON</returns>
        string GetDepartmentSentBoxJson(GetFormCardsRequest request);

        /// <summary>
        /// 取得 部門  某月份已填表單 ，限部門登記桌或管理員，依表單年月為準，非填表時間
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="deptNo">部門編號</param>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <returns>已填表單</returns>
        string GetDepartmentSentBoxYearMonthJson(string empNo, int deptNo, int year, int month);

        /// <summary>取得 Form</summary>
        /// <param name="formUID">The forms forms.</param>
        /// <returns>DataTable Form</returns>
        Form GetForm(Guid formUID);

        /// <summary>
        /// 取得特定時間區間內所有表單，限管理者使用
        /// </summary>
        /// <param name="cardBoFactory"></param>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <returns>表單</returns>
        List<FormCard> GetFormCards(ICardBoFactory cardBoFactory, DateTime startDate, DateTime endDate);

        /// <summary>
        /// 取得特定時間區間內所有表單，限管理者使用
        /// </summary>
        /// <param name="cardBoFactory"></param>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單</returns>
        List<FormCard> GetFormCards(ICardBoFactory cardBoFactory, DateTime startDate, DateTime endDate, string projNo);

        /// <summary>
        /// 取得特定時間區間內所有表單及卡，限管理者使用
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="cardBoFactory"></param>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單</returns>
        List<FormCard>? GetFormCards(string empNo, ICardBoFactory cardBoFactory, DateTime startDate, DateTime endDate, string projNo);

        /// <summary>
        /// 取得特定時間區間內所有表單及卡，限管理者使用
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="cardBoFactory"></param>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單</returns>
        string GetFormCardsJson(string empNo, ICardBoFactory cardBoFactory, DateTime startDate, DateTime endDate, string projNo);

        /// <summary>只取得 Form</summary>
        /// <param name="formUID">The forms forms.</param>
        /// <returns>DataTable Form</returns>
        Form GetFormOnly(Guid formUID);

        /// <summary>
        /// 取得特定時間區間內所有表單 by 填報日期，限管理者使用
        /// </summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <returns>表單</returns>
        List<FormView> GetForms(DateTime startDate, DateTime endDate);

        /// <summary>
        /// 取得特定時間區間內所有表單 by 填報日期，限管理者使用
        /// </summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單</returns>
        List<FormView> GetForms(DateTime startDate, DateTime endDate, string projNo);

        /// <summary>
        /// 取得特定時間區間內所有表單 by 填報日期，限管理者使用
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單</returns>
        List<FormView> GetForms(string empNo, DateTime startDate, DateTime endDate, string projNo);

        /// <summary>
        /// 取得特定時間區間內所有表單 by 內容日期，限管理者使用
        /// </summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <returns>表單</returns>
        List<FormView> GetFormsByContentDate(DateTime startDate, DateTime endDate);

        /// <summary>
        /// 取得特定時間區間內所有表單 by 內容日期，限管理者使用
        /// </summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單</returns>
        List<FormView> GetFormsByContentDate(DateTime startDate, DateTime endDate, string projNo);

        /// <summary>
        /// 取得特定時間區間內所有表單 by 內容日期，限管理者使用
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單</returns>
        List<FormView> GetFormsByContentDate(string empNo, DateTime startDate, DateTime endDate, string projNo);

        /// <summary>
        /// 取得特定時間區間內所有表單 by 內容日期，限管理者使用
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單</returns>
        string GetFormsByContentDateJson(string empNo, DateTime startDate, DateTime endDate, string projNo);

        /// <summary>
        /// 取得特定時間區間內所有表單 by 填報日期，限管理者使用
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單</returns>
        string GetFormsJson(string empNo, DateTime startDate, DateTime endDate, string projNo);

        /// <summary>Gets the name of the Form Status.</summary>
        /// <param name="formStatus">The Form Status.</param>
        /// <returns>Form Status FormName</returns>
        string GetFormStatusName(int formStatus);

        /// <summary>取得收件匣</summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="cardBoFactory">The card bo factory.</param>
        /// <returns>收件匣JSON</returns>
        List<RoleInbox> GetInbox(string empNo, ICardBoFactory cardBoFactory);

        /// <summary>
        /// 取得收件匣數量
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>收件匣數量</returns>
        int GetInboxCount(string empNo);

        /// <summary>取得特定員工 empNo 的待審表單供社內首頁提醒使用</summary>
        /// <param name="empNo">待審員工編號</param>
        /// <param name="userId">查看員工</param>
        /// <param name="cardBoFactory"></param>
        /// <returns>收件匣JSON</returns>
        string GetInboxForRemind(string empNo, string userId, ICardBoFactory cardBoFactory);

        /// <summary>
        ///   <para>userId取得員工待審表單(收件匣)</para>
        /// </summary>
        /// <param name="empNo">待審員工編號</param>
        /// <param name="userId">查看員工</param>
        /// <param name="cardBoFactory"></param>
        /// <returns>收件匣JSON</returns>
        string GetInboxJson(string empNo, string userId, ICardBoFactory cardBoFactory);

        /// <summary>userId取得員工收件匣(待核表單)</summary>
        /// <param name="empNo">收件匣(待核表單) 員工編號</param>
        /// <param name="userId">查看員工編號</param>
        /// <param name="logonUserId">登入員工</param>
        /// <param name="cardBoFactory"></param>
        /// <returns>收件匣JSON</returns>
        string GetInboxJson(string empNo, string userId, string logonUserId, ICardBoFactory cardBoFactory);

        /// <summary>
        /// 取得特定月份某員工所有卡的JSON string，依表單年月為準，非填表時間
        /// </summary>
        /// <param name="cardBoFactory"></param>
        /// <param name="empNo">員工編號</param>
        /// <param name="date">日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns>JSON string</returns>
        string GetMonthSentCardsJson(ICardBoFactory cardBoFactory, string empNo, DateTime date, int? status = null);

        /// <summary>
        /// 取得所有的未讀通知數量
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>所有的未讀通知數量</returns>
        int GetNotificationCount(string empNo);

        /// <summary>
        /// 取得特定通知表單及卡
        /// </summary>
        /// <param name="formUID">Form Unique ID</param>
        /// <param name="cardBo">三卡商業元件</param>
        /// <param name="notifyId">The notify id.</param>
        /// <param name="logonUserId">登入者帳號</param>
        /// <param name="userId">查看員工編號</param>
        /// <returns></returns>
        string GetNotifyFormCard(Guid formUID, ICardBaseBo cardBo, int notifyId, string logonUserId, string userId);

        /// 取得在特定時間區間的通知表單卡別資料
        /// <param name="empNo">申請人員工編號</param>
        /// <param name="userId">被通知者員工編號</param>
        /// <param name="logonUserId">實際查詢通知的員工編號</param>
        /// <param name="deptNo">申請人部門編號</param>
        /// <param name="isRead">通知狀態</param>
        /// <param name="formStatus">表單狀態</param>
        /// <param name="startDate">起始時間</param>
        /// <param name="endDate">結束時間</param>
        /// <returns>通知JSON</returns>
        string GetNotifyFormCards(string empNo, string userId, string logonUserId, int deptNo, List<int> isRead, List<int> formStatus, DateTime startDate, DateTime endDate);

        /// <summary>
        /// 取得未結案表單，管理員傳回所有人的表單，其他人傳回自己的表單或自己填的表單
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>未結案表單</returns>
        string GetOpenFormsJson(string empNo);

        /// <summary>
        /// 以某人身份取得未結案表單
        /// </summary>
        /// <param name="userId">登入者</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>未結案表單</returns>
        string GetOpenFormsJson(string userId, string empNo);

        /// <summary>
        /// 取得未結案表單，管理員傳回所有人的表單，其他人傳回自己的表單或自己填的表單
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <returns>未結案表單</returns>
        string GetOpenFormsJson(string empNo, DateTime startDate, DateTime endDate);

        /// <summary>
        /// 取得未結案表單，管理員傳回所有人的表單，其他人傳回自己的表單或自己填的表單
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="cardBoFactory"></param>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號，若不填則傳回所有</param>
        /// <returns>未結案表單</returns>
        string GetOpenFormsJson(string empNo, ICardBoFactory cardBoFactory, DateTime startDate, DateTime endDate, string projNo);

        /// <summary>Get Remote File</summary>
        /// <param name="attachment">Form Attachment</param>
        /// <returns>Attachment file</returns>
        byte[] GetRemoteFile(FormAttachment? attachment);

        /// <summary>Get Remote File</summary>
        /// <param name="formUID">表單 Guid</param>
        /// <param name="id">附件 id</param>
        /// <returns>遠端檔案</returns>
        byte[] GetRemoteFile(Guid formUID, int id);

        /// <summary>
        /// Gets the sent forms json.
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="userId">登入者</param>
        /// <returns></returns>
        string GetSentBoxJson(string empNo, string userId);

        /// <summary>
        /// 取得特定已填表單及卡
        /// </summary>
        /// <param name="formUID">Form Unique ID</param>
        /// <param name="cardBo">三卡商業元件</param>
        /// <param name="empNo">員工編號</param>
        /// <param name="userId">登入者帳號</param>
        /// <returns></returns>
        string GetSentFormCard(Guid formUID, ICardBaseBo cardBo, string empNo, string userId);

        /// <summary>
        /// 取得特定已簽核表單及卡
        /// </summary>
        /// <param name="formUID">Form Unique ID</param>
        /// <param name="cardBo">三卡商業元件</param>
        /// <param name="empNo">員工編號</param>
        /// <param name="userId">登入者帳號</param>
        /// <returns></returns>
        string GetSignedFormCard(Guid formUID, ICardBaseBo cardBo, string empNo, string userId);

        /// <summary>
        /// 取得 已簽核表單 json.
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="userId">登入者</param>
        /// <param name="days">日數</param>
        /// <returns></returns>
        string GetSignedFormsJson(string empNo, string userId, int days);

        /// <summary>
        /// 取得 已簽核表單 json.，以申請內容日期為準
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="userId">登入者</param>
        /// <param name="startDate">申請內容啟始日期</param>
        /// <param name="endDate">申請內容結束日期</param>
        /// <returns></returns>
        public string GetSignedFormsJsonByContentDate(string empNo, string userId, DateTime startDate, DateTime endDate, DateTime contentStartDate, DateTime contentEndDate);

        /// <summary>
        /// 取得 已簽核表單 json.
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="userId">登入者</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <returns></returns>
        string GetSignedFormsJson(string empNo, string userId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// 取得使用者前N筆簽核意見
        /// </summary>
        /// <param name="userId">登入使用者</param>
        /// <param name="empNo">員工編號</param>
        /// <param name="count">數量</param>
        /// <returns>簽核意見</returns>
        List<string> GetTopApproveComments(string userId, string empNo, int count);

        /// <summary>
        /// 取得特定時間區間內已填表單 JSON，包括他人代填與本人填表，以填表時間為準
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="userId">登入者</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns></returns>
        string GetUserSentBoxJson(string empNo, string userId, DateTime startDate, DateTime endDate, string? projNo = null, int? status = null);

        /// <summary>
        /// 取得特定時間區間內已填表單 JSON，包括他人代填與本人填表，以內容時間為準
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="userId">登入者</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns></returns>
        string GetUserSentBoxJsonByContentDate(string empNo, string userId, DateTime startDate, DateTime endDate, string? projNo = null, int? status = null);

        /// <summary>
        /// 取得特定月份已填表單，包括他人代填與本人填表，依表單年月為準，非填表時間
        /// </summary>
        /// <param name="cardBoFactory">CardBo Factory</param>
        /// <param name="empNo">員工編號</param>
        /// <param name="userId">登入者</param>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <returns></returns>
        string GetUserSentBoxYearMonthJson(ICardBoFactory cardBoFactory, string empNo, string userId, int year, int month);

        /// <summary>
        /// 取得特定月份已填表單及卡，包括他人代填與本人填表，依表單年月為準，非填表時間
        /// </summary>
        /// <param name="cardBoFactory">三卡商業元件</param>
        /// <param name="empNo">填表員工編號</param>
        /// <param name="year">表單年</param>
        /// <param name="month">表單月</param>
        /// <returns></returns>
        List<FormCard> GetUserSentFormCardsYearMonth(ICardBoFactory cardBoFactory, string empNo, int year, int month);

        /// <summary>
        /// 標註某員工及其對應之角色的所有未讀通知為已讀
        /// </summary>
        /// <param name="logonUserId">登入員工</param>
        /// <param name="userId">被通知的員工編號</param>
        /// <returns>有一筆以上的通知被標註已讀則回傳true，否則回傳false</returns>
        bool MarkDeliveredNotifications(string logonUserId, string userId);

        /// <summary>群簽</summary>
        /// <param name="approveList">簽核資料物件 List</param>
        /// <param name="cardBoFactory">三卡商業物件工廠</param>
        /// <param name="userId">簽核員工編號</param>
        /// <returns>簽核成功與否</returns>
        List<string> MultipleApprove(List<Approve> approveList, ICardBoFactory cardBoFactory, string userId);

        /// <summary>抽單</summary>
        /// <param name="form">表單</param>
        /// <param name="cardBo">三卡商業物件</param>
        /// <param name="card">三卡</param>
        /// <param name="withdraw">抽單物件</param>
        /// <returns>抽單成功傳回空值，失敗傳回錯誤訊息</returns>
        string Withdraw(Form form, ICardBaseBo cardBo, CardBase card, Withdraw withdraw);

    }
}