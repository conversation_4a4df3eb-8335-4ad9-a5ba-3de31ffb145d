﻿namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 出勤系統裡各式參數<br />
    /// 日後可改讀取資料庫
    /// </summary>
    public static class AttendanceParameters
    {
        /// <summary>
        /// 旬卡已填報
        /// </summary>
        public const string A1CardAlreadyFilled = "該旬已填報，每旬限填一張工作卡！";

        /// <summary>
        /// 工作日加班總時數超過12小時 之訊息
        /// </summary>
        public const string AboveDayOvertimeLimitError = "每日正常工時8小時加延長工時總時數不得超過12小時";

        /// <summary>
        /// 部門副主管以上填報加班申請 之訊息
        /// </summary>
        public const string AboveDeputyManagerCannotWorkOvertime = "未開放部門副主管以上填報加班申請";

        /// <summary>
        /// 休息日及國定假日加班總時數超過12小時 之訊息
        /// </summary>
        public const string AboveHolidayOvertimeLimitError = "休息日及國定假日加班總時數不得超過12小時";

        /// <summary>
        /// 當月填報之加班時數已達當月上限 之警告訊息
        /// </summary>
        public const string AboveMonthOvertimeLimitWarning = "提醒您當月填報之加班時數已達當月上限";

        /// <summary>
        /// 每月生理假總請假天數超過可休上限
        /// </summary>
        public const string AbovePerMonthMenstrualLeave = "本月已請生理假，不得超假";


        /// <summary>
        /// 帳號鎖定時間 (分鐘)
        /// </summary>
        public const double AccountLockoutTime = 15.0;

        /// <summary>
        /// 帳號鎖定失敗次數
        /// </summary>
        public const int AccountLockoutFailTimes = 3;

        /// <summary>
        /// 帳號鎖定錯誤訊息
        /// </summary>
        public const string AccountLockoutMessage = "帳戶暫時鎖定，無法登入";

        /// <summary>
        /// 當月填報之加班時數已超過當月上限 之錯誤訊息
        /// </summary>
        public const string B1CardAppAboveMonthOvertimeLimitError = "您填報的預定時數已超過當月上限{hours}小時";

        /// <summary>
        /// 當月填報之加班時數已超過當季上限 之錯誤訊息
        /// </summary>
        public const string B1CardAppAboveQuarterOvertimeLimitError = "您填報的預定時數已超過當季上限{hours}小時";

        /// <summary>
        /// 加班申請卡提醒主管同仁的當月加班時數已達XX小時 之訊息
        /// </summary>
        public const string B1CardAppMonthOvertimeSignerWarningA = "提醒您該同仁{name}的當月加班時數已達{hours}小時，請注意該同仁身心健康，如非必要，請提醒該同仁準時下班；若有必要再請確認其工作內容是否需要調整";

        /// <summary>
        /// 加班申請卡提醒主管同仁當月加班時數已達當月上限  之訊息
        /// </summary>
        public const string B1CardAppMonthOvertimeSignerWarningB = "提醒您該同仁{name}的當月加班時數已達當月上限，請注意該同仁身心健康，並請提醒該同仁準時下班；若有必要再請確認其工作內容是否需要調整";

        /// <summary>
        /// 加班申請卡提醒主管同仁當季加班時數已達當季上限  之訊息
        /// </summary>
        public const string B1CardAppReachQuarterOvertimeLimitSignerWarning = "提醒您該同仁{name}的當季加班時數已達當季上限，請注意該同仁身心健康，並請提醒該同仁準時下班；若有必要再請確認其工作內容是否需要調整";

        /// <summary>
        /// 加班時數不足一小時 之訊息
        /// </summary>
        public const string B1CardAtLeastOneHour = "【加班】申請請以1小時為單位";

        /// <summary>
        /// 加班卡提醒同仁的當月加班時數已達XX小時 之訊息
        /// </summary>
        public const string B1CardMonthOvertimeSignerWarningA = "提醒您該同仁{name}的當月加班時數已達{hours}小時，請注意該同仁身心健康，如非必要，請提醒該同仁準時下班；若有必要再請確認其工作內容是否需要調整";

        /// <summary>
        /// 加班卡提醒主管同仁當月加班時數已達當月上限  之訊息
        /// </summary>
        public const string B1CardMonthOvertimeSignerWarningB = "提醒您該同仁{name}的當月加班時數已達當月上限";

        /// <summary>
        /// 加班卡提醒主管同仁當季加班時數已達當季上限  之訊息
        /// </summary>
        public const string B1CardReachQuarterOvertimeLimitSignerWarning = "提醒您該同仁{name}的當季加班時數已達當季上限";

        public const string Bad_Deputy_Error = "代理人並非在職員工";

        /// <summary>
        /// 找不到員工編號 之訊息
        /// </summary>
        public const string BadEmployeeNumber = "查無此員工編號";


        /// <summary>
        /// 重覆送出表單之訊息
        /// </summary>
        public const string RepeatSubmitForm = "重覆送出表單";

        /// <summary>
        /// 加班卡加班時段內包含上班時間 之訊息
        /// </summary>
        public const string BadOvertimeSection = "您填報的加班時段內包含上班時間，請修正";

        /// <summary>
        /// 每日加班上限時數 (最大數)
        /// </summary>
        public const int DayOvertimeLimit = 12;

        /// <summary>
        /// 預設工作日時數
        /// </summary>
        public const int DefaultWorkingHours = 8;

        /// <summary>
        /// 申請天災停班日加班之訊息
        /// </summary>
        public const string DisasterDayWorkOvertimeError = "請注意：天災停班非因業務需求經主管要求加班者，一律不得申請加班。如係應主管要求加班，請提醒部門主管應於事件發生次一日即時填報於內網首頁常用連結公告之—臺北市政府勞動局天災事變突發事件停止假期延長工時通報單，送交行政處以為加班憑據";

        /// <summary>
        /// 司機每月加班時數上限
        /// </summary>
        public const int DriverMonthOvertimeHoursLimit = 92;

        /// <summary>
        /// 司機的職等代碼
        /// </summary>
        public const string DriverRankNo = "091";

        /// <summary>
        /// 【加班截止時間】早於 【加班起始時間】 之訊息
        /// </summary>
        public const string EndTimeMustLaterThanStartTime = "【加班截止時間】需晚於【加班起始時間】，請修正";

        /// <summary>
        /// 加班統計第一季顯示字串
        /// </summary>
        public const string FirstQuarter = "1-3月";

        /// <summary>
        /// 加班統計第四季顯示字串
        /// </summary>
        public const string FourthQuarter = "10-12月";

        public const string FuneralLeaveApplyRepeatly = "您已申請過此假別細項，不得重複申請";

        /// <summary>
        /// 喪假第一次就超假
        /// </summary>
        public const string FuneralLeaveOneTimeOverPermittedDays = "您本次請假{X}天，此事件可休{Y}天，不得超假";

        /// <summary>
        /// 喪假超假
        /// </summary>
        public const string FuneralLeaveOverAvailableDays = "您本次請假{X}天，此事件剩餘可休{Y}天，不得超假";

        /// <summary>
        /// 加班申請卡提醒主管至少填報8小時加班時數 之訊息
        /// </summary>
        public const string HolidayB1CardAppSignerWarning = "同仁本次預定加班日期為{dayType}，預定加班時數為{hours}小時，依勞基法規定該日加班如不足8小時仍須支給8小時加班費";

        /// <summary>
        /// 加班卡至少填報8小時計畫編號及加班時數 之訊息
        /// </summary>
        public const string HolidayB1CardAppWarning = "請注意：該日如加班，屆時【加班卡】請依勞基法規定至少填報8小時計畫編號及加班時數";


        /// <summary>
        /// 預定加班時數為零 之訊息
        /// </summary>
        public const string HoursAreZero = "加班時數不得為零";

        public const string InvalidLeaveSubNumber = "假別細項不正確";


        public const string IsOffServiceStaff = "非在職";

        /// <summary>
        /// 不得跨年請假
        /// </summary>
        public const string Leave_Date_Across_Years = "不得跨年請假，請分年度填報請假卡";

        public const string Leave_Date_Only_Allow_WorkDays = "請假日期限填上班日";

        public const string Leave_Day_Time_Error = "請假時間須從正常上班時間～正常下班時間"; //請假單位為日

        public const string Leave_End_Date_Must_Greater_Than_Start_Date = "【請假截止日期時間】須大於【請假起始日期時間】";

        public const string Leave_HalfDay_Time_Error = "【{X}】請假最小單位為半日，請調整請假時間";

        public const string Leave_Time_Error = "請假時間須介於正常上班時間～最晚彈性下班時間";


        public const string Leave_Used_Other_Related_Error = $"本事件假卡已為其他假卡的相關單號，無法抽單，請通知行政處協助";
        
        /// <summary>
        /// 請假時間超過每日正常工時
        /// </summary>
        public const string Leave_Time_Larger_Than_Working_Hours = "請假時數超過每日正常工時，請修改請假時間";


        /// <summary>
        /// 累計請假時間超過每日正常工時
        /// </summary>
        public const string Accumulated_Leave_Time_Larger_Than_Working_Hours = "您於 {date} 累計請假 {hours} 小時，已超過每日正常工時，請修改請假時間";

        /// <summary>
        /// 請假時數為0 之錯誤訊息
        /// </summary>
        public const string Leave_Time_Zero = "請假時數為0，無須請假";

        /// <summary>
        /// 「起始時間」或「截止時間」在休息時間 之錯誤訊息
        /// </summary>
        public const string LeaveAtRestTimeError = "【請假起始或截止時間】不得為休息時間";

        /// <summary>
        /// 為應檢附相關證明文件之假卡
        /// </summary>
        public const string LeaveCertificateRequired = "此為應檢附相關證明文件之假卡，請查核";

        /// <summary>
        /// 防災復原假簽核提醒應檢附相關證明文件之假卡
        /// </summary>
        public const string DisasterRecoveryLeaveCertificateRequired = "提醒您該同仁所申請之「救災復員假」需檢附相關證明文件及核准簽";

        /// <summary>
        /// 請假截止時間有誤 之錯誤訊息
        /// </summary>
        public const string LeaveEndTimeError = "請假截止時間有誤";

        public const string LeaveMustHaveEventDate = "【事件發生日】不可空白";

        public const string LeaveMustHaveReason = "【請假事由】不可空白";

        /// <summary>
        /// 重覆填報請假卡 之訊息
        /// </summary>
        public const string LeaveTimeOverlay = "請假日期時間不得重複填報";

        /// <summary>
        /// 補休日最少加班可報時數
        /// </summary>
        public const int MakeUpHolidayLeastHours = 8;

        /// <summary>
        /// 婚假第一次就超假
        /// </summary>
        public const string MarriageLeaveOneTimeOverPermittedDays = "您本次請假{X}天，此事件可休{Y}天，不得超假";

        /// <summary>
        /// 婚假超假
        /// </summary>
        public const string MarriageLeaveOverAvailableDays = "您本次請假{X}天，此事件剩餘可休{Y}天，不得超假";

        // 產假一次性超假
        public const string MaternityLeaveOneTimeOverPermittedDays = "您本次請假{X}個日曆天，可休{Y}個日曆天，不得超假";

        // 產假超假
        public const string MaternityLeaveOverPermittedDays = "您本次請假{X}個日曆天，剩餘可休{Y}個日曆天，不得超假";

        /// <summary>
        /// 每月即將達到加班上限時數
        /// </summary>
        public const int MonthCloseToHoursLimit = 40;

        /// <summary>
        /// 加班時數當月即將達上限的訊息(warn)
        /// </summary>
        public const string MonthCloseToHoursUpperBoundMessage = "提醒您當月填報之加班時數已達{hours}小時，即將達加班時數上限。如非必要，請準時下班";

        /// <summary>
        /// 每月加班上限時數
        /// </summary>
        public const double MonthOvertimeHoursLimit = 54;

        /// <summary>
        /// 加班卡
        /// </summary>
        public const string NeedB1CardAppError = "請先確認已填加班申請卡，且經部門主管核可，方可填報";

        public const string No_Deputy_Error = "【代理人】不可空白";

        public const string No_EndDate_Error = "【截止日期時間】不可空白";

        public const string No_LeaveKind_Detail_Error = "【假別細項】不可空白";

        public const string No_LeaveKind_Error = "【假別】不可空白";

        public const string No_StartDate_Error = "【起始日期時間】不可空白";

        public const string Not_allowed_to_take_leave_across_the_new_year = "不得跨年請假";

        /// <summary>
        /// 產檢假第一次就超假
        /// </summary>
        public const string ObstetricInspectionLeaveOneTimeOverAvailableHours = "您本次請假{X}小時，此事件可休{Y}小時，不得超假";

        /// <summary>
        /// 產檢假超假
        /// </summary>
        public const string ObstetricInspectionLeaveOverAvailableHours = "您本次請假{X}小時，此事件剩餘可休{Y}小時，不得超假";

        /// <summary>
        /// 重覆填報加班申請卡 之訊息
        /// </summary>
        public const string OnlyOneB1CardAppPerDay = "一天限填一張加班申請卡，請勿重複填報";

        /// <summary>
        /// 重覆填報加班卡 之訊息
        /// </summary>
        public const string OnlyOneB1CardPerDay = "一天限填一張加班卡，請勿重複填報";

        public const string OnlyWomenCanApplyForMaternityLeave = "產假限女性申請";

        public const string OnlyWomenCanApplyForMenstrualLeave = "生理假限女性申請";

        public const string OnlyWomenCanApplyForObstetricInspectionLeave = "產檢假限女性申請";

        /// <summary>
        /// 請假起始及截止日期時間超出事件的請假期限範圍
        /// </summary>
        public const string OverLeaveRange = "【請假起始或截止日期時間】已超出【請假期限】";

        /// <summary>
        /// 陪產檢及陪產假第一次就超假
        /// </summary>
        public const string PaternityLeaveOneTimeOverAvailableHours = "您本次請假{X}小時，此事件可休{Y}小時，不得超假";

        /// <summary>
        /// 陪產檢及陪產假超假
        /// </summary>
        public const string PaternityLeaveOverAvailableHours = "您本次請假{X}小時，此事件剩餘可休{Y}小時，不得超假";

        public const string QuarantineCareLeave_Pre_Alert = "【防疫照顧假】不支薪，依法不會有其他不利之處分及影響";

        public const string QuarantineIsolationLeave_Pre_Alert = "【防疫隔離假】不支薪，依法不會有其他不利之處分及影響";

        /// <summary>
        /// 每季加班上限時數
        /// </summary>
        public const int QuarterOvertimeHoursLimit = 138;

        /// <summary>
        /// 當月填報之加班時數已達當月上限 之錯誤訊息
        /// </summary>
        public const string ReachMonthOvertimeLimitError = "當月填報之加班時數已達當月上限";

        /// <summary>
        /// 當季填報之加班時數已達當季上限 之錯誤訊息
        /// </summary>
        public const string ReachQuarterOvertimeLimitError = "當季填報之加班時數已達當季上限";

        /// <summary>
        /// 當季填報之加班時數已達當季上限 之警告訊息
        /// </summary>
        public const string ReachQuarterOvertimeLimitWarning = "提醒您當季填報之加班時數已達當季上限";

        /// <summary>
        /// 該日實際加班時數不得超過申請時數之訊息
        /// </summary>
        public const string SaturdayOvertimeCannotOverApplyHours = "請注意：該日實際加班時數請勿超過申請時數";

        /// <summary>
        /// 加班統計第二季顯示字串
        /// </summary>
        public const string SecondQuarter = "4-6月";

        /// <summary>
        /// 組長填報之加班時數已達上限
        /// </summary>
        public const string SpecialStaffAboveMonthOvertimeLimitError = "【加班】時數已達上限";

        /// <summary>
        /// 抽單錯誤訊息
        /// </summary>
        public const string WithdrawErrorMessage = $"抽單發生錯誤，請聯絡系統管理者";

        /// <summary>
        /// 無抽單權限錯誤訊息
        /// </summary>
        public const string WithdrawNoRightError = $"無抽單權限，無法抽單";

        /// <summary>
        /// 送出表單錯誤訊息
        /// </summary>
        public const string SubmitErrorMessage = $"送出表單發生錯誤，請聯絡系統管理者";

        /// <summary>
        /// 送出表單附件錯誤訊息
        /// </summary>
        public const string SaveAttachmentErrorMessage = $"表單已送出，附件儲存發生問題，請聯絡系統管理者";

        /// <summary>
        /// 更新表單錯誤訊息
        /// </summary>
        public const string UpdateErrorMessage = $"更新表單發生錯誤，請聯絡系統管理者";

        /// <summary>
        /// 中間過程錯誤訊息
        /// </summary>
        public const string GeneralErrorMessage = $"發生錯誤，請聯絡系統管理者";

        /// <summary>
        /// 申請週日加班時之訊息
        /// </summary>
        public const string SundayCannotWorkOvertime = "週日為例假日，不得填報加班卡";

        /// <summary>
        /// 加班統計第三季顯示字串
        /// </summary>
        public const string ThirdQuarter = "7-9月";

        /// <summary>
        /// 加班時間重疊 之訊息
        /// </summary>
        public const string TimeOverlapped = "加班時間請勿重疊";

        public const string VaccinationLeave_Pre_Alert = "【疫苗接種假】不支薪，依法不會有其他不利之處分及影響";

        /// <summary>
        /// 每日正常工時 (通則 8小時)
        /// </summary>
        public static readonly int GeneralWorkingHours = 8;

        /// <summary>
        /// 每日最多可工作時數 (12小時)
        /// </summary>
        public static readonly int MaxWorkHours = 12;

        /// <summary>
        /// 最少可加班時數 (1 小時)
        /// </summary>
        public static readonly int MinOvertimeHours = 1;

        /// <summary>
        /// CheckResult 無此員工之檢查錯誤
        /// </summary>
        public static readonly CardCheckResult ResultBadEmployee = new CardCheckResult(3000301, CardStatusEnum.Error, AttendanceParameters.BadEmployeeNumber);

        /// <summary>
        /// CheckResult Submit Error
        /// </summary>
        public static readonly CardCheckResult ResultSubmitError = new CardCheckResult(3000399, CardStatusEnum.Error, AttendanceParameters.SubmitErrorMessage);


        /// <summary>
        /// CheckResult Update A1Card Error
        /// </summary>
        public static readonly CardCheckResult ResultUpdateA1CardError = new CardCheckResult(1000302, CardStatusEnum.Error, AttendanceParameters.UpdateErrorMessage);

        /// <summary>
        /// CheckResult OK
        /// </summary>
        public static readonly CardCheckResult ResultOk = new CardCheckResult(0, CardStatusEnum.Success, string.Empty);

        /// <summary>
        /// CheckResult GeneralError
        /// </summary>
        public static readonly CardCheckResult ResultGeneralError = new CardCheckResult(-100, CardStatusEnum.Error, AttendanceParameters.GeneralErrorMessage);
    }
}
