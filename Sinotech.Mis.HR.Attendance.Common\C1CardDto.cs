﻿using System;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 請假卡DTO，欄位順序不可更改
    /// </summary>
    public class C1CardDto
    {
        /// <summary>
        /// 流水號
        /// </summary>
        public int? ID { get; set; }

        /// <summary>
        /// 表單識別碼
        /// </summary>
        public Guid FormUID { get; set; }

        /// <summary>
        /// 員工編號
        /// </summary>
        public string C1_EMPNO { get; set; } = string.Empty;

        /// <summary>
        /// 民國年月
        /// </summary>
        public string C1_YYMM { get; set; } = string.Empty;
        /// <summary>
        /// 起始日
        /// </summary>
        public string C1_SDD { get; set; } = string.Empty;
        /// <summary>
        /// 起始時
        /// </summary>
        public string C1_SHH { get; set; } = string.Empty;
        /// <summary>
        /// 起始分
        /// </summary>
        public string C1_SMM { get; set; } = string.Empty;

        /// <summary>
        /// 截止日
        /// </summary>
        public string C1_EDD { get; set; } = string.Empty;

        /// <summary>
        /// 截止時
        /// </summary>
        public string C1_EHH { get; set; } = string.Empty;

        /// <summary>
        /// 截止分
        /// </summary>
        public string C1_EMM { get; set; } = string.Empty;

        /// <summary>
        /// 本次請假總時數
        /// </summary>
        public int C1_HOUR { get; set; }

        /// <summary>
        /// 請假別
        /// </summary>
        /// <value>
        /// 01:特別休息假,02:婚假,03:公假,04:事假,05:公出,06:出差,07:產假,08:陪產假,09:公傷假,10:病假,
        /// 11:喪假,12:保留代休假,13:休國外假,14:延休假,15:產檢假,16:生理假,
        /// 17:防疫照顧假,18:防疫隔離假,19:家庭照顧假,20:疫苗接種假,21:生日假,22:防災假
        /// </value>
        public string C1_CODE { get; set; } = string.Empty;

        /// <summary>
        /// 假別細項
        /// </summary>
        public string C1_CODE2 { get; set; } = "  ";

        /// <summary>
        /// 事件發生日
        /// </summary>
        public DateTime? C1_EventDate { get; set; } = null;

        /// <summary>
        /// 請假期限-起始日期時間 時間預設為00:00
        /// </summary>
        public DateTime? C1_DeadlineStartDate { get; set; } = null;

        /// <summary>
        /// 請假期限-截止日期時間 時間預設為23:59
        /// </summary>
        public DateTime? C1_DeadlineEndDate { get; set; } = null; 

        /// <summary>
        /// 相關單號        
        /// 預設空白字串，事件的第一筆使用預設值，第二筆以後為事件第一筆表單單號
        /// </summary>
        public string C1_RelationSheetNo { get; set; } = "";

        /// <summary>
        /// 假別上限
        /// </summary>
        public int? C1_LeaveMaximum { get; set; } = null;

        /// <summary>
        /// 假別上限單位
        /// </summary>
        public string? C1_LeaveUnit { get; set; } = null;

        /// <summary>
        /// 出差計畫編號
        /// </summary>
        public string C1_PrjNo { get; set; } = string.Empty;

        /// <summary>
        /// 出差地點
        /// </summary>
        public string C1_Location { get; set; } = string.Empty;

        /// <summary>
        /// 請假事由
        /// </summary>
        public string C1_Reason { get; set; } = string.Empty;

        /// <summary>
        /// 代理人
        /// </summary>
        public string C1_Agent { get; set; } = string.Empty;

        /// <summary>
        /// YYYMMDD    填卡日期-民國年月日
        /// </summary>
        public string C1_WYYMMDD { get; set; } = string.Empty;

        /// <summary>
        /// YYYMMDD    結案核卡日期-民國年月日
        /// </summary>
        public string? C1_AYYMMDD { get; set; }

        /// <summary>
        /// 簽核狀態
        /// </summary>
        /// <value>
        /// 1:未簽核 2:核可 3:不核可  4:抽單
        /// </value>
        public int C1_STATUS { get; set; }

        /// <summary>
        /// 超假狀態  Y:超假 N:未超假
        /// </summary>
        public string C1_OVER { get; set; } = "N";

        /// <summary>
        /// 請假卡卡號 （表單單號）
        /// </summary>
        public string C1_SHEETNO { get; set; } = string.Empty;

        /// <summary>
        /// 請假卡表單序號 （跨月請假時使用）
        /// </summary>
        public string C1_SERIALNO { get; set; } = "1";

        /// <summary>
        /// 資料來源：
        /// FlowMaster：FlowMaster系統
        /// Attendance：本系統
        /// SECINC：系統承辦人
        /// </summary>
        public string C1_SOURCE { get; set; } = "Attendance";

        /// <summary>
        /// 請保留代休假時，所扣除的上個月剩餘保留代休假時數，目前在本系統沒用到
        /// </summary>
        public int C1_PREMON { get; set; } = 0;

        /// <summary>
        /// 是否匯出薪資系統：0-未匯出 1-已正常匯出
        /// </summary>
        public string C1_CHECK { get; set; } = "0";

        /// <summary>
        /// 匯出日期
        /// </summary>
        public DateTime? ExpDate { get; set; } = null;

        /// <summary>
        /// 請假起始日期時間
        /// </summary>
        public DateTime C1_StartDate { get; set; }

        /// <summary>
        /// 請假截止日期時間
        /// </summary>
        public DateTime C1_EndDate { get; set; }

        /// <summary>
        /// 填報日期時間
        /// </summary>
        public DateTime C1_WDate { get; set; }

        /// <summary>
        /// 核卡日期時間
        /// </summary>
        public DateTime? C1_ADate { get; set; }

        /// <summary>
        /// 最後修改者員工編號
        /// </summary>
        public string? UpdatedEmpNo { get; set; }

        /// <summary>
        /// 最後修改者姓名
        /// </summary>
        public string? UpdatedName { get; set; }

        /// <summary>
        /// 最後修改日期時間
        /// </summary>
        public DateTime? UpdatedTime { get; set; }

        /// <summary>
        /// 最後修改來源IP 位址
        /// </summary>
        public string? UpdatedIP { get; set; }

        /// <summary>
        /// 最後修改來源電腦名稱
        /// </summary>
        public string? UpdatedHost { get; set; }

        public C1CardDto ShallowCopy()
        {
            return (C1CardDto)this.MemberwiseClone();
        }
    }
}
