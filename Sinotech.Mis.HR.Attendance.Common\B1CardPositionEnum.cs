﻿namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// Enumeration of B1CardPosition
    /// </summary>
    public enum B1CardPositionEnum
    {
        /// <summary>
        /// 董事長
        /// </summary>
        Chairman = 591,
        /// <summary>
        /// 執行長
        /// </summary>
        President = 593,
        /// <summary>
        /// 副執行長
        /// </summary>
        VicePresident = 585,
        /// <summary>
        /// 經理
        /// </summary>
        Manager = 491,
        /// <summary>
        /// 副理
        /// </summary>
        DeputyManager = 481,
        /// <summary>
        /// 主任
        /// </summary>
        Director = 492,
        /// <summary>
        /// 副主任
        /// </summary>
        DeputyDirector = 482,
        /// <summary>
        /// 主辦會計
        /// </summary>
        ChiefAccountant = 484,
        /// <summary>
        /// 司機
        /// </summary>
        Driver = 91,
        /// <summary>
        /// 組長
        /// </summary>
        SectionChief = 471,
        /// <summary>
        /// 計畫組長
        /// </summary>
        ProjectSectionChief = 472,
        /// <summary>
        /// 環研實驗室主任
        /// </summary>
        EnvLabChief = 462,
        /// <summary>
        /// 一般員工
        /// </summary>
        GeneralStaff = 0,
        /// <summary>
        /// 計畫主管
        /// </summary>
        ProjectDirector = 497,
        /// <summary>
        /// 計畫副主管
        /// </summary>
        ProjectDeputyDirector = 486,
        /// <summary>
        /// 不在列表的職等
        /// </summary>
        NotInList = -1,
    }
}
