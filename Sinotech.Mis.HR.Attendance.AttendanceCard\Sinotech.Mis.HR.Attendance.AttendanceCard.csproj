﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<RootNamespace>$(MSBuildProjectName)</RootNamespace>
		<TypeScriptCompileBlocked>true</TypeScriptCompileBlocked>
		<TypeScriptToolsVersion>Latest</TypeScriptToolsVersion>
		<IsPackable>false</IsPackable>
		<SpaRoot>ClientApp\</SpaRoot>
		<DefaultItemExcludes>$(DefaultItemExcludes);$(SpaRoot)node_modules\**</DefaultItemExcludes>
		<StartupObject>Sinotech.Mis.HR.Attendance.AttendanceCard.Program</StartupObject>
		<OutputType>Exe</OutputType>
		<Company>財團法人中興工程顧問社</Company>
		<ApplicationIcon>favicon.ico</ApplicationIcon>
		<SatelliteResourceLanguages>en;zh-Hant;</SatelliteResourceLanguages>
		<GenerateDocumentationFile>True</GenerateDocumentationFile>
		<NoWarn>$(NoWarn);1591</NoWarn>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Authentication.Negotiate" Version="8.0.10" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.10" />
		<PackageReference Include="Microsoft.AspNetCore.SpaServices.Extensions" Version="8.0.10" />
		<PackageReference Include="Microsoft.Data.SqlClient" Version="5.2.2" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="Serilog" Version="4.1.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="8.0.3" />
		<PackageReference Include="Serilog.Enrichers.CorrelationId" Version="3.0.1" />
		<PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
		<PackageReference Include="Serilog.Enrichers.Process" Version="3.0.0" />
		<PackageReference Include="Serilog.Enrichers.Thread" Version="4.0.0" />
		<PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
		<PackageReference Include="Serilog.Settings.Configuration" Version="8.0.4" />
		<PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
		<PackageReference Include="Serilog.Sinks.MSSqlServer" Version="7.0.2" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.9.0" />
		<PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
		<PackageReference Include="System.DirectoryServices.Protocols" Version="8.0.0" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\Sinotech.Mis.HR.Attendance.BusinessLogic\Sinotech.Mis.HR.Attendance.BusinessLogic.csproj" />
	  <ProjectReference Include="..\Sinotech.Mis.HR.Attendance.Common\Sinotech.Mis.HR.Attendance.Common.csproj" />
	  <ProjectReference Include="..\Sinotech.Mis.HR.Attendance.DataAccess.Interfaces\Sinotech.Mis.HR.Attendance.DataAccess.Interfaces.csproj" />
	  <ProjectReference Include="..\Sinotech.Mis.HR.Attendance.DataAccess.Ado\Sinotech.Mis.HR.Attendance.DataAccess.Ado.csproj" />
	  <ProjectReference Include="..\Sinotech.Mis.Helpers\Sinotech.Mis.Helpers.csproj" />
	  <ProjectReference Include="..\Sinotech.Mis.HR.Attendance.Extensions.ServiceCollection\Sinotech.Mis.HR.Attendance.Extensions.ServiceCollection.csproj" />
	  <ProjectReference Include="..\Sinotech.Mis.Serilog.Utilities\Sinotech.Mis.Serilog.Utilities.csproj" />
	  <ProjectReference Include="..\Sinotech.Mis.Utilities.DataAccess.Ado\Sinotech.Mis.Utilities.DataAccess.Ado.csproj" />
	  <ProjectReference Include="..\Sinotech.Mis.HR.Attendance.Utilities\Sinotech.Mis.HR.Attendance.Utilities.csproj" />
	</ItemGroup>

  <ItemGroup>
    <!-- Don't publish the SPA source files, but do show them in the project files list -->
    <Content Remove="$(SpaRoot)**" />
    <Content Remove="Upload\**" />
    <EmbeddedResource Remove="Upload\**" />
    <None Remove="$(SpaRoot)**" />
    <None Include="$(SpaRoot)**" Exclude="$(SpaRoot)node_modules\**" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Logs\" />
    <Folder Include="Upload\" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <None Update="Approve.template">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="DeputyApprove.template">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="CommentMail.template">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="DisagreeMail.template">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Notify.template">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

	<Target Name="DebugEnsureNodeEnv" BeforeTargets="Build" Condition=" '$(Configuration)' == 'Debug' And !Exists('$(SpaRoot)node_modules') ">
		<!-- Ensure Node.js is installed -->
		<Exec Command="node --version" ContinueOnError="true">
			<Output TaskParameter="ExitCode" PropertyName="ErrorCode" />
		</Exec>
		<Error Condition="'$(ErrorCode)' != '0'" Text="Node.js is required to build and run this project. To continue, please install Node.js from https://nodejs.org/, and then restart your command prompt or IDE." />
		<Message Importance="high" Text="Restoring dependencies using 'npm'. This may take several minutes..." />
		<Exec WorkingDirectory="$(SpaRoot)" Command="npm install" />
	</Target>

	<Target Name="PublishRunVite" AfterTargets="ComputeFilesToPublish">
		<!-- As part of publishing, ensure the JS resources are freshly built in production mode -->
    <Message Importance="high" Text="Restoring dependencies using 'npm'. This may take several minutes..." />
		<Exec WorkingDirectory="$(SpaRoot)" Command="npm install" />
		<Exec WorkingDirectory="$(SpaRoot)" Command="npm run build" />

		<!-- Include the newly-built files in the publish output -->
		<ItemGroup>
			<DistFiles Include="$(SpaRoot)dist\**; $(SpaRoot)dist-server\**" />
			<ResolvedFileToPublish Include="@(DistFiles->'%(FullPath)')" Exclude="@(ResolvedFileToPublish)">
				<RelativePath>%(DistFiles.Identity)</RelativePath>
				<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
				<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			</ResolvedFileToPublish>
		</ItemGroup>
	</Target>

	<PropertyGroup Condition="'$(Configuration)' == 'Debug'">
		<TypeScriptTarget>ESNext</TypeScriptTarget>
		<TypeScriptJSXEmit>React</TypeScriptJSXEmit>
		<TypeScriptModuleKind>CommonJS</TypeScriptModuleKind>
		<TypeScriptCompileOnSaveEnabled>False</TypeScriptCompileOnSaveEnabled>
		<TypeScriptNoImplicitAny>False</TypeScriptNoImplicitAny>
		<TypeScriptRemoveComments>False</TypeScriptRemoveComments>
		<TypeScriptOutFile />
		<TypeScriptOutDir />
		<TypeScriptGeneratesDeclarations>False</TypeScriptGeneratesDeclarations>
		<TypeScriptNoEmitOnError>True</TypeScriptNoEmitOnError>
		<TypeScriptSourceMap>True</TypeScriptSourceMap>
		<TypeScriptMapRoot>ClientApp\src</TypeScriptMapRoot>
		<TypeScriptSourceRoot>ClientApp\src</TypeScriptSourceRoot>
	</PropertyGroup>

	<PropertyGroup Condition=" '$(RunConfiguration)' == 'AttendanceCard' " />
	<ProjectExtensions>
		<VisualStudio><UserProperties appsettings_1json__JsonSchema="" NpmRestoreOnProjectOpen="False" /></VisualStudio>
	</ProjectExtensions>
</Project>
