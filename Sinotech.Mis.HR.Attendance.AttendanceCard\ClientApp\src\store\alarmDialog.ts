import { defineStore } from 'pinia'

export const useAlarmDialogStore = defineStore('alarmDialog', {
  state: () => ({
    visible: false,
    message: '',
    targetRoute: ''
  }),
  actions: {
    setVisible(data: boolean) {
      this.visible = data
    },
    setMessage(data: string) {
      this.message = data
    },
    setTargetRoute(data: string) {
      this.targetRoute = data
    },
    setOnCheckMessage() {
      this.visible = false
      this.message = ''
      this.targetRoute = ''
    }
  }
})