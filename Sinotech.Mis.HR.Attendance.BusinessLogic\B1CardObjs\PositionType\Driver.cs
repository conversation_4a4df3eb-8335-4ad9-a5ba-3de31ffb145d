﻿using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Diagnostics.CodeAnalysis;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.PositionType
{
    [ExcludeFromCodeCoverage]
    internal class Driver : B1CardPositionBase
    {
        public Driver
            (string employeeNumber, DateTime overtimeDate, IB1CardDataProvider provider) :
            base(employeeNumber, overtimeDate, provider)
        {
        }

        //司機
        //  不用填寫加班卡
        //  要檢查每日加班時數限制
        //  要檢查每月<=92小時
        //  不用檢查每季加班時數限制（即可大於138小時？）
        //  不要勾稽「週六（休息日或逢國定假日）及彈性放假日」加班時數須與加班申請卡時數(因為不用填寫申請卡)

        internal override bool IsOvertimeAllowed => true;
        protected override bool HasAppliedOvertimeWork() => true;
        protected override B1CardResult CheckDailyOvertimeHours() => DefaultCheckDailyOvertimeHours();
        protected override B1CardResult CheckMonthlyOvertimeHours() => DriverCheckMonthlyOvertimeHours();
        protected override B1CardResult CheckQuarterlyOvertimeHours() => SuccessResult;
        protected override B1CardResult CheckAppliedHours() => SuccessResult;

        private B1CardResult DriverCheckMonthlyOvertimeHours()
        {
            var monthlyHours = GetMonthOvertimeHourInQuota();
            var appliedHours = GetAppliedOvertimeHourInQuota();

            if (monthlyHours + appliedHours > 92)
            {
                return new B1CardResult(
                    code: 538,
                    status: B1CardStatusEnum.Error,
                    message: $"超出彈性每月加班時數"
                );
            }

            return SuccessResult;
        }
        protected override B1CardResult GetWarningResult() => SuccessResult;
    }
}
