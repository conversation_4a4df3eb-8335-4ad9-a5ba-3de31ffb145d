﻿using Sinotech.Mis.HR.Attendance.DataAccess.Interfaces;
using System;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.DataAccess.Ado.Tests
{
    [ExcludeFromCodeCoverage]
    public class C1CardDaoTests
    {
        private readonly IC1CardDao _c1CardDao;

        public C1CardDaoTests(IC1CardDao c1CardDao)
        {
            _c1CardDao = c1CardDao;
        }

        [Theory]
        [InlineData(1, 0)]
        [InlineData(2, 2)]
        [InlineData(5, 0)]
        [InlineData(8, 3)]
        [InlineData(15, 3)]
        public void GetLeaveMessageTest(int leaveNumber, int minimumCount)
        {
            DataTable dt = _c1CardDao.GetLeaveMessage(leaveNumber);
            Assert.NotNull(dt);
            Assert.True(dt.Rows.Count >= minimumCount);
        }

        [Fact]
        public void GetLeaveMessagesTest()
        {
            DataTable dt = _c1CardDao.GetLeaveMessages();
            Assert.NotNull(dt);
            Assert.True(dt.Rows.Count > 10);
        }

        [Theory]
        [InlineData("0349", 2021, 2, true)]
        [InlineData("0349", 2022, 2, true)]
        [InlineData("0349", 2023, 2, true)]
        [InlineData("0395", 2022, 2, false)]
        [InlineData("2268", 2023, 2, false)]
        [InlineData("2295", 2023, 2, false)]
        public void IsMenstrualLeaveAlreadyTakenTest(string employeeNumber, int year, int month, bool expected)
        {
            bool actual = _c1CardDao.IsMenstrualLeaveAlreadyTaken(employeeNumber, year, month);
            Assert.Equal(expected, actual);
        }

        [Theory]
        [InlineData("0349", 1, 0, "2022-01-04", false)]
        [InlineData("0349", 2, 0, "2022-01-04", false)]
        [InlineData("0349", 3, 0, "2022-01-04", false)]
        [InlineData("0349", 4, 0, "2022-01-04", false)]
        [InlineData("0349", 5, 0, "2022-01-04", false)]
        [InlineData("0349", 6, 0, "2022-01-04", false)]
        [InlineData("0349", 7, 0, "2022-01-04", false)]
        [InlineData("0349", 7, 1, "2022-01-04", false)]
        [InlineData("0349", 7, 2, "2022-01-04", false)]
        [InlineData("0349", 7, 3, "2022-01-04", false)]
        [InlineData("0349", 7, 4, "2022-01-04", false)]
        [InlineData("0349", 8, 0, "2022-01-04", false)]
        [InlineData("0349", 9, 0, "2022-01-04", false)]
        [InlineData("0349", 10, 0, "2022-01-04", false)]
        [InlineData("0349", 11, 0, "2022-01-04", false)]
        [InlineData("0349", 11, 1, "2022-01-04", false)]
        [InlineData("0349", 11, 2, "2022-01-04", false)]
        [InlineData("0349", 11, 3, "2022-01-04", false)]
        [InlineData("0349", 11, 4, "2022-01-04", false)]
        [InlineData("0349", 11, 5, "2022-01-04", false)]
        [InlineData("0349", 11, 6, "2022-01-04", false)]
        [InlineData("0349", 11, 7, "2022-01-04", false)]
        [InlineData("0349", 11, 8, "2022-01-04", false)]
        [InlineData("0349", 11, 21, "2022-01-04", false)]
        [InlineData("0349", 11, 22, "2022-01-04", false)]
        [InlineData("0349", 11, 23, "2022-01-04", false)]
        [InlineData("0349", 11, 24, "2022-01-04", false)]
        [InlineData("0349", 11, 25, "2022-01-04", false)]
        [InlineData("0349", 11, 26, "2022-01-04", false)]
        [InlineData("0349", 11, 27, "2022-01-04", false)]
        [InlineData("0349", 11, 28, "2022-01-04", false)]
        [InlineData("0349", 11, 29, "2022-01-04", false)]
        [InlineData("0349", 11, 30, "2022-01-04", false)]
        [InlineData("0349", 11, 31, "2022-01-04", false)]
        [InlineData("0349", 11, 32, "2022-01-04", false)]
        [InlineData("0349", 11, 33, "2022-01-04", false)]
        [InlineData("0349", 11, 34, "2022-01-04", false)]
        [InlineData("0349", 11, 35, "2022-01-04", false)]
        [InlineData("0349", 11, 36, "2022-01-04", false)]
        [InlineData("0349", 11, 51, "2022-01-04", false)]
        [InlineData("0349", 11, 52, "2022-01-04", false)]
        [InlineData("0349", 11, 53, "2022-01-04", false)]
        [InlineData("0349", 11, 54, "2022-01-04", false)]
        [InlineData("0349", 11, 55, "2022-01-04", false)]
        [InlineData("0349", 11, 56, "2022-01-04", false)]
        [InlineData("0349", 12, 0, "2022-01-04", false)]
        [InlineData("0349", 13, 0, "2022-01-04", false)]
        [InlineData("0349", 14, 0, "2022-01-04", false)]
        [InlineData("0349", 15, 0, "2022-01-04", false)]
        [InlineData("0349", 16, 0, "2022-01-04", false)]
        [InlineData("0349", 17, 0, "2022-01-04", false)]
        [InlineData("0349", 18, 0, "2022-01-04", false)]
        [InlineData("0349", 19, 0, "2022-01-04", false)]
        [InlineData("0349", 20, 0, "2022-01-04", false)]
        [InlineData("0349", 21, 0, "2022-01-04", false)]
        [InlineData("0349", 22, 0, "2022-01-04", false)]
        [InlineData("0349", 23, 0, "2022-01-04", false)]
        [InlineData("0349", 24, 0, "2022-01-04", false)]
        [InlineData("0349", 25, 0, "2022-01-04", false)]
        [InlineData("0349", 26, 0, "2022-01-04", false)]
        public void GetC1CardByEventDateTest(string empNo, int leaveNumber, int leaveSubNumber, DateTime eventDate, bool expected)
        {
            DataTable dt = _c1CardDao.GetC1CardByEventDate(empNo, leaveNumber, leaveSubNumber, eventDate);
            Assert.NotNull(dt);
            if(expected)
            {
                Assert.True(dt.Rows.Count > 0);
            }
            else
            {
                Assert.True(dt.Rows.Count == 0);
            }
        }
    }
}