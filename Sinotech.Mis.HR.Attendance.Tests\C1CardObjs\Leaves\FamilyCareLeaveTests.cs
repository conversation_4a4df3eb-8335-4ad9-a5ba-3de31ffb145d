﻿using FakeItEasy;
using Xunit;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves;
using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using System;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves.Tests
{
    public class FamilyCareLeaveTests : TestC1CardBase
    {
        public FamilyCareLeaveTests()
        {
            #region Prepare Data

            _c1Card.LeaveNumber = LeaveKindEnum.FamilyCareLeave;

            #endregion
        }

        [Theory]
        [InlineData(false, FamilyCareLeave.CodeLeaveRecordNotCreateYet)]
        [InlineData(true, FamilyCareLeave.CodeOk)]
        public void TestCanTakeThisLeave(bool ifLeaveRecordExists, int returnCode)
        {
            A.<PERSON>o(() => _c1CardBo.IfLeaveRecordExists(A<string>.Ignored,
                A<DateTime>.Ignored, A<DateTime>.Ignored)).Returns(ifLeaveRecordExists);

            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CanTakeThisLeave();

            Assert.Equal(returnCode, result.Code);
        }

        [Theory]
        [InlineData(56, 30, 8, 112, 0, FamilyCareLeave.CodeOk)]
        [InlineData(56, 50, 6, 112, 0, FamilyCareLeave.CodeOk)]
        [InlineData(56, 50, 8, 112, 0, FamilyCareLeave.CodeExceedQuota)]
        [InlineData(56, 28, 8, 112, 76, FamilyCareLeave.CodeOk)]
        [InlineData(56, 28, 8, 112, 77, FamilyCareLeave.CodeExceedPersonalLeaveQuota)]
        [InlineData(56, 50, 8, 112, 55, FamilyCareLeave.CodeExceedQuota)]
        public void TestExceedQuota(int available, int used, int totalHours,
            int personalLeaveAvailable, int personalLeaveUsed, int returnCode)
        {
            A.CallTo(() => _c1CardBo.GetFamilyCareLeaveYearAvailableHours(A<string>.Ignored, A<DateTime>.Ignored)).Returns(available);
            A.CallTo(() => _c1CardBo.GetFamilyCareLeaveYearUsedHours(A<string>.Ignored, A<DateTime>.Ignored)).Returns(used);
            A.CallTo(() => _c1CardBo.CalculateEmpTakeLeaveWorkingHours(A<string>.Ignored, A<DateTime>.Ignored, A<DateTime>.Ignored)).Returns(totalHours);

            A.CallTo(() => _c1CardBo.GetPersonalLeaveYearAvailableHours(A<string>.Ignored, A<DateTime>.Ignored)).Returns(personalLeaveAvailable);
            A.CallTo(() => _c1CardBo.GetPersonalLeaveYearUsedHours(A<string>.Ignored, A<DateTime>.Ignored)).Returns(personalLeaveUsed);

            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CheckOverPermittedLeaveHours();

            Assert.Equal(returnCode, result.Code);
        }

        [Fact]
        public void TestExceedPersonalLeaveQuota()
        {
            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CheckOverPermittedLeaveHours();

            Assert.Equal(FamilyCareLeave.CodeOk, result.Code);
        }

        [Fact]
        public void TestCheckRequiredFields()
        {
            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CheckRequiredFields();

            Assert.Equal(FamilyCareLeave.CodeOk, result.Code);
        }

        [Theory]
        [InlineData(Gender.Female, true)]
        [InlineData(Gender.Male, true)]
        public void TestGender(Gender gender, bool expected)
        {
            var result = FamilyCareLeave.IsAllowForThisGender(gender);
            Assert.Equal(expected, result);
        }
    }
}
