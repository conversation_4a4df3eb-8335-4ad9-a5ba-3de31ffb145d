<template>
  <router-link
    v-slot="{ navigate }"
    :to="{ name: 'Inbox' }"
    custom
  >
    <button
      type="button"
      class="btn btn-outline-dark bi bi-box-arrow-left mt-2 mx-2"
      :disabled="submitted === true"
      @click="navigate"
    >
      <span>待核卡</span>
    </button>
  </router-link>
  <button
    type="button"
    :class="[
      'btn mt-2 mx-2',
      submitted === true ? 'btn-outline-primary' : 'btn-primary'
    ]"
    :disabled="submitted === true"
    @click="onCardAgree"
  >
    <span>同意</span>
  </button>
  <button
    type="button"
    :class="[
      'btn mt-2 mx-2',
      submitted === true ? 'btn-outline-danger' : 'btn-danger'
    ]"
    :disabled="submitted === true"
    @click="onCardDisagree"
  >
    <span>不同意</span>
  </button>
  <h6 class="my-2 fs-2 text-center">
    <slot name="header" />
  </h6>
  <div
    v-if="modelValue?.card?.RemindSigner === true"
    class="container px-0 mb-2"
  >
    <div
      class="alert alert-danger mb-0"
      role="alert"
    >
      <span>警示：</span>
      <template v-if="remindMessage.length > 1">
        <ol>
          <template
            v-for="(rowMessage, rowMessageIndex) in remindMessage"
            :key="rowMessageIndex"
          >
            <li>
              <span>
                {{ rowMessage }}
              </span>
            </li>
          </template>
        </ol>
      </template>
      <template v-else>
        <span>
          {{ remindMessage.toString() }}
        </span>
      </template>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'
import { REFRESH_PAGE_TOAST_TIME, REFRESH_PAGE_TIME, SYSTEM_ERROR_MESSAGE } from '../api/appConst'
import { useAuthUserStore } from '../store/index'
import { useCardStore } from '../store/card'
import { useAlarmDialogStore } from '../store/alarmDialog'
import { useUiStore } from '../store/ui'
import { routerExtend } from '../router'
import { useFormFlow } from '../composable/formFlow'
import { useAbortController } from '../composable/abortController'
import type { PropType } from 'vue'
import type { CardStoreType, FormFlowType, CardApiType, FormFlowApiType } from '../api/appType'

const userStore = useAuthUserStore()
const { flowRes, onFlowAgree, onFlowDisagree, onFlowReviewError } = useFormFlow()
const { abortController, fetchErrorHandler, abortListener } = useAbortController()

const submitted = ref<boolean>(false)

const confirm = useConfirm()
const toast = useToast()

const props = defineProps({
  modelValue: {
    type: Object as PropType<CardStoreType & FormFlowType>,
    default: () => {}
  },
  approveComment: {
    type: String,
    default: ''
  }
})

const remindMessage = computed<Array<string>>((): Array<string> => {
  let result: Array<string> = []
  if (props.modelValue?.card?.RemindMessage) {
    result = props.modelValue?.card?.RemindMessage.split('\n')
  }
  return result
})

const onCardAgree = (): void => {
  let checkSubmitted: number = 0
  submitted.value = true
  confirm.require({
    group: 'app',
    header: '提醒',
    message: '確認同意？',
    acceptProps: {
      severity: 'primary'
    },
    rejectProps: {
      severity: 'secondary'
    },
    accept: (): void => {
      checkSubmitted++
      if (checkSubmitted === 1) {
        onFlowAgree(userStore, props.modelValue, props.approveComment, abortController.signal).then(() => {
          if (flowRes.value.length === 0) {
            toast.add({
              severity: 'success',
              summary: '已簽核',
              life: REFRESH_PAGE_TOAST_TIME,
              group: 'app'
            })

            useUiStore().toggle(true)
            flowRes.value = ''

            setTimeout(() => {
              routerExtend.pushHandler('Inbox')
            }, REFRESH_PAGE_TIME)
          } else if (flowRes.value.includes('已簽核') === true || flowRes.value.includes('已結案') === true) {
            submitted.value = false
            onFlowApprovedErrorHandler()
          } else {
            submitted.value = false
            onFlowReviewErrorHandler()
          }
        }).catch((err: Error) => {
          console.error(err)
          submitted.value = false
          fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
        })
      }
    },
    reject: (): void => {
      submitted.value = false
    }
  })
}

const onCardDisagree = (): void => {
  let checkSubmitted: number = 0
  submitted.value = true
  confirm.require({
    group: 'app',
    header: '提醒',
    message: '確認不同意？',
    acceptProps: {
      severity: 'primary'
    },
    rejectProps: {
      severity: 'secondary'
    },
    accept: (): void => {
      checkSubmitted++
      if (checkSubmitted === 1) {
        onFlowDisagree(userStore, props.modelValue, props.approveComment, abortController.signal).then(() => {
          if (flowRes.value.length === 0) {
            toast.add({
              severity: 'success',
              summary: '已簽核',
              life: REFRESH_PAGE_TOAST_TIME,
              group: 'app'
            })

            useUiStore().toggle(true)
            flowRes.value = ''

            setTimeout(() => {
              routerExtend.pushHandler('Inbox')
            }, REFRESH_PAGE_TIME)
          } else if (flowRes.value.includes('已簽核') === true || flowRes.value.includes('已結案') === true) {
            submitted.value = false
            onFlowApprovedErrorHandler()
          } else {
            submitted.value = false
            onFlowReviewErrorHandler()
          }
        }).catch((err: Error) => {
          console.error(err)
          submitted.value = false
          fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
        })
      }
    },
    reject: (): void => {
      submitted.value = false
    }
  })
}

/**
 * 如果已簽核，在對話框顯示API回應的訊息
 */
const onFlowApprovedErrorHandler = (): void => {
  useAlarmDialogStore().setMessage(flowRes.value)
  useAlarmDialogStore().setVisible(true)
}

/**
 * 如果簽核發生錯誤，先嘗試重讀表單資料，仍讀不到資料的話，就導向'Inbox'頁面
 */
const onFlowReviewErrorHandler = (): void => {
  onFlowReviewError(userStore, props.modelValue, abortController.signal).then((res: CardApiType & FormFlowApiType) => {
    useCardStore().setData(res)
    useAlarmDialogStore().setMessage(flowRes.value)
    useAlarmDialogStore().setVisible(true)

    if ((res.FormUID as string).length > 0) {
      flowRes.value = ''
    } else {
      useAlarmDialogStore().setTargetRoute('Inbox')
    }
  }).catch((err: Error) => {
    throw err
  })
}

onMounted((): void => {
  abortListener()
})
</script>