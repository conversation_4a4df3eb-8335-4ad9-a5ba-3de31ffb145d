﻿namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 正常正作卡細項
    /// </summary>
    public class A1CardDetail
    {
        /// <summary>
        /// 流水號
        /// </summary>
        public int? ID { get; set; }

        /// <summary>
        /// 計畫編號
        /// </summary>
        public string A1_PROJNO { get; set; } = string.Empty;

        /// <summary>
        /// 每旬的1~11天時數
        /// </summary>
        public string A1_DDHH { get; set; } = string.Empty;

        /// <summary>
        /// 每旬每計畫工作總時數
        /// </summary>
        public int A1_HOUR { get; set; }

        /// <summary>
        /// 正常卡表單序號
        /// </summary>
        /// <value>
        /// 從01開始編號
        /// </value>
        public string A1_SERIALNO { get; set; } = string.Empty;

    }

}
