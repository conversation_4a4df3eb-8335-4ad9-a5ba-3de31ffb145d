﻿using Sinotech.Mis.HR.Attendance.Common;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves.Tests
{
    public class QuarantineCareLeaveTests : TestC1CardBase
    {
        public QuarantineCareLeaveTests()
        {
            #region Prepare Data

            _c1Card.LeaveNumber = LeaveKindEnum.QuarantineCareLeave;

            #endregion
        }

        [Theory]
        [InlineData(Gender.Female, true)]
        [InlineData(Gender.Male, true)]
        public void TestGender(Gender gender, bool expected)
        {
            var result = QuarantineCareLeave.IsAllowForThisGender(gender);
            Assert.Equal(expected, result);
        }

        [Fact]
        public void QuarantineCareLeaveTest()
        {
            // 建立 QuarantineCareLeave 物件
            var leave = new QuarantineCareLeave(_c1Card, _c1CardBo);
            Assert.NotNull(leave);
        }
    }
}