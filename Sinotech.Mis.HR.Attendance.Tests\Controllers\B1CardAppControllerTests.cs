﻿using Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers;
using FakeItEasy;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sinotech.Mis.Extensions.Configuration;
using Sinotech.Mis.HR.Attendance.BusinessLogic;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.Tests;
using System;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.Threading.Tasks;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers.Tests
{

    [ExcludeFromCodeCoverage]
    public class B1CardAppControllerTests
    {
        private readonly IAttendanceBo _attendanceBo;

        private readonly IB1CardBo _b1CardBo;
        private readonly IConfiguration _configuration;
        private readonly ILogger<B1CardAppController> _logger;
        private readonly WorkdayBo _workdayBo;
        private readonly B1CardAppController _b1CardAppController;

        public B1CardAppControllerTests(IAttendanceBo attendanceBo, IB1CardBo b1CardBo, IB1CardAppBo b1CardAppBo, WorkdayBo workdayBo, ILogger<B1CardAppController> logger)
        {
            _attendanceBo = attendanceBo;
            _workdayBo = workdayBo;
            _logger = logger;
            IConfiguration configuration = new ConfigurationBuilder().
    AddJsonFile("appsettings.internet.json", optional: true, reloadOnChange: true).Build();
            _b1CardAppController = new B1CardAppController(attendanceBo, b1CardBo, b1CardAppBo, workdayBo,
                configuration, logger);
            configuration = new ConfigurationBuilder().
    AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).Build();
            _b1CardAppController = new B1CardAppController(attendanceBo, b1CardBo, b1CardAppBo, workdayBo,
                configuration, logger);
            _configuration = configuration;
            string ConnectionStringAttendance = configuration.GetSecuredConnectionString("Attendance");
            TestHelper.ClearData(ConnectionStringAttendance);
        }

        private class MyCheckResult
        {
            public string ErrorMessage { get; set; }
            public string DateAlarmMessage { get; set; }
            public bool IsOvertimeAllowed { get; set; }
        }

        [Theory]
        [InlineData("2021-02-01+08:00", "", false)]
        [InlineData("2021-02-01+08:00", "0274", false)]
        [InlineData("2021-02-01+08:00", "0276", true)]
        [InlineData("2021-02-01+08:00", "0319", false)]
        [InlineData("2021-02-01+08:00", "0391", false)]
        [InlineData("2021-02-01+08:00", "2268", true)]
        [InlineData("2021-02-01+08:00", "0395", true)]
        [InlineData("2021-02-01+08:00", "0349", true)]
        [InlineData("2023-01-01+08:00", "2268", false)]
        [InlineData("2023-01-01+08:00", "0395", false)]
        [InlineData("2023-01-01+08:00", "0349", false)]
        public void DateCanFillB1CardAppTest(DateTime date, string empNo, bool expected)
        {
            string result = _b1CardAppController.DateCanFillB1CardApp(date, empNo);
            var x = JsonConvert.DeserializeObject<MyCheckResult>(result);
            Assert.Equal(expected, x.IsOvertimeAllowed);
        }

        [Fact]
        public void GetB1CardTypesTest()
        {
            string types = _b1CardAppController.GetB1CardTypes();
            string actual = "[{\"Type\":1,\"TypeName\":\"加班\"},{\"Type\":2,\"TypeName\":\"社外加班\"},{\"Type\":3,\"TypeName\":\"補休假\"}]";
            Assert.Equal(actual, types);
        }

        [Fact]
        public async Task SubmitTest()
        {
            B1CardApp b1CardApp = new B1CardApp();
            string strResult = await _b1CardAppController.Submit(b1CardApp);
            B1CardAppCheckResult result = JsonConvert.DeserializeObject<B1CardAppCheckResult>(strResult);
            Assert.False(result.IsValid);
            _b1CardAppController.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo("0395");

            strResult = await _b1CardAppController.Submit(b1CardApp);
            result = JsonConvert.DeserializeObject<B1CardAppCheckResult>(strResult);
            Assert.False(result.IsValid);

            b1CardApp.B1_PrjNo = "RP19553";
            b1CardApp.B1_EmpNo = "0395";
            b1CardApp.B1_Hour = 3;
            b1CardApp.B1_PaidHour = 6;
            b1CardApp.B1_Code = '1';
            b1CardApp.B1_Date = DateTime.Parse("2023-11-03+08:00", new CultureInfo("zh-TW"));
            b1CardApp.CreatedTime = DateTime.Parse("2023-11-03 14:45:33+08:00", new CultureInfo("zh-TW"));
            b1CardApp.FilledTime = DateTime.Parse("2023-11-03 14:05:33+08:00", new CultureInfo("zh-TW")); ;
            b1CardApp.B1_WDate = b1CardApp.FilledTime;
            b1CardApp.B1_Reason = "This is a test.";
            strResult = await _b1CardAppController.Submit(b1CardApp);
            result = JsonConvert.DeserializeObject<B1CardAppCheckResult>(strResult);
            Assert.True(result.IsValid);
            Assert.NotNull(result.FormUID);
        }

        [Fact]
        public async Task SubmitTest_Exception()
        {
            B1CardApp b1CardApp = new B1CardApp();
            b1CardApp.B1_EmpNo = "0395";
            IB1CardAppBo b1CardAppBo = A.Fake<IB1CardAppBo>();
            string strResult = await _b1CardAppController.Submit(b1CardApp);
            B1CardAppCheckResult result = JsonConvert.DeserializeObject<B1CardAppCheckResult>(strResult);
            result.IsValid = true;
            result.IsOvertimeAllowed = true;

            A.CallTo(() => b1CardAppBo.DateCanFillB1CardApp(A<DateTime>.Ignored, A<string>.Ignored)).Returns(result);
            A.CallTo(() => b1CardAppBo.CheckData(A<B1CardApp>.Ignored)).Returns(result);
            A.CallTo(() => b1CardAppBo.AddB1CardApp(
                A<string>.Ignored, A<B1CardApp>.Ignored, A<string>.Ignored, A<string>.Ignored))
            .Throws(new Exception("Test"));
            B1CardAppController controller = new B1CardAppController(
_attendanceBo, _b1CardBo, b1CardAppBo, _workdayBo,
    _configuration, _logger);
            controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo("0395");

            strResult = await controller.Submit(b1CardApp);
            result = JsonConvert.DeserializeObject<B1CardAppCheckResult>(strResult);
            Assert.False(result.IsValid);
            Assert.Equal(AttendanceParameters.GeneralErrorMessage, result.ErrorMessage);
        }

        [Fact]
        public async Task DateCanFillB1CardApp_Exception()
        {
            B1CardApp b1CardApp = new B1CardApp();
            b1CardApp.B1_EmpNo = "0395";
            IB1CardAppBo b1CardAppBo = A.Fake<IB1CardAppBo>();
            string strResult = await _b1CardAppController.Submit(b1CardApp);
            B1CardAppCheckResult result = JsonConvert.DeserializeObject<B1CardAppCheckResult>(strResult);
            result.IsValid = true;
            result.IsOvertimeAllowed = true;

            A.CallTo(() => b1CardAppBo.DateCanFillB1CardApp(A<DateTime>.Ignored, A<string>.Ignored))
               .Throws(new Exception("Test"));

            B1CardAppController controller = new B1CardAppController(
_attendanceBo, _b1CardBo, b1CardAppBo, _workdayBo,
    _configuration, _logger);
            controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo("0395");

            strResult = controller.DateCanFillB1CardApp(DateTime.Now, "0395");

            Assert.Equal("{}", strResult);
        }
    }
}
