﻿<?xml version="1.0" encoding="utf-8"?>
<ClassDiagram MajorVersion="1" MinorVersion="1">
  <Class Name="Sinotech.Mis.HR.Attendance.BusinessLogic.DepartmentBo">
    <Position X="1.5" Y="0.5" Width="2.5" />
    <TypeIdentifier>
      <HashCode>AEAAAIAgAIAAAAEACAACAACAAARAAAAAAhAAIAAACAA=</HashCode>
      <FileName>DepartmentBo.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Sinotech.Mis.HR.Attendance.BusinessLogic.EmployeeBo">
    <Position X="4.5" Y="0.75" Width="2.5" />
    <TypeIdentifier>
      <HashCode>GAAAAAAAAIAGAAEAQAAACAACRAQACAIQABAAgAAKkAA=</HashCode>
      <FileName>EmployeeBo.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Sinotech.Mis.HR.Attendance.BusinessLogic.FormBo">
    <Position X="7.5" Y="0.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>CBkaVWBRgKQhAFUgCZKCRAFMdAQgMECAUwUSxDFJAJQ=</HashCode>
      <FileName>FormBo.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Sinotech.Mis.HR.Attendance.BusinessLogic.ProjectBo">
    <Position X="0.5" Y="0.5" Width="2.75" />
    <TypeIdentifier>
      <HashCode>AIAIAAAAAIABCAAAAEAAAAAAAAQAAAAAAgAAAAAIAAA=</HashCode>
      <FileName>ProjectBo.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Sinotech.Mis.HR.Attendance.BusinessLogic.WorkdayBo">
    <Position X="10.25" Y="1" Width="3" />
    <TypeIdentifier>
      <HashCode>ARAAAIAAAIAAAABIAKAACQACAgQgAAAAAIQQgEAiAEQ=</HashCode>
      <FileName>WorkdayBo.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Font Name="Microsoft JhengHei UI" Size="9" />
</ClassDiagram>