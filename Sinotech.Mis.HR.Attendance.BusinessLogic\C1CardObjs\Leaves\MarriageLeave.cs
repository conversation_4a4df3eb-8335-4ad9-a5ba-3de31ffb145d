﻿using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Collections.Generic;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves
{
    /// <summary>
    /// 婚假類別
    /// </summary>
    [LeaveKind(LeaveKindEnum.MarriageLeave)]
    public class MarriageLeave : LeaveWithEvent
    {

        /// <summary>
        /// 建構子
        /// </summary>
        /// <param name="c1Card"></param>
        /// <param name="c1CardBo"></param>
        public MarriageLeave(C1Card c1Card, IC1CardBo c1CardBo) : base(c1Card, c1CardBo)
        {
        }

        /// <summary>
        /// 檢查是否能請此假
        /// </summary>
        /// <returns></returns>
        internal override CardCheckResult CanTakeThisLeave()
        {
            return ResultOk;
        }

        /// <summary>
        /// 檢查剩餘可休  
        /// </summary>
        /// <returns></returns>
        internal override CardCheckResult CheckRemainHours()
        {
            CardCheckResult result = ResultOk;
            if (_c1Card.EventDate != null && _c1Card.EventDate != DateTime.MinValue && _c1Card.EventDate != DateTime.MaxValue)
            {
                //若本次請假超過可請，且無相關單號
                if (string.IsNullOrWhiteSpace(_c1Card.RelatedFormNumber) && TotalHours > PermittedHours)
                {
                    result = GenerateOverLeaveDaysError(PermittedDays,
                        // $"您本次請假{Days}天，此事件可休{restDay}天，不得超假";
                        3002301, AttendanceParameters.MarriageLeaveOneTimeOverPermittedDays);
                    return result;
                }
                // 讀取DB，找出同一事件發生日，計算剩餘可休
                //List<C1Card> c1Cards = _c1CardBo.GetC1CardByEventDate(_c1Card.EmpNo, _c1Card.LeaveNumber, _c1Card.LeaveSubNumber, (DateTime)_c1Card.EventDate);
                //if (c1Cards.Count > 0)
                //{
                //    int usedHours = 0;
                //    foreach (var c1Card in c1Cards)
                //    {
                //        usedHours += c1Card.Hours;
                //    }
                //    int remainHours = PermittedHours - usedHours;
                //    //若本次請假超過剩餘可休
                //    if (TotalHours > remainHours)
                //    {
                //        double remainDays = remainHours * 1.0 / AttendanceParameters.DefaultWorkingHours;
                //        result = GenerateOverLeaveDaysError(remainDays,
                //            // $"您本次請假{Days}天，此事件剩餘可休{remainDays}天，不得超假";
                //            3002302, AttendanceParameters.MarriageLeaveOverAvailableDays);
                //        return result;
                //    }
                //}

                // 若有相關單號
                if (!string.IsNullOrWhiteSpace(_c1Card.RelatedFormNumber))
                {
                    int usedHours = 0;
                    List<EventRelationRecord> records = _c1CardBo.GetEventRelatedRecord(_c1Card.RelatedFormNumber);
                    // 錯誤判斷
                    foreach (var record in records)
                    {
                        usedHours += record.LeaveHour;
                    }
                    int remainHours = PermittedHours - usedHours;
                    //若本次請假超過剩餘可休
                    if (TotalHours > remainHours)
                    {
                        double remainDays = remainHours * 1.0 / AttendanceParameters.DefaultWorkingHours;
                        result = GenerateOverLeaveDaysError(remainDays,
                            // $"您本次請假{Days}天，此事件剩餘可休{remainDays}天，不得超假";
                            3002302, AttendanceParameters.MarriageLeaveOverAvailableDays);
                        return result;
                    }
                }
            }
            else
            {
                result = ResultEventDateFieldRequired;
            }
            return result;
        }

        /// <summary>
        /// 計算最早可請假日期與最晚可請假日期
        /// </summary>
        /// <param name="eventDate">事件發生日</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public override (DateTime, DateTime) CalculateLeavePermittedPeriod(DateTime eventDate, string empNo)
        {
            DateTime startDate = eventDate.Date.AddDays(-10); // 事件發生日前十日
            DateTime endDate = startDate.AddMonths(3).AddDays(1).AddSeconds(-1);   // 請假期限起始日期的月份加上三個月
            return (startDate, endDate);
        }

        /// <summary>
        /// 此性別是否可請此假別
        /// </summary>
        /// <param name="gender"></param>
        /// <returns></returns>
        public static bool IsAllowForThisGender(Gender gender)
        {
            return true;
        }

    }
}
