using FakeItEasy;
using Sinotech.Mis.Common;
using Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.PositionType;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.Tests
{
    [ExcludeFromCodeCoverage]
    public class TestB1CardGeneralRules : TestB1CardBase
    {
        [Fact]
        public void TestB1CardGeneralRules_NeedDetail()
        {
            var provider = GetFakeObject(B1CardPositionEnum.GeneralStaff);
            var p = B1CardPositionFactory.GetB1CardPositionObject(provider);

            _b1Card.Details.Clear();
            var result = p.CheckGeneralRules();
            Assert.Equal(301, result.Code);
        }

        [Fact]
        public void TestB1CardGeneralRules_SholdBeSameDate()
        {
            var provider = GetFakeObject(B1CardPositionEnum.GeneralStaff);
            var p = B1CardPositionFactory.GetB1CardPositionObject(provider);

            _b1Card.Details.Clear();
            _b1Card.Details.Add(
                new B1CardDetail()
                {
                    B1_CODE = 1,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now,
                    EndTime = _now + TimeSpan.FromDays(1),
                    Hour = 0,
                    Project = _project1,
                }
            );

            var result = p.CheckGeneralRules();
            Assert.Equal(302, result.Code);
        }

        [Fact]
        public void TestB1CardGeneralRules_TimeRange()
        {
            var provider = GetFakeObject(B1CardPositionEnum.GeneralStaff);
            var p = B1CardPositionFactory.GetB1CardPositionObject(provider);

            _b1Card.Details.Clear();
            _b1Card.Details.Add(
                new B1CardDetail()
                {
                    B1_CODE = 1,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now + TimeSpan.FromHours(2),
                    EndTime = _now + TimeSpan.FromHours(1),
                    Hour = 0,
                    Project = _project1,
                }
            );

            var result = p.CheckGeneralRules();
            Assert.Equal(303, result.Code);
        }

        [Fact]
        public void TestB1CardGeneralRules_MustGreatThanHour()
        {
            var provider = GetFakeObject(B1CardPositionEnum.GeneralStaff);
            var p = B1CardPositionFactory.GetB1CardPositionObject(provider);

            _b1Card.Details.Clear();
            _b1Card.Details.Add(
                new B1CardDetail()
                {
                    B1_CODE = 1,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now,
                    EndTime = _now + TimeSpan.FromHours(0.5),
                    Hour = 0,
                    Project = _project1,
                }
            );

            var result = p.CheckGeneralRules();
            Assert.Equal(305, result.Code);
        }

        [Fact]
        public void TestB1CardGeneralRules_DetailOvertimeHourMustBeCompatible()
        {
            var provider = GetFakeObject(B1CardPositionEnum.GeneralStaff);
            var p = B1CardPositionFactory.GetB1CardPositionObject(provider);

            // 設定合理的計畫資料
            PrepareCompatibleProject(provider);

            // 設定已填寫加班申請卡
            ApplyOvertimeWork(provider);

            // 設定加班卡資料
            _b1Card.Details.Clear();
            _b1Card.Details.Add(
                new B1CardDetail()
                {
                    B1_CODE = 1,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now,
                    EndTime = _now + TimeSpan.FromHours(2),
                    Hour = 2,
                    Project = _project1,
                }
            );

            _b1Card.Details.Clear();
            _b1Card.Details.Add(
                new B1CardDetail()
                {
                    B1_CODE = 1,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now,
                    EndTime = _now + TimeSpan.FromHours(1.5),
                    Hour = 2,
                    Project = _project1,
                }
            );

            Assert.Throws<ArgumentException>(() => p.CheckData());
            //var exception = Assert.Throws<InvalidOperationException>(() => position.CheckOvertime());

        }

        [Fact]
        public void TestB1CardGeneralRules_TotalHourMustBeCompatible()
        {
            var provider = GetFakeObject(B1CardPositionEnum.GeneralStaff);
            var p = B1CardPositionFactory.GetB1CardPositionObject(provider);

            // 設定合理的計畫資料
            PrepareCompatibleProject(provider);

            // 設定已填寫加班申請卡
            ApplyOvertimeWork(provider);

            // 設定加班卡資料
            _b1Card.Details.Clear();
            _b1Card.Details.Add(
                new B1CardDetail()
                {
                    B1_CODE = 1,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now,
                    EndTime = _now + TimeSpan.FromHours(1),
                    Hour = 1,
                    Project = _project1,
                }
            );
            _b1Card.Details.Add(
                new B1CardDetail()
                {
                    B1_CODE = 1,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now + TimeSpan.FromHours(1),
                    EndTime = DateTime.Now + TimeSpan.FromHours(3),
                    Hour = 2,
                    Project = _project1,
                }
            );
            _b1Card.TotalHours = 2;

            Assert.Throws<ArgumentException>(() => p.CheckData());
        }

        private static void ApplyOvertimeWork(IB1CardDataProvider provider)
        {
            // 模擬回傳已經填寫申請加班卡
            A.CallTo(() => provider.GetHasAppliedOvertimeWork()).Returns(true);
        }

        private void PrepareCompatibleProject(IB1CardDataProvider provider)
        {
            // 準備計畫清單
            List<Project> projectList = new List<Project>()
            {
                new Project()
                {
                    PrjNo = _project1,
                    BDate = _now,
                    EDate = DateTime.MaxValue,
                }
            };

            // 模擬回傳計畫清單
            A.CallTo(() => provider.GetProjectList())
                .Returns(projectList);
        }

        [Fact]
        public void TestB1CardGeneralRules_TimeRangeMustNotOverlap()
        {
            var provider = GetFakeObject(B1CardPositionEnum.GeneralStaff);
            var p = B1CardPositionFactory.GetB1CardPositionObject(provider);


            _b1Card.Details.Clear();
            _b1Card.Details.Add(
                new B1CardDetail()
                {
                    B1_CODE = 1,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now,
                    EndTime = _now + TimeSpan.FromHours(1),
                    Hour = 1,
                    Project = _project1,
                }
            );
            _b1Card.Details.Add(
                new B1CardDetail()
                {
                    B1_CODE = 1,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now + TimeSpan.FromHours(0.5),
                    EndTime = _now + TimeSpan.FromHours(2.5),
                    Hour = 2,
                    Project = _project1,
                }
            );
            _b1Card.TotalHours = 3;

            var result = p.CheckGeneralRules();
            Assert.Equal(308, result.Code);
        }

        [Fact]
        public void TestB1CardGeneralRules_ProjectNumberShouldHaveValue()
        {
            var provider = GetFakeObject(B1CardPositionEnum.GeneralStaff);
            var p = B1CardPositionFactory.GetB1CardPositionObject(provider);


            _b1Card.Details.Clear();
            _b1Card.Details.Add(
                new B1CardDetail()
                {
                    B1_CODE = 1,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now,
                    EndTime = _now + TimeSpan.FromHours(1),
                    Hour = 1,
                    Project = string.Empty,
                }
            );
            _b1Card.TotalHours = 1;

            var result = p.CheckGeneralRules();
            Assert.Equal(309, result.Code);
        }

        [Fact]
        public void TestB1CardGeneralRules_ReasonMustHaveValue()
        {
            var provider = GetFakeObject(B1CardPositionEnum.GeneralStaff);
            var p = B1CardPositionFactory.GetB1CardPositionObject(provider);

            _b1Card.Details.Clear();
            _b1Card.Details.Add(
                new B1CardDetail()
                {
                    B1_CODE = 1,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now,
                    EndTime = _now + TimeSpan.FromHours(1),
                    Hour = 1,
                    Project = _project1,
                }
            );
            _b1Card.TotalHours = 1;
            _b1Card.Reason = string.Empty;

            var result = p.CheckGeneralRules();
            Assert.Equal(310, result.Code);
        }

        [Fact]
        public void TestB1CardGeneralRules_AddSignerFormat()
        {
            var provider = GetFakeObject(B1CardPositionEnum.GeneralStaff);
            var p = B1CardPositionFactory.GetB1CardPositionObject(provider);

            _b1Card.Details.Clear();
            _b1Card.Details.Add(
                new B1CardDetail()
                {
                    B1_CODE = 1,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now,
                    EndTime = _now + TimeSpan.FromHours(1),
                    Hour = 1,
                    Project = _project1,
                }
            );
            _b1Card.TotalHours = 1;

            _b1Card.AddSigners = "0001,00012";
            var result = p.CheckGeneralRules();
            Assert.Equal(311, result.Code);
        }

        [Fact]
        public void TestB1CardGeneralRules_AddSignerFormat2()
        {
            var provider = GetFakeObject(B1CardPositionEnum.GeneralStaff);
            var p = B1CardPositionFactory.GetB1CardPositionObject(provider);

            _b1Card.Details.Clear();
            _b1Card.Details.Add(
                new B1CardDetail()
                {
                    B1_CODE = 1,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now,
                    EndTime = _now + TimeSpan.FromHours(1),
                    Hour = 1,
                    Project = _project1,
                }
            );
            _b1Card.TotalHours = 1;

            _b1Card.AddSigners = "0001,A001";
            var result = p.CheckGeneralRules();
            Assert.Equal(312, result.Code);
        }

        [Fact]
        public void TestB1CardGeneralRules_AddSignerMustNotDuplicate()
        {
            var provider = GetFakeObject(B1CardPositionEnum.GeneralStaff);
            var p = B1CardPositionFactory.GetB1CardPositionObject(provider);

            _b1Card.Details.Clear();
            _b1Card.Details.Add(
                new B1CardDetail()
                {
                    B1_CODE = 1,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now,
                    EndTime = _now + TimeSpan.FromHours(1),
                    Hour = 1,
                    Project = _project1,
                }
            );
            _b1Card.TotalHours = 1;

            _b1Card.AddSigners = "0001,0001,0002";
            var result = p.CheckGeneralRules();
            Assert.Equal(313, result.Code);
        }

        [Fact]
        public void TestB1CardGeneralRules_AddSignerCannotBeApplicant()
        {
            var provider = GetFakeObject(B1CardPositionEnum.GeneralStaff);
            var p = B1CardPositionFactory.GetB1CardPositionObject(provider);

            _b1Card.Details.Clear();
            _b1Card.Details.Add(
                new B1CardDetail()
                {
                    B1_CODE = 1,  // 1：加班 2：社外加班 3：補休假
                    StartTime = _now,
                    EndTime = _now + TimeSpan.FromHours(1),
                    Hour = 1,
                    Project = _project1,
                }
            );
            _b1Card.TotalHours = 1;

            _b1Card.AddSigners = "0001,0002,0000";
            var result = p.CheckGeneralRules();
            Assert.Equal(315, result.Code);
        }

    }
}