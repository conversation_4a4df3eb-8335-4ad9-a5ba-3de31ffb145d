﻿namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 上傳附件檔案
    /// </summary>
    public class UploadedFile
    {
        /// <summary>
        /// 附件檔案路徑名稱
        /// </summary>
        /// <value>
        /// The name of the file path.
        /// </value>
        public string FilePathName { get; set; } = string.Empty;
        /// <summary>
        /// 附件檔案名稱 (不包含路徑)
        /// </summary>
        /// <value>
        /// The name of the file.
        /// </value>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// 檔案大小
        /// </summary>
        /// <value>
        /// The file size.
        /// </value>
        public long Size { get; set; }
    }
}
