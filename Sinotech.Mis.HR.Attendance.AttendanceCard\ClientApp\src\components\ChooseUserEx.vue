<template>
  <div class="row">
    <div class="col-12 col-md text-md-center text-nowrap p-1">
      <span class="me-1">部門</span>
      <template v-if="deptTeamsData.length > 0">
        <VueSelect
          class="d-inline-block"
          :style="{ 'min-width': '140px' }"
          label="deptSName"
          :options="deptTeamsData.map((e: any) => { return { deptNo: e.deptNo, deptSName: e.deptSName }}) as Array<DeptType>"
          :clearable="false"
          :reduce="(e: DeptType) => e.deptNo"
          :modelValue="selectedDept"
          @option:selected="onChangeDept"
        >
          <template #no-options>
            查無資料
          </template>
        </VueSelect>
      </template>
      <template v-else>
        <p class="placeholder-glow d-inline-block">
          <span
            class="placeholder"
            :style="{ 'min-width': '140px' }"
          />
        </p>
      </template>
    </div>

    <div
      v-if="toggleTeam"
      class="col-12 col-md text-md-center text-nowrap p-1"
    >
      <span class="me-1">組別</span>
      <VueSelect
        class="d-inline-block"
        :style="{ 'min-width': '140px' }"
        label="teamCName"
        :options="deptTeamsData.find((e: any) => e.deptNo === selectedDept)?.team"
        :clearable="false"
        :reduce="(e: TeamType) => e.teamId"
        :modelValue="selectedTeam"
        @option:selected="onChangeTeam"
      >
        <template #no-options>
          查無資料
        </template>
      </VueSelect>
    </div>

    <div class="col-12 col-md text-md-center text-nowrap p-1">
      <span class="me-1">員工</span>
      <template v-if="usersData.length > 0">
        <VueSelect
          class="d-inline-block"
          :style="{ 'min-width': '140px' }"
          :options="filteredUsersData"
          :clearable="false"
          :reduce="(e: EmployeeStoreType) => e.userId"
          :modelValue="modelValue"
          :filter="filter"
          @option:selected="onChangeEmp"
        >
          <template #no-options>
            查無資料
          </template>
          <template #option="option">
            {{ option.userId + ' ' + option.userName }}
          </template>
          <template #selected-option="option">
            {{ option.userId + ' ' + option.userName }}
          </template>
        </VueSelect>
      </template>
      <template v-else>
        <p class="placeholder-glow d-inline-block">
          <span
            class="placeholder"
            :style="{ 'min-width': '140px' }"
          />
        </p>
      </template>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import VueSelect from 'vue-select'
import type { EmployeeStoreType, DeptType, TeamType } from '../api/appType'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  employeeData: {
    type: Array<EmployeeStoreType>,
    default: []
  },
  filter: {
    type: Function,
    default: () => {}
  }
})
const emits = defineEmits(['change'])

const usersData = ref<Array<EmployeeStoreType>>([])
const filteredUsersData = ref<Array<EmployeeStoreType>>([])
const deptTeamsData = ref<Array<any>>([])
const selectedDept = ref<number>()
const selectedTeam = ref<number>(-1) // -1代表全部

const toggleTeam = computed<boolean>(() => {
  if (selectedDept.value !== undefined) {
    const deptTeamFound = deptTeamsData.value.find((e: any) => e.deptNo === selectedDept.value)
    if (deptTeamFound) {
      if (deptTeamFound.team.length === 1 && deptTeamFound.team[0].teamId === 0) {
        return false
      }
      return true
    }
  }
  return false
})

const onChangeEmp = (event: any): void => {
  emits('change', event.userId)
}

const onChangeDept = (event: any): void => {
  selectedDept.value = event.deptNo
  if (selectedDept.value === undefined) {
    filteredUsersData.value = usersData.value
    filteredUsersData.value.sort((val1: any, val2: any) => (parseInt(val1.userId) - parseInt(val2.userId)))
  } else {
    const changeTeamEvent = {
      teamId: event.teamId ?? -1,
      userId: event.userId
    }
    onChangeTeam(changeTeamEvent)
  }
}

const onChangeTeam = (event: any): void => {
  selectedTeam.value = event.teamId

  if (selectedTeam.value === -1) {
    filteredUsersData.value = usersData.value.filter((element: EmployeeStoreType) =>
      element.deptNo === selectedDept.value
    )
    filteredUsersData.value.sort((val1: any, val2: any) => (parseInt(val1.userId) - parseInt(val2.userId)))
  } else {
    filteredUsersData.value = usersData.value.filter((element: EmployeeStoreType) =>
      element.deptNo === selectedDept.value && element.teamId === selectedTeam.value
    )
  }

  const changeEmpEvent = {
    userId: event.userId ?? filteredUsersData.value[0].userId
  }
  onChangeEmp(changeEmpEvent)
}

/**
 * 設定[員工選單]和[部門組別選單]的資料
 * @param data 
 */
const onSetData = (data: Array<any>): void => {
  data.forEach((employee: any) => {
    const deptFound = deptTeamsData.value.findIndex((e: any) => e.deptNo === employee.deptNo)
    if (deptFound === -1) {
      deptTeamsData.value.push({
        deptNo: employee.deptNo,
        deptSName: employee.deptSName,
        team: []
      })

      deptTeamsData.value[deptTeamsData.value.length - 1].team.push({
        teamId: employee.teamID,
        teamCName: employee.teamCName
      })
    } else {
      if (employee.teamID === 0) {
        deptTeamsData.value[deptFound].team.push({
          teamId: employee.teamID,
          teamCName: employee.teamCName
        })
      } else {
        deptTeamsData.value[deptFound].team.splice(deptTeamsData.value[deptFound].team.length - 1, 0, {
          teamId: employee.teamID,
          teamCName: employee.teamCName
        })

        // 有2個以上的組別，在組別下拉選單應加上'全部'的選項
        if (deptTeamsData.value[deptFound].team.length === 2) {
          deptTeamsData.value[deptTeamsData.value.length - 1].team.splice(0, 0, {
            teamId: -1,
            teamCName: '全部'
          })
        }
      }
    }

    employee.teamMembers.forEach((teamMember: any) => {
      if (teamMember.empNo === props.modelValue) {
        selectedDept.value = employee.deptNo
        selectedTeam.value = employee.teamID
      }
      usersData.value.push({
        userId: teamMember.empNo,
        userName: teamMember.cName,
        deptNo: employee.deptNo,
        deptSName: employee.deptSName,
        teamId: employee.teamID,
        teamCName: employee.teamCName
      })
    })
  })
}

onMounted(() => {
  onSetData(props.employeeData)

  const changeDeptEvent = {
    deptNo: selectedDept.value,
    teamId: selectedTeam.value ?? -1,
    userId: props.modelValue
  }
  onChangeDept(changeDeptEvent)
})
</script>