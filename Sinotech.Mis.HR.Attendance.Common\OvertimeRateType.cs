﻿namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 加班倍率類型
    /// </summary>
    public enum OvertimeRateType
    {
        /// <summary>
        /// 1倍，週間國定假日、 補假日、週間天災日
        /// </summary>
        One = 1,

        /// <summary>
        /// 1又1/3倍，週間國定假日、 補假日、週間天災日
        /// </summary>
        OneAndOneThird = 2,

        /// <summary>
        ///  1又2/3倍，週間國定假日、 補假日、週間天災日
        /// </summary>
        OneAndTwoThirds = 3,

        /// <summary>
        /// 補休
        /// </summary>
        Compensatory = 4,

        /// <summary>
        /// 星期六 1~8小時 1倍 ，2018年3月後移除
        /// </summary>
        // SaturdayOne = 5,   //星期六 1~8小時 1倍 ，2018年3月後移除

        /// <summary>
        /// 2又2/3倍，星期六或彈性放假日 9~12小時
        /// </summary>
        TwoAndTwoThirds = 6
    }
}
