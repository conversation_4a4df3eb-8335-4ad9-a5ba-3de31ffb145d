﻿using System.Collections.Generic;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 正常工作卡檢視物件
    /// </summary>
    public class A1CardView : CardBase
    {
        /// <summary>
        /// 卡片類別名稱
        /// </summary>
        public override string Name { get; } = "A1Card";

        /// <summary>
        /// 員工編號
        /// </summary>
        public string A1_EMPNO { get; set; } = string.Empty;

        /// <summary>
        /// 民國年月
        /// </summary>
        public string A1_YYMM { get; set; } = string.Empty;

        /// <summary>
        /// 旬卡別 1/2/3 代表 上/中/下
        /// </summary>
        public char A1_NN { get; set; }

        /// <summary>
        /// 正常工作卡檢視細項
        /// </summary>
        public List<A1CardViewDetail> Details { get; set; } = new List<A1CardViewDetail>();

        /// <summary>
        /// YYYMMDD    填卡日期民國年月日
        /// </summary>
        public string A1_WYYMMDD { get; set; } = string.Empty;

#nullable enable
        /// <summary>
        /// YYYMMDD    結案核卡日期民國年月日
        /// </summary>
        public string? A1_AYYMMDD { get; set; }
#nullable disable

        /// <summary>
        /// 簽核狀態 <br />
        /// </summary>
        /// <value>
        /// 1:未完成 2:同意 3:不同意 4:抽單
        /// </value>
        public int A1_STATUS { get; set; }

        /// <summary>
        /// 正常卡表單單號
        /// </summary>
        /// <value>
        /// 來自 Form table 的 FormNo
        /// </value>
        public string A1_SHEETNO { get; set; }

        /// <summary>
        /// 寫入資料來源
        /// </summary>
        /// <value>
        /// EasyFlow、SECINC (承辦人)、Form (本系統) 
        /// </value>
        public string A1_SOURCE { get; set; }


        /// <summary>
        /// 設定申請別/旬別名稱
        /// </summary>
        /// <returns></returns>
        public override void SetApplicationType()
        {
            string tenDays = "上旬";
            switch (A1_NN)
            {
                case '1':
                    tenDays = "上旬";
                    break;
                case '2':
                    tenDays = "中旬";
                    break;
                case '3':
                    tenDays = "下旬";
                    break;
            }
            ApplicationType = tenDays;
        }
    }
}
