import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import CardSent from '../CardSent.vue'

describe('CardSent', () => {
  it('check context', () => {
    const wrapper = mount(CardSent, {
      props: {
        modelValue: {
          formStatus: 1,
          empNo: '2268',
          ...CardSent.props.modelValue.default
        }
      },
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              authUser: {
                userId: '2268'
              }
            }
          })
        ]
      }
    })
    expect(wrapper.html()).toContain('抽單')
  })
  it('check slots', () => {
    const wrapper = mount(CardSent, {
      slots: {
        header: '<span>填報查詢 / </span>'
      }
    })
    expect(wrapper.html()).toContain('填報查詢 / ')
  })
})