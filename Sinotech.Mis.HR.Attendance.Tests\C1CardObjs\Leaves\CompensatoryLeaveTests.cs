﻿using FakeItEasy;
using Microsoft.AspNetCore.Http;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves;
using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves.Tests
{
    public class CompensatoryLeaveTests : TestC1CardBase
    {
        public CompensatoryLeaveTests()
        {
            #region Prepare Data

            _c1Card.LeaveNumber = LeaveKindEnum.CompensatoryLeave;

            #endregion
        }

        [Theory]
        [InlineData(false, CompensatoryLeave.CodeLeaveRecordNotCreateYet)]
        [InlineData(true, CompensatoryLeave.CodeOk)]
        public void TestCanTakeThisLeave(bool ifLeaveRecordExists, int returnCode)
        {
            A.<PERSON>To(() => _c1CardBo.IfLeaveRecordExists(A<string>.Ignored,
                A<DateTime>.Ignored, A<DateTime>.Ignored)).Returns(ifLeaveRecordExists);

            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CanTakeThisLeave();

            Assert.Equal(returnCode, result.Code);
        }

        [Theory]
        [InlineData(10, 110, CompensatoryLeave.CodeOk)]
        [InlineData(110, 110, CompensatoryLeave.CodeOk)]
        [InlineData(115, 110, CompensatoryLeave.CodeExceedQuota)]
        public void TestExceedQuota(int applyHours, int annualLeaveRemainingHour, int returnCode)
        {
            A.CallTo(() => _c1CardBo.CalculateEmpTakeLeaveWorkingHours(A<String>.Ignored, A<DateTime>.Ignored,
                A<DateTime>.Ignored)).Returns(applyHours);
            A.CallTo(() => _c1CardBo.GetCompensatoryLeaveRemainingHours(A<String>.Ignored, A<DateTime>.Ignored)).Returns(annualLeaveRemainingHour);

            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CheckOverPermittedLeaveHours();

            Assert.Equal(returnCode, result.Code);
        }

        [Fact]
        public void TestCheckRequiredFields()
        {
            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CheckRequiredFields();

            Assert.Equal(CompensatoryLeave.CodeOk, result.Code);
        }

        [Theory]
        [InlineData(Gender.Female, true)]
        [InlineData(Gender.Male, true)]
        public void TestGender(Gender gender, bool expected)
        {
            var result = CompensatoryLeave.IsAllowForThisGender(gender);
            Assert.Equal(expected, result);
        }
    }
}
