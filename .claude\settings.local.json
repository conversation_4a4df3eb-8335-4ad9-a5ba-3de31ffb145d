{"permissions": {"allow": ["mcp__serena__check_onboarding_performed", "mcp__serena__activate_project", "mcp__serena__onboarding", "mcp__serena__list_dir", "mcp__serena__find_file", "mcp__serena__get_symbols_overview", "mcp__serena__write_memory", "mcp__serena__think_about_collected_information", "mcp__serena__think_about_whether_you_are_done", "mcp__sequential-thinking__sequentialthinking", "mcp__serena__find_symbol", "<PERSON><PERSON>(mkdir:*)", "Bash(dotnet test:*)", "mcp__serena__insert_before_symbol", "mcp__serena__replace_symbol_body", "mcp__serena__search_for_pattern", "Bash(dotnet build)", "Bash(git add:*)", "Bash(dotnet build:*)", "mcp__time__get_current_time", "mcp__serena__insert_after_symbol", "Bash(del \"D:\\Git\\Attendance\\Sinotech.Mis.HR.Attendance.BusinessLogic\\Services\\ServiceCollectionExtensions.cs\")", "Bash(rm:*)"], "deny": [], "ask": [], "defaultMode": "acceptEdits"}}