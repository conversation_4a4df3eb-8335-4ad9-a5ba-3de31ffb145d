﻿using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace Sinotech.Mis.Extensions.Configuration
{
    /// <summary>
    /// 使用RSA實現加解密
    /// </summary>
    public static class RsaCrypto
    {
        private const int KeyLength = 2048;
        /// <summary>
        /// 使用RSA實現解密
        /// </summary>
        /// <param name="rsaPrivate"></param>
        /// <param name="encryptText"></param>
        /// <returns></returns>
        public static string RsaDecrypt(RSACryptoServiceProvider rsaPrivate, string encryptText)
        {
            string result = encryptText;
            bool useOAEP = true; // 使用 OAEP填充模式
            try
            {
                result = RsaDecrypt(rsaPrivate, encryptText, useOAEP);
            }
            catch (CryptographicException ex)
            {
                Console.WriteLine($"OAEP模式解密失敗: {ex.Message}");
                try
                {
                    useOAEP = false; // 使用 PKCS#1 v1.5填充模式 再試一次
                    result = RsaDecrypt(rsaPrivate, encryptText, useOAEP);
                }
                catch (CryptographicException ex2)
                {
                    Console.WriteLine($"PKCS#1 v1.5模式解密失敗: {ex2.Message}");
                }
            }
            catch(FormatException ex)
            {
                Console.WriteLine($"{ex.Message} {ex.StackTrace}");
            }
            return result;
        }

        /// <summary>
        /// 使用RSA實現解密
        /// </summary>
        /// <param name="rsaPrivate"></param>
        /// <param name="encryptText"></param>
        /// <param name="useOAEP">true使用OAEP，false 使用 PKCS#1 v1.5</param>
        /// <returns></returns>
        private static string RsaDecrypt(RSACryptoServiceProvider rsaPrivate, string encryptText, bool useOAEP)
        {
            string result = encryptText;
            //Create a UnicodeEncoder to convert between byte array and string.
            if (!string.IsNullOrWhiteSpace(encryptText))
            {
                byte[] dataByteArray = Convert.FromBase64String(encryptText); //使用Base64將string轉換為byte  
                                                                              //對資料進行解密
                byte[] privateValue = rsaPrivate.Decrypt(dataByteArray, useOAEP);
                if (privateValue.Length != 0)
                {
                    UnicodeEncoding ByteConverter = new UnicodeEncoding();
                    result = ByteConverter.GetString(privateValue);
                }
            }
            return result;
        }

        /// <summary>
        /// 使用RSA實現解密
        /// </summary>
        /// <param name="privateKeyPath">私鑰路徑</param>
        /// <param name="data">解密資料</param>
        /// <returns></returns>
        /// <returns></returns>
        public static string RsaDecrypt(string privateKeyPath, string encryptText)
        {
            string result = encryptText;
            // C#預設只能使用[私鑰]進行解密(想使用[私鑰加密]可使用第三方元件BouncyCastle來實現)
            string privateKey = File.ReadAllText(privateKeyPath);
            //建立RSA物件並載入[私鑰]
            RSACryptoServiceProvider rsaPrivate = new RSACryptoServiceProvider(KeyLength);
            try
            {
                rsaPrivate.FromXmlString(privateKey);
                result = RsaDecrypt(rsaPrivate, encryptText);
            }
            catch (CryptographicException ex)
            {
                Console.WriteLine($"{ex.Message} {ex.StackTrace}");
            }

            return result;
        }

        /// <summary>
        /// 使用RSA實現加密
        /// </summary>
        /// <param name="publicKeyPath"></param>
        /// <param name="inputText"></param>
        /// <returns></returns>
        public static string RsaEncrypt(string publicKeyPath, string inputText)
        {
            string result = inputText;
            //C#預設只能使用[公鑰]進行加密(想使用[公鑰解密]可使用第三方元件BouncyCastle來實現)
            try
            {
                //建立RSA物件並載入[公鑰]
                string publicKey = File.ReadAllText(publicKeyPath);
                RSACryptoServiceProvider rsaPublic = new RSACryptoServiceProvider(KeyLength);

                rsaPublic.FromXmlString(publicKey);
                //Create a UnicodeEncoder to convert between byte array and string.
                UnicodeEncoding ByteConverter = new UnicodeEncoding();
                byte[] dataToEncrypt = ByteConverter.GetBytes(inputText);

                //對資料進行加密
                byte[] publicValue = rsaPublic.Encrypt(dataToEncrypt, true);
                result = Convert.ToBase64String(publicValue);//使用Base64將byte轉換為string
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
            return result;
        }
    }
}
