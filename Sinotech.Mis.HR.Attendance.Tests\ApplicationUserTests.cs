﻿using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.Common.Tests
{
    [ExcludeFromCodeCoverage]
    public class ApplicationUserTests
    {
        [Fact]
        public void ApplicationUser_DefaultValues_ShouldBeSetCorrectly()
        {
            // Arrange & Act
            var user = new ApplicationUser();

            // Assert
            Assert.Equal(string.Empty, user.EmpNo);
            Assert.Equal(string.Empty, user.CName);
            Assert.Null(user.Email);
        }

        [Fact]
        public void ApplicationUser_SetValues_ShouldBeSetCorrectly()
        {
            // Arrange
            var user = new ApplicationUser
            {
                EmpNo = "12345",
                CName = "<PERSON> Doe",
                Email = "<EMAIL>"
            };

            // Act & Assert
            Assert.Equal("12345", user.EmpNo);
            Assert.Equal("<PERSON> Doe", user.CName);
            Assert.Equal("<EMAIL>", user.Email);
        }
    }
}
