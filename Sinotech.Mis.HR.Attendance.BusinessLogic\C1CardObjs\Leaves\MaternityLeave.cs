﻿﻿using Sinotech.Mis.Common;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves
{
    /// <summary>
    /// 產假類別
    /// </summary>
    [LeaveKind(LeaveKindEnum.MaternityLeave)]
    public class MaternityLeave : LeaveWithEvent
    {

        /// <summary>
        /// 假別細項不正確
        /// </summary>
        private readonly CardCheckResult ResultNoLeaveSubNumber = new CardCheckResult(3007302, CardStatusEnum.Error, AttendanceParameters.No_LeaveKind_Detail_Error);

        /// <summary>
        /// 限女性申請產假
        /// </summary>
        private readonly CardCheckResult ResultOnlyWomenCanApply = new CardCheckResult(3007301, CardStatusEnum.Error, AttendanceParameters.OnlyWomenCanApplyForMaternityLeave);

        /// <summary>
        /// 建構子
        /// </summary>
        /// <param name="c1Card"></param>
        /// <param name="c1CardBo"></param>
        public MaternityLeave(C1Card c1Card, IC1CardBo c1CardBo) : base(c1Card, c1CardBo)
        {
        }

        /// <summary>
        /// 檢查是否能請此假
        /// </summary>
        /// <returns></returns>
        internal override CardCheckResult CanTakeThisLeave()
        {
            CardCheckResult result = ResultOk;
            Employee employee = _c1CardBo.GetEmployeeDetail(_c1Card.EmpNo);
            if (employee.Sex != "女")
            {
                result = ResultOnlyWomenCanApply;
            }
            return result;
        }

        /// <summary>
        /// Override 檢查請假啟始截止日，其他假別限上班日，因產假為日曆天，故無限制
        /// </summary>
        /// <returns></returns>
        internal override CardCheckResult CheckLeaveStartEndDates()
        {
            CardCheckResult result = ResultOk;

            #region 【請假截止日期時間】須大於【請假起始日期時間】
            if (_c1Card.StartDate > _c1Card.EndDate)
            {
                result = new CardCheckResult(3000313, CardStatusEnum.Error, AttendanceParameters.Leave_End_Date_Must_Greater_Than_Start_Date);
                return result;
            }
            #endregion
            return result;
        }

        /// <summary>
        /// 請假剩餘時數檢查，在產假即檢查是否超假
        /// </summary>
        /// <returns></returns>
        [ExcludeFromCodeCoverage]
        internal override CardCheckResult CheckRemainHours()
        {
            CardCheckResult result = ResultOk;
            if (_c1Card.EventDate != null && _c1Card.EventDate != DateTime.MinValue && _c1Card.EventDate != DateTime.MaxValue)
            {
                // 若上層未傳入 PermittedHours 值
                if (PermittedDays == 0)
                {
                    switch (_c1Card.LeaveSubNumber)
                    {
                        case 1:
                            PermittedDays = 56;
                            break;
                        case 2:
                            PermittedDays = 28;
                            break;
                        case 3:
                            PermittedDays = 7;
                            break;
                        case 4:
                            PermittedDays = 5;
                            break;
                        default:
                            result = ResultNoLeaveSubNumber;
                            break;
                    }
                    PermittedHours = PermittedDays* AttendanceParameters.DefaultWorkingHours;
                }

                CardCheckResult resultOneTimeOver = GenerateOverLeaveDaysError(PermittedDays,
                    3007303, AttendanceParameters.MaternityLeaveOneTimeOverPermittedDays);
                //若本次請假超過可請，且無相關單號
                if (string.IsNullOrWhiteSpace(_c1Card.RelatedFormNumber) && Days > PermittedDays)
                {
                    result = resultOneTimeOver;
                }
                int usedHours = 0;
                // 讀取DB，找出同一事件發生日，計算剩餘可休
                //List<C1Card> c1Cards = _c1CardBo.GetC1CardByEventDate(_c1Card.EmpNo, _c1Card.LeaveNumber, _c1Card.LeaveSubNumber, (DateTime)_c1Card.EventDate);

                //if (c1Cards.Count > 0)
                //{
                //    foreach (var c1Card in c1Cards)
                //    {
                //        usedHours += c1Card.Hours;
                //    }
                //    double remainAvailableDay = PermittedDays - usedHours * 1.0 / AttendanceParameters.DefaultWorkingHours;
                //    //若本次請假超過剩餘可休
                //    if (Days > remainAvailableDay)
                //    {
                //        result = GenerateOverLeaveDaysError(remainAvailableDay,
                //            3007304, AttendanceParameters.MaternityLeaveOverPermittedDays);
                //        return result;
                //    }
                //}

                // 若有相關單號
                if (!string.IsNullOrWhiteSpace(_c1Card.RelatedFormNumber))
                {
                    List<EventRelationRecord> records = _c1CardBo.GetEventRelatedRecord(_c1Card.RelatedFormNumber);
                    // 錯誤判斷
                    foreach (var record in records)
                    {
                        usedHours += record.LeaveHour;
                    }
                    double remainAvailableDay = PermittedDays - usedHours * 1.0 / AttendanceParameters.DefaultWorkingHours;
                    //若本次請假超過剩餘可休
                    if (Days > remainAvailableDay)
                    {
                        result = GenerateOverLeaveDaysError(remainAvailableDay,
                            3007304, AttendanceParameters.MaternityLeaveOverPermittedDays);
                        return result;
                    }
                }
            }
            else
            {
                result = ResultEventDateFieldRequired;
            }
            return result;
        }

        /// <summary>
        /// 檢查必要欄位
        /// </summary>
        /// <returns></returns>
        internal override CardCheckResult CheckRequiredFields()
        {
            CardCheckResult result = base.CheckRequiredFields();
            if (result == ResultOk)
            {
                // 檢查 假別細項
                if (_c1Card.LeaveSubNumber == 0)
                {
                    result = ResultNoLeaveSubNumber;
                }
            }
            return result;
        }

        /// <summary>
        /// 計算請假時數
        /// </summary>
        /// <returns></returns>
        public override void CalculateHours()
        {
            if (_c1Card.EndDate > _c1Card.StartDate)
            {
                TimeSpan span = _c1Card.EndDate.Date - _c1Card.StartDate.Date;
                Days = span.Days + 1;
                TotalHours = Days * AttendanceParameters.GeneralWorkingHours;
            }
        }

        /// <summary>
        /// 計算最早可請假日期與最晚可請假日期
        /// </summary>
        /// <param name="eventDate">事件發生日</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public override (DateTime, DateTime) CalculateLeavePermittedPeriod(DateTime eventDate, string empNo)
        {
            if (_c1Card.EventDate != null && _c1Card.EventDate != DateTime.MinValue && _c1Card.EventDate != DateTime.MaxValue) // 事件發生日
            {
                eventDate = (DateTime)_c1Card.EventDate;
            }
            DateTime startDate = eventDate;
            int days = 8 * 7;
            switch (_c1Card.LeaveSubNumber)
            {
                case 1: // 分娩假(8星期) ，若下班後生產則請假日從事件日後一天起算，故加1日
                    startDate = eventDate.AddDays(-28); // 生產前 4週可請
                    days = 56; // 8 * 7
                    break;
                case 2: // 流產假(4星期) ，若下班後流產則請假日從事件日後一天起算，故加1日
                    startDate = eventDate;
                    days = 28; //  4 * 7
                    break;
                case 3: // 流產假(1星期) ，若下班後流產則請假日從事件日後一天起算，故加1日
                    startDate = eventDate;
                    days = 7;
                    break;
                case 4: // 流產假(5日) ，若下班後流產則請假日從事件日後一天起算，故加1日
                    startDate = eventDate;
                    days = 5;
                    break;
            }

            DateTime endDate = eventDate.AddDays(days);
            return (startDate, endDate);
        }

        /// <summary>
        /// 此性別是否可請此假別
        /// </summary>
        /// <param name="gender"></param>
        /// <returns></returns>
        public static bool IsAllowForThisGender(Gender gender)
        {
            return gender == Gender.Female;
        }

    }
}
