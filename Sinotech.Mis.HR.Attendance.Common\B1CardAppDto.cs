﻿using System;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 加班申請卡DTO，欄位順序不可更改
    /// </summary>
    public class B1CardAppDto
    {
        /// <summary>
        /// 流水號
        /// </summary>
        public int? ID { get; set; }

        /// <summary>
        /// 表單識別碼
        /// </summary>
        public Guid FormUID { get; set; }

        /// <summary>
        /// 加班申請單單號
        /// </summary>
        public string? B1_FormID { get; set; }

        /// <summary>
        /// 員工編號
        /// </summary>
        public string B1_EmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 員工所屬部門編號
        /// </summary>
        public int B1_DeptNo { get; set; }

        /// <summary>
        /// 預定加班日期
        /// </summary>
        public DateTime B1_Date { get; set; }

        /// <summary>
        /// 預定加班日期類型
        /// </summary>
        public int B1_DateTypeId { get; set; }

        /// <summary>
        /// 預定加班時數
        /// </summary>
        public int B1_Hour { get; set; }

        /// <summary>
        /// 預定認列加班時數
        /// </summary>
        public int B1_PaidHour { get; set; }

        /// <summary>
        /// 加班申請別
        /// </summary>
        public char B1_Code { get; set; }
        /// <summary>
        /// 計畫編號
        /// </summary>
        public string B1_PrjNo { get; set; } = string.Empty;
        /// <summary>
        /// 加班理由
        /// </summary>
        public string B1_Reason { get; set; } = string.Empty;

        /// <summary>
        /// 是否為逾期填報
        /// </summary>
        public bool B1_IsOverdue { get; set; }

        /// <summary>
        /// 逾期填報原因
        /// </summary>
        public string B1_OverdueReason { get; set; } = "";

        /// <summary>
        /// 填卡人員工編號
        /// </summary>
        public string B1_WritedEmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 填卡日期時間
        /// </summary>
        public DateTime B1_WDate { get; set; }
#nullable enable
        /// <summary>
        /// 修改人員工編號
        /// </summary>
        public string? B1_UpdatedEmpNo { get; set; }
        /// <summary>
        /// 修改日期時間
        /// </summary>
        public DateTime? B1_UDate { get; set; }
#nullable enable
        /// <summary>
        /// 應簽核主管員工編號(申請卡送出時即先填入, 主管簽核後填入簽核日期及簽核狀態)
        /// </summary>
        public string? B1_ShouldSignEmpNo { get; set; } = null;

        /// <summary>
        /// 核卡日期時間
        /// </summary>
        public DateTime? B1_ADate { get; set; }

        /// <summary>
        /// 簽核狀態 1:未簽核 2:核可 3:不核可 4:抽單
        /// </summary>
        public int B1_Status { get; set; }

        /// <summary>
        /// FlowMaster申請單編號
        /// </summary>
        public string? RequisitionID { get; set; } = null;

        /// <summary>
        /// 寫入資料來源
        /// FlowMaster：FlowMaster系統
        /// Attendance：本系統
        /// SECINC：系統承辦人
        /// </summary>
        public string? B1_SOURCE { get; set; } = null;

        /// <summary>
        /// 最後修改者員工編號
        /// </summary>
        public string? UpdatedEmpNo { get; set; }

        /// <summary>
        /// 最後修改者姓名
        /// </summary>
        public string? UpdatedName { get; set; }

        /// <summary>
        /// 最後修改日期時間
        /// </summary>
        public DateTime? UpdatedTime { get; set; }

        /// <summary>
        /// 最後修改來源IP 位址
        /// </summary>
        public string? UpdatedIP { get; set; }

        /// <summary>
        /// 最後修改來源電腦名稱
        /// </summary>
        public string? UpdatedHost { get; set; }

    }
}
