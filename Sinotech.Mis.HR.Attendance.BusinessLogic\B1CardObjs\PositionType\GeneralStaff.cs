﻿using Sinotech.Mis.HR.Attendance.Common;
using System;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.PositionType
{
    internal class GeneralStaff : B1CardPositionBase
    {
        public GeneralStaff
           (string employeeNumber, DateTime overtimeDate, IB1CardDataProvider provider) :
           base(employeeNumber, overtimeDate, provider)
        {
        }

        internal override bool IsOvertimeAllowed => true;
        protected override bool HasAppliedOvertimeWork() => DefaultHasAppliedOvertimeWork();
        protected override B1CardResult CheckAppliedHours() => DefaultCheckAppliedHours();
        protected override B1CardResult CheckDailyOvertimeHours() => DefaultCheckDailyOvertimeHours();
        protected override B1CardResult CheckMonthlyOvertimeHours() => DefaultCheckMonthlyOvertimeHours();
        protected override B1CardResult CheckQuarterlyOvertimeHours() => DefaultCheckQuaterlyOvertimeHours();
        protected override B1CardResult GetWarningResult() => DefaultGetWarningResult();

    }
}
