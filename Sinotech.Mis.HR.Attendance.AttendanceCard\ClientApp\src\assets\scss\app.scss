$bootstrap-icons-font-dir: 'bootstrap-icons/font/fonts';

@use './font' as font;
@use './bootstrap';
@use './bootstrap_variables' as bootstrap_variables;
@use './vselect';
@use './prime';

@import 'bootstrap-icons/font/bootstrap-icons';
@import '@fontsource/noto-sans-tc';

body {
  margin: 0;
}

#app {
  font-family: font.$font-families;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.shake {
  animation: shake 1.2s cubic-bezier(.36, .07, .19, .97) both;
  transform: translate3d(0, 0, 0);
}

::placeholder {
  color: bootstrap_variables.$gray-600 !important;
  font-style: italic !important;
  font-weight: 100;
}

@keyframes shake {

  10%,
  90% {
    transform: translate3d(-1px, 0, 0);
  }

  20%,
  80% {
    transform: translate3d(2px, 0, 0);
  }

  30%,
  50%,
  70% {
    transform: translate3d(-4px, 0, 0);
  }

  40%,
  60% {
    transform: translate3d(4px, 0, 0);
  }
}