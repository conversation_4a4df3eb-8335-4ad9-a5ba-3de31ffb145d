import { mount } from '@vue/test-utils'
import LeavesInfo from '../LeavesInfo.vue'

describe('LeavesInfo', () => {
  it('check context', () => {
    const wrapper = mount(LeavesInfo, {
      props: {
        modelValue: {
          yearForStatic: 1,
          yearLeaveDateStatic: new Date(),
          leavesData:  {
            annualLeaves: undefined,
            compensatoryLeaves: undefined,
            extendedLeaves: undefined
          }
        }
      }
    })
    expect(wrapper.html()).toContain('年度休假統計')
  })
})