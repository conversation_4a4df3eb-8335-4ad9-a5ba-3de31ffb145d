﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Sinotech.Mis.Common;
using Sinotech.Mis.Extensions.Configuration;
using Sinotech.Mis.Helpers;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.Utilities;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Net.Mail;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic
{
    public class NotificationMail: INotificationMail
    {

        private readonly IConfiguration _configuration;
        private readonly IEmployeeBo _employeeBo;
        private IFormFlowBo _formFlowBo;
        private IFormTypeBo _formTypeBo;
        private readonly ILogger<NotificationMail> _logger;

        /// <summary>
        /// NotificationMail Constructor
        /// </summary>
        /// <param name="employeeBo"></param>
        /// <param name="formTypeBo"></param>
        /// <param name="formFlowBo"></param>
        /// <param name="configuration"></param>
        /// <param name="logger"></param>
        public NotificationMail(IEmployeeBo employeeBo, IFormTypeBo formTypeBo, IFormFlowBo formFlowBo, IConfiguration configuration, ILogger<NotificationMail> logger)
        {
            _employeeBo = employeeBo;
            _formTypeBo = formTypeBo;
            _formFlowBo = formFlowBo;
            _configuration = configuration;
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// 產生簽核關卡文字
        /// </summary>
        /// <param name="form"></param>
        /// <param name="approveDto"></param>
        /// <param name="currentStep"></param>
        /// <param name="formText"></param>
        /// <param name="employee"></param>
        /// <returns></returns>
        /// <exception cref="DirectoryNotFoundException"></exception>
        internal string GenerateApproveLevelText(Form form, Approve approveDto, FormFlow currentStep, string formText, Employee employee)
        {
            string assemblyLocation = Assembly.GetExecutingAssembly().Location;
            string? directoryPath = Path.GetDirectoryName(assemblyLocation);
            if (directoryPath == null)
            {
                throw new DirectoryNotFoundException("Path.GetDirectoryName(assemblyLocation) get null");
            }

            //通知範本
            string? notifyTemplateFileName = _configuration.GetValue<string>("Email:NotifyLevelTemplate");
            if (notifyTemplateFileName == null)
            {
                throw new FileNotFoundException("通知範本 Email:NotifyLevelTemplate 未設定");
            }
            string templatePath = Path.Combine(directoryPath, notifyTemplateFileName);
            if (!File.Exists(templatePath))
            {
                throw new FileNotFoundException(templatePath);
            }
            string notifyTemplate = File.ReadAllText(templatePath);

            //簽核範本
            string? approveTemplateFileName = _configuration.GetValue<string>("Email:ApproveLevelTemplate");
            if (approveTemplateFileName == null)
            {
                throw new FileNotFoundException("簽核範本 Email:ApproveLevelTemplate 未設定");
            }
            templatePath = Path.Combine(directoryPath, approveTemplateFileName);
            if (!File.Exists(templatePath))
            {
                throw new FileNotFoundException(templatePath);
            }
            string approveTemplate = File.ReadAllText(templatePath);

            // 代理人簽核範本
            string? deputyApproveTemplateFileName = _configuration.GetValue<string>("Email:DeputyApproveLevelTemplate");
            if (deputyApproveTemplateFileName == null)
            {
                throw new FileNotFoundException("代理人簽核範本 Email:DeputyApproveLevelTemplate 未設定");
            }
            templatePath = Path.Combine(directoryPath, deputyApproveTemplateFileName);
            if (!File.Exists(templatePath))
            {
                throw new FileNotFoundException(templatePath);
            }
            string deputyApproveTemplate = File.ReadAllText(templatePath);

            //通知關卡總數，若只有一筆則不顯示序號
            int notifyLevels = 0;
            //簽核關卡總數，若只有一筆則不顯示序號
            int approveLevels = 0;
            for (int i = 0; i < currentStep.Step; i++)
            {
                FormFlow flow = form.Flows[i];
                if (flow.IsNotification)
                {
                    notifyLevels++;
                }
                else
                {
                    approveLevels++;
                }
            }
            // 是否顯示通知序號
            bool showNotifySerial = false;
            // 是否顯示簽核序號
            bool showApproveSerial = false;
            if (notifyLevels > 1)
            {
                showNotifySerial = true;
            }
            if (approveLevels > 1)
            {
                showApproveSerial = true;
            }
            int notifySerial = 0;
            int approveSerial = 0;
            StringBuilder builder = new StringBuilder();
            // 產生關卡
            for (int i = 0; i < form.CurrentStep; i++)
            {
                string levelTemplate = approveTemplate;
                FormFlow flow = form.Flows[i];
                bool showLevel = true;
                string levelText = string.Empty;
                if (flow.IsNotification)
                {
                    notifySerial++;
                    levelTemplate = notifyTemplate;

                    if ((FlowStatus)approveDto.FlowStatus == FlowStatus.Agree)
                    {
                        showLevel = false;
                    }
                    else
                    {
                        if (showNotifySerial)
                        {
                            levelText = levelTemplate.Replace("{Serial}", notifySerial.ToString());
                        }
                        else
                        {
                            levelText = levelTemplate.Replace("{Serial}", string.Empty);
                        }
                    }
                }
                else
                {
                    if (flow.IsAgentApprove)
                    {
                        levelTemplate = deputyApproveTemplate;
                    }
                    approveSerial++;
                    if (showApproveSerial)
                    {
                        levelText = levelTemplate.Replace("{Serial}", approveSerial.ToString());
                    }
                    else
                    {
                        levelText = levelTemplate.Replace("{Serial}", string.Empty);
                    }
                }

                if (showLevel)
                {
                    levelText = levelText
                        .Replace("{form}", formText) // 包含URL的FormSubject
                        .Replace("{FlowName}", flow.FlowName) // 流程關卡
                        .Replace("{FormInfo}", form.FormInfo)
                        .Replace("{FormUID}", form.FormUID.ToString())
                        .Replace("{FormNo}", form.FormNo) // 單號
                        .Replace("{FormID}", form.FormID) // FormID
                        .Replace("{FormName}", _formTypeBo.GetFormName(form.FormID)) // 表單名稱
                        .Replace("{EmpNo}", form.EmpNo) // 申請人員工編號
                        .Replace("{EmpName}", form.EmpName) // 申請人姓名
                        .Replace("{RecipientName}", flow.RecipientName) // 流程角色
                        .Replace("{FlowStatus}", _formFlowBo.GetFlowStatusName(flow.FlowStatus));
                    if (string.IsNullOrWhiteSpace(flow.ApproveComments))
                    {
                        string pattern = @"<comment>(.*?)<\/comment>";
                        levelText = Regex.Replace(levelText, pattern, string.Empty,
                            RegexOptions.NonBacktracking, TimeSpan.FromSeconds(0.5));
                    }
                    else
                    {
                        levelText = levelText.Replace("{Comment}", flow.ApproveComments)
                            .Replace("<comment>", "").Replace("</comment>", "");
                    }

                    if (flow.IsNotification)
                    {
                        string approveTime = CardUtility.RocChineseDateTimeString(form.StartTime);
                        levelText = levelText.Replace("{Approver}", flow.RecipientName)
                            .Replace("{ApproverEmpNo}", flow.RecipientEmpNo)
                            .Replace("{ApproveTime}", approveTime);
                    }
                    else
                    {
                        if (flow.ApproveTime != null)
                        {
                            string approveTime = CardUtility.RocChineseDateTimeString((DateTime)flow.ApproveTime);
                            levelText = levelText.Replace("{Approver}", flow.ApproverName)
                                .Replace("{ApproverEmpNo}", flow.ApproverEmpNo)
                                .Replace("{ApproveTime}", approveTime);
                        }

                        if (flow.RecipientName != flow.FlowName && flow.RecipientName != flow.ApproverName)
                        {
                            levelText = levelText.Replace("{RecipientName}", flow.RecipientName);
                        }
                    }
                    levelText = levelText.Replace("{RecipientName}", string.Empty);
                    builder.Append(levelText);
                }
            }
            string levels = builder.ToString();
            return levels;
        }

        /// <summary>
        /// Replace Template variables
        /// </summary>
        /// <param name="form"></param>
        /// <param name="approveDto"></param>
        /// <param name="template"></param>
        /// <param name="formText"></param>
        /// <param name="employee"></param>
        /// <returns></returns>
        internal string ReplateTemplateVariables(Form form, Approve approveDto, string template, string formText, Employee employee)
        {
            return template.Replace("{RcptName}", employee.CName)
                .Replace("{Comment}", approveDto.Comment)
                .Replace("{Approver}", approveDto.ApproverName)
                .Replace("{ApproverEmpNo}", approveDto.ApproverEmpNo)
                .Replace("{form}", formText) // 包含URL的FormSubject
                .Replace("{FormInfo}", form.FormInfo)
                .Replace("{CreatedTime}", CardUtility.RocChineseDateTimeString(form.CreatedTime))
                .Replace("{FormUID}", form.FormUID.ToString())
                .Replace("{FormNo}", form.FormNo) // 單號
                .Replace("{FormID}", form.FormID) // FormID
                .Replace("{FormName}", _formTypeBo.GetFormName(form.FormID)) // 表單名稱
                .Replace("{EmpNo}", form.EmpNo) // 申請人員工編號
                .Replace("{EmpName}", form.EmpName) // 申請人姓名
                .Replace("{FlowStatus}", _formFlowBo.GetFlowStatusName(approveDto.FlowStatus));
        }

        /// <summary>
        /// 寄信
        /// </summary>
        /// <param name="formUid"></param>
        /// <param name="approverEmpNo"></param>
        /// <param name="comment"></param>
        /// <param name="smtpHost"></param>
        /// <param name="port"></param>
        /// <param name="userName"></param>
        /// <param name="password"></param>
        /// <param name="subject"></param>
        /// <param name="body"></param>
        /// <param name="employee"></param>
        /// <param name="from"></param>
        /// <param name="to"></param>
        private void SendEmail(Guid formUid, string approverEmpNo, string comment, string smtpHost, int port, string userName, string password, string subject, string body, Employee employee, MailAddress from, MailAddress to)
        {
            try
            {
                MailPriority priority = MailPriority.High;
                bool sendRet = Mailer.SendMail(subject, body, from, to, smtpHost, port, priority, userName, password);
                if (sendRet)
                {
                    _logger.LogInformation("Action: {Action} 寄信成功, FormUID: {FormUID}, {EmpNo} Email: {Email}, Approver: {ApproverEmpNo}, 意見: {Comment}",
                        nameof(SendEmail), formUid, employee.EmpNo, employee.Email, approverEmpNo, comment);
                }
                else
                {
                    _logger.LogInformation("Action: {Action} 寄信失敗, FormUID: {FormUID}, {EmpNo} Email: {Email}, Approver: {ApproverEmpNo}, 意見: {Comment}",
                        nameof(SendEmail), formUid, employee.EmpNo, employee.Email, approverEmpNo, comment);
                }
            }
            catch (Exception ex)
            {
                _logger.LogInformation("Action: {Action} 寄信失敗, FormUID: {FormUID}, {EmpNo} Email: {Email}, Approver: {ApproverEmpNo}, 意見: {Comment} " +
                                               "錯誤訊息: {Message} {StackTrace}",
                    nameof(SendEmail), formUid, employee.EmpNo, employee.Email, approverEmpNo, comment, ex.Message, ex.StackTrace);
            }
        }

        /// <summary>
        /// 寄給之前關卡簽核人
        /// </summary>
        /// <param name="form"></param>
        /// <param name="approveDto"></param>
        /// <param name="currentStep"></param>
        /// <param name="smtpHost"></param>
        /// <param name="port"></param>
        /// <param name="userName"></param>
        /// <param name="password"></param>
        /// <param name="subject"></param>
        /// <param name="template"></param>
        /// <param name="formText"></param>
        /// <param name="approver"></param>
        /// <param name="template"></param>
        private void SendEmailToPreviousApprovers(Form form, Approve approveDto, FormFlow currentStep, string smtpHost, int port, string userName, string password, string subject, string template, string formText, Employee approver)
        {
            // 寄過的人
            List<string> sent = new List<string>();
            // 寄給之前關卡簽核人
            for (int i = 0; i < currentStep.Step - 1; i++)
            {
                FormFlow flow = form.Flows[i];

                string empNo = flow.ApproverEmpNo;
                if (flow.IsNotification)
                {
                    empNo = flow.RecipientEmpNo;
                }
                bool send = WhetherSendInFlow(form, approveDto, flow, sent, empNo);
                if (send)
                {
                    sent.Add(empNo);
                    Employee employee = _employeeBo.GetEmployeeDetail(empNo);
                    string body = ReplateTemplateVariables(form, approveDto, template, formText, employee);
                    SendEmail(form, approveDto, smtpHost, port, userName, password, subject, body, approver, employee);
                }
            }
        }

        /// <summary>
        /// 是否寄給該關卡的簽核人/通知人
        /// </summary>
        /// <param name="form"></param>
        /// <param name="approveDto"></param>
        /// <param name="flow"></param>
        /// <param name="sent"></param>
        /// <param name="empNo"></param>
        /// <returns></returns>
        private bool WhetherSendInFlow(Form form, Approve approveDto, FormFlow flow, List<string> sent, string empNo)
        {
            bool send = true;

            // 同意信不寄通知人
            if (flow.IsNotification && (FlowStatus)approveDto.FlowStatus == FlowStatus.Agree)
            {
                send = false;
            }

            if (sent.Contains(empNo)) // 不重覆寄
            {
                send = false;
            }

            if (form.EmpNo == empNo) // 在流程不再寄本人
            {
                send = false;
            }

            return send;
        }

        /// <summary>
        /// 寄信
        /// </summary>
        /// <param name="form"></param>
        /// <param name="card"></param>
        /// <param name="approveDto"></param>
        /// <param name="currentStep"></param>
        /// <param name="mailTemplate"></param>
        /// <exception cref="DirectoryNotFoundException"></exception>
        private void SendEmail(Form form, CardBase card, Approve approveDto, FormFlow currentStep, string mailTemplate)
        {
            string smtpHost = _configuration.GetSecuredConfigurationString("Email:SmtpHost");
            int port = _configuration.GetValue<int>("Email:Port");
            string userName = _configuration.GetSecuredConfigurationString("Email:Username");
            string password = _configuration.GetSecuredConfigurationString("Email:Password");
            string subject = _configuration.GetValue<string>("Email:Subject")
                .Replace("{FormName}", _formTypeBo.GetFormName(form.FormID));

            // 把Subject 設成可動態切換 意見 與 不同意
            string commentOrResult = "意見";
            if ((FlowStatus)approveDto.FlowStatus == FlowStatus.Deny)
            {
                commentOrResult = "不同意";
            }
            subject = subject.Replace("{CommentOrResult}", commentOrResult);

            string assemblyLocation = Assembly.GetExecutingAssembly().Location;
            string? directoryPath = Path.GetDirectoryName(assemblyLocation);
            if (directoryPath == null)
            {
                throw new DirectoryNotFoundException("Path.GetDirectoryName(assemblyLocation) get null");
            }
            string templatePath = Path.Combine(directoryPath, mailTemplate);
            if (!File.Exists(templatePath))
            {
                throw new FileNotFoundException(templatePath);
            }
            string template = File.ReadAllText(templatePath);
            string applicationUrl = _configuration.GetValue<string>("Email:ApplicationURL");
            string formText = $"<a href=\"{applicationUrl}Sent/{form.FormID}/{form.FormUID}\" target=\"_blank\">{form.FormSubject}</a>";
            if (form.FormID == "A1Card")
            {
                card.ApplicationType = string.Empty;
            }
            template = template.Replace("{Form}", formText).Replace("{ApplicationType}", card.ApplicationType);

            Employee approver = _employeeBo.GetEmployeeDetail(approveDto.ApproverEmpNo);
            Employee employee = _employeeBo.GetEmployeeDetail(form.EmpNo);
            string levels = GenerateApproveLevelText(form, approveDto, currentStep, formText, employee);
            template = template.Replace("{approveLevels}", levels);
            string body = ReplateTemplateVariables(form, approveDto, template, formText, employee);
            SendEmail(form, approveDto, smtpHost, port, userName, password, subject, body, approver, employee);
            // 寄給之前的簽核人
            SendEmailToPreviousApprovers(form, approveDto, currentStep, smtpHost, port, userName, password, subject, template, formText, approver);

        }

        /// <summary>
        /// 寄信
        /// </summary>
        /// <param name="form"></param>
        /// <param name="approveDto"></param>
        /// <param name="smtpHost"></param>
        /// <param name="port"></param>
        /// <param name="userName"></param>
        /// <param name="password"></param>
        /// <param name="subject"></param>
        /// <param name="body"></param>
        /// <param name="approver"></param>
        /// <param name="employee"></param>
        public void SendEmail(Form form, Approve approveDto, string smtpHost, int port, string userName, string password, string subject, string body, Employee approver, Employee employee)
        {
            if (!string.IsNullOrWhiteSpace(employee.Email) && !string.IsNullOrWhiteSpace(approver.Email))
            {
                string senderEmail = _configuration.GetValue<string>("Email:SenderEmail");
                string senderName = _configuration.GetValue<string>("Email:SenderName");
                MailAddress from = new MailAddress(senderEmail, senderName);
                MailAddress to = new MailAddress(employee.Email, employee.CName);
                // Hack: 測試時先寄到自己的email
                //to = new MailAddress("<EMAIL>", employee.CName);

                SendEmail(form.FormUID, approveDto.ApproverEmpNo, approveDto.Comment, smtpHost, port, userName, password, subject, body, employee, from, to);
            }
            else
            {
                _logger.LogInformation("Action: {Action} 寄信失敗, FormUID: {FormUID}, {EmpNo} 沒有 Email, Approver: {ApproverEmpNo}, 意見: {Comment}",
                    nameof(SendEmail), form.FormUID, employee.EmpNo, approveDto.ApproverEmpNo, approveDto.Comment);
            }
        }

        /// <summary>
        /// 寄送通知Email
        /// </summary>
        /// <param name="form"></param>
        /// <param name="card"></param>
        /// <param name="approveDto"></param>
        /// <param name="currentStep"></param>
        public void SendNotifyEmail(Form form, CardBase card, Approve approveDto, FormFlow currentStep)
        {
            string mailTemplate = _configuration.GetValue<string>("Email:CommentMailTemplate");
            if ((FlowStatus)approveDto.FlowStatus == FlowStatus.Deny)
            {
                mailTemplate = _configuration.GetValue<string>("Email:DisagreeMailTemplate");
            }
            SendEmail(form, card, approveDto, currentStep, mailTemplate);
        }

    }
}