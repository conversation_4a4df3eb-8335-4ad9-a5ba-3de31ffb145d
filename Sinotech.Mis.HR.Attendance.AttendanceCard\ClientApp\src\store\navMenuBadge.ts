import { defineStore } from 'pinia'
import { GET_MENUBADGE_URL } from '../api/appUrl'

export const useNavMenuBadgeStore = defineStore('navMenuBadge', {
  state: () => ({
    badgeInbox: 0,
    badgeNotify: 0
  }),
  actions: {
    async setBadge(userId: string, signal: (AbortSignal | null)) {
      try {
        const res: Response = await fetch(GET_MENUBADGE_URL + '/' + userId, {
          method: 'GET',
          signal: signal
        })
        if (!res.ok) {
          throw new Error(res.status.toString())
        }
        const resJson = await res.json()

        this.badgeInbox = resJson.inbox
        this.badgeNotify = resJson.notification
      } catch (err: unknown) {
        console.error(err)
      }
    }
  }
})