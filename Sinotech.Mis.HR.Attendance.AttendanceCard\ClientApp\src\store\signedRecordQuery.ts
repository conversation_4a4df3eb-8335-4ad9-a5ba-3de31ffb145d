import { defineStore } from 'pinia'
import { MONTHS_OPTIONS, TEN_DAYS_OPTIONS } from '../api/appConst'
import type { MonthsOptionsType } from '../api/appType'

const now = new Date()
const startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 30)
const endTime = new Date(now.getFullYear(), now.getMonth(), now.getDate())

const defaultParameter = {
  formID: null as (string | null),
  startTime: startTime,
  endTime: endTime,
  overtimeCompensatoryKind: {
    id: 0,
    name: '全部'
  },
  leaveKind: 0,
  deputySigned: ['0', '1', '2'],
  formStatus: ['0', '1', '2', '3', '4'],
  formNo: '',
  years: endTime.getFullYear() - 1911,
  months: MONTHS_OPTIONS.find((e: MonthsOptionsType) => e.optionValue === (endTime.getMonth() + 1)) ?? MONTHS_OPTIONS[0],
  tenDays: TEN_DAYS_OPTIONS[0]
}

export const useSignedRecordQueryStore = defineStore('signedRecordQuery', {
  state: () => ({
    queryPage: 0,
    sortField: 'approveTime',
    sortOrder: -1,
    ...defaultParameter
  }),
  actions: {
    setParameter(newParameter: any) {
      this.formID = newParameter.formID
      this.startTime = newParameter.startTime
      this.endTime = newParameter.endTime
      this.overtimeCompensatoryKind = newParameter.overtimeCompensatoryKind
      this.leaveKind = newParameter.leaveKind
      this.deputySigned = newParameter.deputySigned
      this.formStatus = newParameter.formStatus
      this.formNo = newParameter.formNo
      this.years = newParameter.years
      this.months = newParameter.months
      this.tenDays = newParameter.tenDays
    }
  }
})