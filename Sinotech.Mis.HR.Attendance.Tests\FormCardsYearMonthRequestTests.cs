using Xunit;
using Sinotech.Mis.HR.Attendance.Common;

namespace Sinotech.Mis.HR.Attendance.Tests
{
    /// <summary>
    /// FormCardsYearMonthRequest 的單元測試
    /// </summary>
    public class FormCardsYearMonthRequestTests
    {
        /// <summary>
        /// 測試建構函數後的預設值
        /// </summary>
        [Fact]
        public void Constructor_ShouldSetDefaultValues()
        {
            // 安排
            var request = new FormCardsYearMonthRequest();

            // 驗證
            Assert.Equal(0, request.TenDays);
            Assert.Equal(string.Empty, request.ProjNo);
            Assert.Equal(string.Empty, request.EmpNo);
            Assert.Equal(0, request.DeptNo);
            Assert.Equal(0, request.Status);
        }

        /// <summary>
        /// 測試屬性設定和取得
        /// </summary>
        [Fact]
        public void Properties_ShouldSetAndGetCorrectly()
        {
            // 安排
            var request = new FormCardsYearMonthRequest
            {
                Year = 2025,
                Month = 1,
                TenDays = 1,
                ProjNo = "P001",
                EmpNo = "E001",
                DeptNo = 100,
                Status = 1
            };

            // 驗證
            Assert.Equal(2025, request.Year);
            Assert.Equal(1, request.Month);
            Assert.Equal(1, request.TenDays);
            Assert.Equal("P001", request.ProjNo);
            Assert.Equal("E001", request.EmpNo);
            Assert.Equal(100, request.DeptNo);
            Assert.Equal(1, request.Status);
        }

        /// <summary>
        /// 測試 TenDays 屬性的有效值範圍
        /// </summary>
        [Theory]
        [InlineData(0)]  // 無指定
        [InlineData(1)]  // 上旬
        [InlineData(2)]  // 中旬
        [InlineData(3)]  // 下旬
        public void TenDays_ShouldAcceptValidValues(int tenDays)
        {
            // 安排
            var request = new FormCardsYearMonthRequest { TenDays = tenDays };

            // 驗證
            Assert.Equal(tenDays, request.TenDays);
        }

        /// <summary>
        /// 測試月份的有效值範圍
        /// </summary>
        [Theory]
        [InlineData(1)]
        [InlineData(6)]
        [InlineData(12)]
        public void Month_ShouldAcceptValidValues(int month)
        {
            // 安排
            var request = new FormCardsYearMonthRequest { Month = month };

            // 驗證
            Assert.Equal(month, request.Month);
        }
    }
}
