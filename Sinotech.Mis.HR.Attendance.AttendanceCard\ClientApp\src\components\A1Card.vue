<template>
  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-a1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span class="mx-1">卡</span>
          <span class="mx-1">號</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ modelValue?.formNo }}
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-a1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>申</span>
          <span class="mx-1">請</span>
          <span>人</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ (modelValue?.empNo ?? '') + ((modelValue?.empNo && modelValue?.empName) ? ' ' : '') + (modelValue?.empName ?? '') }}
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-a1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>所屬部門</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ modelValue?.deptSName }}
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-a1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>填報旬別</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ modelValue?.formInfo }}
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-a1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>加會人員</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ modelValue?.addedSigner }}
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-a1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>填</span>
          <span class="mx-1">表</span>
          <span>人</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ (modelValue?.createdEmpNo ?? '') + ((modelValue?.createdEmpNo && modelValue?.createdName) ? ' ' : '') + (modelValue?.createdName ?? '') }}
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-a1card text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>填報時間</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ modelValue?.createdTime ? dateToRocString(new Date(modelValue.createdTime)) : '' }}
    </div>
  </div>

  <div class="row border-top mx-0">
    <DataTable
      :value="projectWorkingHoursData"
      class="px-0"
    >
      <ColumnGroup type="header">
        <Row>
          <Column
            header="計畫工時"
            :rowspan="2"
          />
          <template
            v-for="(item, index) in columnData"
            :key="index"
          >
            <Column
              :field="item.field"
              :class="item.workHours === 0 ? 'bg-light' : ''"
            >
              <template #header>
                <span :class="item.workHours === 0 ? 'text-black-50' : ''">
                  {{ item.dayFormat }}
                </span>
              </template>
            </Column>
          </template>
        </Row>
        <Row>
          <template
            v-for="(item, index) in columnData"
            :key="index"
          >
            <Column
              :field="item.field"
              :class="item.workHours === 0 ? 'bg-light' : ''"
            >
              <template #header>
                <span :class="item.workHours === 0 ? 'text-black-50' : ''">
                  {{ item.date.toString() }}
                </span>
              </template>
            </Column>
          </template>
        </Row>
      </ColumnGroup>

      <Column
        field="projectNo"
        class="text-center"
        :bodyStyle="{ 'min-width': '130px' }"
      >
        <template #body="{ data, field }">
          <a
            class="align-items-center text-primary text-decoration-underline"
            role="button"
            @click="onProjectLinkClick(data['projectName'])"
          >
            {{ data[field] }}
          </a>
        </template>
      </Column>

      <template
        v-for="(item, index) in columnData"
        :key="index"
      >
        <Column
          :field="item.field"
          :class="[
            'text-center',
            (item.workHours === 0 ? 'bg-light' : '')
          ]"
        >
          <template #body="{ data, field }">
            {{ data[field] }}
          </template>
        </Column>
      </template>

      <ColumnGroup type="footer">
        <Row>
          <Column
            class="bg-a1card bg-opacity-50"
            footer="計畫工時總計"
            footerClass="text-center"
          />
          <template
            v-for="(item, index) in columnData"
            :key="index"
          >
            <Column class="bg-a1card bg-opacity-50 text-center">
              <template #footer>
                {{ projectWorkingHoursData.reduce((accumulator: any, currentValue: any) => accumulator + currentValue[item.field], 0) }}
              </template>
            </Column>
          </template>
        </Row>
      </ColumnGroup>
    </DataTable>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import DataTable from 'primevue/datatable'
import Row from 'primevue/row'
import Column from 'primevue/column'
import ColumnGroup from 'primevue/columngroup'
import { useToast } from 'primevue/usetoast'
import { useWorkday } from '../composable/workdays'
import { useAbortController } from '../composable/abortController'
import { INFO_DISPLAY_TIME, SYSTEM_ERROR_MESSAGE } from '../api/appConst'
import { dateToRocString } from '../api/appFunction'
import type { PropType } from 'vue'
import type { WorkdayType, CardStoreType } from '../api/appType'

const projectWorkingHoursData = ref<Array<any>>([])
const columnData = ref<Array<{
  field: string
  date: number
  day: number
  dayFormat: string
  workHours: number
}>>([])

const toast = useToast()
const { onGetWorkdaysDateRange } = useWorkday()
const { abortController, fetchErrorHandler, abortListener } = useAbortController()

const props = defineProps({
  modelValue: {
    type: Object as PropType<CardStoreType | undefined>,
    default: () => {}
  }
})

const onProjectLinkClick = (projectName: string): void => {
  toast.add({
    severity: 'info',
    summary: '計畫名稱： ' + projectName,
    life: INFO_DISPLAY_TIME,
    group: 'app'
  })
}

const onSetData = (dataSet: Array<WorkdayType>): void => {
  for (let cardIndex: number = 0; cardIndex < props.modelValue?.card.Details.length; cardIndex++) {
    const card: any = props.modelValue?.card.Details[cardIndex]
    const rowData: any = {}

    rowData['projectNo'] = card.A1_PROJNO
    rowData['projectName'] = card.ProjectName

    card.DayDetails.forEach((e: any, index: number) => {
      const field: string = 'd' + index
      rowData[field] = e.hour

      if (cardIndex === 0) {
        const found: WorkdayType | undefined = dataSet.find((day: WorkdayType) => new Date(day.workDate).getDate() === e.day)

        let workHours: number = 0
        if (found?.dayOff !== true) {
          workHours = found?.workHours ?? 0
        }
        columnData.value.push({
          field: field,
          date: e.day,
          day: e.weekday,
          dayFormat: e.rocWeekDay,
          workHours: workHours
        })
      }
    })

    projectWorkingHoursData.value.push(rowData)
  }
}

onMounted(async (): Promise<void> => {
  abortListener()
  if (props.modelValue?.card && props.modelValue?.empNo) {
    let workdayData: Array<WorkdayType> = []
    try {
      workdayData = await onGetWorkdaysDateRange(new Date(props.modelValue?.card.Details[0].DayDetails[0].date), new Date(props.modelValue?.card.Details[0].DayDetails[props.modelValue?.card.Details[0].DayDetails.length - 1].date), props.modelValue.empNo, abortController.signal)
    } catch (err: unknown) {
      console.error(err)
      fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
    }

    onSetData(workdayData)
  }
})
</script>
<style lang="scss" scoped>
:deep(.p-datatable-empty-message) {
  display: none;
}
:deep(.p-datatable .p-datatable-column-header-content) {
  justify-content: center;
  color: black;
}
:deep(.p-datatable .p-datatable-header) {
  padding: 0 12px;
  background: #ffffff;
  color: black;
  border-top: none;
}
:deep(.p-datatable .p-datatable-thead > tr > th) {
  background: #ffffff;
}
:deep(.p-datatable .p-datatable-tfoot > tr > td) {
  color: black;
}
</style>