﻿using Sinotech.Mis.HR.Attendance.Common;
using System;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.Tests
{
    public class EmpWorkShiftTests
    {
        [Fact]
        public void EmpWorkShift_Properties_Should_Set_Correctly()
        {
            // Arrange
            var empNo = "12345";
            var shiftId = 1;
            var workDate = new DateTime(2023, 10, 1);

            // Act
            var empWorkShift = new EmpWorkShift
            {
                EmpNo = empNo,
                ShiftId = shiftId,
                WorkDate = workDate
            };

            // Assert
            Assert.Equal(empNo, empWorkShift.EmpNo);
            Assert.Equal(shiftId, empWorkShift.ShiftId);
            Assert.Equal(workDate, empWorkShift.WorkDate);
        }

        [Fact]
        public void EmpWorkShift_Default_Constructor_Should_Set_Default_Values()
        {
            // Act
            var empWorkShift = new EmpWorkShift();

            // Assert
            Assert.Equal(string.Empty, empWorkShift.EmpNo);
            Assert.Equal(0, empWorkShift.ShiftId);
            Assert.Equal(default(DateTime), empWorkShift.WorkDate);
        }
    }
}
