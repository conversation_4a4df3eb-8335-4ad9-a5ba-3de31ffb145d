<script lang="ts">
import { defineComponent } from 'vue'
import DatePicker from 'primevue/datepicker'

export default defineComponent({
  extends: DatePicker as unknown as InstanceType<typeof DatePicker>,
  computed: {
    yearPickerValues() {
      const base: number = this.currentYear - 1911 - (this.currentYear % 10)
      return Array.from({length: 10}, (_, index) => {
        return {
          value: (base + index).toString() + '年',
          selectable: true
        }
      })
    }
  },
  methods: {
    getYear(year: { year: number }) {
      return '民國' + (year.year - 1911).toString() + '年'
    },
    onYearSelect(event: Event, year: { value: string}) {
      (DatePicker as any).methods.onYearSelect.call(this, event, {
        value: parseInt(year.value.slice(0, -1), 10) + 1911
      })
    }
  }
})
</script>