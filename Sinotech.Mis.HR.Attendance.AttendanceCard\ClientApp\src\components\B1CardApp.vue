<template>
  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1cardapp text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span class="mx-1">卡</span>
          <span class="mx-1">號</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ modelValue?.formNo }}
    </div>
  </div>
  
  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1cardapp text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>申</span>
          <span class="mx-1">請</span>
          <span>人</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ (modelValue?.empNo ?? '') + ((modelValue?.empNo && modelValue?.empName) ? ' ' : '') + (modelValue?.empName ?? '') }}
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1cardapp text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>所屬部門</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ modelValue?.deptSName }}
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1cardapp text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>預定日期</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ modelValue?.formInfo }}
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1cardapp text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>填報計畫</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      <a
        class="align-items-center text-primary text-decoration-underline"
        role="button"
        @click="onProjectLinkClick(modelValue?.card?.B1_PrjNo)"
      >
        {{ modelValue?.card?.B1_PrjNo }}
      </a>
    </div>
  </div>
  
  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1cardapp text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>申</span>
          <span class="mx-1">請</span>
          <span>別</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ b1Code }}
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1cardapp text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>預定時數</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ modelValue?.card?.B1_Hour }}
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1cardapp text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>加班事由</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      <template
        v-for="(rowReason, rowIndex) in reason"
        :key="rowIndex"
      >
        <span>{{ rowReason }}</span>
        <br v-if="rowIndex < (reason.length - 1)">
      </template>
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1cardapp text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>加會人員</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ modelValue?.addedSigner }}
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1cardapp text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span class="mx-1">附</span>
          <span class="mx-1">件</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      <template
        v-for="(file, index) in modelValue?.attachments"
        :key="index"
      >
        <div class="mb-1">
          <button
            type="button"
            class="btn btn-link p-0"
            @click="onClickDownloadUrl(GET_DOWNLOADATTACHMENT_URL + '/' + file.formUID + '/' + file.id)"
          >
            {{ file.originalFileName }}
          </button>
        </div>
      </template>
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1cardapp text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>填</span>
          <span class="mx-1">表</span>
          <span>人</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ (modelValue?.createdEmpNo ?? '') + ((modelValue?.createdEmpNo && modelValue?.createdName) ? ' ' : '') + (modelValue?.createdName ?? '') }}
    </div>
  </div>

  <div class="row mx-0">
    <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1cardapp text-center">
      <div class="d-table h-100 w-100">
        <div class="d-table-cell align-middle">
          <span>填報時間</span>
        </div>
      </div>
    </div>
    <div class="col-9 col-md-10 py-2">
      {{ modelValue?.createdTime ? dateToRocString(new Date(modelValue?.createdTime)) : '' }}
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useOvertimeCompensatory } from '../composable/overtimeCompensatory'
import { useAbortController } from '../composable/abortController'
import { dateToRocString, onClickDownloadUrl } from '../api/appFunction'
import { SYSTEM_ERROR_MESSAGE, INFO_DISPLAY_TIME } from '../api/appConst'
import { GET_DOWNLOADATTACHMENT_URL, GET_PROJECTNAME_URL } from '../api/appUrl'
import { useToast } from 'primevue/usetoast'
import type { PropType } from 'vue'
import type { CardStoreType, OvertimeCompensatoryType } from '../api/appType'

const b1Code = ref<string>('')

const toast = useToast()
const { overtimeCompensatory, onGetOvertimeCompensatory } = useOvertimeCompensatory()
const { abortController, fetchErrorHandler, abortListener } = useAbortController()

const props = defineProps({
  modelValue: {
    type: Object as PropType<CardStoreType | undefined>,
    default: () => {}
  }
})

const reason = computed<Array<string>>((): Array<string> => {
  let result: Array<string> = []
  if (props.modelValue?.card?.B1_Reason) {
    result = props.modelValue?.card?.B1_Reason.split('\n')
  }
  return result
})

const onProjectLinkClick = async (projNo: string): Promise<void> => {
  const params = new URLSearchParams({
    projNo: projNo
  })
  await fetch(GET_PROJECTNAME_URL + '?' + params, {
    method: 'GET',
    signal: abortController.signal
  }).then((res: Response) => {
    if (!res.ok) {
      throw new Error(res.status.toString())
    }
    return res.text()
  }).then(res => {
    toast.add({
      severity: 'info',
      summary: '計畫名稱： ' + res,
      life: INFO_DISPLAY_TIME,
      group: 'app'
    })
  }).catch((err: Error): void => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })
}

onMounted(async (): Promise<void> => {
  abortListener()
  if (props.modelValue?.card) {
    try {
      await onGetOvertimeCompensatory(abortController.signal)
    } catch (err: unknown) {
      console.error(err)
      fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
    }

    b1Code.value = overtimeCompensatory.value.find((found: OvertimeCompensatoryType) => found.id.toString() === props.modelValue?.card?.B1_Code?.toString())?.name ?? ''
  }
})
</script>