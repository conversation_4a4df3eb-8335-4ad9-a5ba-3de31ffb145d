﻿using System;
using System.Collections.Generic;

namespace Sinotech.Mis.HR.Attendance.Common
{
    // 請假 DTO 類別
    public class LeaveView
    {

        /// <summary>
        /// Only for 比對，並非真正 EasyClone
        /// </summary>
        /// <returns></returns>
        public LeaveView EasyClone()
        {
            LeaveView card = new LeaveView();
            card.AddSigners = AddSigners;
            card.CreatedTime = CreatedTime;
            card.FilledTime = FilledTime;
            card.FilledTime = FilledTime;
            card.LeaveKind = LeaveKind;
            card.EmpNo = EmpNo;
            card.Reason = Reason;
            card.EventDate = EventDate;
            return card;
        }

        /// <summary>
        /// 簡單比對，不嚴謹
        /// </summary>
        /// <param name="other"></param>
        /// <returns></returns>
        public bool EasyEquals(LeaveView? other)
        {
            return other is not null &&
                   AddSigners == other.AddSigners &&
                   CreatedTime == other.CreatedTime &&
                   FilledTime == other.FilledTime &&
                   LeaveKind == other.LeaveKind &&
                   EmpNo == other.EmpNo &&
                   EventDate == other.EventDate &&
                   Reason == other.Reason;
        }

        /// <summary>
        /// LeaveDTO轉換為C1Card
        /// </summary>
        /// <param name="leave">假別DTO物件</param>
        /// <returns></returns>
        public static C1Card LeaveDtoToC1Card(LeaveView leave)
        {
            C1Card c1Card = new C1Card();
            c1Card.EmpNo = leave.EmpNo;
            c1Card.StartDate = leave.StartTime;
            c1Card.EndDate = leave.EndTime;
            c1Card.UpdatedEmpNo = leave.UpdatedEmpNo;
            c1Card.UpdatedIP = leave.UpdatedIP;
            c1Card.UpdatedHost = leave.UpdatedHost;
            c1Card.AddSigners = leave.AddSigners;
            c1Card.CreatedTime = leave.CreatedTime;
            c1Card.UpdatedTime = leave.UpdatedTime;
            c1Card.Reason = leave.Reason;
            c1Card.EventDate = leave.EventDate;
            c1Card.Deputy = leave.Deputy;
            c1Card.EndDate = leave.EndTime;
            c1Card.FilledTime = leave.FilledTime;
            c1Card.WDate = leave.CreatedTime;
            c1Card.LeaveNumber = (LeaveKindEnum)leave.LeaveKind;
            if (leave.LeaveDetailNumber != null)
            {
                c1Card.LeaveSubNumber = (int)leave.LeaveDetailNumber;
            }
            if (leave.ProjectNumber != null)
            {
                c1Card.ProjectNumber = leave.ProjectNumber;
            }
            c1Card.Location = leave.Location;
            c1Card.RelatedFormNumber = leave.RelatedFormNumber;
            c1Card.Confirmed = leave.Confirmed;
            c1Card.UploadedFiles = leave.UploadedFiles;
            return c1Card;
        }

        /// <summary>
        /// 加會人員
        /// </summary>
        /// <value></value>
        public string? AddSigners { get; set; } = string.Empty;

        /// <summary>
        /// 已確認
        /// </summary>
        public bool Confirmed { get; set; } = false;

        /// <summary>
        /// 前端按送出的時間
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.MinValue;

        /// <summary>
        /// 代理人
        /// </summary>
        /// <value>
        /// 代理人員工編號
        /// </value>
        public string Deputy { get; set; } = string.Empty;
        /// <summary>
        /// 員工編號
        /// </summary>
        public string EmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 請假截止日期時間
        /// </summary>
        public DateTime EndTime { get; set; } = DateTime.MinValue;

        /// <summary>
        /// 事件日期
        /// </summary>
        public DateTime? EventDate { get; set; } = null;

        /// <summary>
        /// 前端填寫(打開網頁)的時間
        /// </summary>
        public DateTime FilledTime { get; set; } = DateTime.MinValue;

        /// <summary>
        /// 假別細項編號
        /// </summary>
        /// <value>
        /// 假別細項編號
        /// </value>
        public int? LeaveDetailNumber { get; set; } = 0;

        /// <summary>
        /// 假別編號
        /// </summary>
        /// <value>
        /// 假別編號
        /// </value>
        public int LeaveKind { get; set; } = (int)LeaveKindEnum.AnnualLeave;

        /// <summary>
        /// 出差地點
        /// </summary>
        public string Location { get; set; } = string.Empty;

        ///// <summary>
        ///// 超假狀態
        ///// </summary>
        public bool OverPermittedHours { get; set; } = false;

        /// <summary>
        /// 計畫編號
        /// </summary>
        /// <value>
        /// 計畫編號
        /// </value>
        public string? ProjectNumber { get; set; } = string.Empty;

        /// <summary>
        /// 請假事由
        /// </summary>
        public string Reason { get; set; } = string.Empty;

        /// <summary>
        /// 相關單號
        /// </summary>
        /// <value>
        /// 單號
        /// </value>
        public string RelatedFormNumber { get; set; } = string.Empty;

        /// <summary>
        /// 請假起始日期時間
        /// </summary>
        public DateTime StartTime { get; set; } = DateTime.MinValue;

        /// <summary>
        /// 最後修改者員工編號
        /// </summary>
        public string? UpdatedEmpNo { get; set; }

        /// <summary>
        /// 最後修改來源電腦名稱
        /// </summary>
        public string? UpdatedHost { get; set; }

        /// <summary>
        /// 最後修改來源IP 位址
        /// </summary>
        public string? UpdatedIP { get; set; }

        /// <summary>
        /// 最後修改者姓名
        /// </summary>
        public string? UpdatedName { get; set; }

        /// <summary>
        /// 最後修改日期時間
        /// </summary>
        public DateTime? UpdatedTime { get; set; }

        /// <summary>
        /// 附件
        /// </summary>
        public List<UploadedFile>? UploadedFiles { get; set; } = null;
    }
}
