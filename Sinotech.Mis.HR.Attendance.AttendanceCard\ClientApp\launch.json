{"version": "0.2.0", "configurations": [{"type": "chrome", "request": "launch", "name": "vuejs: chrome", "url": "http://localhost:8080", "webRoot": "${workspaceFolder}/src", "breakOnLoad": true, "sourceMapPathOverrides": {"webpack:///src/*": "${webRoot}/*"}}, {"type": "firefox", "request": "launch", "name": "vuejs: firefox", "url": "http://localhost:8080", "webRoot": "${workspaceFolder}/src", "pathMappings": [{"url": "webpack:///src/", "path": "${webRoot}/"}]}]}