﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.Utilities;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers
{
    /// <summary>
    /// 加班卡API
    /// </summary>
    [Authorize]
    [ApiController]
    [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
    [Route("/api/[controller]/[action]")]
    public class B1CardController : ControllerBase
    {
        private readonly bool _useNegotiate;
        private readonly IB1CardBo _b1CardBo;
        // 避免重覆送出鎖定物件
        private static readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);
        private readonly ILogger<B1CardController> _logger;
        private readonly IConfiguration _Configuration;

        /// <summary>
        ///   <para>
        ///     <see cref="B1CardAppController" /> 的建構函式</para>
        /// </summary>
        /// <param name="b1CardBo">加班卡 商業物件.</param>
        /// <param name="configuration">應用程式設定</param>  
        /// <param name="logger">日誌記錄器</param>
        public B1CardController(IB1CardBo b1CardBo,
            IConfiguration configuration, ILogger<B1CardController> logger)
        {
            _b1CardBo = b1CardBo;
            _useNegotiate = configuration.GetValue<bool>("UseNegotiate");
            _Configuration = configuration;
            _logger = logger;
        }

        /// <summary>
        /// 前一張送出的加班卡
        /// </summary>
        private static B1Card _LastCard { get; set; } = new B1Card();

        private class MyCheckResult
        {
            public string ErrorMessage { get; set; }
            public string EndTimeMustLaterThanStartTime { get; set; }
            public bool CanOvertime { get; set; }
        }

        /// <summary>某日期是否可以填報加班卡</summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>{"ErrorMessage":"錯誤訊息", "DateAlarmMessage": "警告訊息", "IsOvertimeAllowed": true/false}</returns>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/")]
        public string DateCanFillB1Card(DateTime date, string empNo)
        {
            string ret = "{}";
            try
            {
                date = date.ToLocalTime();
                date = new DateTime(date.Year, date.Month, date.Day, 0, 0, 0, DateTimeKind.Local);
                B1CardCheckResult result = _b1CardBo.DateCanFillB1Card(date, empNo);
                MyCheckResult myCheckResult = new MyCheckResult
                {
                    ErrorMessage = result.ErrorMessage,
                    EndTimeMustLaterThanStartTime = result.EndTimeMustLaterThanStartTime,
                    CanOvertime = result.CanOvertime
                };
                ret = JsonConvert.SerializeObject(myCheckResult);
            }
            catch (Exception ex)
            {
                _logger.LogError("/api/B1Card/DateCanFillB1Card({date},{empNo}) 發生錯誤：{Message} {StackTrace}", date, empNo, ex.Message, ex.StackTrace);
            }
            return ret;
        }

        /// <summary>
        /// 送出加班卡
        /// </summary>
        /// <param name="b1Card">加班卡</param>
        /// <returns>B1CardCheckResult JSON</returns>
        [Authorize]
        [HttpPost]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/")]
        public async Task<string> Submit(B1Card b1Card)
        {
            string ret = "[]";
            B1CardCheckResult result = new B1CardCheckResult();
            result.IsValid = false;
            result.ErrorMessage = AttendanceParameters.GeneralErrorMessage;
            if (b1Card == null)
            {
                ret = JsonConvert.SerializeObject(result);
                return ret;
            }
            await _semaphore.WaitAsync();
            try
            {
                if (!b1Card.EasyEquals(_LastCard))
                {
                    string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
                    if (string.IsNullOrWhiteSpace(userId))
                    {
                        result.CanOvertime = false;
                        result.ErrorMessage = AttendanceParameters.BadEmployeeNumber;
                        result.IsValid = false;
                        ret = JsonConvert.SerializeObject(result);
                        return ret;
                    }

                    b1Card.B1_WDate = b1Card.B1_WDate.ToLocalTime();
                    b1Card.OvertimeDate = b1Card.OvertimeDate.ToLocalTime();
                    b1Card.Status = (int)FormStatus.Processing;
                    b1Card.Reason = b1Card.Reason.Trim();
                    b1Card.CreatedTime = b1Card.CreatedTime.ToLocalTime();
                    b1Card.FilledTime = b1Card.FilledTime.ToLocalTime();

                    if (b1Card.CreatedTime > b1Card.B1_WDate)
                    {
                        b1Card.B1_WDate = b1Card.CreatedTime;
                    }

                    foreach (B1CardDetail detail in b1Card.Details)
                    {
                        detail.StartTime = detail.StartTime.ToLocalTime();
                        detail.EndTime = detail.EndTime.ToLocalTime();
                    }

                    // 處理上傳的附件
                    if (b1Card.UploadedFiles != null)
                    {
                        string uploadFolder = _Configuration.GetValue<string>("UploadDirectory");
                        CardUtility.PostProcessUploadedFiles(b1Card.UploadedFiles, uploadFolder);
                    }
                    var (ip, hostname) = CardUtility.GetIP_Hostname(Request.HttpContext);
                    result = await _b1CardBo.AddB1Card(userId, b1Card, ip.ToString(), hostname);
                    if (result.IsValid)
                    {
                        _LastCard = b1Card.EasyClone();
                    }
                }
                else
                {
                    result.IsValid = false;
                    result.ErrorMessage = AttendanceParameters.RepeatSubmitForm;
                }
                ret = JsonConvert.SerializeObject(result);
            }
            catch (Exception ex)
            {
                _logger.LogError("/api/B1Card/Submit({b1Card}) 發生錯誤：{Message} {StackTrace}", JsonConvert.SerializeObject(b1Card), ex.Message, ex.StackTrace);
            }
            finally
            {
                _semaphore.Release();
            }
            return ret;
        }
    }
}
