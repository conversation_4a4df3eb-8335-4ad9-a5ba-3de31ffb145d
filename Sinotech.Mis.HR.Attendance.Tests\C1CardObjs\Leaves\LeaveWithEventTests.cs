﻿using FakeItEasy;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Collections.Generic;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves.Tests
{
    public class LeaveWithEventTests
    {
        [Fact]
        public void LeaveWithEventTest()
        {
            C1Card c1Card = new C1Card();
            c1Card.EmpNo = "2268";
            c1Card.Deputy = "0349";
            c1Card.EventDate = new DateTime(2024, 10, 11, 0, 0, 0, DateTimeKind.Local);
            c1Card.LeaveNumber = Common.LeaveKindEnum.PaternityLeave;
            c1Card.LeaveSubNumber = 1;
            c1Card.RelatedFormNumber = "1234567890";
            IC1CardBo c1CardBo = A.Fake<IC1CardBo>();
            List<C1CardDto> fakeCards = new List<C1CardDto>();
            C1CardDto c1CardDto = new C1CardDto();
            c1CardDto.C1_EMPNO = "2268";

            c1CardDto.C1_EventDate = c1Card.EventDate;
            fakeCards.Add(c1CardDto);
            <PERSON><PERSON>(() => c1CardBo.GetRelatedCards(A<string>.Ignored)).Returns(fakeCards);
            PaternityLeave leave = new PaternityLeave(c1Card, c1CardBo);
            Assert.NotNull(leave);

            fakeCards = new List<C1CardDto>();
            c1CardDto = new C1CardDto();
            c1CardDto.C1_EMPNO = "2268";
            c1CardDto.C1_EventDate = c1Card.EventDate;
            c1CardDto.C1_DeadlineStartDate = new DateTime(2024, 1, 11, 0, 0, 0, DateTimeKind.Local);
            c1CardDto.C1_DeadlineEndDate = new DateTime(2024, 11, 11, 0, 0, 0, DateTimeKind.Local);
            fakeCards.Add(c1CardDto);
            A.CallTo(() => c1CardBo.GetRelatedCards(A<string>.Ignored)).Returns(fakeCards);
            leave = new PaternityLeave(c1Card, c1CardBo);
            Assert.NotNull(leave);
        }
    }
}