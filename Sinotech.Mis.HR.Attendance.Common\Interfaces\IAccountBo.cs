﻿using System;
using System.Net;

namespace Sinotech.Mis.HR.Attendance.Common
{
    public interface IAccountBo
    {

        /// <summary>
        /// 驗證用戶
        /// </summary>
        /// <param name="userId">員工編號</param>
        /// <param name="password">密碼</param>
        /// <returns></returns>
        public ApplicationUser? AuthenticateUser(string userId, string password);
        /// <summary>
        /// 登入失敗次數
        /// </summary>
        /// <param name="accountId"></param>
        /// <returns>15分鐘內 登入失敗次數</returns>
        public int GetLoginFailCount(string accountId);

        /// <summary>
        /// 增加登入記錄
        /// </summary>
        /// <param name="userAgent"></param>
        /// <param name="loginTime">登入時間</param>
        /// <param name="username"></param>
        /// <param name="action"></param>
        /// <param name="result"></param>
        /// <param name="ipAddress"></param>
        /// <param name="hostname"></param>
        public bool InsertLoginRecord(string userAgent, DateTime loginTime, string username,
            AccountLogInOutAction action, AccountLogInOutResult result, IPAddress ipAddress, string? hostname);

        /// <summary>
        /// 記錄登入失敗
        /// </summary>
        /// <param name="userAgent"></param>
        /// <param name="loginTime">登入時間</param>
        /// <param name="username"></param>
        /// <param name="ipAddress"></param>
        /// <param name="hostname"></param>
        public bool LogLoginFail(string userAgent, DateTime loginTime, string username, IPAddress ipAddress, string? hostname);

        /// <summary>
        /// Log login success information
        /// </summary>
        /// <param name="userAgent"></param>
        /// <param name="loginTime">登入時間</param>
        /// <param name="iPAddress"></param>
        /// <param name="hostname"></param>
        /// <param name="empNo"></param>
        public bool LogLoginSuccess(string userAgent, DateTime loginTime, IPAddress iPAddress, string? hostname, string empNo);

        /// <summary>
        /// Log logout information
        /// </summary>
        /// <param name="userAgent"></param>
        /// <param name="logoutTime">登出時間</param>
        /// <param name="iPAddress"></param>
        /// <param name="hostname"></param>
        /// <param name="empNo"></param>
        public bool LogLogout(string userAgent, DateTime logoutTime, IPAddress iPAddress, string? hostname, string empNo);

        /// <summary>
        /// 記錄解鎖
        /// </summary>
        /// <param name="userAgent"></param>
        /// <param name="unlockTime">解鎖時間</param>
        /// <param name="username"></param>
        /// <param name="unlocker"></param>
        /// <param name="ipAddress"></param>
        /// <param name="result"></param>
        /// <param name="hostname"></param>
        public bool LogUnlock(string userAgent, DateTime unlockTime, string username, string unlocker, IPAddress ipAddress, AccountLogInOutResult result, string? hostname);

    }
}
