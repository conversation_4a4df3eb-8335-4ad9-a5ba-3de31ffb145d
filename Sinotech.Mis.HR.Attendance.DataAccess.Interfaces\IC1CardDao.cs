﻿using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Data;

namespace Sinotech.Mis.HR.Attendance.DataAccess.Interfaces
{
    /// <summary>
    /// C1Card資料存取介面
    /// </summary>
    public interface IC1CardDao
    {

        /// <summary>
        /// 取得年度員工生日福利資料表
        /// </summary>
        /// <param name="year">西元年</param>
        /// <returns></returns>
        public DataTable GetBirthdayWelfare(int year);

        /// <summary>
        /// 取得年度員工生日福利資料表
        /// </summary>
        /// <param name="year">西元年</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public DataTable GetBirthdayWelfare(int year, string empNo);

        /// <summary>
        /// 查詢特定假別特定同仁於特定事件發生日的所有假單，僅限 已同意與簽核中
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="leaveNumber">假別</param>
        /// <param name="leaveSubNumber">假別細項</param>
        /// <param name="eventDate">事件發生日</param>
        /// <returns></returns>
        public DataTable GetC1CardByEventDate(string empNo, int leaveNumber, int leaveSubNumber, DateTime eventDate);

        /// <summary>
        /// 查詢特定假別特定同仁於特定假別的所有假單，僅限 已同意與簽核中
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="leaveNumber">假別</param>
        /// <param name="leaveSubNumber">假別細項</param>
        /// <returns></returns>
        public DataTable GetC1CardByLeaveKind(string empNo, LeaveKindEnum leaveNumber, int leaveSubNumber);

        /// <summary>
        /// 取得 表單關係人 某月份請假單
        /// </summary>
        /// <param name="empNo">表單關係人 員工編號</param>
        /// <param name="date">日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns>表單</returns>
        public DataTable GetC1CardMonth(string empNo, DateTime date, int? status = null);

        /// <summary>
        /// 取得C1Cards
        /// </summary>
        /// <param name="formUID">表單識別碼</param>
        /// <returns></returns>
        public DataTable GetC1Cards(Guid formUID);

        /// <summary>
        /// 依日期區間取得所有請假卡
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public DataTable GetC1Cards(DateTime startDate, DateTime endDate);

        /// <summary>
        /// 取得某段時間內特定員工的請假卡，檢查重覆使用，故不包括頭尾
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public DataTable GetC1CardsBetween(string empNo, DateTime startDate, DateTime endDate);

        /// <summary>
        /// 依日期區間取得所有表單及請假單
        /// </summary>
        /// <param name="startDate">最早填單日期</param>
        /// <param name="endDate">最晚填單日期</param>
        /// <returns></returns>
        public DataTable GetC1CardsForms(DateTime startDate, DateTime endDate);

        /// <summary>
        /// 依日期區間取得所有表單及請假卡
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="projNo"></param>
        /// <returns></returns>
        public DataTable GetC1CardsForms(DateTime startDate, DateTime endDate, string projNo);

        /// <summary>取得某段時間內特定員工表單及請假單，檢查重覆使用，故不包括頭尾</summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <returns></returns>
        public DataTable GetC1CardsFormsBetween(string empNo, DateTime startDate, DateTime endDate);

        /// <summary>取得某單號的 C1CardDtos，不含流程與附件等</summary>
        /// <param name="formNumber">相關單號</param>
        /// <returns>卡 Card 物件 List</returns>
        public DataTable GetCardsByFormNumber(string formNumber);

        /// <summary>
        /// 取得同一事件的請假記錄（以事件的第一筆請假卡號查詢相關請假記錄）
        /// </summary>
        /// <param name="formNo">表單編號</param>
        /// <returns></returns>
        public DataTable GetEventRelatedRecord(string formNo);

        /// <summary>
        /// 取得指定員工事件假有效期限內的相關卡號資料
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="leaveNumber">假別編號</param>
        /// <param name="leaveSubNumber">假別細項編號</param>
        /// <param name="startDate">請假起始日期時間</param>
        /// <param name="endDate">請假截止日期時間</param>
        /// <returns></returns>
        public DataTable GetEventRelatedSheets(string empNo, int leaveNumber, int leaveSubNumber, DateTime startDate, DateTime endDate);

        /// <summary>
        /// 取得所有假別
        /// </summary>
        public DataTable GetLeaveKinds();

        /// <summary>
        /// 取得假別專有訊息
        /// </summary>
        /// <param name="leaveNumber">假別代碼</param>
        /// <returns>該假別專有訊息 Data Table</returns>
        public DataTable GetLeaveMessage(int leaveNumber);

        /// <summary>
        /// 取得所有假別訊息
        /// </summary>
        public DataTable GetLeaveMessages();

        /// <summary>
        /// 取得相關 C1Cards (C1Card為已核可或簽核中)
        /// </summary>
        /// <param name="formNumber">單號</param>
        /// <returns></returns>
        public DataTable GetRelatedCards(string formNumber);

        /// <summary>
        /// 取得 填表人/表單關係人 某月份已填請假單
        /// </summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <param name="status">表單狀態</param>
        /// <returns>已填表單</returns>
        public DataTable GetSentC1CardYearMonth(string empNo, int year, int month, int? status = null);
        /// <summary>
        /// 檢查個人休假資料表是否有該員工指定年月的資料
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="applyDate">指定年月</param>
        /// <returns>若指定個人休假資料表存在則回傳 true</returns>
        public bool IfAnnualLeaveDataExists(string employeeNumber, DateTime applyDate);

        /// <summary>
        /// 取出該員工剩餘年休假時數（扣除「已同意」及「簽核中」)
        /// </summary>
        /// <param name="empNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>該員工剩餘年休假時數</returns>
        public int GetAnnualLeaveRemainingHours(string employeeNumber, DateTime dt);

        /// <summary>
        /// 檢查該員工剩餘年度延休假時數（扣除「已同意」及「簽核中」)
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>該員工剩餘年度延休假時數</returns>
        public int GetPostponedLeaveRemainingHours(string employeeNumber, DateTime dt);

        ///// <summary>
        ///// 取得員工指定假別休假資料
        ///// </summary>
        ///// <param name="employeeNumber">員工編號</param>
        ///// <param name="year">查詢日期年度</param>
        ///// <param name="month">查詢日期月份</param>
        ///// <param name="leaveKind">假別代號</param>
        ///// <param name="subItem">假別細項代號</param>
        ///// <param name="yearAvailableHours">年度總可用時數</param>
        ///// <param name="yearUsedHours">年度已使用時數(簽核中＋已核可)</param>
        ///// <param name="sumToMonthUsedHours">累至查詢月份已使用時數(簽核中＋已核可)</param>
        ///// <param name="monthUsedHours">查詢月份已使用時數(簽核中＋已核可)</param>
        ///// <param name="yearApprovedHours">年度已核可時數</param>
        ///// <param name="sumToMonthApprovedHours">累至查詢月份已核可時數</param>
        ///// <param name="monthApprovedHours">查詢月份已核可時數</param>
        //public int GetEmployeeLeaveInfo(string employeeNumber, int year, int month, LeaveKindEnum leaveKind, int subItem,
        //    out int yearAvailableHours, out int yearUsedHours,
        //    out int sumToMonthUsedHours, out int monthUsedHours,
        //    out int yearApprovedHours, out int sumToMonthApprovedHours,
        //    out int monthApprovedHours);

        /// <summary>
        /// 取得補修日(含時間)前可補休的時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="applyDateTime">指定的補休時間</param>
        /// <returns></returns>
        public int GetCompensatoryLeaveRemainingHours(string employeeNumber, DateTime applyDateTime);

        /// <summary>
        /// 是否指定月份已經申請生理假
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="year">查詢年度，以西元年表示</param>
        /// <param name="month">查詢月份</param>
        /// <returns>是否已申請</returns>
        public bool IsMenstrualLeaveAlreadyTaken(string employeeNumber, int year, int month);

        /// <summary>
        /// 取得年度已休生理假時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <returns>年度已休生理假時數</returns>
        public int GetMenstrualLeaveYearUsedHours(string employeeNumber, DateTime dt);

        /// <summary>
        /// 取得年度病假統計時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <param name="yearAvailableHours">年度總可用時數</param>
        /// <param name="yearUsedHours">年度已使用時數(簽核中＋已核可)</param>
        /// <param name="yearApprovedHours">年度已核可時數</param>
        public void GetSickLeaveInfo(string employeeNumber, DateTime dt,
            out int yearAvailableHours, out int yearUsedHours, out int yearApprovedHours);

        /// <summary>
        /// 取得年度事假統計時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <param name="yearAvailableHours">年度總可用時數</param>
        /// <param name="yearUsedHours">年度已使用時數(簽核中＋已核可)</param>
        /// <param name="yearApprovedHours">年度已核可時數</param>
        public void GetPersonalLeaveYearInfo(string employeeNumber, DateTime dt,
            out int yearAvailableHours, out int yearUsedHours, out int yearApprovedHours);

        /// <summary>
        /// 取得年度家庭照顧假統計時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <param name="yearAvailableHours">年度總可用時數</param>
        /// <param name="yearUsedHours">年度已使用時數(簽核中＋已核可)</param>
        /// <param name="yearApprovedHours">年度已核可時數</param>
        public void GetFamilyCareLeaveYearInfo(string employeeNumber, DateTime dt,
            out int yearAvailableHours, out int yearUsedHours, out int yearApprovedHours);

        /// <summary>
        /// 取得員工生日假資料，包括查詢年度選擇的生日假方案、是否已經使用生日假
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="year">查詢年度，以西元年表示</param>
        /// <param name="birthdayWelfare">查詢年度的生日假方案，0-查無資料，1-生日假，2-生日禮券</param>
        /// <param name="isUsed">查詢年度是否已經使用生日假</param>
        public void GetBirthdayLeaveInfo(string employeeNumber, int year, out int birthdayWelfare, out bool isUsed);

        /// <summary>
        /// 取得年度育嬰假統計時數
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="dt">查詢時間</param>
        /// <param name="yearAvailableHours">年度總可用時數</param>
        /// <param name="yearUsedHours">年度已使用時數(簽核中＋已核可)</param>
        /// <param name="yearApprovedHours">年度已核可時數</param>
        public void GetParentalLeaveYearInfo(string employeeNumber, DateTime dt,
            out int yearAvailableHours, out int yearUsedHours, out int yearApprovedHours);
    }
}
