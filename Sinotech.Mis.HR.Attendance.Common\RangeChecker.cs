﻿using System;

namespace Sinotech.Mis.HR.Attendance.Common
{
    public class RangeChecker
    {
        /// <summary>
        /// 檢查時段是否重複
        /// </summary>
        /// <param name="f0"></param>
        /// <param name="t0"></param>
        /// <param name="f1"></param>
        /// <param name="t1"></param>
        /// <returns></returns>
        public static bool CheckOverlap(DateTime f0, DateTime t0, DateTime f1, DateTime t1)
        {
            return Max(f0, f1) < Min(t0, t1);
            DateTime Max(DateTime dt0, DateTime dt1) => dt0 > dt1 ? dt0 : dt1;
            DateTime Min(DateTime dt0, DateTime dt1) => dt0 < dt1 ? dt0 : dt1;
        }

        /// <summary>
        /// 檢查被第二時段是否包含第一時段
        /// </summary>
        /// <param name="innerFrom"></param>
        /// <param name="innerTo"></param>
        /// <param name="outerFrom"></param>
        /// <param name="outerTo"></param>
        /// <returns></returns>
        public static bool CheckContainment(DateTime innerFrom, DateTime innerTo, DateTime outerFrom, DateTime outerTo)
        {
            return Max(innerFrom, outerFrom) == innerFrom && Min(innerTo, outerTo) == innerTo;
            DateTime Max(DateTime dt0, DateTime dt1) => dt0 > dt1 ? dt0 : dt1;
            DateTime Min(DateTime dt0, DateTime dt1) => dt0 < dt1 ? dt0 : dt1;
        }

    }
}
