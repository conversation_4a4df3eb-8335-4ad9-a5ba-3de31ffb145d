const mode = import.meta.env.MODE
const API_BASE_URL = (mode === 'test') ? 'http://localhost:3000/' : import.meta.env.BASE_URL

export const IISINC_HOME_URL = 'https://iisinc.sinotech.org.tw/home/',
  SINODAMS_DEPUTY_URL = 'https://sinodams.sinotech.org.tw/SinoSign/Form/Deputy',
  GET_ISADMIN_URL = API_BASE_URL + 'api/Attendance/IsAdmin',
  GET_EMPLOYEES_URL = API_BASE_URL + 'api/Employee/GetEmployees',
  GET_ALLOWEDQUERYEMPLOYEES_URL = API_BASE_URL + 'api/Department/GetAllowedQueryEmployees',
  GET_ALLOWEDQUERYDEPARTMENTSENTBOXEMPLOYEES_URL = API_BASE_URL + 'api/Department/GetAllowedQueryDepartmentSentBoxEmployees',
  GET_ISALLOWEDQUERYALLDEPARTMENTSENTBOX_URL = API_BASE_URL + 'api/Department/IsAllowedQueryAllDepartmentSentBox',
  GET_ISAUTHORIZEDTOQUERYDEPARTMENTSENTBOX_URL = API_BASE_URL + 'api/Attendance/IsAuthorizedToQueryDepartmentSentBox',
  GET_ALLEMPLOYEES_URL = API_BASE_URL + 'api/Employee/GetAllEmployees',
  GET_ELIGIBLEOVERTIMEEMPLOYEES_URL = API_BASE_URL + 'api/Employee/GetEligibleOvertimeEmployees',
  GET_DAYINTIME_URL = API_BASE_URL + 'api/Attendance/GetDayInTime',
  GET_DEPUTIES_URL = API_BASE_URL + 'api/Employee/GetDeputies',
  GET_EMPLOYEE_URL = API_BASE_URL + 'api/Employee/GetEmployee',
  GET_DEPARTMENTS_URL = API_BASE_URL + 'api/Department/GetDepartments',
  GET_MONTHSENTCARDS_URL = API_BASE_URL + 'api/Form/GetMonthSentCards',
  GET_INBOX_URL = API_BASE_URL + 'api/Form/GetInbox',
  GET_OPENPROJECTSDATERANGE_URL = API_BASE_URL + 'api/Project/GetOpenProjectsDateRange',
  GET_WORKDAYSDATERANGE_URL = API_BASE_URL + 'api/Workday/GetWorkdaysDateRange',
  GET_EMPLOYEEWORKDAYSDATERANGE_URL = API_BASE_URL + 'api/Workday/GetEmployeeWorkdaysDateRange',
  GET_MONTHEMPLOYEELEAVES_URL = API_BASE_URL + 'api/Attendance/GetMonthEmployeeLeaves',
  GET_EMPLEAVEINFO_URL = API_BASE_URL + 'api/C1Card/GetEmpLeaveInfo',
  GET_MONTHATTENDANCE_URL = API_BASE_URL + 'api/Attendance/GetMonthAttendance',
  GET_SENTFORMCARD_URL = API_BASE_URL + 'api/Form/GetSentFormCard',
  GET_SIGNEDFORMCARD_URL = API_BASE_URL + 'api/Form/GetSignedFormCard',
  GET_APPROVALFORMCARD_URL = API_BASE_URL + 'api/Form/GetApprovalFormCard',
  GET_NOTIFYFORMCARD_URL = API_BASE_URL + 'api/Form/GetNotifyFormCard',
  GET_MENUBADGE_URL = API_BASE_URL + 'api/Form/MenuBadge',
  GET_A1CARDISFILLED_URL = API_BASE_URL + 'api/A1Card/IsFilled',
  GET_B1CARDTYPES_URL = API_BASE_URL + 'api/B1CardApp/GetB1CardTypes',
  GET_LEAVEKINDS_URL = API_BASE_URL + 'api/C1Card/GetLeaveKinds',
  GET_CALCULATELEAVEDAYHOURS_URL = API_BASE_URL + 'api/C1Card/CalculateLeaveDayHours',
  GET_CALCULATELEAVEENDDATE_URL = API_BASE_URL + 'api/C1Card/CalculateLeaveEndDate',
  GET_OVERTIMEDATA_URL = API_BASE_URL + 'api/Attendance/GetOvertimeData',
  GET_DATECANFILLB1CARDAPP_URL = API_BASE_URL + 'api/B1CardApp/DateCanFillB1CardApp',
  GET_DATECANFILLB1CARD_URL = API_BASE_URL + 'api/B1Card/DateCanFillB1Card',
  GET_DOWNLOADATTACHMENT_URL = API_BASE_URL + 'api/Form/DownloadAttachment',
  GET_PROJECTNAME_URL = API_BASE_URL + 'api/Project/GetProjectName',
  GET_ISOVERTIMEALLOWED_URL = API_BASE_URL + 'api/Employee/IsOvertimeAllowed',
  GET_LOGOUT_URL = API_BASE_URL + 'api/Account/Logout',
  GET_ISAUTH_URL = API_BASE_URL + 'api/Account/IsAuthenticated',
  POST_LOGIN_URL = API_BASE_URL + 'api/Account/Login',
  POST_ISADMIN_URL = API_BASE_URL + 'api/Attendance/IsAdmin',
  POST_EVENTRELATEDSHEET_URL = API_BASE_URL + 'api/C1Card/GetEventRelatedSheets',
  POST_TOPAPPROVECOMMENTS_URL = API_BASE_URL + 'api/Form/GetTopApproveComments',
  POST_APPROVEINBOX_URL = API_BASE_URL + 'api/Form/Approve',
  POST_MULTIPLE_APPROVEINBOX_URL = API_BASE_URL + 'api/Form/MultipleApprove',
  POST_SENTBOXPERIOD_URL = API_BASE_URL + 'api/Form/GetSentBoxPeriod',
  POST_SENTBOXPERIOD_CONTENTENTDATE_URL = API_BASE_URL + 'api/Form/GetSentBoxPeriodByContentDate',
  POST_SENTBOXPERIOD_CONTENTENTYEARMONTH_URL = API_BASE_URL + 'api/Form/GetSentBoxPeriodByContentYearMonth',
  POST_DEPARTMENTSENTBOXPERIOD_URL = API_BASE_URL + 'api/Form/GetDepartmentSentBoxPeriod',
  POST_DEPARTMENTSENTBOXPERIOD_CONTENTENTDATE_URL = API_BASE_URL + 'api/Form/GetDepartmentSentBoxByContentDate',
  POST_DEPARTMENTSENTBOXPERIOD_CONTENTENTYEARMONTH_URL = API_BASE_URL + 'api/Form/GetDepartmentSentBoxByYearMonth',
  POST_SIGNEDFORMSPERIOD_URL = API_BASE_URL + 'api/Form/GetSignedFormsPeriod',
  POST_SIGNEDFORMSYEARMONTH_URL = API_BASE_URL + 'api/Form/GetSignedFormsByYearMonth',
  POST_SUBMITA1CARD_URL = API_BASE_URL + 'api/A1Card/Submit',
  POST_SUBMITB1CARDAPP_URL = API_BASE_URL + 'api/B1CardApp/Submit',
  POST_SUBMITB1CARD_URL = API_BASE_URL + 'api/B1Card/Submit',
  POST_SUBMITC1CARD_URL = API_BASE_URL + 'api/C1Card/Submit',
  POST_CHECKC1CARD_URL = API_BASE_URL + 'api/C1Card/CheckData',
  POST_FILEUPLOAD_URL = API_BASE_URL + 'api/File/Upload',
  POST_FORMS_URL = API_BASE_URL + 'api/Form/GetForms',
  POST_FORMS_CONTENTENTDATE_URL = API_BASE_URL + 'api/Form/GetFormsByContentDate',
  POST_FORMS_CONTENTENTYEARMONTH_URL = API_BASE_URL + 'api/Form/GetFormsByContentYearMonth',
  POST_Notifications_URL = API_BASE_URL + 'api/Form/GetNotifyFormCards',
  POST_DeliveredNotifications_URL = API_BASE_URL + 'api/Form/DeliveredNotifications',
  POST_MARKDELIVEREDNOTIFICATIONS_URL = API_BASE_URL + 'api/Form/MarkDeliveredNotifications',
  POST_LEAVEPERMITTEDPERIOD_URL = API_BASE_URL + 'api/C1Card/LeavePermittedPeriod',
  PUT_UPDATEA1CARD_URL = API_BASE_URL + 'api/A1Card/Update',
  DELETE_WITHDRAW_URL = API_BASE_URL + 'api/Form/Withdraw',
  DELETE_CLOSEDWITHDRAW_URL = API_BASE_URL + 'api/Form/ClosedWithdraw'
  