﻿using System;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 帳號登入登出 Enumeraion (登入 1、登出 2、解鎖 3)
    /// </summary>
    public enum AccountLogInOutAction
    {
        /// <summary>
        /// 登入
        /// </summary>
        Login = 1,
        /// <summary>
        /// 登出
        /// </summary>
        Logout = 2,
        /// <summary>
        /// 解鎖
        /// </summary>
        Unlock = 3
    }

    /// <summary>
    /// 執行結果 (成功 1、失敗 2、鎖定 3)
    /// </summary>
    public enum AccountLogInOutResult
    {
        /// <summary>
        /// 成功
        /// </summary>
        Success = 1,
        /// <summary>
        /// 失敗
        /// </summary>
        Fail = 2,
        /// <summary>
        /// 鎖定
        /// </summary>
        Lockout = 3
    }

    public class AccountLogInOutDto
    {
        /// <summary>
        /// 登出入帳號
        /// </summary>
        public string EmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 執行行為 (登入 1、登出 2、解鎖 3)
        /// </summary>
        public AccountLogInOutAction Action { get; set; }

        /// <summary>
        /// 執行結果 (成功 1、失敗 2、鎖定 3)
        /// </summary>
        public AccountLogInOutResult Result { get; set; }

        /// <summary>
        /// 執行時間
        /// </summary>
        public DateTime ActionTime { get; set; }

        /// <summary>
        /// 來源IP
        /// </summary>
        public string IP { get; set; } = string.Empty;

        /// <summary>
        /// 來源主機名
        /// </summary>
        public string? Hostname { get; set; } = null;

        /// <summary>
        /// 解鎖者
        /// </summary>
        public string? Unlocker { get; set; } = null;

        /// <summary>
        /// 作業系統
        /// </summary>
        public string? OperationSystem { get; set; } = null;

        /// <summary>
        /// 作業系統版本
        /// </summary>
        public string? OperationSystemVersion { get; set; } = null;

        /// <summary>
        /// 瀏覽器
        /// </summary>
        public string? Browser { get; set; } = null;

        /// <summary>
        /// 瀏覽器版本
        /// </summary>
        public string? BrowserVersion { get; set; } = null;

        /// <summary>
        /// 登入設備
        /// </summary>
        public string? Device { get; set; } = null;

        /// <summary>
        /// 登入位置：國家
        /// </summary>
        public string? Country { get; set; } = null;

        /// <summary>
        /// 登入位置：城市
        /// </summary>
        public string? City { get; set; } = null;
    }
}
