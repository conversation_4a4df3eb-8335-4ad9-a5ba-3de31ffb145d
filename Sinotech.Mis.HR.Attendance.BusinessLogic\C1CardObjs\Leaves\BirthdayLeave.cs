﻿﻿﻿using Sinotech.Mis.Common;
using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Diagnostics.CodeAnalysis;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves
{
    /// <summary>
    /// 生日假處理類別
    /// 此類別負責處理員工生日假的申請、驗證及相關業務邏輯
    /// 生日假規則：員工可在生日當月申請一日（8小時）的生日假
    /// </summary>
    /// <seealso cref="Sinotech.Mis.HR.Attendance.C1CardObjs.C1CardBase" />
    [LeaveKind(LeaveKindEnum.BirthdayLeave)]
    public class BirthdayLeave : C1CardBase
    {
        #region CheckResult
        // 檢查結果定義區域
        // 此區域定義各種驗證失敗的錯誤代碼、訊息及狀態

        /// <summary>
        /// 錯誤代碼：未註冊生日假福利方案
        /// </summary>
        public const int CodeNotRegistered = 3021301;
        /// <summary>
        /// 錯誤訊息：員工的生日福利方案未選擇生日假
        /// </summary>
        private readonly string _messageNotRegistered = "您的生日福利方案未選擇【生日假】，不得申請";
        /// <summary>
        /// 錯誤狀態：未註冊生日假福利
        /// </summary>
        private readonly CardStatusEnum _statusNotRegistered = CardStatusEnum.Error;

        /// <summary>
        /// 快取的檢查結果物件：未註冊生日假福利
        /// </summary>
        private CardCheckResult? _resultNotRegistered;
        /// <summary>
        /// 取得未註冊生日假福利的檢查結果（延遲初始化）
        /// </summary>
        private CardCheckResult ResultNotRegistered =>
            _resultNotRegistered ??=
            new CardCheckResult(CodeNotRegistered, _statusNotRegistered, _messageNotRegistered);

        /// <summary>
        /// 錯誤代碼：請假時間不在生日當月範圍內
        /// </summary>
        public const int CodeNotInRange = 3021302;
        /// <summary>
        /// 錯誤訊息：生日假只能在生日當月申請
        /// </summary>
        private readonly string _messageNotInRange = "【生日假】限生日當月請假";
        /// <summary>
        /// 錯誤狀態：請假時間超出允許範圍
        /// </summary>
        private readonly CardStatusEnum _statusNotInRange = CardStatusEnum.Error;

        /// <summary>
        /// 快取的檢查結果物件：請假時間不在允許範圍內
        /// </summary>
        private CardCheckResult? _resultNotInRange;
        /// <summary>
        /// 取得請假時間超出範圍的檢查結果（延遲初始化）
        /// </summary>
        private CardCheckResult ResultNotInRange =>
            _resultNotInRange ??=
            new CardCheckResult(CodeNotInRange, _statusNotInRange, _messageNotInRange);


        /// <summary>
        /// 錯誤代碼：超過生日假額度
        /// </summary>
        public const int CodeExceedQuota = 3016303;
        /// <summary>
        /// 錯誤訊息：本年度已經使用過生日假
        /// </summary>
        private readonly string _messageExceedQuota = "今年度已請過生日假，不得超假";
        /// <summary>
        /// 錯誤狀態：超過允許的假期額度
        /// </summary>
        private readonly CardStatusEnum _statusExceedQuota = CardStatusEnum.Error;

        /// <summary>
        /// 快取的檢查結果物件：超過生日假額度
        /// </summary>
        private CardCheckResult? _resultExceedQuota;
        /// <summary>
        /// 取得超過生日假額度的檢查結果（延遲初始化）
        /// </summary>
        private CardCheckResult ResultExceedQuota =>
            _resultExceedQuota ??=
            new CardCheckResult(CodeExceedQuota, _statusExceedQuota, _messageExceedQuota);


        /// <summary>
        /// 錯誤代碼：請假時數不符合規定
        /// </summary>
        public const int CodeIllegalRange = 3016305;
        /// <summary>
        /// 錯誤訊息：請假時數必須為一整天（8小時）
        /// </summary>
        private readonly string _messageIllegalRange = "請假時數超過每日正常工時，請修改請假時間";
        /// <summary>
        /// 錯誤狀態：請假時數不合法
        /// </summary>
        private readonly CardStatusEnum _statusIllegalRange = CardStatusEnum.Error;

        /// <summary>
        /// 快取的檢查結果物件：請假時數不符合規定
        /// </summary>
        private CardCheckResult? _resultIllegalRange;
        /// <summary>
        /// 取得請假時數不合法的檢查結果（延遲初始化）
        /// </summary>
        private CardCheckResult ResultIllegalRange =>
            _resultIllegalRange ??=
            new CardCheckResult(CodeIllegalRange, _statusIllegalRange, _messageIllegalRange);

        #endregion

        /// <summary>
        /// 生日假建構子
        /// 初始化生日假處理物件，注入必要的相依性
        /// </summary>
        /// <param name="c1Card">請假單據物件，包含請假的基本資訊</param>
        /// <param name="c1CardBo">請假業務邏輯處理物件，提供各種業務邏輯方法</param>
        public BirthdayLeave(C1Card c1Card, IC1CardBo c1CardBo) : base(c1Card, c1CardBo)
        {
            // 建構子主體為空，所有初始化工作由基底類別處理
        }

        /// <summary>
        /// 檢查員工是否符合申請生日假的條件
        /// 此方法會驗證兩個主要條件：
        /// 1. 員工是否已註冊生日假福利方案
        /// 2. 申請日期是否在員工生日當月
        /// </summary>
        /// <returns>檢查結果，包含狀態碼、狀態及錯誤訊息（如有）</returns>
        internal override CardCheckResult CanTakeThisLeave()
        {
            // 檢查員工的生日福利方案是否為「生日假」
            // 只有選擇生日假方案的員工才能申請生日假
            if (!IfRegisterBirthdayLeave())
            {
                return ResultNotRegistered;
            }

            // 檢查申請日期是否在員工生日當月
            // 生日假只能在生日當月申請
            if (!IsInRange())
            {
                return ResultNotInRange;
            }

            // 所有條件都通過，允許申請
            return ResultOk;
        }


        /// <summary>
        /// 檢查是否超過允許的請假時數或額度
        /// 此方法驗證兩個條件：
        /// 1. 本年度是否已經使用過生日假（每年只能使用一次）
        /// 2. 請假時數是否為8小時（生日假必須請整天）
        /// </summary>
        /// <returns>檢查結果，包含狀態碼、狀態及錯誤訊息（如有）</returns>
        [ExcludeFromCodeCoverage]
        internal override CardCheckResult CheckOverPermittedLeaveHours()
        {
            // 檢查本年度是否已經使用過生日假
            // 生日假每年只能使用一次
            if (IsBirthdayLeaveTaken())
            {
                return ResultExceedQuota;
            }

            // 檢查請假時數是否為8小時
            // 生日假必須請整天，不允許半天或其他時數
            if (TotalHours != 8)
            {
                return ResultIllegalRange;
            }

            // 所有檢查都通過
            return ResultOk;
        }

        /// <summary>
        /// 檢查請假單的必要欄位是否已填寫完整
        /// 此方法先呼叫基底類別的欄位檢查，確保基本欄位都已填寫
        /// 生日假目前沒有額外的必要欄位需要檢查
        /// </summary>
        /// <returns>檢查結果，包含狀態碼、狀態及錯誤訊息（如有）</returns>
        internal override CardCheckResult CheckRequiredFields()
        {
            // 呼叫基底類別的必要欄位檢查
            // 包括員工編號、請假日期、請假時數等基本欄位
            var result = base.CheckRequiredFields();
            if (result != ResultOk)
            {
                return result;
            }

            // 生日假沒有額外的必要欄位需要檢查
            // 如果基底類別檢查通過，則直接回傳成功
            return ResultOk;
        }

        /// <summary>
        /// 計算生日假的可申請期間（最早及最晚可請假日期）
        /// 生日假只能在員工生日當月的工作日申請
        /// </summary>
        /// <param name="date">參考日期（通常為申請日期）</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>Tuple包含最早可請假日期及最晚可請假日期</returns>
        public override (DateTime, DateTime) CalculateLeavePermittedPeriod(DateTime date, string empNo)
        {
            // 取得員工詳細資料，包含生日資訊
            Employee employee = _c1CardBo.GetEmployeeDetail(empNo);
            // 取得員工生日，如果沒有生日資料則使用參考日期
            DateTime birthday = employee.Birthday != null ? (DateTime)employee.Birthday : date;
            
            // 原本的實作：使用日曆天計算（已註解）
            // 會包含假日和週末，不符合實際需求
            //DateTime firstDay = new DateTime(date.Year, birthday.Month, 1, 0, 0, 0, DateTimeKind.Local);
            //DateTime lastDay = firstDay.AddMonths(1).AddDays(-1);
            
            // 現在的實作：只計算工作日
            // 取得生日當月的第一個工作日作為最早可請假日期
            DateTime firstDay = _c1CardBo.FirstWorkDayInMonth(date.Year, birthday.Month, empNo);
            // 取得生日當月的最後一個工作日作為最晚可請假日期
            DateTime lastDay = _c1CardBo.LastWorkDayInMonth(date.Year, birthday.Month, empNo);
            
            return (firstDay, lastDay);
        }

        /// <summary>
        /// 檢查指定性別是否可以申請生日假
        /// 生日假不分性別，所有員工都可以申請
        /// </summary>
        /// <param name="gender">員工性別</param>
        /// <returns>永遠回傳true，因為生日假對所有性別開放</returns>
        public static bool IsAllowForThisGender(Gender gender)
        {
            // 生日假是所有員工都享有的福利，不分性別
            return true;
        }

        #region Tools
        // 輔助工具方法區域
        // 此區域包含各種私有方法，用於支援主要業務邏輯的執行

        /// <summary>
        /// 檢查員工是否已註冊生日假福利方案
        /// 員工可以選擇不同的生日福利方案，只有選擇生日假的員工才能申請生日假
        /// </summary>
        /// <returns>true表示已註冊生日假方案，false表示未註冊</returns>
        private bool IfRegisterBirthdayLeave()
        {
            // 取得員工在指定年度的生日福利方案設定
            // 回傳值1表示選擇生日假方案，其他值表示選擇其他福利方案
            return _c1CardBo.GetBirthdayWelfare(_c1Card.EmpNo, _c1Card.StartDate.Year) == 1;
        }



        /// <summary>
        /// 舊版的生日假註冊檢查方法（已停用）
        /// 此方法使用GetBirthdayLeaveInfo來同時取得福利方案和使用狀態
        /// 已被上方的IfRegisterBirthdayLeave()方法取代
        /// </summary>
        //private bool IfRegisterBirthdayLeave()
        //{
        //    _c1CardBo.GetBirthdayLeaveInfo(_c1Card.EmpNo, _c1Card.StartDate.Year,
        //        out int brithdayWelfare, out _);

        //    return brithdayWelfare == 1;
        //}



        /// <summary>
        /// 檢查員工在指定年度是否已經使用過生日假
        /// 每個員工每年只能使用一次生日假
        /// </summary>
        /// <returns>true表示已使用過，false表示尚未使用</returns>
        private bool IsBirthdayLeaveTaken()
        {
            // 查詢員工在指定年度的生日假使用記錄
            return _c1CardBo.IsBirthdayLeaveTaken(_c1Card.EmpNo, _c1Card.StartDate.Year);
        }

        /// <summary>
        /// 舊版的生日假使用狀態檢查方法（已停用）
        /// 此方法使用GetBirthdayLeaveInfo來同時取得福利方案和使用狀態
        /// 已被上方的IsBirthdayLeaveTaken()方法取代
        /// </summary>
        //private bool IsBirthdayLeaveTaken()
        //{
        //    _c1CardBo.GetBirthdayLeaveInfo(_c1Card.EmpNo, _c1Card.StartDate.Year,
        //        out _, out bool isUsed);

        //    return isUsed;
        //}


        /// <summary>
        /// 檢查申請的請假日期是否在員工生日當月
        /// 生日假只能在生日當月申請，不能跨月使用
        /// </summary>
        /// <returns>true表示在生日當月，false表示不在生日當月</returns>
        private bool IsInRange()
        {
            // 比較員工生日的月份與請假開始日期的月份
            return GetBirthday().Month == _c1Card.StartDate.Month;
        }

        /// <summary>
        /// 取得員工的生日日期
        /// 此方法從員工資料中取得生日資訊，用於判斷生日假的申請資格
        /// </summary>
        /// <returns>員工的生日日期</returns>
        private DateTime GetBirthday()
        {
            // 從業務邏輯物件中取得指定員工的生日資料
            return _c1CardBo.GetBirthday(_c1Card.EmpNo);
        }

        #endregion
    }
}
