﻿using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Data;

namespace Sinotech.Mis.HR.Attendance.Common
{
    public interface IOvertimeBo
    {
        /// <summary>
        /// B1Card 計算並轉換為 DayOvertime 物件
        /// </summary>
        /// <param name="b1Card"></param>
        /// <returns></returns>
        DayOvertime B1CardToDayOvertime(B1Card b1Card);

        /// <summary>
        /// 計算某日加班倍率，此處不計算是否超過每日上限
        /// </summary>
        /// <param name="overtime"></param>
        /// <returns></returns>
        DayOvertime CalculateDayOvertimeRate(DayOvertime overtime);

        /// <summary>
        /// 計算實際Rate數值 1又1/3、1又1/6等
        /// </summary>
        /// <param name="overtimeRateType"></param>
        /// <returns></returns>
        double CalculateRateByRateType(OvertimeRateType overtimeRateType);

        /// <summary>
        /// DayOvertime 物件轉為 B1Rate 與 B1Rate_CompHol
        /// </summary>
        /// <param name="overtime"></param>
        /// <param name="b1CardDto">加班卡DTO</param>
        /// <returns>B1Rate, B1Rate_CompHol</returns>
        (DataTable, DataTable) DayOvertime2B1Rate(DayOvertime overtime, B1CardDto b1CardDto);

        /// <summary>
        /// DayOvertime 物件計算並轉為 B1Rate 與 B1Rate_CompHol 資料表
        /// </summary>
        /// <param name="overtime"></param>
        /// <param name="b1Card"></param>
        /// <returns>B1Rate, B1Rate_CompHol</returns>
        (DataTable, DataTable) DayOvertimeToB1Rate(DayOvertime overtime, B1Card b1Card);

        /// <summary>
        /// 讀取員工指定月(依據查詢日期 ) 已填報有效(簽核中與同意)且為加班上限範圍內之加班總時數
        /// </summary>
        /// <param name="theDate">查詢日期.</param>
        /// <param name="empNo">員工編號.</param>
        int GetMonthEmployeeOvertimeHours(DateTime theDate, string empNo);

        /// <summary>
        /// 讀取員工指定日期加班與出勤資料
        /// </summary>
        /// <param name="date">The date.</param>
        /// <param name="empNo">The employee number.</param>
        OvertimeData GetOvertimeData(DateTime date, string empNo);

        /// <summary>
        /// Gets the paid hour.
        /// </summary>
        /// <param name="b1CardApp">The b1 card application.</param>
        /// <returns></returns>
        double GetPaidHour(B1CardApp b1CardApp);

    }
}
