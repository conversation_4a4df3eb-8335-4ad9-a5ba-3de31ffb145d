﻿using Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers;
using FakeItEasy;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sinotech.Mis.HR.Attendance.AttendanceCard.Models;
using Sinotech.Mis.HR.Attendance.BusinessLogic;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.DataAccess.Interfaces;
using Sinotech.Mis.HR.Attendance.Tests;
using System;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers.Tests
{
    [ExcludeFromCodeCoverage]
    public class AccountControllerTests
    {

        private readonly IAccountBo _accountBo;
        private readonly AttendanceBo _attendanceBo;

        private readonly IConfiguration _configuration;
        private readonly AccountController _controller;
        private readonly EmployeeBo _employeeBo;

        public AccountControllerTests(AttendanceBo attendanceBo, EmployeeBo employeeBo)
        {
            _employeeBo = employeeBo;
            _attendanceBo = attendanceBo;
            _configuration = new ConfigurationBuilder().
                AddJsonFile("appsettings.internet.json", optional: true, reloadOnChange: true).
                Build();
            IAccountDao _accountDao = A.Fake<IAccountDao>();
            A.CallTo(() => _accountDao.LogAccountLogin(A<AccountLogInOutDto>.Ignored)).Returns(true);
            A.CallTo(() => _accountDao.LogAccountLogout(A<AccountLogInOutDto>.Ignored)).Returns(true);
            A.CallTo(() => _accountDao.LogAccountUnlock(A<AccountLogInOutDto>.Ignored)).Returns(true);
            A.CallTo(() => _accountDao.GetLoginFailCount(A<string>.Ignored)).Returns(0);
            ILogger<AccountBo> logger = A.Fake<ILogger<AccountBo>>();
            IAccountBo accountBo = new AccountBo(employeeBo, _accountDao, _configuration, logger);
            _accountBo = accountBo;
            ILogger<AccountController> accountLogger = A.Fake<ILogger<AccountController>>();
            _controller = new AccountController(_attendanceBo, _employeeBo, accountBo, _configuration, accountLogger);
        }

        [Fact]
        public async Task CallLogoutGetErrorCode500()
        {
            // Arrange
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo("test");

            // 檢查實際回傳的型別
            var result = await _controller.Logout();

            Assert.IsType<ObjectResult>(result);
            ObjectResult objectResult = result as ObjectResult;
            Assert.Equal(500, objectResult.StatusCode);
        }

        [Fact]
        public async Task CallLogoutGetNullReferenceException()
        {
            // Act & Assert
            await Assert.ThrowsAsync<NullReferenceException>(() => _controller.Logout());
        }


        [Fact]
        public async Task CanCallLogin()
        {
            // Arrange
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo("test");

            var loginModel = new LoginModel
            {
                Username = "TestValue1145293986",
                Password = "TestValue1378493085"
            };

            // Act
            var result = await _controller.Login(loginModel);
            Assert.NotNull(result);
        }

        [Fact]
        public void CanConstruct()
        {
            // Act
            ILogger<AccountController> accountLogger = A.Fake<ILogger<AccountController>>();
            var instance = new AccountController(_attendanceBo, _employeeBo, _accountBo, _configuration, accountLogger);

            // Assert
            Assert.NotNull(instance);
        }

        [Fact]
        public void CannotCallIntranetIsAuthenticated()
        {
            // Act
            Assert.Throws<NullReferenceException>(() => _controller.IntranetIsAuthenticated());
        }

        [Fact]
        public void CannotCallIsAuthenticated()
        {
            Assert.Throws<NullReferenceException>(() => _controller.IsAuthenticated());
        }

        [Fact]
        public async Task CannotCallLogin()
        {
            // Arrange
            var loginModel = new LoginModel
            {
                Username = "TestValue1145293986",
                Password = "TestValue1378493085"
            };

            // Act
            await Assert.ThrowsAsync<NullReferenceException>(() => _controller.Login(loginModel));
        }

        [Fact]
        public async Task CannotCallLoginWithNullLoginModel()
        {
            await Assert.ThrowsAsync<NullReferenceException>(() => _controller.Login(default(LoginModel)));
        }

        [Fact]
        public void CannotConstructWithNullConfiguration()
        {
            ILogger<AccountController> accountLogger = A.Fake<ILogger<AccountController>>();
            Assert.Throws<ArgumentNullException>(() => new AccountController(_attendanceBo, _employeeBo, _accountBo, default(IConfiguration), accountLogger));
        }

        [Fact]
        public void CannotConstructWithNullEmployeeBo()
        {
            ILogger<AccountController> accountLogger = A.Fake<ILogger<AccountController>>();
            Assert.Throws<ArgumentNullException>(() => new AccountController(_attendanceBo, default(EmployeeBo), _accountBo, _configuration, accountLogger));
        }

        [Fact]
        public void CannotConstructWithNullLogger()
        {
            ILogger<AccountController> accountLogger = A.Fake<ILogger<AccountController>>();
            Assert.Throws<ArgumentNullException>(() => new AccountController(_attendanceBo, _employeeBo, _accountBo, null, accountLogger));
        }

        [Theory]
        [InlineData("test", false)]
        [InlineData("0395", true)]
        public void IntranetIsAuthenticated_ReturnsOkResult(string empNo, bool expected)
        {
            // Arrange
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo(empNo);

            // Act
            var result = _controller.IntranetIsAuthenticated() as OkObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(200, result.StatusCode);
            string str = JsonConvert.SerializeObject(result.Value);
            var x = JsonConvert.DeserializeObject<TempClass>(str);
            Assert.NotNull(x);
            Assert.Equal(expected, x.IsAuthenticated);
        }

        [Theory]
        [InlineData("test", false)]
        [InlineData("0395", true)]
        public void IsAuthenticated_ReturnsOkResult(string empNo, bool expected)
        {
            // Arrange
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo(empNo);
            // Act
            var result = _controller.IsAuthenticated() as OkObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(200, result.StatusCode);
            string str = JsonConvert.SerializeObject(result.Value);
            var x = JsonConvert.DeserializeObject<TempClass>(str);
            Assert.NotNull(x);
            Assert.Equal(expected, x.IsAuthenticated);
        }

        [Fact]
        public void IsAuthenticatedTest()
        {
            var configuration = new ConfigurationBuilder().
            AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).
            Build();
            ILogger<AccountController> accountLogger = A.Fake<ILogger<AccountController>>();
            var controller = new AccountController(_attendanceBo, _employeeBo, _accountBo, configuration, accountLogger);
            RedirectToActionResult result = (RedirectToActionResult)controller.IsAuthenticated();
            Assert.Equal("IntranetIsAuthenticated", result.ActionName);
        }

        [Theory]
        [InlineData("test", false)]
        [InlineData("0395", false)]
        [InlineData("0349", false)]
        [InlineData("2008", false)]
        [InlineData("0305", false)]
        public async Task Login_InvalidCredentials_NotAuthenticated(string empNo, bool expected)
        {
            // Arrange
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo(empNo);
            var loginModel = new LoginModel { Username = empNo, Password = "wrong" };

            // Act
            var result = await _controller.Login(loginModel) as OkObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(200, result.StatusCode);
            string str = JsonConvert.SerializeObject(result.Value);
            var x = JsonConvert.DeserializeObject<TempClass>(str);
            Assert.NotNull(x);
            Assert.Equal(expected, x.IsAuthenticated);
        }

        public class TempClass
        {

            public bool IsAuthenticated { get; set; } = true;
            public string UserId { get; set; }

        }

    }
}
