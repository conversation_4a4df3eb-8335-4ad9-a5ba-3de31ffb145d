﻿using Sinotech.Mis.Helpers;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.DataAccess.Interfaces;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;

namespace Sinotech.Mis.HR.Attendance.DataAccess.Ado
{

    /// <summary>
    /// AccountDao
    /// </summary>
    public class AccountDao : IAccountDao
    {

        /// <summary>
        /// 連線字串
        /// </summary>
        private readonly string _connectionString;

        /// <summary>
        /// 建構子
        /// </summary>
        /// <param name="connectionString"></param>
        public AccountDao(string connectionString)
        {
            _connectionString = connectionString;
        }

        /// <summary>
        /// 產生Lockout參數
        /// </summary>
        /// <param name="logInOutDto"></param>
        /// <param name="firstLoginTime"></param>
        /// <param name="firstLockTime"></param>
        /// <param name="failCount"></param>
        /// <returns></returns>
        private static List<SqlParameter> GenerateLockoutParameters(AccountLogInOutDto logInOutDto, DateTime firstLoginTime, DateTime? firstLockTime, int failCount)
        {
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar, 50);
            parameterEmpNo.Value = logInOutDto.EmpNo;
            parameters.Add(parameterEmpNo);

            SqlParameter parameterFirstLoginTime = new SqlParameter("@FirstLoginTime", SqlDbType.DateTime);
            parameterFirstLoginTime.Value = firstLoginTime;
            parameters.Add(parameterFirstLoginTime);

            SqlParameter parameterFailCount = new SqlParameter("@FailCount", SqlDbType.Int);
            parameterFailCount.Value = failCount;
            parameters.Add(parameterFailCount);

            SqlParameter parameterLastLoginTime = new SqlParameter("@LastLoginTime", SqlDbType.DateTime);
            parameterLastLoginTime.Value = logInOutDto.ActionTime;
            parameters.Add(parameterLastLoginTime);

            SqlParameter parameterFirstLockTime = new SqlParameter("@FirstLockTime", SqlDbType.DateTime);
            if (firstLockTime == null)
            {
                parameterFirstLockTime.Value = DBNull.Value;
            }
            else
            {
                parameterFirstLockTime.Value = firstLockTime;
            }
            parameters.Add(parameterFirstLockTime);

            SqlParameter parameterIP = new SqlParameter("@IP", SqlDbType.VarChar);
            if (string.IsNullOrEmpty(logInOutDto.IP))
            {
                parameterIP.Value = DBNull.Value;
            }
            else
            {
                parameterIP.Value = logInOutDto.IP;
            }
            parameters.Add(parameterIP);

            SqlParameter parameterHostname = new SqlParameter("@Hostname", SqlDbType.VarChar);
            if (string.IsNullOrEmpty(logInOutDto.Hostname))
            {
                parameterHostname.Value = DBNull.Value;
            }
            else
            {
                parameterHostname.Value = logInOutDto.Hostname;
            }
            parameters.Add(parameterHostname);

            SqlParameter parameterUnlocker = new SqlParameter("@Unlocker", SqlDbType.VarChar, 4);
            if (string.IsNullOrEmpty(logInOutDto.Unlocker))
            {
                parameterUnlocker.Value = DBNull.Value;
            }
            else
            {
                parameterUnlocker.Value = logInOutDto.Unlocker;
            }
            parameters.Add(parameterUnlocker);

            SqlParameter parameterOperationSystem = new SqlParameter("@OperationSystem", SqlDbType.NVarChar);
            if (string.IsNullOrEmpty(logInOutDto.OperationSystem))
            {
                parameterOperationSystem.Value = DBNull.Value;
            }
            else
            {
                parameterOperationSystem.Value = logInOutDto.OperationSystem;
            }
            parameters.Add(parameterOperationSystem);

            SqlParameter parameterOperationSystemVersion = new SqlParameter("@OperationSystemVersion", SqlDbType.VarChar);
            if (string.IsNullOrEmpty(logInOutDto.OperationSystemVersion))
            {
                parameterOperationSystemVersion.Value = DBNull.Value;
            }
            else
            {
                parameterOperationSystemVersion.Value = logInOutDto.OperationSystemVersion;
            }
            parameters.Add(parameterOperationSystemVersion);

            SqlParameter parameterBrowser = new SqlParameter("@Browser", SqlDbType.NVarChar);
            if (string.IsNullOrEmpty(logInOutDto.Browser))
            {
                parameterBrowser.Value = DBNull.Value;
            }
            else
            {
                parameterBrowser.Value = logInOutDto.Browser;
            }
            parameters.Add(parameterBrowser);

            SqlParameter parameterBrowserVersion = new SqlParameter("@BrowserVersion", SqlDbType.VarChar);
            if (string.IsNullOrEmpty(logInOutDto.BrowserVersion))
            {
                parameterBrowserVersion.Value = DBNull.Value;
            }
            else
            {
                parameterBrowserVersion.Value = logInOutDto.BrowserVersion;
            }
            parameters.Add(parameterBrowserVersion);

            SqlParameter parameterDevice = new SqlParameter("@Device", SqlDbType.NVarChar);
            if (string.IsNullOrEmpty(logInOutDto.Device))
            {
                parameterDevice.Value = DBNull.Value;
            }
            else
            {
                parameterDevice.Value = logInOutDto.Device;
            }
            parameters.Add(parameterDevice);

            SqlParameter parameterCountry = new SqlParameter("@Country", SqlDbType.NVarChar);
            if (string.IsNullOrEmpty(logInOutDto.Country))
            {
                parameterCountry.Value = DBNull.Value;
            }
            else
            {
                parameterCountry.Value = logInOutDto.Country;
            }
            parameters.Add(parameterCountry);

            SqlParameter parameterCity = new SqlParameter("@City", SqlDbType.NVarChar);
            if (string.IsNullOrEmpty(logInOutDto.City))
            {
                parameterCity.Value = DBNull.Value;
            }
            else
            {
                parameterCity.Value = logInOutDto.City;
            }
            parameters.Add(parameterCity);

            return parameters;
        }


        /// <summary>
        /// 產生記錄登入登出參數
        /// </summary>
        /// <param name="logInOutDto"></param>
        /// <returns></returns>
        private static List<SqlParameter> GenerateLogInOutParameters(AccountLogInOutDto logInOutDto)
        {
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar, 50);
            parameterEmpNo.Value = logInOutDto.EmpNo;
            parameters.Add(parameterEmpNo);

            SqlParameter parameterAction = new SqlParameter("@Action", SqlDbType.Int);
            parameterAction.Value = logInOutDto.Action;
            parameters.Add(parameterAction);

            SqlParameter parameterResult = new SqlParameter("@Result", SqlDbType.Int);
            parameterResult.Value = logInOutDto.Result;
            parameters.Add(parameterResult);

            SqlParameter parameterActionTime = new SqlParameter("@ActionTime", SqlDbType.DateTime);
            parameterActionTime.Value = logInOutDto.ActionTime;
            parameters.Add(parameterActionTime);

            SqlParameter parameterIP = new SqlParameter("@IP", SqlDbType.VarChar);
            if (string.IsNullOrEmpty(logInOutDto.IP))
            {
                parameterIP.Value = DBNull.Value;
            }
            else
            {
                parameterIP.Value = logInOutDto.IP;
            }
            parameters.Add(parameterIP);

            SqlParameter parameterHostname = new SqlParameter("@Hostname", SqlDbType.VarChar);
            if (string.IsNullOrEmpty(logInOutDto.Hostname))
            {
                parameterHostname.Value = DBNull.Value;
            }
            else
            {
                parameterHostname.Value = logInOutDto.Hostname;
            }
            parameters.Add(parameterHostname);

            SqlParameter parameterUnlocker = new SqlParameter("@Unlocker", SqlDbType.VarChar);
            if (string.IsNullOrEmpty(logInOutDto.Unlocker))
            {
                parameterUnlocker.Value = DBNull.Value;
            }
            else
            {
                parameterUnlocker.Value = logInOutDto.Unlocker;
            }
            parameters.Add(parameterUnlocker);

            SqlParameter parameterOperationSystem = new SqlParameter("@OperationSystem", SqlDbType.NVarChar);
            if (string.IsNullOrEmpty(logInOutDto.OperationSystem))
            {
                parameterOperationSystem.Value = DBNull.Value;
            }
            else
            {
                parameterOperationSystem.Value = logInOutDto.OperationSystem;
            }
            parameters.Add(parameterOperationSystem);

            SqlParameter parameterOperationSystemVersion = new SqlParameter("@OperationSystemVersion", SqlDbType.VarChar);
            if (string.IsNullOrEmpty(logInOutDto.OperationSystemVersion))
            {
                parameterOperationSystemVersion.Value = DBNull.Value;
            }
            else
            {
                parameterOperationSystemVersion.Value = logInOutDto.OperationSystemVersion;
            }
            parameters.Add(parameterOperationSystemVersion);

            SqlParameter parameterBrowser = new SqlParameter("@Browser", SqlDbType.NVarChar);
            if (string.IsNullOrEmpty(logInOutDto.Browser))
            {
                parameterBrowser.Value = DBNull.Value;
            }
            else
            {
                parameterBrowser.Value = logInOutDto.Browser;
            }
            parameters.Add(parameterBrowser);

            SqlParameter parameterBrowserVersion = new SqlParameter("@BrowserVersion", SqlDbType.VarChar);
            if (string.IsNullOrEmpty(logInOutDto.BrowserVersion))
            {
                parameterBrowserVersion.Value = DBNull.Value;
            }
            else
            {
                parameterBrowserVersion.Value = logInOutDto.BrowserVersion;
            }
            parameters.Add(parameterBrowserVersion);

            SqlParameter parameterDevice = new SqlParameter("@Device", SqlDbType.NVarChar);
            if (string.IsNullOrEmpty(logInOutDto.Device))
            {
                parameterDevice.Value = DBNull.Value;
            }
            else
            {
                parameterDevice.Value = logInOutDto.Device;
            }
            parameters.Add(parameterDevice);

            SqlParameter parameterCountry = new SqlParameter("@Country", SqlDbType.NVarChar);
            if (string.IsNullOrEmpty(logInOutDto.Country))
            {
                parameterCountry.Value = DBNull.Value;
            }
            else
            {
                parameterCountry.Value = logInOutDto.Country;
            }
            parameters.Add(parameterCountry);

            SqlParameter parameterCity = new SqlParameter("@City", SqlDbType.NVarChar);
            if (string.IsNullOrEmpty(logInOutDto.City))
            {
                parameterCity.Value = DBNull.Value;
            }
            else
            {
                parameterCity.Value = logInOutDto.City;
            }
            parameters.Add(parameterCity);

            return parameters;
        }

        /// <summary>
        /// 記錄登入失敗
        /// </summary>
        /// <param name="logInOutDto"></param>
        /// <param name="connection"></param>
        /// <param name="transaction"></param>
        /// <param name="firstLoginTime"></param>
        /// <param name="firstLockTime"></param>
        /// <param name="failCount"></param>
        /// <returns></returns>
        private static bool InsertOrUpdateLoginFail(AccountLogInOutDto logInOutDto, SqlConnection connection, SqlTransaction transaction,
            DateTime firstLoginTime, DateTime? firstLockTime, int failCount)
        {
            bool ret = true;
            string sql = "SELECT * FROM AccountLockout WHERE EmpNo = @EmpNo;";
            SqlParameter parameter = new SqlParameter("@EmpNo", SqlDbType.VarChar, 50);
            parameter.Value = logInOutDto.EmpNo;
            DataTable dt = SqlHelper.GetDataTable(connection, transaction, sql, parameter);
            if (dt != null && dt.Rows.Count > 0) // In fact, there should be only one record
            {
                // Update
                sql = @"UPDATE AccountLockout SET
                    FirstLoginTime=@FirstLoginTime,LastLoginTime=@LastLoginTime,
                    FirstLockTime=@FirstLockTime, FailCount=@FailCount, IP=@IP, Hostname=@Hostname, 
                    OperationSystem=@OperationSystem, OperationSystemVersion=@OperationSystemVersion,
                    Browser=@Browser, BrowserVersion=@BrowserVersion, Device=@Device,
                    Country=@Country,City=@City WHERE EmpNo=@EmpNo;";
            }
            else // Insert
            {
                sql = @"INSERT INTO AccountLockout (EmpNo, FirstLoginTime, LastLoginTime, 
                    FirstLockTime, FailCount, IP, Hostname,
                    OperationSystem, OperationSystemVersion, Browser, BrowserVersion,
                    Device, Country, City) VALUES (@EmpNo, @FirstLoginTime, @LastLoginTime, @FirstLockTime,
                    @FailCount, @IP, @Hostname, @OperationSystem, @OperationSystemVersion, @Browser,
                    @BrowserVersion, @Device, @Country, @City);";
            }
            List<SqlParameter> parameters = GenerateLockoutParameters(logInOutDto, firstLoginTime, firstLockTime, failCount);
            SqlHelper.ExecuteNonQuery(connection, transaction, sql, parameters);
            return ret;
        }

        /// <summary>
        /// 取得登入失敗次數
        /// </summary>
        /// <param name="accountId"></param>
        /// <returns>15分鐘內 登入失敗次數</returns>
        public int GetLoginFailCount(string accountId)
        {
            int failCount = 0;
            DataRow? dataRow = GetLoginFailRecord(accountId);
            if (dataRow != null)
            {
                DateTime lastLoginTime = (DateTime)dataRow["LastLoginTime"];
                DateTime now = DateTime.Now;
                DateTime lockoutUntil = lastLoginTime.AddMinutes(AttendanceParameters.AccountLockoutTime);
                if (lockoutUntil > now) // In 15 minutes, 次數計算仍有效
                {
                    failCount = (int)dataRow["FailCount"];
                }
            }
            return failCount;
        }

        /// <summary>
        /// 取得某帳號登入失敗資料
        /// </summary>
        /// <param name="accountId">帳號</param>
        /// <returns>登入失敗 DataRow</returns>
        public DataRow? GetLoginFailRecord(string accountId)
        {
            DataRow? dataRow = null;
            string sql = "SELECT * FROM AccountLockout WHERE EmpNo = @EmpNo;";
            SqlParameter parameter = new SqlParameter("@EmpNo", SqlDbType.VarChar, 50);
            parameter.Value = accountId;
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameter);
            if (dt != null && dt.Rows.Count > 0) // In fact, there should be only one record
            {
                dataRow = dt.Rows[0];
            }
            return dataRow;
        }

        /// <summary>
        /// 增加登入記錄
        /// </summary>
        /// <param name="logInOutDto"></param>
        public bool InsertLoginRecord(AccountLogInOutDto logInOutDto) 
        {
            // 記錄登入紀錄
            string sql = @"INSERT INTO AttendanceLogInOut 
                (EmpNo, Action, Result, ActionTime, IP, Hostname, Unlocker, 
                OperationSystem, OperationSystemVersion, Browser, BrowserVersion, 
                Device, Country, City) VALUES
                (@EmpNo, @Action, @Result, @ActionTime, @IP, @Hostname, 
                @Unlocker, @OperationSystem, @OperationSystemVersion,
                @Browser, @BrowserVersion, @Device, @Country, @City);";

            List<SqlParameter> parameters = GenerateLogInOutParameters(logInOutDto);
            int result = SqlHelper.ExecuteUpdate(_connectionString, sql, parameters);
            bool ret = result > 0;
            return ret;
        }

        /// <summary>
        /// 增加登入記錄
        /// </summary>
        /// <param name="logInOutDto"></param>
        /// <param name="connection"></param>
        /// <param name="transaction"></param>
        private static void InsertLoginRecord(AccountLogInOutDto logInOutDto, SqlConnection connection, SqlTransaction transaction)
        {
            // 記錄登入紀錄
            string sql = @"INSERT INTO AttendanceLogInOut 
                (EmpNo, Action, Result, ActionTime, IP, Hostname, Unlocker, 
                OperationSystem, OperationSystemVersion, Browser, BrowserVersion, 
                Device, Country, City) VALUES
                (@EmpNo, @Action, @Result, @ActionTime, @IP, @Hostname, 
                @Unlocker, @OperationSystem, @OperationSystemVersion,
                @Browser, @BrowserVersion, @Device, @Country, @City);";

            List<SqlParameter> parameters = GenerateLogInOutParameters(logInOutDto);
            SqlHelper.ExecuteNonQuery(connection, transaction, sql, parameters);
        }

        /// <summary>
        /// 記錄登入成功
        /// </summary>
        /// <param name="logInOutDto"></param>
        /// <returns></returns>
        public bool LogAccountLogin(AccountLogInOutDto logInOutDto)
        {
            bool ret = true;
            using (SqlConnection connection = new SqlConnection(_connectionString))
            {
                connection.Open();
                using (SqlTransaction transaction = connection.BeginTransaction())
                {
                    //增加登入記錄
                    InsertLoginRecord(logInOutDto, connection, transaction);
                    // 刪除失敗紀錄
                    string sql = "DELETE FROM AccountLockout WHERE EmpNo = @EmpNo;";
                    SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar, 50);
                    parameterEmpNo.Value = logInOutDto.EmpNo;
                    SqlHelper.ExecuteNonQuery(connection, transaction, sql, parameterEmpNo);
                    transaction.Commit();
                }
                connection.Close();
            }
            return ret;
        }

        /// <summary>
        /// 記錄登入失敗
        /// </summary>
        /// <param name="logInOutDto"></param>
        /// <param name="firstLoginTime"></param>
        /// <param name="firstLockTime"></param>
        /// <param name="failCount"></param>
        /// <returns></returns>
        public bool LogAccountLoginFail(AccountLogInOutDto logInOutDto, DateTime firstLoginTime, DateTime? firstLockTime, int failCount)
        {
            bool ret = true;

            using (SqlConnection connection = new SqlConnection(_connectionString))
            {
                connection.Open();
                using (SqlTransaction transaction = connection.BeginTransaction())
                {
                    //增加登入記錄
                    InsertLoginRecord(logInOutDto, connection, transaction);
                    // 增加失敗紀錄
                    bool result = InsertOrUpdateLoginFail(logInOutDto, connection, transaction, firstLoginTime, firstLockTime, failCount);
                    if (!result)
                    {
                        ret = false;
                        transaction.Rollback();
                        return ret;
                    }

                    transaction.Commit();
                }
                connection.Close();
            }
            return ret;
        }

        /// <summary>
        /// 記錄登出
        /// </summary>
        /// <param name="logInOutDto"></param>
        /// <returns></returns>
        public bool LogAccountLogout(AccountLogInOutDto logInOutDto)
        {
            bool ret = true;
            // 記錄登出紀錄
            string sql = @"INSERT INTO AttendanceLogInOut 
                (EmpNo, Action, Result, ActionTime, IP, Hostname, Unlocker, 
                OperationSystem, OperationSystemVersion, Browser, BrowserVersion, 
                Device, Country, City) VALUES
                (@EmpNo, @Action, @Result, @ActionTime, @IP, @Hostname, 
                @Unlocker, @OperationSystem, @OperationSystemVersion,
                @Browser, @BrowserVersion, @Device, @Country, @City);";
            List<SqlParameter> parameters = GenerateLogInOutParameters(logInOutDto);
            using (SqlConnection connection = new SqlConnection(_connectionString))
            {
                connection.Open();
                using (SqlTransaction transaction = connection.BeginTransaction())
                {
                    SqlHelper.ExecuteNonQuery(connection, transaction, sql, parameters);
                    transaction.Commit();
                }
                connection.Close();
            }
            return ret;
        }

        /// <summary>
        /// 記錄解鎖
        /// </summary>
        /// <param name="logInOutDto"></param>
        /// <returns></returns>
        public bool LogAccountUnlock(AccountLogInOutDto logInOutDto)
        {
            bool ret = true;
            // 記錄解鎖紀錄
            string sql = @"INSERT INTO AttendanceLogInOut 
                (EmpNo, Action, Result, ActionTime, IP, Hostname, Unlocker, 
                OperationSystem, OperationSystemVersion, Browser, BrowserVersion, 
                Device, Country, City) VALUES
                (@EmpNo, @Action, @Result, @ActionTime, @IP, @Hostname, 
                @Unlocker, @OperationSystem, @OperationSystemVersion,
                @Browser, @BrowserVersion, @Device, @Country, @City);";
            List<SqlParameter> parameters = GenerateLogInOutParameters(logInOutDto);
            using (SqlConnection connection = new SqlConnection(_connectionString))
            {
                connection.Open();
                using (SqlTransaction transaction = connection.BeginTransaction())
                {
                    // 記錄解鎖
                    SqlHelper.ExecuteNonQuery(connection, transaction, sql, parameters);
                    // 刪除鎖定記錄
                    sql = "DELETE FROM AccountLockout WHERE EmpNo = @EmpNo;";
                    SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar, 50);
                    parameterEmpNo.Value = logInOutDto.EmpNo;
                    SqlHelper.ExecuteNonQuery(connection, transaction, sql, parameterEmpNo);
                    transaction.Commit();
                }
                connection.Close();
            }
            return ret;
        }

    }
}
