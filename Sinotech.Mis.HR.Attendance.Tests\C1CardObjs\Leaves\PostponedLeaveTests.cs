﻿using FakeItEasy;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves;
using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves.Tests
{
    public class PostponedLeaveTests : TestC1CardBase
    {
        public PostponedLeaveTests()
        {
            #region Prepare Data

            _c1Card.LeaveNumber = LeaveKindEnum.PostponedLeave;

            #endregion
        }

        [Theory]
        [InlineData(false, PostponedLeave.CodeLeaveRecordNotCreateYet)]
        [InlineData(true, PostponedLeave.CodeOk)]
        public void TestCanTakeThisLeave(bool ifLeaveRecordExists, int returnCode)
        {
            A.CallTo(() => _c1CardBo.IfLeaveRecordExists(A<string>.Ignored,
                A<DateTime>.Ignored, A<DateTime>.Ignored)).Returns(ifLeaveRecordExists);

            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CanTakeThisLeave();

            Assert.Equal(returnCode, result.Code);
        }

        [Theory]
        [InlineData(10, 110, PostponedLeave.CodeOk)]
        [InlineData(110, 110, PostponedLeave.CodeOk)]
        [InlineData(115, 110, PostponedLeave.CodeExceedQuota)]
        public void TestExceedQuota(int applyHours, int postponedLeaveRemainingHours, int returnCode)
        {
            A.CallTo(() => _c1CardBo.CalculateEmpTakeLeaveWorkingHours(A<String>.Ignored, A<DateTime>.Ignored,
                A<DateTime>.Ignored)).Returns(applyHours);
            A.CallTo(() => _c1CardBo.GetPostponedLeaveRemainingHours(A<String>.Ignored, A<DateTime>.Ignored)).Returns(postponedLeaveRemainingHours);

            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CheckOverPermittedLeaveHours();

            Assert.Equal(returnCode, result.Code);
        }

        [Fact]
        public void TestCheckRequiredFields()
        {
            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CheckRequiredFields();

            Assert.Equal(PostponedLeave.CodeOk, result.Code);
        }

        [Theory]
        [InlineData(Gender.Female, true)]
        [InlineData(Gender.Male, true)]
        public void TestGender(Gender gender, bool expected)
        {
            var result = PostponedLeave.IsAllowForThisGender(gender);
            Assert.Equal(expected, result);
        }
    }
}