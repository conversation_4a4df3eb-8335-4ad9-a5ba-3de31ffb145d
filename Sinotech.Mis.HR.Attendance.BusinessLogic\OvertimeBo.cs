﻿using Sinotech.Mis.Helpers;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.DataAccess.Interfaces;
using Sinotech.Mis.HR.Attendance.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Runtime.Caching;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic
{
    /// <summary>
    /// 加班計算 商業物件
    /// </summary>
    public class OvertimeBo: IOvertimeBo
    {

        /// <summary>快取實例</summary>
        private static ObjectCache _cache = MemoryCache.Default;
        private readonly static object CacheLock = new object();
        private readonly IAttendanceBo _attendanceBo;
        private readonly IAttendanceDao _attendanceDao;
        private readonly IEmployeeBo _employeeBo;
        private readonly IWorkdayBo _workdayBo;

        /// <summary>
        /// 建構元
        /// </summary>
        /// <param name="attendanceDao"></param>
        /// <param name="attendanceBo"></param>
        /// <param name="employeeBo"></param>
        /// <param name="workdayBo"></param>
        public OvertimeBo(IAttendanceDao attendanceDao, IAttendanceBo attendanceBo,
            IEmployeeBo employeeBo, IWorkdayBo workdayBo)
        {
            _attendanceDao = attendanceDao;
            _attendanceBo = attendanceBo;
            _employeeBo = employeeBo;
            _workdayBo = workdayBo;

        }

        /// <summary>從 HourDetails 計算加班倍率 B1Rate</summary>
        /// <param name="b1Rates">B1Rate List</param>
        /// <param name="projectNumber">計畫編號</param>
        /// <param name="rate">倍率</param>
        /// <param name="b1Card">加班卡 B1Card</param>
        /// <returns>加班倍率 B1Rate 物件</returns>
        private B1Rate GetB1RateFromHourDetails(List<B1Rate> b1Rates, string projectNumber, OvertimeRateType rate, B1Card b1Card)
        {
            B1Rate? b1Rate = null;
            foreach (B1Rate b in b1Rates)
            {
                if (b.B1_PRJNO == projectNumber && b.B1_RATE == (int)rate)
                {
                    b1Rate = b;
                    break;
                }
            }
            if (b1Rate == null)
            {
                b1Rate = new B1Rate();
                b1Rate.B1_EMPNO = b1Card.EmpNo;
                b1Rate.B1_PRJNO = projectNumber;
                b1Rate.B1_RATE = (int)rate;
                b1Rate.B1_HOURS = 0;
                b1Rate.B1_YYMMDD = CardUtility.RocChineseYYYMMDD(b1Card.Details[0].StartTime);
                b1Rate.B1_SHEETNO = b1Card.SheetNo;
                b1Rate.B1_SOURCE = b1Card.Source;
                b1Rate.UpdatedEmpNo = b1Card.UpdatedEmpNo;
                b1Rate.UpdatedName = b1Card.UpdatedName;
                b1Rate.UpdatedTime = b1Card.UpdatedTime;
                b1Rate.UpdatedIP = b1Card.UpdatedIP;
                b1Rate.UpdatedHost = b1Card.UpdatedHost;
                b1Rates.Add(b1Rate);
            }
            return b1Rate;
        }

        /// <summary>Gets the B1Rate from hour details.</summary>
        /// <param name="b1Rates">The b1 rates.</param>
        /// <param name="projectNumber">The project number.</param>
        /// <param name="rateType">倍率</param>
        /// <param name="b1CardDto">The b1 card dto.</param>
        /// <returns>
        ///   <br />
        /// </returns>
        private B1Rate GetB1RateFromHourDetails(List<B1Rate> b1Rates, string projectNumber, OvertimeRateType rateType, B1CardDto b1CardDto)
        {
            B1Rate? b1Rate = null;
            foreach (B1Rate b in b1Rates)
            {
                if (b.B1_PRJNO == projectNumber && b.B1_RATE == (int)rateType)
                {
                    b1Rate = b;
                    break;
                }
            }
            if (b1Rate == null)
            {
                b1Rate = new B1Rate();
                b1Rate.B1_EMPNO = b1CardDto.B1_EMPNO;
                b1Rate.B1_PRJNO = projectNumber;
                b1Rate.B1_RATE = (int)rateType;
                b1Rate.B1_HOURS = 0;
                b1Rate.B1_YYMMDD = $"{b1CardDto.B1_YYMM}{b1CardDto.B1_SDD}";
                b1Rate.B1_SHEETNO = b1CardDto.B1_SHEETNO;
                b1Rate.B1_SOURCE = b1CardDto.B1_SOURCE;
                b1Rate.UpdatedEmpNo = b1CardDto.UpdatedEmpNo;
                b1Rate.UpdatedName = b1CardDto.UpdatedName;
                b1Rate.UpdatedTime = b1CardDto.UpdatedTime;
                b1Rate.UpdatedIP = b1CardDto.UpdatedIP;
                b1Rate.UpdatedHost = b1CardDto.UpdatedHost;
                b1Rates.Add(b1Rate);
            }
            return b1Rate;
        }

        /// <summary>
        /// 由OvertimeRecord計算出此筆紀錄中每小時詳細倍率分配
        /// </summary>
        /// <param name="overtime"></param>
        /// <param name="hours"></param>
        /// <param name="record"></param>
        /// <returns></returns>
        private int GetHoursDetail(DayOvertime overtime, int hours, OvertimeRecord record)
        {
            List<OvertimeHour> hoursList = new List<OvertimeHour>();
            record.HourDetails = new List<OvertimeHour>();
            record.RateDetails = new List<OvertimeRecordRate>();
            TimeSpan diff = record.EndTime - record.StartTime;
            record.Hours = diff.Hours;

            for (int i = 1; i <= record.Hours; i++)
            {
                hours++;
                OvertimeHour overtimeHour = GetOvertimeHourByRateType(hours, overtime.WorkdayType, record.OvertimeType);
                overtimeHour.ProjectNumber = record.ProjectNumber;
                overtimeHour.TypeOrder = record.Order;
                hoursList.Add(overtimeHour);
            }

            foreach (OvertimeHour overtimeHour in hoursList)
            {
                overtime.HourDetails.Add(overtimeHour);
            }
            return hours;
        }

        /// <summary>
        /// 取得加班倍率，此處不做正確性檢核，若工作日加班超過4小時則會錯誤
        /// </summary>
        /// <param name="nthHour"></param>
        /// <param name="dayType"></param>
        /// <param name="overtimeType"></param>
        /// <returns>傳回 加班倍率, 付費加班倍率, 是否算在加班時數上限</returns>
        /// <exception cref="Exception"></exception>
        private OvertimeHour GetOvertimeHourByRateType(int nthHour, WorkdayType dayType, OvertimeType overtimeType)
        {
            if (dayType == WorkdayType.SundayHoliday || dayType == WorkdayType.SundayRegularHoliday)
            {
                throw new Exception("星期日為休息日不得加班");
            }
            //if (nthHour <= 0 || nthHour > AttendanceParameters.DayOvertimeLimit)
            //{
            //    throw new Exception("嚴禁非法使用");
            //}

            bool inOvertimeLimits = true;
            OvertimeRateType rateType = OvertimeRateType.One;
            OvertimeRateType paidRateType = OvertimeRateType.One;
            // 週間國定假日、 補假日、週間天災日    第 1～ 8小時 1倍 RateType: 1 One
            // 週間國定假日、 補假日、週間天災日    第 9～10小時 1又1/3倍 RateType: 2 OneAndOneThird
            // 週間國定假日、 補假日、週間天災日    第11～12小時 1又2/3倍 RateType: 3 OneAndTwoThirds

            // 工作日、補班日                     第 1～ 2小時 1又1/3倍 RateType: 2 OneAndOneThird
            // 工作日、補班日                     第 3～ 4小時 1又1/3倍 RateType: 3 OneAndTwoThirds

            // 週六休息日、彈性放假日、            第 1～ 2小時 1又1/3倍 RateType: 2 OneAndOneThird
            // 週六國定假日、週六天災日            第 3～ 8小時 1又2/3倍  RateType: 3 OneAndTwoThirds
            // 週六國定假日、週六天災日            第 9～ 12小時 2又2/3倍 RateType: 6 TwoAndTwoThirds

            switch (dayType)
            {
                case WorkdayType.WeekHoliday:
                case WorkdayType.MakeUpHoliday:
                case WorkdayType.WeekNaturalDisasterDay:
                    if (nthHour < 9)
                    {
                        inOvertimeLimits = false;
                        rateType = OvertimeRateType.One;
                        paidRateType = OvertimeRateType.One;
                    }
                    else if (nthHour < 11)
                    {
                        rateType = OvertimeRateType.OneAndOneThird;
                        paidRateType = OvertimeRateType.OneAndOneThird;
                    }
                    else if (nthHour <= 12)
                    {
                        rateType = OvertimeRateType.OneAndTwoThirds;
                        paidRateType = OvertimeRateType.OneAndTwoThirds;
                    }
                    break;
                case WorkdayType.WeekWorkday:
                case WorkdayType.MakeUpWorkday:
                    if (nthHour < 3)
                    {
                        rateType = OvertimeRateType.OneAndOneThird;
                        paidRateType = OvertimeRateType.OneAndOneThird;
                    }
                    else if (nthHour < 5)
                    {
                        rateType = OvertimeRateType.OneAndTwoThirds;
                        paidRateType = OvertimeRateType.OneAndTwoThirds;
                    }
                    break;
                case WorkdayType.SaturdayRestday:
                case WorkdayType.FlexbleHoliday:
                case WorkdayType.SaturdayHoliday:
                case WorkdayType.SaturdayNaturalDisasterDay:
                    if (nthHour < 3)
                    {
                        rateType = OvertimeRateType.OneAndOneThird;
                        paidRateType = OvertimeRateType.OneAndOneThird;
                    }
                    else if (nthHour < 9)
                    {
                        rateType = OvertimeRateType.OneAndTwoThirds;
                        paidRateType = OvertimeRateType.OneAndTwoThirds;
                    }
                    else if (nthHour <= 12)
                    {
                        rateType = OvertimeRateType.TwoAndTwoThirds;
                        paidRateType = OvertimeRateType.TwoAndTwoThirds;
                    }
                    break;
            }

            // 補休假一律傳回 4 ， OvertimeType.ReservedLeave 
            if (overtimeType == OvertimeType.ReservedLeave)
            {
                rateType = OvertimeRateType.Compensatory;
            }
            OvertimeHour overtimeHour = new OvertimeHour();
            overtimeHour.NumberOfHours = nthHour;
            overtimeHour.RateType = rateType;
            overtimeHour.PaidRateType = paidRateType;
            overtimeHour.InOvertime = inOvertimeLimits;
            overtimeHour.OvertimeType = overtimeType;

            overtimeHour.Rate = CalculateRateByRateType(paidRateType);
            overtimeHour.PaidHour = overtimeHour.Rate;
            return overtimeHour;
        }

        /// <summary>
        ///   <para>
        /// 取得 快取策略</para>
        /// </summary>
        /// <value>快取策略</value>
        private static CacheItemPolicy CachePolicy
        {
            get
            {
                CacheItemPolicy policy = new CacheItemPolicy();
                policy.AbsoluteExpiration = DateTimeOffset.Now.AddSeconds(3.0);
                return policy;
            }
        }

        /// <summary>
        /// B1Card 計算並轉換為 DayOvertime 物件
        /// </summary>
        /// <param name="b1Card"></param>
        /// <returns></returns>
        public DayOvertime B1CardToDayOvertime(B1Card b1Card)
        {
            DayOvertime dayOvertime = new DayOvertime();
            if (b1Card.Details.Count > 0)
            {
                // 轉換為OvertimeDate，計算倍率
                DateTime date = b1Card.Details[0].StartTime;
                dayOvertime.OvertimeDate = new DateTime(date.Year, date.Month, date.Day);

                foreach (B1CardDetail detail in b1Card.Details)
                {
                    OvertimeRecord overtime = new OvertimeRecord();
                    overtime.OvertimeType = (OvertimeType)detail.B1_CODE;
                    overtime.Order = detail.SerialNo;
                    overtime.ProjectNumber = detail.Project;
                    overtime.StartTime = detail.StartTime;
                    overtime.EndTime = detail.EndTime;
                    dayOvertime.OvertimeRecords.Add(overtime);
                }
                dayOvertime = CalculateDayOvertimeRate(dayOvertime);
                b1Card.TotalHours = dayOvertime.TotalHours;
                b1Card.InOvertimeHours = dayOvertime.InOvertimeHours;
            }
            return dayOvertime;
        }

        /// <summary>
        /// 計算某日加班倍率，此處不計算是否超過每日上限
        /// </summary>
        /// <param name="overtime"></param>
        /// <returns></returns>
        public DayOvertime CalculateDayOvertimeRate(DayOvertime overtime)
        {
            int hours = 0;
            overtime.PaidHours = 0.0;
            overtime.InOvertimeHours = 0;
            overtime.HourDetails = new List<OvertimeHour>();

            //先找出加班日的類型
            overtime.WorkdayType = _workdayBo.GetDayType(overtime.OvertimeDate);
            int order = 0; // 紀錄的順序
            foreach (OvertimeRecord record in overtime.OvertimeRecords)
            {
                order++;
                record.Order = order;
            }

            foreach (OvertimeRecord record in overtime.OvertimeRecords)
            {
                // 行政處要求補休必須最後算，先跳過補休
                if (record.OvertimeType != OvertimeType.ReservedLeave)
                {
                    hours = GetHoursDetail(overtime, hours, record);
                }
            }

            foreach (OvertimeRecord record in overtime.OvertimeRecords)
            {
                // 行政處要求補休必須最後算，在此補算
                if (record.OvertimeType == OvertimeType.ReservedLeave)
                {
                    hours = GetHoursDetail(overtime, hours, record);
                }
            }

            // 實際加班時數
            overtime.TotalHours = hours;

            // 計算 InOvertimeLimits 加班時數，以及 NumberOfHours
            int nTh = 0;
            foreach (OvertimeHour overtimeHour in overtime.HourDetails)
            {
                nTh++;
                overtimeHour.NumberOfHours = nTh;
                overtime.PaidHours += overtimeHour.PaidHour;
                if (overtimeHour.InOvertime)
                {
                    overtime.InOvertimeHours++;
                }
            }

            // 計算分類型不同倍率加班時數
            foreach (OvertimeRecord record in overtime.OvertimeRecords)
            {
                record.Hours = 0;
                record.InOvertimeHours = 0;
                record.PaidHours = 0.0;
                foreach (OvertimeHour overtimeHour in overtime.HourDetails)
                {
                    if (overtimeHour.OvertimeType == record.OvertimeType &&
                            overtimeHour.TypeOrder == record.Order)
                    {
                        record.Hours++;
                        record.PaidHours += overtimeHour.PaidHour;
                        record.HourDetails.Add(overtimeHour);
                        if (overtimeHour.InOvertime)
                        {
                            record.InOvertimeHours++;
                        }
                    }
                }

                // 計算 RateDetails
                foreach (OvertimeRateType rateType in (OvertimeRateType[])Enum.GetValues(typeof(OvertimeRateType)))
                {
                    OvertimeRecordRate recordRate = new OvertimeRecordRate();
                    recordRate.RateType = rateType;

                    foreach (OvertimeHour overtimeHour in record.HourDetails)
                    {
                        if (overtimeHour.RateType == rateType &&
                        overtimeHour.TypeOrder == record.Order)
                        {
                            recordRate.DetailHours.Add(overtimeHour);
                            recordRate.InOvertime = overtimeHour.InOvertime;
                            recordRate.PaidHours += overtimeHour.PaidHour;
                            if (overtimeHour.InOvertime)
                            {
                                recordRate.InOvertimeLimitHours++;
                            }
                        }
                    }

                    if (recordRate.DetailHours.Count > 0)
                    {
                        record.RateDetails.Add(recordRate);
                    }
                }
            }
            return overtime;
        }

        /// <summary>
        /// 計算實際Rate數值 1又1/3、1又1/6等
        /// </summary>
        /// <param name="overtimeRateType"></param>
        /// <returns></returns>
        public double CalculateRateByRateType(OvertimeRateType overtimeRateType)
        {
            double rate = 0.0;
            switch (overtimeRateType)
            {
                case OvertimeRateType.One:
                    rate = 1.0;
                    break;
                case OvertimeRateType.OneAndOneThird:
                    rate = 4.0 / 3.0;
                    break;
                case OvertimeRateType.OneAndTwoThirds:
                    rate = 5.0 / 3.0;
                    break;
                case OvertimeRateType.TwoAndTwoThirds:
                    rate = 8.0 / 3.0;
                    break;
            }
            return rate;
        }

        /// <summary>
        /// DayOvertime 物件轉為 B1Rate 與 B1Rate_CompHol
        /// </summary>
        /// <param name="overtime"></param>
        /// <param name="b1CardDto">加班卡DTO</param>
        /// <returns>B1Rate, B1Rate_CompHol</returns>
        public (DataTable, DataTable) DayOvertime2B1Rate(DayOvertime overtime, B1CardDto b1CardDto)
        {
            int compHourOrder = 0;
            List<B1Rate> b1Rates = new List<B1Rate>();
            List<B1Rate_CompHol> b1RateComps = new List<B1Rate_CompHol>();

            foreach (OvertimeHour overtimeHour in overtime.HourDetails)
            {
                B1Rate b1Rate = GetB1RateFromHourDetails(b1Rates, overtimeHour.ProjectNumber, overtimeHour.RateType, b1CardDto);
                b1Rate.B1_HOURS++;
                b1Rate.IsOvertime = overtimeHour.InOvertime;
                if (overtimeHour.OvertimeType == OvertimeType.ReservedLeave)
                {
                    compHourOrder++;
                    B1Rate_CompHol b1Rate_Comp = new B1Rate_CompHol();
                    b1Rate_Comp.B1_EMPNO = b1CardDto.B1_EMPNO;
                    b1Rate_Comp.B1_PROJNO = overtimeHour.ProjectNumber;
                    b1Rate_Comp.B1_HOURS = 1;
                    b1Rate_Comp.B1_RATE = (int)overtimeHour.PaidRateType;
                    b1Rate_Comp.IsOvertime = overtimeHour.InOvertime;
                    b1Rate_Comp.B1_YYMMDD = $"{b1CardDto.B1_YYMM}{b1CardDto.B1_SDD}";
                    b1Rate_Comp.B1_SHEETNO = b1CardDto.B1_SHEETNO;
                    b1Rate_Comp.B1_DayHourOrder = compHourOrder;
                    b1Rate_Comp.B1_SOURCE = b1CardDto.B1_SOURCE;
                    b1Rate_Comp.UpdatedEmpNo = b1CardDto.UpdatedEmpNo;
                    b1Rate_Comp.UpdatedName = b1CardDto.UpdatedName;
                    b1Rate_Comp.UpdatedTime = b1CardDto.UpdatedTime;
                    b1Rate_Comp.UpdatedIP = b1CardDto.UpdatedIP;
                    b1Rate_Comp.UpdatedHost = b1CardDto.UpdatedHost;
                    b1RateComps.Add(b1Rate_Comp);
                }
            }

            DataTable dtB1Rate = SqlHelper.CreateDataTable<B1Rate>(b1Rates);
            dtB1Rate.TableName = "B1RATE";
            DataTable dtB1Rate_CompHol = SqlHelper.CreateDataTable<B1Rate_CompHol>(b1RateComps);
            dtB1Rate_CompHol.TableName = "B1RATE_CompHol";
            return (dtB1Rate, dtB1Rate_CompHol);
        }


        /// <summary>
        /// DayOvertime 物件計算並轉為 B1Rate 與 B1Rate_CompHol 資料表
        /// </summary>
        /// <param name="overtime"></param>
        /// <param name="b1Card"></param>
        /// <returns>B1Rate, B1Rate_CompHol</returns>
        public (DataTable, DataTable) DayOvertimeToB1Rate(DayOvertime overtime, B1Card b1Card)
        {
            int compHourOrder = 0;
            List<B1Rate> b1Rates = new List<B1Rate>();
            List<B1Rate_CompHol> b1RateComps = new List<B1Rate_CompHol>();
            foreach (OvertimeHour overtimeHour in overtime.HourDetails)
            {
                B1Rate b1Rate = GetB1RateFromHourDetails(b1Rates, overtimeHour.ProjectNumber, overtimeHour.RateType, b1Card);
                b1Rate.B1_HOURS++;
                b1Rate.IsOvertime = overtimeHour.InOvertime;
                if (overtimeHour.OvertimeType == OvertimeType.ReservedLeave)
                {
                    compHourOrder++;
                    B1Rate_CompHol b1Rate_Comp = new B1Rate_CompHol();
                    b1Rate_Comp.B1_EMPNO = b1Card.EmpNo;
                    b1Rate_Comp.B1_PROJNO = overtimeHour.ProjectNumber;
                    b1Rate_Comp.B1_HOURS = 1;
                    b1Rate_Comp.B1_RATE = (int)overtimeHour.PaidRateType;
                    b1Rate_Comp.IsOvertime = overtimeHour.InOvertime;
                    b1Rate_Comp.B1_YYMMDD = CardUtility.RocChineseYYYMMDD(overtime.OvertimeDate);
                    b1Rate_Comp.B1_SHEETNO = b1Card.SheetNo;
                    b1Rate_Comp.B1_DayHourOrder = compHourOrder;
                    b1Rate_Comp.B1_SOURCE = b1Card.Source;
                    b1Rate_Comp.UpdatedEmpNo = b1Card.UpdatedEmpNo;
                    b1Rate_Comp.UpdatedName = b1Card.UpdatedName;
                    b1Rate_Comp.UpdatedTime = b1Card.UpdatedTime;
                    b1Rate_Comp.UpdatedIP = b1Card.UpdatedIP;
                    b1Rate_Comp.UpdatedHost = b1Card.UpdatedHost;
                    b1RateComps.Add(b1Rate_Comp);
                }
            }

            DataTable dtB1Rate = SqlHelper.CreateDataTable<B1Rate>(b1Rates);
            dtB1Rate.TableName = "B1RATE";
            DataTable dtB1Rate_CompHol = SqlHelper.CreateDataTable<B1Rate_CompHol>(b1RateComps);
            dtB1Rate_CompHol.TableName = "B1RATE_CompHol";
            return (dtB1Rate, dtB1Rate_CompHol);
        }

        /// <summary>
        /// 讀取員工指定月(依據查詢日期 ) 已填報有效(簽核中與同意)且為加班上限範圍內之加班總時數
        /// </summary>
        /// <param name="theDate">查詢日期.</param>
        /// <param name="empNo">員工編號.</param>
        public int GetMonthEmployeeOvertimeHours(DateTime theDate, string empNo)
        {
            int hours = _attendanceDao.GetMonthEmployeeOvertimeHours(theDate, empNo);
            return hours;
        }


        /// <summary>
        /// 讀取員工指定日期加班與出勤資料
        /// </summary>
        /// <param name="date">The date.</param>
        /// <param name="empNo">The employee number.</param>
        public OvertimeData GetOvertimeData(DateTime date, string empNo)
        {
            string cacheName = $"{date.ToString("yyyyMMdd")}{empNo}";
            if (_cache.Contains(cacheName))
            {
                return (OvertimeData)_cache[cacheName];
            }
            OvertimeData overtimeData = new OvertimeData();
            overtimeData.InTime = _attendanceBo.GetDayInTime(date, empNo);
            overtimeData.InTimeString = _attendanceBo.GetDayInTimeString(date, empNo);
            overtimeData.DayDetail = _workdayBo.GetWorkday(date);
            overtimeData.B1Card = _attendanceBo.GetValidB1Card(date, empNo);
            overtimeData.B1CardApp = _attendanceBo.GetValidB1CardApp(date, empNo);
            overtimeData.HoursLowerBound = _workdayBo.GetMinOvertimeHours(date);
            overtimeData.HoursUpperBound = _workdayBo.GetMaxOvertimeHours(date);
            double allowableMonthWeightedOvertimeHour;
            double currentMonthWeightedOvertimeHour;
            overtimeData.IsSpecialStaff = _attendanceBo.IsSpecialStaff(empNo, date,
                out allowableMonthWeightedOvertimeHour, out currentMonthWeightedOvertimeHour);
            overtimeData.SpecialStaffAllowedMonthWeightedOvertimeHours = allowableMonthWeightedOvertimeHour;
            overtimeData.SpecialStaffCurrentMonthWeightedOvertimeHours = currentMonthWeightedOvertimeHour;
            overtimeData.IsDriver = _employeeBo.IsDriver(empNo);

            if (overtimeData.IsSpecialStaff)
            {
                overtimeData.MonthOverHoursUpperBoundValue = allowableMonthWeightedOvertimeHour;
            }
            if (overtimeData.IsDriver)
            {
                overtimeData.MonthOverHoursUpperBoundValue = AttendanceParameters.DriverMonthOvertimeHoursLimit;
            }
            overtimeData.MonthOvertimeStatics = _attendanceBo.GetMonthEmployeeOvertimeStatics(date, empNo);
            overtimeData.QuarterlyOvertimeStatics = _attendanceBo.GetQuarterlyEmployeeOvertimeStatics(date, empNo);
            switch (overtimeData.DayDetail.DayType)
            {
                case WorkdayType.WeekWorkday:
                case WorkdayType.MakeUpWorkday:
                    overtimeData.OverHoursUpperBoundMessage = AttendanceParameters.AboveDayOvertimeLimitError;
                    break;
                case WorkdayType.SaturdayHoliday:
                case WorkdayType.SaturdayRestday:
                case WorkdayType.WeekHoliday:
                    overtimeData.OverHoursUpperBoundMessage = AttendanceParameters.AboveHolidayOvertimeLimitError;
                    break;
            }
            overtimeData.MonthCloseToHoursUpperBoundMessage = overtimeData.MonthCloseToHoursUpperBoundMessage
                .Replace("{hours}", overtimeData.MonthOvertimeStatics.TotalHours.ToString());
            lock (CacheLock)
            {
                _cache.Set(cacheName, overtimeData, CachePolicy);
            }
            return overtimeData;
        }

        /// <summary>
        /// Gets the paid hour.
        /// </summary>
        /// <param name="b1CardApp">The b1 card application.</param>
        /// <returns></returns>
        public double GetPaidHour(B1CardApp b1CardApp)
        {
            DayOvertime dayOvertime = new DayOvertime();
            // 轉換為OvertimeDate，計算倍率
            DateTime date = b1CardApp.B1_Date;
            dayOvertime.OvertimeDate = new DateTime(date.Year, date.Month, date.Day, 0, 0, 0, DateTimeKind.Local);
            OvertimeRecord overtime = new OvertimeRecord();
            overtime.OvertimeType = (OvertimeType)b1CardApp.B1_Code;
            overtime.StartTime = new DateTime(date.Year, date.Month, date.Day, 0, 0, 0, DateTimeKind.Local);
            overtime.EndTime = new DateTime(date.Year, date.Month, date.Day, b1CardApp.B1_Hour, 0, 0, DateTimeKind.Local);
            overtime.Order = 1;
            overtime.ProjectNumber = b1CardApp.B1_PrjNo;
            dayOvertime.OvertimeRecords.Add(overtime);
            dayOvertime = CalculateDayOvertimeRate(dayOvertime);
            double paidHour = dayOvertime.PaidHours;
            return paidHour;
        }

    }
}
