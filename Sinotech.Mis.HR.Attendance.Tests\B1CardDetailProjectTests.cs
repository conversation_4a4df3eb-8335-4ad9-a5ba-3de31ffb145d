﻿using Xunit;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Sinotech.Mis.Common;

namespace Sinotech.Mis.HR.Attendance.Common.Tests
{
    public class B1CardDetailProjectTests
    {
        [Fact]
        public void B1CardDetailProjectTest()
        {

            B1CardDetail b1CardDetail = new();
            b1CardDetail.ID = 1;
            b1CardDetail.Project = "Project";
            b1CardDetail.StartTime = DateTime.Parse("2021-09-01 08:00");
            b1CardDetail.EndTime = DateTime.Parse("2021-09-01 17:00");
            b1CardDetail.Hour = 8;
            b1CardDetail.HourLeft = 0;
            b1CardDetail.B1_CODE = 1;
            b1CardDetail.SerialNo = 1;
            B1CardDetailProject b1CardDetailProject = new B1CardDetailProject(b1CardDetail);
            b1CardDetailProject.ProjectDetail = new Project();
            Assert.Equal(b1CardDetail.ID, b1CardDetailProject.ID);
        }
    }
}