﻿using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves;
using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves.Tests
{
    public class BusinessTripLeaveTests : TestC1CardBase
    {
        public BusinessTripLeaveTests()
        {
            #region Prepare Data

            _c1Card.LeaveNumber = LeaveKindEnum.BusinessTripLeave;

            #endregion
        }

        [Fact]
        public void TestCanTakeThisLeave()
        {
            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CanTakeThisLeave();

            Assert.Equal(BusinessTripLeave.CodeOk, result.Code);
        }

        [Fact]
        public void TestExceedQuota()
        {
            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CheckOverPermittedLeaveHours();

            Assert.Equal(BusinessTripLeave.CodeOk, result.Code);
        }

        [Theory]
        [InlineData(null,           "Location",  "Reason", BusinessTripLeave.CodeProjectNumberFieldRequired)]
        [InlineData("",             "Location",  "Reason", BusinessTripLeave.CodeProjectNumberFieldRequired)]
        [InlineData(" ",            "Location",  "Reason", BusinessTripLeave.CodeProjectNumberFieldRequired)]
        [InlineData(" \t",          "Location",  "Reason", BusinessTripLeave.CodeProjectNumberFieldRequired)]
        [InlineData("ProjectNumber", null,       "Reason", BusinessTripLeave.CodeLocationFieldRequired)]
        [InlineData("ProjectNumber", "",         "Reason", BusinessTripLeave.CodeLocationFieldRequired)]
        [InlineData("ProjectNumber", " ",        "Reason", BusinessTripLeave.CodeLocationFieldRequired)]
        [InlineData("ProjectNumber", " \t",      "Reason", BusinessTripLeave.CodeLocationFieldRequired)]
        [InlineData("ProjectNumber", "Location", null,     BusinessTripLeave.CodeReasonFieldRequired)]
        [InlineData("ProjectNumber", "Location", "",       BusinessTripLeave.CodeReasonFieldRequired)]
        [InlineData("ProjectNumber", "Location", " ",      BusinessTripLeave.CodeReasonFieldRequired)]
        [InlineData("ProjectNumber", "Location", " \t",    BusinessTripLeave.CodeReasonFieldRequired)]
        [InlineData("ProjectNumber", "Location", "Reason", BusinessTripLeave.CodeOk)]
        public void TestCheckRequiredFields(string projectNumber, string location, string reason, int expectedCode)
        {
            _c1Card.ProjectNumber = projectNumber;
            _c1Card.Location = location;
            _c1Card.Reason = reason;

            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CheckRequiredFields();

            Assert.Equal(expectedCode, result.Code);
        }

        [Theory]
        [InlineData(Gender.Female, true)]
        [InlineData(Gender.Male, true)]
        public void TestGender(Gender gender, bool expected)
        {
            var result = BusinessTripLeave.IsAllowForThisGender(gender);
            Assert.Equal(expected, result);
        }
    }
}