﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.Utilities;
using System;

namespace Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers
{
    /// <summary>
    /// 出勤API
    /// </summary>
    [Authorize]
    [ApiController]
    [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
    [Route("/api/[controller]/[action]")]
    public class AttendanceController : ControllerBase
    {
        private readonly bool _useNegotiate = true;
        private readonly IAttendanceBo _attendanceBo;
        private readonly ILogger<AttendanceController> _logger;
        private readonly IOvertimeBo _overtimeBo;

        /// <summary>
        ///   <see cref="AttendanceController" /> 的建構函式</summary>
        /// <param name="attendanceBo">The attendance business object</param>
        /// <param name="overtimeBo">The overtime business object</param>
        /// <param name="configuration"></param>
        /// <param name="logger">The logger.</param>
        public AttendanceController(IAttendanceBo attendanceBo, IOvertimeBo overtimeBo,
            IConfiguration configuration, ILogger<AttendanceController> logger)
        {
            _attendanceBo = attendanceBo;
            _overtimeBo = overtimeBo;
            _useNegotiate = configuration.GetValue<bool>("UseNegotiate");
            _logger = logger;
        }

        /// <summary>
        /// 讀取員工刷卡時間(依據查詢日期 )
        /// </summary>
        /// <param name="date">查詢日期.</param>
        /// <param name="empNo">員工編號.</param>
        /// <returns>員工刷卡時間字串, e.g. "07:02, 16:31"</returns>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public string GetDayInTime(DateTime date, string empNo)
        {
            string ret = string.Empty;
            date = date.ToLocalTime();
            try
            {
                string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
                if (empNo != null && !string.IsNullOrWhiteSpace(userId))
                {
                    ret = _attendanceBo.GetDayInTimeString(date, empNo);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "/api/Attendance/GetDayInTime({Date},{EmpNo}) 發生錯誤：{Message} {StackTrace}", date, empNo, ex.Message, ex.StackTrace);
            }
            return ret;
        }


        /// <summary>
        /// 讀取員工指定月份出勤時間
        /// </summary>
        /// <param name="date">The date.</param>
        /// <param name="empNo">The userId.</param>
        /// <returns>出勤時間 JSON</returns>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public string GetMonthAttendance(DateTime date, string empNo)
        {
            string ret = "[]";
            date = date.ToLocalTime();
            try
            {
                string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
                if (empNo != null && !string.IsNullOrWhiteSpace(userId)
                    && (_attendanceBo.CanSeeAttendance(userId, empNo)))
                {
                    ret = JsonConvert.SerializeObject(_attendanceBo.GetMonthAttendance(date, empNo), Formatting.None);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "/api/Attendance/GetMonthAttendance({Date},{EmpNo}) 發生錯誤：{Message} {StackTrace}", date, empNo, ex.Message, ex.StackTrace);
            }
            return ret;
        }

        /// <summary>
        /// 讀取員工指定月份(依據查詢日期 )休假統計時數
        /// </summary>
        /// <param name="date">查詢日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>休假統計時數 JSON</returns>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public string GetMonthEmployeeLeaves(DateTime date, string empNo)
        {
            string ret = "[]";
            date = date.ToLocalTime();
            try
            {
                string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
                if (!string.IsNullOrWhiteSpace(userId)
                    && userId != string.Empty && empNo != null
                    && _attendanceBo.CanSeeAttendance(userId, empNo))
                {
                    EmployeeLeaves leave = _attendanceBo.GetMonthEmployeeLeaves(date, empNo);
                    ret = JsonConvert.SerializeObject(leave, Formatting.None);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "/api/Attendance/GetMonthEmployeeLeaves({Date},{EmpNo}) 發生錯誤：{Message} {StackTrace}", date, empNo, ex.Message, ex.StackTrace);
            }

            return ret;
        }

        /// <summary>
        /// 讀取員工指定日期加班與出勤資料
        /// </summary>
        /// <param name="date">查詢日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>加班與出勤資料 JSON</returns>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public string GetOvertimeData(DateTime date, string empNo)
        {
            string ret = "{}";
            date = date.ToLocalTime();
            try
            {
                string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
                if (empNo != null && userId != null && !string.IsNullOrWhiteSpace(userId))
                {
                    OvertimeData overtimeData = _overtimeBo.GetOvertimeData(date, empNo);
                    ret = JsonConvert.SerializeObject(overtimeData, Formatting.None);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("/api/Attendance/GetOvertimeData({date},{empNo}) 發生錯誤：{Message} {StackTrace}", date, empNo, ex.Message, ex.StackTrace);
            }
            return ret;
        }

        /// <summary>是否為出勤系統管理者</summary>
        /// <returns>出勤系統管理者傳回<c>true</c>；否則傳回<c>false</c></returns>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        // GET api/<controller>
        public bool IsAdmin()
        {
            string empNo = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            bool ret = _attendanceBo.IsAdmin(empNo);
            return ret;
        }

        /// <summary>是否為出勤系統管理者</summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>出勤系統管理者傳回<c>true</c>；否則傳回<c>false</c></returns>
        [HttpPost]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/{empNo}")]
        public bool IsAdmin(string empNo)
        {
            bool ret = _attendanceBo.IsAdmin(empNo);
            return ret;
        }

        /// <summary>是否有查詢[部門填報紀錄]的權限</summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>有權限傳回<c>true</c>；否則傳回<c>false</c></returns>
        [HttpPost]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/{empNo}")]
        public bool IsAuthorizedToQueryDepartmentSentBox(string empNo)
        {
            bool ret = _attendanceBo.IsAuthorizedToQueryDepartmentSentBox(empNo);
            return ret;
        }

        /// <summary>是否有查詢[部門填報紀錄]的權限</summary>
        /// <param name="deptNo">部門編號</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>有權限傳回<c>true</c>；否則傳回<c>false</c></returns>
        [HttpPost]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public bool IsAuthorizedToQueryDepartmentSentBoxByDeptNo(int deptNo, string empNo)
        {
            bool ret = _attendanceBo.IsAuthorizedToQueryDepartmentSentBox(deptNo, empNo);
            return ret;
        }

    }
}
