﻿using Xunit;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Sinotech.Mis.HR.Attendance.Common.Tests
{
    public class B1CardDtoTests
    {
        [Fact]
        public void B1CardDtoToB1CardTest()
        {
            List<B1CardDto> b1CardDtoList = new List<B1CardDto>();
            B1CardDto b1CardDto = new B1CardDto();
            b1CardDtoList.Add(b1CardDto);
            Assert.Throws<FormatException>(() => B1CardDto.B1CardDtoToB1Card(b1CardDtoList));
            b1CardDtoList = new List<B1CardDto>();
            b1CardDto.B1_CODE = 1;
            b1CardDto.B1_EMPNO = "0395";
            b1CardDto.B1_HOUR = 1;
            b1CardDto.B1_SHEETNO = "SHEETNO";
            b1CardDto.B1_YYMM = "11107";
            b1CardDto.B1_PROJNO = "PROJNO";
            b1CardDto.B1_SDD = "01";
            b1CardDto.B1_EDD = "01";
            b1CardDto.B1_SHH = "18";
            b1CardDto.B1_EHH = "19";
            b1CardDto.B1_SMM = "30";
            b1CardDto.B1_EMM = "30";
            b1CardDto.B1_SERIALNO = "1";
            b1CardDto.B1_Reason = "Reason";
            b1CardDtoList.Add(b1CardDto);
            B1Card b1Card = B1CardDto.B1CardDtoToB1Card(b1CardDtoList);
            Assert.NotNull(b1Card);
        }
    }
}