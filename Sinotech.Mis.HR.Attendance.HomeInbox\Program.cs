﻿using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Sinotech.Mis.HR.Attendance.Extensions.ServiceCollection;
using System.Diagnostics.CodeAnalysis;
namespace Sinotech.Mis.HR.Attendance.AttendanceCard
{
    [ExcludeFromCodeCoverage]
    public static class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);
            IConfiguration Configuration = builder.Configuration;
            // Add services to the container.
            string MyAllowSpecificOrigins = "_myAllowSpecificOrigins";
            builder.Services.AddCors(options =>
            {
                options.AddPolicy(name: MyAllowSpecificOrigins,
                                  policy =>
                                  {
                                      policy.WithOrigins("https://iisinc.sinotech.org.tw",
                                                         "https://attendance.sinotech.org.tw",
                                                         "http://localhost",
                                                         "https://localhost",
                                                         "http://localhost:8080",
                                                         "http://localhost:8080/SinoAttendance",
                                                         "https://localhost:3000",
                                                         "http://localhost:5173",
                                                         "https://localhost:5001")
                                                         .AllowAnyHeader()
                                                         .AllowAnyMethod();
                                  });
            });

            builder.Services.AddControllers();

            builder.Services.AddAuthorization(options =>
            {
                // By default, all incoming requests will be authorized according to the default policy.
                options.FallbackPolicy = options.DefaultPolicy;
            });

            builder.Services.AddAttendanceBusinessObjects(Configuration);  // By 林志偉，2022.05.25

            var app = builder.Build();

            //加入 CORS 支援
            app.UseCors(MyAllowSpecificOrigins);

            app.MapControllers();

            app.Run();
        }
    }
}
