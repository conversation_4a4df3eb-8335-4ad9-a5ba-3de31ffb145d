import { useRouter } from 'vue-router'
import { useAuthUserStore } from '../store/index'
import type { ToastServiceMethods } from 'primevue/toastservice'

export function useAbortController() {
  const userStore = useAuthUserStore()
  const abortController = new AbortController()
  const router = useRouter()
  
  const abortListener = (): void => abortController.signal.addEventListener('abort', () => {
    if (abortController.signal.reason.message === '401') {
      userStore.$reset()
      router.push({
        name: 'Login'
      })
    }
  })

  const fetchErrorHandler = (err: unknown, toast: ToastServiceMethods, toastMessage: string, toastGroup: string): boolean => {
    if ((err as Error).message === '401') {
      abortController.abort(err)
      return true
    } else {
      toast.add({
        severity: 'error',
        summary: toastMessage,
        group: toastGroup
      })
      return false
    }
  }

  return { abortController, abort<PERSON><PERSON>ener, fetchError<PERSON><PERSON><PERSON> }
}