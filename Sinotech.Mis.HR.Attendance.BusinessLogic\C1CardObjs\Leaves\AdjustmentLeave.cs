﻿using Sinotech.Mis.HR.Attendance.Common;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves
{
    [LeaveKind(LeaveKindEnum.AdjustmentLeave)]
    public class AdjustmentLeave : C1CardBypassAnyCheck
    {
        /// <summary>
        /// 調整工時假建構子
        /// </summary>
        /// <param name="c1Card"></param>
        /// <param name="c1CardBo"></param>
        public AdjustmentLeave(C1Card c1Card, IC1CardBo c1CardBo) : base(c1Card, c1CardBo)
        {
        }

    }
}
