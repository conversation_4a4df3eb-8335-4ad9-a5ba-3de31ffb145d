﻿using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Collections.Generic;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves
{
    /// <summary>
    /// 喪假類別
    /// </summary>
    [LeaveKind(LeaveKindEnum.FuneralLeave)]
    public class FuneralLeave : LeaveWithEvent
    {

        internal readonly CardCheckResult OverApplyCheckResult = new CardCheckResult(3011302, CardStatusEnum.Error,
                            // $"您已申請過此假別細項，不得重複申請"
                            AttendanceParameters.FuneralLeaveApplyRepeatly);

        /// <summary>
        /// 建構子
        /// </summary>
        /// <param name="c1Card"></param>
        /// <param name="c1CardBo"></param>
        public FuneralLeave(C1Card c1Card, IC1CardBo c1CardBo) : base(c1Card, c1CardBo)
        {
        }

        /// <summary>
        /// 檢查是否能請此假
        /// </summary>
        /// <returns></returns>
        internal override CardCheckResult CanTakeThisLeave()
        {
            return ResultOk;
        }

        /// <summary>
        /// 檢查剩餘可休  
        /// </summary>
        /// <returns></returns>
        internal override CardCheckResult CheckRemainHours()
        {
            CardCheckResult result = ResultOk;

            if (_c1Card.EventDate != null && _c1Card.EventDate != DateTime.MinValue && _c1Card.EventDate != DateTime.MaxValue)
            {
                //若本次請假超過可請，且無相關單號
                if (string.IsNullOrWhiteSpace(_c1Card.RelatedFormNumber) && TotalHours > PermittedHours)
                {
                    result = GenerateOverLeaveDaysError(PermittedDays,
                        // $"您本次請假{Days}天，此事件可休{restDay}天，不得超假"
                        3011301, AttendanceParameters.FuneralLeaveOneTimeOverPermittedDays);
                    return result;
                }
            }
            else
            {
                result = ResultEventDateFieldRequired;
            }

            // 若有相關單號
            if (!string.IsNullOrWhiteSpace(_c1Card.RelatedFormNumber))
            {
                int usedHours = 0;
                List<EventRelationRecord> records = _c1CardBo.GetEventRelatedRecord(_c1Card.RelatedFormNumber);
                // 錯誤判斷
                foreach (var record in records)
                {
                    usedHours += record.LeaveHour;
                }
                int remainHours = PermittedHours - usedHours;
                //若請假超過剩餘可休
                if (TotalHours > remainHours)
                {
                    double remainDays = remainHours * 1.0 / AttendanceParameters.DefaultWorkingHours;
                    result = GenerateOverLeaveDaysError(remainDays,
                        // $"您本次請假{Days}天，此事件剩餘可休{restDay}天，不得超假"
                        3011302, AttendanceParameters.FuneralLeaveOverAvailableDays);
                    return result;
                }
            }
            else // 無相關單號
            {
                // 檢查一生僅允許請一次事件
                switch (_c1Card.LeaveSubNumber)
                {
                    case 1:  // 父
                    case 2:  // 母
                    case 3:  // 養父
                    case 4:  // 養母
                    case 21: // 祖父
                    case 22: // 祖母
                    case 23: // 外祖父
                    case 24: // 外祖母
                    case 27: // 配偶之祖父
                    case 28: // 配偶之祖母
                    case 29: // 配偶之父
                    case 30: // 配偶之母
                    case 31: // 配偶之養父
                    case 32: // 配偶之養母
                    case 35: // 配偶之外祖父
                    case 36: // 配偶之外祖母
                             //List<C1Card> records = _c1CardBo.GetC1CardByLeaveKind(_c1Card.EmpNo, _c1Card.LeaveNumber, _c1Card.LeaveSubNumber);
                             //if (records.Count > 0 && _c1Card.EventDate != null)
                             //{
                             // 讀取DB，找出同一事件日剩餘可休
                             //List<C1Card> c1Cards = _c1CardBo.GetC1CardByEventDate(_c1Card.EmpNo, _c1Card.LeaveNumber, _c1Card.LeaveSubNumber, (DateTime)_c1Card.EventDate);
                             //if (c1Cards.Count > 0) // 有同一事件日
                             //{
                             //    int usedHours = 0;
                             //    foreach (var c1Card in c1Cards)
                             //    {
                             //        usedHours += c1Card.Hours;
                             //    }
                             //    int remainHours = PermittedHours - usedHours;
                             //    //若本次請假超過剩餘可休
                             //    if (TotalHours > remainHours)
                             //    {
                             //        double remainDays = remainHours * 1.0 / AttendanceParameters.DefaultWorkingHours;
                             //        result = GenerateOverLeaveDaysError(remainDays,
                             //            // "您本次請假{Days}天，此事件剩餘可休{remainDays}天，不得超假"
                             //            3011302, AttendanceParameters.FuneralLeaveOverAvailableDays);
                             //        return result;
                             //    }
                             //}
                             //else // 無同一事件日
                             //{
                             //    result = OverApplyCheckResult;
                             //    return result;
                             //}
                             //}

                        // 讀取DB，找出同一假別，因為無相關單號，有就錯了
                        List<C1Card> records = _c1CardBo.GetC1CardByLeaveKind(_c1Card.EmpNo, _c1Card.LeaveNumber, _c1Card.LeaveSubNumber);
                        if (records.Count > 0)
                        {
                            result = OverApplyCheckResult;
                            return result;
                        }
                        break;
                }
            }
            return result;
        }

        /// <summary>
        /// 計算最早可請假日期與最晚可請假日期
        /// </summary>
        /// <param name="date">請假日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public override (DateTime, DateTime) CalculateLeavePermittedPeriod(DateTime date, string empNo)
        {
            if (_c1Card.EventDate != null && _c1Card.EventDate != DateTime.MinValue && _c1Card.EventDate != DateTime.MaxValue) // 事件發生日
            {
                date = (DateTime)_c1Card.EventDate;
            }
            DateTime startDate = date.Date;
            DateTime firstDay = new DateTime(startDate.Year, startDate.Month, 1, 0, 0, 0, DateTimeKind.Local);
            DateTime endDate = firstDay.AddMonths(4).AddSeconds(-1);
            return (startDate, endDate);
        }

        /// <summary>
        /// 本假別細項可休假天數
        /// </summary>
        /// <returns></returns>
        public int GetPermittedDays(int leaveSubNumber)
        {
            int days = 0;
            // 改為從DB讀取值為主，由上層傳入 C1Card.PermittedHours
            if (_c1Card.LeaveMaximum != null)
            {
                days = (int)_c1Card.LeaveMaximum;
            }
            else
            {
                switch (leaveSubNumber)
                {
                    case 1: // 父
                    case 2: // 母
                    case 3: // 養父
                    case 4: // 養母
                    case 5: // 繼父
                    case 6: // 繼母
                    case 7: // 配偶
                            // 8工作天
                        days = 8;
                        break;
                    case 21: // 祖父
                    case 22: // 祖母
                    case 23: // 外祖父
                    case 24:  // 外祖母
                    case 25: // 子
                    case 26: // 女
                    case 29: // 配偶之父
                    case 30: // 配偶之母
                    case 31: // 配偶之養父
                    case 32: // 配偶之養母
                    case 33: // 配偶之繼父
                    case 34: // 配偶之繼母
                        days = 6;
                        break;
                    case 27: // 配偶之祖父
                    case 28: // 配偶之祖母
                    case 35: // 配偶之外祖父
                    case 36: // 配偶之外祖母
                    case 51: // 兄
                    case 52: // 弟
                    case 53: // 姊
                    case 54: // 妹
                    case 55: // 曾祖父母
                        days = 3;
                        break;
                }
            }
            return days;
        }

        /// <summary>
        /// 此性別是否可請此假別
        /// </summary>
        /// <param name="gender"></param>
        /// <returns></returns>
        public static bool IsAllowForThisGender(Gender gender)
        {
            return true;
        }

    }
}
