﻿using Sinotech.Mis.Helpers;
using Sinotech.Mis.Utilities.DataAccess.Interfaces;
using System.Data;
using System.Data.SqlClient;

namespace Sinotech.Mis.Utilities.DataAccess.Ado
{
    /// <summary>
    /// 部門資料存取元件
    /// </summary>
    /// <seealso cref="Sinotech.Mis.Utilities.DataAccess.Interfaces.IDepartmentDao" />
    public class DepartmentDao : IDepartmentDao
    {

        private readonly string _connectionString;

        public DepartmentDao(string connectionString)
        {
            _connectionString = connectionString;
        }

        /// <summary>
        /// 取得副理以上資料表
        /// </summary>
        /// <returns></returns>
        public DataTable GetAboveDeputyManagerDataTable()
        {
            string sql = "SELECT DeptNo,DeptSName,JobNo,JobName,EmpNo,CName,IsActive,Email FROM vwDeptManager;";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql);
            return dt;
        }

        /// <summary>
        /// 取得部門內所有員工
        /// </summary>
        /// <param name="departmentNumber">部門編號</param>
        /// <returns>部門內所有員工</returns>
        public DataTable GetDepartmentEmployees(int departmentNumber)
        {
            string sql = "SELECT EmpNo, CName, DeptNo, DeptSName, Status, Email, TelExtension, SpecLine, Sex, JobNo, JobName FROM vwCard_Emp WHERE DeptNo=@DeptNo AND Status=N'0' ORDER BY EmpNo;";
            SqlParameter sqlParameter = new SqlParameter("@DeptNo", SqlDbType.Int);
            sqlParameter.Value = departmentNumber;
            return SqlHelper.GetDataTable(_connectionString, sql, sqlParameter);
        }

        public string GetDepartmentName(int departmentNumber)
        {
            string sRet = "";
            string sql = "SELECT DeptName FROM vwDeptData WHERE DeptNo=@DeptNo;";
            SqlParameter sqlParameter = new SqlParameter("@DeptNo", SqlDbType.Int);
            sqlParameter.Value = departmentNumber;
            var ret = SqlHelper.GetFieldValue(_connectionString, sql, sqlParameter);
            if (ret != null)
            {
                sRet = (string)ret;
            }
            return sRet;
        }

        /// <summary>
        /// 取得所有部門
        /// </summary>
        /// <returns></returns>
        public DataTable GetDepartments()
        {
            string sql = @"SELECT DeptNo, DeptESSName, DeptESName, DeptSSName, DeptSName, DeptName, 
            DeptEName, StNo FROM vwDeptData WHERE StNo=1;";
            return SqlHelper.GetDataTable(_connectionString, sql);
        }

        public string GetDepartmentShortName(int departmentNumber)
        {
            string sRet = "";
            string sql = "SELECT DeptSName FROM vwDeptData WHERE DeptNo=@DeptNo;";
            SqlParameter sqlParameter = new SqlParameter("@DeptNo", SqlDbType.Int);
            sqlParameter.Value = departmentNumber;
            var ret = SqlHelper.GetFieldValue(_connectionString, sql, sqlParameter);
            if (ret != null)
            {
                sRet = (string)ret;
            }
            return sRet;
        }

        /// <summary>
        /// 取得部門主管<br />
        /// 蔣明峰提供各部門主管判斷邏輯，先依照Left(JobNo,2) DESC再依 Right(JobNo,1) ASC
        /// </summary>
        /// <param name="departmentNumber">部門編號</param>
        /// <returns>部門主管</returns>
        public DataTable GetDepartmentsManager(int departmentNumber)
        {
            string sql = $"SELECT Top 1 Right(JobNo, 1) AS orderNo,DeptNo,DeptSName,JobNo,Left(JobNo,2) AS RankNo,JobName,EmpNo,CName FROM vwDeptManager WHERE DeptNo={departmentNumber} ORDER BY DeptNo ASC, RankNo Desc, orderNo ASC;";
            return SqlHelper.GetDataTable(_connectionString, sql);
        }

        /// <summary>
        /// 取得各部門各級主管
        /// </summary>
        /// <returns>各部門各級主管</returns>
        public DataTable GetDepartmentsManagers()
        {
            string sql = $"SELECT Right(JobNo, 1) AS orderNo,DeptNo,DeptSName,JobNo,Left(JobNo,2) AS RankNo,JobName,EmpNo,CName FROM vwDeptManager ORDER BY DeptNo ASC, RankNo Desc, orderNo ASC;";
            return SqlHelper.GetDataTable(_connectionString, sql);
        }

        /// <summary>
        /// 取得部門各級主管
        /// </summary>
        /// <param name="departmentNumber">部門編號</param>
        /// <returns>部門各級主管</returns>
        public DataTable GetDepartmentsManagers(int departmentNumber)
        {
            string sql = $"SELECT Right(JobNo, 1) AS orderNo,DeptNo,DeptSName,JobNo,Left(JobNo,2) AS RankNo,JobName,EmpNo,CName FROM vwDeptManager WHERE DeptNo={departmentNumber} ORDER BY DeptNo ASC, RankNo Desc, orderNo ASC;";
            return SqlHelper.GetDataTable(_connectionString, sql);
        }



        /// <summary>
        /// 取得部門與組別
        /// </summary>
        /// <returns></returns>
        public DataTable GetDepartmentsTeams()
        {
            string sql = @"SELECT vwDeptTeam.DeptNo,vwDeptData.DeptSName,vwDeptTeam.TeamID, vwDeptTeam.Seq,
vwDeptTeam.TeamCName,vwDeptData.DeptName,vwDeptData.DeptEName
FROM vwDeptTeam INNER JOIN vwDeptData ON vwDeptTeam.DeptNo = vwDeptData.DeptNo
WHERE (vwDeptTeam.IsActive = 1) ORDER BY vwDeptTeam.DeptNo,vwDeptTeam.TeamID;";
            return SqlHelper.GetDataTable(_connectionString, sql);
        }

        /// <summary>
        /// 取得行政部門
        /// </summary>
        /// <returns></returns>
        public DataTable GetAdministrativedepartments()
        {
            string sql = @"SELECT DeptNo, DeptESSName, DeptESName, DeptSSName, DeptSName, DeptName, 
            DeptEName, StNo FROM vwDeptData WHERE StNo=1 AND IsResearchCenter IS NULL;";
            return SqlHelper.GetDataTable(_connectionString, sql);
        }

        /// <summary>
        /// 取得研究中心
        /// </summary>
        /// <returns></returns>
        public DataTable GetResearchCenters()
        {
            string sql = @"SELECT DeptNo, DeptESSName, DeptESName, DeptSSName, DeptSName, DeptName, 
            DeptEName, StNo FROM vwDeptData WHERE StNo=1 AND IsResearchCenter=1;";
            return SqlHelper.GetDataTable(_connectionString, sql);
        }

        /// <summary>取得部門內所有組及員工</summary>
        /// <returns></returns>
        public DataTable GetDepartmentsTeamsEmployees()
        {
            string sql = @"SELECT vwCard_Emp.DeptNo,vwCard_Emp.DeptSName,vwDeptTeamEmp.TeamID,vwDeptTeam.TeamCName, 
 vwCard_Emp.EmpNo,vwCard_Emp.CName,vwCard_Emp.RankNo,vwCard_Emp.RankName,vwCard_Emp.JobNo,JobName,vwCard_Emp.Sex,
vwCard_Emp.Birthday,vwCard_Emp.TelExtension,
 vwCard_Emp.SpecLine,vwCard_Emp.Email FROM vwCard_Emp INNER JOIN
 vwDeptTeamEmp ON vwCard_Emp.EmpNo = vwDeptTeamEmp.EmpNo INNER JOIN
 vwDeptTeam ON vwDeptTeamEmp.TeamID = vwDeptTeam.TeamID
WHERE (vwCard_Emp.Status = N'0') UNION
SELECT DeptNo,DeptSName,NULL AS TeamID,NULL AS TeamCName,EmpNo,CName,RankNo,RankName,JobNo,JobName,Sex,Birthday,
TelExtension,SpecLine,Email FROM vwCard_Emp AS vwCard_Emp_1
WHERE (Status = N'0') AND (EmpNo NOT IN (SELECT EmpNo
 FROM vwDeptTeamEmp AS vwDeptTeamEmp_1 WHERE (IsActive = 1)))
ORDER BY vwCard_Emp.DeptNo,vwDeptTeamEmp.TeamID,vwCard_Emp.EmpNo;";
            return SqlHelper.GetDataTable(_connectionString, sql);
        }

        public DataTable GetDepartmentTeams(int departmentNumber)
        {
            string sql = @"SELECT vwDeptTeam.DeptNo,vwDeptData.DeptSName,vwDeptTeam.TeamID, vwDeptTeam.Seq,
vwDeptTeam.TeamCName,vwDeptData.DeptName,vwDeptData.DeptEName
FROM vwDeptTeam INNER JOIN vwDeptData ON vwDeptTeam.DeptNo = vwDeptData.DeptNo
WHERE (vwDeptTeam.IsActive = 1) AND (vwDeptTeam.DeptNo=@DeptNo)  ORDER BY vwDeptTeam.DeptNo,vwDeptTeam.TeamID;";
            SqlParameter parameter = new SqlParameter("@DeptNo", SqlDbType.Int);
            parameter.Value = departmentNumber;
            return SqlHelper.GetDataTable(_connectionString, sql, parameter);
        }

        /// <summary>
        /// Retrieves the employees of a department's teams.
        /// </summary>
        /// <param name="departmentNumber">The department number.</param>
        /// <returns>A DataTable containing the employees of the department's teams.</returns>
        public DataTable GetDepartmentTeamsEmployees(int departmentNumber)
        {
            string sql = @"SELECT vwCard_Emp.DeptNo,vwCard_Emp.DeptSName,vwDeptTeamEmp.TeamID,vwDeptTeam.TeamCName,
 vwCard_Emp.EmpNo,vwCard_Emp.CName,vwCard_Emp.RankNo,vwCard_Emp.RankName,vwCard_Emp.JobNo,
 vwCard_Emp.JobName,vwCard_Emp.Sex,vwCard_Emp.Birthday,vwCard_Emp.TelExtension,
 vwCard_Emp.SpecLine,vwCard_Emp.Email FROM vwCard_Emp INNER JOIN
 vwDeptTeamEmp ON vwCard_Emp.EmpNo = vwDeptTeamEmp.EmpNo INNER JOIN
 vwDeptTeam ON vwDeptTeamEmp.TeamID = vwDeptTeam.TeamID
WHERE (vwCard_Emp.Status = N'0') AND vwCard_Emp.DeptNo=@DeptNO UNION
SELECT DeptNo,DeptSName,NULL AS TeamID,NULL AS TeamCName,EmpNo,CName,RankNo,RankName,JobNo,
JobName,Sex,Birthday,TelExtension,SpecLine,Email FROM vwCard_Emp AS vwCard_Emp_1
WHERE (Status = N'0') AND (EmpNo NOT IN (SELECT EmpNo
 FROM vwDeptTeamEmp AS vwDeptTeamEmp_1 WHERE (IsActive = 1))) AND DeptNo=@DeptNo
ORDER BY vwCard_Emp.DeptNo,vwDeptTeamEmp.TeamID,vwCard_Emp.EmpNo;";
            SqlParameter parameter = new SqlParameter("@DeptNo", SqlDbType.Int);
            parameter.Value = departmentNumber;
            return SqlHelper.GetDataTable(_connectionString, sql, parameter);
        }

        /// <summary>取得所有現役組長</summary>
        /// <returns>
        ///   <br />
        /// </returns>
        public DataTable GetTeamLeaders()
        {
            string sql = @"SELECT DeptNo, DeptSName, TeamID, TeamCName, JobNo, JobName, TLEmpNo AS EmpNo, CName, 
                         StartDate, EndDate, IsActive FROM vwDeptTeamLeader WHERE (IsActive = 1)";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql);
            return dt;
        }

    }
}
