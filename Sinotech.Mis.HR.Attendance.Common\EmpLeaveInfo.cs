﻿namespace Sinotech.Mis.HR.Attendance.Common
{
    public class EmpLeaveInfo
    {
        /// <summary>
        /// 假別代號
        /// </summary>
        public int LeaveNumber { get; set; }

        /// <summary>
        /// 假別細項代號
        /// </summary>
        public int LeaveSubNumber { get; set; }

        /// <summary>
        /// 假別名稱
        /// </summary>
        public string? LeaveName { get; set; } = string.Empty;

        /// <summary>
        /// 年度總可用時數
        /// </summary>
        public int YearAvailableHours { get; set; }

        /// <summary>
        /// 年度已使用時數(簽核中＋已核可)
        /// </summary>
        public int YearUsedHours { get; set; }

        /// <summary>
        /// 累至查詢月份已使用時數(簽核中＋已核可)
        /// </summary>
        public int SumToMonthUsedHours { get; set; }

        /// <summary>
        /// 查詢月份已使用時數(簽核中＋已核可)
        /// </summary>
        public int MonthUsedHours { get; set; }

        /// <summary>
        /// 年度已核可時數
        /// </summary>
        public int YearApprovedHours { get; set; }

        /// <summary>
        /// 累至查詢月份已核可時數
        /// </summary>
        public int SumToMonthApprovedHours { get; set; }

        /// <summary>
        /// 查詢月份已核可時數
        /// </summary>
        public int MonthApprovedHours { get; set; }
    }
}
