using FakeItEasy;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.C1CardObjs;
using System.Collections.Generic;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves.Tests
{
    public class DisasterRecoveryLeaveTests : TestC1CardBase
    {
        public DisasterRecoveryLeaveTests()
        {
            // 設定假別為防災復原假
            _c1Card.LeaveNumber = LeaveKindEnum.DisasterRecoveryLeave;
        }

        [Fact]
        public void CheckRequiredFields_ReasonIsRequired()
        {
            // Arrange
            _c1Card.Reason = "   "; // 空白事由
            _c1Card.UploadedFiles = new List<UploadedFile> { new UploadedFile { FileName = "proof.pdf" } }; // 附件存在以避免先被附件攔下

            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);

            // Act
            var result = card.CheckRequiredFields();

            // Assert
            Assert.Equal(DisasterRecoveryLeave.CodeReasonFieldRequired, result.Code);
            Assert.Equal(CardStatusEnum.Error, result.Status);
            Assert.Equal(AttendanceParameters.LeaveMustHaveReason, result.Message);
        }

        [Fact]
        public void CheckRequiredFields_AttachmentIsRequired()
        {
            // Arrange
            _c1Card.Reason = "因天然災害復原處理"; // 有事由
            _c1Card.UploadedFiles = new List<UploadedFile>(); // 無附件

            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);

            // Act
            var result = card.CheckRequiredFields();

            // Assert
            Assert.Equal(DisasterRecoveryLeave.CodeAttachmentRequired, result.Code);
            Assert.Equal(CardStatusEnum.Error, result.Status);
            Assert.False(string.IsNullOrWhiteSpace(result.Message));
        }

        [Fact]
        public void CheckRequiredFields_Ok_WhenReasonAndAttachmentProvided()
        {
            // Arrange
            _c1Card.Reason = "災後復原作業";
            _c1Card.UploadedFiles = new List<UploadedFile> { new UploadedFile { FileName = "evidence.pdf" } };

            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);

            // Act
            var result = card.CheckRequiredFields();

            // Assert
            Assert.Equal(C1CardBase.CodeOk, result.Code);
        }

        [Theory]
        [InlineData(Gender.Female, true)]
        [InlineData(Gender.Male, true)]
        public void TestGender(Gender gender, bool expected)
        {
            var result = C1CardBase.IsAllowForThisGender(_c1Card.LeaveNumber, gender);
            Assert.Equal(expected, result);
        }

        [Fact]
        public void CanTakeThisLeave_Ok()
        {
            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CanTakeThisLeave();
            Assert.Equal(C1CardBase.CodeOk, result.Code);
        }

        [Fact]
        public void CheckOverPermittedLeaveHours_Ok()
        {
            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CheckOverPermittedLeaveHours();
            Assert.Equal(C1CardBase.CodeOk, result.Code);
        }
    }
}
