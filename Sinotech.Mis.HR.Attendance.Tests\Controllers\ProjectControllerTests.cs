﻿using Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sinotech.Mis.HR.Attendance.BusinessLogic;
using System;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using Xunit;
using Sinotech.Mis.Common;

namespace Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers.Tests
{
    [ExcludeFromCodeCoverage]
    public class ProjectControllerTests
    {
        private readonly ProjectBo _projectBo;
        private readonly ProjectController _controller;

        public ProjectControllerTests(ProjectBo projectBo, ILogger<ProjectController> logger)
        {
            _projectBo = projectBo;
            IConfiguration configuration = new ConfigurationBuilder().
                AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).
                Build();
            _controller = new ProjectController(_projectBo, configuration, logger);
        }

        [Fact]
        public void GetOpenProjectsDateRange_StartDateEqualsEndDate_ReturnsArray()
        {
            // Arrange
            DateTime startDate = new DateTime(2022, 1, 1, 0, 0, 0, DateTimeKind.Local);
            DateTime endDate = startDate;
            int deptNo = 0;
            // Act
            string result = _controller.GetOpenProjectsDateRange(startDate, endDate, deptNo);

            // Assert
            DataTable dt = JsonConvert.DeserializeObject<DataTable>(result);
            Assert.NotEmpty(dt.Rows);
            Assert.True(1100 < dt.Rows.Count);
        }

        [Fact]
        public void GetOpenProjectsDateRange_StartDateEarlierThanEndDate_ReturnsProjectList()
        {
            // Arrange
            DateTime startDate = new DateTime(2022, 1, 1, 0, 0, 0, DateTimeKind.Local);
            DateTime endDate = new DateTime(2022, 1, 31, 0, 0, 0, DateTimeKind.Local);
            int deptNo = 10;

            // Act
            string result = _controller.GetOpenProjectsDateRange(startDate, endDate, deptNo);

            // Assert
            DataTable dt = JsonConvert.DeserializeObject<DataTable>(result);
            Assert.NotEmpty(dt.Rows);
            Assert.True(1100 < dt.Rows.Count);
        }

        [Fact]
        public void GetOpenProjectsDateRange_StartDateLaterThanEndDate_ReturnsEmptyArray()
        {
            // Arrange
            DateTime startDate = new DateTime(2022, 1, 31, 0, 0, 0, DateTimeKind.Local);
            DateTime endDate = new DateTime(2022, 1, 1, 0, 0, 0, DateTimeKind.Local);
            int deptNo = -1;

            // Act
            string result = _controller.GetOpenProjectsDateRange(startDate, endDate, deptNo);

            // Assert
            DataTable dt = JsonConvert.DeserializeObject<DataTable>(result);
            Assert.NotEmpty(dt.Rows);
            Assert.True(1100 < dt.Rows.Count);
        }

        [Theory]
        [InlineData("RP19553", "MIS資訊系統技術精進")]
        [InlineData("TI21016", "中興鐵道局－宜蘭羅東高架綜規")]
        [InlineData("TG22213", "大域台鐵台東隧道檢測")]
        public void GetProjectNameTest(string projectNo, string expected)
        {
            string actual = _controller.GetProjectName(projectNo);
            Assert.Equal(expected, actual);
        }

#nullable enable
        [Theory]
        [InlineData("")]
        [InlineData(null)]
        public void GetProjectNameTest_IsNull(string? projectNo)
        {
            string projectName = _controller.GetProjectName(projectNo);
            Assert.Null(projectName);
        }

    }
}
