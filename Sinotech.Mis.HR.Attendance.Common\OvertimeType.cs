﻿namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 加班類型
    /// </summary>
    public enum OvertimeType
    {
        /// <summary>
        /// 加班
        /// </summary>
        Overtime = 1,
        /// <summary>
        /// 社外加班
        /// </summary>
        OutsideOvertime = 2,
        /// <summary>
        /// 補休假
        /// </summary>
        ReservedLeave = 3 // 補休假, aka 保留代休
    }
}
