﻿using FakeItEasy;
using Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.PositionType;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.Tests
{
    [ExcludeFromCodeCoverage]
    public class PositionTest
    {
        // 檢查工廠方法是否回傳正確物件
        [Theory]
        [InlineData(B1CardPositionEnum.Chairman, false, typeof(StaffOvertimeNotAllowed))]
        [InlineData(B1CardPositionEnum.President, false, typeof(StaffOvertimeNotAllowed))]
        [InlineData(B1CardPositionEnum.VicePresident, false, typeof(StaffOvertimeNotAllowed))]
        [InlineData(B1CardPositionEnum.Manager, false, typeof(StaffOvertimeNotAllowed))]
        [InlineData(B1CardPositionEnum.Deputy<PERSON>anager, false, typeof(StaffOvertimeNotAllowed))]
        [InlineData(B1CardPositionEnum.Director, false, typeof(StaffOvertimeNotAllowed))]
        [InlineData(B1CardPositionEnum.DeputyDirector, false, typeof(StaffOvertimeNotAllowed))]
        [InlineData(B1CardPositionEnum.ProjectDirector, false, typeof(StaffOvertimeNotAllowed))]
        [InlineData(B1CardPositionEnum.Driver, true, typeof(Driver))]
        [InlineData(B1CardPositionEnum.ChiefAccountant, true, typeof(GeneralStaff))]
        [InlineData(B1CardPositionEnum.SectionChief, true, typeof(GeneralStaff))]
        [InlineData(B1CardPositionEnum.ProjectSectionChief, true, typeof(GeneralStaff))]
        [InlineData(B1CardPositionEnum.EnvLabChief, true, typeof(GeneralStaff))]
        [InlineData(B1CardPositionEnum.GeneralStaff, true, typeof(GeneralStaff))]
        public void CheckIsOvertimeAllowed(
            B1CardPositionEnum positionToBeChecked,
            bool canWorkOvertime,
            Type expectedType)
        {
            var provider = A.Fake<IB1CardDataProvider>();
            A.CallTo(() => provider.EmployeeNumber).Returns("0000");
            A.CallTo(() => provider.OvertimeDate).Returns(DateTime.Now);
            A.CallTo(() => provider.GetPositionType())
                .Returns(positionToBeChecked);

            //var provider = new B1CardParameters();
            //provider.EmployeePosition = positionToBeChecked;
            //provider.OvertimeDate = DateTime.Now;
            //provider.EmployeeDetail = new Mis.Common.Employee();
            //provider.EmployeeDetail.EmpNo = "0000";

            var position = B1CardPositionFactory.GetB1CardPositionObject(provider);

            Assert.IsType(expectedType, position);
            Assert.Equal(position.IsOvertimeAllowed, canWorkOvertime);
            //Assert.Throws<InvalidOperationException>(() => position.CheckOvertime());
            //var exception = Assert.Throws<InvalidOperationException>(() => position.CheckOvertime());
            //Assert.IsInstanceOf<InvalidOperationException>(exception);
            //Assert.AreEqual("Invalid Operation!!!", exception.Message);
        }

        // 檢查工廠方法是否回傳正確物件
        [Fact]
        public void CheckIsOvertimeAllowed_SpecialStaff()
        {
            var provider = A.Fake<IB1CardDataProvider>();
            A.CallTo(() => provider.EmployeeNumber).Returns("0000");
            A.CallTo(() => provider.OvertimeDate).Returns(DateTime.Now);
            A.CallTo(() => provider.GetPositionType())
                .Returns(B1CardPositionEnum.GeneralStaff);
            A.CallTo(() => provider.IsSpecialStaff()).Returns(true);

            var position = B1CardPositionFactory.GetB1CardPositionObject(provider);

            Assert.IsType<SpecialStaff>(position);
            Assert.True(position.IsOvertimeAllowed);
        }

        // 檢查工廠方法是否回傳正確物件，檢查例外狀況
        [Theory]
        [InlineData(B1CardPositionEnum.NotInList)]
        public void CheckIsOvertimeAllowed_InvalidEmployeeNumber(
            B1CardPositionEnum positionToBeChecked)
        {

            var provider = A.Fake<IB1CardDataProvider>();
            A.CallTo(() => provider.EmployeeNumber).Returns("0000");
            A.CallTo(() => provider.OvertimeDate).Returns(DateTime.Now);
            A.CallTo(() => provider.GetPositionType())
                .Returns(positionToBeChecked);
            var exception = Assert.Throws<ArgumentException>(() => B1CardPositionFactory.GetB1CardPositionObject(provider));

        }
    }
}