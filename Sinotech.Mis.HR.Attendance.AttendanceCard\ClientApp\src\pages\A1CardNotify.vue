<template>
  <CardNotify>
    <template #header>
      <i class="bi bi-bell me-1" />
      <span>三卡通知 / </span>
      <span>{{ FORM_ID.A1Card }}</span>
    </template>
  </CardNotify>
  <div class="container border border-2 px-0">
    <A1Card :modelValue="cardStore.data" />
  </div>
  <div class="container px-0">
    <FormFlow :modelValue="cardStore.data" />
  </div>

  <div class="container px-0">
    <div class="text-black-50 text-end">
      <span>
        查詢時間：
      </span>
      <span class="fst-italic">
        {{ dateToRocString(cardStore.queryDataTime) }}
      </span>
    </div>
  </div>
</template>
<script setup lang="ts">
import { onMounted } from 'vue'
import { useRoute, onBeforeRouteLeave } from 'vue-router'
import { useAuthUserStore } from '../store/index'
import { useCardStore } from '../store/card'
import { useNavMenuBadgeStore } from '../store/navMenuBadge'
import { useToast } from 'primevue/usetoast'
import { useAbortController } from '../composable/abortController'
import { FORM_ID, SYSTEM_ERROR_MESSAGE } from '../api/appConst'
import { dateToRocString } from '../api/appFunction'
import { POST_DeliveredNotifications_URL } from '../api/appUrl'
import CardNotify from '../components/CardNotify.vue'
import A1Card from '../components/A1Card.vue'
import FormFlow from '../components/FormFlow.vue'

const route = useRoute()
const userStore = useAuthUserStore()
const cardStore = useCardStore()
const navMenuBadgeStore = useNavMenuBadgeStore()
const toast = useToast()
const { abortController, fetchErrorHandler, abortListener } = useAbortController()

onBeforeRouteLeave(() => {
  cardStore.$reset()
  toast.removeGroup('app')
})

onMounted(() => {
  abortListener()
  fetch(POST_DeliveredNotifications_URL, {
    method: 'POST',
    headers: {
      'content-type': 'application/json'
    },
    body: JSON.stringify({
      id: route.params.id
    }),
    signal: abortController.signal
  }).then((res: Response) => {
    if (!res.ok) {
      throw new Error(res.status.toString())
    }
    return res.json()
  }).then(() => {
    navMenuBadgeStore.setBadge(userStore.userId, abortController.signal)
  }).catch((err: Error): void => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })
})
</script>