﻿using Sinotech.Mis.Common;

namespace Sinotech.Mis.HR.Attendance.Common
{
    public interface INotificationMail
    {
        /// <summary>
        /// 寄信
        /// </summary>
        /// <param name="form"></param>
        /// <param name="approveDto"></param>
        /// <param name="smtpHost"></param>
        /// <param name="port"></param>
        /// <param name="userName"></param>
        /// <param name="password"></param>
        /// <param name="subject"></param>
        /// <param name="body"></param>
        /// <param name="approver"></param>
        /// <param name="employee"></param>
        void SendEmail(Form form, Approve approveDto, string smtpHost, int port, string userName, 
            string password, string subject, string body, Employee approver, Employee employee);

        /// <summary>
        /// 寄送通知Email
        /// </summary>
        /// <param name="form"></param>
        /// <param name="card"></param>
        /// <param name="approveDto"></param>
        /// <param name="currentStep"></param>
        void SendNotifyEmail(Form form, CardBase card, Approve approveDto, FormFlow currentStep);

    }
}
