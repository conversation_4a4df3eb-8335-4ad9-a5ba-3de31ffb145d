﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sinotech.Mis.HR.Attendance.BusinessLogic;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.Utilities;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers
{
    /// <summary>
    /// 加班申請卡API
    /// </summary>
    [Authorize]
    [ApiController]
    [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
    [Route("/api/[controller]/[action]")]
    public class B1CardAppController : ControllerBase
    {

        private readonly bool _useNegotiate = true;
        // 避免重覆送出鎖定物件
        private static readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);
        private readonly IAttendanceBo _attendanceBo;
        private readonly IB1CardAppBo _b1CardAppBo;
        private readonly IB1CardBo _b1CardBo;

        private readonly IConfiguration _Configuration;
        private readonly ILogger<B1CardAppController> _logger;
        private readonly IWorkdayBo _workdayBo;

        /// <summary>
        ///   <para>
        ///     <see cref="B1CardAppController" /> 的建構函式</para>
        /// </summary>
        /// <param name="attendanceBo"></param>
        /// /// <param name="b1CardBo"> 加班單 商業物件.</param>
        /// <param name="b1CardAppBo"> 加班申請單 商業物件.</param>
        /// <param name="workdayBo">工作日 商業物件.</param>
        /// <param name="configuration">應用程式設定</param>  
        /// <param name="logger">日誌記錄器</param>
        public B1CardAppController(IAttendanceBo attendanceBo, IB1CardBo b1CardBo,
            IB1CardAppBo b1CardAppBo, IWorkdayBo workdayBo, IConfiguration configuration,
            ILogger<B1CardAppController> logger)
        {
            _attendanceBo = attendanceBo;
            _b1CardBo = b1CardBo;
            _b1CardAppBo = b1CardAppBo;
            _workdayBo = workdayBo;
            _Configuration = configuration;
            _useNegotiate = configuration.GetValue<bool>("UseNegotiate");
            _logger = logger;
        }

        /// <summary>
        /// 前一張送出的加班申請卡
        /// </summary>
        private static B1CardApp _LastCard { get; set; } = new B1CardApp();


        private class MyCheckResult
        {
            public string ErrorMessage { get; set; }
            public string DateAlarmMessage { get; set; }
            public bool IsOvertimeAllowed { get; set; }
        }

        /// <summary>某日期是否可以填報加班申請卡</summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>{"ErrorMessage":"錯誤訊息", "DateAlarmMessage": "警告訊息", "IsOvertimeAllowed": true/false}</returns>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/")]
        public string DateCanFillB1CardApp(DateTime date, string empNo)
        {
            string ret = "{}";
            try
            {
                date = date.ToLocalTime();

                B1CardAppCheckResult result = _b1CardAppBo.DateCanFillB1CardApp(date, empNo);
                MyCheckResult myCheckResult = new MyCheckResult
                {
                    ErrorMessage = result.ErrorMessage,
                    DateAlarmMessage = result.DateAlarmMessage,
                    IsOvertimeAllowed = result.IsOvertimeAllowed
                };
                ret = JsonConvert.SerializeObject(myCheckResult);
            }
            catch (Exception ex)
            {
                _logger.LogError("/api/B1CardApp/DateCanFillB1CardApp({date},{empNo}) 發生錯誤：{Message} {StackTrace}", date, empNo, ex.Message, ex.StackTrace);
            }
            return ret;
        }

        /// <summary>
        /// 取得加班類型
        /// </summary>
        /// <returns>所有加班類型 JSON</returns>
        [Authorize]
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/")]
        public string GetB1CardTypes()
        {
            string ret = "{}";
            try
            {
                ret = JsonConvert.SerializeObject(_b1CardBo.GetB1CardTypes());
            }
            catch (Exception ex)
            {
                _logger.LogError("/api/B1CardApp/GetB1CardTypes 發生錯誤：{Message} {StackTrace}", ex.Message, ex.StackTrace);
            }
            return ret;
        }

        /// <summary> 
        /// 送出加班申請卡
        /// </summary>
        /// <returns>B1CardAppCheckResult JSON</returns>
        [Authorize]
        [HttpPost]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/")]
        public async Task<string> Submit(B1CardApp b1CardApp)
        {
            B1CardAppCheckResult result = new B1CardAppCheckResult();
            result.IsValid = false;

            await _semaphore.WaitAsync();
            try
            {
                result = await ProcessSubmit(b1CardApp);
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.ErrorMessage = AttendanceParameters.GeneralErrorMessage;
                _logger.LogError(ex, "/api/B1CardApp/Submit({B1CardApp}) 發生錯誤：{Message} {StackTrace}", JsonConvert.SerializeObject(b1CardApp), ex.Message, ex.StackTrace);
            }
            finally
            {
                _semaphore.Release();
            }
            string ret = JsonConvert.SerializeObject(result);
            return ret;
        }

        /// <summary>
        /// 真正呼叫送出加班申請卡
        /// </summary>
        /// <param name="b1CardApp"></param>
        /// <returns>B1CardAppCheckResult JSON</returns>
        private async Task<B1CardAppCheckResult> ProcessSubmit(B1CardApp b1CardApp)
        {
            B1CardAppCheckResult result = new B1CardAppCheckResult();
            string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            if (string.IsNullOrWhiteSpace(userId))
            {
                result.ErrorMessage = AttendanceParameters.BadEmployeeNumber;
                result.IsValid = false;
                return result;
            }

            b1CardApp.B1_Date = b1CardApp.B1_Date.ToLocalTime().Date;

            if (b1CardApp.EasyEquals(_LastCard))
            {
                result.ErrorMessage = AttendanceParameters.RepeatSubmitForm;
                result.IsValid = false;
                return result;
            }

            result = _b1CardAppBo.DateCanFillB1CardApp(b1CardApp.B1_Date, b1CardApp.B1_EmpNo);
            if (!result.IsOvertimeAllowed)
            {
                return result;
            }

            b1CardApp = PrepareB1CardApp(b1CardApp, userId);

            result = _b1CardAppBo.CheckData(b1CardApp);
            if (!result.IsValid)
            {
                return result;
            }

            var (ip, hostname) = CardUtility.GetIP_Hostname(Request.HttpContext);
            result = await _b1CardAppBo.AddB1CardApp(userId, b1CardApp, ip.ToString(), hostname);

            if (string.IsNullOrWhiteSpace(result.ErrorMessage))
            {
                _logger.LogInformation("Action: {Action}, {UserId} 填{EmpNo} {Date}加班申請卡，申請時數{Hour}, 小時，計畫編號{ProjectNumber}", nameof(Submit), b1CardApp.B1_WritedEmpNo, b1CardApp.B1_EmpNo, b1CardApp.B1_Date.ToString("yyyy/MM/dd"), b1CardApp.B1_Hour, b1CardApp.B1_PrjNo);
            }

            if (result.IsValid)
            {
                _LastCard = b1CardApp.EasyClone();
            }

            return result;
        }

        /// <summary>
        /// 準備加班申請卡資料
        /// </summary>
        /// <param name="b1CardApp">加班申請卡</param>
        /// <param name="userId">員工編號</param>
        /// <returns>B1CardApp物件</returns>
        private B1CardApp PrepareB1CardApp(B1CardApp b1CardApp, string userId)
        {
            b1CardApp.B1_WDate = b1CardApp.B1_WDate.ToLocalTime();
            b1CardApp.B1_SOURCE = "Attendance";
            b1CardApp.B1_Status = (int)FormStatus.Processing;
            b1CardApp.CreatedTime = b1CardApp.CreatedTime.ToLocalTime();
            b1CardApp.FilledTime = b1CardApp.FilledTime.ToLocalTime();

            string uploadFolder = _Configuration.GetValue<string>("UploadDirectory");
            CardUtility.PostProcessUploadedFiles(b1CardApp.UploadedFiles, uploadFolder);

            b1CardApp.B1_WritedEmpNo = userId;
            return b1CardApp;
        }

    }
}
