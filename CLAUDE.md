# 中興工程顧問社出勤管理系統

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

Always reply in zh-TW.

## Project Overview

This is an attendance management system (出勤系統) for Sinotech company, built with a .NET 8.0 backend and Vue 3 + TypeScript frontend. The system handles various types of attendance forms including normal work cards, overtime applications, overtime cards, and leave requests.

## Architecture

The solution follows a layered architecture with clear separation of concerns:

- **AttendanceCard**: Main web application (ASP.NET Core 8.0)
- **Common**: Shared data models and interfaces
- **BusinessLogic**: Core business logic implementation  
- **DataAccess.Interfaces**: Data access layer interfaces
- **DataAccess.Ado**: ADO.NET implementation of data access
- **Utilities**: Utility classes and helpers
- **Tests**: xUnit test projects
- **ClientApp**: Vue 3 + TypeScript frontend (inside AttendanceCard folder)

## Development Commands

### Backend (.NET)

```bash
# Build the entire solution
dotnet build

# Run tests
dotnet test

# Run tests with coverage
dotnet test --collect "Code Coverage"

# Run the web application
cd Sinotech.Mis.HR.Attendance.AttendanceCard
dotnet run

# SonarScanner analysis (requires SonarQube server)
SonarScanner.bat
```

### Frontend (Vue 3 + TypeScript)

```bash
cd Sinotech.Mis.HR.Attendance.AttendanceCard/ClientApp

# Install dependencies
npm install

# Development server
npm run dev

# Build for production
npm run build

# Run tests (Vitest)
npm run test

# Coverage report
npm run coverage

# Type checking
vue-tsc --noEmit
```

## Testing Framework

- **Backend**: xUnit with FakeItEasy (preferred over Moq)
- **Frontend**: Vitest with Vue Test Utils
- **Coverage**: Coverlet for .NET, Istanbul for frontend

## Technology Stack

### Backend

- .NET 8.0 C#
- ASP.NET Core Web API
- ADO.NET for data access
- Serilog for logging
- xUnit for testing

### Frontend  

- Node.js 20+
- Vue 3 with Composition API
- TypeScript 5
- Vite 5 build tool
- PrimeVue UI components
- Bootstrap 5 for styling
- Pinia for state management
- Vue Router 4

## Key Conventions

- **Language**: All comments and documentation should be in Traditional Chinese (zh-TW)
- **UI Framework**: Use PrimeVue for UI components
- **Testing**: Prefer FakeItEasy over Moq for .NET mocking
- **Code Style**: Follow existing patterns in the codebase

## Project Structure

The main application is `Sinotech.Mis.HR.Attendance.AttendanceCard` which contains:

- Controllers for API endpoints
- Vue.js SPA in ClientApp folder
- Configuration files and templates
- Email templates for notifications

Business logic is centralized in the BusinessLogic project with separate business objects (Bo) for each card type (A1, B1, C1).

Data access uses the repository pattern with interfaces in DataAccess.Interfaces and ADO.NET implementations in DataAccess.Ado.
