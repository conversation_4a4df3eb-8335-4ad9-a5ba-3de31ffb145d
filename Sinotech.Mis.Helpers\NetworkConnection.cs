﻿using System;
using System.ComponentModel;
using System.Diagnostics.CodeAnalysis;
using System.Net;
using System.Runtime.InteropServices;

namespace Sinotech.Mis.Helpers
{
    /// <summary>
    /// 網路連結
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class NetworkConnection : IDisposable
    {
        readonly string _networkName;

        /// <summary>
        /// 產生連線
        /// </summary>
        /// <param name="networkName">網路連線名稱</param>
        /// <param name="credentials">憑證</param>
        /// <exception cref="Win32Exception">Win32 Exception</exception>
        public NetworkConnection(string networkName, NetworkCredential credentials)
        {
            _networkName = networkName;

            var netResource = new NetResource
            {
                Scope = ResourceScope.GlobalNetwork,
                ResourceType = ResourceType.Disk,
                DisplayType = ResourceDisplaytype.Share,
                RemoteName = networkName
            };

            var userName = string.IsNullOrEmpty(credentials.Domain)
                ? credentials.UserName
                : string.Format(@"{0}\{1}", credentials.Domain, credentials.UserName);

            var result = WNetAddConnection2(
                netResource,
                credentials.Password,
                userName,
                0);

            if (result != 0)
            {
                throw new Win32Exception(result, "Error connecting to remote share");
            }
        }

        /// <summary>
        /// 解構元
        /// </summary>
        ~NetworkConnection()
        {
            Dispose(false);
        }

        /// <summary>
        /// 資源顯示類型
        /// </summary>
        public enum ResourceDisplaytype
        {
            /// <summary>
            /// Generic
            /// </summary>
            Generic = 0x0,
            /// <summary>
            /// Domain
            /// </summary>
            Domain = 0x01,
            /// <summary>
            /// Server
            /// </summary>
            Server = 0x02,
            /// <summary>
            /// Share
            /// </summary>
            Share = 0x03,
            /// <summary>
            /// File
            /// </summary>
            File = 0x04,
            /// <summary>
            /// Group
            /// </summary>
            Group = 0x05,
            /// <summary>
            /// Network
            /// </summary>
            Network = 0x06,
            /// <summary>
            /// Root
            /// </summary>
            Root = 0x07,
            /// <summary>
            /// Share admin
            /// </summary>
            Shareadmin = 0x08,
            /// <summary>
            /// Directory
            /// </summary>
            Directory = 0x09,
            /// <summary>
            /// Tree
            /// </summary>
            Tree = 0x0a,
            /// <summary>
            /// NDS container
            /// </summary>
            Ndscontainer = 0x0b
        }

        /// <summary>
        /// 資源 Scope
        /// </summary>
        public enum ResourceScope
        {
            /// <summary>
            /// Connected
            /// </summary>
            Connected = 1,
            /// <summary>
            /// GlobalNetwork
            /// </summary>
            GlobalNetwork,
            /// <summary>
            /// Remembered
            /// </summary>
            Remembered,
            /// <summary>
            /// Recent
            /// </summary>
            Recent,
            /// <summary>
            /// Context
            /// </summary>
            Context
        };

        /// <summary>
        /// 資源 Type
        /// </summary>
        public enum ResourceType
        {
            /// <summary>
            /// Any
            /// </summary>
            Any = 0,
            /// <summary>
            /// Disk
            /// </summary>
            Disk = 1,
            /// <summary>
            /// Print
            /// </summary>
            Print = 2,
            /// <summary>
            /// Reserved
            /// </summary>
            Reserved = 8,
        }

        /// <summary>
        /// Dispose
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Dispose
        /// </summary>
        /// <param name="disposing"></param>
        protected virtual void Dispose(bool disposing)
        {
            WNetCancelConnection2(_networkName, 0, true);
        }

        /// <summary>
        /// Net Add Connection
        /// </summary>
        /// <param name="netResource"></param>
        /// <param name="password"></param>
        /// <param name="username"></param>
        /// <param name="flags"></param>
        /// <returns></returns>
        [DllImport("mpr.dll")]
        private static extern int WNetAddConnection2(NetResource netResource,
            string password, string username, int flags);

        /// <summary>
        /// 取消連線
        /// </summary>
        /// <param name="name">連線名稱</param>
        /// <param name="flags">旗標</param>
        /// <param name="force">是否強制中斷</param>
        /// <returns></returns>
        [DllImport("mpr.dll")]
        private static extern int WNetCancelConnection2(string name, int flags,
            bool force);

        /// <summary>
        /// Net Resource
        /// </summary>
        [StructLayout(LayoutKind.Sequential)]
        public class NetResource
        {
            /// <summary>
            /// Scope
            /// </summary>
            public ResourceScope Scope;
            /// <summary>
            /// ResourceType
            /// </summary>
            public ResourceType ResourceType;
            /// <summary>
            /// DisplayType
            /// </summary>
            public ResourceDisplaytype DisplayType;
            /// <summary>
            /// Usage
            /// </summary>
            public int Usage;
            /// <summary>
            /// LocalName
            /// </summary>
            public string LocalName = string.Empty;
            /// <summary>
            /// RemoteName
            /// </summary>
            public string RemoteName = string.Empty;
            /// <summary>
            /// Comment
            /// </summary>
            public string Comment = string.Empty;
            /// <summary>
            /// Provider
            /// </summary>
            public string Provider = string.Empty;
        }
    }
}
