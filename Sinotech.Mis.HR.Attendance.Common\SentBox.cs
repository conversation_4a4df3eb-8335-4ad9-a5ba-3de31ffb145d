﻿using System;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 寄件匣/填報紀錄
    /// </summary>
    public class SentBox: IFormView
    {
        /// <summary>
        /// 流水號
        /// </summary>
        public int ID { get; set; }

        /// <summary>
        /// 表單識別碼
        /// </summary>
        public Guid FormUID { get; set; }

        /// <summary>
        /// 別人代填
        /// </summary>
        public bool IsAgentFilled { get; set; } = false;

        /// <summary>
        /// 替別人填
        /// </summary>
        public bool IsAgency { get; set; } = false;

        /// <summary>
        /// 表單編號
        /// </summary>
        public string FormID { get; set; } = string.Empty;

        /// <summary>
        /// 表單單號
        /// </summary>
        public string FormNo { get; set; } = string.Empty;

        /// <summary>
        /// 表單主旨
        /// </summary>
        public string FormSubject { get; set; } = string.Empty;

        /// <summary>
        /// 表單資訊
        /// </summary>
        public string FormInfo { get; set; } = string.Empty;

        /// <summary>
        /// 申請別
        /// </summary>
        public string ApplicationType { get; set; } = string.Empty;

        /// <summary>
        /// 申請人員工編號
        /// </summary>
        public string EmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 申請人姓名
        /// </summary>
        public string EmpName { get; set; } = string.Empty;

        /// <summary>
        /// 申請人部門編號
        /// </summary>
        public int DeptNo { get; set; }

        /// <summary>
        /// 申請人部門簡稱
        /// </summary>
        public string DeptSName { get; set; } = string.Empty;

        /// <summary>
        /// 申請人組別編號
        /// </summary>
        public int? TeamID { get; set; }

        /// <summary>
        /// 申請人組別名稱
        /// </summary>
        public string? TeamCName { get; set; }

        /// <summary>
        /// 填表人員工編號
        /// </summary>
        public string CreatedEmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 填表人姓名
        /// </summary>
        public string CreatedName { get; set; } = string.Empty;

        /// <summary>
        /// 開始填表時間
        /// </summary>
        public DateTime FilledTime { get; set; }

        /// <summary>
        /// 資料建立時間
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 資料建立來源IP 位址
        /// </summary>
        public string CreatedIP { get; set; } = string.Empty;

        /// <summary>資料建立來源電腦名稱</summary>
        public string? CreatedHost { get; set; }
        /// <summary>
        /// 加會人員員工編號
        /// </summary>
        public string? AddedSigner { get; set; }


        /// <summary>
        /// 表單流程開始日期時間
        /// </summary>
        public DateTime StartTime { get; set; }


        /// <summary>
        /// 表單流程結案日期時間
        /// </summary>
        public DateTime? EndTime { get; set; }


        /// <summary>
        /// 表單狀態(資料庫是 tinyint)
        /// </summary>
        public int FormStatus { get; set; }

        /// <summary>
        /// 表單簽核狀態名稱
        /// </summary>
        public string? FormStatusName { get; set; } = null;

        /// <summary>
        /// 總關卡數(共幾關)
        /// </summary>
        public int TotalSteps { get; set; }

        /// <summary>
        /// 目前關卡
        /// </summary>
        public int CurrentStep { get; set; }

        /// <summary>
        /// 填報總時數
        /// </summary>
        public int TotalHours { get; set; } = 0;

        /// <summary>
        /// 最後修改人員工編號
        /// </summary>
        public string? UpdatedEmpNo { get; set; }

        /// <summary>
        /// 最後修改日期時間
        /// </summary>
        public DateTime? UpdatedTime { get; set; }

        /// <summary>
        /// 最後修改來源IP 位址
        /// </summary>
        public string? UpdatedIP { get; set; }

        /// <summary>
        /// 最後修改來源電腦名稱
        /// </summary>
        public string? UpdatedHost { get; set; }
    }
}
