﻿using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.Tests
{
    [ExcludeFromCodeCoverage]   
    public class BusinessInjuryLeaveTests
    {
        private readonly BusinessInjuryLeave _businessInjuryLeave;
        private readonly C1Card _c1Card;
        private readonly IC1CardBo _c1CardBo;

        public BusinessInjuryLeaveTests(IC1CardBo c1CardBo)
        {
            _c1Card = new C1Card();
            _c1CardBo = c1CardBo;
            _businessInjuryLeave = new BusinessInjuryLeave(_c1Card, _c1CardBo);
        }

        [Fact]
        public void CanTakeThisLeave_ShouldReturnResultOk()
        {
            var result = _businessInjuryLeave.CanTakeThisLeave();
            Assert.Equal(_businessInjuryLeave.ResultOk, result);
        }

        [Fact]
        public void CheckLeaveRange_ShouldReturnResultOk()
        {
            var result = _businessInjuryLeave.CheckLeaveRange();
            Assert.Equal(_businessInjuryLeave.ResultOk, result);
        }

        [Fact]
        public void CheckOverPermittedLeaveHours_ShouldReturnResultOk()
        {
            var result = _businessInjuryLeave.CheckOverPermittedLeaveHours();
            Assert.Equal(_businessInjuryLeave.ResultOk, result);
        }

        [Fact]
        public void CheckRemainHours_ShouldReturnResultOk()
        {
            var result = _businessInjuryLeave.CheckRemainHours();
            Assert.Equal(_businessInjuryLeave.ResultOk, result);
        }

        [Fact]
        public void CalculateLeavePermittedPeriod_ShouldReturnDefaultPeriod()
        {
            DateTime date = DateTime.Now;
            string empNo = "12345";
            var result = _businessInjuryLeave.CalculateLeavePermittedPeriod(date, empNo);
            var expected = _businessInjuryLeave.DefaultCalculateLeavePermittedPeriod(date, empNo);
            Assert.Equal(expected, result);
        }

        [Fact]
        public void IsAllowForThisGender_ShouldReturnTrue()
        {
            Assert.True(BusinessInjuryLeave.IsAllowForThisGender(Gender.Male));
            Assert.True(BusinessInjuryLeave.IsAllowForThisGender(Gender.Female));
        }
    }

}
