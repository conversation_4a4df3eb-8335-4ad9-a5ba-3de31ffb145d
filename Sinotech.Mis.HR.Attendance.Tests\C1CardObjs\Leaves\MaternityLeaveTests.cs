﻿using Sinotech.Mis.HR.Attendance.Common;
using System;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves.Tests
{
    public class MaternityLeaveTests
    {
        private readonly C1CardBo _c1CardBo;

        public MaternityLeaveTests(C1CardBo c1CardBo)
        {
            _c1CardBo = c1CardBo;
        }

        [Theory]
        [InlineData("2022-02-14", "0349", 1, "2022-01-17", "2022-04-11")]
        [InlineData("2022-02-14", "0349", 2, "2022-02-14", "2022-03-14")]
        [InlineData("2022-02-14", "0349", 3, "2022-02-14", "2022-02-21")]
        [InlineData("2022-02-14", "0349", 4, "2022-02-14", "2022-02-19")]
        [InlineData("2022-02-24", "0349", 1, "2022-01-27", "2022-04-21")]
        [InlineData("2022-02-24", "0349", 2, "2022-02-24", "2022-03-24")]
        [InlineData("2022-02-24", "0349", 3, "2022-02-24", "2022-03-03")]
        [InlineData("2022-02-24", "0349", 4, "2022-02-24", "2022-03-01")]
        [InlineData("2022-10-11", "0349", 1, "2022-09-13", "2022-12-06")]
        [InlineData("2022-10-11", "0349", 2, "2022-10-11", "2022-11-08")]
        [InlineData("2022-10-11", "0349", 3, "2022-10-11", "2022-10-18")]
        [InlineData("2022-10-11", "0349", 4, "2022-10-11", "2022-10-16")]
        [InlineData("2022-12-10", "0349", 1, "2022-11-12", "2023-02-04")]
        [InlineData("2022-12-10", "0349", 2, "2022-12-10", "2023-01-07")]
        [InlineData("2022-12-10", "0349", 3, "2022-12-10", "2022-12-17")]
        [InlineData("2022-12-10", "0349", 4, "2022-12-10", "2022-12-15")]
        public void CalculateLeavePermittedPeriodTest(DateTime eventDate, string empNo, int leaveSubNumber, DateTime expectedStartDate, DateTime expectedEndDate)
        {
            DateTime startDate;
            DateTime endDate;
            C1Card leave = new C1Card();
            leave.LeaveNumber = LeaveKindEnum.MaternityLeave;
            leave.LeaveSubNumber = leaveSubNumber;
            MaternityLeave maternityLeave = new MaternityLeave(leave, _c1CardBo);
            (startDate, endDate) = maternityLeave.CalculateLeavePermittedPeriod(eventDate, empNo);
            Assert.Equal(expectedStartDate, startDate);
            Assert.Equal(expectedEndDate, endDate);
        }

        [Theory]
        [InlineData(Gender.Female, true)]
        [InlineData(Gender.Male, false)]
        [InlineData((Gender)2, false)]
        public void IsAllowForThisGenderTest(Gender gender, bool expected)
        {
            bool result = MaternityLeave.IsAllowForThisGender(gender);
            Assert.Equal(expected, result);
        }
    }
}