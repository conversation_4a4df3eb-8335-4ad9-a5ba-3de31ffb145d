﻿using System;
using System.Data;
using Xunit;
using Sinotech.Mis.HR.Attendance.Common;

namespace Sinotech.Mis.HR.Attendance.Common.Tests
{
    public class FormFlowTests
    {
        [Fact]
        public void DataRowToFormFlow_ShouldPopulateAllProperties()
        {
            // Arrange
            DataTable dt = new DataTable();
            dt.Columns.Add("FlowUID", typeof(Guid));
            dt.Columns.Add("FormUID", typeof(Guid));
            dt.Columns.Add("RecipientEmpNo", typeof(string));
            dt.Columns.Add("RecipientName", typeof(string));
            dt.Columns.Add("RecipientDeptNo", typeof(int));
            dt.Columns.Add("RecipientDeptSName", typeof(string));
            dt.Columns.Add("RecipientTeamID", typeof(int));
            dt.Columns.Add("RecipientTeamCName", typeof(string));
            dt.Columns.Add("FlowName", typeof(string));
            dt.Columns.Add("Step", typeof(byte));
            dt.Columns.Add("ApproverEmpNo", typeof(string));
            dt.Columns.Add("ApproverName", typeof(string));
            dt.Columns.Add("ApproverDeptNo", typeof(int));
            dt.Columns.Add("ApproverDeptSName", typeof(string));
            dt.Columns.Add("ApproverTeamID", typeof(int));
            dt.Columns.Add("ApproverTeamCName", typeof(string));
            dt.Columns.Add("ApproveTime", typeof(DateTime));
            dt.Columns.Add("ApproveIP", typeof(string));
            dt.Columns.Add("ApproveHost", typeof(string));
            dt.Columns.Add("IsAgentApprove", typeof(bool));
            dt.Columns.Add("FlowStatus", typeof(byte));
            dt.Columns.Add("FlowStatusName", typeof(string));
            dt.Columns.Add("IsNotification", typeof(bool));

            DataRow row = dt.NewRow();
            row["FlowUID"] = Guid.NewGuid();
            row["FormUID"] = Guid.NewGuid();
            row["RecipientEmpNo"] = "Emp001";
            row["RecipientName"] = "John Doe";
            row["RecipientDeptNo"] = 101;
            row["RecipientDeptSName"] = "HR";
            row["RecipientTeamID"] = 201;
            row["RecipientTeamCName"] = "Team A";
            row["FlowName"] = "Approval";
            row["Step"] = 1;
            row["ApproverEmpNo"] = "Emp002";
            row["ApproverName"] = "Jane Smith";
            row["ApproverDeptNo"] = 102;
            row["ApproverDeptSName"] = "Finance";
            row["ApproverTeamID"] = 202;
            row["ApproverTeamCName"] = "Team B";
            row["ApproveTime"] = DateTime.Now;
            row["ApproveIP"] = "***********";
            row["ApproveHost"] = "Host1";
            row["IsAgentApprove"] = true;
            row["FlowStatus"] = 2;
            row["FlowStatusName"] = "Approved";
            row["IsNotification"] = false;

            // Act
            FormFlow formFlow = FormFlow.DataRowToFormFlow(row);

            // Assert
            Assert.Equal(row["FlowUID"], formFlow.FlowUID);
            Assert.Equal(row["FormUID"], formFlow.FormUID);
            Assert.Equal(row["RecipientEmpNo"], formFlow.RecipientEmpNo);
            Assert.Equal(row["RecipientName"], formFlow.RecipientName);
            Assert.Equal(row["RecipientDeptNo"], formFlow.RecipientDeptNo);
            Assert.Equal(row["RecipientDeptSName"], formFlow.RecipientDeptSName);
            Assert.Equal(row["RecipientTeamID"], formFlow.RecipientTeamID);
            Assert.Equal(row["RecipientTeamCName"], formFlow.RecipientTeamCName);
            Assert.Equal(row["FlowName"], formFlow.FlowName);
            Assert.Equal((int)(byte)row["Step"], formFlow.Step);
            Assert.Equal(row["ApproverEmpNo"], formFlow.ApproverEmpNo);
            Assert.Equal(row["ApproverName"], formFlow.ApproverName);
            Assert.Equal(row["ApproverDeptNo"], formFlow.ApproverDeptNo);
            Assert.Equal(row["ApproverDeptSName"], formFlow.ApproverDeptSName);
            Assert.Equal(row["ApproverTeamID"], formFlow.ApproverTeamID);
            Assert.Equal(row["ApproverTeamCName"], formFlow.ApproverTeamCName);
            Assert.Equal(row["ApproveTime"], formFlow.ApproveTime);
            Assert.Equal(row["ApproveIP"], formFlow.ApproveIP);
            Assert.Equal(row["ApproveHost"], formFlow.ApproveHost);
            Assert.Equal(row["IsAgentApprove"], formFlow.IsAgentApprove);
            Assert.Equal((int)(byte)row["FlowStatus"], formFlow.FlowStatus);
            Assert.Equal(row["FlowStatusName"], formFlow.FlowStatusName);
            Assert.Equal(row["IsNotification"], formFlow.IsNotification);
        }
    }
}
