﻿using Newtonsoft.Json;
using Sinotech.Mis.HR.Attendance.BusinessLogic;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.Tests.C1CardObjs
{
    [ExcludeFromCodeCoverage]
    public class C1CardBoTest
    {
        private C1CardBo _c1CardBo;
        public C1CardBoTest(C1CardBo c1CardBo)
        {
            _c1CardBo = c1CardBo;
        }

        [Theory]
        [InlineData(2023, 4, 24, 8, 0, 2023, 4, 24, 17, 0, 8)] // 同一天，正常工時 8小時
        [InlineData(2023, 4, 24, 9, 10, 2023, 4, 24, 16, 5, 6)] // 同一天，正常工時 5小時
        [InlineData(2023, 4, 24, 9, 5, 2023, 4, 24, 16, 10, 7)] // 同一天，正常工時 7小時
        [InlineData(2023, 4, 24, 8, 30, 2023, 4, 24, 12, 30, 4)] // 同一天，早上 8:30 到中午 12:30, 4小時
        [InlineData(2023, 4, 24, 7, 15, 2023, 4, 24, 12, 30, 4)] // 同一天，早上 7:15 到中午 12:30, 4小時
        [InlineData(2023, 4, 24, 7, 15, 2023, 4, 24, 12, 59, 4)] // 同一天，早上 7:15 到中午 12:59, 5小時
        [InlineData(2023, 4, 24, 7, 15, 2023, 4, 24, 13, 0, 4)] // 同一天，早上 7:15 到中午 13:00, 4小時
        [InlineData(2023, 4, 24, 7, 15, 2023, 4, 24, 13, 1, 5)] // 同一天，早上 7:15 到中午 13:01, 5小時
        [InlineData(2023, 4, 24, 8, 0, 2023, 4, 25, 17, 0, 16)] // 2天，正常工時 16小時
        [InlineData(2023, 4, 24, 10, 0, 2023, 4, 25, 15, 0, 12)] // 2天，早上 10:00 到下午 15:00, 12小時
        [InlineData(2023, 4, 24, 10, 4, 2023, 4, 25, 15, 9, 13)] // 2天，早上 10:04 到下午 15:09, 13小時,因為休息時間不再自動展延
        [InlineData(2023, 4, 24, 10, 30, 2023, 4, 25, 15, 10, 13)] // 2天，早上 10:30 到下午 15:10, 13小時
        [InlineData(2023, 4, 24, 10, 30, 2023, 4, 25, 15, 11, 13)] // 2天，早上 10:30 到下午 15:11, 13小時
        [InlineData(2023, 4, 24, 8, 0, 2023, 4, 26, 17, 0, 24)] // 3天，早上 8:00 到下午 17:00, 24小時
        [InlineData(2023, 4, 24, 9, 10, 2023, 4, 26, 16, 5, 23)] // 2天，早上 9:10 到下午 16:05, 23小時
        [InlineData(2023, 4, 24, 9, 5, 2023, 4, 26, 16, 10, 23)] // 2天，早上 9:05 到下午 16:10, 23小時
        public void CalculateTakeLeaveWorkingHoursTest(int startYear, int startMonth, int startDay, int startHour, int startMinute,
        int endYear, int endMonth, int endDay, int endHour, int endMinute, int expectedHours)
        {
            DateTime startDate = new DateTime(startYear, startMonth, startDay, startHour, startMinute, 0);
            DateTime endDate = new DateTime(endYear, endMonth, endDay, endHour, endMinute, 0);
            int hours = _c1CardBo.CalculateTakeLeaveWorkingHours(startDate, endDate);
            Assert.Equal(expectedHours, hours);
        }

        [Fact]
        public void GetRestKindsStringTest()
        {
            string empNo = "0349";
            string json = _c1CardBo.GetLeaveKindsString(empNo);
            Assert.NotEmpty(json);
            List<LeaveKind> kinds = JsonConvert.DeserializeObject<List<LeaveKind>>(json);
            Assert.True(kinds.Count > 10);

            empNo = "0395";
            json = _c1CardBo.GetLeaveKindsString(empNo);
            Assert.NotEmpty(json);
            List<LeaveKind> manKinds = JsonConvert.DeserializeObject<List<LeaveKind>>(json);
            Assert.True(manKinds.Count > 10);

            Assert.True(kinds.Count > manKinds.Count);
        }
    }
}
