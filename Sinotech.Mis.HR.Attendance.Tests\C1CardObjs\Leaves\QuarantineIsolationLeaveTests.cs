﻿using Xunit;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using System.Diagnostics.CodeAnalysis;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves.Tests
{
    [ExcludeFromCodeCoverage]
    public class QuarantineIsolationLeaveTests : LeaveNotFoundExceptionTests
    {
        public QuarantineIsolationLeaveTests()
        {
            #region Prepare Data

            _c1Card.LeaveNumber = LeaveKindEnum.QuarantineIsolationLeave;

            #endregion
        }

        [Theory]
        [InlineData(Gender.Female, true)]
        [InlineData(Gender.Male, true)]
        public void TestGender(Gender gender, bool expected)
        {
            var result = QuarantineIsolationLeave.IsAllowForThisGender(gender);
            Assert.Equal(expected, result);
        }
    }
}