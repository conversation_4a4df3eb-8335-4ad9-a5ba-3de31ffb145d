﻿using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Security.Cryptography;
using Xunit;

namespace Sinotech.Mis.Extensions.Configuration.Tests
{
    [ExcludeFromCodeCoverage]
    public class RsaCryptoTests
    {
        private const int KeySize = 2048;

        [Fact]
        public void RsaEncrypt_ShouldEncryptInputText()
        {
            // Arrange
            string inputText = "Hello, World!";
            string publicKeyPath = "publicKeyTest.xml";

            // 生成公私鑰
            using var rsa = new RSACryptoServiceProvider(KeySize);
            string publicKey = rsa.ToXmlString(false);
            File.WriteAllText(publicKeyPath, publicKey);

            // Act
            string encryptedText = RsaCrypto.RsaEncrypt(publicKeyPath, inputText);

            // Assert
            Assert.NotEqual(inputText, encryptedText);
            // 清理
            File.Delete(publicKeyPath);
        }

        [Fact]
        public void RsaDecrypt_WithPrivateKeyPath_ShouldDecryptEncryptedText()
        {
            // Arrange
            string inputText = "Hello, World!";
            string publicKeyPath = "publicKeyTest.xml";
            string privateKeyPath = "privateKeyTest.xml";

            // 生成公私鑰
            using var rsa = new RSACryptoServiceProvider(KeySize);
            string publicKey = rsa.ToXmlString(false);
            string privateKey = rsa.ToXmlString(true);
            File.WriteAllText(publicKeyPath, publicKey);
            File.WriteAllText(privateKeyPath, privateKey);

            // 加密
            string encryptedText = RsaCrypto.RsaEncrypt(publicKeyPath, inputText);

            // Act
            string decryptedText = RsaCrypto.RsaDecrypt(privateKeyPath, encryptedText);

            // Assert
            Assert.Equal(inputText, decryptedText);

            // 清理
            File.Delete(publicKeyPath);
            File.Delete(privateKeyPath);
        }

        [Fact]
        public void RsaDecrypt_WithRSACryptoServiceProvider_ShouldDecryptEncryptedText()
        {
            // Arrange
            string inputText = "Hello, World!";
            using var rsa = new RSACryptoServiceProvider(KeySize);
            string privateKey = rsa.ToXmlString(true);

            // 加密
            string encryptedText = RsaCrypto.RsaEncrypt(privateKey, inputText);
            
            // Act
            string decryptedText = RsaCrypto.RsaDecrypt(rsa, encryptedText);

            // Assert
            Assert.Equal(inputText, decryptedText);

        }

        [Fact]
        public void RsaDecrypt_WithInvalidPrivateKey_ShouldReturnOriginalText()
        {
            // Arrange
            string invalidPrivateKeyPath = "invalidprivateKeyTest.xml";
            File.WriteAllText(invalidPrivateKeyPath, "Invalid Key");
            string encryptedText = "無效的加密文";

            // Act
            string result = RsaCrypto.RsaDecrypt(invalidPrivateKeyPath, encryptedText);

            // Assert
            Assert.Equal(encryptedText, result);

            // 清理
            File.Delete(invalidPrivateKeyPath);
        }

        [Fact]
        public void RsaEncrypt_WithInvalidPublicKey_ShouldReturnOriginalText()
        {
            // Arrange
            string invalidPublicKeyPath = "invalidpublicKeyTest.xml";
            File.WriteAllText(invalidPublicKeyPath, "Invalid Key");
            string inputText = "Test Text";

            // Act
            string result = RsaCrypto.RsaEncrypt(invalidPublicKeyPath, inputText);

            // Assert
            Assert.Equal(inputText, result);

            // 清理
            File.Delete(invalidPublicKeyPath);
        }
    }
}