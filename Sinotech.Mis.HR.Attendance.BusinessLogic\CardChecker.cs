﻿using Sinotech.Mis.Common;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.Utilities;
using System;
using System.Collections.Generic;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic
{
    /// <summary>
    /// 負責檢查卡的業務邏輯介面。
    /// </summary>
    public class CardChecker : ICardChecker
    {

        private readonly IProjectBo _projectBo;
        private readonly IWorkdayBo _workdayBo;
        protected readonly IA1CardBo _a1CardBo;
        protected readonly IB1CardAppBo _b1CardAppBo;
        protected readonly IB1CardBo _b1CardBo;
        protected readonly IC1CardBo _c1CardBo;

        public CardChecker(IA1CardBo a1CardBo, IB1CardAppBo b1CardAppBo, IB1CardBo b1CardBo, 
            IC1CardBo c1CardBo, IWorkdayBo workdayBo, IProjectBo projectBo)
        {
            _a1CardBo = a1CardBo;
            _b1CardAppBo = b1CardAppBo;
            _b1CardBo = b1CardBo;
            _c1CardBo = c1CardBo;
            _workdayBo = workdayBo;
            _projectBo = projectBo;
        }

        /// <summary>檢查正常工作卡共用部份</summary>
        /// <param name="a1Card">正常工作卡</param>
        /// <returns>CardCheckResult，正常傳回 Code == 0</returns>
        private CardCheckResult CheckA1CardCommon(A1Card a1Card)
        {
            CardCheckResult result = new(0, CardStatusEnum.Success, string.Empty);

            //檢查是否有填報計畫
            if (a1Card.Details.Count == 0)
            {
                result = new CardCheckResult(100, CardStatusEnum.Error, "請填報計畫");
                return result;
            }

            int year = int.Parse(a1Card.A1_YYMM.Substring(0, 3)) + 1911;
            int month = int.Parse(a1Card.A1_YYMM.Substring(3, 2));
            int iTenDays = int.Parse(a1Card.A1_NN.ToString());
            int serialNo = 0;
            DateTime startDate = DateTime.Now;
            DateTime endDate = DateTime.Now;

            switch (a1Card.A1_NN)
            {
                case '1': // 上旬
                    startDate = new DateTime(year, month, 1, 0, 0, 0, DateTimeKind.Local);
                    endDate = new DateTime(year, month, 10, 0, 0, 0, DateTimeKind.Local);
                    break;
                case '2': // 中旬
                    startDate = new DateTime(year, month, 11, 0, 0, 0, DateTimeKind.Local);
                    endDate = new DateTime(year, month, 20, 0, 0, 0, DateTimeKind.Local);
                    break;
                case '3': // 下旬
                    startDate = new DateTime(year, month, 21, 0, 0, 0, DateTimeKind.Local);
                    endDate = CardUtility.LastDayOfMonth(startDate);
                    break;
            }

            int[] dayTotalHours = new int[a1Card.Details[0].A1_DDHH.Length];
            Array.Clear(dayTotalHours);
            foreach (A1CardDetail detail in a1Card.Details)
            {
                serialNo++;
                string a1SerialNo = serialNo.ToString();
                detail.A1_SERIALNO = a1SerialNo;

                Project project = _projectBo.GetProject(detail.A1_PROJNO);
                if (project != null)
                {
                    int projectHours = 0;
                    char[] chars = detail.A1_DDHH.ToCharArray();
                    for (int i = 0; i < chars.Length; i++)
                    {
                        int hours = int.Parse(chars[i].ToString());
                        if (hours > 0)
                        {
                            projectHours += hours; // 本計畫累計工時
                        }
                        dayTotalHours[i] += hours;
                    }

                    // 單一計畫每旬工時加總須大於0
                    if (projectHours == 0)
                    {
                        int errorCode = 120;
                        string errorMessage = $"計畫編號：{project.PrjNo} 工時有誤，每旬總工時須大於 0 小時";
                        result = new CardCheckResult(errorCode, CardStatusEnum.Error, errorMessage);
                        return result;
                    }
                    else
                    {
                        detail.A1_HOUR = projectHours;
                    }

                    //檢查計畫編號在A1Card的填報日是否結案，前端已先檢查
                    if (project.EDate != null)
                    {
                        for (int i = 0; i < chars.Length; i++)
                        {
                            int hours = int.Parse(chars[i].ToString());
                            if (hours > 0)
                            {
                                int day = 10 * (iTenDays - 1) + i + 1;
                                DateTime date = new DateTime(year, month, day, 0, 0, 0, DateTimeKind.Local);
                                if (project.EDate <= date)
                                {
                                    int errorCode = 130;
                                    string errorMessage = $"計畫{project.PrjNo}於{((DateTime)project.EDate).ToString("yyyy/MM/dd")}已結案";
                                    result = new CardCheckResult(errorCode, CardStatusEnum.Error, errorMessage);
                                    return result;
                                }
                            }
                        }
                    }
                }
            }

            // 檢查每日的總和是否超過上班時數
            List<Workday> daysList = _workdayBo.GetEmpWorkdaysDateRange((DateTime)startDate, (DateTime)endDate, a1Card.A1_EMPNO);
            Workday[] workdays = daysList.ToArray();
            for (int i = 0; i < workdays.Length; i++)
            {
                if (dayTotalHours[i] > workdays[i].WorkHours)
                {
                    string rocDate = CardUtility.RocChineseDateString(workdays[i].WorkDate);
                    int errorCode = 140;
                    string errorMessage = $"{rocDate}填報的工時超過當日的正常工作時數，請修正";
                    result = new CardCheckResult(errorCode, CardStatusEnum.Error, errorMessage);
                    return result;
                }
            }
            return result;
        }

        /// <summary>檢查新增正常工作卡</summary>
        /// <param name="a1Card">正常工作卡</param>
        /// <returns>CardCheckResult，正常傳回 Code == 0</returns>
        public CardCheckResult CheckA1CardAdd(A1Card a1Card)
        {
            CardCheckResult result;

            //檢查是否已填過
            FormFilled filled = _a1CardBo.IsFilled(a1Card.A1_EMPNO, a1Card.A1_YYMM, a1Card.A1_NN);
            if (filled.FormUID != null)
            {
                result = new CardCheckResult(110, CardStatusEnum.Error, AttendanceParameters.A1CardAlreadyFilled);
                return result;
            }

            return CheckA1CardCommon(a1Card);
        }

        /// <summary>檢查更新正常工作卡</summary>
        /// <param name="a1Card">正常工作卡</param>
        /// <returns>CardCheckResult，正常傳回 Code == 0</returns>
        public CardCheckResult CheckA1CardUpdate(A1Card a1Card)
        {
            return CheckA1CardCommon(a1Card);
        }

    }
}
