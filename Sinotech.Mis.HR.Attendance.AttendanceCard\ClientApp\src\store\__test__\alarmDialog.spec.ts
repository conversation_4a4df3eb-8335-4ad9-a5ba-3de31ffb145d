import { setActivePinia, createP<PERSON> } from 'pinia'
import { useAlarmDialogStore } from '../alarmDialog'

describe('useAlarmDialogStore', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('visible', () => {
    const dialog = useAlarmDialogStore()
    expect(dialog.visible).toBe(false)
    dialog.setVisible(true)
    expect(dialog.visible).toBe(true)
  })

  it('message', () => {
    const dialog = useAlarmDialogStore()
    expect(dialog.message).toBe('')
    dialog.setMessage('test')
    expect(dialog.message).toBe('test')
  })

  it('targetRoute', () => {
    const dialog = useAlarmDialogStore()
    expect(dialog.targetRoute).toBe('')
    dialog.setTargetRoute('test')
    expect(dialog.targetRoute).toBe('test')
  })

  it('setOnCheckMessage', () => {
    const dialog = useAlarmDialogStore()
    expect(dialog.visible).toBe(false)
    expect(dialog.message).toBe('')
    expect(dialog.targetRoute).toBe('')
    dialog.setOnCheckMessage()
    expect(dialog.visible).toBe(false)
    expect(dialog.message).toBe('')
    expect(dialog.targetRoute).toBe('')
  })
})