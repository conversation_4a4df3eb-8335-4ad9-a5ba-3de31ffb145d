﻿using Microsoft.Extensions.FileSystemGlobbing.Internal;
using System;
using System.ComponentModel;
using System.IO;
using System.Net;
using Xunit;

namespace Sinotech.Mis.Helpers.Tests
{
    public class NetworkCopierTests
    {
        private readonly NetworkCopier _networkCopier;

        public NetworkCopierTests()
        {
            _networkCopier = new NetworkCopier("testUser", "testPassword", "testDomain");
        }


        [Fact]
        public void DirFiles_ShouldReturnFiles()
        {
            string[] files = { "file1.txt", "file2.txt" };
            string path = @"\\test\path";
            string pattern = "*.txt";
            Assert.Throws<Win32Exception>(() => NetworkCopier.DirFiles(path, pattern));
        }

        [Fact]
        public void Copy_ShouldCopyFile()
        {
            string srcPath = @"\\test\path\srcFile.txt";
            string dstPath = @"\\test\path\dstFile.txt";
            Assert.Throws<Win32Exception>(() => _networkCopier.Copy(srcPath, dstPath));
        }

        [Fact]
        public void SaveTo_ShouldSaveFile()
        {
            string dest = @"\\test\path\file.txt";
            byte[] content = System.Text.Encoding.UTF8.GetBytes("Test content");
            Assert.Throws<Win32Exception>(() => _networkCopier.SaveTo(dest, content));
        }

        [Fact]
        public void GetConnectionContext_ShouldReturnNetworkConnection()
        {
            string path = @"\\127.0.0.1\path";
            string domain = "testDomain";
            string userName = "testUser";
            string password = "testPassword";

            Assert.Throws<Win32Exception>(() => NetworkCopier.GetConnectionContext(path, domain, userName, password));
        }
    }
}
