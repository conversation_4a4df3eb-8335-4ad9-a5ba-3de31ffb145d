import { createApp, Plugin } from 'vue'
import { create<PERSON><PERSON> } from 'pinia'
import PrimeVue from 'primevue/config'
import ConfirmationService from 'primevue/confirmationservice'
import ToastService from 'primevue/toastservice'
import Tooltip from 'primevue/tooltip'
import { definePreset } from '@primevue/themes'
import Lara from '@primevue/themes/lara'

// import * as bootstrap from 'bootstrap'
import { Alert, Button, Collapse, Dropdown } from 'bootstrap'

import PrimeLocaleZhTW from 'primelocale/zh-TW.json'

import router from './router'

import 'vue-select/dist/vue-select.css'

import './assets/scss/app.scss'
import bootstrapVariables from './assets/scss/_bootstrap_variables.module.scss'

// Assumes your root component is App.vue
// and placed in same folder as main.ts
import App from './App.vue'

const bootstrapPlugin: Plugin = {
  install(_app, _options) {
    app.provide('alert', Alert)
    app.provide('button', Button)
    app.provide('collapse', Collapse)
    app.provide('dropdown', Dropdown)
  }
}

const LaraPreset = definePreset(<PERSON>, {
  semantic: {
    primary: {
      50: bootstrapVariables.blue100,
      100: bootstrapVariables.blue100,
      200: bootstrapVariables.blue200,
      300: bootstrapVariables.blue300,
      400: bootstrapVariables.blue400,
      500: bootstrapVariables.blue500,
      600: bootstrapVariables.blue600,
      700: bootstrapVariables.blue700,
      800: bootstrapVariables.blue800,
      900: bootstrapVariables.blue900,
      950: bootstrapVariables.blue900
    }
  }
})

const app = createApp(App)

app.use(PrimeVue, {
  locale: PrimeLocaleZhTW['zh-TW'],
  theme: {
    preset: LaraPreset,
    options: {
      darkModeSelector: 'light'
    }
  },
  csp: {
    nonce: 'c2lub3RlY2hBdHRlbmRhbmNl'
  }
})
app.use(ConfirmationService)
app.use(ToastService)

// app.use(bootstrap as unknown as Plugin)
app.use(bootstrapPlugin)

app.use(router)
app.use(createPinia())

app.directive('tooltip', Tooltip)

// Assumes you have a <div id="app"></div> in your index.html
app.mount('#app')
