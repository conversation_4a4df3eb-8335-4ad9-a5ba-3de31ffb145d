﻿using MaxMind.GeoIP2;
using MaxMind.GeoIP2.Responses;
using Microsoft.AspNetCore.Http;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using UAParser.Objects;

#nullable enable

namespace Sinotech.Mis.HR.Attendance.Utilities
{
    /// <summary>
    /// 三卡系統公用輔助程式
    /// </summary>
    public static class CardUtility
    {
        /// <summary>
        /// 使用 AES 從位元組陣列解密字串。
        /// </summary>
        /// <param name="cipherText">加密的位元組陣列。</param>
        /// <param name="Key">加密金鑰。</param>
        /// <param name="IV">初始化向量。</param>
        /// <returns>解密後的字串。</returns>
        static string DecryptStringFromBytes_Aes(byte[] cipherText, byte[] Key, byte[] IV)
        {
            // Check arguments.
            if (cipherText == null || cipherText.Length <= 0)
                throw new ArgumentNullException("cipherText");
            if (Key == null || Key.Length <= 0)
                throw new ArgumentNullException("Key");
            if (IV == null || IV.Length <= 0)
                throw new ArgumentNullException("IV");

            // Declare the string used to hold
            // the decrypted text.
            string? plaintext = null;

            // Create an Aes object
            // with the specified key and IV.
            using (Aes aesAlg = Aes.Create())
            {
                aesAlg.Key = Key;
                aesAlg.IV = IV;
                // Create a decryptor to perform the stream transform.
                ICryptoTransform decryptor = aesAlg.CreateDecryptor(aesAlg.Key, aesAlg.IV);
                // Create the streams used for decryption.
                using (MemoryStream msDecrypt = new MemoryStream(cipherText))
                {
                    using (CryptoStream csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                    {
                        using (StreamReader srDecrypt = new StreamReader(csDecrypt))
                        {
                            // Read the decrypted bytes from the decrypting stream
                            // and place them in a string.
                            plaintext = srDecrypt.ReadToEnd();
                        }
                    }
                }
            }
            return plaintext;
        }

        /// <summary>
        /// 計算輸入字串的 MD5 散列值。
        /// </summary>
        /// <param name="input">要計算散列值的輸入字串。</param>
        /// <returns>輸入字串的 MD5 散列值，以 16 進制字串表示。</returns>
        public static string ComputeMD5Hash(string input)
        {
            // 1. 創建一個MD5實例
            using (MD5 md5 = MD5.Create())
            {
                // 2. 將輸入字串轉換成字節數組
                byte[] inputBytes = Encoding.UTF8.GetBytes(input);

                // 3. 計算字節數組的散列值
                byte[] hashBytes = md5.ComputeHash(inputBytes);

                // 4. 將散列值轉換成16進制字串
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < hashBytes.Length; i++)
                {
                    sb.Append(hashBytes[i].ToString("x2"));
                }

                return sb.ToString();
            }
        }

        /// <summary>
        /// 字串加密為 byte[]
        /// </summary>
        /// <param name="plainText"></param>
        /// <param name="Key"></param>
        /// <param name="IV"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        static byte[] EncryptStringToBytes_Aes(string plainText, byte[] Key, byte[] IV)
        {
            // Check arguments.
            if (plainText == null || plainText.Length <= 0)
                throw new ArgumentNullException("plainText");
            if (Key == null || Key.Length <= 0)
                throw new ArgumentNullException("Key");
            if (IV == null || IV.Length <= 0)
                throw new ArgumentNullException("IV");
            byte[] encrypted;

            // Create an Aes object
            // with the specified key and IV.
            using (Aes aesAlg = Aes.Create())
            {
                aesAlg.Key = Key;
                aesAlg.IV = IV;

                // Create an encryptor to perform the stream transform.
                ICryptoTransform encryptor = aesAlg.CreateEncryptor(aesAlg.Key, aesAlg.IV);

                // Create the streams used for encryption.
                using (MemoryStream msEncrypt = new MemoryStream())
                {
                    using (CryptoStream csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                    {
                        using (StreamWriter swEncrypt = new StreamWriter(csEncrypt))
                        {
                            //Write all text to the stream.
                            swEncrypt.Write(plainText);
                        }
                        encrypted = msEncrypt.ToArray();
                    }
                }
            }

            // Return the encrypted bytes from the memory stream.
            return encrypted;
        }

        /// <summary>
        /// 產生隨機鹽值。
        /// </summary>
        /// <param name="size">鹽值大小。</param>
        /// <returns>隨機鹽值的位元組陣列。</returns>
        private static byte[] GenerateRandomSalt(int size = 16)
        {
            byte[] salt = new byte[size];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(salt);
            }
            return salt;
        }

        /// <summary>
        /// 字串解密(對稱式)
        /// </summary>
        /// <param name="cryptoKey">解密金鑰</param>
        /// <param name="encryptedBase64">加密後的 Base64 字串</param>
        /// <returns>解密後的字串</returns>
        public static string AesDecryptBase64(string cryptoKey, string encryptedBase64)
        {
            if (string.IsNullOrEmpty(cryptoKey))
                throw new ArgumentException("解密金鑰不可為空", nameof(cryptoKey));
            if (string.IsNullOrEmpty(encryptedBase64))
                throw new ArgumentException("加密後的資料不可為空", nameof(encryptedBase64));

            try
            {
                byte[] combinedBytes = Convert.FromBase64String(encryptedBase64);

                // 假設 salt 的長度為 16 字節，IV 的長度為 16 字節
                int saltSize = 16;
                int ivSize = 16;

                if (combinedBytes.Length < saltSize + ivSize)
                    throw new ArgumentException("加密資料格式不正確");

                byte[] salt = combinedBytes.Take(saltSize).ToArray();
                byte[] iv = combinedBytes.Skip(saltSize).Take(ivSize).ToArray();
                byte[] cipherText = combinedBytes.Skip(saltSize + ivSize).ToArray();

                using (var kdf = new Rfc2898DeriveBytes(cryptoKey, salt, 100_000, HashAlgorithmName.SHA256))
                {
                    byte[] key = kdf.GetBytes(32); // 256 位元金鑰

                    string decryptedText = DecryptStringFromBytes_Aes(cipherText, key, iv);
                    return decryptedText;
                }
            }
            catch (FormatException ex)
            {
                Console.WriteLine($"Base64 格式錯誤: {ex.Message}");
                throw;
            }
            catch (CryptographicException ex)
            {
                Console.WriteLine($"解密失敗: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"發生錯誤: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 字串加密(對稱式)
        /// </summary>
        /// <param name="cryptoKey">加密金鑰</param>
        /// <param name="sourceString">加密前字串</param>
        /// <returns>加密後字串</returns>
        public static string AesEncryptBase64(string cryptoKey, string sourceString)
        {
            if (string.IsNullOrEmpty(cryptoKey))
                throw new ArgumentException("解密金鑰不可為空", nameof(cryptoKey));
            if (string.IsNullOrEmpty(sourceString))
                throw new ArgumentException("加密前的資料不可為空", nameof(sourceString));

            try
            {
                byte[] salt = GenerateRandomSalt();
                using (var kdf = new Rfc2898DeriveBytes(cryptoKey, salt, 100_000, HashAlgorithmName.SHA256))
                {
                    byte[] key = kdf.GetBytes(32); // 256 位元金鑰
                    using (Aes aesAlg = Aes.Create())
                    {
                        aesAlg.Key = key;
                        aesAlg.GenerateIV(); // 隨機生成 IV
                        byte[] iv = aesAlg.IV;

                        byte[] encryptedBytes = EncryptStringToBytes_Aes(sourceString, key, iv);
                        // 將 salt, IV 與加密資料一起儲存或傳輸
                        byte[] combinedBytes = new byte[salt.Length + iv.Length + encryptedBytes.Length];
                        Buffer.BlockCopy(salt, 0, combinedBytes, 0, salt.Length);
                        Buffer.BlockCopy(iv, 0, combinedBytes, salt.Length, iv.Length);
                        Buffer.BlockCopy(encryptedBytes, 0, combinedBytes, salt.Length + iv.Length, encryptedBytes.Length);

                        return Convert.ToBase64String(combinedBytes);
                    }
                }
            }
            catch (CryptographicException ex)
            {
                Console.WriteLine($"加密失敗: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"發生錯誤: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 將 Unix 時間戳記轉換為 DateTime。
        /// </summary>
        /// <param name="unixTimeStamp">Unix 時間戳記。</param>
        /// <returns>轉換後的 DateTime。</returns>
        public static DateTime ConvertFromUnixTimestamp(long unixTimeStamp)
        {
            return DateTime.UnixEpoch.AddSeconds(unixTimeStamp).ToLocalTime();
        }

        /// <summary>
        /// 將 DateTime 轉換為 Unix 時間戳記。
        /// </summary>
        /// <param name="dateTime">要轉換的 DateTime。</param>
        /// <returns>轉換後的 Unix 時間戳記。</returns>
        public static long ConvertToUnixTimestamp(DateTime dateTime)
        {
            return (long)(dateTime.ToUniversalTime() - DateTime.UnixEpoch).TotalSeconds;
        }

        /// <summary>
        /// 建立 RSA 金鑰並儲存到指定路徑。
        /// </summary>
        /// <param name="privateKeyPath">私鑰儲存路徑。</param>
        /// <param name="publicKeyPath">公鑰儲存路徑。</param>
        public static void CreateRSAKey(string privateKeyPath, string publicKeyPath)
        {
            try
            {
                //建立RSA物件
                RSACryptoServiceProvider rsa = new RSACryptoServiceProvider(4096);
                //生成RSA[公鑰私鑰]
                string privateKey = rsa.ToXmlString(true);
                string publicKey = rsa.ToXmlString(false);

                //將金鑰寫入指定路徑
                File.WriteAllText(privateKeyPath, privateKey);//檔案內包含公鑰和私鑰
                File.WriteAllText(publicKeyPath, publicKey);//檔案內只包含公鑰
            }
            catch (CryptographicException ex)
            {
                Console.WriteLine(ex.Message);
            }
            catch (ArgumentNullException ex)
            {
                Console.WriteLine(ex.Message);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
        }

        /// <summary>
        /// 獲取網路供應商資訊
        /// </summary>
        /// <param name="ipAddress">IP Address</param>
        /// <param name="asnDbPath">供應商資料庫</param>
        /// <returns>AsnResponse，可能為null</returns>
        public static AsnResponse? GetAsnResponse(string ipAddress, string asnDbPath)
        {
            AsnResponse? response = null;
            try
            {
                using (var reader = new DatabaseReader(asnDbPath))
                {
                    response = reader.Asn(ipAddress);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"發生錯誤: {ex.Message}");
            }
            return response;
        }

        /// <summary>
        /// Get UAParser.objects.ClientInfo instance
        /// </summary>
        /// <param name="userAgent">user agent string</param>
        /// <returns>UAParser.objects.ClientInfo or null</returns>
        public static ClientInfo? GetClientInfo(string userAgent)
        {
            ClientInfo? _clientInfo = null;
            if (!string.IsNullOrWhiteSpace(userAgent))
            {
                var parser = UAParser.Parser.GetDefault();
                _clientInfo = parser.Parse(userAgent);
            }
            return _clientInfo;
        }

        /// <summary>
        ///     取得網域名稱
        /// </summary>
        /// <param name="username">The username.</param>
        /// <returns></returns>
        public static string GetDomain(string username)
        {
            return username.Substring(0, username.IndexOf(@"\")).ToUpper();
        }

        /// <summary>Gets the IP and hostname.</summary>
        /// <param name="context">The context.</param>
        /// <returns>IP and Hostname</returns>
        public static (IPAddress?, string?) GetIP_Hostname(HttpContext context)
        {
            IPAddress? ipAddress = null;
            string? hostname = null;
            if (context != null && context.Connection != null && context.Connection.RemoteIpAddress != null)
            {
                ipAddress = context.Connection.RemoteIpAddress;

                if (ipAddress.IsIPv4MappedToIPv6)
                {
                    ipAddress = ipAddress.MapToIPv4();
                }

                if (IPAddress.IsLoopback(ipAddress))
                {
                    try
                    {
                        string ip = Dns.GetHostEntry(Dns.GetHostName()).AddressList.First(o => o.AddressFamily == AddressFamily.InterNetwork).ToString();
                        ipAddress = IPAddress.Parse(ip);
                    }
                    catch (ArgumentNullException ex)
                    {
                        Console.WriteLine($"{ex.Message} {ex.StackTrace}");
                    }
                    catch (FormatException ex)
                    {
                        Console.WriteLine($"{ex.Message} {ex.StackTrace}");
                    }
                    hostname = GetLocalHostName();
                }
                else
                {
                    try
                    {
                        IPHostEntry host = Dns.GetHostEntry(ipAddress);
                        hostname = host.HostName;
                    }
                    catch (SocketException ex)
                    {
                        Console.WriteLine($"{ex.Message} {ex.StackTrace}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"{ex.Message} {ex.StackTrace}");
                    }
                }
            }
            return (ipAddress, hostname);
        }

        /// <summary>
        /// Get Local Host Name
        /// </summary>
        /// <returns></returns>
        public static string? GetLocalHostName()
        {
            string? ret = null;
            try
            {
                // Get the local computer host name.
                string hostName = Dns.GetHostName();
                ret = hostName;

            }
            catch (SocketException ex)
            {
                Console.WriteLine("CardUtility.GetLocalHostName: SocketException caught!!!");
                Console.WriteLine("Source : " + ex.Source);
                Console.WriteLine("Message : " + ex.Message);
            }
            catch (Exception ex)
            {
                Console.WriteLine("CardUtility.GetLocalHostName: Exception caught!!!");
                Console.WriteLine("Source : " + ex.Source);
                Console.WriteLine("Message : " + ex.Message);
            }
            return ret;
        }

        /// <summary>
        /// 獲取地理位置資訊
        /// </summary>
        /// <param name="ipAddress">IP Address</param>
        /// <param name="geoDbPath">地理位置資料庫路徑</param>
        /// <returns>CityResponse</returns>
        public static CityResponse? GetLocation(IPAddress ipAddress, string geoDbPath)
        {
            CityResponse? city = null;
            // 建立資料庫讀取器
            try
            {
                bool isPrivateIp = IsPrivateIp(ipAddress);
                if (!isPrivateIp)
                {
                    using (var reader = new DatabaseReader(geoDbPath))
                    {
                        // 解析 IP 地址並獲取地理位置信息
                        city = reader.City(ipAddress);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"{ex.Message} {ex.StackTrace}");
            }
            return city;
        }

        /// <summary>
        ///     Gets the logon user.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public static string? GetLogonUser(System.Security.Principal.IIdentity identity)
        {
            //在Windows 認證時應該等同於 HttpContext.Current.User.Identity.Name的值
            return identity.Name;
        }

        /// <summary>Gets the type.</summary>
        /// <param name="assemblyName"></param>
        /// <param name="typeName">FormName of the type.</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public static Type? GetTypeByAssemblyName(string assemblyName, string typeName)
        {
            Assembly assembly = Assembly.Load(assemblyName);
            Type? daoType = assembly.GetType(typeName);
            return daoType;
        }

        /// <summary>Gets the type.</summary>
        /// <param name="dllName">The assembly dll</param>
        /// <param name="typeName">FormName of the type.</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public static Type? GetTypeByFile(string dllName, string typeName)
        {
            Type? daoType = null;
            string[] Result = Directory.GetFiles(Directory.GetCurrentDirectory(), dllName, System.IO.SearchOption.AllDirectories);
            if (Result.Length > 0)
            {
                string file = Result[0];
#pragma warning disable S3885 // "Assembly.Load" should be used
                Assembly assembly = Assembly.LoadFile(file);
#pragma warning restore S3885 // "Assembly.Load" should be used
                daoType = assembly.GetType(typeName);
            }
            return daoType;
        }

        /// <summary>
        ///     Gets the user's identification.
        /// </summary>
        /// <param name="username">The username.</param>
        /// <returns></returns>
        public static string GetValidEmpNo(string username)
        {
            string userId = string.Empty;
            if (username.Contains('\\'))
            {
                string domain = GetDomain(username).ToLower();
                if ((domain == "secinc" || domain == "sinotech") && !string.IsNullOrEmpty(username))
                {
                    userId = username[(username.IndexOf('\\') + 1)..]; // 取代 SubString
                }
            }

            return userId;
        }

        /// <summary>取得有效員工編號</summary>
        /// <param name="context">The context.</param>
        /// <param name="useNegotiate">Use Negotiate Authentication</param>
        /// <returns>
        /// </returns>
        public static string GetValidEmpNo(HttpContext context, bool useNegotiate)
        {
            string empNo = string.Empty;
            if (context != null && context.User != null && context.User.Identity != null && context.User.Identity.IsAuthenticated && !string.IsNullOrWhiteSpace(context.User.Identity.Name))
            {
                string identityName = context.User.Identity.Name;
                if (useNegotiate)
                {
                    empNo = GetValidEmpNo(identityName);
                }
                else
                {
                    empNo = identityName;
                }
            }

            return empNo;
        }

        /// <summary>
        /// 判斷傳入的浮點數是否為整數
        /// </summary>
        /// <param name="number"></param>
        /// <returns></returns>
        public static bool IsInteger(double number)
        {
            return Math.Ceiling(number) == Math.Floor(number);
        }

        /// <summary>
        /// 判斷是否為私有IP
        /// </summary>
        /// <param name="ip"></param>
        /// <returns></returns>
        public static bool IsPrivateIp(IPAddress ip)
        {
            if (IPAddress.IsLoopback(ip)) return true;

            byte[] bytes = ip.GetAddressBytes();

            if (ip.AddressFamily == AddressFamily.InterNetwork)
            {
                // IPv4
                return
                    (bytes[0] == 10) ||
                    (bytes[0] == 172 && bytes[1] >= 16 && bytes[1] <= 31) ||
                    (bytes[0] == 192 && bytes[1] == 168);
            }
            else if (ip.AddressFamily == AddressFamily.InterNetworkV6)
            {
                // IPv6
                return ip.IsIPv6LinkLocal || ip.IsIPv6SiteLocal;
            }

            return false;
        }

        /// <summary>Determines whether [is valid email] [the specified string in].</summary>
        /// <param name="strIn">The string in.</param>
        /// <returns>
        ///   <c>true</c> if [is valid email] [the specified string in]; otherwise, <c>false</c>.</returns>
        public static bool IsValidEmail(string strIn)
        {
            // Return true if strIn is in valid e-mail format.

            var result = Regex.IsMatch(strIn,
                @"^([\w-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([\w-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$",
                RegexOptions.NonBacktracking, TimeSpan.FromSeconds(0.5));
            return result;
        }

        /// <summary>
        /// 下個月的第一天
        /// </summary>
        /// <param name="date"></param>
        /// <returns></returns>
        public static DateTime LastDayOfMonth(DateTime date)
        {
            DateTime datetime = new DateTime(date.Year, date.Month, 1, 0, 0, 0, DateTimeKind.Local);
            return datetime.AddMonths(1).AddDays(-1);
        }

        public static string MaskNumber(long number)
        {
            const string key = "1014432032420132";
            var masked = new StringBuilder();
            string numberString = number.ToString();
            for (int i = 0; i < numberString.Length; i++)
            {
                masked.Append((char)(numberString[i] ^ key[i % key.Length]));
            }
            return masked.ToString();
        }

        /// <summary>
        /// Web API Submit時 後處理附件
        /// </summary>
        /// <param name="files"></param>
        /// <param name="uploadFolder"></param>
        public static void PostProcessUploadedFiles(List<UploadedFile> files, string uploadFolder)
        {
            if (files != null && files.Count > 0)
            {
                foreach (UploadedFile file in files)
                {
                    if (!file.FilePathName.StartsWith(uploadFolder))
                    {
                        file.FilePathName = Path.Combine(uploadFolder, file.FilePathName);
                    }
                }
            }
        }

        /// <summary>
        /// Web API Submit時 後處理附件
        /// </summary>
        /// <param name="card"></param>
        /// <param name="uploadFolder"></param>
        public static void PostProcessUploadedFiles(CardBase card, string uploadFolder)
        {
            if (card.UploadedFiles != null && card.UploadedFiles.Count > 0)
            {
                foreach (UploadedFile file in card.UploadedFiles)
                {
                    if (!file.FilePathName.StartsWith(uploadFolder))
                    {
                        file.FilePathName = Path.Combine(uploadFolder, file.FilePathName);
                    }
                }
            }
        }

        /// <summary>
        /// 中華民國日期字串 YYY/MM/dd
        /// </summary>
        /// <param name="date"></param>
        /// <returns></returns>
        public static string RocChineseDateString(DateTime date)
        {
            int rocYear = RocChineseYear(date.Year);
            return $"{rocYear}/{date:MM/dd}";
        }

        /// <summary>
        /// 中華民國日期時間字串
        /// </summary>
        /// <param name="date"></param>
        /// <returns></returns>
        public static string RocChineseDateTimeString(DateTime date)
        {
            int rocYear = RocChineseYear(date.Year);
            return $"{rocYear}/{date:MM/dd HH:mm}";
        }

        /// <summary>
        /// 西元年轉為中華民國年
        /// </summary>
        /// <param name="year"></param>
        /// <returns></returns>
        public static int RocChineseYear(int year)
        {
            int rocYear = year - 1911;
            return rocYear;
        }

        /// <summary>
        /// 中華民國年
        /// </summary>
        /// <param name="date"></param>
        /// <returns></returns>
        public static int RocChineseYear(DateTime date)
        {
            int rocYear = RocChineseYear(date.Year);
            return rocYear;
        }

        /// <summary>
        /// 取得民國年月字串。
        /// </summary>
        /// <param name="date">日期。</param>
        /// <returns>民國年月字串。</returns>
        public static string RocChineseYearMonthString(DateTime date)
        {
            int rocYear = RocChineseYear(date.Year);
            //return $"{rocYear}年{date.Month}月";
            return $"{rocYear}年{date:M月}";
        }

        /// <summary>
        /// 取得民國年份字串。
        /// </summary>
        /// <param name="date">日期。</param>
        /// <returns>民國年份字串。</returns>
        public static string RocChineseYearString(DateTime date)
        {
            int rocYear = RocChineseYear(date.Year);
            return rocYear.ToString();
        }

        /// <summary>
        /// 取得民國年月字串 (YYYMM)。
        /// </summary>
        /// <param name="date">日期。</param>
        /// <returns>民國年月字串 (YYYMM)。</returns>
        public static string RocChineseYYYMM(DateTime date)
        {
            int rocYear = RocChineseYear(date.Year);
            return $"{rocYear.ToString("000")}{date:MM}";
        }

        /// <summary>
        /// 取得民國年月日字串 (YYYMMDD)。
        /// </summary>
        /// <param name="date">日期。</param>
        /// <returns>民國年月日字串 (YYYMMDD)。</returns>
        public static string RocChineseYYYMMDD(DateTime date)
        {
            int rocYear = RocChineseYear(date.Year);
            return $"{rocYear.ToString("000")}{date:MMdd}";
        }

        /// <summary>
        /// 中華民國中文星期幾簡稱
        /// </summary>
        /// <param name="weekday"></param>
        /// <returns></returns>
        public static string RocWeekDay(int weekday)
        {
            string ret = "";
            switch (weekday)
            {
                case 0:
                    ret = "日";
                    break;
                case 1:
                    ret = "一";
                    break;
                case 2:
                    ret = "二";
                    break;
                case 3:
                    ret = "三";
                    break;
                case 4:
                    ret = "四";
                    break;
                case 5:
                    ret = "五";
                    break;
                case 6:
                    ret = "六";
                    break;
            }
            return ret;
        }

        /// <summary>
        /// 中華民國中文星期幾簡稱字一一山
        /// </summary>
        /// <param name="weekday"></param>
        /// <returns></returns>
        public static char RocWeekDayChar(int weekday)
        {
            char ret = ' ';
            switch (weekday)
            {
                case 0:
                    ret = '日';
                    break;
                case 1:
                    ret = '一';
                    break;
                case 2:
                    ret = '二';
                    break;
                case 3:
                    ret = '三';
                    break;
                case 4:
                    ret = '四';
                    break;
                case 5:
                    ret = '五';
                    break;
                case 6:
                    ret = '六';
                    break;
            }
            return ret;
        }

        /// <summary>
        /// Roles To comma separate string
        /// </summary>
        /// <param name="roles"></param>
        /// <returns></returns>
        public static string RolesToQuotedString(List<Role> roles)
        {
            string strRoles;
            StringBuilder stringBuilder = new StringBuilder();
            foreach (Role role in roles)
            {
                stringBuilder.Append($"'{role.RoleId}',");
            }
            strRoles = stringBuilder.ToString();
            if (strRoles.Length > 1)
            {
                strRoles = strRoles[..^1]; // strRoles.Substring(0, strRoles.Length - 1)
            }
            return strRoles;
        }

        /// <summary>
        /// 使用RSA實現解密
        /// </summary>
        /// <param name="rsaPrivate">RSA Crypto Service Provider</param>
        /// <param name="encryptText">Encrypted Text</param>
        /// <returns>Decrypted Plain Text</returns>
        public static string RsaDecrypt(RSACryptoServiceProvider rsaPrivate, string encryptText)
        {
            string result = encryptText;

            //Create a UnicodeEncoder to convert between byte array and string.
            try
            {
                byte[] dataByteArray = Convert.FromBase64String(encryptText);
                //對資料進行解密
                byte[] privateValue = rsaPrivate.Decrypt(dataByteArray, false);//使用Base64將string轉換為byte            
                if (privateValue.Length != 0)
                {
                    UnicodeEncoding ByteConverter = new UnicodeEncoding();
                    result = ByteConverter.GetString(privateValue);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
            return result;
        }

        /// <summary>
        /// 使用RSA實現解密
        /// </summary>
        /// <param name="privateKeyPath">私鑰路徑</param>
        /// <param name="encryptText">Encrypted Text</param>
        /// <returns>解密資料</returns>
        public static string RsaDecrypt(string privateKeyPath, string encryptText)
        {
            // C#預設只能使用[私鑰]進行解密(想使用[私鑰加密]可使用第三方元件BouncyCastle來實現)
            string privateKey = File.ReadAllText(privateKeyPath);

            //建立RSA物件並載入[私鑰]
            RSACryptoServiceProvider rsaPrivate = new RSACryptoServiceProvider(2048);
            rsaPrivate.FromXmlString(privateKey);
            string result = encryptText;

            //Create a UnicodeEncoder to convert between byte array and string.
            try
            {
                byte[] dataByteArray = Convert.FromBase64String(encryptText);
                //對資料進行解密
                byte[] privateValue = rsaPrivate.Decrypt(dataByteArray, true);//使用Base64將string轉換為byte            
                if (privateValue.Length != 0)
                {
                    UnicodeEncoding ByteConverter = new UnicodeEncoding();
                    result = ByteConverter.GetString(privateValue);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
            return result;
        }

        /// <summary>
        /// 使用RSA實現解密
        /// </summary>
        /// <param name="privateKey">Private Key</param>
        /// <param name="encryptText">Encrypted Text</param>
        /// <returns>Decrypted Plain Text</returns>
        public static string RsaDecryptByPrivateKey(string privateKey, string encryptText)
        {
            string decrypt = string.Empty;
            try
            {
                RSACryptoServiceProvider rsaPCrypto = new RSACryptoServiceProvider(2048);
                rsaPCrypto.FromXmlString(privateKey);
                decrypt = RsaDecrypt(rsaPCrypto, encryptText);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
            return decrypt;
        }

        /// <summary>
        /// 使用RSA實現加密
        /// </summary>
        /// <param name="publicKeyPath">PublicKey Path</param>
        /// <param name="inputText">Plain Text</param>
        /// <returns>加密文字</returns>
        public static string RsaEncrypt(string publicKeyPath, string inputText)
        {
            string result = inputText;
            //C#預設只能使用[公鑰]進行加密(想使用[公鑰解密]可使用第三方元件BouncyCastle來實現)
            string publicKey = File.ReadAllText(publicKeyPath);
            try
            {
                //建立RSA物件並載入[公鑰]
                RSACryptoServiceProvider rsaPublic = new RSACryptoServiceProvider(2048);

                rsaPublic.FromXmlString(publicKey);
                //Create a UnicodeEncoder to convert between byte array and string.
                UnicodeEncoding ByteConverter = new UnicodeEncoding();
                byte[] dataToEncrypt = ByteConverter.GetBytes(inputText);

                //對資料進行加密
                byte[] publicValue = rsaPublic.Encrypt(dataToEncrypt, true);
                result = Convert.ToBase64String(publicValue);//使用Base64將byte轉換為string
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
            return result;
        }

        /// <summary>
        /// 字串末端補零，長度超過預計時傳回原字串
        /// </summary>
        /// <param name="input">輸入字串</param>
        /// <param name="length">補零字串長度</param>
        /// <returns></returns>
        public static string TailFillZero(string input, int length)
        {
            string result;
            if (input != null && input.Length <= length)
            {
                StringBuilder sb = new StringBuilder(input, length);
                for (int i = 0; i < length - input.Length; i++)
                {
                    sb.Append('0');
                }
                result = sb.ToString();
            }
            else if (input != null && input.Length > length)
            {
                result = input;
            }
            else
            {
                StringBuilder sb = new StringBuilder(length);
                for (int i = 0; i < length; i++)
                {
                    sb.Append('0');
                }
                result = sb.ToString();
            }
            return result;
        }

        /// <summary>
        /// 時間字串
        /// </summary>
        /// <param name="date"></param>
        /// <returns></returns>
        public static string TimeString(DateTime date)
        {
            return $"{date.ToString("HH:mm")}";
        }

        /// <summary>
        /// 取消字串遮罩並轉換為數字。
        /// </summary>
        /// <param name="maskedNumber">被遮罩的字串。</param>
        /// <returns>轉換後的數字。</returns>
        public static long UnmaskNumber(string maskedNumber)
        {
            const string key = "1014432032420132";
            var unmasked = new StringBuilder();
            for (int i = 0; i < maskedNumber.Length; i++)
            {
                unmasked.Append((char)(maskedNumber[i] ^ key[i % key.Length]));
            }
            return long.Parse(unmasked.ToString());
        }

    }
}