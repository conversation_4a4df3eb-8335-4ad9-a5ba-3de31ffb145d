<template>
  <h6 class="my-2 fs-2 text-center">
    <i class="bi bi-moon me-1" />
    <span>{{ FORM_ID.B1Card }}</span>
  </h6>
  <div class="container px-0 text-center">
    <div class="border border-dark-subtle border-2 mx-2 mx-sm-0">
      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1card">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>申</span>
              <span class="mx-1">請</span>
              <span>人</span>
            </div>
          </div>
        </div>
        <div class="col-9 col-md-10 py-2 text-start">
          <div class="row">
            <div class="col col-sm-5 col-md-4 col-lg-3 pe-0 py-0">
              <ChooseUser
                :modelValue="{
                  userId: employee.userId,
                  userName: employee.userName,
                  deptNo: employee.deptNo,
                  deptSName: employee.deptSName
                }"
                :employeeData="canWorkOvertimeEmployeeData"
                :filter="onSearchEmployeeData"
                :clearable="false"
                :mode="'apply'"
                :disabled="submitted === true"
                @update:modelValue="onChangeEmployee"
              />
            </div>
            <div class="col-auto">
              <template v-if="userStore.userId !== employee?.userId">
                <span class="badge rounded-pill bg-warning">代填</span>
              </template>
            </div>
          </div>
        </div>
      </div>

      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1card">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>所屬部門</span>
            </div>
          </div>
        </div>
        <div class="col-9 col-md-10 py-2 d-flex align-items-center">
          {{ employee.deptSName }}
        </div>
      </div>

      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1card">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>加班日期</span>
            </div>
          </div>
        </div>
        <div class="col-9 col-md-10 py-2 text-start">
          <div class="row">
            <div class="col-auto">
              <RocCalendarSelect
                :modelValue="overtimeDate"
                :inputClass="{
                  'form-control': true,
                  'w-auto': true,
                  'shake': shake.date
                }"
                :disabled="submitted === true"
                @click="onClickDate"
                @update:modelValue="onChangeDate"
                @update:year-month="onChangeYearMonth"
              >
                <template
                  v-if="calendarLoaded === true"
                  #date="{ date }"
                >
                  <span :class="getCalendarDayClass(date, workdays)">
                    {{ (date as any).day }}
                  </span>
                </template>
              </RocCalendarSelect>
            </div>
            <div
              v-if="errorMessage.date.length > 0"
              class="col-12 text-danger"
            >
              <small>{{ errorMessage.date }}</small>
            </div>
          </div>
        </div>
      </div>

      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1card">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>刷卡時間</span>
            </div>
          </div>
        </div>
        <div class="col-9 col-md-10 py-2 text-start">
          <template v-if="overtimeData">
            {{ overtimeData.inTimeString }}
          </template>
        </div>
      </div>

      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1card">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>加班申請卡</span>
            </div>
          </div>
        </div>
        <div class="col-9 col-md-10 py-2 text-start">
          <template v-if="b1CardApp.formUID !== null && b1CardApp.formUID?.length !== 0 && b1CardApp.formNo">
            <template v-if="(userStore.userId === employee?.userId) || (userStore.userId === b1CardApp.applyEmpNo)">
              <router-link
                v-slot="{ href }"
                :to="{
                  name: b1CardApp.formID + 'Sent',
                  params: { formUID: b1CardApp.formUID }
                }"
                custom
              >
                <a
                  class
                  role="button"
                  tabindex="0"
                  @keydown.enter="onKeydownEditLink(href)"
                  @keydown.space="onKeydownEditLink(href)"
                  @click="onClickEditLink(href)"
                >
                  {{ b1CardApp.formNo }}
                </a>
              </router-link>
            </template>
            <template v-else>
              {{ b1CardApp.formNo }}
            </template>
            <template v-if="b1CardApp.flowStatus === 1">
              <span>(簽核中)</span>
            </template>
          </template>
        </div>
      </div>

      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1card">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>填報計畫</span>
            </div>
          </div>
        </div>
        <div class="col py-2 text-start">
          <div class="row">
            <div :class="['col py-0', shake.project ? 'shake' : '']">
              <ProjectSelect
                :modelValue="project"
                :projectData="projectData"
                :placeholder="'請輸入或選取計畫後，按【填寫計畫加班資料】'"
                :clearable="true"
                :disabled="(checkCanWorkOvertime === false) || (submitted === true)"
                @update:modelValue="onSelectProject"
                @delete="onDeleteProject"
              />
              <button
                type="button"
                :class="[
                  'btn mt-2 me-1 p-2',
                  (((project === null) || (checkCanWorkOvertime === false) || (submitted === true)) ? 'btn-outline-secondary' : 'btn-primary')
                ]"
                :disabled="(project === null) || (checkCanWorkOvertime === false) || (submitted === true)"
                @click="onAddRow"
              >
                <span>填寫計畫加班資料</span>
              </button>
            </div>
            <div class="col-12 fw-normal text-secondary text-start">
              <small>※ 計畫進度已達100%第3個月起不可填報</small>
            </div>
          </div>
        </div>
      </div>

      <DataTable
        v-show="resultData.length > 0"
        :value="resultData"
        class="border px-0 text-start"
      >
        <Column
          field="projectId"
          header="計畫編號"
          headerClass="bg-light"
          class="text-center"
        >
          <template #body="{ index, data, field }">
            <div class="text-center">
              {{ data[field] }}
              <button
                v-tooltip="{ value: '刪除計畫', escape: true }"
                type="button"
                class="btn btn-danger btn-sm mx-1 px-1 py-0"
                @click="onDeleteRow(index)"
              >
                <i class="bi bi-x-lg d-inline d-xl-none" />
                <span class="d-none d-xl-inline">刪除</span>
              </button>
            </div>
          </template>
        </Column>

        <Column
          field="overtimeCompensatory"
          header="申請別"
          headerClass="bg-light"
        >
          <template #body="{ index, data, field }">
            <ul class="list-group">
              <li 
                v-for="(item, itemIndex) in overtimeCompensatory"
                :key="itemIndex"
                class="list-group-item text-center"
              >
                <input
                  :id="'overtimeCompensatory' + index.toString() + itemIndex.toString()"
                  :value="item.id"
                  class="form-check-input me-1"
                  type="radio"
                  v-model="data[field]"
                >
                <label
                  class="form-check-label"
                  :for="'overtimeCompensatory' + index.toString() + itemIndex.toString()"
                >
                  {{ item.name }}
                </label>
              </li>
            </ul>
          </template>
        </Column>

        <Column
          field="startTime"
          header="起始時間"
          headerClass="bg-light"
        >
          <template #body="{ data, field }">
            <div class="text-center">
              <select
                class="form-select d-inline-block w-auto"
                :value="(data[field].getDate() > overtimeDate.getDate()) ? 24 : data[field].getHours()"
                @input="onSelectHour($event, data, field)"
              >
                <template
                  v-for="hour in 24"
                  :key="hour - 1"
                >
                  <option :value="hour - 1">
                    {{ (hour - 1).toString().padStart(2, '0') }}
                  </option>
                </template>
              </select>

              <span class="mx-1">:</span>

              <select
                class="form-select d-inline-block w-auto"
                :value="(data[field].getDate() > overtimeDate.getDate()) ? 0 : data[field].getMinutes()"
                @input="onSelectMinute($event, data, field)"
              >
                <template v-if="data[field].getDate() > overtimeDate.getDate()">
                  <option :value="0">
                    00
                  </option>
                </template>
                <template v-else>
                  <template
                    v-for="minute in 60"
                    :key="minute - 1"
                  >
                    <option :value="minute - 1">
                      {{ (minute - 1).toString().padStart(2, '0') }}
                    </option>
                  </template>
                </template>
              </select>
            </div>
          </template>
        </Column>

        <Column
          field="endTime"
          header="截止時間"
          headerClass="bg-light"
        >
          <template #body="{ data, field }">
            <div class="text-center">
              <select
                class="form-select d-inline-block w-auto"
                :value="(data[field].getDate() > overtimeDate.getDate()) ? 24 : data[field].getHours()"
                @input="onSelectHour($event, data, field)"
              >
                <template
                  v-for="hour in 25"
                  :key="hour - 1"
                >
                  <option :value="hour - 1">
                    {{ (hour - 1).toString().padStart(2, '0') }}
                  </option>
                </template>
              </select>

              <span class="mx-1">:</span>

              <select
                class="form-select d-inline-block w-auto"
                :value="(data[field].getDate() > overtimeDate.getDate()) ? 0 : data[field].getMinutes()"
                @input="onSelectMinute($event, data, field)"
              >
                <template v-if="data[field].getDate() > overtimeDate.getDate()">
                  <option :value="0">
                    00
                  </option>
                </template>
                <template v-else>
                  <template
                    v-for="minute in 60"
                    :key="minute - 1"
                  >
                    <option :value="minute - 1">
                      {{ (minute - 1).toString().padStart(2, '0') }}
                    </option>
                  </template>
                </template>
              </select>
            </div>
          </template>
        </Column>

        <Column
          header="時數"
          class="text-center"
          headerClass="bg-light"
        >
          <template #body="{ data }">
            <template v-if="(data.endTime - data.startTime) >= 0">
              {{ Math.floor((data.endTime - data.startTime) / (1 * 60 * 60 * 1000)) }}
            </template>
            <template v-else>
              {{ Math.ceil((data.endTime - data.startTime) / (1 * 60 * 60 * 1000)) }}
            </template>
          </template>
        </Column>
      </DataTable>

      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1card">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>加班事由</span>
            </div>
          </div>
        </div>
        <div class="col-9 col-md-10 py-2 text-start">
          <div class="row">
            <div
              :class="[
                'col py-0',
                (shake.reason && reason.length === 0) ? 'shake' : ''
              ]"
            >
              <textarea
                v-model="reason"
                class="form-control w-100"
                :placeholder="'請填寫加班事由'"
                :maxlength="REASON_TEXTAREA_MAX"
                :disabled="(checkCanWorkOvertime === false) || (submitted === true)"
                @focusout="onTextareaFocusout"
              />
              <small class="text-secondary">最大可輸入字數：{{ REASON_TEXTAREA_MAX }}</small>
            </div>
          </div>
        </div>
      </div>

      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1card">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>加會人員</span>
            </div>
          </div>
        </div>
        <div :class="['col-sm-auto py-2', (signers.length > 0) ? 'col-2' : 'col-auto']">
          <button
            type="button"
            :class="[
              'btn me-1 p-2',
              (((checkCanWorkOvertime === false) || (submitted === true)) ? 'btn-outline-secondary' : 'btn-primary')
            ]"
            :disabled="(checkCanWorkOvertime === false) || (submitted === true)"
            @click="onAddSignerClick"
          >
            <span>新增加會人員</span>
          </button>
        </div>

        <div class="col py-2">
          <template
            v-for="(signer, index) in signers"
            :key="index"
          >
            <div class="row mb-2">
              <div class="col-auto col-md-1 text-md-center p-0">
                <span class="badge bg-secondary">
                  {{ index + 1 }}
                </span>
              </div>
              <div class="col col-sm-8 col-md-6 col-lg-4 pt-1">
                <ChooseColleague
                  :modelValue="signer.userId ? {
                    userId: signer.userId,
                    userName: signer.userName
                  } : null"
                  :customClass="(shake.signer && signer.userId === null) ? 'shake' : ''"
                  :disabled="submitted === true"
                  :employeeData="employeeData.filter((employeeEle) => ((signers.findIndex((signerEle) => signerEle?.userId ? (signerEle.userId === employeeEle.userId) : false) === -1) && (employee.userId !== employeeEle.userId)))"
                  :employeeFilter="employee.userId"
                  :signerFilter="employeeData.filter((employeeEle) => ((signers.findIndex((signerEle) => signerEle?.userId ? (signerEle.userId === employeeEle.userId) : false) !== -1)))"
                  :placeholder="'請輸入或選取加會人員'"
                  :filter="onSearchEmployeeData"
                  :clearable="true"
                  :alwaysShowClearButton="true"
                  @update:modelValue="onChangeSigner($event, index)"
                  @delete="onDeleteSigner(index)"
                />
              </div>
            </div>
          </template>
        </div>
      </div>

      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1card">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>上傳附件</span>
            </div>
          </div>
        </div>
        <div class="col-9 col-md-10 py-2 text-start">
          <AttachedFile
            :disabled="(checkCanWorkOvertime === false) || (submitted === true)"
            @update:modelValue="onUploadFiles"
          />
        </div>
      </div>

      <template v-if="userStore.userId === employee?.userId">
        <div class="row mx-0 text-start">
          <div class="col-12">
            <template v-if="overtimeData !== null">
              <OvertimeInfo
                :modelValue="{
                  card: FORM_ID.B1Card,
                  overtimeDateStatic: overtimeDateStatic,
                  overtimeData: overtimeData,
                  currentInfoNote: true
                }"
              />

              <template v-if="overtimeData.monthOvertimeStatics.TotalHours >= overtimeData.monthCloseToHoursUpperBoundValue">
                <div class="text-danger fs-5">
                  <i class="bi bi-exclamation-triangle" />
                  <template v-if="overtimeData.monthOvertimeStatics.TotalHours >= overtimeData.monthOverHoursUpperBoundValue">
                    {{ overtimeData.monthOverHoursUpperBoundMessage }}
                  </template>
                  <template v-else>
                    {{ overtimeData.monthCloseToHoursUpperBoundMessage }}
                  </template>
                </div>
              </template>
              <template v-else-if="overtimeData.quarterlyOvertimeStatics.TotalHours >= overtimeData.quarterOverHoursUpperBoundValue">
                <div class="text-danger fs-5">
                  <i class="bi bi-exclamation-triangle" />
                  {{ overtimeData.quarterOverHoursUpperBoundMessage }}
                </div>
              </template>
            </template>
          </div>

          <div class="col-12">
            <span>※ 當月/當季加班總時數說明：</span>
            <ol class="mb-1">
              <li>
                <span>當月/當季加班時數為加班卡已同意＋簽核中時數總和，按勞基法規定不計入週間國定假日、補假日及週間天災日前8小時加班時數。</span>
              </li>
              <li>
                <span>當季加班時數係以每年1月起算類推，計算週期為1-3月、4-6月、7-9月及10-12月。</span>
              </li>
            </ol>
          </div>
        </div>
      </template>
    </div>
  </div>

  <div class="row mt-2 mx-0">
    <div class="col text-center">
      <button
        type="button"
        :class="[
          'btn mx-2',
          ((checkCanWorkOvertime === false) || (submitted === true) ? 'btn-outline-secondary' : 'btn-primary')
        ]"
        :disabled="(checkCanWorkOvertime === false) || (submitted === true)"
        @click="onSubmit"
      >
        <span>送出</span>
      </button>
      <button
        type="button"
        :class="[
          'btn mx-2',
          (((resultData.length === 0) || (submitted === true)) ? 'btn-outline-secondary' : 'btn-secondary')
        ]"
        :disabled="(resultData.length === 0)|| (submitted === true)"
        @click="onResetForm"
      >
        <span>重填</span>
      </button>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'
import { useAuthUserStore } from '../store/index'
import { useMessageStore } from '../store/message'
import { useOvertimeCompensatory } from '../composable/overtimeCompensatory'
import { useOvertimeData } from '../composable/overtimeData'
import { useWorkday } from '../composable/workdays'
import { useProject } from '../composable/project'
import { useEmployeeData } from '../composable/employeeData'
import { useCanWorkOvertimeEmployeeData } from '../composable/canWorkOvertimeEmployeeData'
import { useAddSigner } from '../composable/signer'
import { useAbortController } from '../composable/abortController'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import { GET_DATECANFILLB1CARD_URL, POST_SUBMITB1CARD_URL } from '../api/appUrl'
import { SHAKE_RESIST_TIME, SMALL_DATE_TIME, SYSTEM_ERROR_MESSAGE, REASON_TEXTAREA_MAX, SIGNERS_MAX, FORM_ID } from '../api/appConst'
import type { EmployeeStoreBaseType, SignerType, ProjectType, UploadedFileType, MonthType, B1CardSubmitType } from '../api/appType'
import { getCalendarDayClass, onSearchEmployeeData } from '../api/appFunction'
import ChooseUser from '../components/ChooseUser.vue'
import ChooseColleague from '../components/ChooseColleague.vue'
import ProjectSelect from '../components/ProjectSelect.vue'
import RocCalendarSelect from '../components/RocCalendarSelect.vue'
import AttachedFile from '../components/AttachedFile.vue'
import OvertimeInfo from '../components/OvertimeInfo.vue'
import { onBeforeRouteLeave } from 'vue-router'
import { routerExtend } from '../router'

const userStore = useAuthUserStore()
const confirm = useConfirm()
const toast = useToast()
const { overtimeCompensatory, onGetOvertimeCompensatory } = useOvertimeCompensatory()
const { overtimeDateStatic, overtimeDate, overtimeData, onSetOvertimeDate, onGetOvertimeData } = useOvertimeData()
const { workdays, calendarLoaded, onLoadCalendarData } = useWorkday()
const { projectData, onGetProjectsDateRange } = useProject()
const { employeeData, onSetEmployeeData } = useEmployeeData()
const { canWorkOvertimeEmployeeData, onSetCanWorkOvertimeEmployeeData } = useCanWorkOvertimeEmployeeData()
const { signers, signersString, onChangeSigner, onChangeSigners, onAddSigner, onDeleteSigner, onDeleteSigners, onCheckSigner } = useAddSigner()
const { abortController, fetchErrorHandler, abortListener } = useAbortController()

/** 前端開始填表時間 */
const filledTime: Date = new Date()

const errorMessage = ref<{
  date: string
  endTimeMustLaterThanStartTime: string
}>({
  date: '',
  endTimeMustLaterThanStartTime: ''
})
const shake = ref<{
  date: boolean
  project: boolean
  reason: boolean
  signer: boolean
}>({
  date: false,
  project: false,
  reason: false,
  signer: false
})

const checkCanWorkOvertime = ref<boolean>(false)
const submitted = ref<boolean>(false)

const employee = ref<EmployeeStoreBaseType>({
  userId: userStore.userId,
  userName: userStore.userName,
  deptNo: userStore.deptNo,
  deptSName: userStore.deptSName
})
const project = ref<ProjectType | null>(null)
const reason = ref<string>('')
const resultData = ref<Array<{
  projectId: string
  overtimeCompensatory: number
  startTime: Date
  endTime: Date
}>>([])
const uploadedFiles = ref<Array<UploadedFileType>>([])
const b1CardApp = ref<{
  formID: string | null
  formNo: string | null
  formUID: string | null
  flowStatus: number | null,
  applyEmpNo: string | null
}>({
  formID: null,
  formNo: null,
  formUID: null,
  flowStatus: null,
  applyEmpNo: null
})

/**
 * 切換申請人
 * @param event 單一名員工的資料
 */
const onChangeEmployee = (event: EmployeeStoreBaseType): void => {
  const tempEmployee = employee.value
  const tempB1CardApp = b1CardApp.value
  const tempResultData = resultData.value
  const tempReason = reason.value
  const tempProject = project.value
  const tempErrorMessage = errorMessage.value
  const tempOvertimeData = overtimeData.value
  const tempSigners = signers.value

  employee.value = {
    userId: event.userId,
    userName: event.userName,
    deptNo: event.deptNo,
    deptSName: event.deptSName
  }
  b1CardApp.value = {
    formID: null,
    formNo: null,
    formUID: null,
    flowStatus: null,
    applyEmpNo: null
  }
  resultData.value = []
  reason.value = ''
  project.value = null
  errorMessage.value = {
    date: '',
    endTimeMustLaterThanStartTime: ''
  }
  overtimeData.value = null
  signers.value = []

  if (tempResultData.length > 0) {
    confirm.require({
      group: 'app',
      header: '提醒',
      message: '目前填報計畫的資料將被清空，是否繼續？',
      acceptProps: {
        severity: 'primary'
      },
      rejectProps: {
        severity: 'secondary'
      },
      accept: (): void => {
        onLoadOvertimeCard()
      },
      reject: (): void => {
        employee.value = tempEmployee
        b1CardApp.value = tempB1CardApp
        resultData.value = tempResultData
        reason.value = tempReason
        project.value = tempProject
        errorMessage.value = tempErrorMessage
        overtimeData.value = tempOvertimeData
        signers.value = tempSigners
      }
    })
  } else if (signers.value.find((e: SignerType) => e.userId === event.userId) !== undefined) {
    const tempAddSigner = signers.value
    onDeleteSigners()

    confirm.require({
      group: 'app',
      header: '提醒',
      message: '目前加會人員的資料將被清空，是否繼續？',
      acceptProps: {
        severity: 'primary'
      },
      rejectProps: {
        severity: 'secondary'
      },
      accept: (): void => {
        onLoadOvertimeCard()
      },
      reject: (): void => {
        employee.value = tempEmployee
        resultData.value = tempResultData
        project.value = tempProject
        errorMessage.value = tempErrorMessage
        onChangeSigners(tempAddSigner)
      }
    })
  } else if (onCheckSigner() === true) {
    onDeleteSigners()
  } else {
    onLoadOvertimeCard()
  }
}

const onAddRow = (): void => {
  if (project.value !== null) {
    resultData.value.push({
      projectId: project.value.id ?? '',
      overtimeCompensatory: 1,
      startTime: new Date(overtimeDate.value.getFullYear(), overtimeDate.value.getMonth(), overtimeDate.value.getDate(), 0, 0), // 精度到分鐘
      endTime: new Date(overtimeDate.value.getFullYear(), overtimeDate.value.getMonth(), overtimeDate.value.getDate(), 0, 0) // 精度到分鐘
    })
  } else {
    toast.add({
      severity: 'error',
      summary: '系統發生例外狀況',
      group: 'app'
    })
  }

  project.value = null
}

const onDeleteRow = (index: number): void => {
  confirm.require({
    group: 'app',
    header: '提醒',
    message: '確認刪除？',
    acceptProps: {
      severity: 'primary'
    },
    rejectProps: {
      severity: 'secondary'
    },
    accept: (): void => {
      const removed: Array<{
        projectId: string
        overtimeCompensatory: number
        startTime: Date
        endTime: Date
      }> = resultData.value.splice(index, 1)
      if (removed.length > 0) {
        toast.add({
          severity: 'success',
          summary: '刪除成功',
          group: 'app'
        })
      } else {
        toast.add({
          severity: 'error',
          summary: '系統發生例外狀況，刪除失敗',
          group: 'app'
        })
      }
    }
  })
}

const onSelectHour = (event: Event, data: any, field: string): void => {
  if ((event.target as HTMLSelectElement).value.toString() === '24') {
    data[field] = new Date(overtimeDate.value.getFullYear(), overtimeDate.value.getMonth(), overtimeDate.value.getDate() + 1, 0, 0)
  } else {
    data[field] = new Date(overtimeDate.value.getFullYear(), overtimeDate.value.getMonth(), overtimeDate.value.getDate(), parseInt((event.target as HTMLSelectElement).value, 10), data[field].getMinutes())
  }
}

const onSelectMinute = (event: Event, data: any, field: string): void => {
  data[field] = new Date(overtimeDate.value.getFullYear(), overtimeDate.value.getMonth(), overtimeDate.value.getDate(), data[field].getHours(), parseInt((event.target as HTMLSelectElement).value, 10))
}

const onCheckInitTime = (): string => {
  let msg = ''
  resultData.value.some((e: any) => {
    if (overtimeDate.value.getDate() === e.startTime.getDate() && overtimeDate.value.getDate() === e.endTime.getDate()) {
      if (e.endTime.getHours() + e.endTime.getMinutes() === 0) {
        msg = errorMessage.value.endTimeMustLaterThanStartTime
        return true
      }
    }
  })
  return msg
}

const onClickDate = async (): Promise<void> => {
  const thisMonthFirstDate = new Date(overtimeDate.value.getFullYear(), overtimeDate.value.getMonth(), 1)
  const nextMonthFirstDate = new Date(overtimeDate.value.getFullYear(), overtimeDate.value.getMonth() + 1, 0)
  const startDate = new Date(overtimeDate.value.getFullYear(), overtimeDate.value.getMonth(), 1 - thisMonthFirstDate.getDay())
  const endDate = new Date(overtimeDate.value.getFullYear(), overtimeDate.value.getMonth() + 1, 6 - nextMonthFirstDate.getDay())

  try {
    await onLoadCalendarData(startDate, endDate, employee.value.userId, abortController.signal)
  } catch (err: unknown) {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  }
}

const onChangeDate = (event: Date): void => {
  if (event >= SMALL_DATE_TIME.floor && event < SMALL_DATE_TIME.ceil) {
    const tempDate = overtimeDate.value
    const tempB1CardApp = b1CardApp.value
    const tempResultData = resultData.value
    const tempReason = reason.value
    const tempProject = project.value
    const tempErrorMessage = errorMessage.value
    const tempOvertimeData = overtimeData.value

    onSetOvertimeDate(event)
    b1CardApp.value = {
      formID: null,
      formNo: null,
      formUID: null,
      flowStatus: null,
      applyEmpNo: null
    }
    resultData.value = []
    reason.value = ''
    project.value = null
    errorMessage.value = {
      date: '',
      endTimeMustLaterThanStartTime: ''
    }
    overtimeData.value = null

    if (tempResultData.length > 0) {
      confirm.require({
        group: 'app',
        header: '提醒',
        message: '目前填報計畫的資料將被清空，是否繼續？',
        acceptProps: {
          severity: 'primary'
        },
        rejectProps: {
          severity: 'secondary'
        },
        accept: (): void => {
          onLoadOvertimeCard()
        },
        reject: (): void => {
          onSetOvertimeDate(tempDate)
          b1CardApp.value = tempB1CardApp
          resultData.value = tempResultData
          reason.value = tempReason
          project.value = tempProject
          errorMessage.value = tempErrorMessage
          overtimeData.value = tempOvertimeData
        }
      })
    } else {
      onLoadOvertimeCard()
    }
  } else {
    toast.add({
      severity: 'warn',
      summary: '系統不支援該日期',
      group: 'app'
    })
  }
}

const onChangeYearMonth = async (event: MonthType): Promise<void> => {
  const thisMonthFirstDate = new Date(event.year, event.month, 1)
  const nextMonthFirstDate = new Date(event.year, event.month + 1, 0)
  const startDate = new Date(event.year, event.month, 1 - thisMonthFirstDate.getDay())
  const endDate = new Date(event.year, event.month + 1, 6 - nextMonthFirstDate.getDay())

  try {
    await onLoadCalendarData(startDate, endDate, employee.value.userId, abortController.signal)
  } catch (err: unknown) {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  }
}

const onSelectProject = (projectData: ProjectType) => {
  project.value = projectData
}

const onDeleteProject = (): void => {
  project.value = null
}

const onAddSignerClick = (): void => {
  if (signers.value.find((e: SignerType) => e.userId === null)) {
    toast.add({
      severity: 'warn',
      summary: '請先輸入加會人員',
      group: 'app'
    })
  } else if (signers.value.length >= SIGNERS_MAX) {
    toast.add({
      severity: 'warn',
      summary: '僅開放最多加會' + SIGNERS_MAX + '名人員',
      group: 'app'
    })
  } else {
    onAddSigner()
  }
}

const onUploadFiles = (event: Array<UploadedFileType>): void => {
  uploadedFiles.value = event
}

const onSubmit = (): void => {
  let checkSubmitted: number = 0
  submitted.value = true
  confirm.require({
    group: 'app',
    header: '提醒',
    message: '確認送出表單？',
    acceptProps: {
      severity: 'primary'
    },
    rejectProps: {
      severity: 'secondary'
    },
    accept: (): void => {
      checkSubmitted++
      if (checkSubmitted === 1) {
        const checkInitTimeMsg = onCheckInitTime()
        if (onCheckSigner() === true) {
          shake.value.signer = true
          toast.add({
            severity: 'warn',
            summary: '請輸入或選取加會人員',
            group: 'app'
          })
          submitted.value = false
          setTimeout((): void => {
            shake.value.signer = false
          }, SHAKE_RESIST_TIME)
        } else if (checkInitTimeMsg.length > 0) {
          toast.add({
            severity: 'warn',
            summary: checkInitTimeMsg,
            group: 'app'
          })
          submitted.value = false
        } else {
          const sentTime: Date = new Date() // 前端按送出的時間
          const b1_Details: Array<{
            project: string
            b1_CODE: number
            startTime: Date
            endTime: Date
          }> = resultData.value.map((e: {
            projectId: string
            overtimeCompensatory: number
            startTime: Date
            endTime: Date
          }) => {
            return {
              project: e.projectId,
              b1_CODE: e.overtimeCompensatory,
              startTime: e.startTime,
              endTime: e.endTime
            }
          })

          const postData: B1CardSubmitType = {
            empNo: employee.value.userId,
            reason: reason.value,
            sheetNo: '',
            b1_WDate: sentTime,
            overTimeDate: overtimeDate.value,
            details: b1_Details,
            addSigners: signersString.value,
            createdTime: sentTime,
            filledTime: filledTime,
            uploadedFiles: uploadedFiles.value
          }

          fetch(POST_SUBMITB1CARD_URL, {
            method: 'POST',
            headers: {
              'content-type': 'application/json'
            },
            body: JSON.stringify(postData),
            signal: abortController.signal
          }).then((res: Response): Promise<any> => {
            if (!res.ok) {
              throw new Error(res.status.toString())
            }
            return res.json()
          }).then((res: any): void => {
            const errorMsg: string = res.ErrorMessage
            if (errorMsg.length === 0) {
              const messageStore = useMessageStore()
              messageStore.setData('表單已送出')

              routerExtend.pushHandler('Message')
            } else {
              toast.add({
                severity: 'warn',
                summary: errorMsg,
                group: 'app'
              })
            }
          }).catch((err: Error): void => {
            console.error(err)
            fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
          }).finally((): void => {
            submitted.value = false
          })
        }
      }
    },
    reject: (): void => {
      submitted.value = false
    }
  })
}

const onResetForm = (): void => {
  submitted.value = true
  confirm.require({
    group: 'app',
    header: '提醒',
    message: '確認重置表單？',
    acceptProps: {
      severity: 'primary'
    },
    rejectProps: {
      severity: 'secondary'
    },
    accept: (): void => {
      toast.add({
        severity: 'success',
        summary: '表單已重置',
        group: 'app'
      })

      employee.value = {
        userId: userStore.userId,
        userName: userStore.userName,
        deptNo: userStore.deptNo,
        deptSName: userStore.deptSName
      }
      project.value = null
      resultData.value = []
      reason.value = ''
      onSetOvertimeDate(new Date())
      onLoadOvertimeCard()
      submitted.value = false
    },
    reject: (): void => {
      submitted.value = false
    }
  })
}

/**
 * 查詢該日期是否可以填報加班卡 & 取得前端填報時需要即時反應的錯誤訊息
 */
const onLoadDateCanFillCard = async (): Promise<void> => {
  const params = new URLSearchParams({
    empNo: employee.value.userId,
    date: overtimeDate.value.toISOString()
  })
  await fetch(GET_DATECANFILLB1CARD_URL + '?' + params, {
    method: 'GET',
    signal: abortController.signal
  }).then((res: Response): Promise<any> => {
    if (!res.ok) {
      throw new Error(res.status.toString())
    }
    return res.json()
  }).then((res: any): void => {
    errorMessage.value.date = res.ErrorMessage
    errorMessage.value.endTimeMustLaterThanStartTime = res.EndTimeMustLaterThanStartTime
    checkCanWorkOvertime.value = res.CanOvertime

    if (checkCanWorkOvertime.value === false && errorMessage.value.date.length > 0) {
      signers.value = []
      shake.value.date = true
      setTimeout((): void => {
        shake.value.date = false
      }, SHAKE_RESIST_TIME)
    }
  }).catch((err: Error): void => {
    console.error(err)
    const checkRedirect = fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
    if (checkRedirect === false) {
      checkCanWorkOvertime.value = false
      errorMessage.value.date = SYSTEM_ERROR_MESSAGE
    }
  })
}

const onLoadOvertimeData = async (): Promise<void> => {
  try {
    await onGetOvertimeData(employee.value.userId, abortController.signal)

    if (overtimeData.value?.b1CardApp?.B1_FormID) {
      b1CardApp.value = {
        formID: overtimeData.value.b1CardApp.Name,
        formNo: overtimeData.value.b1CardApp.B1_FormID,
        formUID: overtimeData.value.b1CardApp.FormUID,
        flowStatus: overtimeData.value.b1CardApp.B1_Status,
        applyEmpNo: overtimeData.value.b1CardApp.B1_WritedEmpNo
      }
      reason.value = overtimeData.value?.b1CardApp?.B1_Reason ?? ''
      const projectFound = projectData.value.find((e: ProjectType) => e.id === overtimeData.value?.b1CardApp.B1_PrjNo)
      if (projectFound === undefined) {
        project.value = null
      } else {
        project.value = projectFound
      }
    } else {
      b1CardApp.value = {
        formID: null,
        formNo: null,
        formUID: null,
        flowStatus: null,
        applyEmpNo: null
      }
    }
  } catch (err: unknown) {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  }
}

const onLoadOvertimeCard = (): void => {
  Promise.all([
    onSetCanWorkOvertimeEmployeeData(userStore.deptNo, abortController.signal),
    onSetEmployeeData(employee.value.deptNo, abortController.signal),
    onLoadProjectsDateRange(),
    onLoadDateCanFillCard(),
    onLoadOvertimeData()
  ]).catch((err: Error): void => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })
}

const onLoadOvertimeCompensatory = async (): Promise<void> => {
  try {
    await onGetOvertimeCompensatory(abortController.signal)
  } catch (err: unknown) {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  }
}

const onLoadProjectsDateRange = async (): Promise<void> => {
  try {
    await onGetProjectsDateRange(overtimeDate.value, overtimeDate.value, employee.value.deptNo, abortController.signal)
  } catch (err: unknown) {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  }
}

const onTextareaFocusout = (): void => {
  const temp = reason.value.trim()
  if (temp.length === 0) {
    reason.value = temp
  }
}

const onKeydownEditLink = (href: string): void => {
  window.open(href, '_blank')
}

const onClickEditLink = (href: string): void => {
  window.open(href, '_blank')
}

onBeforeRouteLeave((): void => {
  toast.removeGroup('app')
})

onMounted(async (): Promise<void> => {
  abortListener()
  onSetOvertimeDate(filledTime)
  onSetCanWorkOvertimeEmployeeData(userStore.deptNo, abortController.signal)
  onSetEmployeeData(employee.value.deptNo, abortController.signal)
  await onLoadProjectsDateRange() 
  await Promise.all([
    onLoadDateCanFillCard(),
    onLoadOvertimeData(),
    onLoadOvertimeCompensatory()
  ])
})
</script>
<style lang="scss" scoped>
@use '../assets/scss/calendar';
:deep(.p-datatable .p-column-header-content) {
  justify-content: center;
  color: black;
}
</style>