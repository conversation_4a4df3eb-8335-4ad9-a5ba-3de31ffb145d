﻿using Newtonsoft.Json;
using Sinotech.Mis.Common;
using Sinotech.Mis.Helpers;
using Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.DataAccess.Interfaces;
using Sinotech.Mis.HR.Attendance.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Runtime.Caching;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic
{
    /// <summary>
    /// 出勤 商業物件
    /// </summary>
    public class AttendanceBo : IAttendanceBo
    {

        private static ObjectCache _cache = MemoryCache.Default;
        private static readonly object _cacheLock = new();
        private static Dictionary<int, string>? _formFlowStatusNames = null;
        private static Dictionary<int, string>? _formStatusNames = null;
        private readonly IAttendanceDao _attendanceDao;
        private readonly IDepartmentBo _departmentBo;
        private readonly IEmployeeBo _employeeBo;
        private readonly ISinoSignBo _sinoSignBo;
        private readonly IWorkdayBo _workdayBo;

        /// <summary>
        ///   <see cref="AttendanceBo" /> 的建構函式</summary>
        /// <param name="attendanceDao">The attendance DAO.</param>
        /// <param name="employeeBo">The Employee 商業物件.</param>
        /// <param name="sinoSignBo">SinoSign 商業物件.</param>
        /// <param name="workdayBo">The Workday 商業物件.</param>
        public AttendanceBo(IAttendanceDao attendanceDao, IDepartmentBo departmentBo, 
            IEmployeeBo employeeBo, ISinoSignBo sinoSignBo, IWorkdayBo workdayBo)
        {
            _attendanceDao = attendanceDao;
            _departmentBo = departmentBo;
            _employeeBo = employeeBo;
            _sinoSignBo = sinoSignBo;
            _workdayBo = workdayBo;
        }

        /// <summary>
        /// 取得所有管理員名單
        /// </summary>
        /// <returns></returns>
        private DataTable GetAdmins()
        {
            const string cacheName = "dtAdmins";
            if (_cache.Contains(cacheName))
            {
                return (DataTable)_cache[cacheName];
            }
            DataTable admins = _attendanceDao.GetAdministrators();
            lock (_cacheLock)
            {
                _cache.Set(cacheName, admins, CachePolicy);
            }
            return admins;
        }

        /// <summary>
        /// 快取2分鐘
        /// </summary>
        private static CacheItemPolicy CachePolicy
        {
            get
            {
                CacheItemPolicy policy = new CacheItemPolicy();
                policy.AbsoluteExpiration = DateTimeOffset.Now.AddMinutes(2.0);
                return policy;
            }
        }

        /// <summary>
        /// 快取60秒
        /// </summary>
        private static CacheItemPolicy CachePolicy60Seconds
        {
            get
            {
                CacheItemPolicy policy = new CacheItemPolicy();
                policy.AbsoluteExpiration = DateTimeOffset.Now.AddSeconds(60.0);
                return policy;
            }
        }

        /// <summary>
        /// 是否能看出勤資料
        /// </summary>
        /// <param name="watcherNo">觀看者員工編號</param>
        /// <param name="empNo">被看者員工編號</param>
        /// <returns></returns>
        public bool CanSeeAttendance(string watcherNo, string empNo)
        {
            bool canSee = false;
            // 觀看者 為 被看者
            if (watcherNo == empNo)
            {
                return true;
            }

            // 管理員可以看所有人
            if (IsAdmin(watcherNo))
            {
                return true;
            }

            // 觀看者 為 被看者組長
            if (_employeeBo.IsTeamLeaderOf(watcherNo, empNo))
            {
                return true;
            }
            Employee? watcher = _employeeBo.GetEmployeeDetail(watcherNo);
            Employee? employee = _employeeBo.GetEmployeeDetail(empNo);
            if (watcher == null || employee == null)
            {
                return false;
            }

            // 觀看者 為 治理部門
            if (watcher.DeptNo == 1)
            {
                return true;
            }

            // 觀看者 為 被看者部門主管
            if (watcher.IsAboveDeputyManager && watcher.DeptNo == employee.DeptNo)
            {
                return true;
            }

            return canSee;
        }

        /// <summary>
        /// 新增表單
        /// </summary>
        /// <param name="formID">表單名稱</param>
        /// <param name="chineseYear">ROC年度</param>
        /// <param name="dataSet">包含所有待新增資料表的 Data Set</param>
        /// <returns>(單號，錯誤訊息)</returns>
        public (string?, string) AddForm(string formID, int chineseYear, DataSet dataSet)
        {
            var (formNo, errorMessage) = _attendanceDao.AddForm(formID, chineseYear, dataSet);
            return (formNo, errorMessage);
        }

        /// <summary>是否可以填加班</summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="date">日期</param>
        /// <returns>傳回B1CardAppCheckResult</returns>
        public B1CardAppCheckResult IsOvertimeAllowed(string empNo, DateTime date)
        {
            B1CardAppCheckResult ret = new B1CardAppCheckResult();
            ret.IsValid = false;
            ret.IsOvertimeAllowed = false;
            if (string.IsNullOrWhiteSpace(empNo) || !_employeeBo.IsEmployee(empNo))
            {
                ret.UserErrorMessage = AttendanceParameters.BadEmployeeNumber;
                ret.ErrorMessage = AttendanceParameters.BadEmployeeNumber;
                return ret;
            }
            string cacheName = $"IsOvertimeAllowed-{empNo}-{date.ToString("yyyyMMdd")}";
            if (_cache.Contains(cacheName))
            {
                ret = (B1CardAppCheckResult)_cache[cacheName];
            }
            else
            {
                B1CardParameters b1CardParameters = new B1CardParameters();
                b1CardParameters.OvertimeDate = date;
                b1CardParameters.IsFilledB1CardApp = true;
                B1Card b1Card = new();
                b1Card.EmpNo = empNo;
                Employee? employeeDetail = _employeeBo.GetEmployeeDetail(empNo);
                if (employeeDetail == null)
                {
                    ret.UserErrorMessage = AttendanceParameters.BadEmployeeNumber;
                    ret.ErrorMessage = AttendanceParameters.BadEmployeeNumber;
                    return ret;
                }
                b1CardParameters.EmployeeDetail = employeeDetail;
                b1CardParameters.EmployeePosition = GetPositionType(empNo);
                b1CardParameters.B1Card = b1Card;
                var checker = new B1CardChecker(b1CardParameters);
                var b1CardResult = checker.IsOvertimeAllowed();
                if (b1CardResult.Code != 0)
                {
                    ret.ErrorMessage = b1CardResult.Message;
                }
                else
                {
                    ret.IsOvertimeAllowed = true;
                    ret.IsValid = true;
                }

                lock (_cacheLock)
                {
                    _cache.Set(cacheName, ret, CachePolicy);
                }
            }
            return ret;
        }

        /// <summary>
        /// 結案抽單
        /// </summary>
        /// <param name="withdraw">抽單物件</param>
        /// <returns>抽單成功傳回空值，失敗傳回錯誤訊息</returns>
        public string ClosedWithdraw(Withdraw withdraw)
        {
            string errorMessage = _attendanceDao.ClosedWithdraw(withdraw);
            return errorMessage;
        }

        /// <summary>
        /// 紀錄第一次讀取通知的時間
        /// </summary>
        /// <param name="id">通知編號</param>
        /// <returns>紀錄第一次讀取通知的結果是成功或失敗</returns>
        public bool DeliveredNotifications(int id)
        {
            return _attendanceDao.DeliveredNotifications(id);
        }


        /// <summary>
        /// 依指定員工身分，取得該員在【三卡首頁】可查詢的資料
        /// 
        /// 員工身分	                                     可查詢資料範圍
        /// 系統管理者（業務承辦主管、業務承辦人、系統開發者）	 全社員工資料
        /// 治理部門正、副主管	                              全社員工資料
        /// 稽核室正、副主管	                              全社員工資料
        /// 行政處正、副主管	                              全社員工資料
        /// 部門正、副主管	                                  管轄部門的員工資料
        /// 組長		                                      管轄組別的員工資料
        /// 一般同仁		                                  個人資料
        /// </summary>
        /// <returns>部門編號、部門簡稱、組別編號、組別名稱、員工編號、員工姓名</returns>
        public List<DeptTeam> GetAllowedQueryEmployees(string empNo)
        {
            bool isAdmin = IsAdmin(empNo);
            var employee = _employeeBo.GetEmployeeDetail(empNo); // 取得員工詳細資料
            List<DeptTeam> result = new List<DeptTeam>(); // 用來存放可查詢的員工資料

            if (employee == null)
            {
                return result;
            }

            // 系統管理者可查詢全社員工資料
            if (isAdmin)
            {
                result = _departmentBo.GetDepartmentsTeamsEmployees();
                return result;
            }

            // 治理部門 1、行政處 2、稽核室 15的正副主管可查詢全社員工資料
            if (employee.IsAboveDeputyManager && (employee.DeptNo == 1 || employee.DeptNo == 2 || employee.DeptNo == 15))
            {
                result = _departmentBo.GetDepartmentsTeamsEmployees();
                return result;
            }

            // 部門正、副主管可查詢管轄部門的員工資料
            if (employee.IsAboveDeputyManager)
            {
                List<int> deptNos = _employeeBo.GetAboveDeputyManagerDeptNos(empNo);
                foreach (int deptNo in deptNos)
                {
                    List<DeptTeam> deptEmps = _departmentBo.GetDepartmentTeamsEmployees(deptNo);
                    result.AddRange(deptEmps);
                }
            }

            // 組長可查詢管轄組別的員工資料
            if (employee.IsTeamLeader)
            {
                List<int> teamNos = _employeeBo.GetTeamLeaderTeamNos(empNo);
foreach(int teamId in teamNos)
                {
                    List<DeptTeam> resultTeam = _departmentBo.GetDepartmentTeamEmployees(employee.DeptNo, teamId);
                    foreach (var deptTeam in resultTeam)
                    {
                        if (!result.Contains(deptTeam))
                        {
                            result.Add(deptTeam);
                        }
                    }
                }
            }

            if (result.Count == 0)
            {
                // 一般同仁只能查詢個人資料
                var onePerson = new List<DeptTeam>();
                var deptTeams = _departmentBo.GetDepartmentTeamsEmployees(employee.DeptNo);
                var onesTeam = deptTeams
                    .Where(deptTeam => deptTeam.TeamMembers.Any(e => e.EmpNo == empNo))
                    .Take(1)
                    .ToList();
                if (onesTeam != null && onesTeam.Count > 0)
                {
                    var oneTeamMember = onesTeam[0].TeamMembers
                        .Where(member => member.EmpNo == empNo)
                        .ToList();
                    if (oneTeamMember != null)
                    {
                        var deptTeam = onesTeam[0];
                        var filteredDeptTeam = new DeptTeam
                        {
                            DeptNo = deptTeam.DeptNo,
                            DeptSName = deptTeam.DeptSName,
                            TeamCName = deptTeam.TeamCName,
                            TeamID = deptTeam.TeamID,
                            TeamMembers = oneTeamMember
                        };
                        onePerson.Add(filteredDeptTeam);
                    }
                }
                DeptTeam? oneTeam = onePerson.FirstOrDefault();
                if (oneTeam != null && !result.Contains(oneTeam))
                {
                    result.Add(oneTeam);
                }
            }
            return result;
        }

        /// <summary>
        /// 依指定員工身分，取得該員在【部門填報紀錄】可查詢的資料
        /// 
        /// 員工身分	                                     可查詢資料範圍
        /// 治理部門正、副主管	                              全社員工資料
        /// 部門正、副主管	                                  管轄部門的員工資料
        /// 部門登記桌	                                      所屬部門的員工資料
        /// </summary>
        /// <returns>部門編號、部門簡稱、組別編號、組別名稱、員工編號、員工姓名</returns>
        public List<DeptTeam> GetAllowedQueryDepartmentSentBoxEmployees(string empNo)
        {
            var employee = _employeeBo.GetEmployeeDetail(empNo); // 取得員工詳細資料
            List<DeptTeam> result = new List<DeptTeam>(); // 用來存放可查詢的員工資料s
            List<int> deptsList = new List<int>(); // 用來存放可查詢的部門編號
            if (employee != null)
            {
                // 治理部門 1的正副主管可查詢全社員工資料
                if (employee.IsAboveDeputyManager && employee.DeptNo == 1)
                {
                    result = _departmentBo.GetDepartmentsTeamsEmployees();
                    return result;
                }

                // 部門正、副主管可查詢管轄部門的員工資料
                if (employee.IsAboveDeputyManager)
                {
                    List<int> deptNos = _employeeBo.GetAboveDeputyManagerDeptNos(empNo);
                    deptsList.AddRange(deptNos);
                }

                // 部門登記桌可查詢所屬部門的員工資料
                List<Role> roles = _sinoSignBo.GetUserRoles(empNo);

                IEnumerable<Role> query = roles.Where(x => x.RoleId.Length == 2);

                if (query.Count() > 0) // 角色為登記桌
                {
                    foreach (Role role in query)
                    {
                        if (int.TryParse(role.RoleId, out int deptNo))
                        {
                            if(!deptsList.Contains(deptNo))
                            {
                                deptsList.Add(deptNo);
                            }
                        }
                    }
                }

                if (deptsList.Count > 0)
                {
                    foreach (int deptNo in deptsList)
                    {
                        List<DeptTeam> deptEmps = _departmentBo.GetDepartmentTeamsEmployees(deptNo);
                        result.AddRange(deptEmps);
                    }

                }
            }
            return result;
        }

        /// <summary>
        /// 依指定員工身分，取得該員在【部門填報紀錄】能否查詢全社資料
        /// 
        /// 員工身分	                                     可查詢資料範圍
        /// 治理部門正、副主管	                              全社員工資料
        /// </summary>
        /// <returns></returns>
        public bool IsAllowedQueryAllDepartmentSentBox(string empNo)
        {
            var employee = _employeeBo.GetEmployeeDetail(empNo); // 取得員工詳細資料
            if (employee != null)
            {
                // 治理部門 1的正副主管可查詢全社員工資料
                if (employee.IsAboveDeputyManager && employee.DeptNo == 1)
                {   
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 取得全部已填表單，以填表日期為準
        /// </summary>
        /// <param name="startDate">最早填卡日期</param>
        /// <param name="endDate">最晚填卡日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns>已填表單</returns>
        public DataTable GetAllSentBox(DateTime startDate, DateTime endDate, int? status = null)
        {
            DataTable result = _attendanceDao.GetForms(startDate, endDate);
            if (status != null)
            {
                DataRow?[] rows = result.Select($"Status={status}");
                if (rows.Length > 0)
                {
                    DataTable dt = result.Clone(); // Clone the structure of the original DataTable
                    foreach (DataRow? row in rows)
                    {
                        if (row != null)
                        {
                            DataRow newRow = dt.NewRow();
                            newRow.ItemArray = row.ItemArray.Clone() as object[];
                            dt.Rows.Add(newRow);
                        }
                    }
                    result = dt;
                }
                else
                {
                    result.Clear();
                }
            }
            return result;
        }

        /// <summary>
        /// 取得全部已填表單，以填表日期為準
        /// </summary>
        /// <param name="startDate">最早填卡日期</param>
        /// <param name="endDate">最晚填卡日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>已填表單</returns>
        public DataTable GetAllSentBox(DateTime startDate, DateTime endDate, string projNo)
        {
            DataTable result = _attendanceDao.GetForms(startDate, endDate, projNo);
            return result;
        }

        /// <summary>
        /// 取得全部已填表單，以內容日期為準
        /// </summary>
        /// <param name="startDate">最早日期</param>
        /// <param name="endDate">最晚日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>已填表單</returns>
        public DataTable GetAllSentBoxByContentDate(DateTime startDate, DateTime endDate, string projNo)
        {
            DataTable result = _attendanceDao.GetFormsByContentDate(startDate, endDate, projNo);
            return result;
        }

        /// <summary>
        /// 取得全部已填表單，以內容日期為準
        /// </summary>
        /// <param name="startDate">最早日期</param>
        /// <param name="endDate">最晚日期</param>
        /// <param name="status">表單狀態，預設 null</param>
        /// <returns>已填表單</returns>
        public DataTable GetAllSentBoxByContentDate(DateTime startDate, DateTime endDate, int? status = null)
        {
            DataTable result = _attendanceDao.GetFormsByContentDate(startDate, endDate);
            if (status != null)
            {
                DataRow?[] rows = result.Select($"Status={status}");
                if (rows.Length > 0)
                {
                    DataTable dt = result.Clone(); // Clone the structure of the original DataTable
                    foreach (DataRow? row in rows)
                    {
                        if (row != null)
                        {
                            DataRow newRow = dt.NewRow();
                            newRow.ItemArray = row.ItemArray.Clone() as object[];
                            dt.Rows.Add(newRow);
                        }
                    }
                    result = dt;
                }
                else
                {
                    result.Clear();
                }
            }
            return result;
        }

        /// <summary>
        /// 取得表單附件
        /// </summary>
        /// <param name="formUID">表單Guid</param>
        /// <returns></returns>
        public DataTable GetAttachments(Guid formUID)
        {
            DataTable dt = _attendanceDao.GetAttachments(formUID);
            return dt;
        }

        /// <summary>
        /// 取得可加班員工DataTable
        /// 會將優先部門放在前面
        /// </summary>
        ///<param name="deptNo">優先部門代號，預設 -1 表示不將優先部門置前</param>
        /// <returns>可加班員工DataTable</returns>
        public DataTable GetEligibleOvertimeEmployeesDataTable(int deptNo = -1)
        {
            DataTable ret;
            string cacheName = $"EligibleOvertimeEmployeesDataTable-{deptNo}";
            if (_cache.Contains(cacheName))
            {
                ret = (DataTable)_cache[cacheName];
            }
            else
            {
                DataTable dt = _employeeBo.GetEmployeeDataTable(deptNo);
                DataTable dtNew = dt.Clone();
                foreach (DataRow row in dt.Rows)
                {
                    string empNo = (string)row["EmpNo"];
                    if (IsOvertimeAllowed(empNo, DateTime.Now).IsOvertimeAllowed)
                    {
                        DataRow newRow = dtNew.NewRow();
#pragma warning disable CS8601 // Possible null reference assignment. 此處為Sonar Lint 誤判
                        newRow.ItemArray = row.ItemArray.Clone() as object[];
#pragma warning restore CS8601 // Possible null reference assignment.
                        dtNew.Rows.Add(newRow);
                    }
                }
                lock (_cacheLock)
                {
                    _cache.Set(cacheName, dtNew, CachePolicy);
                }
                ret = dtNew;
            }
            return ret;
        }

        /// <summary>
        /// 取得所有可加班現職員工詳細資料 JSON String
        /// 會將優先部門放在前面
        /// </summary>
        ///<param name="departmentNumber">優先部門代號，Smaller than 1 表示不將優先部門置前</param>
        /// <returns>所有可加班現職員工詳細資料 Json String</returns>
        public string GetEligibleOvertimeEmployeesJson(int departmentNumber)
        {
            DataTable dt = GetEligibleOvertimeEmployeesDataTable(departmentNumber);
            List<Employee> list = Employee.DataTableToList(dt);
            List<EmployeeSimple> eligibleOvertimeEmployees = EmployeeSimple.EmployeeToEmployeeSimple(list);
            return JsonConvert.SerializeObject(eligibleOvertimeEmployees);
        }

        /// <summary>
        /// 取得所有可加班現職員工詳細資料 JSON String
        /// </summary>
        /// <param name="sortField">排序欄位</param>
        /// <param name="ascDesc">升冪或降冪 ASC or DESC</param>
        /// <returns>所有可加班現職員工詳細資料 Json String</returns>
        public string GetEligibleOvertimeEmployeesJson(string sortField = "EmpNo", string ascDesc = "ASC")
        {
            DataTable dt = GetEligibleOvertimeEmployeesDataTable();
            if (dt != null && dt.Columns.Contains(sortField))
            {
                dt.DefaultView.Sort = $"{sortField} {ascDesc}";
                dt = dt.DefaultView.ToTable();
            }
            return JsonConvert.SerializeObject(dt);
        }

        /// <summary>
        /// 讀取員工刷卡時間(依據查詢日期 )
        /// </summary>
        /// <param name="theDate">查詢日期.</param>
        /// <param name="empNo">員工編號.</param>
        public DayInTime GetDayInTime(DateTime theDate, string empNo)
        {
            DayInTime dayInTime = new DayInTime();
            string cacheName = $"DayInTime{theDate.ToString("yyyyMMdd")}-{empNo}";
            if (_cache.Contains(cacheName))
            {
                dayInTime = (DayInTime)_cache[cacheName];
            }
            else
            {
                dayInTime.EmpNo = empNo;
                string intimeStr = GetDayInTimeString(theDate, empNo);
                string[] intimes = intimeStr.Split(',');
                for (int i = 0; i < intimes.Length; i++)
                {
                    string dateString = theDate.ToString("yyyy/MM/dd");
                    intimes[i] = intimes[i].Replace("(公出)", string.Empty).Trim();
                    dayInTime.InTimeString.Add(intimes[i]);
                    DateTime inTime = DateTime.Parse($"{dateString} {intimes[i]}");
                    dayInTime.InTime.Add(inTime);
                }
                lock (_cacheLock)
                {
                    _cache.Set(cacheName, dayInTime, CachePolicy60Seconds);
                }
            }
            return dayInTime;
        }

        /// <summary>
        /// 讀取員工刷卡時間字串(依據查詢日期 )
        /// </summary>
        /// <param name="theDate">查詢日期.</param>
        /// <param name="empNo">員工編號.</param>
        public string GetDayInTimeString(DateTime theDate, string empNo)
        {
            string ret = string.Empty;
            string cacheName = $"DayInTimeString{theDate.ToString("yyyyMMdd")}-{empNo}";
            if (_cache.Contains(cacheName))
            {
                ret = (string)_cache[cacheName];
            }
            else
            {
                DataTable dt = _attendanceDao.GetDayInTime(theDate, empNo);
                if (dt != null && dt.Rows.Count == 1)
                {
                    ret = (string)dt.Rows[0]["IN_HHMM"];
                }
                lock (_cacheLock)
                {
                    _cache.Set(cacheName, ret, CachePolicy60Seconds);
                }
            }
            return ret;
        }

        /// <summary>
        /// 取得部門已填表單
        /// </summary>
        /// <param name="deptNo">部門員工編號</param>
        /// <returns>已填表單</returns>
        public DataTable GetDepartmentSentBox(int deptNo)
        {
            DataTable dt = _attendanceDao.GetDepartmentSentBox(deptNo);
            return dt;
        }

        /// <summary>
        /// 取得部門已填表單，以填表日期為準
        /// </summary>
        /// <param name="deptNo">部門員工編號</param>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <returns>已填表單</returns>
        public DataTable GetDepartmentSentBox(int deptNo, DateTime startDate, DateTime endDate)
        {
            DataTable dt = _attendanceDao.GetDepartmentSentBox(deptNo, startDate, endDate);
            return dt;
        }

        /// <summary>
        /// 取得部門已填表單，以填表日期為準
        /// </summary>
        /// <param name="deptNo">部門員工編號</param>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>已填表單</returns>
        public DataTable GetDepartmentSentBox(int deptNo, DateTime startDate, DateTime endDate, string projNo)
        {
            DataTable dt = _attendanceDao.GetDepartmentSentBox(deptNo, startDate, endDate, projNo);
            return dt;
        }

        /// <summary>
        /// 取得部門已填表單，以內容日期為準
        /// </summary>
        /// <param name="deptNo">部門員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <returns>已填表單</returns>
        public DataTable GetDepartmentSentBoxByContentDate(int deptNo, DateTime startDate, DateTime endDate)
        {
            DataTable dt = _attendanceDao.GetDepartmentSentBoxByContentDate(deptNo, startDate, endDate);
            return dt;
        }

        /// <summary>
        /// 取得部門已填表單，以內容日期為準
        /// </summary>
        /// <param name="deptNo">部門員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>已填表單</returns>
        public DataTable GetDepartmentSentBoxByContentDate(int deptNo, DateTime startDate, DateTime endDate, string projNo)
        {
            DataTable dt = _attendanceDao.GetDepartmentSentBoxByContentDate(deptNo, startDate, endDate, projNo);
            return dt;
        }

        /// <summary>
        /// 取得 部門  某月份已填表單 ，限部門登記桌或管理員
        /// </summary>
        /// <param name="deptNo">部門編號</param>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <returns>已填表單</returns>
        public DataTable GetDepartmentSentBoxYearMonth(int deptNo, int year, int month)
        {
            DataTable dt = _attendanceDao.GetDepartmentSentBoxYearMonth(deptNo, year, month);
            return dt;
        }

        /// <summary>
        /// 取得當年度1月至該月分之剩餘補休假時數
        /// </summary>
        /// <param name="date"></param>
        /// <param name="empNo"></param>
        /// <returns>剩餘補休假時數</returns>
        public int GetEmployeeCompensatoryLeaveHours(DateTime date, string empNo)
        {
            int hours = _attendanceDao.GetEmployeeCompensatoryLeaveHours(date, empNo);
            return hours;
        }

        /// <summary>
        /// 取得員工指定假別休假資料，包括年度總可用時數、年度已使用時數(簽核中＋已核可)、
        /// 累至查詢月份已使用時數(簽核中＋已核可)、查詢月份已使用時數(簽核中＋已核可)、
        /// 年度已核可時數(已核可)、累至查詢月份年度已核可時數(已核可)、
        /// 查詢月份已核可時數(已核可)
        /// 提供以下假別代碼查詢：
        /// 01：特別休息假
        /// 04：事假
        /// 10：病假
        /// 12：補休假(未開發)
        /// 14：延休假
        /// 16：生理假
        /// 19：家庭照顧假
        /// </summary>
        /// <param name="date">查詢日期</param>
        /// <param name="empNo">員工編號</param>
        /// <param name="leaveNumber">假別</param>
        /// <param name="leaveSubNumber">假別細項</param>
        public string GetEmployeeLeaveInfo(DateTime date, string empNo, LeaveKindEnum leaveNumber, int leaveSubNumber)
        {
            List<SqlParameter> parameters = _attendanceDao.GetEmployeeLeaveInfo(date, empNo, leaveNumber, leaveSubNumber);
            Dictionary<string, int> dict = new Dictionary<string, int>();
            foreach (SqlParameter parameter in parameters)
            {
                dict.Add(parameter.ParameterName.Replace("@", ""), (int)parameter.Value);
            }
            string jsonString = JsonConvert.SerializeObject(dict);
            return jsonString;
        }

        /// <summary>
        /// 讀取員工指定月份(依據查詢日期 )休假統計時數，
        /// 包括年度總可用時數、年度已使用時數(簽核中＋已核可)、累至查詢月份已使用時數(簽核中＋已核可)、
        /// 查詢月份已使用時數(簽核中＋已核可)、年度已核可時數(已核可)、
        /// 累至查詢月份年度已核可時數(已核可)、查詢月份已核可時數(已核可)
        /// 提供 01：特別休息假 12：補休假  14：延休假
        /// </summary>
        /// <param name="theDate">查詢日期.</param>
        /// <param name="empNo">員工編號.</param>
        public List<EmpLeaveInfo> GetEmployeeLeaveInformation(DateTime theDate, string empNo)
        {
            List<EmpLeaveInfo> leaveInformation = _attendanceDao.GetEmpLeaveInfo(theDate, empNo);
            return leaveInformation;
        }

        /// <summary>取得 Form</summary>
        /// <param name="formUID">表單識別碼</param>
        /// <returns>DataTable Form</returns>
        public DataTable GetForm(Guid formUID)
        {
            DataTable dt = _attendanceDao.GetForm(formUID);
            return dt;
        }

        /// <summary>取得 FormFLow</summary>
        /// <param name="flowUID">流程識別碼</param>
        /// <returns>DataTable FormFlow</returns>
        public DataTable GetFormFlow(Guid flowUID)
        {
            DataTable dt = _attendanceDao.GetFormFlow(flowUID);
            return dt;
        }

        /// <summary>取得 FormFLow</summary>
        /// <param name="formUID">表單識別碼</param>
        /// <returns>DataTable FormFlow</returns>
        public DataTable GetFormFlows(Guid formUID)
        {
            DataTable dt = _attendanceDao.GetFormFlows(formUID);
            return dt;
        }

        /// <summary>
        /// 取得 FormFlowStatus名稱列表
        /// </summary>
        /// <returns></returns>
        public Dictionary<int, string> GetFormFlowStatusNames()
        {
            Dictionary<int, string> dictionary;

            if (_formFlowStatusNames != null)
            {
                dictionary = _formFlowStatusNames;
            }
            else
            {
                dictionary = new Dictionary<int, string>();
                DataTable dt = _attendanceDao.FormFlowStatusNames();
                foreach (DataRow dr in dt.Rows)
                {
                    dictionary.Add((int)(byte)dr["ID"], (string)dr["Name"]);
                }
#pragma warning disable S2696 // Instance members should not write to "static" fields. 此處為Sonar Lint 誤判
                _formFlowStatusNames = dictionary;
#pragma warning restore S2696 // Instance members should not write to "static" fields
            }
            return dictionary;
        }

        /// <summary>
        /// 依日期區間取得所有表單 by 填報日期，限管理員使用
        /// </summary>
        /// <param name="startDate">開始日</param>
        /// <param name="endDate">結束日</param>
        /// <returns>表單資料表</returns>
        public DataTable GetForms(DateTime startDate, DateTime endDate)
        {
            return _attendanceDao.GetForms(startDate, endDate);
        }

        /// <summary>
        /// 依日期區間取得所有表單 by 填報日期，限管理員使用
        /// </summary>
        /// <param name="startDate">開始日</param>
        /// <param name="endDate">結束日</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單資料表</returns>
        public DataTable GetForms(DateTime startDate, DateTime endDate, string projNo)
        {
            return _attendanceDao.GetForms(startDate, endDate, projNo);
        }
        /// <summary>
        /// 依日期區間取得所有表單 by 內容日期，限管理員使用
        /// </summary>
        /// <param name="startDate">開始日</param>
        /// <param name="endDate">結束日</param>
        /// <returns>表單資料表</returns>
        public DataTable GetFormsByContentDate(DateTime startDate, DateTime endDate)
        {
            return _attendanceDao.GetFormsByContentDate(startDate, endDate);
        }

        /// <summary>
        /// 依日期區間取得所有表單 by 內容日期，限管理員使用
        /// </summary>
        /// <param name="startDate">開始日</param>
        /// <param name="endDate">結束日</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單資料表</returns>
        public DataTable GetFormsByContentDate(DateTime startDate, DateTime endDate, string projNo)
        {
            return _attendanceDao.GetFormsByContentDate(startDate, endDate, projNo);
        }

        /// <summary>
        /// Get Forms by FormStatus
        /// </summary>
        /// <param name="status">FormStatus</param>
        /// <returns>Forms</returns>
        public DataTable GetFormsByStatus(FormStatus status)
        {
            DataTable dt = _attendanceDao.GetFormsByStatus(status);
            return dt;
        }

        /// <summary>
        /// Get Forms by FormStatus
        /// </summary>
        /// <param name="status">FormStatus</param>
        /// <param name="startDate">最早填卡日期</param>
        /// <param name="endDate">最晚填卡日期</param>
        /// <returns>Forms</returns>
        public DataTable GetFormsByStatus(FormStatus status, DateTime startDate, DateTime endDate)
        {
            DataTable dt = _attendanceDao.GetFormsByStatus(status, startDate, endDate);
            return dt;
        }

        /// <summary>
        /// 取得 FormStatus名稱列表
        /// </summary>
        /// <returns></returns>
        public Dictionary<int, string> GetFormStatusNames()
        {
            Dictionary<int, string> dictionary = new Dictionary<int, string>();

            if (_formStatusNames != null)
            {
                dictionary = _formStatusNames;
            }
            else
            {
                DataTable dt = _attendanceDao.FormStatusNames();
                foreach (DataRow dr in dt.Rows)
                {
                    dictionary.Add((int)(byte)dr["ID"], (string)dr["Name"]);
                }
#pragma warning disable S2696 // Instance members should not write to "static" fields. 此處為Sonar Lint 誤判
                _formStatusNames = dictionary;
#pragma warning restore S2696 // Instance members should not write to "static" fields
            }
            return dictionary;
        }

        /// <summary>
        /// 取得 FormType DataTable
        /// </summary>
        /// <returns></returns>
        public DataTable GetFormTypeDataTable()
        {
            return _attendanceDao.GetFormTypes();
        }

        /// <summary>取得收件匣 DataTable</summary>
        /// <param name="roles">角色的List</param>
        /// <returns>收件匣 DataTable</returns>
        public DataTable GetInboxes(List<Role> roles)
        {
            DataTable dt = _attendanceDao.GetInboxes(roles);
            return dt;
        }


        /// <summary>
        /// 讀取員工指定月份出勤時間
        /// </summary>
        /// <param name="date">The date.</param>
        /// <param name="userid">The userId.</param>
        /// <returns></returns>
        public List<DayAttendance> GetMonthAttendance(DateTime date, string userid)
        {
            List<DayAttendance> monthAttendances;
            if (string.IsNullOrWhiteSpace(userid))
            {
                throw new ArgumentNullException(nameof(userid), "userid 為 null");
            }

            string cacheName = $"MonthAttendance{date:yyyyMM}-{userid}";
            if (_cache.Contains(cacheName))
            {
                monthAttendances = (List<DayAttendance>)_cache[cacheName];
                return monthAttendances;
            }

            monthAttendances = FetchMonthAttendance(date, userid);

            lock (_cacheLock)
            {
                _cache.Set(cacheName, monthAttendances, CachePolicy60Seconds);
            }

            return monthAttendances;
        }

        /// <summary>
        /// 讀取員工指定月份出勤時間
        /// </summary>
        /// <param name="date">The date.</param>
        /// <param name="userid">The userId.</param>
        /// <returns></returns>
        private List<DayAttendance> FetchMonthAttendance(DateTime date, string userid)
        {
            List<DayAttendance> monthAttendances = new List<DayAttendance>();
            string temp = string.Format("{0, 3:G}{1:D2}", CardUtility.RocChineseYear(date), date.Month);
            string temp2 = string.Format("{0, 4:G}-{1:D2}-", date.Year, date.Month);

            using (DataTable dt = _attendanceDao.GetMonthAttendance(date, userid))
            using (DataTable dt2 = _attendanceDao.GetMonthInTimeString(date, userid))
            {
                if (dt != null && dt.Rows.Count > 0)
                {
                    for (int j = 1; j <= 31; j++)
                    {
                        DayAttendance dayAttendance = CreateDayAttendance(date, dt, dt2, temp, temp2, j);
                        monthAttendances.Add(dayAttendance);
                    }
                }
            }

            return monthAttendances;
        }

        /// <summary>    
        /// 建立 DayAttendance 物件
        /// </summary>
        private static DayAttendance CreateDayAttendance(DateTime date, DataTable dt, DataTable dt2, string temp, string temp2, int day)
        {
            DayAttendance dayAttendance = new DayAttendance
            {
                Year = date.Year,
                Month = date.Month,
                Day = day
            };

            DataRow[] drs = dt.Select($"IN_YYMMDD = '{temp}{day:00}'");
            DataRow[] drs2 = dt2.Select($"IN_YYYYMMDD = '{temp2}{day:00}'");

            dayAttendance.Attendances = GetAttendances(drs);
            dayAttendance.InTimeString = GetInTimeString(drs2);

            return dayAttendance;
        }

        /// <summary>
        /// 由DataRow陣列轉為出勤時間陣列
        /// </summary>
        private static string[] GetAttendances(DataRow[] drs)
        {
            string[] sret = new string[drs.Length];
            for (int i = 0; i < drs.Length; i++)
            {
                if (drs[i]["IN_HHMM"] != DBNull.Value)
                {
                    string? stemp = drs[i]["IN_HHMM"] as string;
                    if (stemp != null)
                    {
                        sret[i] = $"{stemp.Substring(0, 2)}:{stemp.Substring(2, 2)}";
                    }
                }
            }
            return sret;
        }

        /// <summary>
        /// 取得出勤時間字串
        /// </summary>
        private static string GetInTimeString(DataRow[] drs2)
        {
            if (drs2 != null && drs2.Length > 0 && drs2[0]["IN_HHMM"] != null)
            {
                string? s = drs2[0]["IN_HHMM"] as string;
                if (s != null)
                {
                    return s;
                }
            }
            return string.Empty;
        }

        /// <summary>
        /// 讀取員工指定月份(依據查詢日期 )休假統計時數
        /// </summary>
        /// <param name="theDate">查詢日期.</param>
        /// <param name="empNo">員工編號.</param>
        public EmployeeLeaves GetMonthEmployeeLeaves(DateTime theDate, string empNo)
        {
            EmployeeLeaves leave = new EmployeeLeaves();
            SqlParameter[] paramArray = _attendanceDao.GetMonthEmployeeLeaves(theDate, empNo);
            if (Convert.ToInt16(paramArray[27].Value) == 0)
            {
                leave.TotalAnnualLeaves = Convert.ToInt16(paramArray[2].Value);
                leave.UsedAnnualLeaves = Convert.ToInt16(paramArray[3].Value);
                leave.UsedPersonalLeaves = Convert.ToInt16(paramArray[4].Value);
                leave.UsedSickLeaves = Convert.ToInt16(paramArray[5].Value);
                leave.UsedAnnualLeavesMonth = Convert.ToInt16(paramArray[6].Value);
                leave.UsedPersonalLeavesMonth = Convert.ToInt16(paramArray[7].Value);
                leave.UsedSickLeavesMonth = Convert.ToInt16(paramArray[8].Value);
                leave.RemainAnnualLeaves = Convert.ToInt16(paramArray[9].Value);
                leave.RemainReservedLeavesLastMonth = Convert.ToInt16(paramArray[10].Value);
                leave.NewReservedLeavesMonth = Convert.ToInt16(paramArray[11].Value);
                leave.RemainReservedLeaves = Convert.ToInt16(paramArray[12].Value);
                //2010.12.13 新增延休假
                leave.ExtendedLeaves = Convert.ToInt16(paramArray[13].Value);
                leave.UsedExtendedLeaves = Convert.ToInt16(paramArray[14].Value);
                leave.UsedExtendedLeavesMonth = Convert.ToInt16(paramArray[15].Value);
                leave.RemainedExtendedLeaves = Convert.ToInt16(paramArray[16].Value);
                leave.DisplayExtendedLeaves = Convert.ToBoolean(paramArray[17].Value);
                leave.ExtendedLeavesIsImported = Convert.ToBoolean(paramArray[18].Value);
                // 2018/05/23 新增補休假
                leave.UsedMenstruationLeaves = Convert.ToInt16(paramArray[19].Value);
                leave.UsedMenstruationLeavesMonth = Convert.ToInt16(paramArray[20].Value);
                leave.Gender = Convert.ToChar(paramArray[21].Value);
                leave.NewRemainCompensatoryLeavesMonth = Convert.ToInt16(paramArray[22].Value);
                leave.UsedCompensatoryLeavesMonth = Convert.ToInt16(paramArray[23].Value);
                leave.TotalNewRemainCompensatoryLeaves = Convert.ToInt16(paramArray[24].Value);
                leave.TotalUsedCompensatoryLeaves = Convert.ToInt16(paramArray[25].Value);
                leave.TotalCompensatoryLeaves = Convert.ToInt16(paramArray[26].Value);
            }
            return leave;
        }

        /// <summary>
        /// 讀取員工指定月(依據查詢日期 ) 已填報有效(簽核中與同意)且為加班上限範圍內之加班統計
        /// </summary>
        /// <param name="date">查詢日期.</param>
        /// <param name="empNo">員工編號.</param>
        public MonthOvertimeStatics GetMonthEmployeeOvertimeStatics(DateTime date, string empNo)
        {
            MonthOvertimeStatics statics = new MonthOvertimeStatics();
            statics.Year = date.Year;
            statics.Month = date.Month;
            DataTable dt = _attendanceDao.GetMonthEmployeeOvertimeStatics(date, empNo);
            statics.Month = date.Month;
            statics.MonthName = $"{statics.Month}月";
            if (dt != null && dt.Rows.Count > 0)
            {
                if (dt.Rows[0]["ApprovedHours"] != DBNull.Value)
                {
                    statics.ApprovedHours = (int)dt.Rows[0]["ApprovedHours"];
                }
                if (dt.Rows[0]["UnderApprovalHours"] != DBNull.Value)
                {
                    statics.UnderApprovalHours = (int)dt.Rows[0]["UnderApprovalHours"];
                }
                statics.TotalHours = statics.ApprovedHours + statics.UnderApprovalHours;
            }
            return statics;
        }

        /// <summary>取得指定表單新單號 (最大表單單號+1)</summary>
        /// <param name="formID">表單編號</param>
        /// <param name="chineseYear">民國年</param>
        public string GetNewFormNo(string formID, string chineseYear)
        {
            //string formNo = _attendanceDao.GetNewFormNo(formID, chineseYear);
            string formNo = "0000000000";
            return formNo;
        }

        /// <summary>
        /// 取得所有通知
        /// </summary>
        /// <returns>通知 List<Notification></returns>
        public List<Notification> GetNotifications()
        {
            DataTable dt = _attendanceDao.GetNotifications();
            List<Notification> notifications = SqlHelper.ConvertDataTable<Notification>(dt);
            return notifications;
        }

        /// <summary>
        /// 取得所有通知表單資料
        /// </summary>
        /// <returns>通知 List<Notification></returns>
        public List<NotifyFormCards> GetNotifyFormCards(string empNo, int deptNo, List<Role> roles, List<int> isRead, List<int>formStatus, DateTime startDate, DateTime endDate)
        {
            DataTable dt = _attendanceDao.GetNotifyFormCards(empNo, deptNo, roles, isRead, formStatus, startDate, endDate);
            List<NotifyFormCards> notifications = SqlHelper.ConvertDataTable<NotifyFormCards>(dt);
            return notifications;
        }

        /// <summary>
        /// 取得加權加班時數
        /// </summary>
        /// <param name="date"></param>
        /// <param name="hours"></param>
        /// <returns></returns>
        public int GetPaidHour(DateTime date, int hours)
        {
            int minPaidOvertimeHours = _workdayBo.GetMinPaidOvertimeHours(date);
            int maxOvertimeHours = _workdayBo.GetMaxOvertimeHours(date);
            if (hours < minPaidOvertimeHours)
            {
                hours = minPaidOvertimeHours;
            }
            else if (hours > maxOvertimeHours)
            {
                hours = maxOvertimeHours;
            }
            return hours;
        }

        /// <summary>
        /// 取得員工 B1Card Position
        /// </summary>
        /// <param name="employeeNumber"></param>
        /// <returns></returns>
        public B1CardPositionEnum GetPositionType(string employeeNumber)
        {
            B1CardPositionEnum positionType = B1CardPositionEnum.NotInList;
            string cacheName = $"PositionType-{employeeNumber}";
            if (_cache.Contains(cacheName))
            {
                positionType = (B1CardPositionEnum)_cache[cacheName];
            }
            else
            {
                Employee? employee = _employeeBo.GetEmployeeDetail(employeeNumber);
                if (employee != null && employee.JobNo != null)
                {
                    int jobNo = int.Parse(employee.JobNo);
                    switch (jobNo)
                    {
                        case (int)B1CardPositionEnum.Chairman:
                            positionType = B1CardPositionEnum.Chairman;
                            break;
                        case (int)B1CardPositionEnum.President:
                            positionType = B1CardPositionEnum.President;
                            break;
                        case (int)B1CardPositionEnum.VicePresident:
                            positionType = B1CardPositionEnum.VicePresident;
                            break;
                        case (int)B1CardPositionEnum.Manager:
                            positionType = B1CardPositionEnum.Manager;
                            break;
                        case (int)B1CardPositionEnum.DeputyManager:
                            positionType = B1CardPositionEnum.DeputyManager;
                            break;
                        case (int)B1CardPositionEnum.Director:
                            positionType = B1CardPositionEnum.Director;
                            break;
                        case (int)B1CardPositionEnum.DeputyDirector:
                            positionType = B1CardPositionEnum.DeputyDirector;
                            break;
                        case (int)B1CardPositionEnum.ProjectDirector:
                            positionType = B1CardPositionEnum.ProjectDirector;
                            break;
                        case (int)B1CardPositionEnum.ProjectDeputyDirector:
                            positionType = B1CardPositionEnum.ProjectDeputyDirector;
                            break;
                        case (int)B1CardPositionEnum.ChiefAccountant:
                            positionType = B1CardPositionEnum.ChiefAccountant;
                            break;
                        case (int)B1CardPositionEnum.SectionChief:
                            positionType = B1CardPositionEnum.SectionChief;
                            break;
                        case (int)B1CardPositionEnum.ProjectSectionChief:
                            positionType = B1CardPositionEnum.ProjectSectionChief;
                            break;
                        default:
                            positionType = B1CardPositionEnum.GeneralStaff;
                            break;
                    }
                }
                else
                {
                    if (employee != null && !string.IsNullOrWhiteSpace(employee.RankNo))
                    {
                        int rankNo = int.Parse(employee.RankNo);
                        if (rankNo == (int)B1CardPositionEnum.Driver)
                        {
                            positionType = B1CardPositionEnum.Driver;
                        }
                        else
                        {
                            positionType = B1CardPositionEnum.GeneralStaff;
                        }
                    }

                }
                lock (_cacheLock)
                {
                    _cache.Set(cacheName, positionType, CachePolicy);
                }
            }
            return positionType;
        }

        /// <summary>
        /// 讀取員工指定季(依據查詢日期 ) 已填報有效(簽核中與同意)且為加班上限範圍內之加班總時數
        /// </summary>
        /// <param name="theDate">查詢日期.</param>
        /// <param name="empno">員工編號.</param>
        public int GetQuarterlyEmployeeOvertimeHours(DateTime theDate, string empno)
        {
            int hours = _attendanceDao.GetQuarterlyEmployeeOvertimeHours(theDate, empno);
            return hours;
        }

        /// <summary>
        /// 讀取員工指定季(依據查詢日期 ) 已填報有效(簽核中與同意)且為加班上限範圍內之加班時數統計
        /// </summary>
        /// <param name="date">查詢日期.</param>
        /// <param name="empNo">員工編號.</param>
        public QuarterlyOvertimeStatics GetQuarterlyEmployeeOvertimeStatics(DateTime date, string empNo)
        {
            QuarterlyOvertimeStatics statics = new QuarterlyOvertimeStatics();
            int quarterly = (date.Month - 1) / 3 + 1;
            switch (quarterly)
            {
                case 1:
                    statics.QuarterName = AttendanceParameters.FirstQuarter;
                    break;
                case 2:
                    statics.QuarterName = AttendanceParameters.SecondQuarter;
                    break;
                case 3:
                    statics.QuarterName = AttendanceParameters.ThirdQuarter;
                    break;
                case 4:
                    statics.QuarterName = AttendanceParameters.FourthQuarter;
                    break;
            }
            int year = date.Year;

            for (int i = 1; i < 4; i++)
            {
                int month = (quarterly - 1) * 3 + i;
                DateTime newDate = new DateTime(year, month, 1, 0, 0, 0, DateTimeKind.Local);
                MonthOvertimeStatics monthStatics = GetMonthEmployeeOvertimeStatics(newDate, empNo);
                statics.ApprovedHours += monthStatics.ApprovedHours;
                statics.Months.Add(monthStatics);
                statics.UnderApprovalHours += monthStatics.UnderApprovalHours;
            }

            statics.TotalHours = statics.ApprovedHours + statics.UnderApprovalHours;
            return statics;
        }

        /// <summary>取得 填表人/表單關係人 已填表單</summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns>寄件匣 DataTable</returns>
        public DataTable GetSentBox(string empNo, DateTime startDate, DateTime endDate, int? status = null)
        {
            DataTable dt = _attendanceDao.GetSentBox(empNo, startDate, endDate, status);
            return dt;
        }

        /// <summary>取得 填表人/表單關係人 已填表單</summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <param name="status">表單狀態</param>
        /// <returns>寄件匣 DataTable</returns>
        public DataTable GetSentBox(string empNo, DateTime startDate, DateTime endDate, string projNo, int? status = null)
        {
            DataTable dt = _attendanceDao.GetSentBox(empNo, startDate, endDate, projNo, status);
            return dt;
        }

        /// <summary>取得 填表人/表單關係人 已填表單</summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns>寄件匣 DataTable</returns>
        public DataTable GetSentBoxByContentDate(string empNo, DateTime startDate, DateTime endDate, int? status = null)
        {
            DataTable dt = _attendanceDao.GetSentBoxByContentDate(empNo, startDate, endDate, status);
            return dt;
        }

        /// <summary>取得 填表人/表單關係人 已填表單</summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <param name="status">表單狀態</param>
        /// <returns>寄件匣 DataTable</returns>
        public DataTable GetSentBoxByContentDate(string empNo, DateTime startDate, DateTime endDate, string projNo, int? status = null)
        {
            DataTable dt = _attendanceDao.GetSentBoxByContentDate(empNo, startDate, endDate, projNo, status);
            return dt;
        }

        /// <summary>
        /// 取得 填表人/表單關係人 某月份已填表單
        /// </summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <param name="status">表單狀態</param>
        /// <returns>已填表單</returns>
        public DataTable GetSentBoxYearMonth(string empNo, int year, int month, int? status = null)
        {
            DataTable dt = _attendanceDao.GetSentBoxYearMonth(empNo, year, month, status);
            return dt;
        }

        /// <summary>
        /// 取得 已簽核表單，包括本人簽核或代理人簽核，及預設本人角色(非代理)簽核
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="roles">角色</param>
        /// <returns>已簽核表單</returns>
        public DataTable GetSignedForms(string empNo, DateTime startDate, DateTime endDate, List<Role> roles)
        {
            DataTable dt = _attendanceDao.GetSignedForms(empNo, startDate, endDate, roles);
            return dt;
        }

        /// <summary>
        /// 取得 已簽核表單，包括本人簽核或代理人簽核，及預設本人角色(非代理)簽核，以申請內容日期為準
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="contentStartDate">申請內容啟始日期</param>
        /// <param name="contentEndDate">申請內容結束日期</param>
        /// <param name="roles">角色</param>
        /// <returns>已簽核表單</returns>
        public DataTable GetSignedFormsByContentDate(string empNo, DateTime startDate, DateTime endDate, DateTime contentStartDate, DateTime contentEndDate, List<Role> roles)
        {
            DataTable dt = _attendanceDao.GetSignedFormsByContentDate(empNo, startDate, endDate, contentStartDate, contentEndDate, roles);
            return dt;
        }

        /// <summary>
        /// 取得旬的起始與結束日期，包括非工作日
        /// </summary>
        /// <param name="year">西元年</param>
        /// <param name="month">月</param>
        /// <param name="tenDays">旬別(1:上旬、2:中旬、3:下旬)</param>
        /// <returns></returns>
        public (DateTime, DateTime) GetTendays(int year, int month, int tenDays)
        {
            DateTime startDate = DateTime.MaxValue;
            DateTime endDate = DateTime.MinValue;
            switch (tenDays)
            {
                case 1: // "上旬";
                    startDate = new DateTime(year, month, 1, 0, 0, 0, DateTimeKind.Local);
                    endDate = new DateTime(year, month, 10, 0, 0, 0, DateTimeKind.Local);
                    break;
                case 2: // "中旬";
                    startDate = new DateTime(year, month, 11, 0, 0, 0, DateTimeKind.Local);
                    endDate = new DateTime(year, month, 20, 0, 0, 0, DateTimeKind.Local);
                    break;
                case 3: // "下旬";
                    startDate = new DateTime(year, month, 21, 0, 0, 0, DateTimeKind.Local);
                    endDate = CardUtility.LastDayOfMonth(startDate);
                    break;
            }
            return (startDate, endDate);
        }

        /// <summary>
        /// 取得年月旬的起始與結束日期，包括非工作日
        /// </summary>
        /// <param name="year">西元年，不可為0</param>
        /// <param name="month">月、0:不指定</param>
        /// <param name="tenDays">旬別(1:上旬、2:中旬、3:下旬、0:不指定)</param>
        /// <returns></returns>
        public (DateTime, DateTime) GetStartEndDays(int year, int month, int tenDays)
        {
            if (year == 0) throw new ArgumentException("年度不可為空");
            DateTime startDate = DateTime.MaxValue;
            DateTime endDate = DateTime.MinValue;
            if (tenDays == 0) // 沒有旬
            {
                if (month == 0) // 全年
                {
                    startDate = new DateTime(year, 1, 1, 0, 0, 0, DateTimeKind.Local);
                    endDate = new DateTime(year, 12, 31, 0, 0, 0, DateTimeKind.Local);
                }
                else // 年月
                {
                    startDate = new DateTime(year, month, 1);
                    endDate = CardUtility.LastDayOfMonth(startDate);
                }
            }
            else // 年月旬
            {
                (startDate, endDate) = GetTendays(year, month, tenDays);
            }
            return (startDate, endDate);
        }


        /// <summary>
        /// 取得使用者前N筆簽核意見
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="count">數量</param>
        /// <returns>簽核意見</returns>
        public List<string> GetTopApproveComments(string empNo, int count)
        {
            List<string> ret = new List<string>();
            DataTable dt = _attendanceDao.GetTopApproveComments(empNo, count);
            foreach (DataRow dr in dt.Rows)
            {
                ret.Add((string)dr["ApproveComments"]);
            }
            return ret;
        }

        /// <summary>
        /// 取得加班卡
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public B1Card? GetValidB1Card(DateTime date, string empNo)
        {
            B1Card? card = null;
            DataTable dt = _attendanceDao.GetValidB1Card(date, empNo);
            List<B1CardDto> b1CardDtos;
            if (dt != null && dt.Rows.Count > 0)
            {
                b1CardDtos = SqlHelper.ConvertDataTable<B1CardDto>(dt);
                card = B1CardDto.B1CardDtoToB1Card(b1CardDtos);
            }
            return card;
        }

        /// <summary>
        /// 取得加班申請卡
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public B1CardApp? GetValidB1CardApp(DateTime date, string empNo)
        {
            DataTable dt = _attendanceDao.GetValidB1CardApp(date, empNo);
            B1CardApp? card = null;
            if (dt != null && dt.Rows.Count == 1) //超過一張就有問題
            {
                List<B1CardApp> b1Cards = SqlHelper.ConvertDataTable<B1CardApp>(dt);
                card = b1Cards.ToArray()[0];
            }
            return card;
        }


        /// <summary>
        /// 取得本月份的全年病假可休總時數
        /// </summary>
        /// <param name="date"></param>
        /// <param name="empNo"></param>
        /// <returns></returns>
        public int GetYearSickLeaveHours(DateTime date, string empNo)
        {
            int hours = _attendanceDao.GetYearSickLeaveHours(date, empNo);
            return hours;
        }

        /// <summary>
        /// 是否為管理員
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public bool IsAdmin(string empNo)
        {
            bool ret = false;
            DataTable dtAdmins = GetAdmins();
            if (dtAdmins != null && dtAdmins.Rows.Count > 0)
            {
                DataRow[] drs = dtAdmins.Select($"EmpNo='{empNo}'");
                if (drs.Length > 0)
                {
                    ret = true;
                }
            }
            return ret;
        }

        /// <summary>是否有查詢[部門填報紀錄]的權限</summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>有權限傳回<c>true</c>；否則傳回<c>false</c></returns>
        public bool IsAuthorizedToQueryDepartmentSentBox(string empNo)
        {
            bool canSee = false;
            //// 先判斷是否為管理員
            //if (IsAdmin(empNo))
            //{
            //    return true;
            //}
            // 是否有此員工資料
            var employee = _employeeBo.GetEmployeeDetail(empNo);
            if (employee != null)
            {
                // 是否為治理部門主管
                if (employee.IsAboveDeputyManager && employee.DeptNo == 1)
                {
                    canSee = true;
                    return canSee;
                }

                // 是否為副主管以上
                if (employee.IsAboveDeputyManager)
                {
                    canSee = true;
                    return canSee;
                }

                if (!canSee)
                {
                    // 是否為登記桌
                    List<Role> roles = _sinoSignBo.GetUserRoles(empNo);
                    // 2025-08-21 改為任一部門的登記桌都可以查詢
                    IEnumerable<Role> query = roles.Where(x => x.RoleId.Length == 2);

                    if (query.Count() > 0) // 角色為登記桌
                    {
                        canSee = true;
                    }
                }
            }
            return canSee;
        }

        /// <summary>是否有查詢[部門填報紀錄]的權限</summary>
        /// <param name="deptNo">部門編號</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>有權限傳回<c>true</c>；否則傳回<c>false</c></returns>
        public bool IsAuthorizedToQueryDepartmentSentBox(int deptNo, string empNo)
        {
            bool canSee = false;
            //// 先判斷是否為管理員
            //if (IsAdmin(empNo))
            //{
            //    return true;
            //}
            // 是否有此員工資料
            var employee = _employeeBo.GetEmployeeDetail(empNo);
            if (employee != null)
            {
                // 是否為治理部門主管
                if (employee.IsAboveDeputyManager && employee.DeptNo == 1)
                {
                    canSee = true;
                    return canSee;
                }

                // 是否為副主管以上
                if (_departmentBo.IsAboveDeputyManager(deptNo, empNo))
                {
                    canSee = true;
                }

                if (!canSee)
                {
                    // 是否為登記桌
                    List<Role> roles = _sinoSignBo.GetUserRoles(empNo);
                    string role = deptNo.ToString("00");
                    IEnumerable<Role> query = roles.Where(x => x.RoleId == role);

                    if (query.Count() == 1) // 角色為登記桌
                    {
                        canSee = true;
                    }
                }
            }
            return canSee;
        }


        /// <summary>
        /// 檢查是否有指定的通知
        /// </summary>
        /// <param name="formUID">The form guid.</param>
        /// <param name="notifyId">The notify id.</param>
        /// <returns></returns>
        public bool IsNotificationExist(Guid formUID, int notifyId)
        {
            bool ret = _attendanceDao.IsNotificationExist(formUID, notifyId);
            return ret;
        }

        /// <summary>
        /// 檢查是否有指定的通知
        /// </summary>
        /// <param name="roles">角色list</param>
        /// <param name="formUID">The form guid.</param>
        /// <param name="notifyId">The notify id.</param>
        /// <returns></returns>
        public bool IsNotificationExist(List<Role> roles, Guid formUID, int notifyId)
        {
            bool ret = _attendanceDao.IsNotificationExist(roles, formUID, notifyId);
            return ret;
        }

        /// <summary>
        /// 判斷是不是可加班特殊員工
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <returns>若為特殊員工，傳回true</returns>
        public bool IsSpecialStaff(string employeeNumber)
        {
            DateTime date = DateTime.Now;
            bool isSpecial = IsSpecialStaff(employeeNumber, date);
            return isSpecial;
        }

        /// <summary>
        /// 判斷是不是可加班特殊員工
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="date">加班日</param>
        /// <returns>若為特殊員工，傳回true</returns>
        public bool IsSpecialStaff(string employeeNumber, DateTime date)
        {
            bool isSpecial;
            (isSpecial, _) = _attendanceDao.IsSpecialStaff(employeeNumber, date);
            return isSpecial;
        }

        /// <summary>
        /// 判斷是不是可加班特殊員工
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="date">加班日</param>
        /// <param name="allowedMonthWeightedOvertimeHour">加班日當月可加班加權時數</param>
        /// <param name="currentMonthWeightedOvertimeHour">加班日當月已加班加權時數</param>
        /// <returns>若為特殊員工，傳回true</returns>
        public bool IsSpecialStaff(string employeeNumber, DateTime date,
            out double allowedMonthWeightedOvertimeHour, out double currentMonthWeightedOvertimeHour)
        {
            bool isSpecial;
            double hoursLimit;
            date = new DateTime(date.Year, date.Month, date.Day, 0, 0, 0, DateTimeKind.Local);
            (isSpecial, hoursLimit) = _attendanceDao.IsSpecialStaff(employeeNumber, date);
            if (isSpecial)
            {
                allowedMonthWeightedOvertimeHour = hoursLimit;
                currentMonthWeightedOvertimeHour = _attendanceDao.GetMonthEmployeeWeightedOvertimeHours(date, employeeNumber);
            }
            else
            {
                allowedMonthWeightedOvertimeHour = AttendanceParameters.MonthOvertimeHoursLimit; //一般員工
                currentMonthWeightedOvertimeHour = 0.0;
            }
            return isSpecial;
        }

        /// <summary>
        /// 標註某員工及其對應之角色的所有未讀通知為已讀
        /// </summary>
        /// <param name="roles">員工角色</param>
        /// <returns>有一筆以上的通知被標註已讀則回傳true，否則回傳false</returns>
        public bool MarkDeliveredNotifications(List<Role> roles)
        {
            return _attendanceDao.MarkDeliveredNotifications(roles);
        }

        /// <summary>
        /// 更新表單
        /// </summary>
        /// <param name="formID">表單名稱</param>
        /// <param name="dataSet">包含所有待更新資料表的 Data Set</param>
        /// <returns>成功為空值，失敗時為錯誤訊息 </returns>
        public string UpdateForm(string formID, DataSet dataSet)
        {
            string errorMessage = _attendanceDao.UpdateForm(formID, dataSet);
            return errorMessage;
        }
    }
}
