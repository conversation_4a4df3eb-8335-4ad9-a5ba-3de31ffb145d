﻿using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves;
using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves.Tests
{
    public abstract class TestC1CardBypassAnyCheck : TestC1CardBase
    {
        [Fact]
        public void TestCanTakeThisLeave()
        {
            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CanTakeThisLeave();

            Assert.Equal(C1CardBase.CodeOk, result.Code);
        }

        [Fact]
        public void TestExceedQuota()
        {
            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CheckOverPermittedLeaveHours();

            Assert.Equal(C1CardBase.CodeOk, result.Code);
        }

        [Fact]
        public void TestCheckRequiredFields()
        {
            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CheckRequiredFields();

            Assert.Equal(C1CardBase.CodeOk, result.Code);
        }

        //[Theory]
        //[InlineData(Gender.Female, true)]
        //[InlineData(Gender.Male, true)]
        //public void TestGender(Gender gender, bool expected)
        //{
        //    var result = C1CardBase.IsAllowForThisGender(_c1Card.LeaveNumber, gender);
        //    Assert.Equal(expected, result);
        //}
    }
}