﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.Utilities;
using System;

namespace Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers
{
    /// <summary>
    /// 員工 Web API
    /// </summary>
    [Authorize]
    [ApiController]
    [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
    [Route("/api/[controller]/[action]")]
    public class EmployeeController : ControllerBase
    {
        private readonly bool _useNegotiate = true;
        private readonly IAttendanceBo _attendanceBo;
        private readonly IEmployeeBo _employeeBo;
        private readonly ISinoSignBo _sinoSignBo;
        private readonly ILogger<EmployeeController> _logger;

        
        /// <summary><see cref="EmployeeController" /> 的建構函式</summary>
        /// <param name="employeeBo">The employee 商業物件.</param>
        /// <param name="attendanceBo">The attendance 商業物件.</param>
        /// <param name="sinoSignBo">The SinoSign 商業物件.</param>
        /// <param name="configuration">設定檔</param>
        /// <param name="logger">日誌記錄器</param>
        public EmployeeController(IEmployeeBo employeeBo, IAttendanceBo attendanceBo,
            ISinoSignBo sinoSignBo, IConfiguration configuration, ILogger<EmployeeController> logger)
        {
            _employeeBo = employeeBo;
            _attendanceBo = attendanceBo;
            _sinoSignBo = sinoSignBo;
            _logger = logger;
            _useNegotiate = configuration.GetValue<bool>("UseNegotiate");
        }

        /// <summary>取得指定員工目前是否可以報加班</summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>可以加班傳回 true，不行則傳回 false</returns>
        [Authorize]
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/{empNo}")]
        public bool IsOvertimeAllowed(string empNo)
        {
            DateTime date = DateTime.Now;
            B1CardAppCheckResult result = new B1CardAppCheckResult();
            result.ErrorMessage = AttendanceParameters.GeneralErrorMessage;
            result.IsOvertimeAllowed = false;
            result.IsValid = false;
            try
            {
                string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
                if (!string.IsNullOrWhiteSpace(userId))
                {
                    result = _attendanceBo.IsOvertimeAllowed(empNo, date);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("/api/Employee/IsOvertimeAllowed({empNo}) 發生錯誤：{Message} {StackTrace}", empNo, ex.Message, ex.StackTrace);
            }
            return result.IsOvertimeAllowed;
        }

        /// <summary>
        /// 取得所有員工，包括離職員工
        /// 會將優先部門放在前面
        /// </summary>
        /// <param name="deptNo">優先部門代碼</param>
        /// <returns>所有現職員工</returns>
        [Authorize]
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public string GetAllEmployees(int deptNo = 0)
        {
            return _employeeBo.GetAllEmployeesJson(deptNo);
        }

        /// <summary>
        /// 取得所有可加班現職員工
        /// 會將優先部門放在前面
        /// </summary>
        ///<param name="deptNo">優先部門代號，預設 0 表示不將優先部門置前</param>
        /// <returns>所有可加班現職員工</returns>
        [Authorize]
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public string GetEligibleOvertimeEmployees(int deptNo)
        {
            return _attendanceBo.GetEligibleOvertimeEmployeesJson(deptNo);
        }

        /// <summary>
        /// 取得代理人(們)
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        [Authorize]
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/{empNo}")]
        public string GetDeputies(string empNo)
        {
            return JsonConvert.SerializeObject(_sinoSignBo.GetDeputies(empNo));
        }

        /// <summary>
        /// 取得指定員工的精簡資料
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        [Authorize]
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/{empNo}")]
        public string GetEmployee(string empNo)
        {
            return _employeeBo.GetEmployeeSimpleJson(empNo);
        }

        /// <summary>
        /// 取得所有現職員工
        /// 會將優先部門放在前面
        /// </summary>
        /// <param name="deptNo">優先部門代碼</param>
        /// <returns>所有現職員工</returns>
        [Authorize]
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public string GetEmployees(int deptNo = 0)
        {
            return _employeeBo.GetEmployeesSimpleJson(deptNo);
        }

        /// <summary>
        /// 取得指定員工的角色
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>員工角色</returns>
        [Authorize]
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/{empNo}")]
        //[EnableCors("MyAllowSpecificOrigins")]
        public string GetUserRoles(string empNo)
        {
            return JsonConvert.SerializeObject(_sinoSignBo.GetUserRoles(empNo));
        }
    }
}
