﻿using FakeItEasy;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Sinotech.Mis.Extensions.Configuration;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.Tests;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using Xunit;
using static Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardAppBo;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.Tests
{
    [ExcludeFromCodeCoverage]
    public class B1CardAppBoTests
    {

        private readonly AttendanceBo _attendanceBo;
        private readonly B1CardAppBo _b1CardAppBo;
        private readonly B1CardBo _b1CardBo;
        private readonly CardBoFactory _cardBoFactory;
        private readonly FormBo _formBo;

        public B1CardAppBoTests(AttendanceBo attendanceBo, B1CardBo b1CardBo, B1CardAppBo b1CardAppBo, FormBo formBo, CardBoFactory cardBoFactory)
        {
            _attendanceBo = attendanceBo;
            _b1CardBo = b1CardBo;
            _b1CardAppBo = b1CardAppBo;
            _formBo = formBo;
            _cardBoFactory = cardBoFactory;
            IConfiguration configuration = new ConfigurationBuilder().
            AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).Build();
            string ConnectionStringAttendance = configuration.GetSecuredConnectionString("Attendance");
            TestHelper.ClearData(ConnectionStringAttendance);
        }

        private string AddSimpleCard1()
        {

            string errorMessage;
            string jsonForm = "{\"ID\":null,\"FormUID\":\"5fadb665-cc43-4fc3-8061-764c48885ee7\",\"FormID\":\"B1CardApp\",\"FormNo\":null,\"FormSubject\":\"加班申請卡-白燕菁-111/08/18\",\"FormInfo\":\"111/08/18\",\"EmpNo\":\"0349\",\"EmpName\":\"白燕菁\",\"DeptNo\":4,\"DeptSName\":\"企劃處\",\"TeamID\":null,\"TeamCName\":null,\"CreatedEmpNo\":\"0395\",\"CreatedName\":\"曾騰毅\",\"FilledTime\":\"2022-08-19T08:35:42.582+08:00\",\"CreatedTime\":\"2022-08-19T08:36:07.111+08:00\",\"CreatedIP\":\"***********\",\"CreatedHost\":\"03-769\",\"AddedSigner\":\"0274\",\"StartTime\":\"2022-08-19T08:36:35.8711009+08:00\",\"EndTime\":null,\"FormStatus\":1,\"TotalSteps\":2,\"CurrentStep\":0,\"UpdatedEmpNo\":null,\"UpdatedName\":null,\"UpdatedTime\":null,\"UpdatedIP\":null,\"UpdatedHost\":null,\"Flows\":[{\"ID\":null,\"FlowUID\":\"eafddfe2-5139-4c14-bbd2-26e6813c6425\",\"FormUID\":\"5fadb665-cc43-4fc3-8061-764c48885ee7\",\"RecipientEmpNo\":\"0274\",\"RecipientName\":\"薛強\",\"RecipientDeptNo\":10,\"RecipientDeptSName\":\"土研中心\",\"RecipientTeamID\":null,\"RecipientTeamCName\":null,\"FlowName\":\"加會人員\",\"Step\":1,\"ApproverEmpNo\":null,\"ApproverName\":null,\"ApproverDeptNo\":0,\"ApproverDeptSName\":null,\"ApproverTeamID\":null,\"ApproverTeamCName\":null,\"ApproverIP\":null,\"ApproverHost\":null,\"ApproveTime\":null,\"IsAgentApprove\":false,\"FlowStatus\":1,\"IsNotification\":false},{\"ID\":null,\"FlowUID\":\"a25071ea-3c20-4641-af78-59bf77302394\",\"FormUID\":\"5fadb665-cc43-4fc3-8061-764c48885ee7\",\"RecipientEmpNo\":\"L04\",\"RecipientName\":\"企劃處經理\",\"RecipientDeptNo\":4,\"RecipientDeptSName\":\"企劃處\",\"RecipientTeamID\":null,\"RecipientTeamCName\":null,\"FlowName\":\"申請人部門主管\",\"Step\":2,\"ApproverEmpNo\":null,\"ApproverName\":null,\"ApproverDeptNo\":0,\"ApproverDeptSName\":null,\"ApproverTeamID\":null,\"ApproverTeamCName\":null,\"ApproverIP\":null,\"ApproverHost\":null,\"ApproveTime\":null,\"IsAgentApprove\":false,\"FlowStatus\":0,\"IsNotification\":false}]}";
            Form form = JsonConvert.DeserializeObject<Form>(jsonForm);
            B1CardApp card = new B1CardApp();
            card.FormUID = new Guid("5fadb665-cc43-4fc3-8061-764c48885ee7");
            card.B1_EmpNo = "0349";
            card.B1_DeptNo = 4;
            card.B1_Date = DateTime.Parse("2022-08-18T00:00:00");
            card.B1_Hour = 4;
            card.B1_Code = '2';
            card.B1_PrjNo = "RP19553";
            card.B1_Reason = "test";
            card.B1_WritedEmpNo = "0395";
            card.B1_WDate = DateTime.Parse("2022-08-19T08:35:42.582+08:00");
            card.B1_UpdatedEmpNo = null;
            card.B1_Status = 1;
            card.B1_SOURCE = "Attendance";
            card.UpdatedEmpNo = "0395";
            card.UpdatedName = "曾騰毅";
            card.UpdatedTime = DateTime.Parse("2022-08-19T08:36:07.111+08:00");
            card.UpdatedIP = "***********";
            card.UpdatedHost = "03-769";
            card.AddSigners = "0274";
            card.CreatedTime = DateTime.Parse("2022-08-19T08:36:07.111+08:00");
            var fake = new Fake<IEmployeeBo>();
            fake.CallsTo(x => x.IsEmployee(card.B1_WritedEmpNo)).Returns(true);
            fake.CallsTo(x => x.IsEmployee(card.B1_UpdatedEmpNo)).Returns(true);
            ICardBaseBo cardBo = _cardBoFactory.GetCardBo("B1CardApp");
            errorMessage = _formBo.AddForm(form, cardBo, card);
            return errorMessage;
        }

        [Fact]
        public async Task AddB1CardAppTest()
        {
            B1CardApp b1CardApp = new B1CardApp();
            b1CardApp.FormUID = new Guid("5fadb665-cc43-4fc3-8061-764c48885ee7");
            b1CardApp.B1_EmpNo = "0349";
            b1CardApp.B1_DeptNo = 4;
            b1CardApp.B1_Date = DateTime.Parse("2022-08-18T00:00:00");
            b1CardApp.B1_Hour = 4;
            b1CardApp.B1_Code = '2';
            b1CardApp.B1_PrjNo = "RP19553";
            b1CardApp.B1_Reason = "test";
            b1CardApp.B1_WritedEmpNo = "0395";
            b1CardApp.B1_WDate = DateTime.Parse("2022-08-19T08:35:42.582+08:00");
            b1CardApp.B1_UpdatedEmpNo = null;
            b1CardApp.B1_Status = 1;
            b1CardApp.B1_SOURCE = "Attendance";
            b1CardApp.UpdatedEmpNo = "0395";
            b1CardApp.UpdatedName = "曾騰毅";
            b1CardApp.UpdatedTime = DateTime.Parse("2022-08-19T08:36:07.111+08:00");
            b1CardApp.UpdatedIP = "***********";
            b1CardApp.UpdatedHost = "03-769";
            b1CardApp.AddSigners = "0274";
            b1CardApp.CreatedTime = DateTime.Parse("2022-08-19T08:36:07.111+08:00");
            b1CardApp.FilledTime = DateTime.Parse("2022-08-19T08:33:07.111+08:00");

            B1CardAppCheckResult result = await _b1CardAppBo.AddB1CardApp(b1CardApp.B1_WritedEmpNo, b1CardApp, b1CardApp.UpdatedIP, b1CardApp.UpdatedHost);
            Assert.False(result.IsFilled);
            Assert.True(result.IsValid);
            Assert.True(result.IsOvertimeAllowed);
            Assert.Equal("逾期填報", result.DateAlarmMessage);
            Assert.Equal("", result.DateErrorMessage);
            Assert.Equal("", result.ErrorMessage);
            result = await _b1CardAppBo.AddB1CardApp(b1CardApp.B1_WritedEmpNo, b1CardApp, b1CardApp.UpdatedIP, b1CardApp.UpdatedHost);
            Assert.True(result.IsFilled);
            Assert.False(result.IsValid);
            Assert.False(result.IsOvertimeAllowed);
            Assert.Equal("逾期填報", result.DateAlarmMessage);
            Assert.Equal("一天限填一張加班申請卡，請勿重複填報", result.DateErrorMessage);
            Assert.Equal("一天限填一張加班申請卡，請勿重複填報", result.ErrorMessage);
        }

        [Theory]
        [InlineData(2023, 3, 1, "2268", true)]
        [InlineData(2023, 3, 1, "0391", false)]
        [InlineData(2023, 3, 1, "2096", false)]
        [InlineData(2023, 3, 1, "2040", false)]
        [InlineData(2023, 2, 26, "2268", false)]
        public void CanCallIsOvertimeAllowed(int year, int month, int day, string empNo, bool canWorkOvertime)
        {
            // Arrange
            var date = new DateTime(year, month, day);

            // Act
            var retString = _b1CardAppBo.IsOvertimeAllowed(date, empNo);
            // Assert
            bool result = string.IsNullOrWhiteSpace(retString);
            Assert.Equal(canWorkOvertime, result);
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("   ")]
        public void CannotCallIsOvertimeAllowedWithInvalidEmpNo(string value)
        {
            var date = new DateTime(2023, 3, 1);
            var result = _b1CardAppBo.IsOvertimeAllowed(date, value);
            Assert.NotEmpty(result);
        }

        [Fact]
        public void IsOvertimeAllowedTest()
        {
            string empNo = "0391";
            bool canWorkOvertime = _attendanceBo.IsOvertimeAllowed(empNo, DateTime.Now).IsOvertimeAllowed;
            Assert.False(canWorkOvertime);

            empNo = "0349";
            canWorkOvertime = _attendanceBo.IsOvertimeAllowed(empNo, DateTime.Now).IsOvertimeAllowed;
            Assert.True(canWorkOvertime);
        }

        [Fact]
        public void DateCanFillB1CardAppTest()
        {
            DateTime date = new DateTime(2022, 10, 9);
            string empNo = "0391";
            B1CardAppCheckResult result = _b1CardAppBo.DateCanFillB1CardApp(date, empNo);
            Assert.NotEmpty(result.ErrorMessage);
            Assert.False(result.IsOvertimeAllowed);

            date = new DateTime(2022, 10, 10);
            result = _b1CardAppBo.DateCanFillB1CardApp(date, empNo);
            Assert.NotEmpty(result.ErrorMessage);
            Assert.False(result.IsOvertimeAllowed);

            date = new DateTime(2022, 10, 9);
            empNo = "0349";
            result = _b1CardAppBo.DateCanFillB1CardApp(date, empNo);
            Assert.NotEmpty(result.ErrorMessage);
            Assert.False(result.IsOvertimeAllowed);
            date = new DateTime(2022, 10, 10);
            result = _b1CardAppBo.DateCanFillB1CardApp(date, empNo);
            Assert.Empty(result.ErrorMessage);
            Assert.True(result.IsOvertimeAllowed);
        }

        [Fact]
        public void GetB1CardTypesTest()
        {
            List<B1CardType> list = _b1CardBo.GetB1CardTypes();
            Assert.Equal(3, list.Count);
            B1CardType type1 = list[0];
            Assert.Equal(1, type1.Type);
            Assert.Equal("加班", type1.TypeName);
            B1CardType type2 = list[1];
            Assert.Equal(2, type2.Type);
            Assert.Equal("社外加班", type2.TypeName);
            B1CardType type3 = list[2];
            Assert.Equal(3, type3.Type);
            Assert.Equal("補休假", type3.TypeName);
            list = _b1CardBo.GetB1CardTypes();
            type3 = list[2];
            Assert.Equal(3, type3.Type);
            Assert.Equal("補休假", type3.TypeName);
        }

        [Fact]
        public string GetCardsTest()
        {
            string errorMessage = AddSimpleCard1();
            Assert.Empty(errorMessage);
            Guid guid = new Guid("5fadb665-cc43-4fc3-8061-764c48885ee7");
            CardBase card = _b1CardAppBo.GetCard(guid);
            B1CardApp b1CardApp = (B1CardApp)card;
            Assert.Equal(guid, b1CardApp.FormUID);
            return errorMessage;
        }

        [Fact]
        public void GetFormCardsTest()
        {
            string errorMessage = AddSimpleCard1();
            Assert.Empty(errorMessage);
            Guid guid = new Guid("5fadb665-cc43-4fc3-8061-764c48885ee7");
            string userId = "2268";
            Form form = _formBo.GetForm(guid);
            FormCard formCard = _b1CardAppBo.GetFormCard(form, userId);
            Assert.NotNull(formCard);
            Assert.NotNull(formCard.Card);
            B1CardApp card = (B1CardApp)formCard.Card;
            Assert.Equal(guid, card.FormUID);
        }

        [Fact]
        public void GetListRemindMessageTest()
        {
            B1CardApp b1CardApp = new B1CardApp();
            b1CardApp.B1_EmpNo = "0349";
            b1CardApp.B1_Hour = 2;
            b1CardApp.B1_DateTypeId = 1;
            b1CardApp.B1_Date = new DateTime(2023, 4, 10);
            b1CardApp.B1_WDate = new DateTime(2023, 5, 10);
            int iRet;
            string errorMessage;
            (iRet, errorMessage) = _b1CardAppBo.GetListRemindMessage(b1CardApp);
            Assert.Equal(5, iRet);
            Assert.Equal($"逾期填報", errorMessage);

            b1CardApp.B1_EmpNo = "0045";
            (iRet, errorMessage) = _b1CardAppBo.GetListRemindMessage(b1CardApp);
            Assert.Equal(99, iRet);
            Assert.Equal($"申請人{AttendanceParameters.IsOffServiceStaff}", errorMessage);

            b1CardApp.B1_EmpNo = "0349";
            b1CardApp.B1_Date = new DateTime(2022, 9, 9);
            b1CardApp.B1_WDate = new DateTime(2022, 9, 10);
            b1CardApp.B1_Hour = 2;
            b1CardApp.B1_DateTypeId = 4; // 週間國定假日
            (iRet, errorMessage) = _b1CardAppBo.GetListRemindMessage(b1CardApp);
            Assert.Equal(4, iRet);
            Assert.Equal($"同仁本次預定加班日期為週間國定假日，預定加班時數為2小時，依勞基法規定該日加班如不足8小時仍須支給8小時加班費", errorMessage);

            b1CardApp.B1_DateTypeId = 8; // 補假日
            (iRet, errorMessage) = _b1CardAppBo.GetListRemindMessage(b1CardApp);
            Assert.Equal(4, iRet);
            Assert.Equal($"同仁本次預定加班日期為補假日，預定加班時數為2小時，依勞基法規定該日加班如不足8小時仍須支給8小時加班費", errorMessage);
        }


        [Fact]
        public void GetValidB1CardAppTest()
        {
            string errorMessage = AddSimpleCard1();
            Assert.Empty(errorMessage);
            DateTime date = new DateTime(2022, 8, 18);
            string empNo = "0349";
            B1CardApp b1CardApp = _attendanceBo.GetValidB1CardApp(date, empNo);
            Assert.NotNull(b1CardApp);
            Assert.Equal(empNo, b1CardApp.B1_EmpNo);
            Assert.Equal(date, b1CardApp.B1_Date);
        }

        [Fact]
        public void InAllowDaysTest()
        {
            string result;
            DateTime fakeNow = new DateTime(2022, 10, 11); //國慶日後一天上班日，週二
            //測試前一天 10/10 週一
            DateTime yesterDay = fakeNow.AddDays(-1);
            result = _b1CardAppBo.InAllowOvertimeDays(yesterDay, fakeNow);
            Assert.Empty(result);

            //測試前2天 10/9 Sunday
            DateTime twoDaysAgo = fakeNow.AddDays(-2);
            result = _b1CardAppBo.InAllowOvertimeDays(twoDaysAgo, fakeNow);
            Assert.NotEmpty(result);

            //測試前3天 10/8 Saturday
            DateTime threeDaysAgo = fakeNow.AddDays(-3);
            result = _b1CardAppBo.InAllowOvertimeDays(threeDaysAgo, fakeNow);
            Assert.Empty(result);

            // 5天後
            DateTime fiveDayslater = fakeNow.AddDays(5);
            result = _b1CardAppBo.InAllowOvertimeDays(fiveDayslater, fakeNow);
            Assert.NotEmpty(result);

            // 30 天後
            DateTime thirtyDaysLater = fakeNow.AddDays(30);
            result = _b1CardAppBo.InAllowOvertimeDays(thirtyDaysLater, fakeNow);
            Assert.Empty(result);

            // 31 天後
            DateTime thirtyOneDaysLater = fakeNow.AddDays(31);
            result = _b1CardAppBo.InAllowOvertimeDays(thirtyOneDaysLater, fakeNow);
            Assert.NotEmpty(result);
            Assert.Equal("僅開放填報當日起 30 日內之加班申請卡", result);
        }

        [Fact]
        public void IsOverdueTest()
        {
            DateTime fillDate = new DateTime(2022, 10, 12); // Wednesday 工作日
            DateTime date = new DateTime(2022, 10, 11); // Tuesday 工作日
            bool isOverdue = _b1CardAppBo.IsOverdue(date, fillDate);
            Assert.False(isOverdue);
            date = new DateTime(2022, 10, 10);
            isOverdue = _b1CardAppBo.IsOverdue(date, fillDate);
            Assert.True(isOverdue);
            date = new DateTime(2022, 10, 8); // Saturday
            isOverdue = _b1CardAppBo.IsOverdue(date, fillDate);
            Assert.True(isOverdue);
            date = new DateTime(2022, 10, 7); // Friday 工作日
            isOverdue = _b1CardAppBo.IsOverdue(date, fillDate);
            Assert.True(isOverdue);

            fillDate = new DateTime(2022, 10, 11);
            date = new DateTime(2022, 10, 10);
            isOverdue = _b1CardAppBo.IsOverdue(date, fillDate);
            Assert.False(isOverdue);
            date = new DateTime(2022, 10, 8); // Saturday
            isOverdue = _b1CardAppBo.IsOverdue(date, fillDate);
            Assert.False(isOverdue);
            date = new DateTime(2022, 10, 7); // Friday 工作日
            isOverdue = _b1CardAppBo.IsOverdue(date, fillDate);
            Assert.False(isOverdue);
        }

        [Fact]
        public void IsValidB1CardAppTest()
        {
            B1CardApp b1CardApp = new B1CardApp();
            b1CardApp.B1_Hour = 1;
            b1CardApp.B1_PrjNo = "";
            B1CardAppCheckResult ret = _b1CardAppBo.CheckData(b1CardApp);
            Assert.False(ret.IsValid);
            Assert.False(ret.IsOvertimeAllowed);
            b1CardApp.FormUID = Guid.NewGuid();
            b1CardApp.B1_PrjNo = "RP19553";
            b1CardApp.B1_FormID = "申111000009";
            b1CardApp.B1_EmpNo = "0395";
            b1CardApp.B1_Date = new DateTime(2022, 7, 11);
            b1CardApp.CreatedTime = new DateTime(2022, 7, 11, 14, 11, 9);
            b1CardApp.B1_DateTypeId = 1;
            b1CardApp.B1_Reason = "測試";
            b1CardApp.B1_Status = 1;
            b1CardApp.UpdatedEmpNo = "2268";
            b1CardApp.UpdatedName = "孫睿宏";
            b1CardApp.UpdatedIP = "***********";
            b1CardApp.UpdatedTime = b1CardApp.CreatedTime;
            b1CardApp.UpdatedHost = "03-788";
            ret = _b1CardAppBo.CheckData(b1CardApp);
            Assert.True(ret.IsValid);
            Assert.True(ret.IsOvertimeAllowed);
            Assert.True(ret.IsOverdue);
            b1CardApp.B1_EmpNo = "2023";
            ret = _b1CardAppBo.CheckData(b1CardApp);
            Assert.False(ret.IsOvertimeAllowed);
            Assert.False(ret.IsValid);
            Assert.Equal("未開放部門副主管以上填報加班申請", ret.ErrorMessage);
        }

        [Fact]
        public void TestGetSentFormCardsYearMonth_WithInvalidEmpNo_ShouldReturnEmptyList()
        {
            // Arrange
            string empNo = "";
            int year = 2023;
            int month = 4;

            // Act
            List<FormCard> formCards = _b1CardAppBo.GetUserSentFormCardsYearMonth(empNo, year, month);

            // Assert
            Assert.Empty(formCards);
        }

        [Fact]
        public void TestGetSentFormCardsYearMonth_WithInvalidMonth_ShouldReturnEmptyList()
        {
            // Arrange
            string empNo = "0349";
            int year = 2023;
            int month = 0;

            // Act
            List<FormCard> formCards = _b1CardAppBo.GetUserSentFormCardsYearMonth(empNo, year, month);

            // Assert
            Assert.Empty(formCards);
        }

        [Fact]
        public void TestGetSentFormCardsYearMonth_WithInvalidYear_ShouldReturnEmptyList()
        {
            // Arrange
            string empNo = "0349";
            int year = 0;
            int month = 4;

            // Act
            List<FormCard> formCards = _b1CardAppBo.GetUserSentFormCardsYearMonth(empNo, year, month);

            // Assert
            Assert.Empty(formCards);
        }

        [Fact]
        public void TestGetSentFormCardsYearMonth_WithValidParameters_ShouldReturnListOfFormCardDto()
        {
            // Arrange
            AddSimpleCard1();
            string empNo = "0395";
            int year = 2022;
            int month = 8;

            // Act
            List<FormCard> formCards = _b1CardAppBo.GetUserSentFormCardsYearMonth(empNo, year, month);

            // Assert
            Assert.NotNull(formCards);
            Assert.NotEmpty(formCards);
        }

        [Fact]
        public void WithdrawTest()
        {
            // Arrange
            AddSimpleCard1();
            string empNo = "0395";
            int year = 2022;
            int month = 8;
            List<FormCard> formCards = _b1CardAppBo.GetUserSentFormCardsYearMonth(empNo, year, month);
            FormCard formCard = formCards[0];
            CardBase card = formCard.Card;

            Withdraw withdraw = new Withdraw();
            withdraw.WithdrawIP = "**********";
            withdraw.WithdrawName = empNo;
            withdraw.FormID = card.Name;
            withdraw.FormUID = card.FormUID;

            // Act
            bool result = _b1CardAppBo.Withdraw(card, withdraw);

            // Assert
            Assert.True(result);
        }

        [Theory]
        [InlineData("2003-01-18", "2003-05-05", true)]
        [InlineData("2022-08-18", "2022-08-18", true)]
        [InlineData("2022-08-18", "2022-08-20", false)]
        public void GetCardsTest_DatePeriod(DateTime startDate, DateTime endDate, bool isEmpty)
        {
            AddSimpleCard1();
            List<CardBase> list = _b1CardAppBo.GetCards(startDate, endDate);
            if (isEmpty)
            {
                Assert.Empty(list);
            }
            else
            {
                Assert.NotEmpty(list);
            }
        }

    }
}
