<template>
  <h6 class="my-2 fs-2 text-center">
    <i class="bi bi-cloud-moon me-1" />
    <span>{{ FORM_ID.B1CardApp }}</span>
  </h6>
  <div class="container px-0 text-center">
    <div class="border border-dark-subtle border-2 mx-2 mx-sm-0">
      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1cardapp">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>申</span>
              <span class="mx-1">請</span>
              <span>人</span>
            </div>
          </div>
        </div>
        <div class="col-9 col-md-10 py-2 text-start">
          <div class="row">
            <div class="col col-sm-5 col-md-4 col-lg-3 pe-0 py-0">
              <ChooseUser
                :modelValue="{
                  userId: employee.userId,
                  userName: employee.userName,
                  deptNo: employee.deptNo,
                  deptSName: employee.deptSName
                }"
                :employeeData="canWorkOvertimeEmployeeData"
                :filter="onSearchEmployeeData"
                :clearable="false"
                :mode="'apply'"
                :disabled="submitted === true"
                @update:modelValue="onChangeEmployee"
              />
            </div>
            <div class="col-auto">
              <template v-if="userStore.userId !== employee?.userId">
                <span class="badge rounded-pill bg-warning">代填</span>
              </template>
            </div>
          </div>
        </div>
      </div>

      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1cardapp">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>所屬部門</span>
            </div>
          </div>
        </div>
        <div class="col-9 col-md-10 py-2 d-flex align-items-center">
          {{ employee.deptSName }}
        </div>
      </div>

      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1cardapp">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>預定日期</span>
            </div>
          </div>
        </div>
        <div class="col-9 col-md-10 py-2 text-start">
          <div class="row">
            <div class="col-auto">
              <RocCalendarSelect
                :modelValue="overtimeDate"
                :inputClass="{
                  'form-control': true,
                  'w-auto': true,
                  'shake': shake.date
                }"
                :disabled="submitted === true"
                @click="onClickDate"
                @update:modelValue="onChangeDate"
                @update:year-month="onChangeYearMonth"
              >
                <template
                  v-if="calendarLoaded === true"
                  #date="{ date }"
                >
                  <span :class="getCalendarDayClass(date, workdays)">
                    {{ (date as DayType).day }}
                  </span>
                </template>
              </RocCalendarSelect>
            </div>
            <div
              v-if="errorMessage.length > 0"
              class="col-12 text-danger"
            >
              <small>{{ errorMessage }}</small>
            </div>
            <div
              v-else-if="warnMessage.length > 0"
              class="col-12 text-warning"
            >
              <small>{{ warnMessage }}</small>
            </div>
          </div>
        </div>
      </div>

      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1cardapp">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>填報計畫</span>
            </div>
          </div>
        </div>
        <div class="col py-2">
          <div class="row">
            <div :class="['col py-0', shake.project ? 'shake': '']">
              <ProjectSelect
                :modelValue="project"
                :projectData="projectData"
                :placeholder="'請輸入或選取計畫'"
                :clearable="true"
                :disabled="(checkCanWorkOvertime === false) || (submitted === true)"
                @update:modelValue="onSelectProject"
                @delete="onDeleteProject"
              />
            </div>
            <div class="col-12 fw-normal text-secondary text-start">
              <small>※ 計畫進度已達100%第3個月起不可填報</small>
            </div>
          </div>
        </div>
      </div>

      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1cardapp">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>申</span>
              <span class="mx-1">請</span>
              <span>別</span>
            </div>
          </div>
        </div>
        <div class="col-9 col-md-10 py-2 text-start">
          <div class="row">
            <div class="col pe-0 py-0">
              <div
                v-for="item in overtimeCompensatory"
                :key="item.id"
                class="form-check"
              >
                <input
                  :id="'overtimeCompensatory' + item.id"
                  :value="item.id"
                  :disabled="(checkCanWorkOvertime === false) || (submitted === true)"
                  class="form-check-input me-1"
                  type="radio"
                  v-model="overtimeCompensatoryCode"
                >
                <label
                  class="form-check-label"
                  :for="'overtimeCompensatory' + item.id"
                >
                  {{ item.name }}
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1cardapp">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>預定時數</span>
            </div>
          </div>
        </div>
        <div class="col-9 col-md-10 py-2 text-start">
          <div
            :class="[
              'pe-0 py-0',
              (shake.overTimeHour === true) ? 'shake' : ''
            ]"
          >
            <select
              v-if="overtimeData !== null"
              class="form-select d-inline-block w-auto"
              :style=" { 'min-width': '5rem' }"
              :value="overtimeHours"
              :disabled="(checkCanWorkOvertime === false) || (submitted === true)"
              @input="onSelectHour"
            >
              <template
                v-for="hour in overtimeData.hoursUpperBound"
                :key="hour"
              >
                <option :value="hour">
                  {{ hour }}
                </option>
              </template>
            </select>
            <span class="ms-1">小時</span>
          </div>
        </div>
      </div>

      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1cardapp">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>加班事由</span>
            </div>
          </div>
        </div>
        <div class="col-9 col-md-10 py-2 text-start">
          <div class="row">
            <div
              :class="[
                'col py-0',
                (shake.reason === true) ? 'shake' : ''
              ]"
            >
              <textarea
                v-model="reason"
                class="form-control w-100"
                :placeholder="'請填寫加班事由'"
                :maxlength="REASON_TEXTAREA_MAX"
                :disabled="(checkCanWorkOvertime === false) || (submitted === true)"
              />
              <small class="text-secondary">最大可輸入字數：{{ REASON_TEXTAREA_MAX }}</small>
            </div>
          </div>
        </div>
      </div>

      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1cardapp">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>加會人員</span>
            </div>
          </div>
        </div>
        <div :class="['col-sm-auto py-2', (signers.length > 0) ? 'col-2' : 'col-auto']">
          <button
            type="button"
            :class="[
              'btn me-1 p-2',
              (((checkCanWorkOvertime === false) || (submitted === true)) ? 'btn-outline-secondary' : 'btn-primary')
            ]"
            :disabled="(checkCanWorkOvertime === false) || (submitted === true)"
            @click="onAddSignerClick"
          >
            <span>新增加會人員</span>
          </button>
        </div>

        <div class="col py-2">
          <template
            v-for="(signer, index) in signers"
            :key="index"
          >
            <div class="row mb-2">
              <div class="col-auto p-0">
                <span class="badge bg-secondary">
                  {{ index + 1 }}
                </span>
              </div>
              <div class="col col-sm-8 col-md-6 col-lg-4 pt-1">
                <ChooseColleague
                  :modelValue="signer.userId ? {
                    userId: signer.userId,
                    userName: signer.userName
                  } : null"
                  :customClass="((shake.signer === true) && (signer.userId === null)) ? 'shake' : ''"
                  :disabled="submitted === true"
                  :employeeData="employeeData.filter((employeeEle) => ((signers.findIndex((signerEle) => signerEle?.userId ? (signerEle.userId === employeeEle.userId) : false) === -1) && (employee.userId !== employeeEle.userId)))"
                  :employeeFilter="employee.userId"
                  :signerFilter="employeeData.filter((employeeEle) => ((signers.findIndex((signerEle) => signerEle?.userId ? (signerEle.userId === employeeEle.userId) : false) !== -1)))"
                  :placeholder="'請輸入或選取加會人員'"
                  :filter="onSearchEmployeeData"
                  :clearable="true"
                  :alwaysShowClearButton="true"
                  @update:modelValue="onChangeSigner($event, index)"
                  @delete="onDeleteSigner(index)"
                />
              </div>
            </div>
          </template>
        </div>
      </div>

      <div class="row border-bottom border-dark-subtle mx-0">
        <div class="col-3 col-md-2 px-0 py-2 fw-bold bg-b1cardapp">
          <div class="d-table h-100 w-100">
            <div class="d-table-cell align-middle">
              <span>上傳附件</span>
            </div>
          </div>
        </div>
        <div class="col-9 col-md-10 py-2 text-start">
          <AttachedFile
            :disabled="(checkCanWorkOvertime === false) || (submitted === true)"
            @update:modelValue="onUploadFiles"
          />
        </div>
      </div>

      <div class="row mx-0 text-start">
        <template v-if="userStore.userId === employee?.userId">
          <div class="col-12">
            <template v-if="overtimeData !== null">
              <OvertimeInfo
                :modelValue="{
                  card: FORM_ID.B1CardApp,
                  overtimeDateStatic: overtimeDateStatic,
                  overtimeData: overtimeData,
                  currentInfoNote: true
                }"
              />

              <template v-if="overtimeData.monthOvertimeStatics.TotalHours >= overtimeData.monthCloseToHoursUpperBoundValue">
                <div class="text-danger fs-5">
                  <i class="bi bi-exclamation-triangle" />
                  <template v-if="overtimeData.monthOvertimeStatics.TotalHours >= overtimeData.monthOverHoursUpperBoundValue">
                    {{ overtimeData.monthOverHoursUpperBoundMessage }}
                  </template>
                  <template v-else>
                    {{ overtimeData.monthCloseToHoursUpperBoundMessage }}
                  </template>
                </div>
              </template>
              <template v-else-if="overtimeData.quarterlyOvertimeStatics.TotalHours >= overtimeData.quarterOverHoursUpperBoundValue">
                <div class="text-danger fs-5">
                  <i class="bi bi-exclamation-triangle" />
                  {{ overtimeData.quarterOverHoursUpperBoundMessage }}
                </div>
              </template>
            </template>
          </div>

          <div class="col-12">
            <span>※ 當月/當季加班總時數說明：</span>
            <ol>
              <li>
                <span>當月/當季加班時數為加班卡已同意＋簽核中時數總和，按勞基法規定不計入週間國定假日、補假日及週間天災日前8小時加班時數。</span>
              </li>
              <li>
                <span>當季加班時數係以每年1月起算類推，計算週期為1-3月、4-6月、7-9月及10-12月。</span>
              </li>
            </ol>
          </div>
        </template>

        <div class="col-12">
          <span>※ 填報說明：</span>
          <ol>
            <li>
              <span>同仁於平日有加班需求者，請於當日下午4時前填報本表單。如於星期六（休息日）、國定假日、彈性放假日有加班需求者，請於前一工作日下午4時前填報本表單。</span>
            </li>
            <li>
              <span>同仁如因公出或出差之情形事先填報本表單有困難者，得展延至次一工作日補填申請。</span>
            </li>
            <li>
              <span>同仁實際加班時數，仍應另填加班卡。</span>
            </li>
            <li>
              <span>預定時數僅供參考，同仁實際加班時數仍應遵守勞基法規定，避免影響身心健康。</span>
            </li>
            <li>
              <span>同仁預計申請加班之計畫編號如不只一個且含其他部門之計畫編號，請同仁於加班事由說明，並自行加會該計畫部門主管。</span>
            </li>
          </ol>
        </div>
      </div>
    </div>
  </div>

  <div class="row mt-2 mx-0">
    <div class="col text-center">
      <button
        type="button"
        :class="['btn mx-2', (((checkCanWorkOvertime === false) || (submitted === true)) ? 'btn-outline-secondary' : 'btn-primary')]"
        :disabled="(checkCanWorkOvertime === false) || (submitted === true)"
        @click="onSubmit"
      >
        <span>送出</span>
      </button>
      <button
        type="button"
        :class="['btn mx-2', ((((project === null) && (reason.length === 0)) || (submitted === true)) ? 'btn-outline-secondary' : 'btn-secondary')]"
        :disabled="((project === null) && (reason.length === 0)) || (submitted === true)"
        @click="onResetForm"
      >
        <span>重填</span>
      </button>
    </div>
  </div>

  <Dialog
    v-model:visible="alarmDialogVisible"
    modal
    :closable="false"
  >
    <template #header>
      <span class="p-dialog-title">提醒</span>
    </template>
    <p class="m-0">
      {{ alarmMessage }}
    </p>
    <template #footer>
      <button
        type="button"
        class="btn btn-primary"
        @click="onCheckAlarmMessage"
      >
        <span>是</span>
      </button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import Dialog from 'primevue/dialog'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'
import { useAuthUserStore } from '../store/index'
import { useMessageStore } from '../store/message'
import { useAbortController } from '../composable/abortController'
import { useOvertimeCompensatory } from '../composable/overtimeCompensatory'
import { useOvertimeData } from '../composable/overtimeData'
import { useWorkday } from '../composable/workdays'
import { useProject } from '../composable/project'
import { useEmployeeData } from '../composable/employeeData'
import { useCanWorkOvertimeEmployeeData } from '../composable/canWorkOvertimeEmployeeData'
import { useAddSigner } from '../composable/signer'
import { GET_DATECANFILLB1CARDAPP_URL, POST_SUBMITB1CARDAPP_URL } from '../api/appUrl'
import { SHAKE_RESIST_TIME, SMALL_DATE_TIME, SYSTEM_ERROR_MESSAGE, REASON_TEXTAREA_MAX, SIGNERS_MAX, FORM_ID } from '../api/appConst'
import type { EmployeeStoreBaseType, SignerType, ProjectType, MonthType, DayType, UploadedFileType, B1CardAppSubmitType } from '../api/appType'
import { getCalendarDayClass, onSearchEmployeeData } from '../api/appFunction'
import ChooseUser from '../components/ChooseUser.vue'
import ChooseColleague from '../components/ChooseColleague.vue'
import ProjectSelect from '../components/ProjectSelect.vue'
import RocCalendarSelect from '../components/RocCalendarSelect.vue'
import AttachedFile from '../components/AttachedFile.vue'
import OvertimeInfo from '../components/OvertimeInfo.vue'
import { onBeforeRouteLeave } from 'vue-router'
import { routerExtend } from '../router'

const userStore = useAuthUserStore()
const confirm = useConfirm()
const toast = useToast()
const { overtimeCompensatory, onGetOvertimeCompensatory } = useOvertimeCompensatory()
const { overtimeDateStatic, overtimeDate, overtimeData, onSetOvertimeDate, onGetOvertimeData } = useOvertimeData()
const { workdays, calendarLoaded, onLoadCalendarData } = useWorkday()
const { projectData, onGetProjectsDateRange } = useProject()
const { employeeData, onSetEmployeeData } = useEmployeeData()
const { canWorkOvertimeEmployeeData, onSetCanWorkOvertimeEmployeeData } = useCanWorkOvertimeEmployeeData()
const { abortController, fetchErrorHandler, abortListener } = useAbortController()
const { signers, signersString, onChangeSigner, onChangeSigners, onAddSigner, onDeleteSigner, onDeleteSigners, onCheckSigner } = useAddSigner()

/** 前端開始填表時間 */
const filledTime: Date = new Date()

const errorMessage = ref<string>('')
const warnMessage = ref<string>('')
const shake = ref<{
  date: boolean
  project: boolean
  overTimeHour: boolean
  reason: boolean
  signer: boolean
}>({
  date: false,
  project: false,
  overTimeHour: false,
  reason: false,
  signer: false
})

const checkCanWorkOvertime = ref<boolean>(false)
const submitted = ref<boolean>(false)

const employee = ref<EmployeeStoreBaseType>({
  userId: userStore.userId,
  userName: userStore.userName,
  deptNo: userStore.deptNo,
  deptSName: userStore.deptSName
})
const project = ref<ProjectType | null>(null)
const reason = ref<string>('')
const overtimeHours = ref<number>(1)
const overtimeCompensatoryCode = ref<number>(1)
const alarmDialogVisible = ref<boolean>(false)
const alarmMessage = ref<string>('')
const uploadedFiles = ref<Array<UploadedFileType>>([])

/**
 * 切換申請人
 * @param event 單一名員工的資料
 */
const onChangeEmployee = (event: EmployeeStoreBaseType): void => {
  const tempEmployee = employee.value
  const tempProject = project.value
  const tempErrorMessage = errorMessage.value

  employee.value = {
    userId: event.userId,
    userName: event.userName,
    deptNo: event.deptNo,
    deptSName: event.deptSName
  }
  project.value = null

  if (tempProject !== null) {
    confirm.require({
      group: 'app',
      header: '提醒',
      message: '目前填報計畫的資料將被清空，是否繼續？',
      acceptProps: {
        severity: 'primary'
      },
      rejectProps: {
        severity: 'secondary'
      },
      accept: (): void => {
        onLoadOvertimeCard()
      },
      reject: (): void => {
        employee.value = tempEmployee
        project.value = tempProject
        errorMessage.value = tempErrorMessage
      }
    })
  } else if (signers.value.find((e: SignerType) => e.userId === event.userId) !== undefined) {
    const tempAddSigner = signers.value
    onDeleteSigners()

    confirm.require({
      group: 'app',
      header: '提醒',
      message: '目前加會人員的資料將被清空，是否繼續？',
      acceptProps: {
        severity: 'primary'
      },
      rejectProps: {
        severity: 'secondary'
      },
      accept: (): void => {
        onLoadOvertimeCard()
      },
      reject: (): void => {
        employee.value = tempEmployee
        project.value = tempProject
        errorMessage.value = tempErrorMessage
        onChangeSigners(tempAddSigner)
      }
    })
  } else if (onCheckSigner() === true) {
    onDeleteSigners()
  } else {
    onLoadOvertimeCard()
  }
}

const onSelectHour = (event: Event): void => {
  overtimeHours.value = parseInt((event.target as HTMLSelectElement).value, 10)
}

const onSelectProject = (projectData: ProjectType) => {
  project.value = projectData
}

const onDeleteProject = (): void => {
  project.value = null
}

const onAddSignerClick = (): void => {
  if (signers.value.find((e: SignerType) => e.userId === null)) {
    toast.add({
      severity: 'warn',
      summary: '請先輸入加會人員',
      group: 'app'
    })
  } else if (signers.value.length >= SIGNERS_MAX) {
    toast.add({
      severity: 'warn',
      summary: '僅開放最多加會' + SIGNERS_MAX + '名人員',
      group: 'app'
    })
  } else {
    onAddSigner()
  }
}

const onUploadFiles = (event: Array<UploadedFileType>): void => {
  uploadedFiles.value = event
}

const onSubmit = (): void => {
  let checkSubmitted: number = 0
  submitted.value = true
  confirm.require({
    group: 'app',
    header: '提醒',
    message: '確認送出表單？',
    acceptProps: {
      severity: 'primary'
    },
    rejectProps: {
      severity: 'secondary'
    },
    accept: (): void => {
      checkSubmitted++
      if (checkSubmitted === 1) {
        if (onCheckSigner() === true) {
          shake.value.signer = true
          toast.add({
            severity: 'warn',
            summary: '請輸入或選取加會人員',
            group: 'app'
          })
          submitted.value = false
          setTimeout((): void => {
            shake.value.signer = false
          }, SHAKE_RESIST_TIME)
        } else {
          const sentTime: Date = new Date() // 前端按送出的時間
          const postData: B1CardAppSubmitType = {
            b1_EmpNo: employee.value.userId,
            b1_DeptNo: employee.value.deptNo,
            b1_Date: overtimeDate.value,
            b1_Hour: overtimeHours.value,
            b1_Code: overtimeCompensatoryCode.value.toString(),
            b1_PrjNo: project.value ? project.value.id : '',
            b1_Reason: reason.value,
            b1_WritedEmpNo: userStore.userId,
            b1_WDate: sentTime,
            addSigners: signersString.value,
            createdTime: sentTime,
            filledTime: filledTime,
            uploadedFiles: uploadedFiles.value
          }

          fetch(POST_SUBMITB1CARDAPP_URL, {
            method: 'POST',
            headers: {
              'content-type': 'application/json'
            },
            body: JSON.stringify(postData),
            signal: abortController.signal
          }).then((res: Response): Promise<any> => {
            if (!res.ok) {
              throw new Error(res.status.toString())
            }
            return res.json()
          }).then((res: any): void => {
            const errorMsg: string = res.ErrorMessage
            const alarmMsg: string = res.HoursAlarmMessage
            if (errorMsg.length === 0 && alarmMsg.length === 0) {
              const messageStore = useMessageStore()
              messageStore.setData('表單已送出')
              routerExtend.pushHandler('Message')
            } else if (errorMsg.length === 0 && alarmMsg.length > 0) {
              const messageStore = useMessageStore()
              messageStore.setData('表單已送出')
              alarmMessage.value = alarmMsg
              alarmDialogVisible.value = true
            } else {
              toast.add({
                severity: 'warn',
                summary: errorMsg,
                group: 'app'
              })
            }
          }).catch((err: Error): void => {
            console.error(err)
            fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
          }).finally((): void => {
            submitted.value = false
          })
        }
      }
    },
    reject: (): void => {
      submitted.value = false
    }
  })
}

const onResetForm = (): void => {
  submitted.value = true
  confirm.require({
    group: 'app',
    header: '提醒',
    message: '確認重置表單？',
    acceptProps: {
      severity: 'primary'
    },
    rejectProps: {
      severity: 'secondary'
    },
    accept: (): void => {
      toast.add({
        severity: 'success',
        summary: '表單已重置',
        group: 'app'
      })

      employee.value = {
        userId: userStore.userId,
        userName: userStore.userName,
        deptNo: userStore.deptNo,
        deptSName: userStore.deptSName
      }
      project.value = null
      reason.value = ''
      onSetOvertimeDate(new Date())
      onLoadOvertimeCard()
      submitted.value = false
    },
    reject: (): void => {
      submitted.value = false
    }
  })
}

const onCheckAlarmMessage = (): void => {
  alarmMessage.value = ''
  alarmDialogVisible.value = false
  routerExtend.pushHandler('Message')
}

const onClickDate = async (): Promise<void> => {
  const thisMonthFirstDate = new Date(overtimeDate.value.getFullYear(), overtimeDate.value.getMonth(), 1)
  const nextMonthFirstDate = new Date(overtimeDate.value.getFullYear(), overtimeDate.value.getMonth() + 1, 0)
  const startDate = new Date(overtimeDate.value.getFullYear(), overtimeDate.value.getMonth(), 1 - thisMonthFirstDate.getDay())
  const endDate = new Date(overtimeDate.value.getFullYear(), overtimeDate.value.getMonth() + 1, 6 - nextMonthFirstDate.getDay())

  try {
    await onLoadCalendarData(startDate, endDate, employee.value.userId, abortController.signal)
  } catch (err: unknown) {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  }
}

const onChangeDate = (event: Date): void => {
  if (event >= SMALL_DATE_TIME.floor && event < SMALL_DATE_TIME.ceil) {
    const tempDate = overtimeDate.value
    const tempProject = project.value
    const tempErrorMessage = errorMessage.value

    onSetOvertimeDate(event)
    project.value = null
    errorMessage.value = ''

    if (tempProject !== null) {
      confirm.require({
        group: 'app',
        header: '提醒',
        message: '目前填報計畫的資料將被清空，是否繼續？',
        acceptProps: {
          severity: 'primary'
        },
        rejectProps: {
          severity: 'secondary'
        },
        accept: (): void => {
          onLoadOvertimeCard()
        },
        reject: (): void => {
          onSetOvertimeDate(tempDate)
          project.value = tempProject
          errorMessage.value = tempErrorMessage
        }
      })
    } else {
      onLoadOvertimeCard()
    }
  } else {
    toast.add({
      severity: 'warn',
      summary: '系統不支援該日期',
      group: 'app'
    })
  }
}

const onChangeYearMonth = async (event: MonthType): Promise<void> => {
  const thisMonthFirstDate = new Date(event.year, event.month, 1)
  const nextMonthFirstDate = new Date(event.year, event.month + 1, 0)
  const startDate = new Date(event.year, event.month, 1 - thisMonthFirstDate.getDay())
  const endDate = new Date(event.year, event.month + 1, 6 - nextMonthFirstDate.getDay())

  try {
    await onLoadCalendarData(startDate, endDate, employee.value.userId, abortController.signal)
  } catch (err: unknown) {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  }
}

/**
 * 查詢該日期是否可以填報加班申請卡 & 取得前端填報時需要即時反應的錯誤訊息
 */
const onCheckDateCouldFillCard = async (): Promise<void> => {
  await onLoadDateCanFillCard()

  if (checkCanWorkOvertime.value === true) {
    onLoadProjectsDateRange()
  } else {
    signers.value = []
    shake.value.date = true
    setTimeout((): void => {
      shake.value.date = false
    }, SHAKE_RESIST_TIME)
  }
}

const onLoadDateCanFillCard = async (): Promise<void> => {
  const params = new URLSearchParams({
    empNo: employee.value.userId,
    date: overtimeDate.value.toISOString()
  })
  await fetch(GET_DATECANFILLB1CARDAPP_URL + '?' + params, {
    method: 'GET',
    signal: abortController.signal
  }).then((res: Response): Promise<any> => {
    if (!res.ok) {
      throw new Error(res.status.toString())
    }
    return res.json()
  }).then((res: any): void => {
    errorMessage.value = res.ErrorMessage
    warnMessage.value = res.DateAlarmMessage
    checkCanWorkOvertime.value = res.IsOvertimeAllowed
  }).catch((err: Error): void => {
    console.error(err)
    checkCanWorkOvertime.value = false
    const checkRedirect = fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
    if (checkRedirect === false) {
      errorMessage.value = SYSTEM_ERROR_MESSAGE
    }
  })
}

const onLoadOvertimeData = async (): Promise<void> => {
  try {
    await onGetOvertimeData(employee.value.userId, abortController.signal)
    overtimeHours.value = overtimeData.value ? overtimeData.value.hoursLowerBound : 1
  } catch (err: unknown) {
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  }
}

const onLoadOvertimeCard = async (): Promise<void> => {
  await Promise.all([
    onSetCanWorkOvertimeEmployeeData(userStore.deptNo, abortController.signal),
    onSetEmployeeData(employee.value.deptNo, abortController.signal),
    onCheckDateCouldFillCard(),
    onLoadOvertimeData()
  ]).catch((err: Error): void => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })
}

const onLoadOvertimeCompensatory = async (): Promise<void> => {
  try {
    await onGetOvertimeCompensatory(abortController.signal)
  } catch (err: unknown) {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  }
}

const onLoadProjectsDateRange = async (): Promise<void> => {
  try {
    await onGetProjectsDateRange(overtimeDate.value, overtimeDate.value, employee.value.deptNo, abortController.signal)
  } catch (err: unknown) {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  }
}

onBeforeRouteLeave((): void => {
  toast.removeGroup('app')
})

onMounted(async (): Promise<void> => {
  abortListener()
  onSetOvertimeDate(filledTime)
  onSetCanWorkOvertimeEmployeeData(userStore.deptNo, abortController.signal)
  await onSetEmployeeData(employee.value.deptNo, abortController.signal).catch((err: Error): void => {
    console.error(err)
    fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
  })
  Promise.all([
    onCheckDateCouldFillCard(),
    onLoadOvertimeData(),
    onLoadOvertimeCompensatory()
  ])
})
</script>
<style lang="scss" scoped>
@use '../assets/scss/calendar';
</style>