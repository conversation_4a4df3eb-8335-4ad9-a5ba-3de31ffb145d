﻿// SerilogAsyncExceptionFilter, General exception handler
// 2022/07/05
// By 林志偉

using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;

namespace Sinotech.Mis.Serilog.Utilities.Filters
{
    [ExcludeFromCodeCoverage]
    public class SerilogAsyncExceptionFilter : IAsyncExceptionFilter
    {
        private readonly ILogger<SerilogAsyncExceptionFilter> _logger;
        [ExcludeFromCodeCoverage]
        public SerilogAsyncExceptionFilter(ILogger<SerilogAsyncExceptionFilter> logger)
        {
            _logger = logger;
        }
        [ExcludeFromCodeCoverage]
        public Task OnExceptionAsync(ExceptionContext context)
        {
            if (!context.ExceptionHandled)
            {
                //var logger = context.HttpContext.RequestServices.GetService<ILogger<SerilogAsyncExceptionFilter>>();
                //_logger.LogError(context.Exception, "Error in Action: {action}", context.ActionDescriptor.DisplayName);
                string currentAction = context.RouteData.Values["Action"] as string;
                string currentController = context.RouteData.Values["Controller"] as string;
                _logger.LogError(context.Exception,
                    "Error in Controller {controller}, Action: {action}",
                    currentController, currentAction);
            }
            //context.ExceptionHandled = true; 

            return Task.CompletedTask;
        }
    }
}
