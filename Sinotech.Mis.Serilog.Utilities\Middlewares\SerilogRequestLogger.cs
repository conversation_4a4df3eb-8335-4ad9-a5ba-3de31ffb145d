﻿// SerilogRequestLogger, set properties, output response body
// 2022/06/25
// By 林志偉

using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Serilog.Context;
using Sinotech.Mis.HR.Attendance.Utilities;
using System;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Sinotech.Mis.Serilog.Utilities.Middlewares
{
    // https://www.carlrippon.com/adding-useful-information-to-asp-net-core-web-api-serilog-logs/
    // https://blog.poychang.net/logging-http-request-in-asp-net-core/
    [ExcludeFromCodeCoverage]
    public class SerilogRequestLogger
    {
        readonly RequestDelegate _next;
        [ExcludeFromCodeCoverage]
        public SerilogRequestLogger(RequestDelegate next)
        {
            _next = next ?? throw new ArgumentNullException(nameof(next));
        }

        [ExcludeFromCodeCoverage]
        public async Task Invoke(HttpContext httpContext, ILogger<SerilogRequestLogger> logger)
        {
            if (httpContext == null)
            {
                throw new ArgumentNullException(nameof(httpContext));
            }

            // Getting the request body is a little tricky because it's a stream
            // So, we need to read the stream and then rewind it back to the beginning
            // https://iter01.com/598002.html
            httpContext.Request.EnableBuffering();

            Stream body = httpContext.Request.Body;
            byte[] buffer = new byte[Convert.ToInt32(httpContext.Request.ContentLength)];
            await httpContext.Request.Body.ReadAsync(buffer, 0, buffer.Length);

            var requestBody = Encoding.UTF8.GetString(buffer);
            if (httpContext.Request.Path.Value != null
                && httpContext.Request.Path.Value.ToLower().Contains("/account/login"))
            {
                string pattern = "\"password\":\"(.*?)\"";
                string replacement = "\"password\":\"XXXXXXXXXXXX\"";
                string result= Regex.Replace(requestBody, pattern, replacement,
                            RegexOptions.NonBacktracking, TimeSpan.FromSeconds(0.5));
                pattern = "\"password\": \"(.*?)\""; // 冒號後面有空格再處理一次，避免疏露
                result = Regex.Replace(result, pattern, replacement,
                            RegexOptions.NonBacktracking, TimeSpan.FromSeconds(0.5));
                requestBody = result;
            }
            body.Seek(0, SeekOrigin.Begin);
            httpContext.Request.Body = body;

            // Push the user name into the log context so that it is included in all log entries
            LogContext.PushProperty("Username", httpContext.User?.Identity?.Name);

            LogContext.PushProperty("UserAgent", httpContext.Request.Headers["User-Agent"]);
            var (ip, hostname) = CardUtility.GetIP_Hostname(httpContext.Request.HttpContext);
            LogContext.PushProperty("ClientIp", ip);
            LogContext.PushProperty("MachineName", hostname);
            LogContext.PushProperty("RequestMethod", httpContext.Request.HttpContext.Request.Method.ToUpper());
            LogContext.PushProperty("RequestProtocol", httpContext.Request.Protocol);
            LogContext.PushProperty("RequestQueryString", httpContext.Request.QueryString);
            LogContext.PushProperty("RequestContentType", httpContext.Request.ContentType);
            LogContext.PushProperty("ResquestPath", httpContext.Request.Path);
            LogContext.PushProperty("RequestHeaders", httpContext.Request.Headers.ToDictionary(h => h.Key, h => h.Value.ToString()), destructureObjects: true);
            LogContext.PushProperty("RequestBody", requestBody);
            LogContext.PushProperty("RequestHost", httpContext.Request.Host.Value);
            LogContext.PushProperty("RequestScheme", httpContext.Request.Scheme);

            // Do not output to save space
            //var logger = Log.ForContext<SerilogRequestLogger>();
            //logger.LogInformation("Request information {RequestMethod} {RequestPath} information", httpContext.Request.Method, httpContext.Request.Path);

            // The reponse body is also a stream so we need to:
            // - hold a reference to the original response body stream
            // - re-point the response body to a new memory stream
            // - read the response body after the request is handled into our memory stream
            // - copy the response in the memory stream out to the original response stream
            using (var responseBodyMemoryStream = new MemoryStream())
            {
                var originalResponseBodyReference = httpContext.Response.Body;
                httpContext.Response.Body = responseBodyMemoryStream;

                await _next(httpContext);

                httpContext.Response.Body.Seek(0, SeekOrigin.Begin);
                var responseBody = await new StreamReader(httpContext.Response.Body).ReadToEndAsync();
                httpContext.Response.Body.Seek(0, SeekOrigin.Begin);

                LogContext.PushProperty("ResponseBody", responseBody);
                LogContext.PushProperty("StatusCode", httpContext.Response.StatusCode);
                logger.LogInformation("Response information {RequestMethod} {RequestPath} {StatusCode}",
                              httpContext.Request.Method,
                              httpContext.Request.Path,
                              httpContext.Response.StatusCode);

                await responseBodyMemoryStream.CopyToAsync(originalResponseBodyReference);
            }
        }
    }
}
