﻿using System;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 取得特定時間區間內所有表單的請求
    /// </summary>
    public class GetFormCardsRequest
    {
        /// <summary>
        /// 啟始填表日期 或 內容查詢啟始日期 
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 結束填表日期 或 內容查詢結束日期 
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 計畫編號，若不填則傳回所有
        /// </summary>
        public string ProjNo { get; set; } = string.Empty;

        /// <summary>
        /// 員工編號，若不填則傳回所有
        /// </summary>
        public string EmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 部門編號，若為0則是全部
        /// </summary>
        public int DeptNo { get; set; } = 0;

        /// <summary>
        /// 表單狀態，若為0則是全部
        /// </summary>
        public int Status { get; set; } = 0;
    }
}
