﻿using System;
using System.Collections.Generic;
using System.Data;

namespace Sinotech.Mis.Common
{
    /// <summary>
    /// 員工基本資料Simple
    /// </summary>
    public class EmployeeSimple
    {
        /// <summary>
        /// 員工編號
        /// </summary>
        public string EmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 中文姓名
        /// </summary>
        public string CName { get; set; } = string.Empty;

        /// <summary>
        /// 部門編號
        /// </summary>
        public int DeptNo { get; set; }

        /// <summary>
        /// 部門簡稱
        /// </summary>
        public string DeptSName { get; set; } = string.Empty;


        /// <summary>
        /// 將 List Employee 轉換為 List EmployeeSimple 物件
        /// </summary>
        /// <param name="list">Employee list</param>
        /// <returns>EmployeeSimple list</returns>
        public static List<EmployeeSimple> EmployeeToEmployeeSimple(List<Employee> list)
        {
            List<EmployeeSimple> result = new List<EmployeeSimple>();
            foreach (var item in list)
            {
                result.Add(EmployeeToEmployeeSimple(item));
            }
            return result;
        }

        /// <summary>
        /// 將 DataRow 轉換為 EmployeeSimple 物件
        /// </summary>
        /// <param name="row"></param>
        /// <returns></returns>
        public static EmployeeSimple FromDataRow(DataRow row)
        {
            EmployeeSimple employee = new EmployeeSimple();
            if (row.Table.Columns.Contains("EmpNo") && row["EmpNo"] != DBNull.Value)
            {
                employee.EmpNo = (string)row["EmpNo"];
            }
            if (row.Table.Columns.Contains("CName") && row["CName"] != DBNull.Value)
            {
                employee.CName = (string)row["CName"];
            }
            if (row.Table.Columns.Contains("DeptNo") && row["DeptNo"] != DBNull.Value)
            {
                employee.DeptNo = (int)row["DeptNo"];
            }
            if (row.Table.Columns.Contains("DeptSName") && row["DeptSName"] != DBNull.Value)
            {
                employee.DeptSName = (string)row["DeptSName"];
            }

            return employee;
        }

        /// <summary>
        /// 將 Employee 物件轉換為 EmployeeSimple 物件
        /// </summary>
        /// <param name="employee">要轉換的員工物件</param>
        /// <returns>轉換後的 EmployeeSimple 物件</returns>
        public static EmployeeSimple EmployeeToEmployeeSimple(Employee employee)
        {
            if (employee == null)
                return null!;

            return new EmployeeSimple
            {
                EmpNo = employee.EmpNo,
                CName = employee.CName,
                DeptNo = employee.DeptNo,
                DeptSName = employee.DeptSName
            };
        }
    }
}
