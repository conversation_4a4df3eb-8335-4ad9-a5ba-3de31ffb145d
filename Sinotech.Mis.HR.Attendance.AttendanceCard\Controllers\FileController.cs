﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers
{
    /// <summary>
    /// 處理檔案上傳API
    /// </summary>
    [Authorize]
    [ApiController]
    [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
    [Route("/api/[controller]/[action]")]
    public class FileController : ControllerBase
    {

        private readonly IConfiguration _Configuration;
        private readonly ILogger<FileController> _logger;

        /// <summary>
        /// FileController 建構子
        /// </summary>
        /// <param name="configuration"></param>
        /// <param name="logger"></param>
        public FileController(IConfiguration configuration, ILogger<FileController> logger)
        {
            _Configuration = configuration;
            _logger = logger;
        }

        /// <summary>
        /// 檔案上傳
        /// </summary>
        /// <param name="files">檔案</param>
        /// <returns>上傳檔案詳細資料</returns>
        [HttpPost]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public async Task<IActionResult> Upload(List<IFormFile> files)
        {

            // full path to file in temp location
            string uploadFolder = _Configuration.GetValue<string>("UploadDirectory");
            string randomFolderName = Path.GetRandomFileName();
            string folderPath = Path.Combine(uploadFolder, randomFolderName);
            List<UploadedFile> list = []; // Copilot 建議的寫法

            try
            {
                if (!Directory.Exists(folderPath))
                {
                    Directory.CreateDirectory(folderPath);
                }

                foreach (IFormFile formFile in files)
                {
                    if (formFile.Length > 0)
                    {
                        UploadedFile uploaded = new UploadedFile();
                        //附檔名統一轉小寫
                        string ext = Path.GetExtension(formFile.FileName).ToLower();
                        string name = Path.GetFileNameWithoutExtension(formFile.FileName);
                        string newFileName = $"{name}{ext}";
                        string fileName = Path.Combine(folderPath, newFileName);

                        using (FileStream stream = new FileStream(fileName, FileMode.Create))
                        {
                            await formFile.CopyToAsync(stream);
                        }
                        string returnFilePathName = $"{randomFolderName}/{newFileName}";
                        uploaded.FilePathName = returnFilePathName;
                        uploaded.FileName = newFileName;
                        uploaded.Size = formFile.Length;
                        list.Add(uploaded);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in FileController.Upload {Message} {StackTrace} ", ex.Message, ex.StackTrace);
            }
            return Ok(new { list });
        }

    }
}
