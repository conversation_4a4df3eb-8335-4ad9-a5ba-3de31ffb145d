<template>
  <CardInbox
    :modelValue="data"
    :approveComment="approveComment"
  >
    <template #header>
      <i class="bi bi-vector-pen me-1" />
      <span>待核卡 / </span>
      <span>{{ FORM_ID.A1Card }}</span>
    </template>
  </CardInbox>
  <div class="container border border-2 px-0">
    <A1Card :modelValue="data" />
  </div>
  <div class="container px-0">
    <FormFlow
      :modelValue="data"
      :editComment="true"
      @update:approveComment="onUpdateComment"
    />
  </div>

  <div class="container px-0">
    <div class="text-black-50 text-end">
      <span>
        查詢時間：
      </span>
      <span class="fst-italic">
        {{ dateToRocString(cardStore.queryDataTime) }}
      </span>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { storeToRefs } from 'pinia'
import { useToast } from 'primevue/usetoast'
import { useCardStore } from '../store/card'
import { onBeforeRouteLeave } from 'vue-router'
import { FORM_ID } from '../api/appConst'
import { dateToRocString } from '../api/appFunction'
import CardInbox from '../components/CardInbox.vue'
import A1Card from '../components/A1Card.vue'
import FormFlow from '../components/FormFlow.vue'

const approveComment = ref<string>('')
const cardStore = useCardStore()
const { data } = storeToRefs(cardStore)
const toast = useToast()

const onUpdateComment = (event: string): void => {
  approveComment.value = event
}

onBeforeRouteLeave(() => {
  cardStore.$reset()
  toast.removeGroup('app')
})
</script>