﻿using Sinotech.Mis.Common;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.PositionType
{
    public abstract class B1CardPositionBase : IB1CardPosition
    {
        protected readonly string _employeeNumber;
        protected readonly DateTime _overtimeDate;
        protected readonly IB1CardDataProvider _provider;
        protected B1CardResult SuccessResult { get; }
        private const int QuartlyOvertimeUpperBound = 138;

        // 提供前端統一用字
        internal static B1CardResult TimeNotInSequenceResult { get; } =
            new B1CardResult(
                        code: 303,
                        status: B1CardStatusEnum.Error,
                        message: "【加班截止時間】需晚於【加班起始時間】，請修正"
                        );
        internal static B1CardResult OverlappedTimeSegmentResult { get; } =
            new B1CardResult(
                        code: 308,
                        status: B1CardStatusEnum.Error,
                        message: "加班時間請勿重疊"
                        );

        /// <summary>
        /// 建構函數，建立代表該員工的加班規則物件
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="overtimeDate">加班日</param>
        /// <param name="dataProvider">資料輔助物件，用以讀取資料</param>
        public B1CardPositionBase
            (string employeeNumber, DateTime overtimeDate, IB1CardDataProvider dataProvider)
        {
            _employeeNumber = employeeNumber;
            _overtimeDate = overtimeDate;
            _provider = dataProvider;
            SuccessResult = new B1CardResult(
                code: 0,
                status: B1CardStatusEnum.Success,
                message: String.Empty
                );
        }

        #region Abstract Members / Functions

        /// <summary>
        /// 是否可加班
        /// </summary>
        internal abstract bool IsOvertimeAllowed { get; }

        /// <summary>
        /// 每日加班時數限制
        /// </summary>
        /// <returns></returns>
        protected abstract B1CardResult CheckDailyOvertimeHours();

        /// <summary>
        /// 每月加班時數限制
        /// </summary>
        /// <returns></returns>
        protected abstract B1CardResult CheckMonthlyOvertimeHours();

        /// <summary>
        /// 每季加班時數限制
        /// </summary>
        /// <returns></returns>
        protected abstract B1CardResult CheckQuarterlyOvertimeHours();

        /// <summary>
        /// 加班申請卡勾稽
        /// </summary>
        /// <returns></returns>
        protected abstract B1CardResult CheckAppliedHours();

        /// <summary>
        /// 是否已填加班申請卡
        /// </summary>
        /// <returns></returns>
        protected abstract bool HasAppliedOvertimeWork();

        /// <summary>
        /// 取出加班時數警告訊息
        /// </summary>
        /// <returns></returns>
        protected abstract B1CardResult GetWarningResult();

        #endregion

        #region 檢查加班卡資料

        internal B1CardResult CheckData()
        {
            // 根據測試意見調整檢查順序，這樣比較合理
            ////////////////////////////////////
            // 申請人
            ////////////////////////////////////
            var result = CheckIsOvertimeAllowed();
            if (result.Status != B1CardStatusEnum.Success)
            {
                return result;
            }

            ////////////////////////////////////
            // 加班日期
            ////////////////////////////////////
            result = CheckOvertimeDate();
            if (result.Status != B1CardStatusEnum.Success)
            {
                return result;
            }

            /////////////////////////////////////////////////
            /// 是否已填加班申請卡
            /// 原本於 加班申請卡勾稽 時檢查，應於此檢查較為合理
            /////////////////////////////////////////////////
            result = CheckIfAppliedOvertimeWork();
            if (result.Status != B1CardStatusEnum.Success)
            {
                return result;
            }

            ////////////////////////////////////
            // 計畫編號
            ////////////////////////////////////
            result = CheckProject();
            if (result.Status != B1CardStatusEnum.Success)
            {
                return result;
            }

            // 檢查一般資料是否合理
            result = CheckGeneralRules();
            if (result.Status != B1CardStatusEnum.Success)
            {
                return result;
            }

            ////////////////////////////////////
            // 加班時段
            ////////////////////////////////////
            result = CheckOvertimeSegment();
            if (result.Status != B1CardStatusEnum.Success)
            {
                return result;
            }

            ////////////////////////////////////
            // 加班時數計算
            // 1.以「小時」為計算單位。==> 在 CheckGeneralRule 已經檢查
            // 2.加班總時數為各加班時段之加班時數總和，不以累計所有時段總分鐘數再折算時數方式計算。
            //   ==> 在 CheckGeneralRule 中檢查
            ////////////////////////////////////

            ////////////////////////////////////
            // 每日加班時數限制
            ////////////////////////////////////
            result = CheckDailyOvertimeHours();
            if (result.Status != B1CardStatusEnum.Success)
            {
                return result;
            }

            ////////////////////////////////////
            // 申請別
            ////////////////////////////////////
            result = CheckOvertimeType();
            if (result.Status != B1CardStatusEnum.Success)
            {
                return result;
            }

            ////////////////////////////////////
            // 加班申請卡勾稽
            ////////////////////////////////////
            result = CheckAppliedHours();
            if (result.Status != B1CardStatusEnum.Success)
            {
                return result;
            }

            ////////////////////////////////////
            // 每月／每季加班時數限制
            ////////////////////////////////////
            result = CheckMonthlyOvertimeHours();
            if (result.Status != B1CardStatusEnum.Success)
            {
                return result;
            }

            result = CheckQuarterlyOvertimeHours();
            if (result.Status != B1CardStatusEnum.Success)
            {
                return result;
            }

            ////////////////////////////////////////////////////////
            // 加班事由，不可空白 ==> 已於 CheckGeneralRules 檢查
            ////////////////////////////////////////////////////////

            ////////////////////////////////////////////////////////
            // 加會人員 ==> 已於 CheckGeneralRules 檢查
            ////////////////////////////////////////////////////////

            ////////////////////////////////////
            // 加班提醒資訊
            ////////////////////////////////////
            result = GetWarningResult();
            if (result.Status != B1CardStatusEnum.Success)
            {
                return result;
            }

            ////////////////////////////////////
            // OK
            ////////////////////////////////////

            return SuccessResult;
        }

        internal B1CardResult CheckGeneralRules()
        {
            var b1Card = GetB1Card();

            // 應該要有加班資料
            var count = b1Card.Details?.Count ?? 0;
            if (count <= 0)
            {
                return new B1CardResult(
                    code: 301,
                    status: B1CardStatusEnum.Error,
                    message: "應該要有加班資料"
                    );
            }

            var b1CardDetails = b1Card.Details!;

            // 加班日期應為同一天
            var date = b1CardDetails[0].StartTime.Date;
            foreach (var detail in b1CardDetails)
            {
                if (date != detail.StartTime.Date || date != GetDatePart(detail.EndTime))
                {
                    return new B1CardResult(
                        code: 302,
                        status: B1CardStatusEnum.Error,
                        message: "加班日期應為同一天"
                        );
                }
            }

            // 檢查加班時段
            var totalHours = 0;
            foreach (var detail in b1CardDetails)
            {
                if (detail.StartTime >= detail.EndTime)
                {
                    return TimeNotInSequenceResult;
                }

                var timespan = detail.EndTime - detail.StartTime;
                if (timespan.Hours < 1)
                {
                    return new B1CardResult(
                        code: 305,
                        status: B1CardStatusEnum.Error,
                        message: "【加班】申請請以1小時為單位"
                        );
                }

                // 額外檢查填寫時數
                if (timespan.Hours != detail.Hour)
                {
                    throw new ArgumentException($"{nameof(CheckGeneralRules)}:【加班時數】與申報時段不符合");

                    //return new B1CardResult(
                    //    code: 306,
                    //    status: B1CardStatusEnum.Error,
                    //    message: "【加班時數】與申報時段不符合"
                    //);
                }

                totalHours += detail.Hour;
            }

            // 總加班時數應該一致
            if (b1Card.TotalHours != totalHours)
            {
                throw new ArgumentException($"{nameof(CheckGeneralRules)}:【總加班時數】與累計【加班時數】不符合");
                //return new B1CardResult(
                //    code: 307,
                //    status: B1CardStatusEnum.Error,
                //    message: "【總加班時數】與累計【加班時數】不符合"
                //);
            }

            // 檢查時段是否重複
            for (int i = 0; i < b1CardDetails.Count - 1; i++)
            {
                for (int j = i + 1; j < b1CardDetails.Count; j++)
                {
                    if (RangeChecker.CheckOverlap(b1CardDetails[i].StartTime, b1CardDetails[i].EndTime,
                                     b1CardDetails[j].StartTime, b1CardDetails[j].EndTime))
                    {
                        return OverlappedTimeSegmentResult;
                    }
                }
            }

            // 計畫編號，不可空白
            foreach (var detail in b1CardDetails)
            {
                if (string.IsNullOrWhiteSpace((detail.Project ?? String.Empty).Trim()))
                {
                    return new B1CardResult(
                        code: 309,
                        status: B1CardStatusEnum.Error,
                        message: "計畫編號不可空白"
                        );
                }
            }

            // 加班事由，不可空白
            if (string.IsNullOrWhiteSpace((b1Card.Reason ?? String.Empty).Trim()))
            {
                return new B1CardResult(
                    code: 310,
                    status: B1CardStatusEnum.Error,
                    message: "加班事由不可空白"
                    );
            }

            // 加會人員不得重複，不可為申請人本人
            // 加會人員須為社內正式在職員工 ==> 由資料提供者檢查
            if (!string.IsNullOrEmpty(b1Card.AddSigners?.Trim()))
            {
                var persons = b1Card.AddSigners.Split(",");
                var set = new HashSet<string>();
                foreach (var p in persons)
                {
                    var signer = p.Trim();

                    // 檢查格式
                    if (signer.Length <= 0 || signer.Length >= 5)
                    {
                        return new B1CardResult(
                            code: 311,
                            status: B1CardStatusEnum.Error,
                            message: "加會人員員工編號長度不對"
                            );
                    }

                    // if (Regex.IsMatch(signer, @"^\d+$")
                    if (!signer.All(char.IsDigit))
                    {
                        return new B1CardResult(
                            code: 312,
                            status: B1CardStatusEnum.Error,
                            message: "加會人員員工編號須為數字"
                            );
                    }

                    // 檢查重複
                    if (set.Contains(signer))
                    {
                        return new B1CardResult(
                            code: 313,
                            status: B1CardStatusEnum.Error,
                            message: "加會人員重複"
                            );
                    }
                    set.Add(signer);

                    // 加會人員不可為申請人本人
                    if (signer == b1Card.EmpNo)
                    {
                        return new B1CardResult(
                            code: 315,
                            status: B1CardStatusEnum.Error,
                            message: "加會人員員工編號須為數字"
                            );
                    }
                }
            }

            return SuccessResult;

        }

        internal B1CardResult CheckIsOvertimeAllowed()
        {
            if (IsOvertimeAllowed)
            {
                return SuccessResult;
            }

            return new B1CardResult(
                code: 500,
                status: B1CardStatusEnum.Error,
                message: "申請人職務不能申報加班"
                );
        }

        internal B1CardResult CheckIfAppliedOvertimeWork()
        {
            if (!HasAppliedOvertimeWork())
            {
                return new B1CardResult(
                    code: 555,
                    status: B1CardStatusEnum.Error,
                    message: $"請先確認已填加班申請卡，且經部門主管核可，方可填報"
                    );
            }

            return SuccessResult;
        }

        internal B1CardResult CheckOvertimeDate()
        {
            // 週日為例假日，不開放同仁填報加班卡
            var dateInfo = GetOverTimeDateInfo();
            if (dateInfo.DayType == WorkdayType.SundayRegularHoliday ||
                dateInfo.DayType == WorkdayType.SundayHoliday)
            {
                return new B1CardResult(
                    code: 502,
                    status: B1CardStatusEnum.Error,
                    message: "週日為例假日，不得填報加班卡"
                    );
            }

            // 提供加班日期當日刷卡時間以供同仁填報參考

            // .如遇天災停班日不閞放填報
            if (dateInfo.DayType == WorkdayType.WeekNaturalDisasterDay ||
                dateInfo.DayType == WorkdayType.SaturdayNaturalDisasterDay)
            {
                return new B1CardResult(
                    code: 503,
                    status: B1CardStatusEnum.Error,
                    message: "因天災致使本社停班，同仁如應主管要求加班，請提醒部門主管應於事件發生次一日即時填報於內網首頁常用連結公告之—臺北市政府勞動局天災事變突發事件停止假期延長工時通報單，送交行政處以為加班憑據"
                    );
            }

            // 每一申請人每日限填一張加班卡。
            if (GetHasB1CardFilled())
            {
                return new B1CardResult(
                    code: 501,
                    status: B1CardStatusEnum.Error,
                    message: "一天限填一張加班卡，請勿重複填報"
                    );
            }

            return SuccessResult;
        }

        internal B1CardResult CheckOvertimeType()
        {
            var dateInfo = GetOverTimeDateInfo();
            //.加班申請別分為以下三種：
            //  a.加班：提供社內加班及欲支領加班費者填報。
            //  b.社外加班：提供社外加班及欲支領加班費者填報。
            //  c.補休假：提供未來有休假須求而須累計補休假時數者填報。

            //「週間國定假日及補假日」前8小時不得選擇補休假。
            // 如選擇補休假，提示訊息：
            //「國定假日8小時內加班別請選填【加班】或【社外加班】」
            //※	勞基法規定自民國107年3月起，加班可依勞工意願選擇補休，本社於107年第1次勞資會議決議，因勞基法僅公告加班補休適用於平日及休息日，本社國定假日前8小時外，才可填報加班補休。
            if (dateInfo.DayType == WorkdayType.WeekHoliday ||
                dateInfo.DayType == WorkdayType.MakeUpHoliday)
            {
                var b1CardDetails = GetB1Card().Details;
                var hourCount = 0;
                for (int i = 0; i < b1CardDetails.Count; i++)
                {
                    var detail = b1CardDetails[i];
                    // 加班申請別代號  1：加班 2：社外加班 3：補休假
                    if (hourCount < 8 && detail.B1_CODE == 3)
                    {
                        return new B1CardResult(
                            code: 511,
                            status: B1CardStatusEnum.Error,
                            message: "國定假日8小時內加班別請選填【加班】或【社外加班】"
                            );
                    }

                    // 放這裡就不會有跨八小時的問題
                    hourCount += detail.Hour;
                }
            }

            return SuccessResult;
        }

        internal B1CardResult CheckOvertimeSegment()
        {
            //  起始時間～截止時間

            var dateInfo = GetOverTimeDateInfo();
            var b1CardDetails = GetB1Card().Details;

            //  2.每一加班時段之截止時間須大於起始時間。
            //     如不符規定，提示訊息： 
            //    「【加班截止時間】須大於【加班起始時間】，請修正。」

            //  3.每一加班時段之時數至少須為1小時。
            //    如加班時數不足1小時，提示訊息：
            //    「【加班】申請請以1小時為單位」

            // 已經在 CheckGeneralRule 檢查


            //  每一加班時段可填報加班時間範圍
            //      a.工作日及補班日：除「正常上班起始時間～最早彈性下班時間」外，皆可填報加班。
            //        如不符規定，提示訊息：
            //        「您填報的加班時段內包含上班時間，請修正」
            //      b.週間國定假日、補假日、週六休息日、週六國定假日、彈性放假日：全天時間皆可填報加班。

            for (int i = 0; i < b1CardDetails.Count; i++)
            {
                var detail = b1CardDetails[i];
                switch (dateInfo.DayType)
                {
                    case WorkdayType.WeekWorkday:
                    case WorkdayType.MakeUpWorkday:
                        if (RangeChecker.CheckOverlap(detail.StartTime, detail.EndTime,
                            dateInfo.ArrivalTime, dateInfo.FlexibleDepartureBefore))
                        {
                            return new B1CardResult(
                                code: 525,
                                status: B1CardStatusEnum.Error,
                                message: "您填報的加班時段內包含上班時間，請修正"
                                );
                        }
                        break;
                    case WorkdayType.WeekHoliday:
                    case WorkdayType.MakeUpHoliday:
                    case WorkdayType.SaturdayRestday:
                    case WorkdayType.SaturdayHoliday:
                    case WorkdayType.FlexbleHoliday:
                        break;

                    default:
                        throw new InvalidOperationException($"{nameof(B1CardPositionBase)}/{nameof(CheckOvertimeSegment)}: 規格未定義的工作日型態 {dateInfo.DayType}");
                }
            }


            //  4.每一加班時段時間不得重疊。
            //    如加班時間重疊，提示訊息：
            //   「加班時間請勿重疊。」

            // 已於 CheckGeneralRule 查核


            //  5.不同加班時段允許填報相同的計畫編號。


            return SuccessResult;
        }

        #region Default Implementation

        protected B1CardResult DefaultCheckDailyOvertimeHours()
        {
            //每日總工時上限：12小時
            var workdayType = GetOverTimeDateInfo().DayType;
            var b1CardDetails = GetB1Card().Details;

            // 加總加班時數
            var hours = 0;
            for (int i = 0; i < b1CardDetails.Count; i++)
            {
                hours += b1CardDetails[i].Hour;
            }

            // 檢查
            switch (workdayType)
            {
                //「工作日及補班日」加班時數上限：每日總工時上限（12）－正常工時（8）＝4小時。
                //如超時填報，提示訊息：
                //「每日正常工時8小時加延長工時總時數不得超過12小時。」
                case WorkdayType.WeekWorkday:
                case WorkdayType.MakeUpWorkday:
                    if (hours > 4)
                    {
                        return new B1CardResult(
                            code: 536,
                            status: B1CardStatusEnum.Error,
                            message: "每日正常工時8小時加延長工時總時數不得超過12小時"
                            );
                    }
                    break;

                //「休息日及國定假日(註)」加班時數上限：12小時。
                // 「休息日及國定假日」包括週間國定假日、補假日、週六休息日、週六國定假日、
                //                       彈性放假日、週間天災日及週六天災日。
                case WorkdayType.MakeUpHoliday:                 // 補假日
                case WorkdayType.SaturdayRestday:               // 週六休息日
                case WorkdayType.SaturdayHoliday:               // 週六國定假日
                case WorkdayType.FlexbleHoliday:                // 彈性放假日
                case WorkdayType.WeekNaturalDisasterDay:        // 週間天災日
                case WorkdayType.SaturdayNaturalDisasterDay:    // 週六天災日
                case WorkdayType.WeekHoliday:                   // 週間國定假日
                    if (hours > 12)
                    {
                        return new B1CardResult(
                            code: 536,
                            status: B1CardStatusEnum.Error,
                            message: "休息日及國定假日加班總時數不得超過12小時"
                            );
                    }
                    break;

                default:
                    throw new InvalidOperationException($"{nameof(B1CardPositionBase)}/{nameof(CheckDailyOvertimeHours)}: 規格未定義的工作日型態 {workdayType}");

            }

            //「週六（休息日或逢國定假日）及彈性放假日」加班時數須比對加班申請卡時數。
            //如超過申請時數，提示訊息： 
            //「加班時數請勿超過加班申請卡核准之時數。」
            // 改在 CheckAppliedHours 檢查

            //「週間國定假日及補假日」如填報時數不足8小時，提示訊息：
            //「您填報的加班總時數為X小時，該日請依勞基法規定填報8小時計畫編號及加班時數。」
            switch (workdayType)
            {
                case WorkdayType.WeekHoliday:                   // 週間國定假日
                case WorkdayType.MakeUpHoliday:                 // 補假日
                    if (hours < 8)
                    {
                        return new B1CardResult(
                            code: 537,
                            status: B1CardStatusEnum.Error,
                            message: $"您填報的加班總時數為{hours}小時，該日請依勞基法規定填報8小時計畫編號及加班時數"
                            );
                    }
                    break;
            }

            // UI related
            // 如未填報任何計畫加班時數時，須控管不得提供傳送表單功能。


            return SuccessResult;
        }

        protected B1CardResult DefaultCheckMonthlyOvertimeHours()
        {
            var monthlyHours = GetMonthOvertimeHourInQuota();
            var appliedHours = GetAppliedOvertimeHourInQuota();
            var hoursExceeded = monthlyHours + appliedHours - 54;

            if (hoursExceeded > 0)
            {
                return new B1CardResult(
                    code: 538,
                    status: B1CardStatusEnum.Error,
                    message: $"您填報的加班時數已超過當月上限 {hoursExceeded} 小時"
                );
            }

            return SuccessResult;
        }

        protected B1CardResult DefaultCheckQuaterlyOvertimeHours()
        {
            var quarterlyHours = GetQuarterOvertimeHourInQuota();
            var appliedHours = GetAppliedOvertimeHourInQuota();
            var hoursExceeded = quarterlyHours + appliedHours - QuartlyOvertimeUpperBound;

            if (hoursExceeded > 0)
            {
                return new B1CardResult(
                    code: 539,
                    status: B1CardStatusEnum.Error,
                    message: $"填報的加班時數已超過當季上限 {hoursExceeded} 小時"
                );
            }

            return SuccessResult;
        }

        protected B1CardResult DefaultCheckAppliedHours()
        {
            var b1cardApp = GetB1CardApp();
            if (b1cardApp == null)
            {
                throw new InvalidOperationException($"{nameof(DefaultCheckAppliedHours)}: 勾稽時，發現未填寫加班申請卡");
            }

            // 「週六（休息日或逢國定假日）及彈性放假日」加班時數須比對加班申請卡時數。

            var workdayType = GetOverTimeDateInfo().DayType;
            var b1CardDetails = GetB1Card().Details;

            // 加總加班時數
            var hours = 0;
            for (int i = 0; i < b1CardDetails.Count; i++)
            {
                hours += b1CardDetails[i].Hour;
            }

            switch (workdayType)
            {
                case WorkdayType.SaturdayRestday:
                case WorkdayType.SaturdayHoliday:
                case WorkdayType.FlexbleHoliday:
                    var hoursApplied = b1cardApp.B1_Hour;
                    if (hours > hoursApplied)
                    {
                        return new B1CardResult(
                            code: 550,
                            status: B1CardStatusEnum.Error,
                            message: $"加班時數請勿超過加班申請卡核准之時數"
                            );
                    }
                    break;
                default:
                    break;
            }

            return SuccessResult;
        }

        protected bool DefaultHasAppliedOvertimeWork()
        {
            return GetHasAppliedOvertimeWork();
        }

        protected B1CardResult DefaultGetWarningResult()
        {
            var monthlyHours = GetMonthOvertimeHourInQuota();
            var quarterlyHours = GetQuarterOvertimeHourInQuota();
            var thisCardHour = GetB1Card().InOvertimeHours;

            // 如同仁加班時數已達「A.當月第40~53小時」、
            //                 「B.當月第54小時」或
            //                 「C.當季第138小時」，須提醒
            var totalMonthlyHours = monthlyHours + thisCardHour;
            switch (totalMonthlyHours)
            {
                case >= 40 and <= 53:
                    return new B1CardResult(
                        code: 533,
                        status: B1CardStatusEnum.Warning,
                        message: $"提醒您當月填報之加班時數已達{totalMonthlyHours}小時，即將達加班時數上限。如非必要，請準時下班"
                        );

                case 54:
                    return new B1CardResult(
                        code: 536,
                        status: B1CardStatusEnum.Warning,
                        message: $"提醒您當月填報之加班時數已達當月上限"
                        );
                case > 54:
                    throw new InvalidOperationException($"{nameof(GeneralStaff)}/{nameof(GetWarningResult)}: 不應在此出現加班時數超出上限");
            }

            var totalQuarterlyHours = quarterlyHours + thisCardHour;
            switch (totalQuarterlyHours)
            {
                case QuartlyOvertimeUpperBound:
                    return new B1CardResult(
                        code: 539,
                        status: B1CardStatusEnum.Warning,
                        message: $"提醒您當季填報之加班時數已達當季上限"
                        );
                case > QuartlyOvertimeUpperBound:
                    throw new InvalidOperationException($"{nameof(GeneralStaff)}/{nameof(GetWarningResult)}: 不應在此出現加班時數超出上限");
            }

            return SuccessResult;
        }

        #endregion

        internal B1CardResult CheckProject()
        {
            var b1Card = GetB1Card();
            foreach (var detail in b1Card.Details)
            {
                // 取出計畫資料
                var project = GetProjectInfo(detail.Project);
                if (project == null)
                {
                    return new B1CardResult(
                        code: 531,
                        status: B1CardStatusEnum.Error,
                        message: $"找不到計畫編號「{detail.Project}」資料"
                    );
                }

                // 須為加班日期當日已成立且未結案之計畫
                project.BDate = project.BDate?.Date ?? DateTime.MinValue.Date;
                project.EDate = (project.EDate ?? DateTime.MaxValue).Date;
                //project.BDate ??= DateTime.MinValue;
                //project.EDate = (project.EDate ?? DateTime.MaxValue) - TimeSpan.FromDays(1.0);
                if (!RangeChecker.CheckContainment(detail.StartTime, detail.EndTime, (DateTime)project.BDate, (DateTime)project.EDate))
                {
                    return new B1CardResult(
                        code: 532,
                        status: B1CardStatusEnum.Error,
                        message: $"計畫編號「{detail.Project}」須為加班日期當日已成立且未結案之計畫"
                    );
                }

                //檢查計畫編號在B1Card的申請日是否已截止填報
                if (project != null && project.SubmitDueDate != null && project.SubmitDueDate <= b1Card.OvertimeDate)
                {
                    return new B1CardResult(
                        code: 534,
                        status: B1CardStatusEnum.Error,
                        message: $"計畫編號「{detail.Project}」進度已達100%，填報截止日為{project.SubmitDueDate:yyyy/MM/dd}，在{b1Card.OvertimeDate:yyyy/MM/dd}已截止填報"
                    );
                }
            }

            // 不同加班時段允許填報相同的計畫編號

            return SuccessResult;
        }

        #endregion

        #region  Data Provider

        protected bool GetHasAppliedOvertimeWork()
        {
            return _provider.GetHasAppliedOvertimeWork();
        }

        protected bool GetHasB1CardFilled()
        {
            return _provider.GetHasB1CardFilled();
        }

        protected int GetMonthOvertimeHourInQuota()
        {
            // 已經扣除費率 1.0 的時數
            return _provider.GetMonthOvertimeHourInQuota();
        }

        protected int GetQuarterOvertimeHourInQuota()
        {
            // 已經扣除費率 1.0 的時數
            return _provider.GetQuarterOvertimeHourInQuota();
        }

        protected int GetAppliedOvertimeHourInQuota()
        {
            // 已經扣除費率 1.0 的時數
            return _provider.GetAppliedOvertimeHourInQuota();
        }

        protected B1CardPositionEnum GetPositionType()
        {
            return _provider.GetPositionType();
        }

        protected Project? GetProjectInfo(string projectNumber)
        {
            projectNumber = projectNumber.Trim().ToUpper();
            foreach (var p in _provider.GetProjectList())
            {
                if (projectNumber == p?.PrjNo?.Trim().ToUpper())
                {
                    return p;
                }
            }
            return null;
        }

        protected bool IsSpecialStaff()
        {
            return _provider.IsSpecialStaff();
        }

        protected B1Card GetB1Card()
        {
            return _provider.GetB1Card();
        }

        protected Workday GetOverTimeDateInfo()
        {
            return _provider.GetOverTimeDateInfo();
        }

        protected B1CardApp? GetB1CardApp()
        {
            return _provider.GetB1CardApp();
        }

        protected double GetSpecialStaffAllowedMonthlyWeightedOvertimeHours()
        {
            return _provider.GetSpecialStaffAllowedMonthlyWeightedOvertimeHours();
        }

        protected double GetCurrentMonthlyWeightedOvertimeHours()
        {
            return _provider.GetCurrentMonthlyWeightedOvertimeHours();
        }

        protected double GetWeightedOvertimeHours()
        {
            return _provider.GetWeightedOvertimeHours();
        }

        #endregion

        #region Utilites

        private DateTime GetDatePart(DateTime dt)
        {
            if (dt.TimeOfDay.TotalSeconds == 0)
            {
                return dt.Date.Subtract(new TimeSpan(1, 0, 0, 0));
            }
            return dt.Date;
        }

        #endregion
    }
}