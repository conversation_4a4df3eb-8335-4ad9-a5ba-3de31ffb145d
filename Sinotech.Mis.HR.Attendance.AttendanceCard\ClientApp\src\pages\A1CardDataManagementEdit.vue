<template>
  <router-link
    v-slot="{ navigate }"
    :to="{ name: 'DataManagement' }"
    custom
  >
    <button
      type="button"
      class="btn btn-outline-dark bi bi-box-arrow-left mt-2 mx-2"
      :disabled="submitted === true"
      @click="navigate"
    >
      <span>三卡管理</span>
    </button>
  </router-link>
  
  <button
    type="button"
    :class="[
      'btn mt-2 mx-2',
      submitted === true ? 'btn-outline-danger' : 'btn-danger'
    ]"
    :disabled="submitted === true"
    @click="onSave"
  >
    <span>儲存</span>
  </button>

  <h6 class="my-2 fs-2 text-center">
    <i class="bi bi-gear me-1" />
    <span>三卡管理 / 編輯 / </span>
    <span>{{ FORM_ID.A1Card }}</span>
  </h6>
  
  <div class="container border border-2 px-0">
    <div class="row mx-0">
      <div class="col-2 py-2 fw-bold bg-a1card text-center">
        <span>卡號</span>
      </div>
      <div class="col-10 py-2">
        {{ cardStore.data?.formNo }}
      </div>
    </div>
    
    <div class="row mx-0">
      <div class="col-2 py-2 fw-bold bg-a1card text-center">
        <span>申請人</span>
      </div>
      <div class="col-10 py-2">
        {{ (cardStore.data?.empNo ?? '') + ((cardStore.data?.empNo && cardStore.data?.empName) ? ' ' : '') + (cardStore.data?.empName ?? '') }}
      </div>
    </div>

    <div class="row mx-0">
      <div class="col-2 py-2 fw-bold bg-a1card text-center">
        <div class="row">
          <div class="col-12 col-sm-6 p-0 text-sm-end">
            <span>所屬</span>
          </div>
          <div class="col-12 col-sm-6 p-0 text-sm-start">
            <span>部門</span>
          </div>
        </div>
      </div>
      <div class="col-10 py-2">
        {{ cardStore.data?.deptSName }}
      </div>
    </div>

    <div class="row mx-0">
      <div class="col-2 py-2 fw-bold bg-a1card text-center">
        <div class="row">
          <div class="col-12 col-sm-6 p-0 text-sm-end">
            <span>填報</span>
          </div>
          <div class="col-12 col-sm-6 p-0 text-sm-start">
            <span>旬別</span>
          </div>
        </div>
      </div>
      <div class="col-10 py-2">
        {{ cardStore.data?.formInfo }}
      </div>
    </div>

    <div class="row mx-0">
      <div class="col-2 py-2 fw-bold bg-a1card text-center">
        <div class="row">
          <div class="col-12 col-sm-6 p-0 text-sm-end">
            <span>加會</span>
          </div>
          <div class="col-12 col-sm-6 p-0 text-sm-start">
            <span>人員</span>
          </div>
        </div>
      </div>
      <div class="col-10 py-2">
        {{ cardStore.data?.addedSigner }}
      </div>
    </div>

    <div class="row mx-0">
      <div class="col-2 py-2 fw-bold bg-a1card text-center">
        <span>填表人</span>
      </div>
      <div class="col-10 py-2">
        {{ (cardStore.data?.createdEmpNo ?? '') + ((cardStore.data?.createdEmpNo && cardStore.data?.createdName) ? ' ' : '') + (cardStore.data?.createdName ?? '') }}
      </div>
    </div>

    <div class="row mx-0">
      <div class="col-2 py-2 fw-bold bg-a1card text-center">
        <div class="row">
          <div class="col-12 col-sm-6 p-0 text-sm-end">
            <span>填報</span>
          </div>
          <div class="col-12 col-sm-6 p-0 text-sm-start">
            <span>時間</span>
          </div>
        </div>
      </div>
      <div class="col-10 py-2">
        {{ cardStore.data?.createdTime ? dateToRocString(new Date(cardStore.data.createdTime)) : '' }}
      </div>
    </div>

    <div class="row border-top mx-0">
      <DataTable
        :value="projectWorkingHoursData"
        class="px-0"
      >
        <ColumnGroup type="header">
          <Row>
            <Column
              header="計畫工時"
              class="col-2"
              :rowspan="2"
            />
            <template
              v-for="(item, index) in columnData"
              :key="index"
            >
              <Column
                :field="item.field"
                :class="item.workHours === 0 ? 'bg-light' : ''"
              >
                <template #header>
                  <span :class="item.workHours === 0 ? 'text-black-50' : ''">
                    {{ item.dayFormat }}
                  </span>
                </template>
              </Column>
            </template>
          </Row>
          <Row>
            <template
              v-for="(item, index) in columnData"
              :key="index"
            >
              <Column
                :field="item.field"
                :class="item.workHours === 0 ? 'bg-light' : ''"
              >
                <template #header>
                  <span :class="item.workHours === 0 ? 'text-black-50' : ''">
                    {{ item.date.toString() }}
                  </span>
                </template>
              </Column>
            </template>
          </Row>
        </ColumnGroup>

        <Column
          field="projectNo"
          class="text-center"
          :bodyStyle="{ 'min-width': '130px' }"
        >
          <template #body="{ data, field }">
            <a
              class="align-items-center text-primary text-decoration-underline"
              role="button"
              @click="onProjectLinkClick(data['projectName'])"
            >
              {{ data[field] }}
            </a>
          </template>
        </Column>

        <template
          v-for="(item, index) in columnData"
          :key="index"
        >
          <Column
            :field="item.field"
            :class="[
              'text-center',
              (item.workHours === 0 ? 'bg-light' : '')
            ]"
          >
            <template #body="{ data, field }">
              <input
                type="text"
                maxlength="2"
                :aria-label="item.field"
                :style="{ width: '3rem' }"
                :class="[
                  'form-control d-inline-block text-center py-2 px-0',
                  item.workHours === 0 ? 'text-black-50' : '',
                  data['shake-' + field] === true ? 'shake' : ''
                ]"
                :value="data[field]"
                :disabled="(item.workHours === 0) || (shake.column.find(e => e === field) !== undefined) || (submitted === true)"
                @blur="onBlurHour($event, data, field)"
              >
            </template>
          </Column>
        </template>

        <ColumnGroup
          v-if="projectWorkingHoursData.length > 0"
          type="footer"
        >
          <Row>
            <Column
              class="bg-a1card bg-opacity-50"
              footer="計畫工時總計"
              footerClass="text-center"
            />
            <template
              v-for="(item, index) in columnData"
              :key="index"
            >
              <Column class="bg-a1card bg-opacity-50 text-center">
                <template #footer>
                  <span :class="(projectWorkingHoursData.reduce((accumulator: any, currentValue: any) => accumulator + currentValue[item.field], 0) > item.workHours) ? 'text-danger' : ''">
                    {{ projectWorkingHoursData.reduce((accumulator: any, currentValue: any) => accumulator + currentValue[item.field], 0) }}
                  </span>
                </template>
              </Column>
            </template>
          </Row>
        </ColumnGroup>
      </DataTable>
    </div>
  </div>
  <div class="container px-0">
    <FormFlow :modelValue="cardStore.data" />
  </div>

  <div class="container px-0">
    <div class="text-black-50 text-end">
      <span>
        查詢時間：
      </span>
      <span class="fst-italic">
        {{ dateToRocString(cardStore.queryDataTime) }}
      </span>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import DataTable from 'primevue/datatable'
import Row from 'primevue/row'
import Column from 'primevue/column'
import ColumnGroup from 'primevue/columngroup'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'
import { useCardStore } from '../store/card'
import { useAuthUserStore } from '../store/index'
import { useWorkday } from '../composable/workdays'
import { useAbortController } from '../composable/abortController'
import { onBeforeRouteLeave } from 'vue-router'
import { PUT_UPDATEA1CARD_URL } from '../api/appUrl'
import type { WorkdayType, FormSubmitResponseApiType } from '../api/appType'
import { FORM_ID, INFO_DISPLAY_TIME, SYSTEM_ERROR_MESSAGE, REFRESH_PAGE_TOAST_TIME, SHAKE_RESIST_TIME } from '../api/appConst'
import { dateToRocString } from '../api/appFunction'
import FormFlow from '../components/FormFlow.vue'
import { routerExtend } from '../router'

const cardStore = useCardStore()
const userStore = useAuthUserStore()
const confirm = useConfirm()
const toast = useToast()
const { onGetWorkdaysDateRange } = useWorkday()
const { abortController, fetchErrorHandler, abortListener } = useAbortController()

const projectWorkingHoursData = ref<Array<any>>([])

const submitted = ref<boolean>(false)
const shake = ref<{
  column: Array<string>
}>({
  column: []
})
const columnData = ref<Array<{
  field: string
  date: number
  day: number
  dayFormat: string
  workHours: number
}>>([])

const onProjectLinkClick = (projectName: string): void => {
  toast.add({
    severity: 'info',
    summary: '計畫名稱： ' + projectName,
    life: INFO_DISPLAY_TIME,
    group: 'app'
  })
}

const onBlurHour = (event: Event, data: any, field: string): void => {
  const value: number = parseInt((event.target as HTMLInputElement).value, 10)

  if (isNaN(parseInt((event.target as HTMLInputElement).value, 10)) || Number.isNaN(value) || value < 0 || (value.toString() !== (event.target as HTMLInputElement).value)) {
    toast.add({
      severity: 'warn',
      summary: '請填報正確的時數',
      group: 'app'
    })
    data['shake-' + field] = true
    shake.value.column.push(field)
    setTimeout((): void => {
      const indexColumn: number = shake.value.column.findIndex((e: string) => e === field)
      shake.value.column.splice(indexColumn)
      data['shake-' + field] = false
    }, SHAKE_RESIST_TIME)
  } else {
    data[field] = value
  }
}

const onSave = () => {
  let checkSubmitted: number = 0
  submitted.value = true
  confirm.require({
    group: 'app',
    header: '提醒',
    message: '確認儲存？',
    acceptProps: {
      severity: 'primary'
    },
    rejectProps: {
      severity: 'secondary'
    },
    accept: (): void => {
      checkSubmitted++
      if (checkSubmitted === 1) {
        // 因為要傳一旬每天的時數a1_DDHH，需要以下時數的檢查
        const columnDataIndex: number = columnData.value.findIndex((columnEach: {
          field: string
          date: number
          day: number
          dayFormat: string
          workHours: number
        }) => projectWorkingHoursData.value.reduce((accumulator: any, currentValue: any) => accumulator + currentValue[columnEach.field], 0) > columnEach.workHours)

        if (columnDataIndex >= 0) {
          const year: string = cardStore.data?.card.A1_YYMM.substring(0, cardStore.data?.card.A1_YYMM.length - 2)
          const month: string = cardStore.data?.card.A1_YYMM.substring(cardStore.data?.card.A1_YYMM.length - 2)

          toast.add({
            severity: 'warn',
            summary: year + '/' + month + '/' + columnData.value[columnDataIndex].date + ' 填報的工時超過當日的正常工作時數，請修正',
            group: 'app'
          })
          submitted.value = false
        } else {
          const data: Array<any> = []
          projectWorkingHoursData.value.forEach((row: any) => {
            let dayWorkHours: string = ''
            for (let dateNumber: number = 0; dateNumber < row['days']; dateNumber++) {
              dayWorkHours += row['d' + dateNumber].toString()
            }

            data.push({
              id: row.id,
              formUID: cardStore.data?.formUID,
              a1_EMPNO: cardStore.data?.empNo,
              a1_YYMM: cardStore.data?.card.A1_YYMM,
              a1_NN: cardStore.data?.card.A1_NN,
              a1_DDHH: dayWorkHours
            })
          })

          fetch(PUT_UPDATEA1CARD_URL, {
            method: 'PUT',
            headers: {
              'content-type': 'application/json'
            },
            body: JSON.stringify(data),
            signal: abortController.signal
          }).then((res: Response): Promise<FormSubmitResponseApiType> => {
            if (!res.ok) {
              throw new Error(res.status.toString())
            }
            return res.json()
          }).then((res: FormSubmitResponseApiType): void => {
            if (res.Status === 0) {
              toast.add({
                severity: 'success',
                summary: '已編輯',
                life: REFRESH_PAGE_TOAST_TIME,
                group: 'app'
              })

              routerExtend.pushHandler('DataManagement')
            } else {
              toast.add({
                severity: 'warn',
                summary: res.Message,
                group: 'app'
              })
            }
          }).catch((err: Error): void => {
            console.error(err)
            fetchErrorHandler(err, toast, SYSTEM_ERROR_MESSAGE, 'app')
          }).finally((): void => {
            submitted.value = false
          })
        }
      }
    },
    reject: (): void => {
      submitted.value = false
    }
  })
}

const onSetData = (dataSet: Array<WorkdayType>): void => {
  for (let cardIndex: number = 0; cardIndex < cardStore.data?.card.Details.length; cardIndex++) {
    const card: any = cardStore.data?.card.Details[cardIndex]
    const rowData: any = {}

    rowData['id'] = card.ID
    rowData['projectNo'] = card.A1_PROJNO
    rowData['projectName'] = card.ProjectName
    rowData['days'] = card.DayDetails.length

    card.DayDetails.forEach((e: any, index: number) => {
      const field: string = 'd' + index
      rowData[field] = e.hour

      if (cardIndex === 0) {
        const found: WorkdayType | undefined = dataSet.find((day: WorkdayType) => new Date(day.workDate).getDate() === e.day)

        let workHours: number = 0
        if (found?.dayOff !== true) {
          workHours = found?.workHours ?? 0
        }
        columnData.value.push({
          field: field,
          date: e.day,
          day: e.weekday,
          dayFormat: e.rocWeekDay,
          workHours: workHours
        })
      }
    })

    projectWorkingHoursData.value.push(rowData)
  }
}

onBeforeRouteLeave(() => {
  cardStore.$reset()
  toast.removeGroup('app')
})

onMounted(async (): Promise<void> => {
  abortListener()
  if (cardStore.data?.card && cardStore.data?.empNo) {
    let workdayData: Array<WorkdayType> = []
    try {
      workdayData = await onGetWorkdaysDateRange(new Date(cardStore.data?.card.Details[0].DayDetails[0].date), new Date(cardStore.data?.card.Details[0].DayDetails[cardStore.data?.card.Details[0].DayDetails.length - 1].date), cardStore.data.empNo, abortController.signal)
    } catch (err: unknown) {
      console.error(err)
      toast.add({
        severity: 'error',
        summary: SYSTEM_ERROR_MESSAGE,
        group: 'app'
      })
    }

    onSetData(workdayData)
  }
})
</script>
<style lang="scss" scoped>
:deep(.p-datatable-empty-message) {
  display: none;
}
:deep(.p-datatable .p-datatable-column-header-content) {
  justify-content: center;
  color: black;
}
:deep(.p-datatable .p-datatable-header) {
  padding: 0 12px;
  background: #ffffff;
  color: black;
  border-top: none;
}
:deep(.p-datatable .p-datatable-thead > tr > th) {
  background: #ffffff;
}
:deep(.p-datatable .p-datatable-tfoot > tr > td) {
  color: black;
}
</style>