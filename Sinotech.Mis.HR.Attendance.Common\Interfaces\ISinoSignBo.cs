﻿using System.Collections.Generic;

namespace Sinotech.Mis.HR.Attendance.Common
{
    public interface ISinoSignBo
    {
        /// <summary>取得代理人</summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>
        ///   <para>代理人List</para>
        /// </returns>
        Dictionary<string, string> GetDeputies(string empNo);

        /// <summary>
        /// 取得具備角色之預設員工(非代理)
        /// </summary>
        /// <param name="role">角色</param>
        /// <returns>員工編號 List</returns>
        List<string> GetRoleDefaultUser(string role);

        /// <summary>
        /// 取得具備角色之員工
        /// </summary>
        /// <param name="role">角色</param>
        /// <returns>員工編號 List</returns>
        List<string> GetRoleUsers(string role);

        /// <summary>
        /// 是否有此Role，若無則傳回null
        /// </summary>
        /// <param name="empNo"></param>
        /// <param name="roleId"></param>
        /// <returns></returns>
        Role? UserInRole(string empNo, string roleId);

        /// <summary>
        /// 取得使用者正式角色
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>角色List</returns>
        List<Role> GetUserFormalRoles(string empNo);

        /// <summary>
        /// 取得使用者角色
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>角色List</returns>
        List<Role> GetUserRoles(string empNo);

        /// <summary>
        /// 取得部門收發員工編號列表
        /// </summary>
        /// <param name="deptNo">部門編號</param>
        /// <returns>部門收發員工編號 List</returns>
        List<string> GetDepartmentMailroom(int deptNo);

        /// <summary>
        /// 取得角色名稱
        /// </summary>
        /// <param name="roleId">角色代碼</param>
        /// <returns>角色名稱</returns>
        string GetRoleName(string roleId);

        /// <summary>
        /// 取得所有角色群組名稱
        /// </summary>
        /// <returns></returns>
        Dictionary<string, string> GetRoleGroupNames();
    }
}
