<template>
  <NavMenu v-if="(userStore.userId.length > 0) && (route.name !== 'Login' && route.name !== 'Error')" />

  <router-view
    v-if="(userStore.userId.length > 0) || route.name === 'Login' || route.name === 'Error'"
    v-slot="{ Component }
  ">
    <component :is="Component" />
  </router-view>

  <Dialog
    v-model:visible="visible"
    modal
    :closable="false"
  >
    <template #header>
      <span class="p-dialog-title">提醒</span>
    </template>
    <p class="m-0">
      {{ message }}
    </p>
    <template #footer>
      <button
        type="button"
        class="btn btn-primary"
        @click="onCheckDialog"
      >
        <span>確定</span>
      </button>
    </template>
  </Dialog>

  <ConfirmDialog
    :closable="false"
    group="app"
  />

  <Toast
    position="top-center"
    group="app"
    :style="{ width: '35vw' }"
    :breakpoints="{
      [bootstrapVariables.gridBreakpointsLg]: { width: '60vw' },
      [bootstrapVariables.gridBreakpointsSm]: { width: '80vw' }
    }"
  >
    <template #closeicon>
      <i class="bi bi-x-lg" />
    </template>
  </Toast>

  <BlockUI
    :blocked="block"
    :fullScreen="true"
  />
</template>
<script setup lang="ts">
import NavMenu from './components/NavMenu.vue'
import Dialog from 'primevue/dialog'
import ConfirmDialog from 'primevue/confirmdialog'
import Toast from 'primevue/toast'
import BlockUI from 'primevue/blockui'
import { useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useAuthUserStore } from './store/index'
import { useAlarmDialogStore } from './store/alarmDialog'
import { useUiStore } from './store/ui'
import { routerExtend } from './router'
import bootstrapVariables from './assets/scss/_bootstrap_variables.module.scss'

const userStore = useAuthUserStore()
const alarmDialogStore = useAlarmDialogStore()
const route = useRoute()
const { block } = storeToRefs(useUiStore())
const { visible, message, targetRoute } = storeToRefs(alarmDialogStore)

const onCheckDialog = (): void => {
  alarmDialogStore.setOnCheckMessage()
  if (targetRoute.value.length > 0) {
    const target: string = JSON.parse(JSON.stringify(targetRoute.value))
    routerExtend.pushHandler(target)
  }
}
</script>