﻿using System;
using Xunit;
using Sinotech.Mis.HR.Attendance.Common;
using System.Diagnostics.CodeAnalysis;

namespace Sinotech.Mis.HR.Attendance.Tests
{
    [ExcludeFromCodeCoverage]
    public class NotifyFormCardsTests
    {

        [Fact]
        public void NotifyFormCards_DefaultValues_ShouldBeInitializedCorrectly()
        {
            // Arrange & Act
            var notifyFormCards = new NotifyFormCards();

            // Assert
            Assert.Equal(0, notifyFormCards.ID);
            Assert.Equal(string.Empty, notifyFormCards.NotifyEmpNo);
            Assert.Equal(string.Empty, notifyFormCards.NotifyName);
            Assert.Equal(default(DateTime), notifyFormCards.NotifyTime);
            Assert.Null(notifyFormCards.ViewTime);
            Assert.Equal(default(Guid), notifyFormCards.FormUID);
            Assert.Equal(string.Empty, notifyFormCards.FormID);
            Assert.Equal(string.Empty, notifyFormCards.FormNo);
            Assert.Equal(string.Empty, notifyFormCards.FormSubject);
            Assert.Equal(string.Empty, notifyFormCards.FormInfo);
            Assert.Equal(string.Empty, notifyFormCards.EmpNo);
            Assert.Equal(string.Empty, notifyFormCards.EmpName);
            Assert.Equal(0, notifyFormCards.DeptNo);
            Assert.Equal(0, notifyFormCards.FormStatus);
            Assert.Equal(string.Empty, notifyFormCards.FormStatusName);
            Assert.Equal(0, notifyFormCards.CardID);
            Assert.Equal(string.Empty, notifyFormCards.ApplicationType);
        }

        [Fact]
        public void NotifyFormCards_SetValues_ShouldBeSetCorrectly()
        {
            // Arrange
            var notifyFormCards = new NotifyFormCards
            {
                ID = 1,
                NotifyEmpNo = "E123",
                NotifyName = "John Doe",
                NotifyTime = new DateTime(2023, 10, 1, 12, 0, 0),
                ViewTime = new DateTime(2023, 10, 2, 12, 0, 0),
                FormUID = Guid.NewGuid(),
                FormID = "F123",
                FormNo = "FN123",
                FormSubject = "Test Subject",
                FormInfo = "Test Info",
                EmpNo = "E456",
                EmpName = "Jane Doe",
                DeptNo= 0,
                FormStatus = 1,
                FormStatusName = "Approved",
                CardID = 2,
                ApplicationType = "Leave"
            };

            // Act & Assert
            Assert.Equal(1, notifyFormCards.ID);
            Assert.Equal("E123", notifyFormCards.NotifyEmpNo);
            Assert.Equal("John Doe", notifyFormCards.NotifyName);
            Assert.Equal(new DateTime(2023, 10, 1, 12, 0, 0), notifyFormCards.NotifyTime);
            Assert.Equal(new DateTime(2023, 10, 2, 12, 0, 0), notifyFormCards.ViewTime);
            Assert.NotEqual(Guid.Empty, notifyFormCards.FormUID);
            Assert.Equal("F123", notifyFormCards.FormID);
            Assert.Equal("FN123", notifyFormCards.FormNo);
            Assert.Equal("Test Subject", notifyFormCards.FormSubject);
            Assert.Equal("Test Info", notifyFormCards.FormInfo);
            Assert.Equal("E456", notifyFormCards.EmpNo);
            Assert.Equal("Jane Doe", notifyFormCards.EmpName);
            Assert.Equal(0, notifyFormCards.DeptNo);
            Assert.Equal(1, notifyFormCards.FormStatus);
            Assert.Equal("Approved", notifyFormCards.FormStatusName);
            Assert.Equal(2, notifyFormCards.CardID);
            Assert.Equal("Leave", notifyFormCards.ApplicationType);
        }

    }
}