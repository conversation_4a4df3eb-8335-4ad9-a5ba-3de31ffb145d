export type DeptType = {
  deptNo: number
  deptSName: string
}

export type TeamType = {
  teamId: number
  teamCName: string
}

export type DeptTeamType = DeptType & {
  team: Array<TeamType>
}

export type EmployeeType = {
  userId: string
  userName: string
}

export type EmployeeStoreBaseType = DeptType & EmployeeType

export type EmployeeStoreType = EmployeeStoreBaseType & {
  teamId: number
  teamCName: string
}

export type SignerType = { [Employee in keyof EmployeeType]: EmployeeType[Employee] | null }

export type ProjectType = {
  id: string
  name: string
  startDate: Date | null
  endDate: Date | null
}

export type EmployeeLeavesType = {
  remainAnnualLeaves: number
  remainReservedLeaves: number
  remainReservedLeavesLastMonth: number
  newReservedLeavesMonth: number
  usedAnnualLeavesMonth: number
  totalAnnualLeaves: number
  usedAnnualLeaves: number
  usedPersonalLeaves: number
  usedPersonalLeavesMonth: number
  usedSickLeaves: number
  usedSickLeavesMonth: number
  displayExtendedLeaves: boolean
  extendedLeaves: number
  usedExtendedLeavesMonth: number
  usedExtendedLeaves: number
  remainedExtendedLeaves: number
  extendedLeavesIsImported: boolean
  usedMenstruationLeaves: number
  usedMenstruationLeavesMonth: number
  gender: string
  newRemainCompensatoryLeavesMonth: number
  usedCompensatoryLeavesMonth: number
  totalNewRemainCompensatoryLeaves: number
  totalUsedCompensatoryLeaves: number
  totalCompensatoryLeaves: number
}

export type MonthsOptionsType = {
  field: string | null
  optionValue: number
}

export type TenDaysOptionsType = {
  field: string | null
  optionValue: string
}

export type CardOptionsType = {
  label: string,
  value: string | null
}

export type MonthType = {
  year: number
  month: number
}

export type DayType = MonthType & {
  day: number
}

export type LeaveKindDetailType = {
  displayOrder: number
  leaveSubName: string
  leaveSubNumber: number
  leaveSubType: string
  permitExtraLeave: number
  permittedApplyNumber: number
  upperLimit: number
}

export type LeaveKindType = {
  attachmentExplanation: string
  attachmentPageExplanation: string
  autoEndDate: boolean
  certificateRequired: boolean
  detail: Array<LeaveKindDetailType>
  displayOrder: number
  endTimeExplanation: string
  eventExplanation: string
  isCalendarDay: boolean
  isEvent: boolean
  leaveKindExplanation: string
  minimumUnit: string
  name: string
  needLocation: boolean
  needProject: boolean
  number: number
  onlyOneDay: boolean
  pageExplanation: string
  switchLeavePrompt: string
  unit: string
  upperLimit: number
}

export type LeaveKindOptionsType = {
  name: string
  number: number
}

export type UploadedFileType = {
  filePathName: string
  fileName: string
  size: number
}

export type WorkdayType = {
  chineseWeekDay: string
  dayNumber: number
  dayType: number
  isWorkDay: number
  weekDay: number
  workDate: string
  workHours: number
  dayOff: boolean
}

export type CalendarDayType = Record<PropertyKey, { dayType: number }>

export type CardStoreType = {
  formID: string | null
  formUID: string | null
  formNo: string | null
  empNo: string | null
  empName: string | null
  deptNo: number | null
  deptSName: string | null
  formInfo: string | null
  addedSigner: string | null
  createdEmpNo: string | null
  createdName: string | null
  createdTime: string | null
  card: any
  attachments: Array<AttachmentsType>
}

export type AttachmentsType = {
  id: number
  formUID: string
  originalFileName: string
}

export type FlowType = {
  flowUID: string,
  step: number
  flowName: string
  recipientEmpNo: string
  recipientName: string
  approverName: string
  approveComments: string
  isAgentApprove: boolean
  approveTime: string
  flowStatusName: string
}

export type FormFlowType = {
  flows: Array<FlowType>
  currentStep: number
  formStatus: number
  formStatusName: string
  updatedName: string
  updatedTime: string
  endTime: string
}

export type OvertimeCompensatoryType = {
  id: number
  name: string
}

export type OvertimeDataType = {
  b1Card: any
  b1CardApp: any
  dayDetail: any
  hoursAreZeroMessage: string
  hoursLowerBound: number
  hoursUpperBound: number
  inTimeString: string
  isDriver: boolean
  isSpecialStaff: boolean
  monthCloseToHoursUpperBoundMessage: string
  monthCloseToHoursUpperBoundValue: number
  monthOverHoursUpperBoundMessage: string
  monthOverHoursUpperBoundValue: number
  monthOvertimeStatics: any
  overHoursLowerBoundMessage: string
  overHoursUpperBoundMessage: string
  quarterOverHoursUpperBoundMessage: string
  quarterOverHoursUpperBoundValue: number
  quarterlyOvertimeStatics: any
  specialStaffAllowedMonthWeightedOvertimeHours: number
  specialStaffCurrentMonthWeightedOvertimeHours: number
}

export type A1CardSubmitType = {
  a1_EMPNO: string
  a1_YYMM: string
  a1_NN: string
  a1_PROJNO: string
  a1_DDHH: string
  a1_HOUR: number
  a1_WYYMMDD: string
  a1_SOURCE: string
  addSigners: string
  a1_WDate: Date
  createdTime: Date
  filledTime: Date
}

export type B1CardAppSubmitType = {
  b1_EmpNo: string
  b1_DeptNo: number
  b1_Date: Date
  b1_Hour: number
  b1_Code: string
  b1_PrjNo: string
  b1_Reason: string
  b1_WritedEmpNo: string
  b1_WDate: Date
  addSigners: string | null
  createdTime: Date
  filledTime: Date
  uploadedFiles: Array<UploadedFileType>
}

export type B1CardSubmitType = {
  empNo: string
  reason: string
  sheetNo: string
  b1_WDate: Date
  overTimeDate: Date
  details: Array<{
    project: string
    b1_CODE: number
    startTime: Date
    endTime: Date
  }>
  addSigners: string | null
  createdTime: Date
  filledTime: Date
  uploadedFiles: Array<UploadedFileType>
}

export type C1CardSubmitType = {
  empNo: string
  startTime: Date
  endTime: Date
  eventDate: Date | null
  leaveKind: number | null
  leaveDetailNumber: number | null
  relatedFormNumber: string | null
  reason: string
  deputy: string
  projectNumber: string | null
  location: string | null
  addSigners: string | null
  createdTime: Date
  filledTime: Date
  uploadedFiles: Array<UploadedFileType>
  confirmed: boolean
}

export type SignInboxType = {
  roleID: string
  roleName: string
  roleSelectCheck: boolean
  formUID: string | null
  formID: string | null
  formNo: string | null
  empName: string | null
  deptSName: string | null
  formInfo: string | null
  addedSigner: string | null
  receiveDate: Date
  hour: number
  flowUID: string
  remindMessage: string | null
  remindSigner: boolean
  card: any
  attached: AttachmentsApiType[] | null
}

export type NotifyInboxType = {
  id: number
  formID: string
  formUID: string
  formNo: string
  formInfo: string
  formStatus: number
  formStatusName: string
  applicationType: string
  empNo: string
  empName: string
  notifyTime: string
  viewTime: string
  isRead: string
}

export type FormSubmitResponseApiType = {
  Code: number
  Message: string
  Status: number
}

export type DayApiType = {
  Year: number
  Month: number
  Day: number
}

export type DayAttendanceApiType = DayApiType & {
  InTimeString: string
}

export type DeptApiType = {
  DeptNo: number
  DeptSName: string
}

export type EmployeeApiBaseType = DeptApiType & {
  EmpNo: string
  CName: string
}

export type EmployeeApiType = EmployeeApiBaseType & {
  TeamID: number
  TeamCName: string
}

export type OvertimeCompensatoryApiType = {
  Type: number
  TypeName: string
}

export type LeaveInfoApiType = {
  LeaveName: string
  LeaveNumber: number
  LeaveSubNumber: number
  MonthApprovedHours: number
  MonthUsedHours: number
  SumToMonthApprovedHours: number
  SumToMonthUsedHours: number
  YearApprovedHours: number
  YearAvailableHours: number
  YearUsedHours: number
}

export type LeaveKindApiType = {
  AttachmentExplanation: string
  AttachmentPageExplanation: string
  AutoEndDate: boolean
  CertificateRequired: boolean
  Detail: Array<LeaveKindDetailApiType>
  DisplayOnFrontEnd: boolean
  DisplayOrder: number
  EndTimeExplanation: string
  EventExplanation: string
  IsCalendarDay: boolean
  IsEvent: boolean
  MinimumUnit: string
  Name: string
  NeedLocation: boolean
  NeedProject: boolean
  Number: number
  OnlyOneDay: boolean
  PageExplanation: string
  SwitchLeavePrompt: string
  LeaveKindExplanation: string
  Unit: string
  UpperLimit: number
}

export type LeaveKindDetailApiType = {
  DisplayOrder: number
  LeaveSubName: string
  LeaveSubNumber: number
  LeaveSubType: string
  PermitExtraLeave: number
  PermittedApplyNumber: number
  UpperLimit: number
}

export type ProjectApiType = {
  PrjNo: number
  PrjName: string
}

export type CardApiType = {
  ID: number | null
  FormID: string | null
  FormUID: string | null
  FormNo: string | null
  EmpNo: string | null
  EmpName: string | null
  DeptNo: number | null
  DeptSName: string | null
  FormInfo: string | null
  AddedSigner: string | null
  CreatedEmpNo: string | null
  CreatedName: string | null
  CreatedTime: string | null
  Card: any
  Attachments: Array<AttachmentsApiType> | null
}

export type InboxCardApiType = CardApiType & {
  RecipientEmpNo: string
  RecipientName: string
  StartTime: string
  EndTime: string | null
  FlowUID: string
  Hours: number
  RemindMessage: string | null
  RemindSigner: boolean
}

export type FlowApiType = {
  FlowUID: string,
  Step: number
  FlowName: string
  RecipientEmpNo: string
  RecipientName: string
  ApproverName: string
  ApproveComments: string
  IsAgentApprove: boolean
  ApproveTime: string
  FlowStatusName: string
}

export type FormFlowApiType = {
  Flows: Array<FlowApiType>
  CurrentStep: number
  FormStatus: number
  FormStatusName: string
  UpdatedName: string
  UpdatedTime: string
  EndTime: string
}

export type AttachmentsApiType = {
  ID: number
  FormUID: string
  OriginalFileName: string
}

export type FileApiType = {
  fileName: string,
  filePathName: string,
  size: number
}