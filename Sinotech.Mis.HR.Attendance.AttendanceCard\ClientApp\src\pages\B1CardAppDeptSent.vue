<template>
  <CardDeptSent>
    <template #header>
      <i class="bi bi-search me-1" />
      <span>填報查詢 / </span>
      <span>{{ FORM_ID.B1CardApp }}</span>
    </template>
  </CardDeptSent>
  <div class="container border border-2 px-0">
    <B1CardApp :modelValue="cardStore.data" />
  </div>
  <div class="container px-0">
    <FormFlow :modelValue="cardStore.data" />
  </div>

  <div class="container px-0">
    <div class="text-black-50 text-end">
      <span>
        查詢時間：
      </span>
      <span class="fst-italic">
        {{ dateToRocString(cardStore.queryDataTime) }}
      </span>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useToast } from 'primevue/usetoast'
import { useCardStore } from '../store/card'
import { onBeforeRouteLeave } from 'vue-router'
import { FORM_ID } from '../api/appConst'
import { dateToRocString } from '../api/appFunction'
import CardDeptSent from '../components/CardDeptSent.vue'
import B1CardApp from '../components/B1CardApp.vue'
import FormFlow from '../components/FormFlow.vue'

const cardStore = useCardStore()
const toast = useToast()

onBeforeRouteLeave(() => {
  cardStore.$reset()
  toast.removeGroup('app')
})
</script>