﻿using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Diagnostics.CodeAnalysis;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.PositionType
{
    [ExcludeFromCodeCoverage]
    internal class SpecialStaff : B1CardPositionBase
    {
        public SpecialStaff
            (string employeeNumber, DateTime overtimeDate, IB1CardDataProvider provider) :
            base(employeeNumber, overtimeDate, provider)
        {
        }

        //組長(特殊人員)
        //    需檢查每日加班時數限制
        //    要每月加班時數上限(由資料表設定，此值應確定不會超出勞基法規定)
        //    不用檢查每季加班時數限制，要檢查也可以
        //    要勾稽「週六（休息日或逢國定假日）及彈性放假日」加班時數須與加班申請卡時數
        internal override bool IsOvertimeAllowed => true;
        protected override bool HasAppliedOvertimeWork() => DefaultHasAppliedOvertimeWork();
        protected override B1CardResult CheckAppliedHours() => DefaultCheckAppliedHours();
        protected override B1CardResult CheckDailyOvertimeHours() => DefaultCheckDailyOvertimeHours();
        protected override B1CardResult CheckMonthlyOvertimeHours() => SpecialStaffCheckMonthlyOvertimeHours();
        protected override B1CardResult CheckQuarterlyOvertimeHours() => DefaultCheckQuaterlyOvertimeHours();
        protected override B1CardResult GetWarningResult() => DefaultGetWarningResult();
        private B1CardResult SpecialStaffCheckMonthlyOvertimeHours()
        {
            var result = DefaultCheckMonthlyOvertimeHours();
            if (result.Status != B1CardStatusEnum.Success)
            {
                return result;
            }

            var allowed = GetSpecialStaffAllowedMonthlyWeightedOvertimeHours();
            var current = GetCurrentMonthlyWeightedOvertimeHours();
            var thisCard = GetWeightedOvertimeHours();

            if (current + thisCard > allowed)
            {
                return new B1CardResult(
                    code: 538,
                    status: B1CardStatusEnum.Error,
                    message: $"【加班】時數已達上限"
                );
            }

            return SuccessResult;
        }
    }
}
