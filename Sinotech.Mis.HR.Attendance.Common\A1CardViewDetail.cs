﻿using System;
using System.Collections.Generic;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 正常工作卡檢視細項物件
    /// </summary>
    public class A1CardViewDetail
    {
        /// <summary>
        /// 流水號
        /// </summary>
        public int? ID { get; set; }

        /// <summary>
        /// 正常工作卡細項中每日細項
        /// </summary>
        public class FillDayDetail
        {
            /// <summary>
            /// 日數 
            /// </summary>
            /// <value>1~31</value>
            public int day { get; set; }

            /// <summary>
            /// 日期
            /// </summary>
            public DateTime date;

            /// <summary>
            /// 時數
            /// </summary>
            public int hour { get; set; } = 0;

            /// <summary>
            /// 星期幾
            /// </summary>
            /// <value>0~6</value>
            public int weekday { get; set; } = 0;

            /// <summary>
            /// 星期幾
            /// </summary>
            /// <value>日~六</value>
            public char rocWeekDay { get; set; } = ' ';
        }

        /// <summary>
        /// 計畫編號
        /// </summary>
        public string A1_PROJNO { get; set; } = string.Empty;

        /// <summary>
        /// 計畫名稱
        /// </summary>
        public string ProjectName { get; set; } = string.Empty;

        /// <summary>
        /// 每旬的1~11天時數
        /// </summary>
        public string A1_DDHH { get; set; } = string.Empty;

        /// <summary>
        /// 每日的細項
        /// </summary>
        public List<FillDayDetail> DayDetails { get; set; } = new List<FillDayDetail>();

        /// <summary>
        /// 每旬每計畫工作總時數
        /// </summary>
        public int A1_HOUR { get; set; }

        /// <summary>
        /// 正常卡表單序號
        /// </summary>
        /// <value>
        /// 從1開始編號
        /// </value>
        public string A1_SERIALNO { get; set; } = string.Empty;

    }
}
