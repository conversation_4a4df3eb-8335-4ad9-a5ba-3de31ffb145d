﻿using Xunit;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves;
using Sinotech.Mis.HR.Attendance.C1CardObjs;
using System;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves.Tests
{
    /// <summary>
    /// 假別未提供例外測試類別
    /// 從 nUnit 轉換為 xUnit 版本
    /// </summary>
    public abstract class LeaveNotFoundExceptionTests : TestC1CardBase
    {
        [Fact]
        public void TestCanTakeThisLeave()
        {
            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            Assert.Throws<InvalidOperationException>(() => card.CanTakeThisLeave());
        }

        [Fact]
        public void TestExceedQuota()
        {
            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            Assert.Throws<InvalidOperationException>(() => card.CheckOverPermittedLeaveHours());
        }

        [Fact]
        public void TestCheckRequiredFields()
        {
            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            Assert.Throws<InvalidOperationException>(() => card.CheckRequiredFields());
        }
    }
}