﻿using System.Collections.Generic;
using System.Data;

namespace Sinotech.Mis.HR.Attendance.Common
{
    public interface IDepartmentBo
    {
        /// <summary>
        /// 取得部門內所有員工
        /// </summary>
        /// <param name="departmentNumber">部門編號</param>
        /// <returns>部門內所有員工</returns>
        public DataTable GetDepartmentEmployees(int departmentNumber);

        /// <summary>
        /// 取得部門內所有員工 JSON String
        /// </summary>
        /// <param name="departmentNumber">部門編號</param>
        /// <returns>部門內所有員工 JSON String</returns>
        public string GetDepartmentEmployeesJson(int departmentNumber);

        /// <summary>
        /// 取得部門主管
        /// </summary>
        /// <param name="departmentNumber"></param>
        /// <returns></returns>
        public string GetDepartmentManager(int departmentNumber);

        /// <summary>取得部門名稱</summary>
        /// <param name="departmentNumber">部門編號</param>
        /// <returns>
        ///   部門名稱<br />
        /// </returns>
        public string GetDepartmentName(int departmentNumber);

        /// <summary>
        /// 取得所有部門
        /// </summary>
        /// <returns>所有部門</returns>
        public DataTable GetDepartments();

        /// <summary>取得部門簡稱</summary>
        /// <param name="departmentNumber">部門編號</param>
        /// <returns>
        ///   部門簡稱<br />
        /// </returns>
        public string GetDepartmentShortName(int departmentNumber);

        /// <summary>
        /// 取得所有部門 JSON String
        /// </summary>
        /// <returns>所有部門 JSON String</returns>
        public string GetDepartmentsJson();

        /// <summary>
        /// 取得行政部門
        /// </summary>
        /// <returns></returns>
        public List<Department> GetAdministrativeDepartments();

        /// <summary>
        /// 取得研究中心
        /// </summary>
        /// <returns></returns>
        public List<Department> GetResearchCenters();


        /// <summary>
        /// 取得各部門各級主管
        /// </summary>
        /// <returns>各部門各級主管</returns>
        public DataTable GetDepartmentsManagers();

        /// <summary>
        /// Gets the departments teams employees.
        /// </summary>
        /// <returns></returns>
        public List<DeptTeam> GetDepartmentsTeamsEmployees();

        /// <summary>
        /// 取得指定部門的組別及員工資料
        /// </summary>
        /// <param name="departmentNumber">部門編號</param>
        /// <param name="teamNumber">組別編號</param>
        /// <returns></returns>
        public List<DeptTeam> GetDepartmentTeamEmployees(int departmentNumber, int teamNumber);

        /// <summary>
        /// 取得指定部門的組別及員工資料
        /// </summary>
        /// <param name="departmentNumber">部門編號</param>
        /// <returns></returns>
        public List<DeptTeam> GetDepartmentTeamsEmployees(int departmentNumber);

        /// <summary>
        /// 是否為副理以上
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>副理以上傳回<c>true</c>，否則傳回<c>false</c></returns>
        public bool IsAboveDeputyManager(string empNo);

        /// <summary>
        /// 是否為副理以上
        /// </summary>
        /// <param name="deptNo">部門編號</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>副理以上傳回<c>true</c>，否則傳回<c>false</c></returns>
        public bool IsAboveDeputyManager(int deptNo, string empNo);
    }
}
