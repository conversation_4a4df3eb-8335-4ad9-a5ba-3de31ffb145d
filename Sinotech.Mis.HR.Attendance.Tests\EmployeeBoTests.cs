﻿using FakeItEasy;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sinotech.Mis.Common;
using Sinotech.Mis.Extensions.Configuration;
using Sinotech.Mis.HR.Attendance.Tests;
using Sinotech.Mis.Utilities.DataAccess.Interfaces;
using System.Collections.Generic;
using System.Data;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.Tests
{
#nullable enable
    public class EmployeeBoTests
    {

        private EmployeeBo employeeBo;

        public EmployeeBoTests(IEmployeeDao employeeDao)
        {
            ILogger<EmployeeBo> logger = A.Fake<ILogger<EmployeeBo>>();
            employeeBo = new EmployeeBo(employeeDao, logger);
            IConfiguration configuration = new ConfigurationBuilder().
                AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).Build();
            string? ConnectionStringAttendance = configuration.GetSecuredConnectionString("Attendance");
            if (ConnectionStringAttendance != null)
            {
                TestHelper.ClearData(ConnectionStringAttendance);
            }
        }

        [Fact]
        public void GetEmployeeNameTest()
        {
            string empNo = employeeBo.GetEmployeeName("0395");
            Assert.Equal("曾騰毅", empNo);

            empNo = employeeBo.GetEmployeeName("0219");
            Assert.Equal("鍾裕仁(非在職)", empNo);
        }

        [Theory]
        [InlineData(-1, "0178")]
        [InlineData(0, "0178")]
        [InlineData(1, "1048")]
        [InlineData(2, "0281")]
        [InlineData(3, "0390")]
        [InlineData(4, "0178")]
        [InlineData(5, "0178")]
        [InlineData(6, "0178")]
        [InlineData(7, "0178")]
        [InlineData(8, "0217")]
        [InlineData(9, "0270")]
        [InlineData(10, "0273")]
        [InlineData(11, "0178")]
        [InlineData(12, "0178")]
        [InlineData(13, "0178")]
        [InlineData(14, "0178")]
        [InlineData(15, "2320")]
        [InlineData(16, "1055")]
        [InlineData(17, "0931")]
        [InlineData(18, "2037")]
        public void GetEmployeesJsonTest(int deptNo, string expected)
        {
            string ret = employeeBo.GetEmployeesSimpleJson(deptNo);
            DataTable? dt = JsonConvert.DeserializeObject<DataTable>(ret);
            Assert.NotNull(dt);
            Assert.True(dt.Rows.Count > 0);
            Assert.NotNull(dt.Rows[0]["EmpNO"]);
            string empNo = (string)dt.Rows[0]["EmpNO"];
            Assert.Equal(expected, empNo);
        }

        [Fact]
        public void GetTeamLeadersTest()
        {
            List<TeamLeader> teamLeaders = employeeBo.GetTeamLeaders();
            Assert.NotEmpty(teamLeaders);
        }

        [Theory]
        [InlineData("0178", "0349", false)]
        [InlineData("2008", "2308", true)]
        [InlineData("2008", "2309", false)]
        [InlineData("2008", "2310", false)]
        [InlineData("0391", "2008", false)]
        [InlineData("2131", "2094", true)]
        public void IsTeamLeaderOfTest(string leaderId, string empNo, bool expected)
        {
            bool actual = employeeBo.IsTeamLeaderOf(leaderId, empNo);
            Assert.Equal(expected, actual);
        }

        [Theory]
        [InlineData("0349", true)]
        [InlineData("2308", true)]
        [InlineData("2000", false)]
        [InlineData("0001", false)]
        [InlineData("2008", true)]
        [InlineData("2094", true)]
        public void GetDepartmentNameTest(string empNo, bool expected)
        {
            string deptName = employeeBo.GetDepartmentName(empNo);
            if (expected)
            {
                Assert.NotEmpty(deptName);
            }
            else
            {
                Assert.Empty(deptName);
            }
        }
    }
}
