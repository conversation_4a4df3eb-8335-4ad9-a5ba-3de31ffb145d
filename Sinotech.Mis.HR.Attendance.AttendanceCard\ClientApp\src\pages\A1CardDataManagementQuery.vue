<template>
  <CardDataManagementQuery :modelValue="cardStore.data">
    <template #header>
      <i class="bi bi-gear me-1" />
      <span>三卡管理 / </span>
      <span>{{ FORM_ID.A1Card }}</span>
    </template>
  </CardDataManagementQuery>
  <div class="container border border-2 px-0">
    <A1Card :modelValue="cardStore.data" />
  </div>
  <div class="container px-0">
    <FormFlow :modelValue="cardStore.data" />
  </div>

  <div class="container px-0">
    <div class="text-black-50 text-end">
      <span>
        查詢時間：
      </span>
      <span class="fst-italic">
        {{ dateToRocString(cardStore.queryDataTime) }}
      </span>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useToast } from 'primevue/usetoast'
import { useCardStore } from '../store/card'
import { onBeforeRouteLeave } from 'vue-router'
import { FORM_ID } from '../api/appConst'
import { dateToRocString } from '../api/appFunction'
import CardDataManagementQuery from '../components/CardDataManagementQuery.vue'
import A1Card from '../components/A1Card.vue'
import FormFlow from '../components/FormFlow.vue'

const cardStore = useCardStore()
const toast = useToast()

onBeforeRouteLeave(() => {
  cardStore.$reset()
  toast.removeGroup('app')
})
</script>