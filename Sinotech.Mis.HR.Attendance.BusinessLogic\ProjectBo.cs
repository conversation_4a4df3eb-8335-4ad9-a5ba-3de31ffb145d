﻿using Newtonsoft.Json;
using Sinotech.Mis.Common;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.Utilities.DataAccess.Interfaces;
using System;
using System.Data;
using System.Runtime.Caching;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic
{
    /// <summary>
    /// 計畫 商業物件
    /// </summary>
    public class ProjectBo: IProjectBo
    {

        private static ObjectCache _cache = MemoryCache.Default;
        private readonly static object CacheLock = new object();
        private readonly IProjectDao _projectDao;

        /// <summary>Initializes a new instance of the <see cref="ProjectBo" /> class.</summary>
        /// <param name="projectDao">project DAO</param>
        public ProjectBo(IProjectDao projectDao)
        {
            _projectDao = projectDao;
        }


        /// <summary>
        /// 取得所有計畫
        /// </summary>
        /// <returns></returns>
        private DataTable GetAllProjects()
        {
            DataTable dt;
            if (_cache.Contains("AllProjects"))
            {
                dt = (DataTable)_cache["AllProjects"];
            }
            else
            {
                dt = _projectDao.GetAllProjects();
                lock (CacheLock)
                {
                    _cache.Set("AllProjects", dt, CachePolicy);
                }
            }
            return dt;
        }

        private static CacheItemPolicy CachePolicy
        {
            get
            {
                CacheItemPolicy policy = new CacheItemPolicy();
                policy.AbsoluteExpiration = DateTimeOffset.Now.AddMinutes(2.0);
                return policy;
            }
        }

        ///<summary>查詢指定期間內已成立及未結案計畫。(查詢結束日期(不含)前成立 且 起始日期後結案或未結案 之計畫)</summary>
        ///<param name="startDate">起始日期</param>
        ///<param name="endDate">結束日期</param>
        ///<param name="deptNo">優先部門代號，預設 -1 表示不將優先部門置前</param>
        ///<returns>JSON String (PrjNo：計畫編號、PrjName：計畫名稱、BDate：成立日期、EDate：結案日期、MainDeptNo：主辦部門代號、DeptSName：主辦部門名稱)</returns>
        public string GetOpenProjectsDateRange(DateTime startDate, DateTime endDate, int deptNo = -1)
        {
            string ret = "[]";
            DataTable dt = _projectDao.GetOpenProjectsDateRange(startDate, endDate, deptNo);
            ret = JsonConvert.SerializeObject(dt);
            return ret;
        }

        /// <summary>Gets the project.</summary>
        /// <param name="projectNumber">The project number.</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public Project GetProject(string projectNumber)
        {
            Project project = new Project();
            DataTable dt = GetAllProjects();
            DataRow[] drs = dt.Select($"PrjNo='{projectNumber}'");
            if (drs.Length > 0)
            {
                DataRow dr = drs[0];
                project.PrjNo = (string)dr["PrjNo"];
                if (dr["PrjName"] != null && dr["PrjName"] != DBNull.Value)
                {
                    project.PrjName = (string)dr["PrjName"];
                    if(string.IsNullOrEmpty(project.PrjName) && dr["PrjFName"] != null && dr["PrjFName"] != DBNull.Value)
                    {
                        project.PrjName = (string)dr["PrjFName"];
                    }
                }
                else if (dr["PrjFName"] != null && dr["PrjFName"] != DBNull.Value)
                {
                    project.PrjName = (string)dr["PrjFName"];
                }
                project.MainDeptNo = (int)dr["MainDeptNo"];
                project.DeptSName = (string)dr["DeptSName"];
                if (dr["BDate"] == DBNull.Value)
                {
                    project.BDate = null;
                }
                else
                {
                    project.BDate = (DateTime)dr["BDate"];
                }

                if (dr["EDate"] == DBNull.Value)
                {
                    project.EDate = null;
                }
                else
                {
                    project.EDate = (DateTime)dr["EDate"];
                }

                if (dr["YM_BanFillManMonth"] == DBNull.Value)
                {
                    project.SubmitDueDate = null;
                }
                else
                {
                    project.SubmitDueDate = (DateTime)dr["YM_BanFillManMonth"];
                }
            }
            return project;
        }


        /// <summary>Gets the name of the project.</summary>
        /// <param name="projectNumber">The project number.</param>
        /// <returns>
        ///   <br />
        /// </returns>
        public string? GetProjectName(string projectNumber)
        {
            string? ret = null;
            DataTable dt = GetAllProjects();
            DataRow[] drs = dt.Select($"PrjNo='{projectNumber}'");
            if (drs.Length > 0)
            {
                DataRow dr = drs[0];
                if (dr["PrjName"] != null && dr["PrjName"] != DBNull.Value)
                {
                    ret = (string)dr["PrjName"];
                }
            }
            return ret;
        }
    }
}
