<template>
  <div :class="[prodModeClass, 'px-3 py-2']">
    <router-link
      class="router-link-active router-link-exact-active navbar-brand text-black me-0"
      role="button"
      :to="{ name: 'Home' }"
    >
      <img
        :src="attendanceLogo"
        :style="{ width: '3rem', height: '3rem' }"
        alt="三卡"
        v-tooltip="'三卡首頁'"
      >
    </router-link>
  </div>
  <div class="text-danger">
    <span>{{ SYSTEM_ERROR_MESSAGE }}</span>
  </div>
</template>
<script setup lang="ts">
import attendanceLogo from '../assets/Attendance.png'
import { SYSTEM_ERROR_MESSAGE } from '../api/appConst'
const prodModeClass = import.meta.env.VITE_PROD_MODE_CLASS
</script>