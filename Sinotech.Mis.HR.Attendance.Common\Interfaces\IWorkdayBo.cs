﻿using System;
using System.Collections.Generic;
using System.Data;

namespace Sinotech.Mis.HR.Attendance.Common
{
    public interface IWorkdayBo
    {
        /// <summary>
        /// 此處的 days 只允許 正整數
        /// </summary>
        /// <param name="startDate">開始日期</param>
        /// <param name="days">天數</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        DateTime AddWorkDays(DateTime startDate, int days, string empNo);

        /// <summary>
        /// 此處的 days 只允許 正整數
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="days"></param>
        /// <param name="shiftId"></param>
        /// <returns></returns>
        DateTime AddWorkDays(DateTime startDate, int days, int shiftId = 1);

        /// <summary>查詢指定期間內之工時合計，每日目前最多8小時 <br />
        /// 此處計算出若在此區間內請假需幾小時，區間自動排除午休與非上班時段<br />
        /// 求整數時要用  (int) Math.Ceiling(hours); 無條件進位<br />
        /// 此API不適用請假時離開時間包含中午時段特例的時數計算
        /// </summary>
        /// <param name="startDate">起始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns>工時</returns>
        double CalculateWorkingHoursDateDiff(DateTime startDate, DateTime endDate, int shiftId = 1);

        /// <summary>
        /// 取得該日的類型
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="shiftId">班別ID</param>
        /// <returns></returns>
        WorkdayType GetDayType(DateTime date, int shiftId = 1);

        /// <summary>查詢指定期間內員工之行事曆資料。</summary>
        /// <param name="startDate">起始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>List<Workday>(行事曆資料)</returns>
        List<Workday> GetEmpWorkdaysDateRange(DateTime startDate, DateTime endDate, string empNo);

        /// <summary>
        /// 某月的日曆天資料
        /// </summary>
        /// <param name="year">西元年</param>
        /// <param name="month">月</param>
        /// <param name="empNo">員工編號/param>
        /// <returns>List<Workday>(日曆天資料)</returns>
        List<Workday> GetEmpWorkDaysInMonth(int year, int month, string empNo);

        /// <summary>取得某日期所屬旬的最後一天工作日之日曆天日期</summary>
        /// <param name="year">The year.</param>
        /// <param name="month">The month.</param>
        /// <param name="day">The day.</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns>最後一天工作日之日曆天日期</returns>
        DateTime GetLastWorkDayInTenDays(int year, int month, int day, int shiftId = 1);

        /// <summary>取得最多可加班時數</summary>
        /// <param name="date">日期</param>
        /// <param name="shiftId">班別ID，預設 1 正常班</param>
        /// <returns>
        ///   <br />
        /// </returns>
        int GetMaxOvertimeHours(DateTime date, int shiftId = 1);

        /// <summary>取得最少可加班時數</summary>
        /// <param name="date">日期</param>
        /// <param name="shiftId">班別ID，預設 1 正常班</param>
        /// <returns>
        ///   <br />
        /// </returns>
        int GetMinOvertimeHours(DateTime date, int shiftId = 1);

        /// <summary>取得給薪最少加班時數</summary>
        /// <param name="date">日期</param>
        /// <param name="shiftId">班別ID，預設 1 正常班</param>
        /// <returns>
        ///   <br />
        /// </returns>
        int GetMinPaidOvertimeHours(DateTime date, int shiftId = 1);

        /// <summary>
        /// 整月工時
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="shiftId">工作班別ID，預設 1 正常班</param>
        /// <returns>整月工時合計</returns>
        int GetMonthWorkHours(DateTime date, int shiftId = 1);

        /// <summary>
        /// 整月工時
        /// </summary>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <param name="shiftId">班別ID，預設 1 正常班</param>
        /// <returns>整月工時合計</returns>
        int GetMonthWorkHours(int year, int month, int shiftId = 1);

        /// <summary>
        /// 取得該日的詳細資料
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns></returns>
        Workday GetWorkday(DateTime date, int shiftId = 1);

        /// <summary>查詢指定期間內之行事曆資料。</summary>
        /// <param name="startDate">起始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns>List<Workday>(行事曆資料)</returns>
        List<Workday> GetWorkdaysDateRange(DateTime startDate, DateTime endDate, int shiftId = 1);

        /// <summary>
        /// 某月的日曆天資料
        /// </summary>
        /// <param name="year">西元年</param>
        /// <param name="month">月</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns>DataTable(日曆天資料)</returns>
        DataTable GetWorkDaysInMonth(int year, int month, int shiftId = 1);

        /// <summary>
        /// Gets the work days in month list.
        /// </summary>
        /// <param name="year">The year.</param>
        /// <param name="month">The month.</param>
        /// <param name="shiftId">The shift identifier.</param>
        /// <returns></returns>
        List<Workday> GetWorkDaysInMonthList(int year, int month, int shiftId = 1);

        /// <summary>取得某日期所屬旬的最後一天工作日之日期</summary>
        /// <param name="year">西元年</param>
        /// <param name="month">月</param>
        /// <param name="tenDays">旬別(1:上旬、2:中旬、3:下旬)</param>
        /// <param name="shiftId">班別(預設值1:正常班)，可省略</param>
        /// <returns>DataTable(日曆天資料)</returns>
        DataTable GetWorkdaysInTendays(int year, int month, int tenDays, int shiftId = 1);

        string GetWorkdayTypeName(WorkdayType dayType, int shiftId = 1);

        /// <summary>
        /// 開始日不算，幾工作日之內
        /// </summary>
        /// <param name="startDate">The start date.</param>
        /// <param name="endDate">The end date.</param>
        /// <param name="days">天數</param>
        /// <param name="shiftId">The shift identifier.</param>
        /// <returns></returns>
        bool InWorkdays(DateTime startDate, DateTime endDate, int days, int shiftId = 1);

        /// <summary>
        /// 是否為該旬最後一個工作日
        /// </summary>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <param name="day">日</param>
        /// <param name="shiftId">The shift identifier.</param>
        /// <returns>
        ///   <c>true</c> if [is last work day in ten days]; otherwise, <c>false</c>.
        /// </returns>
        bool IsLastWorkDayInTenDays(int year, int month, int day, int shiftId = 1);

        /// <summary>
        /// Determines whether the specified day type is workday.
        /// </summary>
        /// <param name="dayType">Type of the day.</param>
        /// <returns>
        ///   <c>true</c> if the specified day type is workday; otherwise, <c>false</c>.
        /// </returns>
        bool IsWorkday(WorkdayType dayType);

        /// <summary>
        /// 是否為工作日
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        bool IsWorkday(DateTime date, string empNo);

        /// <summary>
        /// 是否為工作日
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="shiftId">班別ID</param>
        /// <returns>
        ///   <c>true</c> if the specified date is workday; otherwise, <c>false</c>.
        /// </returns>
        bool IsWorkday(DateTime date, int shiftId = 1);

        /// <summary>該日是否為工作日</summary>
        /// <param name="year">The year.</param>
        /// <param name="month">The month.</param>
        /// <param name="day">The day.</param>
        /// <param name="shiftId">The shift identifier.</param>
        /// <returns>
        ///   <c>true</c> if the specified year is workday; otherwise, <c>false</c>.</returns>
        bool IsWorkday(int year, int month, int day, int shiftId = 1);

        /// <summary>
        /// 某旬中最後一日曆天
        /// </summary>
        /// <param name="year">西元年</param>
        /// <param name="month">月</param>
        /// <param name="tenDays">旬</param>
        /// <returns></returns>
        int LastDayInTenDays(int year, int month, int tenDays);

        /// <summary>Find the last work day in month.</summary>
        /// <param name="year">The year. 西元年</param>
        /// <param name="month">The month. 月分</param>
        /// <param name="workdays">包含此日期的工作日集合</param>
        /// <returns>最後一個工作天的日期(int)</returns>
        DateTime LastWorkDayInMonth(int year, int month, List<Workday> workdays);

        /// <summary>Find the first work day in month.</summary>
        /// <param name="year">The year. 西元年</param>
        /// <param name="month">The month. 月分</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>第一天工作日的日期(int)</returns>
        DateTime FirstWorkDayInMonth(int year, int month, string empNo);

        /// <summary>Find the last work day in month.</summary>
        /// <param name="year">The year. 西元年</param>
        /// <param name="month">The month. 月分</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>最後一天工作日的日期(int)</returns>
        DateTime LastWorkDayInMonth(int year, int month, string empNo);

        /// <summary>Find the last work day in month.</summary>
        /// <param name="year">The year. 西元年</param>
        /// <param name="month">The month. 月分</param>
        /// <param name="shiftId">班別ID</param>
        /// <returns>最後一天的日期(int)</returns>
        DateTime LastWorkDayInMonth(int year, int month, int shiftId = 1);

        /// <summary>
        /// 找出此日期(含)之後最接近的工作日
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="workdays">包含此日期的工作日集合</param>
        /// <returns></returns>
        DateTime MostRecentWorkDateAfter(DateTime date, List<Workday> workdays);

        /// <summary>
        /// 找出此日期(含)之前最接近的工作日
        /// </summary>
        /// <param name="date"></param>
        /// <param name="shiftId">班別編號</param>
        /// <returns></returns>
        DateTime MostRecentWorkDateBefore(DateTime date, List<Workday> workdays);

        /// <summary>
        /// 依日曆天月份將啟迄時間分段，切分中間自動設為早上08:00 至 下午17:00
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="shiftId"></param>
        /// <returns></returns>
        List<(DateTime, DateTime)> SplitDateByMonthCalendarDay(DateTime startDate, DateTime endDate, int shiftId);

        /// <summary>
        /// 依月份與工作日將啟迄時間分段，切分中間自動設為早上08:00 至 下午17:00
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        List<(DateTime, DateTime)> SplitDateByMonthWorkday(DateTime startDate, DateTime endDate, string empNo);

        /// <summary>
        /// 開始日不算，幾工作日之後
        /// </summary>
        /// <param name="startDate">The start date.</param>
        /// <param name="endDate">The end date.</param>
        /// <param name="shiftId">The shift identifier.</param>
        /// <returns></returns>
        int WorkdaysRangeDiff(DateTime startDate, DateTime endDate, int shiftId = 1);

    }
}