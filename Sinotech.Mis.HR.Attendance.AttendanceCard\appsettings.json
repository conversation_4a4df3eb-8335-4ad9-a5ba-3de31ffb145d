{"ConnectionStrings": {"Attendance": "uLonnYntZz8nHyQESL9J2tOEVgR+mZ3jN1qfWT3wDT008bJYhlqwM2ZYo2jGnRbVdC2bt3DjFVkVPlzVwXdKV/up0tgWcFNRLfhi5K2K4vQuEu9X/72irQ3HlYIluKWeW4DM7l2VeU0F0Ls2TX6Ljd6zWG5uRylDsxZWM1IrOlVdllxXSFkEAJnvtlmZIVszf7zIrnjlfAO5sUgMA045s6zCUkF5pD0aDuowmcpM80cGkNX2fDzhipfGcvPwREp7Kx7lOdwZLvo8GibFvk6QabR4nskWdP5cYSXSJhW7khEK0zhNFVYcPe6L/Ym10WUXqDXGFX8SmfoT8R/FkHKsWIBn75gAjTStf+EWKL499EHroeQem4uc1+NTOrykS6kLfgoyBvPELsRFSSC4PmshnpwAcJtTMBC0ffFmri6cINDgkGcpoX0gLjaaEBs7DLXwd4q+/SkE2CDqG5/5lfkg1jRHiF6aFY29M7V74sl0w4qg1RcnQkZ27UqgzhXk4YU19aoRekoCVn4e4lBhIaM89zz3N82P0+bv8iwzEoDCHzlc/vGV4zvajemEYOj+mp781BAoDbD9ihEPX6aLw83lhmgtPvUPacVYAlHqaV6zZP9Vl7CJxnUg37axx7VjWPq/xu8RGZFRCNejGc/y/XL1ki0wIOg86eUdY7w4d+cVcow=", "MIS": "DB19gnv8ZRUlnSLDll4CltzVkxCHftsvUq9YN+LzHybV9mI8pZpqlisB3G0eBaCoEUZnsA/uLG1tgmZV6m6sVAg2IXGuAY2jAMqxItAWsBMrKqZF7apxzh6Mg43LwMD7dC+HkWpJ8bA9k1Ly6DX3FDTmkBzL0ZHgKExZeYHuHfEUxkMpExiW/zL+RjUP6qw8yiJZKjE8taFB3O+hEEpdqbx+LbtA7VPk6/dcWTqPhkfQbMI2pFhGGGVRA8RZ9E5B1qWSsd1OGGT58fj7c7aWBlG8HTP8aLU2IHlR1SKzOa0PfTRX12BDATZ+eq/XorrwggRw6R5ncL/Aj0ATQkSV/yufygB+XFtJuU43gxsaySi+WTVemTLY484luhLeEvn3DPVtZbJ5aHhPSukkyOeVnty+AduYNEKW7HEjgEA+xDReYwwBPSiKHiitTLO5Gxab78z1aqa9kYZaSRj3vVGuI7gllgeJEZg894wL0Of0Wm7tRPwmrssDFfhpI7Arrhb/odbQIko+Hd+RmilAB5GSBz+f6F6JEMBXsNntLlp7wercA4ELnfc4X+SXtv4AcnXXcFxWLsGTGnC5MTYqEtheRC81bwPpDdBR7neGJ2nLKP5cuQeX49+VfVdore2ToBIATtplXDDOtT4EuOmg8NQyHB8uva7HCj7445qSFVD0gzs=", "SinoSign": "nY8IV3VBD4kzyJ1wc9UKvBWg4GW7AKbr+S+Wr/zkkMiymrmbkZIegqNs619DoIsHDAb2BukZUElArNrLfFg6ZAWkG9UYUQDlEmr2JY/jw0b9Dn8rL66bvvu1ks1CDBAc7Gpnw3Py9c4UwWPoh5rX8tg40h0iLpSrylSX6xpFHOKLfbVvKtt0N7KSpAOcC4P5qiEn/zF+EvrIYLAwewaSk66Pa6BSZZ+Fx4UJ+dqIOF/aGrZFpv66JkFLsTnQzN/QSsO9GNzR/VZnX7JDePKPIj3JPeN0zmRTLC5xxW0mvmWuu9cS7r9JuW9QBolp6reW3BtgPBk5j+QYYMRO3IPUGy3lqYQF9GF0vYyeqR0kzHEHLx8zVww65zVhR1uLUnSIv1wFMCP9QwEOMuEWSqxmJ/+Hxqddrc//UupgB6NI2AIlqvBlT2QZesoO+H3SketCmGmHHHY7tE2javOtqEiwUd2wM666QBLFxfvtNu/yfpefpS13+N71Qe2FFNosSmzaR9AdCiFbYbWr6hyqzKaALlJ5VtqVKz7Y+spLUZS0chXr+658yGR5MQ8AEPZ2aD4TwJoe4+bdfAdi8PQfKntETZN+NW57332xeF4sfSw3OH2K8vptFgU78LkV48KqKdE1XSF14apcJD7h1dLzYmDBLQPHZiRluVVF6i9cwFwsM+8=", "Workday": "0VJDXjkIxVkAxdN4lbfzDWNOmsRNvVHk001s8H1RRD4PlhREPSl654TmuKFxgJxxFzq9l97jBKZ11umTWU2Kvjv2mL9pmY51Beh5r7C63IFvRTlt2TG7NhUeKXlxfco8xYCl4APxXDBGF8zDSVikQILCpLEKRpoZRSACn/vCqaPmavduGkBIeO2OrPiNUSfMgM1chle7YFXrQGzJo0cRAe84R+KB7jxJzDT43k+uhuDNJALqDtj7SovFjuXcjEmZF7O7EWh+1ypAvwb7PPAoqGgDV17Lq6G0TAeqxIpqxzZJXzDc3xSIv49ICShbGx/4gi/yixUTVvh+ci5m5kov0xTvBS7/7wUk37A2ZUUOAWgmQM6SxTgpiTn9Box4dH/IrJmqD/FhfpKm+U+EaO42UmSyaeLDclMuOmaBVgFZ4UTvzxad4wr1XhD2VjirebUasxwv0xamcCS5Lt69uxGxQbs1TNLbxZ0G84iWWDx7rVDz1jKV2Ue5uU9RZFIIjfjB1P/8LWsYT9QSpL+SqHlrBOzTdYElggxGtYNRcD+2UWSrEbYoGcoJ4lnp6lVfwpWwL6zjJo89pzCZ0NXbn30c14BsqbivUD/1qB4MP8+ajm7FdHOdSudgaJyGpmespmu+JdbdrZ29OR5y0aTiytyDzKtzwEEGhFEobYojIH0qP1Y=", "SeriLog": "cQQ/2HWF/jIVKoFt1RRw3W9Ook0/wElNw/eaRRzNV+MZLHgUknTqAkAQlXPACDorskK3FrlwGm+0L1BDTMH6B9IsCkHBvUrqkdpUt0vuVYRnlVR0uyEd7owSWyMbZbJdQ6tF2Wz1BEq1/IZkJd5QemGZfcTAY9Nz0/LdY5B1Iz0/1gal4+FJaglUs0CRt6h5JB3KCjU1I323lcrg9X8RL5bNVGJTL2onhhJlt1b6SNJUi4c03I2TuDm+14ahGeJRRyALe1koXbb52a+2JJxUSWvl/6hnrqHRZEaBy42cwodGoGxnfUhExFewf4/TYBpkJF1Fxg4eTy7KU2RNqsgX+5v7Px/DV/4+7kf9Y5BN0jW2HnLpBV+tBif1fJJRHj6eQlbf7+2cAYtb8222mwYTEHUAZqXVj2eBTPI2rqH9nTGXPchra3CzKfPAWsAQuvaxPSjFaWvCxAqmSjFid0AQBpC6XxDcXRE0BTJyWRvnQVOQ2S8BJnlqi79xGCjt41bfe3b8rXD05t9aitfwuDz4sYQeBIei6qgtOyOHMvqDdc7yCR3SSAB1a4ZshioHR8KGdFxBm0YDAOgR8npStUcwm57VoIAFA01hPHzQqWt8uAJ62IKyoOXNag69Zb1bt0+NlcaH4dJSfr2eZgBdc/5sPnw8b9Q5QXPmlZ2aoXCl/Us="}, "ConnectionStringKeys": {"Attendance": "Attendance", "Project": "MIS", "Department": "MIS", "Employee": "MIS", "SinoSign": "SinoSign", "Workday": "Workday", "SeriLog": "Serilog"}, "Email": {"SendEmail": false, "SmtpHost": "xyYKvy3lMfrOtwue3Fm7Y/bY77650NRuzSPvoymr/uMUgvxz/kmLEWputkqPqZYSn+A41VFsDoUOrQQNXnKdLwXOdbIGWDUINANbSzCQ5999B952/CEOmehzeq5J0iETLd99C5aj8R/63T3BggTN2aacVZTVdUp1RR2wldu7cwsIkiDB99K2+ahdn2wIWt1CHdOhP2iTh0GS+USiu5+tkDQGsUuibAasuuu3pK/uFB1ez6vEHTqoJjulTbTJYXICBJYZ6VHBWi4uvpLBRnmAAz1oHZwXqLiytWYihCNAIVZIKiaGQyflg1uR0LDu/NNi42LgQZrRbCTHolUvxc8Hu3CZWQYU/cg/t456exkbgTiJ2/PuiIHYIwuYsMx3/MpLfLkXNR7g/t7rs0nlXfl39VzPtyvgnW4PTPYESYPUOskK2HUIj0tn+T28ON8f74pL02LB5OaEGsIjpez81KXeskNXe9Si/JOMQQoaa3vlhMRpGBOpvOrVpbEvS/x+FlPfE+Y921EXcarqh0cOy6gm9YCgf99xyXRVCbwXuBpC2a4SzVNTcnXqkqDAwI57a86eNPFNVXCAL1p+kM4s3v9ZUuwqY2LnxYfIn2u7p23HmahnaVBOtRRAqWA7v+EIw2tUDjcGo6jBb2zFicSNEmH3GPTH3ku32dw2+wJ9opcSxKY=", "Port": 25, "Subject": "【{FormName}】簽核{CommentOrResult}通知", "SenderEmail": "<EMAIL>", "SenderName": "三卡電子表單", "CommentMailTemplate": "CommentMail.template", "DisagreeMailTemplate": "DisagreeMail.template", "NotifyLevelTemplate": "Notify.template", "ApproveLevelTemplate": "Approve.template", "DeputyApproveLevelTemplate": "DeputyApprove.template", "Username": "Uv/RMLGIGRoj2plvKJ5agU7XKGyKO1XY9Eawx3veLzAQPgokllxgshXzg5L/i/DzAIq0kRunsOSc26OKuyjTsDwMaKcH9sRHAiCNMrP8QigmpfjIyOBQCR3hXInTiVp2K7jercJ59AM813ZWG1cBJ+D+oU3lyXs3Y/hVku+AQxS30bTqbXocV51XhXhP2q+UVm/4pgitReLwvEBRHTR49g3/sPW9HIh6ac6TiGuzFgW3kMpCyHP8qE6AxCBhziHzhc4/T2WRx+vJDHJAfSseGIWTeywAfstnAStlsjSBslFjAGWXa13F8UrYt0g2MAjIswMIa3+I6MbE0pE3+nKsviTQMvk1CcqZjibIDBTJI2fdf0UT40tTB5PZvIQjJHWeckssmg26hE0yDRzz6Jq1cKDt8l3AI1K4SCqe89tPzJimbUqQ+mWtyLmGrcAfGw0NDahtbqJIzrIbxWGqH9KLXRWwhHMiYqla2OJs/vmemyyLZBHddi0Y7YXh5FHOys/rugoeZJpEUmj0ee8rOQOtl3PJrdLy0VULAEYkG8jjl3DCHimuatUznM3y2gKkvAdscMvy4Vg5mdf9XnDQi2YMtJ2IMclXQ4mUESX8syxSKaDizqJ0Ms7LOyLgwMvQXdSpTDM3Coe9n7amaJwPr/cZUyU/IEb1bDKJ3dz0YpdwAf4=", "Password": "eTa5TEc9MYXSI/HryEtIbq6jBzXxvOX01T5LkoE1/ksZZO637cwWe1x8z8lYh27BPxaVaNgzbO4HL2e6IBTvW2UuPYdziClwGKAXKrm64xuXJALJTRQpmdYhyDHrjoBcazxAxRF4hEdn3z/mil9LGrMKWQhWZUZofOg12mmnO+VDC8iItwAzRvCX6YRZRjXDxxqqkt+F3IgYf/AHUNwSYRLbt5eZdrAnKa1xRBW56TZrvazqwfRT8A9gdFY8JtnY+draHQGYQbrUGwuA9ml0i3WYsLJGmiJqbZrz5tgZBFazCAkHUS3wXzqMWGD5kpvaPDBmBLuhDaxDNrS4+7k0CMwPYh89Fs9ICeyZUQCKPhK0doBp3pdfmrkPB0WrF4t7djiGP/tAtuxVMt1WinTcqdiOMVKMxLqGeZ0HRmSu+V9rbIO4kikfLIAkA1A4nqtsLn9akfvpqhMBZZ3KrgS9BxEi2dnhljgHjOt64XLIr7YAXhxBNEXiHkkeeO/WwSMlXlOXRBLDikNxPAlhH8gZh+ZZzGDNzCZwEA3TqcF0Eg17maKqhJXTbaCXSyqryVRda8PzE7+l6drIEHWWTYdiDcTzG4X6s8n3SCnMlfEzrXBzCYjAD0eZ4CcW7Jzder0VRTDDMv3x8BDrbEORSouPexl7f93ZO6obxBtrHp3Akhc=", "ApplicationURL": "https://attendance.sinotech.org.tw/AttendanceCard/"}, "UseNegotiate": true, "UploadDirectory": "D:\\AttendanceCard\\Upload", "GeoDbDirectory": "d:\\GeoIP", "ApproveCommentsHtml": false, "AllowedIPs": "*", "IsMultipleVicePresidents": true, "Attachments": {"ShareFolder": "AEsYzoxZxmOoo1el/nfvU2htcMOfiJNnqHOGdpUq1HCBrA7o9IONOyHrrWSk4ZfT5NMwsnvzjSLOVoO2mnAtQXdg+RYvRDmGZahqC1ZJjDrDW86iy32rO2EZzhhrT6KspqYEeTQB7W9yMj93DdQsgNvywCJIh7R9z7Xh8vIJRqh2obdxvj8JeTF6dSHLENUXkUHfprTQCi6/71Z3hgHLMTTuFVwraXbV8Ka/taCPu8EVNejjC8o5tMrm3jFACH2NcwzxhHxR8x/x3AKstoSHhOsUY4E7ERiQCr+jXFzKRYdAB2KndweoUBicRSYjIediezVHqN6fpSwswwbQE8QZbLdDm0i9ESyD5R9KzbMJ9FUhZzynvLZae5IDGMxqezrjdko8xxD/Uexm03ZtLJDZh6T/lLTT1S6pPlqnGOYH1d3BznJd8E14X+2ot1m9SS33IBJbICnEqCbcMJtqf4uKqv/IPlghjmXctrEPZSswUjDmzhlQJmykdewExDGO9UpSz7RsVEJh9xOLrR2WWwIWbcPjjcIDKUc5YEEFWkwWMLErn/pDr5rposD62czibj8etfdk3zfNiNJASIOFTKrJd6/BEdl9Qp5yFmMj9Hkd4UUXknGD//spkLtQVaDe/cJQwkFWlXC8b1n6kB/suZ9Zt6kgAk9xEUT5a9MqI5BrUzo=", "Domain": "tFBroWPirGRkhnzVe8nAMPcnvrZEhUQBRCdruefnjegnu758CqGnDdGZWPkmkUZc268pUxVtfqbPqBA28ZXyc4Y1KZ9k/2o8SXznRI6wohavWJK4dZYvO74RtUiLNYmnugnk28YY+UnSk78Ihr/V3OidwI3lcvkfJbZQwkdIZxsHzKqNzO3kefNslEnDXhmKw6O+Lg3U7tXhVuc3Gz+4lqhWBBYkYNDFM2h/S5src8xtT8bSOP8/JpGvh4JmINgBOcQjgFjU/qe5TwuD3lZo9F5L2rTNmhkekYSn4ZsvCxE1Ua3sJMz38JydocpWUe02NQFB1qYn5BfrGAt2zfqcMfjuyrGUr1/Q5DhRhLHUgGjvG966F0scFSM9b+1m5QN6aTDN3D2MrAOFUvhYwiJ+Bg8wdaNHwxN8F23D1nfHR6Q9Q6DGaSRK/fTOx61+fdtkSC95z30AXptGfLvkAtUKHywDqCQvc6UpiLwTdUI/La4pHbpnV4JcY2bndkvK0vvY6x9LKjGudZE0x6EVz/TefvcfwsygbfwhLJO4gCFHqaulpOzZGFDQURY0EL7Swmc2jnJ3lka5CtQuhWt/DLNyovVqBg8vB7e0u911yjOwBF+FXoKFXMq0gO7biTXiWPa1/LmgHJ1xH0P3N9lbp8eC0l2RAc70ORtwaUfuRheB00U=", "UserName": "YkVFiOLQuaxaoe9VADJ6rIemRD0jjNHL56yZnqc326iBzq4YTS/IntNS/L+i+JFqNnwjLcgYumHSiO72CO4gHGfIf+pLB6vUNUQdIUOcjN4ydqKheDN/fKWmmJbs1aF7eb8qHnOLJRs4c0iEIVbXu0Vinf1vAxRSRS9UAVMb1d1auEj2TJ7hUDWKepxSmVDQ/IGrhthjmLEoW3MMgmhVoVZxiTxGwVW6puMbWF2fyZFC+ThgW69oXB1XsyJOEXw3X8Dh6q3MunnA/C1+SYPSTmq89Tb4J0rTSWzGV+I4Ww0rIr7q69GO8TcOA5rfTwJko3HAbLMU1e09qkproPvcLcKgTxgyjXPwvSUrD+VZPnCBZgwXfExcm889tVudPPFwhRrr01nbwsaxKUKupe3MN9MrSdp1deOZACxbvGuPoWVlu4mTPZzOhYnJzDDVhNkuTh7k/mWp9D2k2p36E1nFdPdCUQxjIyyWq3HzjR6FO/jUeTelPvgjnAXtuI2nGgxEopmLIo2JiVkAw8SGYIDZ8Y28NTHCeLRGewanLONCThamYWpSvD5La1qtX7yeWgPY+jBq/vh0chIOb/X8YRDQarZQ+m9F/hjdFaNxyRv5zt3MmhuZx2Uu5tNMq7krvKaaf+4OcKavFdIqkwboSjO+8LVa2Ox4IFh56Xk3Yg8e9PI=", "Password": "XejidNu68KeclvpEs0JT2z2Z0TZ+E1y4fo5eP3YpX0zCGYrI1EsgC8NgVDy4x0FOItxo6tXclCIz4B/Sz6oRSTBliUTp3VXn9SZs4FDkciGUpuLT74PQSxMs3HnAR7wH5FFQxXLDWWQiT2Mp0+tlozWRjAnsqBu6D8yrgIlSt5A6sjuBKGFBQq5YKQIJyOYrGgEfL1vgZxkDkxyPULAiRbmSC61rZ1fzGwPpwJ2Ph8GKlb1rcICvSowN5IfnjSvbXhZVcqsRW+jQghym+f21QlqwdI5H7a/u+icYF9vwawKPQj1cNI7agHP5WZfC67byokYPMNeJB/JNKvbGzyNdi8vSEwks2VQK3aokN9l/oziXB3m2ppxL86v5fSC6QdS+xXnJmKTENaBujaIMERntg13T83DfL2gL+bKK5VK6zXYynpaDhG2b5t1A6ma7lXQ4RDNK8p87umvgBH6/4BrdidvvFRuSYPpZRd8ypIkpBRNaAOBsEdPk8NTf1McoN8dy5kr4csBTV2WYfAbSN4bKrHyHyrZBlh7hIDqVi7Rh9DxkFx9vwf6pQJPa3p/zL2PhWH13EXtO6Q0AgNxcGKpOMsWMGqCfOi8zsUE5IwzO2g5ZIgCNmCLQE3Oi661YKrZOD4+RW09RC6wEU1B40iXz/c2QRUEr6+/VIdn/9Go3b5g=", "ShareFolder-decrypt": "\\\\attendancedev.sinotech.org.tw\\Attachment", "Domain-decrypt": "Sinotech", "UserName-decrypt": "SinoForm", "Password-decrypt": "Form20210228"}, "Serilog": {"MinimumLevel": {"Default": "Verbose", "Override": {"Microsoft": "Warning", "Serilog.AspNetCore.RequestLoggingMiddleware": "Warning", "Sinotech.Mis.Serilog.Utilities.Middlewares.SerilogRequestLogger": "Warning", "Vue": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"restrictedToMinimumLevel": "Information"}}, {"Name": "File", "Args": {"path": "D:\\AttendanceCard\\Logs\\log.txt", "rollingInterval": "Day", "outputTemplate": "{Timestamp} {Message}{NewLine:1}{Exception:1}", "restrictedToMinimumLevel": "Information", "shared": "true"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithProcessId", "WithProcessName", "WithCorrelationId", "WithThreadId"], "Properties": {"ApplicationName": "AttendanceCard", "ApplicationVersion": "1.0"}}, "Serilog.MsSqlServer": {"Name": "MSSqlServer", "Args": {"connectionString": "Serilog", "sinkOptionsSection": {"tableName": "LogEvents", "useBulkInsert": false, "useSqlBulkCopy": false, "schemaName": "dbo", "autoCreateSqlTable": false}, "restrictedToMinimumLevel": "Information", "columnOptionsSection": {"disableTriggers": true, "clusteredColumnstoreIndex": false, "primaryKeyColumnName": "Id", "addStandardColumns": ["LogEvent"], "removeStandardColumns": ["Properties"], "additionalColumns": [{"ColumnName": "Username", "DataType": "n<PERSON><PERSON><PERSON>", "AllowNull": true, "DataLength": 256}, {"ColumnName": "ClientIp", "DataType": "n<PERSON><PERSON><PERSON>", "AllowNull": true, "DataLength": 128}, {"ColumnName": "UserAgent", "DataType": "n<PERSON><PERSON><PERSON>", "AllowNull": true, "DataLength": 256}, {"ColumnName": "MachineName", "DataType": "n<PERSON><PERSON><PERSON>", "AllowNull": true, "DataLength": 128}, {"ColumnName": "ProcessName", "DataType": "n<PERSON><PERSON><PERSON>", "AllowNull": true, "DataLength": 256}, {"ColumnName": "SourceContext", "DataType": "n<PERSON><PERSON><PERSON>", "AllowNull": true, "DataLength": 512}, {"ColumnName": "RequestHost", "DataType": "n<PERSON><PERSON><PERSON>", "AllowNull": true, "DataLength": 512}, {"ColumnName": "RequestQueryString", "DataType": "n<PERSON><PERSON><PERSON>", "AllowNull": true, "DataLength": -1}, {"ColumnName": "RequestContentType", "DataType": "n<PERSON><PERSON><PERSON>", "AllowNull": true, "DataLength": 256}, {"ColumnName": "RequestProtocol", "DataType": "n<PERSON><PERSON><PERSON>", "AllowNull": true, "DataLength": 128}, {"ColumnName": "RequestScheme", "DataType": "n<PERSON><PERSON><PERSON>", "AllowNull": true, "DataLength": 128}, {"ColumnName": "RequestMethod", "DataType": "n<PERSON><PERSON><PERSON>", "AllowNull": true, "DataLength": 10}, {"ColumnName": "RequestPath", "DataType": "n<PERSON><PERSON><PERSON>", "AllowNull": true, "DataLength": -1}, {"ColumnName": "RequestHeaders", "DataType": "n<PERSON><PERSON><PERSON>", "AllowNull": true, "DataLength": -1}, {"ColumnName": "RequestBody", "DataType": "n<PERSON><PERSON><PERSON>", "AllowNull": true, "DataLength": -1}, {"ColumnName": "ResponseBody", "DataType": "n<PERSON><PERSON><PERSON>", "AllowNull": true, "DataLength": -1}, {"ColumnName": "StatusCode", "DataType": "n<PERSON><PERSON><PERSON>", "AllowNull": true, "DataLength": 10}], "id": {"nonClusteredIndex": true}, "message": {"columnName": "Message"}, "messageTemplate": {"columnName": "MessageTemplate"}, "level": {"columnName": "Level", "storeAsEnum": false}, "timeStamp": {"columnName": "TimeStamp", "convertToUtc": false}, "exception": {"columnName": "Exception"}, "logEvent": {"columnName": "LogEvent", "excludeAdditionalProperties": false, "excludeStandardColumns": false}}}}}