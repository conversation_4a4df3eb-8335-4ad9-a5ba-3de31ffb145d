﻿<?xml version="1.0" encoding="utf-8"?>
<ClassDiagram MajorVersion="1" MinorVersion="1">
  <Class Name="Sinotech.Mis.HR.Attendance.C1Card.C1CardBase" Collapsed="true">
    <Position X="3.25" Y="0.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AABAAAAAAABACAAAEABAAAAAAAgAAAAAQAAAAAAAAAA=</HashCode>
      <FileName>C1Card\C1CardBase.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Sinotech.Mis.HR.Attendance.C1Card.C1CardFactory">
    <Position X="7.25" Y="0.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>C1Card\C1CardFactory.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Sinotech.Mis.HR.Attendance.BusinessLogic.C1Card.Leaves.AnnualLeave" Collapsed="true">
    <Position X="2" Y="2.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>C1Card\Leaves\AnnualLeave.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Sinotech.Mis.HR.Attendance.BusinessLogic.C1Card.Leaves.BirthdayLeave" Collapsed="true">
    <Position X="15.5" Y="2.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>C1Card\Leaves\BirthdayLeave.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Sinotech.Mis.HR.Attendance.BusinessLogic.C1Card.Leaves.BusinessInjuryLeave" Collapsed="true">
    <Position X="22.25" Y="2.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>C1Card\Leaves\BusinessInjuryLeave.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Sinotech.Mis.HR.Attendance.BusinessLogic.C1Card.Leaves.BusinessOutLeave" Collapsed="true">
    <Position X="33.5" Y="2.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>C1Card\Leaves\BusinessOutLeave.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Sinotech.Mis.HR.Attendance.BusinessLogic.C1Card.Leaves.BusinessTripLeave" Collapsed="true">
    <Position X="42.5" Y="2.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>C1Card\Leaves\BusinessTripLeave.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Sinotech.Mis.HR.Attendance.BusinessLogic.C1Card.Leaves.CompensatoryLeave" Collapsed="true">
    <Position X="4.5" Y="2.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>C1Card\Leaves\CompensatoryLeave.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Sinotech.Mis.HR.Attendance.BusinessLogic.C1Card.Leaves.DisasterLeave" Collapsed="true">
    <Position X="17.75" Y="2.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>C1Card\Leaves\DisasterLeave.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Sinotech.Mis.HR.Attendance.BusinessLogic.C1Card.Leaves.FamilyCareLeave" Collapsed="true">
    <Position X="24.5" Y="2.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>C1Card\Leaves\FamilyCareLeave.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Sinotech.Mis.HR.Attendance.BusinessLogic.C1Card.Leaves.ForeignLeave" Collapsed="true">
    <Position X="35.75" Y="2.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>C1Card\Leaves\ForeignLeave.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Sinotech.Mis.HR.Attendance.BusinessLogic.C1Card.Leaves.FuneralLeave" Collapsed="true">
    <Position X="8.75" Y="5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAAAAAAAAAAA=</HashCode>
      <FileName>C1Card\Leaves\FuneralLeave.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Sinotech.Mis.HR.Attendance.BusinessLogic.C1Card.Leaves.LeaveWithEvent">
    <Position X="7.25" Y="2.75" Width="2.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAAAAAAAAAAA=</HashCode>
      <FileName>C1Card\Leaves\LeaveWithEvent.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Sinotech.Mis.HR.Attendance.BusinessLogic.C1Card.Leaves.MarriageLeave" Collapsed="true">
    <Position X="4.25" Y="5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAAAAAAAAAAA=</HashCode>
      <FileName>C1Card\Leaves\MarriageLeave.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Sinotech.Mis.HR.Attendance.BusinessLogic.C1Card.Leaves.MaternityLeave" Collapsed="true">
    <Position X="26.75" Y="2.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>C1Card\Leaves\MaternityLeave.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Sinotech.Mis.HR.Attendance.BusinessLogic.C1Card.Leaves.MenstrualLeave" Collapsed="true">
    <Position X="38" Y="2.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>C1Card\Leaves\MenstrualLeave.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Sinotech.Mis.HR.Attendance.BusinessLogic.C1Card.Leaves.ObstetricInspectionLeave" Collapsed="true">
    <Position X="11" Y="5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAAAAAAAAAAA=</HashCode>
      <FileName>C1Card\Leaves\ObstetricInspectionLeave.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Sinotech.Mis.HR.Attendance.BusinessLogic.C1Card.Leaves.OfficialLeave" Collapsed="true">
    <Position X="10.75" Y="2.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>C1Card\Leaves\OfficialLeave.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Sinotech.Mis.HR.Attendance.BusinessLogic.C1Card.Leaves.PaternityLeave" Collapsed="true">
    <Position X="6.5" Y="5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAAAAAAAAAAA=</HashCode>
      <FileName>C1Card\Leaves\PaternityLeave.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Sinotech.Mis.HR.Attendance.BusinessLogic.C1Card.Leaves.PersonalLeave" Collapsed="true">
    <Position X="29" Y="2.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>C1Card\Leaves\PersonalLeave.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Sinotech.Mis.HR.Attendance.BusinessLogic.C1Card.Leaves.PostponedLeave" Collapsed="true">
    <Position X="40.25" Y="2.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>C1Card\Leaves\PostponedLeave.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Sinotech.Mis.HR.Attendance.BusinessLogic.C1Card.Leaves.QuarantineCareLeave" Collapsed="true">
    <Position X="44.75" Y="2.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>C1Card\Leaves\QuarantineCareLeave.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Sinotech.Mis.HR.Attendance.BusinessLogic.C1Card.Leaves.QuarantineIsolationLeave" Collapsed="true">
    <Position X="13.25" Y="2.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>C1Card\Leaves\QuarantineIsolationLeave.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Sinotech.Mis.HR.Attendance.BusinessLogic.C1Card.Leaves.SickLeave" Collapsed="true">
    <Position X="20" Y="2.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>C1Card\Leaves\SickLeave.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Sinotech.Mis.HR.Attendance.BusinessLogic.C1Card.Leaves.VaccinationLeave" Collapsed="true">
    <Position X="31.25" Y="2.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>C1Card\Leaves\VaccinationLeave.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Font Name="Microsoft JhengHei UI" Size="9" />
</ClassDiagram>