﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Sinotech.Mis.HR.Attendance.Common;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.Tests
{
    [ExcludeFromCodeCoverage]
    public class NotifyFormCardsTest
    {
        [Fact]
        public void NotifyFormCards_Test()
        {
            NotifyFormCards notifyFormCards = new NotifyFormCards();
            Assert.NotNull(notifyFormCards);
        }
    }
}
