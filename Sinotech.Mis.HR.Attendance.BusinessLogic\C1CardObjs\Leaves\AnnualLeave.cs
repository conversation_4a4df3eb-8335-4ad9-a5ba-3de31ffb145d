﻿using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using System;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves;

/// <summary>
/// 特休類別
/// </summary>
[LeaveKind(LeaveKindEnum.AnnualLeave)]
public class AnnualLeave : C1CardBase
{
    #region CheckResult

    //////////////////////////////////////////////////////////////////////////////////////////////////////////////

    //////public static int CodeXXXXXXXXX { get => 3001301; }
    //public const int CodeXXXXXXXXX = 3001302; }
    //private readonly string _message_XXXXXXXXX = "您的年度休假資料尚未建立，請通知行政處";
    //private readonly CardStatusEnum _status_XXXXXXXXX = CardStatusEnum.Error;

    //public CardCheckResult? _resultLeaveDataNotCreatedYet;
    //private CardCheckResult ResultLeaveDataNotCreatedYet =>
    //    _resultLeaveDataNotCreatedYet ??=
    //    new CardCheckResult(Code_XXXXXXXXX, _status_XXXXXXXXX, _message_XXXXXXXXX);

    //////////////////////////////////////////////////////////////////////////////////////////////////////////////

    //////public static int CodeXXXXXXXXX { get => 3001302; }
    //public const int CodeXXXXXXXXX = 3001302; }
    //private readonly CardStatusEnum _statusXXXXXXXXX = CardStatusEnum.Error;
    //private readonly string _messagePatternXXXXXXXXX = "您尚有剩餘延休假 {0} 小時，請優先填報【延休假】";

    //////////////////////////////////////////////////////////////////////////////////////////////////////////////

    public const int CodePostponedLeaveExists = 3001301;
    private readonly CardStatusEnum _statusPostponedLeaveExists = CardStatusEnum.Error;
    private readonly string _messagePatternPostponedLeaveExists = "您尚有剩餘延休假 {0} 小時，請優先填報【延休假】";

    public const int CodeExceedQuota = 3001302;
    private readonly CardStatusEnum _statusExceedQuota = CardStatusEnum.Error;
    private readonly string _messagePatternExceedQuota = "您本次請假 {0} 小時，年度特別休息假剩餘 {1} 小時，不得超假";

    #endregion

    /// <summary>
    /// 特休建構子
    /// </summary>
    /// <param name="c1Card"></param>
    /// <param name="c1CardBo"></param>

    public AnnualLeave(C1Card c1Card, IC1CardBo c1CardBo) : base(c1Card, c1CardBo)
    {
    }

    /// <summary>
    /// 檢查是否能請此假
    /// </summary>
    /// <returns></returns>
    internal override CardCheckResult CanTakeThisLeave()
    {
        // 是否已經建立休假資料
        if (!IfLeaveRecordExists())
        {
            return ResultLeaveRecordNotCreatedYet;
        }

        // 是否仍有延休假
        var postponedLeaveRemainHours = GetPostponedLeaveRemainingHours();
        if (postponedLeaveRemainHours > 0)
        {
            return new CardCheckResult(CodePostponedLeaveExists, _statusPostponedLeaveExists,
                string.Format(_messagePatternPostponedLeaveExists, postponedLeaveRemainHours));
        }

        return ResultOk;
    }

    /// <summary>
    /// 檢查是否超假
    /// </summary>
    /// <returns></returns>
    internal override CardCheckResult CheckOverPermittedLeaveHours()
    {
        var annualLeaveRemainingHours = GetAnnualLeavetRemainingHour();
        if (annualLeaveRemainingHours < TotalHours)
        {
            return new CardCheckResult(CodeExceedQuota, _statusExceedQuota,
                string.Format(_messagePatternExceedQuota, TotalHours, annualLeaveRemainingHours));
        }
        return ResultOk;
    }

    /// <summary>
    /// 檢查必要欄位
    /// </summary>
    /// <returns></returns>
    internal override CardCheckResult CheckRequiredFields()
    {
        var result = base.CheckRequiredFields();
        if (result != ResultOk)
        {
            return result;
        }

        return ResultOk;
    }

    /// <summary>
    /// 計算最早可請假日期與最晚可請假日期
    /// </summary>
    /// <param name="date">請假日期</param>
    /// <param name="empNo">員工編號</param>
    /// <returns></returns>
    public override (DateTime, DateTime) CalculateLeavePermittedPeriod(DateTime date, string empNo) => DefaultCalculateLeavePermittedPeriod(date, empNo);

    /// <summary>
    /// 此性別是否可請此假別
    /// </summary>
    /// <param name="gender"></param>
    /// <returns></returns>
    public static bool IsAllowForThisGender(Gender gender)
    {
        return true;
    }

    #region  Delegate to another gateway

    private int GetAnnualLeavetRemainingHour()
    {
        return _c1CardBo.GetAnnualLeaveRemainingHours(_c1Card.EmpNo, _c1Card.StartDate);
    }

    #endregion
}
