﻿using System.Collections.Generic;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 假別
    /// </summary>
    public class LeaveKind
    {
        /// <summary>
        /// 假別編號
        /// </summary>
        public LeaveKindEnum Number { get; set; }

        /// <summary>
        /// 假別名稱
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 假別上限
        /// </summary>
        public int UpperLimit { get; set; } = 0;

        /// <summary>
        /// 顯示順序
        /// </summary>
        public int DisplayOrder { get; set; }

        /// <summary>
        /// 是否為事件類假別
        /// </summary>
        public bool IsEvent { get; set; } = false;

        /// <summary>
        /// 是否允許請假日期為日曆天
        /// </summary>
        public bool IsCalendarDay { get; set; } = false;

        /// <summary>
        /// 是否須檢附證明文件
        /// </summary>
        public bool CertificateRequired { get; set; } = false;

        /// <summary>
        /// 請假單位
        /// </summary>
        public string Unit { get; set; } = "H";

        /// <summary>
        /// 假別上限，單位依照 Unit 而定
        /// </summary>
        public int? LeaveMaximum { get; set; } = null;

        /// <summary>
        /// 最小請假單位
        /// H：小時
        /// D：天
        /// F：半日
        /// </summary>

        public string MinimumUnit { get; set; } = "H";


        /// <summary>
        /// 適用性別： 0 女性   1 男性  2 男女皆可
        /// </summary>
        public int Gender { get; set; } = 2;

        /// <summary>
        /// 自動計算請假截止日，介面上不開放截止時間
        /// </summary>
        public bool AutoEndDate { get; set; } = false;

        /// <summary>
        /// 一次只允許請一天
        /// </summary>
        public bool OnlyOneDay { get; set; } = false;

        /// <summary>
        /// 必填計畫
        /// </summary>
        public bool NeedProject { get; set; } = false;

        /// <summary>
        /// 必填地點
        /// </summary>
        public bool NeedLocation { get; set; } = false;

        /// <summary>
        /// 是否在前端顯示
        /// </summary>
        public bool DisplayOnFrontEnd { get; set; } = true;

        /// <summary>
        /// 附件填報說明
        /// </summary>
        public string? AttachmentExplanation { get; set; } = null;

        /// <summary>
        /// 附件頁面填報說明
        /// </summary>
        public string? AttachmentPageExplanation { get; set; } = null;

        /// <summary>
        /// 頁面填報說明
        /// </summary>
        public string? PageExplanation { get; set; } = null;

        /// <summary>
        /// 截止時間填報說明
        /// </summary>
        public string? EndTimeExplanation { get; set; } = null;

        /// <summary>
        /// 事件發生日填報說明
        /// </summary>
        public string? EventExplanation { get; set; } = null;

        /// <summary>
        /// 附件頁面填報說明
        /// </summary>
        public string? SwitchLeavePrompt { get; set; } = null;

        /// <summary>
        /// 假別下方的提示訊息
        /// </summary>
        public string? LeaveKindExplanation { get; set; } = null;

        /// <summary>
        /// 假別細項
        /// </summary>
        public List<LeaveSubKind> Detail { get; set; } = new List<LeaveSubKind>();
    }
}
