﻿namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 員工的各種假(Leave)
    /// </summary>
    public class EmployeeLeaves
    {
        /// <summary>
        /// 剩餘可用特休時數
        /// </summary>
        public int RemainAnnualLeaves { get; set; } //剩餘可用特休時數

        /// <summary>
        /// 可用保留代休
        /// </summary>
        public int RemainReservedLeaves { get; set; } //可用保留代休

        /// <summary>
        /// 上月剩餘保留代休時數
        /// </summary>
        public int RemainReservedLeavesLastMonth { get; set; } //上月剩餘保留代休時數

        /// <summary>
        /// 本月新增保留代休
        /// </summary>
        public int NewReservedLeavesMonth { get; set; } //本月新增保留代休

        /// <summary>
        /// 本月使用特休時數
        /// </summary>
        public int UsedAnnualLeavesMonth { get; set; } //本月使用特休時數

        /// <summary>
        /// 年度可用特休時數
        /// </summary>
        public int TotalAnnualLeaves { get; set; } //年度可用特休時數

        /// <summary>
        /// 累計使用特休時數
        /// </summary>
        public int UsedAnnualLeaves { get; set; } //累計使用特休時數

        /// <summary>
        /// 累計事假時數
        /// </summary>
        public int UsedPersonalLeaves { get; set; } //累計事假時數

        /// <summary>
        /// 本月事假時數
        /// </summary>
        public int UsedPersonalLeavesMonth { get; set; }  //本月事假時數

        /// <summary>
        /// 累計病假時數
        /// </summary>
        public int UsedSickLeaves { get; set; } //累計病假時數

        /// <summary>
        /// 本月使用病假時數
        /// </summary>
        public int UsedSickLeavesMonth { get; set; } //本月使用病假時數

        // 2015年起全面使用延休假,之前為特簽
        /// <summary>
        /// 是否顯示 延休假
        /// </summary>
        public bool DisplayExtendedLeaves { get; set; } // 是否顯示 延休假欄位

        /// <summary>
        /// 延休假時數
        /// </summary>
        public int ExtendedLeaves { get; set; } //前一年留下來的 延休假時數{get;set;}

        /// <summary>
        /// 本月 延休假 使用時數
        /// </summary>
        public int UsedExtendedLeavesMonth { get; set; } //本月使用延休假

        /// <summary>
        /// 累計使用延休假時數
        /// </summary>
        public int UsedExtendedLeaves { get; set; } //累計使用延休假時數

        /// <summary>
        /// 剩餘延休假
        /// </summary>
        public int RemainedExtendedLeaves { get; set; } //對應白燕菁的 surplus, 剩餘延休假

        /// <summary>
        /// 是否已經匯入特休資料
        /// </summary>
        public bool ExtendedLeavesIsImported { get; set; } //是否已經匯入特休資料


        /// <summary>
        /// 累計已休生理假
        /// </summary>
        public int UsedMenstruationLeaves { get; set; }

        /// <summary>
        /// 本月已休生理假
        /// </summary>
        public int UsedMenstruationLeavesMonth { get; set; }

        /// <summary>
        /// 性別
        /// </summary>
        public char Gender { get; set; } //性別

        /// <summary>
        /// 本月新增保留代休
        /// </summary>
        public int NewRemainCompensatoryLeavesMonth { get; set; }

        /// <summary>
        /// 本月已用保留代休
        /// </summary>
        public int UsedCompensatoryLeavesMonth { get; set; }

        /// <summary>
        /// 所有新增保留代休
        /// </summary>
        public int TotalNewRemainCompensatoryLeaves { get; set; }

        /// <summary>
        /// 所有已用保留代休
        /// </summary>
        public int TotalUsedCompensatoryLeaves { get; set; }
        /// <summary>
        /// 所有保留代休
        /// </summary>
        public int TotalCompensatoryLeaves { get; set; }
    }
}
