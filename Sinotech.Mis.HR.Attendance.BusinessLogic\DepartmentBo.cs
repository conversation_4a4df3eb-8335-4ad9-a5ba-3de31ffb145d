﻿using Newtonsoft.Json;
using Sinotech.Mis.Common;
using Sinotech.Mis.Helpers;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.Utilities.DataAccess.Interfaces;
using System;
using System.Collections.Generic;
using System.Data;
using System.Runtime.Caching;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic
{
    /// <summary>
    /// 部門 商業物件
    /// </summary>
    public class DepartmentBo : IDepartmentBo
    {

        private static ObjectCache _cache = MemoryCache.Default;
        private static readonly object _cacheLock = new object();
        private readonly IDepartmentDao _departmentDao;
        private readonly IEmployeeBo _employeeBo;

        /// <summary>
        /// 建構元
        /// </summary>
        /// <param name="departmentDao"></param>
        public DepartmentBo(IDepartmentDao departmentDao, IEmployeeBo employeeBo)
        {
            _departmentDao = departmentDao;
            _employeeBo = employeeBo;
        }

        /// <summary>
        ///   <para>合成組資料</para>
        /// </summary>
        /// <param name="dt">The dt.</param>
        /// <returns>
        ///   <br />
        /// </returns>
        private static List<DeptTeam> CompositeTeamData(DataTable dt)
        {
            List<DeptTeam> ret = new List<DeptTeam>();
            DataView dataView = dt.DefaultView;

            DataTable dataTableDistinct = dataView.ToTable(true, "DeptNo", "DeptSName", "TeamID", "TeamCName");

            foreach (DataRow dr in dataTableDistinct.Rows)
            {
                DeptTeam deptTeamDto = new DeptTeam();
                int teamId = 0;
                if (dr["TeamID"] != DBNull.Value)
                {
                    teamId = (int)dr["TeamID"];
                }

                deptTeamDto.DeptNo = (int)dr["DeptNo"];
                deptTeamDto.DeptSName = (string)dr["DeptSName"];
                deptTeamDto.TeamID = teamId;
                if (dr["TeamCName"] != DBNull.Value)
                {
                    deptTeamDto.TeamCName = (string)dr["TeamCName"];
                }
                else
                {
                    deptTeamDto.TeamCName = "未分組";
                }
                string selectStr = $"TeamID = {teamId} AND DeptNo = {deptTeamDto.DeptNo}";
                if (teamId == 0)
                {
                    selectStr = $"TeamID is null AND DeptNo = {deptTeamDto.DeptNo}";
                }

                DataRow[] dataRows = dt.Select(selectStr);
                List<EmployeeTiny> employeeDtos = new List<EmployeeTiny>();
                foreach (DataRow drEmployee in dataRows)
                {
                    EmployeeTiny employeeDto = EmployeeTiny.FromDataRow(drEmployee);
                    employeeDtos.Add(employeeDto);
                }
                deptTeamDto.TeamMembers = employeeDtos;
                ret.Add(deptTeamDto);
            }
            return ret;
        }

        /// <summary>
        /// 取得副理以上資料表
        /// </summary>
        /// <returns></returns>
        private DataTable GetAboveDeputyManagerDataTable()
        {
            string cacheName = "dtAboveDeputyManager";
            if (_cache.Contains(cacheName))
            {
                return (DataTable)_cache[cacheName];
            }
            DataTable dt = _departmentDao.GetAboveDeputyManagerDataTable();
            lock (_cache)
            {
                _cache.Set(cacheName, dt, CachePolicy);
            }
            return dt;
        }

        /// <summary>
        ///  取得組長資料表
        /// </summary>
        /// <returns></returns>
        private DataTable GetTeamLeadersDataTable()
        {
            string cacheName = "dtTeamLeaders";
            if (_cache.Contains(cacheName))
            {
                return (DataTable)_cache[cacheName];
            }
            DataTable dt = _departmentDao.GetTeamLeaders();
            lock (_cache)
            {
                _cache.Set(cacheName, dt, CachePolicy);
            }
            return dt;
        }

        private static CacheItemPolicy CachePolicy
        {
            get
            {
                CacheItemPolicy policy = new CacheItemPolicy();
                policy.AbsoluteExpiration = DateTimeOffset.Now.AddMinutes(2.0);
                return policy;
            }
        }


        /// <summary>
        /// 取得行政部門
        /// </summary>
        /// <returns></returns>
        public List<Department> GetAdministrativeDepartments()
        {
            string cacheName = "listAdministrativedepartments";
            if (_cache.Contains(cacheName))
            {
                return (List<Department>)_cache[cacheName];
            }
            DataTable dt = _departmentDao.GetAdministrativedepartments();
            List<Department> departments = SqlHelper.DataTableToList<Department>(dt);
            lock (_cache)
            {
                _cache.Set(cacheName, departments, CachePolicy);
            }
            return departments;
        }

        /// <summary>
        /// 取得研究中心
        /// </summary>
        /// <returns></returns>
        public List<Department> GetResearchCenters()
        {
            string cacheName = "listResearchCenters";
            if (_cache.Contains(cacheName))
            {
                return (List<Department>)_cache[cacheName];
            }
            DataTable dt = _departmentDao.GetResearchCenters();
            List<Department> departments = SqlHelper.DataTableToList<Department>(dt);
            lock (_cache)
            {
                _cache.Set(cacheName, departments, CachePolicy);
            }
            return departments;
        }

        /// <summary>
        /// 取得部門內所有員工
        /// </summary>
        /// <param name="departmentNumber">部門編號</param>
        /// <returns>部門內所有員工</returns>
        public DataTable GetDepartmentEmployees(int departmentNumber)
        {
            DataTable dt;
            string cacheName = $"DepartmentEmployees{departmentNumber}";
            if (_cache.Contains(cacheName))
            {
                dt = (DataTable)_cache[cacheName];
            }
            else
            {
                dt = _departmentDao.GetDepartmentEmployees(departmentNumber);
                lock (_cacheLock)
                {
                    _cache.Set(cacheName, dt, CachePolicy);
                }
            }
            return dt;
        }

        /// <summary>
        /// 取得部門內所有員工 JSON String
        /// </summary>
        /// <param name="departmentNumber">部門編號</param>
        /// <returns>部門內所有員工 JSON String</returns>
        public string GetDepartmentEmployeesJson(int departmentNumber)
        {
            string json;
            string cacheName = $"DepartmentEmployeesJson{departmentNumber}";
            if (_cache.Contains(cacheName))
            {
                json = (string)_cache[cacheName];
            }
            else
            {
                json = JsonConvert.SerializeObject(_departmentDao.GetDepartmentEmployees(departmentNumber));
                lock (_cacheLock)
                {
                    _cache.Set(cacheName, json, CachePolicy);
                }
            }
            return json;
        }

        /// <summary>
        /// 取得部門主管
        /// </summary>
        /// <param name="departmentNumber"></param>
        /// <returns></returns>
        public string GetDepartmentManager(int departmentNumber)
        {
            string ret = string.Empty;
            DataTable dt = GetDepartmentsManagers();
            DataRow[] drs = dt.Select($"DeptNo={departmentNumber}");
            if (drs.Length > 0)
            {
                ret = (string)drs[0]["EmpNo"];
            }
            return ret;
        }

        /// <summary>取得部門名稱</summary>
        /// <param name="departmentNumber">部門編號</param>
        /// <returns>
        ///   部門名稱<br />
        /// </returns>
        public string GetDepartmentName(int departmentNumber)
        {
            string ret = string.Empty;
            DataTable dt = GetDepartments();
            DataRow[] drs = dt.Select($"DeptNo={departmentNumber}");
            if (drs.Length > 0)
            {
                ret = (string)drs[0]["DeptName"];
            }
            return ret;
        }

        /// <summary>
        /// 取得所有部門
        /// </summary>
        /// <returns>所有部門</returns>
        public DataTable GetDepartments()
        {
            DataTable dt;
            string cacheName = "dtDepartments";
            if (_cache.Contains(cacheName))
            {
                dt = (DataTable)_cache[cacheName];
            }
            else
            {
                dt = _departmentDao.GetDepartments();
                lock (_cacheLock)
                {
                    _cache.Set(cacheName, dt, CachePolicy);
                }
            }
            return dt;
        }

        /// <summary>取得部門簡稱</summary>
        /// <param name="departmentNumber">部門編號</param>
        /// <returns>
        ///   部門簡稱<br />
        /// </returns>
        public string GetDepartmentShortName(int departmentNumber)
        {
            string ret = string.Empty;
            DataTable dt = GetDepartments();
            DataRow[] drs = dt.Select($"DeptNo={departmentNumber}");
            if (drs.Length > 0)
            {
                ret = (string)drs[0]["DeptSName"];
            }
            return ret;
        }

        /// <summary>
        /// 取得所有部門 JSON String
        /// </summary>
        /// <returns>所有部門 JSON String</returns>
        public string GetDepartmentsJson()
        {
            string json;
            string cacheName = "DepartmentsTeamsEmployeesJson";
            if (_cache.Contains(cacheName))
            {
                json = (string)_cache[cacheName];
            }
            else
            {
                json = JsonConvert.SerializeObject(_departmentDao.GetDepartments());
                lock (_cacheLock)
                {
                    _cache.Set(cacheName, json, CachePolicy);
                }
            }
            return json;
        }

        /// <summary>
        /// 取得各部門各級主管
        /// </summary>
        /// <returns>各部門各級主管</returns>
        public DataTable GetDepartmentsManagers()
        {
            DataTable dt;
            string cacheName = "DepartmentsManagers";
            if (_cache.Contains(cacheName))
            {
                dt = (DataTable)_cache[cacheName];
            }
            else
            {
                dt = _departmentDao.GetDepartmentsManagers();
                lock (_cacheLock)
                {
                    _cache.Set(cacheName, dt, CachePolicy);
                }
            }
            return dt;
        }

        /// <summary>
        /// 取得所有部門的組別及員工資料
        /// </summary>
        /// <returns></returns>
        public List<DeptTeam> GetDepartmentsTeamsEmployees()
        {
            List<DeptTeam> list = new List<DeptTeam>();
            string cacheName = "GetDepartmentsTeamsEmployees";
            if (_cache.Contains(cacheName))
            {
                list = (List<DeptTeam>)_cache[cacheName];
            }
            else
            {
                DataTable dt = _departmentDao.GetDepartmentsTeamsEmployees();

                if (dt != null && dt.Rows.Count > 0)
                {
                    dt.Columns.Add(new DataColumn("IsTeamLeader", typeof(bool)));
                    dt.Columns.Add(new DataColumn("IsAboveDeputyManager", typeof(bool)));

                    DataTable dtTeamLeaders = GetTeamLeadersDataTable();
                    foreach (DataRow dr in dt.Rows)
                    {
                        if (!dr.IsNull("EmpNo"))
                        {
                            string? empNo = dr.Field<string>("EmpNo");
                            if (empNo != null)
                            {
                                dr["IsTeamLeader"] = 0;
                                DataRow[] rows = dtTeamLeaders.Select($"EmpNo={empNo}");
                                if (rows.Length == 1)
                                {
                                    dr["IsTeamLeader"] = 1;
                                }

                                dr["IsAboveDeputyManager"] = IsAboveDeputyManager(empNo);
                            }
                        }
                    }
                }
                if (dt != null && dt.Rows.Count > 0)
                {
                    list = CompositeTeamData(dt);
                    lock (_cacheLock)
                    {
                        _cache.Set(cacheName, list, CachePolicy);
                    }
                }
            }
            return list;
        }

        /// <summary>
        /// 取得指定部門的組別及員工資料
        /// </summary>
        /// <param name="departmentNumber">部門編號</param>
        /// <param name="teamNumber">組別編號</param>
        /// <returns></returns>
        public List<DeptTeam> GetDepartmentTeamEmployees(int departmentNumber, int teamNumber)
        {
            List<DeptTeam> list = GetDepartmentTeamsEmployees(departmentNumber);
            List<DeptTeam> result = new List<DeptTeam>();
            foreach (var item in list)
            {
                if (item.TeamID == teamNumber)
                {
                    result.Add(item);
                }
            }
            return result;
        }

        /// <summary>
        /// 取得指定部門的組別及員工資料
        /// </summary>
        /// <param name="departmentNumber">部門編號</param>
        /// <returns></returns>
        public List<DeptTeam> GetDepartmentTeamsEmployees(int departmentNumber)
        {
            List<DeptTeam> list = new List<DeptTeam>();
            string cacheName = $"DepartmentTeamsEmployees{departmentNumber}";
            if (_cache.Contains(cacheName))
            {
                list = (List<DeptTeam>)_cache[cacheName];
            }
            else
            {
                DataTable dt = _departmentDao.GetDepartmentTeamsEmployees(departmentNumber);

                if (dt != null && dt.Rows.Count > 0)
                {
                    dt.Columns.Add(new DataColumn("IsTeamLeader", typeof(bool)));
                    dt.Columns.Add(new DataColumn("IsAboveDeputyManager", typeof(bool)));

                    DataTable dtTeamLeaders = GetTeamLeadersDataTable();
                    foreach (DataRow dr in dt.Rows)
                    {
                        if (!dr.IsNull("EmpNo"))
                        {
                            string? empNo = dr.Field<string>("EmpNo");
                            if (empNo != null)
                            {
                                dr["IsTeamLeader"] = 0;
                                DataRow[] rows = dtTeamLeaders.Select($"EmpNo={empNo}");
                                if (rows.Length == 1)
                                {
                                    dr["IsTeamLeader"] = 1;
                                }

                                dr["IsAboveDeputyManager"] = IsAboveDeputyManager(empNo);
                            }
                        }
                    }
                }

                if (dt != null && dt.Rows.Count > 0)
                {
                    list = CompositeTeamData(dt);
                    lock (_cacheLock)
                    {
                        _cache.Set(cacheName, list, CachePolicy);
                    }
                }
            }
            return list;
        }

        /// <summary>
        /// 是否為副理以上
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>副理以上傳回<c>true</c>，否則傳回<c>false</c></returns>
        public bool IsAboveDeputyManager(string empNo)
        {
            bool isAboveDeputyManager = false;
            DataTable dt = GetAboveDeputyManagerDataTable();
            DataRow[] drs = dt.Select($"EmpNo='{empNo}'");
            if (drs.Length > 0) { isAboveDeputyManager = true; }
            return isAboveDeputyManager;
        }

        /// <summary>
        /// 是否為此部門的副理以上
        /// </summary>
        /// <param name="deptNo">部門編號</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>副理以上傳回<c>true</c>，否則傳回<c>false</c></returns>
        public bool IsAboveDeputyManager(int deptNo, string empNo)
        {
            bool isAboveDeputyManager = false;
            DataTable dt = GetAboveDeputyManagerDataTable();
            DataRow[] drs = dt.Select($"EmpNo='{empNo}' AND DeptNo={deptNo}");
            if (drs.Length > 0) 
            {
                isAboveDeputyManager = true;
            }
            return isAboveDeputyManager;
        }
    }
}
