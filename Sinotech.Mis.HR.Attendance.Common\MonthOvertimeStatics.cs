﻿namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 月加班統計
    /// </summary>
    public class MonthOvertimeStatics
    {
        /// <summary>
        /// 西元年
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// 月份
        /// </summary>
        public int Month { get; set; }

        /// <summary>
        /// 月份名，例如：3月。
        /// </summary>
        public string MonthName { get; set; } = "";

        /// <summary>
        /// 本月份已核可加班時數，只包括 InOvertime = true
        /// </summary>
        public int ApprovedHours { get; set; } = 0;

        /// <summary>
        /// 本月份未核可加班時數，只包括 InOvertime = true
        /// </summary>
        public int UnderApprovalHours { get; set; } = 0;

        /// <summary>
        /// 本月份加班時數，只包括 InOvertime = true ，等同於 ApprovedHours + UnderApprovalHours
        /// </summary>
        public int TotalHours { get; set; } = 0;
    }
}
