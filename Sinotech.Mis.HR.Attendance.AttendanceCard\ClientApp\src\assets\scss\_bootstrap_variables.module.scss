@use "sass:map";
@use './bootstrap_variables' as bootstrap;

:export {
  gridBreakpointsXs: map.get(bootstrap.$grid-breakpoints, "xs");
  gridBreakpointsSm: map.get(bootstrap.$grid-breakpoints, "sm");
  gridBreakpointsMd: map.get(bootstrap.$grid-breakpoints, "md");
  gridBreakpointsLg: map.get(bootstrap.$grid-breakpoints, "lg");
  gridBreakpointsXl: map.get(bootstrap.$grid-breakpoints, "xl");
  gridBreakpointsXxl: map.get(bootstrap.$grid-breakpoints, "xxl");
  blue: bootstrap.$blue;
  blue100: bootstrap.$blue-100;
  blue200: bootstrap.$blue-200;
  blue300: bootstrap.$blue-300;
  blue400: bootstrap.$blue-400;
  blue500: bootstrap.$blue-500;
  blue600: bootstrap.$blue-600;
  blue700: bootstrap.$blue-700;
  blue800: bootstrap.$blue-800;
  blue900: bootstrap.$blue-900;
}