﻿using Sinotech.Mis.Helpers;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.DataAccess.Interfaces;
using Sinotech.Mis.HR.Attendance.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;


namespace Sinotech.Mis.HR.Attendance.DataAccess.Ado
{
    public class A1CardDao : IA1CardDao
    {

        /// <summary>
        /// The connection string
        /// </summary>
        private readonly string _connectionString;

        /// <summary>Initializes a new instance of the <see cref="AttendanceDao" /> class.</summary>
        /// <param name="connectionString">The connection string.</param>
        public A1CardDao(string connectionString)
        {
            _connectionString = connectionString;
        }

        /// <summary>
        /// 取得 A1Cards
        /// </summary>
        /// <param name="formUID"></param>
        /// <returns></returns>
        public DataTable GetA1Cards(Guid formUID)
        {
            string sql = @"SELECT * FROM A1Card WHERE FormUID=@FormUID";
            SqlParameter parameter = new SqlParameter("@FormUID", SqlDbType.UniqueIdentifier);
            parameter.Value = formUID;
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameter);
            return dt;
        }

        /// <summary>
        /// 依填卡日期區間取得所有旬卡
        /// </summary>
        /// <param name="startDate">最早填卡日期</param>
        /// <param name="endDate">最晚填卡日期</param>
        /// <returns>日期區間內所有旬卡</returns>
        public DataTable GetA1Cards(DateTime startDate, DateTime endDate)
        {
            string sql = @"SELECT * FROM A1Card WHERE A1_WDate >= @StartDate AND A1_WDate <= @EndDate ORDER BY ID, A1_SERIALNO;";
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            return dt;
        }

        /// <summary>
        /// 依填卡日期區間取得某計畫所有旬卡
        /// </summary>
        /// <param name="startDate">最早填卡日期</param>
        /// <param name="endDate">最晚填卡日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>日期區間內所有旬卡</returns>
        public DataTable GetA1Cards(DateTime startDate, DateTime endDate, string projNo)
        {
            string sql = @"SELECT * FROM A1Card WHERE A1_WDate >= @StartDate AND A1_WDate <= @EndDate 
            AND A1_PROJNO like @ProjNo ORDER BY ID, A1_SERIALNO;";
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);
            SqlParameter parameterProjNo = new SqlParameter("@ProjNo", SqlDbType.VarChar, 10); //做 Like 查詢，必須放寬
            parameterProjNo.Value = $"%{projNo}%";
            parameters.Add(parameterProjNo);
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            return dt;
        }

        /// <summary>
        /// 依填卡日期區間取得所有表單與旬卡
        /// </summary>
        /// <param name="startDate">最早填卡日期</param>
        /// <param name="endDate">最晚填卡日期</param>
        /// <returns>日期區間內某計畫所有表單及旬卡</returns>
        public DataTable GetA1CardsForms(DateTime startDate, DateTime endDate)
        {
            string sql = @"SELECT A1CARD.ID,A1CARD.FormUID,A1CARD.A1_EMPNO,A1CARD.A1_YYMM,A1CARD.A1_NN,A1CARD.A1_PROJNO,
 FormFlow.ID AS FormFlowID,FormFlow.FlowName,FormFlow.Step,A1CARD.A1_ITEMNO,A1CARD.A1_EXPNO,
 A1CARD.A1_DDHH,A1CARD.A1_HOUR,A1CARD.A1_WYYMMDD,A1CARD.A1_AYYMMDD,A1CARD.A1_STATUS,
 A1CARD.A1_SHEETNO,A1CARD.A1_SERIALNO,A1CARD.A1_SOURCE,A1CARD.A1_WDate,A1CARD.A1_ADate,
 A1CARD.UpdatedEmpNo,A1CARD.UpdatedName,A1CARD.UpdatedTime,A1CARD.UpdatedIP,A1CARD.UpdatedHost,
 A1CARD.ValidFrom,A1CARD.ValidTo,FormFlow.FlowUID,FormFlow.RecipientEmpNo,FormFlow.RecipientName,
 FormFlow.RecipientDeptNo,FormFlow.RecipientDeptSName,FormFlow.RecipientTeamID,
 FormFlow.RecipientTeamCName,FormFlow.ApproverEmpNo,FormFlow.ApproverName,FormFlow.ApproverDeptNo,
 FormFlow.IsNotification,FormFlow.FlowStatus,FormFlowStatus.Name AS FlowStatusName,
 FormFlow.IsAgentApprove,FormFlow.ApproveHost,FormFlow.ApproveIP,FormFlow.ApproveTime,
 FormFlow.ApproverTeamCName,FormFlow.ApproverTeamID,FormFlow.ApproverDeptSName,
 Form.FormUID AS FormFormUID,Form.FormID,Form.FormSubject,Form.FormNo,Form.FormInfo,Form.EmpNo,
 Form.EmpName,Form.DeptNo,Form.DeptSName,Form.TeamID,Form.TeamCName,Form.RankNo,Form.RankName,
 Form.JobNo,Form.JobName,Form.ContentStartTime,Form.ContentEndTime,Form.CreatedEmpNo,
 Form.CreatedName,Form.FilledTime,Form.CreatedTime,Form.CreatedIP,Form.CreatedHost,Form.AddedSigner,
 Form.StartTime,Form.EndTime,Form.FormStatus,Form.TotalSteps,Form.CurrentStep,
 Form.UpdatedTime AS FormUpdatedTime,Form.UpdatedIP AS FormUpdatedIP,
 Form.UpdatedHost AS FormUpdatedHost,Form.UpdatedName AS FormUpdatedName,
 Form.UpdatedEmpNo AS FormUpdatedEmpNo,Form.ID AS FormIntID,FormAttachment.EncodedFileName,
 FormAttachment.OriginalFileName,FormAttachment.FileDirectory,FormAttachment.ID AS AttachmentID,
 FormStatus.Name AS FormStatusName
FROM A1CARD INNER JOIN
 Form ON A1CARD.FormUID = Form.FormUID LEFT JOIN
 FormFlow ON Form.FormUID = FormFlow.FormUID LEFT JOIN
 FormFlowStatus ON FormFlow.FlowStatus = FormFlowStatus.ID INNER JOIN
 FormStatus ON A1CARD.A1_STATUS = FormStatus.ID AND Form.FormStatus = FormStatus.ID LEFT  JOIN
 FormAttachment ON Form.FormUID = FormAttachment.FormUID
WHERE (CONVERT(DATE,A1CARD.A1_WDate) >= CONVERT(DATE,@StartDate)) AND (CONVERT(DATE,A1CARD.A1_WDate) <= CONVERT(DATE,@EndDate))
ORDER BY A1CARD.ID,A1CARD.A1_SERIALNO,FormFlow.Step;";
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            return dt;
        }

        /// <summary>
        /// 依填卡日期區間取得某計畫所有表單及旬卡
        /// </summary>
        /// <param name="startDate">最早填卡日期</param>
        /// <param name="endDate">最晚填卡日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>日期區間內某計畫所有表單及旬卡</returns>
        public DataTable GetA1CardsForms(DateTime startDate, DateTime endDate, string projNo)
        {
            string sql = @"SELECT A1CARD.ID,A1CARD.FormUID,A1CARD.A1_EMPNO,A1CARD.A1_YYMM,A1CARD.A1_NN,A1CARD.A1_PROJNO,
 FormFlow.ID AS FormFlowID,FormFlow.FlowName,FormFlow.Step,A1CARD.A1_ITEMNO,A1CARD.A1_EXPNO,
 A1CARD.A1_DDHH,A1CARD.A1_HOUR,A1CARD.A1_WYYMMDD,A1CARD.A1_AYYMMDD,A1CARD.A1_STATUS,
 A1CARD.A1_SHEETNO,A1CARD.A1_SERIALNO,A1CARD.A1_SOURCE,A1CARD.A1_WDate,A1CARD.A1_ADate,
 A1CARD.UpdatedEmpNo,A1CARD.UpdatedName,A1CARD.UpdatedTime,A1CARD.UpdatedIP,A1CARD.UpdatedHost,
 A1CARD.ValidFrom,A1CARD.ValidTo,FormFlow.FlowUID,FormFlow.RecipientEmpNo,FormFlow.RecipientName,
 FormFlow.RecipientDeptNo,FormFlow.RecipientDeptSName,FormFlow.RecipientTeamID,
 FormFlow.RecipientTeamCName,FormFlow.ApproverEmpNo,FormFlow.ApproverName,FormFlow.ApproverDeptNo,
 FormFlow.IsNotification,FormFlow.FlowStatus,FormFlowStatus.Name AS FlowStatusName,
 FormFlow.IsAgentApprove,FormFlow.ApproveHost,FormFlow.ApproveIP,FormFlow.ApproveTime,
 FormFlow.ApproverTeamCName,FormFlow.ApproverTeamID,FormFlow.ApproverDeptSName,
 Form.FormUID AS FormFormUID,Form.FormID,Form.FormSubject,Form.FormNo,Form.FormInfo,Form.EmpNo,
 Form.EmpName,Form.DeptNo,Form.DeptSName,Form.TeamID,Form.TeamCName,Form.RankNo,Form.RankName,
 Form.JobNo,Form.JobName,Form.ContentStartTime,Form.ContentEndTime,Form.CreatedEmpNo,
 Form.CreatedName,Form.FilledTime,Form.CreatedTime,Form.CreatedIP,Form.CreatedHost,Form.AddedSigner,
 Form.StartTime,Form.EndTime,Form.FormStatus,Form.TotalSteps,Form.CurrentStep,
 Form.UpdatedTime AS FormUpdatedTime,Form.UpdatedIP AS FormUpdatedIP,
 Form.UpdatedHost AS FormUpdatedHost,Form.UpdatedName AS FormUpdatedName,
 Form.UpdatedEmpNo AS FormUpdatedEmpNo,Form.ID AS FormIntID,FormAttachment.EncodedFileName,
 FormAttachment.OriginalFileName,FormAttachment.FileDirectory,FormAttachment.ID AS AttachmentID,
 FormStatus.Name AS FormStatusName
FROM A1CARD INNER JOIN
 Form ON A1CARD.FormUID = Form.FormUID LEFT JOIN
 FormFlow ON Form.FormUID = FormFlow.FormUID LEFT JOIN
 FormFlowStatus ON FormFlow.FlowStatus = FormFlowStatus.ID INNER JOIN
 FormStatus ON A1CARD.A1_STATUS = FormStatus.ID AND Form.FormStatus = FormStatus.ID LEFT JOIN
 FormAttachment ON Form.FormUID = FormAttachment.FormUID
 WHERE (CONVERT(DATE,A1CARD.A1_WDate) >= CONVERT(DATE,@StartDate)) AND (CONVERT(DATE,A1CARD.A1_WDate) <= CONVERT(DATE,@EndDate)) 
 AND A1_PROJNO like @ProjNo
 ORDER BY A1CARD.ID,A1CARD.A1_SERIALNO,FormFlow.Step;";
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            parameterStartDate.Value = startDate;
            parameters.Add(parameterStartDate);
            SqlParameter parameterEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            parameterEndDate.Value = endDate;
            parameters.Add(parameterEndDate);
            SqlParameter parameterProjNo = new SqlParameter("@ProjNo", SqlDbType.VarChar, 10);
            parameterProjNo.Value = $"%{projNo}%";
            parameters.Add(parameterProjNo);
            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);
            return dt;
        }

        /// <summary>
        /// 取得 表單關係人 某月份已填正常工時單，依表單年月為準，非填表時間
        /// </summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="date">日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns>已填表單</returns>
        public DataTable GetA1CardMonth(string empNo, DateTime date, int? status = null)
        {
            List<SqlParameter> parameters = new List<SqlParameter>();
            string strSql = @"SELECT * FROM A1CARD WHERE A1_YYMM=@YYYMM AND A1_EMPNO=@EmpNo";

            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            parameterEmpNo.Value = empNo;
            parameters.Add(parameterEmpNo);

            string yyyMM = CardUtility.RocChineseYYYMM(date);
            SqlParameter parameterYYYMM = new SqlParameter("@YYYMM", SqlDbType.VarChar);
            parameterYYYMM.Value = yyyMM;
            parameters.Add(parameterYYYMM);
            if (status != null)
            {
                strSql += @" AND A1_STATUS=@Status";
                SqlParameter parameterStatus = new SqlParameter("@Status", SqlDbType.Int);
                parameterStatus.Value = status;
                parameters.Add(parameterStatus);
            }
            strSql += " ORDER BY A1_YYMM,A1_NN;";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql, parameters);
            return dt;
        }


        /// <summary>
        /// 取得 填表人/表單關係人 某月份已填正常工時單與表單、流程、附件等
        /// </summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <param name="status">表單狀態</param>
        /// <returns>已填表單</returns>
        public DataTable GetSentFormA1CardYearMonth(string empNo, int year, int month, int? status = null)
        {
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterEmpNo = new SqlParameter("@EmpNo", SqlDbType.VarChar);
            parameterEmpNo.Value = empNo;
            parameters.Add(parameterEmpNo);
            string strSql = @"SELECT A1CARD.ID,A1CARD.FormUID,A1CARD.A1_EMPNO,A1CARD.A1_YYMM,A1CARD.A1_NN,A1CARD.A1_PROJNO,
 FormFlow.ID AS FormFlowID,FormFlow.FlowName,FormFlow.Step,A1CARD.A1_ITEMNO,A1CARD.A1_EXPNO,
 A1CARD.A1_DDHH,A1CARD.A1_HOUR,A1CARD.A1_WYYMMDD,A1CARD.A1_AYYMMDD,A1CARD.A1_STATUS,
 A1CARD.A1_SHEETNO,A1CARD.A1_SERIALNO,A1CARD.A1_SOURCE,A1CARD.A1_WDate,A1CARD.A1_ADate,
 A1CARD.UpdatedEmpNo,A1CARD.UpdatedName,A1CARD.UpdatedTime,A1CARD.UpdatedIP,A1CARD.UpdatedHost,
 A1CARD.ValidFrom,A1CARD.ValidTo,FormFlow.FlowUID,FormFlow.RecipientEmpNo,FormFlow.RecipientName,
 FormFlow.RecipientDeptNo,FormFlow.RecipientDeptSName,FormFlow.RecipientTeamID,
 FormFlow.RecipientTeamCName,FormFlow.ApproverEmpNo,FormFlow.ApproverName,FormFlow.ApproverDeptNo,
 FormFlow.IsNotification,FormFlow.FlowStatus,FormFlowStatus.Name AS FlowStatusName,
 FormFlow.IsAgentApprove,FormFlow.ApproveHost,FormFlow.ApproveIP,FormFlow.ApproveTime,
 FormFlow.ApproverTeamCName,FormFlow.ApproverTeamID,FormFlow.ApproverDeptSName,
 Form.FormUID AS FormFormUID,Form.FormID,Form.FormSubject,Form.FormNo,Form.FormInfo,Form.EmpNo,
 Form.EmpName,Form.DeptNo,Form.DeptSName,Form.TeamID,Form.TeamCName,Form.RankNo,Form.RankName,
 Form.JobNo,Form.JobName,Form.ContentStartTime,Form.ContentEndTime,Form.CreatedEmpNo,
 Form.CreatedName,Form.FilledTime,Form.CreatedTime,Form.CreatedIP,Form.CreatedHost,Form.AddedSigner,
 Form.StartTime,Form.EndTime,Form.FormStatus,Form.TotalSteps,Form.CurrentStep,
 Form.UpdatedTime AS FormUpdatedTime,Form.UpdatedIP AS FormUpdatedIP,
 Form.UpdatedHost AS FormUpdatedHost,Form.UpdatedName AS FormUpdatedName,
 Form.UpdatedEmpNo AS FormUpdatedEmpNo,Form.ID AS FormIntID,FormAttachment.EncodedFileName,
 FormAttachment.OriginalFileName,FormAttachment.FileDirectory,FormAttachment.ID AS AttachmentID,
 FormStatus.Name AS FormStatusName FROM A1CARD INNER JOIN Form ON A1CARD.FormUID = Form.FormUID 
 LEFT JOIN FormFlow ON Form.FormUID = FormFlow.FormUID 
 LEFT JOIN FormFlowStatus ON FormFlow.FlowStatus = FormFlowStatus.ID 
 LEFT JOIN FormStatus ON A1CARD.A1_STATUS = FormStatus.ID AND Form.FormStatus = FormStatus.ID 
 LEFT OUTER JOIN FormAttachment ON Form.FormUID = FormAttachment.FormUID
 WHERE ((Form.CreatedEmpNo = @EmpNo) OR (Form.EmpNo = @EmpNo)) AND (Form.FormInfo LIKE @FormInfo)";

            int rocYear = CardUtility.RocChineseYear(year);
            string formInfo = $"{rocYear}_{month.ToString("00")}%";
            SqlParameter parameterFormInfo = new SqlParameter("@FormInfo", SqlDbType.VarChar, 50);
            parameterFormInfo.Value = formInfo;
            parameters.Add(parameterFormInfo);
            if (status != null)
            {
                strSql += @" AND Form.FormStatus=@Status";
                SqlParameter parameterStatus = new SqlParameter("@Status", SqlDbType.Int);
                parameterStatus.Value = status;
                parameters.Add(parameterStatus);
            }
            strSql += " ORDER BY FormInfo,Step;";
            DataTable dt = SqlHelper.GetDataTable(_connectionString, strSql, parameters);
            return dt;
        }

        /// <summary>
        /// 檢查某員工是否已填某旬之有效（簽核中及同意）正常工作卡
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="chineseYearMonth">民國年月(yyymm)</param>
        /// <param name="tenDays">旬別(1:上旬、2:中旬、3:下旬)</param>
        public DataTable IsFilledA1Card(string empNo, string chineseYearMonth, char tenDays)
        {
            string sql = @"SELECT FormUID,A1_SHEETNO,A1_STATUS FROM A1CARD WHERE A1_EMPNO=@EmpNo AND A1_YYMM=@ChineseYearMonth AND A1_NN=@TenDays AND A1_STATUS IN (1, 2)";
            SqlParameter[] parameters = new SqlParameter[3];
            parameters[0] = new SqlParameter("@EmpNo", SqlDbType.VarChar)
            {
                Value = empNo
            };
            parameters[1] = new SqlParameter("@ChineseYearMonth", SqlDbType.Char, 5)
            {
                Value = chineseYearMonth
            };
            parameters[2] = new SqlParameter("@TenDays", SqlDbType.Char, 1)
            {
                Value = tenDays
            };

            DataTable dt = SqlHelper.GetDataTable(_connectionString, sql, parameters);

            return dt;
        }

        /// <summary>
        /// 更新A1Card計畫編號
        /// </summary>
        /// <param name="a1Card"></param>
        /// <returns></returns>
        public bool UpdateA1CardProjectNo(A1CardDto a1Card)
        {
            bool status = false;
            string sql = @"UPDATE A1Card SET A1_DDHH=@A1_DDHH,A1_HOUR=@A1_HOUR,UpdatedEmpNo=@UpdatedEmpNo,
 UpdatedName=@UpdatedName, UpdatedIP=@UpdatedIP, UpdatedHost=@UpdatedHost, UpdatedTime=@UpdatedTime
 WHERE ID=@ID;";
            List<SqlParameter> parameters = new List<SqlParameter>();
            SqlParameter parameterID = new SqlParameter("@ID", SqlDbType.Int);
            parameterID.Value = a1Card.ID;
            parameters.Add(parameterID);
            SqlParameter parameterA1_DDHH = new SqlParameter("@A1_DDHH", SqlDbType.Char, 12);
            parameterA1_DDHH.Value = a1Card.A1_DDHH;
            parameters.Add(parameterA1_DDHH);
            SqlParameter parameterA1_HOUR = new SqlParameter("@A1_HOUR", SqlDbType.Int);
            parameterA1_HOUR.Value = a1Card.A1_HOUR;
            parameters.Add(parameterA1_HOUR);
            SqlParameter parameterUpdatedEmpNo = new SqlParameter("@UpdatedEmpNo", SqlDbType.VarChar, 10);
            if (!string.IsNullOrWhiteSpace(a1Card.UpdatedEmpNo))
            {
                parameterUpdatedEmpNo.Value = a1Card.UpdatedEmpNo;
            }
            else
            {
                parameterUpdatedEmpNo.Value = DBNull.Value;
            }
            parameters.Add(parameterUpdatedEmpNo);

            SqlParameter parameterUpdatedName = new SqlParameter("@UpdatedName", SqlDbType.NVarChar, 20);
            if (!string.IsNullOrWhiteSpace(a1Card.UpdatedName))
            {
                parameterUpdatedName.Value = a1Card.UpdatedName;
            }
            else
            {
                parameterUpdatedName.Value = DBNull.Value;
            }
            parameters.Add(parameterUpdatedName);
            SqlParameter parameterUpdatedIP = new SqlParameter("@UpdatedIP", SqlDbType.NVarChar, 50);
            if (!string.IsNullOrWhiteSpace(a1Card.UpdatedIP))
            {
                parameterUpdatedIP.Value = a1Card.UpdatedIP;
            }
            else
            {
                parameterUpdatedIP.Value = DBNull.Value;
            }
            parameters.Add(parameterUpdatedIP);
            SqlParameter parameterUpdatedHost = new SqlParameter("@UpdatedHost", SqlDbType.NVarChar, 256);
            if (!string.IsNullOrWhiteSpace(a1Card.UpdatedHost))
            {
                parameterUpdatedHost.Value = a1Card.UpdatedHost;
            }
            else
            {
                parameterUpdatedHost.Value = DBNull.Value;
            }
            parameters.Add(parameterUpdatedHost);
            SqlParameter parameterUpdatedTime = new SqlParameter("@UpdatedTime", SqlDbType.DateTime);
            parameterUpdatedTime.Value = a1Card.UpdatedTime;
            parameters.Add(parameterUpdatedTime);
            int affectedRows = SqlHelper.ExecuteSqlNonQuery(_connectionString, sql, parameters);
            status = affectedRows == 1;
            return status;
        }
    }
}
