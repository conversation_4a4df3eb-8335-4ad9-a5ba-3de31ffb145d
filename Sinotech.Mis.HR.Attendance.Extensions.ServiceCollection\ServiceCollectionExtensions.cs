﻿// ServiceProvider 擴充方法
// 2022/05/25
// By 林志偉

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Sinotech.Mis.Extensions.Configuration;
using Sinotech.Mis.HR.Attendance.BusinessLogic;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.DataAccess.Ado;
using Sinotech.Mis.HR.Attendance.DataAccess.Interfaces;
using Sinotech.Mis.Utilities.DataAccess.Ado;
using Sinotech.Mis.Utilities.DataAccess.Interfaces;
using System.Diagnostics.CodeAnalysis;

namespace Sinotech.Mis.HR.Attendance.Extensions.ServiceCollection
{
    [ExcludeFromCodeCoverage]
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// 注入Attendance需要的系統元件
        /// </summary>
        /// <param name="services">IServiceCollection介面</param>
        /// <param name="config"></param>
        /// <returns></returns>
        [ExcludeFromCodeCoverage]
        public static IServiceCollection AddAttendanceBusinessObjects(this IServiceCollection services, IConfiguration config)
        {
            services.AddSingleton<IProjectDao, ProjectDao>
                (sp => new ProjectDao(config.GetSecuredConnectionString("Project")));
            services.AddSingleton<ProjectBo>();
            services.AddSingleton<IProjectBo, ProjectBo>();

            services.AddSingleton<IEmployeeDao, EmployeeDao>
                (sp => new EmployeeDao(config.GetSecuredConnectionString("Employee")));
            services.AddSingleton<EmployeeBo>();
            services.AddSingleton<Common.IEmployeeBo, EmployeeBo>();

            services.AddSingleton<IDepartmentDao, DepartmentDao>
                (sp => new DepartmentDao(config.GetSecuredConnectionString("Department")));
            services.AddSingleton<DepartmentBo>();
            services.AddSingleton<IDepartmentBo, DepartmentBo>();

            services.AddSingleton<ISinoSignDao, SinoSignDao>
                (sp => new SinoSignDao(config.GetSecuredConnectionString("SinoSign")));
            services.AddSingleton<SinoSignBo>();
            services.AddSingleton<ISinoSignBo, SinoSignBo>();

            services.AddSingleton<IWorkdayDao, WorkdayDao>
                (sp => new WorkdayDao(config.GetSecuredConnectionString("Workday")));
            services.AddSingleton<WorkdayBo>();
            services.AddSingleton<IWorkdayBo, WorkdayBo>();

            services.AddSingleton<IAttendanceDao, AttendanceDao>
                (sp => new AttendanceDao(config.GetSecuredConnectionString("Attendance")));
            services.AddSingleton<AttendanceBo>();
            services.AddSingleton<IAttendanceBo, AttendanceBo>();

            services.AddSingleton<IA1CardDao, A1CardDao>
                (sp => new A1CardDao(config.GetSecuredConnectionString("Attendance")));
            services.AddSingleton<A1CardBo>();
            services.AddSingleton<IA1CardBo, A1CardBo>();

            services.AddSingleton<IB1CardDao, B1CardDao>
                (sp => new B1CardDao(config.GetSecuredConnectionString("Attendance")));

            services.AddSingleton<B1CardBo>();
            services.AddSingleton<IB1CardBo, B1CardBo>();

            services.AddSingleton<IB1CardAppDao, B1CardAppDao>
                (sp => new B1CardAppDao(config.GetSecuredConnectionString("Attendance")));
            services.AddSingleton<B1CardAppBo>();
            services.AddSingleton<IB1CardAppBo, B1CardAppBo>();

            services.AddSingleton<IC1CardDao, C1CardDao>
                (sp => new C1CardDao(config.GetSecuredConnectionString("Attendance")));
            services.AddSingleton<C1CardBo>();
            services.AddSingleton<Common.IC1CardBo, C1CardBo>();

            services.AddSingleton<IAccountDao, AccountDao>
                (sp => new AccountDao(config.GetSecuredConnectionString("Attendance")));

            services.AddSingleton<CardBoFactory>();
            services.AddSingleton<ICardBoFactory, CardBoFactory>();

            services.AddSingleton<FormTypeBo>();
            services.AddSingleton<IFormTypeBo, FormTypeBo>();

            services.AddSingleton<FormFlowBo>();
            services.AddSingleton<IFormFlowBo, FormFlowBo>();

            services.AddSingleton<NotificationMail>();
            services.AddSingleton<INotificationMail, NotificationMail>();

            services.AddSingleton<FormBo>();
            services.AddSingleton<IFormBo, FormBo>();

            services.AddSingleton<OvertimeBo>();
            services.AddSingleton<IOvertimeBo, OvertimeBo>();

            services.AddSingleton<AccountBo>();
            services.AddSingleton<IAccountBo, AccountBo>();

            services.AddSingleton<CardChecker>();
            services.AddSingleton<ICardChecker, CardChecker>();

            return services;
        }
    }
}

