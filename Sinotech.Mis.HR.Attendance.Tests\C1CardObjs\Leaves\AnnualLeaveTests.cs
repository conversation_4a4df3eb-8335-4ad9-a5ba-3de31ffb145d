﻿using FakeItEasy;
using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves.Tests
{
    /// <summary>
    /// 年假測試類別
    /// 從 nUnit 轉換為 xUnit 版本
    /// </summary>
    public class AnnualLeaveTests : TestC1CardBase
    {
        /// <summary>
        /// 初始化年假測試資料
        /// 取代 nUnit 的 [SetUp] 方法
        /// </summary>
        public AnnualLeaveTests()
        {
            #region Prepare Data

            _c1Card.LeaveNumber = LeaveKindEnum.AnnualLeave;

            #endregion
        }

        /// <summary>
        /// 測試是否可以申請此假別
        /// </summary>
        [Theory]
        [InlineData(false, 110, AnnualLeave.CodeLeaveRecordNotCreateYet)]
        [InlineData(true, 110, AnnualLeave.CodePostponedLeaveExists)]
        [InlineData(true, 0, AnnualLeave.CodeOk)]
        public void TestCanTakeThisLeave(bool ifLeaveRecordExists, int remainingPostponeLeaveHours, int returnCode)
        {
            A.CallTo(() => _c1CardBo.IfLeaveRecordExists(A<string>.Ignored,
                A<DateTime>.Ignored, A<DateTime>.Ignored)).Returns(ifLeaveRecordExists);
            A.CallTo(() => _c1CardBo.GetPostponedLeaveRemainingHours(A<string>.Ignored, A<DateTime>.Ignored)).Returns(remainingPostponeLeaveHours);

            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CanTakeThisLeave();

            Assert.Equal(returnCode, result.Code);
        }

        /// <summary>
        /// 測試是否超過配額
        /// </summary>
        [Theory]
        [InlineData(10, 110, AnnualLeave.CodeOk)]
        [InlineData(110, 110, AnnualLeave.CodeOk)]
        [InlineData(115, 110, AnnualLeave.CodeExceedQuota)]
        public void TestExceedQuota(int applyHours, int annualLeaveRemainingHour, int returnCode)
        {
            A.CallTo(() => _c1CardBo.CalculateEmpTakeLeaveWorkingHours(A<String>.Ignored, A<DateTime>.Ignored,
                A<DateTime>.Ignored)).Returns(applyHours);
            A.CallTo(() => _c1CardBo.GetAnnualLeaveRemainingHours(A<String>.Ignored, A<DateTime>.Ignored)).Returns(annualLeaveRemainingHour);

            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CheckOverPermittedLeaveHours();

            Assert.Equal(returnCode, result.Code);
        }

        /// <summary>
        /// 測試檢查必要欄位
        /// </summary>
        [Fact]
        public void TestCheckRequiredFields()
        {
            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CheckRequiredFields();

            Assert.Equal(AnnualLeave.CodeOk, result.Code);
        }

        /// <summary>
        /// 測試性別限制
        /// </summary>
        [Theory]
        [InlineData(Gender.Female, true)]
        [InlineData(Gender.Male, true)]
        public void TestGender(Gender gender, bool expected)
        {
            var result = AnnualLeave.IsAllowForThisGender(gender);
            Assert.Equal(expected, result);
        }
    }
}
