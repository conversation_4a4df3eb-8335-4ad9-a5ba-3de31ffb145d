﻿using FakeItEasy;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sinotech.Mis.Extensions.Configuration;
using Sinotech.Mis.HR.Attendance.BusinessLogic;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.Tests;
using System;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers.Tests
{
    [ExcludeFromCodeCoverage]
    public class AttendanceControllerTests
    {

        private readonly AttendanceController _controller;
        private readonly IConfiguration _configuration;
        private readonly OvertimeBo _overtimeBo;
        private readonly ILogger<AttendanceController> _logger;
        public AttendanceControllerTests(AttendanceBo attendanceBo, OvertimeBo overtimeBo, ILogger<AttendanceController> logger)
        {
            _overtimeBo = overtimeBo;
            _logger = logger;
            IConfiguration configuration = new ConfigurationBuilder().
                 AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).Build();
            _configuration = configuration;
            _controller = new AttendanceController(attendanceBo, overtimeBo, configuration, logger);
            string ConnectionStringAttendance = configuration.GetSecuredConnectionString("Attendance");
            TestHelper.ClearData(ConnectionStringAttendance);
        }

        [Theory]
        [InlineData(2023, 11, 4, "0395", "07:04, 17:08")]
        public void GetDayInTimeTest(int year, int month, int day, string empNo, string expected)
        {
            DateTime date = new DateTime(year, month, day, 0, 0, 0, DateTimeKind.Local);
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo(empNo);
            string strDayInTime = _controller.GetDayInTime(date, empNo);
            Assert.Equal(expected, strDayInTime);
        }



        [Theory]
        [InlineData(2011, 1, 1, "0319", true)]
        [InlineData(2011, 2, 1, "0273", true)]
        [InlineData(2022, 10, 21, "0349", false)]
        [InlineData(2023, 2, 10, "0395", false)]
        public void GetMonthAttendanceTest(int year, int month, int day, string empNo, bool isEmpty)
        {
            DateTime date = new DateTime(year, month, day, 0, 0, 0, DateTimeKind.Local);
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo(empNo);
            string strDayInTime = _controller.GetMonthAttendance(date, empNo);
            if (isEmpty)
            {
                Assert.Equal("[]", strDayInTime);
            }
            else
            {
                Assert.NotEqual("[]", strDayInTime);
            }
        }

        [Theory]
        [InlineData(2011, 1, 1, "0000", false)]
        [InlineData(2011, 2, 1, "0001", false)]
        [InlineData(2022, 10, 21, "0349", false)]
        [InlineData(2023, 2, 10, "0395", false)]
        public void GetMonthEmployeeLeaves_Empty_Test(int year, int month, int day, string empNo, bool isEmpty)
        {
            DateTime date = new DateTime(year, month, day, 0, 0, 0, DateTimeKind.Local);
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo(empNo);
            string leaves = _controller.GetMonthEmployeeLeaves(date, empNo);
            Assert.NotEmpty(leaves);
            if (isEmpty)
            {
                Assert.Equal("[]", leaves);
            }
            else
            {
                Assert.NotEqual("[]", leaves);
            }
        }

        [Theory]
        [InlineData(2011, 1, 1, "0000", true)]
        [InlineData(2011, 1, 1, "0319", true)]
        [InlineData(2011, 2, 1, "0273", true)]
        [InlineData(2022, 10, 21, "0349", true)]
        [InlineData(2023, 2, 10, "0395", true)]
        public void GetMonthEmployeeLeavesTest(int year, int month, int day, string empNo, bool expected)
        {
            DateTime date = new DateTime(year, month, day, 0, 0, 0, DateTimeKind.Local);
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo(empNo);
            string leaves = _controller.GetMonthEmployeeLeaves(date, empNo);
            if (expected)
            {
                Assert.NotEmpty(leaves);
            }
            else
            {
                Assert.Empty(leaves);
            }

        }



        [Theory]
        [InlineData(2011, 1, 1, "0319", "{}")]
        [InlineData(2011, 2, 1, "0273", "{}")]
        [InlineData(2022, 10, 21, "0349", "{}")]
        [InlineData(2023, 2, 10, "0395", "{}")]
        public void GetOvertimeData_Empty_Test(int year, int month, int day, string empNo, string expected)
        {
            DateTime date = new DateTime(year, month, day, 0, 0, 0, DateTimeKind.Local);
            string overtimeData = _controller.GetOvertimeData(date, empNo);
            Assert.Equal(expected, overtimeData);
        }

        [Theory]
        [InlineData(2011, 1, 1, "0000", true)]
        [InlineData(2011, 1, 1, "0319", true)]
        [InlineData(2011, 2, 1, "0273", true)]
        [InlineData(2022, 10, 21, "0349", true)]
        [InlineData(2023, 2, 10, "0395", true)]
        public void GetOvertimeDataTest(int year, int month, int day, string empNo, bool expected)
        {
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo(empNo);
            DateTime date = new DateTime(year, month, day, 0, 0, 0, DateTimeKind.Local);
            string overtimeData = _controller.GetOvertimeData(date, empNo);
            if (expected)
            {
                Assert.NotEmpty(overtimeData);
            }
            else
            {
                Assert.Empty(overtimeData);
            }
        }


        [Theory]
        [InlineData("", false)]
        [InlineData("0274", false)]
        [InlineData("0276", false)]
        [InlineData("2268", true)]
        [InlineData("0395", true)]
        [InlineData("0349", true)]
        public void IsAdminTest(string empNo, bool expected)
        {
            bool result = _controller.IsAdmin(empNo);
            Assert.Equal(expected, result);
            _controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo(empNo);
            result = _controller.IsAdmin();
            Assert.Equal(expected, result);
        }

        [Fact]
        public void GetDayInTime_ThrowException()
        {
            IAttendanceBo attendanceBo = A.Fake<IAttendanceBo>();
            A.CallTo(() => attendanceBo.GetDayInTimeString(A<DateTime>.Ignored, A<string>.Ignored)).Throws<Exception>();
            AttendanceController controller = new AttendanceController(attendanceBo, _overtimeBo, _configuration, _logger);
            controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo("2268");
            DateTime dateTime = new DateTime(2022, 11, 4, 0, 0, 0, DateTimeKind.Local);
            string result = controller.GetDayInTime(dateTime, "2268");
            Assert.Empty(result);
        }

    }
}
