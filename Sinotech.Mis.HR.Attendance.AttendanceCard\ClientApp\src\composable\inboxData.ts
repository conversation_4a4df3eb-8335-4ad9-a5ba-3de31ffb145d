import { ref } from 'vue'
import { GET_INBOX_URL } from '../api/appUrl'

/**
 * 待核卡資料
 * @returns 
 */
export function useInboxData() {
  const inboxData = ref<Array<any>>([])

  const onGetInboxData = async (empNo: string, signal: AbortSignal): Promise<void> => {
    try {
      const res: Response = await fetch(GET_INBOX_URL + '/' + empNo, {
        method: 'GET',
        signal: signal
      })
      if (!res.ok) {
        throw new Error(res.status.toString())
      }
      inboxData.value = await res.json()
    } catch (err: unknown) {
      throw err
    }
  }

  return { inboxData, onGetInboxData }
}