﻿using Sinotech.Mis.Common;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 加班卡細項帶計畫
    /// </summary>
    public class B1CardDetailProject : B1CardDetail
    {
        /// <summary>
        /// 預設建構元
        /// </summary>
        public B1CardDetailProject() { }

        /// <summary>
        /// 建構元
        /// </summary>
        /// <param name="b1CardDetail"></param>
        public B1CardDetailProject(B1CardDetail b1CardDetail)
        {
            if (b1CardDetail.ID != null)
            {
                ID = b1CardDetail.ID;
            }
            Project = b1CardDetail.Project;
            StartTime = b1CardDetail.StartTime;
            EndTime = b1CardDetail.EndTime;
            Hour = b1CardDetail.Hour;
            HourLeft = b1CardDetail.HourLeft;
            B1_CODE = b1CardDetail.B1_CODE;
            SerialNo = b1CardDetail.SerialNo;
        }

        /// <summary>
        /// 計畫詳細資訊
        /// </summary>
        public Project ProjectDetail { get; set; } = new Project();
    }
}
