﻿using Sinotech.Mis.Common;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Collections.Generic;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.PositionType
{
    public class B1CardDataProvider : IB1CardDataProvider
    {
        public string EmployeeNumber { get; }
        public DateTime OvertimeDate { get; }

        public B1CardParameters _parameters;
        /// <summary>
        /// 建構式
        /// </summary>
        /// <param name="employeeNumber">員工編號</param>
        /// <param name="overtimeDate">加班日</param>
        /// <param name="parameters">傳入之B1Card參數物件</param>
        public B1CardDataProvider(B1CardParameters parameters)
        {
            _parameters = parameters;
            EmployeeNumber = _parameters.EmployeeDetail.EmpNo;
            OvertimeDate = _parameters.OvertimeDate;

            if (EmployeeNumber != GetB1Card().EmpNo)
            {
                throw new ArgumentException($"{nameof(B1CardDataProvider)}: 不一致的員工編號");
            }
        }

        #region  Data Provider

        public int GetMonthOvertimeHourInQuota()
        {
            // 已經扣除費率 1.0 的時數
            return _parameters.MonthOvertimeStatics.TotalHours;
        }

        public int GetQuarterOvertimeHourInQuota()
        {
            // 已經扣除費率 1.0 的時數
            return _parameters.QuarterlyOvertimeStatics.TotalHours;
        }

        public int GetAppliedOvertimeHourInQuota()
        {
            // 已經扣除費率 1.0 的時數
            return _parameters.DayOvertime.InOvertimeHours;
        }

        public B1CardPositionEnum GetPositionType()
        {
            return _parameters.EmployeePosition;
        }

        public List<Project> GetProjectList()
        {
            return _parameters.Projects;
        }

        public Project? GetProjectInfo(string projectNumber)
        {
            projectNumber = projectNumber.Trim().ToUpper();
            foreach (var p in _parameters.Projects)
            {
                if (projectNumber == p.PrjNo.Trim().ToUpper())
                {
                    return p;
                }
            }
            return null;
        }

        public bool IsSpecialStaff()
        {
            return _parameters.IsSpecialStaff;
        }

        //public bool GetSpecialStaffStatistics(out double allowableMonthWeightedOvertimeHour, out double currentMonthWeightedOvertimeHour)
        //{
        //    allowableMonthWeightedOvertimeHour = _parameters.SpecialStaffAllowedMonthWeightedOvertimeHours;
        //    currentMonthWeightedOvertimeHour = _parameters.CurrentMonthWeightedOvertimeHours;
        //    return _parameters.IsSpecialStaff;
        //}

        public B1Card GetB1Card()
        {
            return _parameters.B1Card;
        }

        public Workday GetOverTimeDateInfo()
        {
            return _parameters.DayDetail;
        }

        public B1CardApp? GetB1CardApp()
        {
            return _parameters.B1CardApp;
        }

        public double GetSpecialStaffAllowedMonthlyWeightedOvertimeHours()
        {
            return _parameters.SpecialStaffAllowedMonthWeightedOvertimeHours;
        }

        public double GetCurrentMonthlyWeightedOvertimeHours()
        {
            return _parameters.CurrentMonthWeightedOvertimeHours;
        }

        public double GetWeightedOvertimeHours()
        {
            return _parameters.DayOvertime.PaidHours;
        }

        public bool GetHasB1CardFilled()
        {
            return _parameters.IsFilledB1Card;
        }

        public bool GetHasAppliedOvertimeWork()
        {
            return _parameters.IsFilledB1CardApp;
        }

        #endregion
    }
}