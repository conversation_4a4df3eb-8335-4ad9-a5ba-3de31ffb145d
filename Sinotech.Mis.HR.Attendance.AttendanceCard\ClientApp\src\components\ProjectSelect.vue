<template>
  <VueSelect
    ref="selectRef"
    label="id"
    :options="projectData"
    :filter="onSearchProjectData"
    :clearable="clearable"
    :modelValue="modelValue"
    :placeholder="placeholder"
    @option:selected="onChange"
    @search="onSearch"
  >
    <template #no-options>
      <span class="text-danger">
        {{ noOptions }}
      </span>
    </template>
    <template #option="option">
      {{ option.id + ' ' + option.name }}
    </template>
    <template #selected-option="option">
      {{ option ? (option.id + ' ' + option.name) : null }}
    </template>
  </VueSelect>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import VueSelect from 'vue-select'
import type { VueSelectInstance } from 'vue-select'
import type { ProjectType } from '../api/appType'
import { onSearchProjectData } from '../api/appFunction'

const props = withDefaults(
  defineProps<{
    projectData: Array<ProjectType>
    clearable: boolean
    modelValue: ProjectType | null
    projectFilter?: Array<ProjectType>
    placeholder: string
  }>(),
  {
    projectData: () => [],
    clearable: false,
    modelValue: () => {
      return {
        id: '',
        name: '',
        startDate: null,
        endDate: null
      }
    },
    projectFilter: () => [],
    placeholder: ''
  }
)
const emits = defineEmits(['update:modelValue', 'delete'])

const selectRef = ref<VueSelectInstance | undefined>()
const noOptions = ref<string>('')

const onChange = (event: ProjectType): void => {
  emits('update:modelValue', {
    id: event.id,
    name: event.name,
    startDate: event.startDate,
    endDate: event.endDate
  })
}

/**
 * 只會變更noOptions的值，不影響搜尋的結果
 * @param search
 */
const onSearch = (search: string): void => {
  const searchResult = onSearchProjectData(props.projectFilter, search)
  if (searchResult.length > 0) {
    noOptions.value = '不可重複'
  } else {
    noOptions.value = '查無可填報計畫'
  }
}

onMounted(() => {
  selectRef.value!.$refs.clearButton.title = ''
  selectRef.value!.$refs.clearButton.addEventListener('click', () => emits('delete'))
})
</script>
<style lang="scss" scoped>
@use '../assets/scss/bootstrap_variables' as bootstrap;
:deep(input::placeholder) {
  color: bootstrap.$gray-400;
}
</style>