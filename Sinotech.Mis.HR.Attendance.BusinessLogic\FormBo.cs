﻿using Ganss.Xss;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sinotech.Mis.Common;
using Sinotech.Mis.Extensions.Configuration;
using Sinotech.Mis.Helpers;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic
{
    /// <summary>
    /// 表單 商業物件
    /// </summary>
    public class FormBo : IFormBo
    {

        private const int HASH_CODE_EXPIRE_SECONDS = 10;

        private static readonly Mutex _mutex = new Mutex();
        private readonly IAttendanceBo _attendanceBo;
        private readonly IConfiguration _configuration;
        private readonly IDepartmentBo _departmentBo;
        private readonly IEmployeeBo _employeeBo;
        private readonly IFormFlowBo _formFlowBo;
        private readonly IFormTypeBo _formTypeBo;

        private string _lastApproveHashCode = string.Empty;
        private DateTime _lastApproveTime = DateTime.MinValue;
        private string _lastClosedWithdrawHashCode = string.Empty;
        private DateTime _lastClosedWithdrawTime = DateTime.MinValue;
        private string _lastMultiApproveHashCode = string.Empty;
        private DateTime _lastMultiApproveTime = DateTime.MinValue;
        private string _lastWithdrawHashCode = string.Empty;
        private DateTime _lastWithdrawTime = DateTime.MinValue;
        private readonly ILogger<FormBo> _logger;
        private readonly INotificationMail _notificationMail;
        private readonly ISinoSignBo _sinoSignBo;
        private readonly bool _IsMultipleVicePresidents = false; // 有2位副執行長
        private readonly string _AdministrativeVicePresident = string.Empty;
        private readonly string _ResearchVicePresident = string.Empty;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="attendanceBo"></param>
        /// <param name="departmentBo"></param>
        /// <param name="employeeBo"></param>
        /// <param name="formTypeBo"></param>
        /// <param name="formFlowBo"></param>
        /// <param name="sinoSignBo"></param>
        /// <param name="notificationMail"></param>
        /// <param name="logger"></param>
        /// <param name="configuration"></param>
        public FormBo(IAttendanceBo attendanceBo, IDepartmentBo departmentBo,
            IEmployeeBo employeeBo, IFormTypeBo formTypeBo,
            IFormFlowBo formFlowBo, ISinoSignBo sinoSignBo, INotificationMail notificationMail,
            ILogger<FormBo> logger, IConfiguration configuration)
        {
            _attendanceBo = attendanceBo;
            _departmentBo = departmentBo;
            _employeeBo = employeeBo;
            _sinoSignBo = sinoSignBo;
            _configuration = configuration;
            _logger = logger;
            _notificationMail = notificationMail;
            _formTypeBo = formTypeBo;
            _formFlowBo = formFlowBo;
            // 取得設定檔 IsMultipleVicePresidents
            if (_configuration != null)
            {
                var k = _configuration["IsMultipleVicePresidents"];
                if (!string.IsNullOrWhiteSpace(k))
                {
                    _IsMultipleVicePresidents = _configuration.GetValue<bool>("IsMultipleVicePresidents");
                }

                if (_IsMultipleVicePresidents)
                {
                    (_AdministrativeVicePresident, _ResearchVicePresident) = InitializeRoles();
                }
            }
        }

        /// <summary>
        /// 初始化執行長角色
        /// </summary>
        /// <returns>行政副執行長, 研究副執行長</returns>
        private (string administrativeVicePresident, string researchVicePresident) InitializeRoles()
        {
            string administrativeVicePresident = string.Empty;
            string researchVicePresident = string.Empty;
            try
            {
                var s04Users = _sinoSignBo.GetRoleDefaultUser("S04");
                administrativeVicePresident = s04Users.Any() ? s04Users[0] : string.Empty;

                var s05Users = _sinoSignBo.GetRoleDefaultUser("S05");
                researchVicePresident = s05Users.Any() ? s05Users[0] : string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogWarning("初始化角色時發生錯誤: {Message}", ex.Message);
            }
            return (administrativeVicePresident, researchVicePresident);
        }

        /// <summary>
        /// 取得特定時間區間內所有表單 by 填報日期，限管理者使用
        /// </summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <returns>表單</returns>
        private delegate List<FormView> DelegateGetForms(DateTime startDate, DateTime endDate);

        /// <summary>
        /// 取得特定時間區間內所有表單，限管理者使用
        /// </summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單</returns>
        private delegate List<FormView> DelegateGetFormsProject(DateTime startDate, DateTime endDate, string projNo);

        /// <summary>
        /// 取得 已填表單
        /// </summary>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="status">表單狀態</param>
        public delegate DataTable DelegateGetSentBox(DateTime startDate, DateTime endDate, int? status = null);

        /// <summary>
        /// 取得 已填表單
        /// </summary>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="projNo">計畫編號</param>
        public delegate DataTable DelegateGetSentBoxProject(DateTime startDate, DateTime endDate, string projNo);

        /// <summary>
        /// 取得 填表人/表單關係人 已填表單
        /// </summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="status">表單狀態</param>
        public delegate DataTable DelegateGetUserSentBox(string empNo, DateTime startDate, DateTime endDate, int? status = null);

        /// <summary>
        /// 取得 填表人/表單關係人 已填表單
        /// </summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <param name="status">表單狀態</param>
        public delegate DataTable DelegateGetUserSentBoxProject(string empNo, DateTime startDate, DateTime endDate, string projNo, int? status = null);

        /// <summary>
        /// 將意見文字的HTML語法淨化
        /// </summary>
        /// <param name="comment"></param>
        /// <returns></returns>
        private static string ApproveCommentSanitize(string comment, bool isHtml)
        {
            var sanitizer = new HtmlSanitizer();
            comment = sanitizer.Sanitize(comment.Trim());
            if (!isHtml)
            {
                comment = Dangl.TextConverter.Html.HtmlToText.ConvertHtmlToPlaintext(comment);
            }
            return comment;
        }

        /// <summary>
        /// 簽核後是否結案
        /// </summary>
        /// <param name="form"></param>
        /// <param name="approveStatus"></param>
        /// <returns></returns>
        private static bool ApproveIsFinish(Form form, int approveStatus)
        {
            bool finish = false; // 是否結案
            if (form.FormStatus == (int)FormStatus.Processing)
            {
                if (form.TotalSteps == form.CurrentStep)
                {
                    form.FormStatus = approveStatus;
                    finish = true;
                }
                else
                {
                    switch (approveStatus)
                    {
                        case (int)FormStatus.Deny:
                            form.FormStatus = (int)FormStatus.Deny;
                            finish = true;
                            break;
                        case (int)FormStatus.Withdraw:
                            form.FormStatus = (int)FormStatus.Withdraw;
                            finish = true;
                            break;
                    }
                }
                // 檢查後續關卡是否均為通知
                if (form.TotalSteps > form.CurrentStep)
                {
                    bool notNotification = false;
                    foreach (FormFlow flow in form.Flows)
                    {
                        if (flow.Step > form.CurrentStep)
                        {
                            if (!flow.IsNotification)
                            {
                                notNotification = true;
                            }
                        }
                    }
                    if (!notNotification) // 後續都是通知
                    {
                        finish = true;
                    }
                }
            }
            else
            {
                finish = true;
            }
            return finish;
        }

        /// <summary>
        /// DataTable to SentBox List.
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="dt">DataTable</param>
        /// <returns></returns>
        /// <summary>
        private List<SentBox> DataTableToSentBoxList(string empNo, DataTable dt)
        {
            Dictionary<int, string> formStatusNames = _attendanceBo.GetFormStatusNames();
            List<SentBox> sentBoxes = SqlHelper.ConvertDataTable<SentBox>(dt);
            foreach (SentBox sentBox in sentBoxes)
            {
                if (sentBox.EmpNo == empNo && sentBox.CreatedEmpNo != empNo)
                {
                    sentBox.IsAgentFilled = true; // 別人代填
                }
                if (sentBox.EmpNo != empNo && sentBox.CreatedEmpNo == empNo)
                {
                    sentBox.IsAgency = true; //代別人填，自己是代理人
                }
                sentBox.FormStatusName = formStatusNames[sentBox.FormStatus];
                sentBox.AddedSigner = AddSignersAddName(sentBox.AddedSigner);
            }
            return sentBoxes;
        }

        /// <summary>DataTable轉為SignedForms</summary>
        /// <param name="dt"></param>
        /// <returns></returns>
        private List<SignedForm> DataTableToSignedForms(DataTable dt)
        {
            Dictionary<int, string> formStatusNames = _attendanceBo.GetFormStatusNames();
            List<SignedForm> signedForms = SqlHelper.ConvertDataTable<SignedForm>(dt);
            foreach (SignedForm signedForm in signedForms)
            {
                if (signedForm.CreatedEmpNo != signedForm.EmpNo)
                {
                    signedForm.IsAgentFilled = true;
                }
                signedForm.FormStatusName = formStatusNames[signedForm.FormStatus];
                signedForm.AddedSigner = AddSignersAddName(signedForm.AddedSigner);
            }
            return signedForms;
        }

        /// <summary>
        /// 去除重FormView裡的重覆資料
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="list"></param>
        /// <returns></returns>
        private static List<T> DedupFormViews<T>(List<T> list) where T : IFormView
        {
            List<T> newFormViews = new List<T>();
            var groups = from form in list
                         orderby form.FormID ascending, form.FormNo ascending
                         group form by form.FormUID into formGroup
                         where formGroup.Count() == 1
                         select formGroup.ToList();

            foreach (List<T> views in groups)
            {
                foreach (T formView in views)
                {
                    newFormViews.Add(formView);
                }
            }

            // 去除重覆卡號，合併 ApplicationType 及 TotalHours
            // 2025/07/31 改為依照 UpdatedTime 排序，取最新的
            var x = from form in list
                    orderby form.FormID ascending, form.FormNo ascending
                    group form by form.FormUID into grp
                    where grp.Count() > 1
                    select grp.OrderByDescending(f => f.UpdatedTime).ToList();

            // Dedup Guid, Regenerate ApplicationType(s)
            foreach (List<T> views in x)
            {
                T? formView = views.FirstOrDefault();
                if (formView != null)
                {
                    var types = (from t in views select t.ApplicationType).Distinct().ToList();
                    string applicationType = String.Join("、", types);
                    var typesList = applicationType.Split('、').ToList().Distinct().ToList();
                    applicationType = String.Join("、", typesList);
                    formView.ApplicationType = applicationType;
                    int totalHours = views.Sum(item => item.TotalHours);
                    formView.TotalHours = totalHours;
                    newFormViews.Add(formView);
                }
            }
            return newFormViews;
        }

        /// <summary>
        /// 將 FormAttachment 轉為 UploadedFile
        /// </summary>
        /// <param name="formAttachments"></param>
        /// <returns></returns>
        private static List<UploadedFile> FormAttachment2UploadedFile(List<FormAttachment> formAttachments)
        {
            List<UploadedFile> uploadedFiles = new List<UploadedFile>();
            foreach (FormAttachment attachment in formAttachments)
            {
                UploadedFile uploadedFile = new UploadedFile();
                uploadedFile.FilePathName = $"{attachment.FileDirectory}{Path.PathSeparator}{attachment.EncodedFileName}";
                uploadedFile.FileName = attachment.EncodedFileName;
                uploadedFiles.Add(uploadedFile);
            }
            return uploadedFiles;
        }

        /// <summary>
        /// 取得 填表人/表單關係人 結案已填表單
        /// </summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <returns>已填表單</returns>        
        private List<SentBox> GetAgreedSentBox(string empNo, DateTime startDate, DateTime endDate)
        {
            DataTable dt = _attendanceBo.GetSentBox(empNo, startDate, endDate, (int)FormStatus.Agree);
            List<SentBox> origSentBoxes = DataTableToSentBoxList(empNo, dt);
            List<SentBox> sentBoxes = DedupFormViews<SentBox>(origSentBoxes);
            Dictionary<int, string> formStatusNames = _attendanceBo.GetFormStatusNames();
            foreach (SentBox box in sentBoxes)
            {
                box.FormStatusName = formStatusNames[box.FormStatus];
            }
            return sentBoxes;
        }

        /// <summary>
        /// 取得 填表人/表單關係人 某月份已同意已填表單，依表單年月為準，非填表時間
        /// </summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <returns>已填表單</returns>
        private List<SentBox> GetAgreedSentBoxYearMonth(string empNo, int year, int month)
        {
            DataTable dt = _attendanceBo.GetSentBoxYearMonth(empNo, year, month, (int)FormStatus.Agree);
            List<SentBox> origSentBoxes = DataTableToSentBoxList(empNo, dt);
            List<SentBox> sentBoxes = DedupFormViews<SentBox>(origSentBoxes);
            Dictionary<int, string> formStatusNames = _attendanceBo.GetFormStatusNames();
            foreach (SentBox box in sentBoxes)
            {
                box.FormStatusName = formStatusNames[box.FormStatus];
            }
            return sentBoxes;
        }


        /// <summary>
        /// 取得日期區間內全社的已填表單，以填表日期為準
        /// </summary>
        /// <param name="startDate">The start date.</param>
        /// <param name="endDate">The end date.</param>
        /// <param name="projNo">計畫編號</param>
        /// <param name="getSentBox"></param>
        /// <param name="getSentBoxProj"></param>
        /// <returns></returns>
        private List<SentBox> GetAllSentBox(DateTime startDate, DateTime endDate, DelegateGetSentBox getSentBox, DelegateGetSentBoxProject getSentBoxProj, string? projNo)
        {
            DataTable dt;
            if (string.IsNullOrWhiteSpace(projNo))
            {
                dt = getSentBox(startDate, endDate);
            }
            else
            {
                dt = getSentBoxProj(startDate, endDate, projNo);
            }
            List<SentBox> sentBoxes = SqlHelper.ConvertDataTable<SentBox>(dt);
            List<SentBox> newSentBoxes = DedupFormViews<SentBox>(sentBoxes);

            Dictionary<int, string> formStatusNames = _attendanceBo.GetFormStatusNames();
            foreach (SentBox sentBox in newSentBoxes)
            {
                if (sentBox.EmpNo != sentBox.CreatedEmpNo)
                {
                    sentBox.IsAgentFilled = true; // 別人代填
                }
                sentBox.FormStatusName = formStatusNames[sentBox.FormStatus];
                sentBox.AddedSigner = AddSignersAddName(sentBox.AddedSigner);
            }
            return newSentBoxes;
        }

        /// <summary>
        /// 取得 部門  某月份已填表單 ，限部門登記桌或管理員，依表單年月為準，非填表時間
        /// </summary>
        /// <param name="deptNo">部門編號</param>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <returns>已填表單</returns>
        private List<SentBox> GetDepartmentSentBoxYearMonth(int deptNo, int year, int month)
        {
            DataTable dt = _attendanceBo.GetDepartmentSentBoxYearMonth(deptNo, year, month);
            List<SentBox> origSentBoxes = SqlHelper.ConvertDataTable<SentBox>(dt);
            List<SentBox> sentBoxes = DedupFormViews<SentBox>(origSentBoxes);
            Dictionary<int, string> formStatusNames = _attendanceBo.GetFormStatusNames();
            foreach (SentBox sentBox in sentBoxes)
            {
                if (sentBox.EmpNo != sentBox.CreatedEmpNo)
                {
                    sentBox.IsAgentFilled = true; // 別人代填
                }
                sentBox.FormStatusName = formStatusNames[sentBox.FormStatus];
                sentBox.AddedSigner = AddSignersAddName(sentBox.AddedSigner);
            }
            // 2025/05/15 因為在 Line 394 已經DedupFormViews，故不再重覆處理，改為直接回傳 sentBoxes
            // List<SentBox> dedupedSentboxes = DedupFormViews<SentBox>(sentBoxes);
            // return dedupedSentboxes;
            return sentBoxes;
        }

        /// <summary>
        /// 取得特定時間區間內所有表單，限管理者使用
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <param name="getForms"></param>
        /// <param name="getFormsProject"></param>
        /// <returns>表單</returns>
        private List<FormView> GetForms(string empNo, DateTime startDate, DateTime endDate, string projNo, DelegateGetForms getForms, DelegateGetFormsProject getFormsProject)
        {
            List<FormView> forms = new List<FormView>();
            // 所有表單限管理員可看
            if (_attendanceBo.IsAdmin(empNo))
            {
                if (string.IsNullOrWhiteSpace(projNo))
                {
                    forms = getForms(startDate, endDate);
                }
                else
                {
                    projNo = projNo.ToUpper();
                    forms = getFormsProject(startDate, endDate, projNo);
                }
                foreach (FormView form in forms)
                {
                    form.AddedSigner = AddSignersAddName(form.AddedSigner);
                }
            }
            List<FormView> dedupedforms = DedupFormViews<FormView>(forms);
            return dedupedforms;
        }

        /// <summary>
        /// 取得 填表人/表單關係人 已填表單
        /// </summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <returns>已填表單</returns>
        private List<SentBox> GetSentBox(string empNo)
        {
            DateTime endDate = DateTime.Now.Date;
            DateTime startDate = DateTime.Now.AddMonths(-1);
            DataTable dt = _attendanceBo.GetSentBox(empNo, startDate, endDate);
            List<SentBox> origSentBoxes = DataTableToSentBoxList(empNo, dt);
            List<SentBox> sentBoxes = DedupFormViews<SentBox>(origSentBoxes);
            Dictionary<int, string> formStatusNames = _attendanceBo.GetFormStatusNames();
            foreach (SentBox box in sentBoxes)
            {
                box.FormStatusName = formStatusNames[box.FormStatus];
            }
            return sentBoxes;
        }

        /// <summary>
        /// 取得 自己簽核表單
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="days">日數</param>
        /// <returns>已簽核表單</returns>
        private List<SignedForm> GetSignedForms(string empNo, int days)
        {
            DateTime endDate = DateTime.Now;
            DateTime startDate = endDate.AddDays(-1 * days);
            List<SignedForm> forms = GetSignedForms(empNo, startDate, endDate);
            List<SignedForm> signedForms = DedupFormViews<SignedForm>(forms);
            return signedForms;
        }


        /// <summary>
        /// 取得 已簽核表單，包括本人簽核或代理人簽核，及預設本人角色(非代理)簽核
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <returns>已簽核表單</returns>
        private List<SignedForm> GetSignedForms(string empNo, DateTime startDate, DateTime endDate)
        {
            List<Role> roles = _sinoSignBo.GetUserRoles(empNo);
            List<Role> newRoles = new List<Role>();
            for (int i = roles.Count - 1; i >= 0; i--)
            {
                Role role = roles[i];
                if (role.RoleType != RoleType.Agent)
                {
                    newRoles.Add(role); // 非代理的角色都加
                }
            }
            DataTable dt = _attendanceBo.GetSignedForms(empNo, startDate, endDate, newRoles);
            List<SignedForm> signedForms = DataTableToSignedForms(dt);
            Dictionary<int, string> formStatusNames = _attendanceBo.GetFormStatusNames();
            foreach (SignedForm form in signedForms)
            {
                form.FormStatusName = formStatusNames[form.FormStatus];
            }

            List<SignedForm> newSignedForms = DedupSignedFormViews(signedForms);
            return newSignedForms;
        }


        /// <summary>
        /// 去除重SignedForm裡的重覆資料
        /// </summary>
        /// <typeparam name="SignedForm"></typeparam>
        /// <param name="list"></param>
        /// <returns></returns>
        private static List<SignedForm> DedupSignedFormViews(List<SignedForm> list)
        {
            List<SignedForm> newFormViews = new List<SignedForm>();
            var groups = from form in list
                         orderby form.FormID ascending, form.FormNo ascending
                         group form by form.FormUID into formGroup
                         where formGroup.Count() == 1
                         select formGroup.ToList();

            foreach (List<SignedForm> views in groups)
            {
                foreach (SignedForm formView in views)
                {
                    newFormViews.Add(formView);
                }
            }

            // 去除重覆卡號，合併 ApplicationType 及 TotalHours
            var x = from form in list
                    orderby form.FormID ascending, form.FormNo ascending
                    group form by form.FormUID into grp
                    where grp.Count() > 1
                    select grp.OrderByDescending(f => f.Step).ToList();

            // Dedup Guid, Regenerate ApplicationType(s)
            foreach (List<SignedForm> views in x)
            {
                SignedForm? formView = views.FirstOrDefault();
                if (formView != null)
                {
                    var types = (from t in views select t.ApplicationType).Distinct().ToList();
                    string applicationType = String.Join("、", types);
                    var typesList = applicationType.Split('、').ToList().Distinct().ToList();
                    applicationType = String.Join("、", typesList);
                    formView.ApplicationType = applicationType;
                    int totalHours = views.Sum(item => item.TotalHours);
                    formView.TotalHours = totalHours;
                    newFormViews.Add(formView);
                }
            }
            return newFormViews;
        }


        /// <summary>
        /// 取得 已簽核表單，包括本人簽核或代理人簽核，及預設本人角色(非代理)簽核，以申請內容日期為準
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="contentStartDate">申請內容啟始日期</param>
        /// <param name="contentEndDate">申請內容結束日期</param>
        /// <returns>已簽核表單</returns>
        private List<SignedForm> GetSignedFormsByContentDate(string empNo, DateTime startDate, DateTime endDate, DateTime contentStartDate, DateTime contentEndDate)
        {
            List<Role> roles = _sinoSignBo.GetUserRoles(empNo);
            List<Role> newRoles = new List<Role>();
            for (int i = roles.Count - 1; i >= 0; i--)
            {
                Role role = roles[i];
                if (role.RoleType != RoleType.Agent)
                {
                    newRoles.Add(role); // 非代理的角色都加
                }
            }
            DataTable dt = _attendanceBo.GetSignedFormsByContentDate(empNo, startDate, endDate, contentStartDate, contentEndDate, newRoles);
            List<SignedForm> signedForms = DataTableToSignedForms(dt);
            Dictionary<int, string> formStatusNames = _attendanceBo.GetFormStatusNames();
            foreach (SignedForm form in signedForms)
            {
                form.FormStatusName = formStatusNames[form.FormStatus];
            }

            List<SignedForm> newSignedForms = DedupFormViews<SignedForm>(signedForms);
            return newSignedForms;
        }

        /// <summary>
        /// 取得 填表人/表單關係人 已填表單，以填表日期為準
        /// </summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <param name="status">表單狀態</param>
        /// <returns>已填表單</returns>
        private List<SentBox> GetUserSentBox(string empNo, DateTime startDate, DateTime endDate, string? projNo, int? status = null)
        {
            List<SentBox> sentBoxes = GetUserSentBox(empNo, startDate, endDate, _attendanceBo.GetSentBox, _attendanceBo.GetSentBox, projNo, status);
            return sentBoxes;
        }

        /// <summary>
        /// 取得 填表人/表單關係人 已填表單，以填表日期為準
        /// </summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <param name="status">表單狀態</param>
        /// <param name="getSentBox"></param>
        /// <param name="getSentBoxProject"></param>
        /// <returns>已填表單</returns>
        private List<SentBox> GetUserSentBox(string empNo, DateTime startDate, DateTime endDate,
            DelegateGetUserSentBox getSentBox, DelegateGetUserSentBoxProject getSentBoxProject,
            string? projNo, int? status = null)
        {
            DataTable dt;
            if (string.IsNullOrWhiteSpace(projNo))
            {
                dt = getSentBox(empNo, startDate, endDate, status);
            }
            else
            {
                dt = getSentBoxProject(empNo, startDate, endDate, projNo, status);
            }
            List<SentBox> origSentBoxes = DataTableToSentBoxList(empNo, dt);
            List<SentBox> sentBoxes = DedupFormViews<SentBox>(origSentBoxes);
            Dictionary<int, string> formStatusNames = _attendanceBo.GetFormStatusNames();
            foreach (SentBox box in sentBoxes)
            {
                box.FormStatusName = formStatusNames[box.FormStatus];
            }
            return sentBoxes;
        }

        /// <summary>
        /// 取得 填表人/表單關係人 已填表單，以內容日期為準
        /// </summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <param name="status">表單狀態</param>
        /// <returns>已填表單</returns>
        private List<SentBox> GetUserSentBoxByContentDate(string empNo, DateTime startDate, DateTime endDate, string? projNo, int? status = null)
        {
            List<SentBox> sentBoxes = GetUserSentBox(empNo, startDate, endDate, _attendanceBo.GetSentBoxByContentDate, _attendanceBo.GetSentBoxByContentDate, projNo, status);
            return sentBoxes;
        }

        // 2022/07/11 一次只推論一關，確保通知時機正確
        // 2023/11/21 對後續關卡若都是通知時要繼續處理
        /// <summary>
        /// 解析表單關卡，一次只處理一關，無遞迴</summary>
        /// <param name="form">表單</param>
        /// <param name="cardBo">三卡商業邏輯元件</param>
        /// <param name="card">三卡</param>
        /// <param name="notifications">通知</param>
        private void Inference(Form form, ICardBaseBo cardBo, CardBase card, List<Notification> notifications)
        {
            if (form != null)
            {
                if (form.Flows != null && form.Flows.Count > 0)
                {
                    form.TotalSteps = form.Flows.Count;
                    if (form.CurrentStep == 0)
                    {
                        form.CurrentStep = 1;
                    }
                    Dictionary<int, string> flowStatusNames = _attendanceBo.GetFormFlowStatusNames();
                    FormFlow? flow = form.Flows.Find(x => x.Step == form.CurrentStep);
                    if (flow != null)
                    {
                        if (flow.IsNotification)
                        {
                            Notification notification = new Notification();
                            notification.FormUID = form.FormUID;
                            notification.NotifyTime = DateTime.Now;
                            notification.NotifyEmpNo = flow.RecipientEmpNo;
                            notification.NotifyName = flow.RecipientName;
                            notifications.Add(notification);
                            flow.IsAgentApprove = false;
                            flow.FlowStatus = (int)FlowStatus.Notified;
                            if (form.TotalSteps == flow.Step)
                            {
                                //流程結束
                                form.FormStatus = (int)FormStatus.Agree;
                                form.EndTime = DateTime.Now;

                                Updater updateDto = new Updater();
                                if (!string.IsNullOrWhiteSpace(form.UpdatedEmpNo))
                                {
                                    updateDto.UpdatedEmpNo = form.UpdatedEmpNo;
                                }
                                if (!string.IsNullOrWhiteSpace(form.UpdatedName))
                                {
                                    updateDto.UpdatedName = form.UpdatedName;
                                }
                                updateDto.UpdatedTime = (DateTime)form.EndTime;
                                if (!string.IsNullOrWhiteSpace(form.UpdatedIP))
                                {
                                    updateDto.UpdatedIP = form.UpdatedIP;
                                }
                                if (!string.IsNullOrWhiteSpace(form.UpdatedHost))
                                {
                                    updateDto.UpdatedHost = form.UpdatedHost;
                                }
                                flow.IsAgentApprove = false;
                                cardBo.Finish(card, (FormStatus)form.FormStatus, updateDto);
                            }
                            else //通知之後 繼續解析下一關
                            {
                                form.CurrentStep++;
                                Inference(form, cardBo, card, notifications);
                            }
                        }
                        else //一般關卡
                        {
                            if (flow.FlowStatus == (int)FlowStatus.NotSend) //未傳送
                            {
                                flow.FlowStatus = (int)FlowStatus.Processing;
                            }
                        }
                        flow.FlowStatusName = flowStatusNames[flow.FlowStatus];
                    }
                }
                else //若無流程時
                {
                    form.TotalSteps = 0;
                    form.CurrentStep = 0;
                    form.FormStatus = (int)FormStatus.Agree;
                    form.EndTime = DateTime.Now;
                    Updater updateDto = new Updater();
                    updateDto.UpdatedEmpNo = (!string.IsNullOrWhiteSpace(card.UpdatedEmpNo)) ? card.UpdatedEmpNo : string.Empty;
                    updateDto.UpdatedName = (!string.IsNullOrWhiteSpace(card.UpdatedName)) ? card.UpdatedName : string.Empty;
                    updateDto.UpdatedTime = (DateTime)form.EndTime;
                    updateDto.UpdatedIP = (!string.IsNullOrWhiteSpace(card.UpdatedIP)) ? card.UpdatedIP : string.Empty;
                    updateDto.UpdatedHost = (!string.IsNullOrWhiteSpace(card.UpdatedHost)) ? card.UpdatedHost : string.Empty;
                    cardBo.Finish(card, (FormStatus)form.FormStatus, updateDto);
                }
            }
        }

        private static string MakeAttachmentDirectory(Form form)
        {
            if (form.ContentStartTime == null)
            {
                form.ContentStartTime = DateTime.Now;
            }

            DateTime contentStartTime = (DateTime)form.ContentStartTime;
            string rocYYY = CardUtility.RocChineseYearString(contentStartTime);
            string mm = contentStartTime.Month.ToString("00");
            string separator = Path.DirectorySeparatorChar.ToString();
            string directory = $"{form.FormID}{separator}{rocYYY}{separator}{mm}{separator}{form.FormNo}";
            return directory;
        }

        /// <summary>
        /// 組合表單資料集，供 新增/修改/刪除 使用
        /// </summary>
        /// <param name="form"></param>
        /// <param name="cardBo"></param>
        /// <param name="card"></param>
        /// <param name="notifications"></param>
        /// <param name="attachments"></param>
        /// <returns></returns>
        private static DataSet PrepareDataSet(Form form, ICardBaseBo cardBo, CardBase card, List<Notification> notifications, List<FormAttachment> attachments)
        {
            DataSet dataSet = new DataSet();
            DataTable dtForm = form.ToUserDefinedDataTable();
            dataSet.Tables.Add(dtForm);

            DataTable dtFormFlow = FormFlow.ToUserDefinedDataTable(form.Flows);
            dataSet.Tables.Add(dtFormFlow);

            List<DataTable> dtCards = cardBo.ToUserDefinedDataTables(card);
            foreach (DataTable dt in dtCards)
            {
                dataSet.Tables.Add(dt);
            }

            DataTable dtAttachments = SqlHelper.CreateDataTable<FormAttachment>(attachments);
            dtAttachments.TableName = "FormAttachment";
            dtAttachments.Columns.Remove("URL");
            dataSet.Tables.Add(dtAttachments);

            if (notifications.Count > 0)
            {
                DataTable dtNotification = Notification.ToUserDefinedDataTable(notifications);
                dataSet.Tables.Add(dtNotification);
            }

            return dataSet;
        }

        /// <summary>
        ///  將各個DataTable匯整為單一DataSet供 新增/修改/刪除 使用
        /// </summary>
        /// <param name="form"></param>
        /// <param name="flows"></param>
        /// <param name="cardBo"></param>
        /// <param name="card"></param>
        /// <param name="isAdd"></param>
        /// <param name="notifications"></param>
        /// <returns>
        ///   <br />
        /// </returns>
        private static DataSet PrepareDataSet(Form form, List<FormFlow> flows, ICardBaseBo cardBo, CardBase card, bool isAdd, List<Notification>? notifications)
        {
            DataSet dataSet = new DataSet();
            DataTable dtForm = form.ToUserDefinedDataTable();
            dataSet.Tables.Add(dtForm);
            if (flows != null && flows.Count > 0)
            {
                DataTable dtFormFlow = FormFlow.ToUserDefinedDataTable(flows);
                dataSet.Tables.Add(dtFormFlow);
            }

            List<DataTable> dtCards = cardBo.ToUserDefinedDataTables(card, isAdd);
            foreach (DataTable dt in dtCards)
            {
                dataSet.Tables.Add(dt);
            }

            if (notifications != null && notifications.Count > 0)
            {
                DataTable dtNotification = Notification.ToUserDefinedDataTable(notifications);
                dataSet.Tables.Add(dtNotification);
            }

            return dataSet;
        }


        /// <summary>
        /// 保存表單附件到遠端
        /// </summary>
        /// <param name="form">表單</param>
        /// <param name="uploadedFiles">上傳檔案</param>
        /// <returns></returns>
        /// <exception cref="ArgumentException">上傳檔案數量與附件數量不符時</exception>
        private string SaveAttachmentsToRemote(Form form, List<UploadedFile> uploadedFiles)
        {
            string sret = string.Empty;
            // 依照FormNo重新建立FormAttachment檔名
            List<FormAttachment> attachments = UploadedFile2FormAttachment(form, uploadedFiles);
            form.Attachments = attachments;

            string shareFolder = _configuration.GetSecuredConfigurationString($"Attachments:ShareFolder");
            string fileDirectory = MakeAttachmentDirectory(form);
            string combinedDirectory = Path.Combine(shareFolder, fileDirectory);
            for (int i = 0; i < attachments.Count; i++)
            {
                UploadedFile file = uploadedFiles[i];
                FormAttachment attachment = attachments[i];
                //另存新檔
                string pathName = Path.Combine(combinedDirectory, attachment.EncodedFileName);
                sret = SaveFileToRemote(file.FilePathName, pathName);
                if (sret != string.Empty)
                {
                    return sret;
                }
            }
            return sret;
        }

        /// <summary>Save local file to Remote Shared Folder</summary>
        /// <param name="sourceFilePathName">source file path name</param>
        /// <param name="targetFilePathName"></param>
        /// <returns>
        ///   <br />
        /// </returns>
        private string SaveFileToRemote(string sourceFilePathName, string targetFilePathName)
        {
            string ret = string.Empty;
            string username = _configuration.GetSecuredConfigurationString("Attachments:UserName");
            string password = _configuration.GetSecuredConfigurationString("Attachments:Password");
            string domain = _configuration.GetSecuredConfigurationString("Attachments:Domain");
            NetworkCredential credential = new NetworkCredential(username, password, domain);
            string networkPath = _configuration.GetSecuredConfigurationString("Attachments:ShareFolder");
            NetworkConnection networkConnection = new NetworkConnection(networkPath, credential);

            using (networkConnection)
            {
                if (File.Exists(sourceFilePathName))
                {
                    FileInfo fi = new FileInfo(targetFilePathName);
                    if (fi.Exists)
                    {
                        _logger.LogInformation("Action: {Action}失敗, source file: {SourceFileName}, 已存在 target file: {TargetFilePathName}",
                        nameof(SaveFileToRemote), sourceFilePathName, targetFilePathName);

                        throw new IOException($"Target file:{targetFilePathName} already exist!");
                    }

                    if (fi.Directory != null)
                    {
                        string folder = fi.Directory.FullName;
                        if (!Directory.Exists(folder))
                        {
                            Directory.CreateDirectory(folder);
                        }
                        byte[] bytes = File.ReadAllBytes(sourceFilePathName);
                        File.WriteAllBytes(targetFilePathName, bytes);
                        try
                        {
                            File.Delete(sourceFilePathName);
                        }
                        catch (IOException ex)
                        {
                            Console.WriteLine($"砍檔失敗:{sourceFilePathName} {ex.Message} {ex.StackTrace}");
                        }
                    }
                    else
                    {
                        throw new IOException($"Target file:{targetFilePathName} directory not exist!");
                    }
                }
                else
                {
                    _logger.LogInformation("Action: {Action}失敗, source file: {SourceFileName}, target file: {TargetFilePathName}：找不到 {SourceFilePathName}",
                        nameof(SaveFileToRemote), sourceFilePathName, targetFilePathName, sourceFilePathName);
                    ret += $"找不到 {sourceFilePathName} ";
                }
            }
            return ret;
        }

        /// <summary>
        /// 管理員代理簽核時，將簽核人顯示為管理員
        /// </summary>
        /// <param name="approveDto"></param>
        /// <param name="userId"></param>
        /// <param name="employee"></param>
        /// <returns></returns>
        private static bool SetAdminApprove(Approve approveDto, string userId, Employee employee)
        {
            // 以下為管理員代理簽核時，將簽核人顯示為管理員
            bool isAgent = true;
            approveDto.ApproverEmpNo = userId;
            approveDto.ApproverName = employee.CName;
            approveDto.ApproverDeptNo = employee.DeptNo;
            approveDto.ApproverDeptSName = employee.DeptSName;
            approveDto.ApproverTeamID = employee.TeamID;
            approveDto.ApproverTeamCName = employee.TeamCName;
            return isAgent;
        }

        /// <summary>新增 Form</summary>
        /// <param name="form">表單</param>
        /// <param name="flows">表單關卡</param>
        /// <param name="cardBo">三卡商業物件</param>
        /// <param name="card">卡</param>
        /// <param name="notifications">通知</param>
        /// <returns></returns>
        private string UpdateForm(Form form, List<FormFlow> flows, ICardBaseBo cardBo, CardBase card, List<Notification> notifications)
        {
            string errorMessage = string.Empty;

            if (form == null || cardBo == null || card == null)
            {
                errorMessage = "輸入資料不全";
                return errorMessage;
            }

            try
            {
                DataSet dataSet = PrepareDataSet(form, flows, cardBo, card, false, notifications);
                //呼叫FormDao寫入DB
                errorMessage = _attendanceBo.UpdateForm(form.FormID, dataSet);
            }
            catch (Exception ex)
            {
                string strForm = JsonConvert.SerializeObject(form);
                _logger.LogError(ex, "Action: {Action} 發生錯誤, 內容:{Form}，錯誤訊息:{Message} {StackTrace}", nameof(UpdateForm), strForm, ex.Message, ex.StackTrace);
                Console.WriteLine(ex.Message);
                //隱藏真實錯誤訊息 原應為 ex.Message;
                errorMessage = AttendanceParameters.UpdateErrorMessage;
            }

            return errorMessage;
        }

        /// <summary>
        /// List<UploadedFiles></UploadedFiles> 轉換為 Form.Attachments
        /// </summary>
        /// <param name="form"></param>
        /// <param name="uploadedFiles"></param>
        /// <returns></returns>
        private List<FormAttachment> UploadedFile2FormAttachment(Form form, List<UploadedFile>? uploadedFiles)
        {
            List<FormAttachment> attachments = new List<FormAttachment>();
            if (uploadedFiles != null && uploadedFiles.Count > 0 && form.ContentStartTime != null)
            {
                string fileDirectory = MakeAttachmentDirectory(form);
                List<UploadedFile> files = uploadedFiles;
                int serial = 0;
                foreach (UploadedFile file in files)
                {
                    serial++;
                    FormAttachment attachment = new FormAttachment();
                    attachment.FormUID = form.FormUID;
                    attachment.OriginalFileName = file.FileName;
                    string ext = Path.GetExtension(file.FileName).ToLower();
                    attachment.FileDirectory = fileDirectory;
                    attachment.EncodedFileName = EncodeAttachmentName(form.FormNo, serial, ext);
                    attachments.Add(attachment);
                }
            }
            return attachments;
        }

        /// <summary>
        /// 加入附件
        /// </summary>
        /// <param name="dtCardForms">The dt card notificationForms.</param>
        /// <param name="formCard">The form card.</param>
        /// <param name="drs">The DRS.</param>
        public void AddAttachments(DataTable dtCardForms, FormCard formCard, DataRow[] drs)
        {
            var attachmentIDs = (from DataRow dr in drs
                                 where dr["AttachmentID"] != DBNull.Value
                                 select (int)dr["AttachmentID"])
                                .Distinct().OrderBy(AttachmentID => AttachmentID);
            if (attachmentIDs.Any())
            {
                formCard.Attachments = new List<FormAttachment>();
                foreach (int attachmentID in attachmentIDs)
                {

                    DataRow[] drAttachments = dtCardForms.Select($"AttachmentID={attachmentID}");
                    if (drAttachments.Length > 0)
                    {
                        DataRow dr = drAttachments[0];
                        FormAttachment attachment = new FormAttachment();
                        attachment.ID = (int)dr["AttachmentID"];
                        attachment.FormUID = (Guid)dr["FormUID"];
                        attachment.OriginalFileName = (string)dr["OriginalFileName"];
                        attachment.EncodedFileName = (string)dr["EncodedFileName"];
                        attachment.FileDirectory = (string)dr["FileDirectory"];
                        formCard.Attachments.Add(attachment);
                    }

                }
            }
        }

        /// <summary>
        /// 依計畫判斷，非本部門計畫加會該部門主管
        /// </summary>
        /// <param name="form">表單物件</param>
        /// <param name="project">計畫物件</param>
        /// <returns>增加流程數， 0 或 1</returns>
        public int AddFlowByProject(Form form, Project project)
        {
            int counter = 0;
            string stageName; // 關卡名稱
            if (project.MainDeptNo != form.DeptNo)
            {
                string roleOther = $"L{project.MainDeptNo.ToString("00")}";
                string roleNameOther = EmployeeBo.CorrectJobName($"{project.DeptSName}主管");
                if (roleOther == "L01") // 治理部門主管
                {
                    // 取得副執行長關卡名稱
                    (roleOther, _, roleNameOther) = _formFlowBo.GetDeputyCEOStageInfo(form.DeptNo);
                }
                //檢查是否已經有該部門主管的流程
                int index = form.Flows.FindIndex(x => x.RecipientEmpNo == roleOther);
                if (index == -1)
                {
                    stageName = "主辦部門主管";
                    FormFlow flowProjDeptartmentManager = _formFlowBo.GenerateFlowDto(form, roleOther, stageName, project.MainDeptNo, project.DeptSName, roleNameOther);
                    form.Flows.Add(flowProjDeptartmentManager);
                    form.TotalSteps = form.Flows.Count;
                    counter++;
                }
            }
            return counter;
        }

        // 2024/11/15 改為資料庫寫入後才處理附件
        // 2022/07/01 決議採用 Stored Procedure 一次性寫入
        /// <summary>新增 Form</summary>
        /// <param name="form">表單</param>
        /// <param name="cardBo">三卡商業邏輯元件</param>
        /// <param name="card">三卡</param>
        /// <returns></returns>
        public string AddForm(Form form, ICardBaseBo cardBo, CardBase card)
        {
            string errorMessage = string.Empty;
            if (form == null || cardBo == null || card == null)
            {
                errorMessage = "輸入資料不全";
                return errorMessage; // false
            }

            //2022/12/27 限制最多加會10人
            if (!string.IsNullOrWhiteSpace(form.AddedSigner))
            {
                string[] addSigners = form.AddedSigner.Split(',');
                if (addSigners.Length > 10)
                {
                    errorMessage = "僅開放最多加會10名人員";
                    return errorMessage;
                }
            }

            // 先計算全部步驟，設定關卡數量
            List<Notification> notifications = new List<Notification>();
            // 解析關卡
            Inference(form, cardBo, card, notifications);
            bool formSent = false; //表單正確送出

            try
            {
                DateTime date = DateTime.Now;
                if (form.ContentStartTime != null)
                {
                    date = (DateTime)form.ContentStartTime;
                }

                int rocYear = CardUtility.RocChineseYear(date);
                _mutex.WaitOne();
                form.FormNo = _attendanceBo.GetNewFormNo(form.FormID, rocYear.ToString());

                //檢查Form.UID
                if (form.FormUID == Guid.Empty)
                {
                    form.FormUID = Guid.NewGuid();
                }
                //檢查FormFlow的FormUID
                if (form.Flows != null && form.Flows.Count > 0)
                {
                    foreach (FormFlow flow in form.Flows)
                    {
                        if (flow.FormUID == Guid.Empty)
                        {
                            flow.FormUID = form.FormUID;
                        }
                    }
                }

                cardBo.AmendCard(form, card); // 回寫FormNo等資料
                // 檢查 card.formUID
                if (card.FormUID == Guid.Empty)
                {
                    card.FormUID = form.FormUID;
                }

                foreach (Notification notification in notifications)
                {
                    notification.FormUID = form.FormUID;
                }

                // 附件轉換
                List<FormAttachment> attachments = UploadedFile2FormAttachment(form, card.UploadedFiles);
                form.Attachments = attachments;

                // 呼叫FormDao寫入DB
                DataSet dataSet = PrepareDataSet(form, cardBo, card, notifications, attachments);
                string? formNo = null;
                (formNo, errorMessage) = _attendanceBo.AddForm(form.FormID, rocYear, dataSet);
                if (errorMessage == string.Empty && !string.IsNullOrWhiteSpace(formNo)) // 寫入DB成功
                {
                    form.FormNo = formNo;
                    formSent = true;
                    // 寫入DB成功後儲存附件到遠端
                    if (card.UploadedFiles != null && card.UploadedFiles.Count > 0)
                    {
                        errorMessage = SaveAttachmentsToRemote(form, card.UploadedFiles);
                    }

                    //若是通知則需要繼續處理下一關卡
                    if (form.TotalSteps > form.CurrentStep && form.Flows != null && form.Flows.Count > 0)
                    {
                        FormFlow? currentStep = form.Flows.Find(x => x.Step == form.CurrentStep);
                        if (currentStep != null && currentStep.IsNotification)
                        {
                            form.CurrentStep++;
                            form.FormStatus = (int)FormStatus.Processing;
                            FormFlow? nextStep = form.Flows.Find(x => x.Step == form.CurrentStep);
                            if (nextStep != null)
                            {
                                nextStep.FlowStatus = (int)FlowStatus.Processing;
                                // 先計算全部步驟，設定關卡數量
                                notifications = new List<Notification>();
                                // 解析關卡
                                Inference(form, cardBo, card, notifications);
                                List<FormFlow> flows = new List<FormFlow>();
                                flows.Add(nextStep);
                                errorMessage = UpdateForm(form, flows, cardBo, card, notifications);
                            }
                        }
                    }
                    string formJson = JsonConvert.SerializeObject(form, Formatting.Indented);
                    _logger.LogInformation("Action: {Action}, 填單人 {CreatedName} 填{Name} \n 內容:{FormJson}",
                        nameof(AddForm), form.CreatedName, card.Name, formJson);
                }
            }
            catch (Exception ex)
            {
                //隱藏真實錯誤訊息 ，原來 errorMessage = ex.Message;
                if (formSent)
                {
                    errorMessage = AttendanceParameters.SaveAttachmentErrorMessage;
                }
                else
                {
                    errorMessage = AttendanceParameters.SubmitErrorMessage;
                }

                string formJson = JsonConvert.SerializeObject(form, Formatting.Indented);
                _logger.LogError(ex, "Action: {Action} 發生錯誤，錯誤訊息: {Message} {StackTrace}，內容為 {FormJson}", nameof(AddForm), ex.Message, ex.StackTrace, formJson);
                Console.WriteLine($"{DateTime.Now} AddForm 發生錯誤 {ex.Message} {ex.StackTrace}");
            }
            finally
            {
                _mutex.ReleaseMutex();
            }
            return errorMessage;
        }

        /// <summary>
        /// 替加會人員加上姓名
        /// </summary>
        /// <param name="addSigners">加會人員</param>
        /// <returns></returns>
        public string? AddSignersAddName(string? addSigners)
        {
            string? ret = null;
            if (!string.IsNullOrWhiteSpace(addSigners))
            {
                StringBuilder sb = new StringBuilder();
                string[] users = addSigners.Split(',');
                for (int i = 0; i < users.Length; i++)
                {
                    string empNo = users[i];
                    string empName = _employeeBo.GetEmployeeName(empNo);
                    sb.Append($",{empNo} {empName}");
                }
                ret = sb.ToString();
                ret = ret.Substring(1, ret.Length - 1);
            }
            return ret;
        }

        /// <summary>簽核</summary>
        /// <param name="approveDto">簽核資料物件</param>
        /// <param name="cardBo">三卡商業物件</param>
        /// <param name="empNo">簽核員工編號</param>
        /// <returns>簽核成功與否，成功為空值，失敗時為錯誤訊息 </returns>
        public string Approve(Approve approveDto, ICardBaseBo cardBo, string empNo)
        {
            string errorMessage = string.Empty;
            bool isAgent = false;
            if (approveDto == null || cardBo == null || empNo == string.Empty)
            {
                errorMessage = "輸入資料不全";
            }
            else
            {
                Employee? employee = _employeeBo.GetEmployeeDetail(empNo);
                if (employee == null)
                {
                    errorMessage = "員工資料不存在";
                    return errorMessage;
                }

                // 計算 hash code
                string jsonString = JsonConvert.SerializeObject(approveDto);
                string approveHashCode = CardUtility.ComputeMD5Hash(jsonString);
                DateTime now = DateTime.Now;
                double timeDiff = (now - _lastApproveTime).TotalSeconds;
                //檢查是否重覆送出
                if (approveHashCode == _lastApproveHashCode && timeDiff <= HASH_CODE_EXPIRE_SECONDS)
                {
                    errorMessage = "重覆送出簽核";
                    return errorMessage;
                }
                _lastApproveHashCode = approveHashCode;
                _lastApproveTime = now;

                approveDto.ApproveTime = approveDto.ApproveTime.ToLocalTime();
                //簽核人不是真正登入者，呼叫WebAPI或是管理員模擬身份
                if (approveDto.ApproverEmpNo != empNo)
                {
                    // Hack: 測試寄發email時先 disable 下一列，才會讓簽核人為模擬身份本人，不會是管理員代簽核， isAgent=false
                    // 下一列會設定簽核人為真實簽核的管理員
                    isAgent = SetAdminApprove(approveDto, empNo, employee);

                    // Log記下來
                    _logger.LogInformation("Action: {Action}, {userId} 模擬身份簽核 {formUID}", nameof(Approve), empNo, approveDto.FormUID);
                }
                // 加上 HtmlSanitizer 防止 插入惡意 Script
                if (!string.IsNullOrWhiteSpace(approveDto.Comment))
                {
                    bool isHtml = _configuration.GetValue<bool>("ApproveCommentsHtml");
                    approveDto.Comment = ApproveCommentSanitize(approveDto.Comment, isHtml);
                }
                Form form = GetForm(approveDto.FormUID);
                //先檢查是否有此表單
                if (form.CurrentStep == 0)
                {
                    errorMessage = "查無表單資料，無法簽核";
                    return errorMessage;
                }
                //先檢查是否為簽核中
                if (form.FormStatus != (int)FormStatus.Processing)
                {
                    errorMessage = "表單已結案，無法簽核";
                    return errorMessage;
                }
                List<FormFlow> flows = new List<FormFlow>();
                //檢查是否為目前簽核關卡
                FormFlow flow = _formFlowBo.GetFormFlowByFlowUID(approveDto.FlowUID);

                if (form.CurrentStep != flow.Step)
                {
                    if (form.CurrentStep > flow.Step)
                    {
                        errorMessage = "目前關卡已簽核";
                    }
                    else if (form.CurrentStep < flow.Step)
                    {
                        errorMessage = "非目前可簽核關卡";
                    }
                    return errorMessage;
                }
                if (flow.FlowUID == Guid.Empty || flow.RecipientEmpNo == null)
                {
                    errorMessage = "輸入資料不全";
                    return errorMessage;
                }

                //檢查簽核人身份，通知時不檢查
                if (approveDto.ApproverEmpNo != flow.RecipientEmpNo && !flow.IsNotification)
                {
                    bool inRoles = false;
                    if (flow.RecipientEmpNo.Length != 4)
                    {
                        List<Role> roles = _sinoSignBo.GetUserRoles(approveDto.ApproverEmpNo);
                        foreach (Role role in roles)
                        {
                            if (role.RoleId == flow.RecipientEmpNo)
                            {
                                inRoles = true;
                                if (role.RoleType == RoleType.Agent)
                                {
                                    isAgent = true;
                                }
                                break;
                            }
                        }
                    }
                    else if (flow.IsNotification)
                    {
                        inRoles = true;
                    }
                    else // if (flow.RecipientEmpNo.Length == 4) // 員工 ，不要限定只有4碼
                    {
                        Dictionary<string, string> deputies = _sinoSignBo.GetDeputies(flow.RecipientEmpNo);
                        foreach (var user in deputies)
                        {
                            if (user.Key == approveDto.ApproverEmpNo)
                            {
                                inRoles = true;
                                isAgent = true;
                                break;
                            }
                        }
                    }
                    if (!inRoles && !_attendanceBo.IsAdmin(empNo)) // 簽核人 不是該角色 也不是代理人，也不是管理員
                    {
                        _logger.LogInformation("Action: {Action}, {userId} 模擬身份{empNo}簽核 FormUID: {FormUID} 失敗 ",
                            nameof(Approve), empNo, approveDto.ApproverEmpNo, approveDto.FormUID);
                        errorMessage = "無簽核權限";
                        return errorMessage;
                    }
                }
                form.UpdatedEmpNo = approveDto.ApproverEmpNo;
                form.UpdatedHost = approveDto.ApproveHost;
                form.UpdatedIP = approveDto.ApproveIP;
                form.UpdatedName = approveDto.ApproverName;
                form.UpdatedTime = approveDto.ApproveTime;

                // 目前關卡是否存在
                FormFlow? currentStep = form.Flows.Find(x => x.Step == form.CurrentStep);
                if (currentStep != null)
                {
                    currentStep.FlowStatus = approveDto.FlowStatus;
                    flow = currentStep;
                    flows.Add(flow);
                }
                else
                {
                    errorMessage = "目前關卡不存在";
                    return errorMessage;
                }

                // Card
                if (cardBo != null && string.IsNullOrWhiteSpace(errorMessage))
                {
                    CardBase? card = cardBo.GetCard(form.FormUID);
                    if (card != null)
                    {
                        card.UpdatedEmpNo = approveDto.ApproverEmpNo;
                        card.UpdatedName = approveDto.ApproverName;
                        card.UpdatedTime = approveDto.ApproveTime;
                        card.UpdatedIP = approveDto.ApproveIP;
                        card.UpdatedHost = approveDto.ApproveHost;
                        card.SetApplicationType();
                        if (flow.IsNotification)
                        {
                            flow.IsAgentApprove = false;
                            flow.ApproverEmpNo = string.Empty;
                            flow.ApproverName = string.Empty;
                            flow.ApproverDeptNo = 0;
                            flow.ApproverDeptSName = string.Empty;
                            flow.ApproverTeamID = null;
                            flow.ApproverTeamCName = null;
                            flow.ApproveTime = null;
                            flow.ApproveIP = null;
                            flow.ApproveHost = null;
                        }
                        else
                        {
                            flow.IsAgentApprove = isAgent;
                            flow.ApproverEmpNo = approveDto.ApproverEmpNo;
                            flow.ApproverName = approveDto.ApproverName;
                            flow.ApproverDeptNo = approveDto.ApproverDeptNo;
                            flow.ApproverDeptSName = approveDto.ApproverDeptSName;
                            flow.ApproverTeamID = approveDto.ApproverTeamID;
                            flow.ApproverTeamCName = approveDto.ApproverTeamCName;
                            flow.ApproveTime = approveDto.ApproveTime;
                            flow.ApproveIP = approveDto.ApproveIP;
                            flow.ApproveHost = approveDto.ApproveHost;
                            flow.ApproveComments = approveDto.Comment;
                        }

                        // 是否結案
                        bool finished = ApproveIsFinish(form, approveDto.FlowStatus);
                        Updater updateDto = new Updater();
                        updateDto.UpdatedEmpNo = empNo;
                        updateDto.UpdatedName = employee.CName;
                        if (approveDto.ApproveTime != DateTime.MinValue)
                        {
                            updateDto.UpdatedTime = approveDto.ApproveTime;
                        }
                        if (!string.IsNullOrWhiteSpace(approveDto.ApproveIP))
                        {
                            updateDto.UpdatedIP = approveDto.ApproveIP;
                        }
                        if (!string.IsNullOrWhiteSpace(approveDto.ApproveHost))
                        {
                            updateDto.UpdatedHost = approveDto.ApproveHost;
                        }
                        if (errorMessage == string.Empty)
                        {
                            form.EndTime = approveDto.ApproveTime.ToLocalTime();
                            if (finished)
                            {
                                form.FormStatus = approveDto.FlowStatus;
                                cardBo.Finish(card, (FormStatus)form.FormStatus, updateDto);
                            }
                            else
                            {
                                form.FormStatus = (int)FormStatus.Processing;
                                cardBo.Update(card, updateDto);
                            }
                        }
                        if (finished)
                        {
                            form.EndTime = DateTime.Now;
                        }

                        // 先計算全部步驟，設定關卡數量
                        List<Notification> notifications = new List<Notification>();
                        // 解析關卡
                        Inference(form, cardBo, card, notifications);
                        FormFlow[] flowArray = form.Flows.ToArray();
                        // 若已同意結案表示後續若有關卡則需一次處理FormFlow，否則在Stored Procedure會無法寫入，但不同意就不必
                        if (finished && approveDto.FlowStatus == (int)FlowStatus.Agree
                            && form.TotalSteps > form.CurrentStep && form.Flows != null)
                        {
                            // 從下一關到最後一關
                            for (int i = form.CurrentStep; i < form.TotalSteps; i++)
                            {
                                flow = flowArray[i]; // 後續關卡
                                if (flow != null && flow.IsNotification) //若為通知進入下一關，因為已結束，後續只能為通知
                                {
                                    form.CurrentStep++;
                                    flows.Add(flow);
                                    Inference(form, cardBo, card, notifications);
                                }
                            }

                        }
                        // 執行 Stored Procedure, Call Stored Procedure更新Form、FormFlow、Card、Notification
                        errorMessage = UpdateForm(form, flows, cardBo, card, notifications);
                        _logger.LogInformation("Action:{Action} {UserId} 簽核 {Subject} FormUID: {FormUID} 結果 {ErrorMessage} ",
                        nameof(Approve), empNo, form.FormSubject, form.FormUID, errorMessage);

                        // 讀取是否寄發Email設定
                        bool boolSendEmail = _configuration.GetValue<bool>("Email:SendEmail");
                        bool noError = errorMessage == string.Empty;
                        bool hasCommentOrDeny = (((FlowStatus)approveDto.FlowStatus == FlowStatus.Deny)
                                || (!string.IsNullOrWhiteSpace(approveDto.Comment)));
                        if (boolSendEmail && noError && hasCommentOrDeny) // 若不同意或有簽核意見時，寄發 email
                        {
                            _notificationMail.SendNotifyEmail(form, card, approveDto, currentStep);
                        }
                        bool moreSteps = form.TotalSteps > form.CurrentStep;

                        if (!finished && form.Flows != null && noError && moreSteps)
                        {
                            form.CurrentStep++;
                            FormFlow? nextStep = form.Flows.Find(x => x.Step == form.CurrentStep);
                            if (nextStep != null)
                            {
                                flow = nextStep;
                                if (flow.IsNotification)
                                {
                                    flow.FlowStatus = (int)FlowStatus.Agree;
                                }
                                else
                                {
                                    flow.FlowStatus = (int)FlowStatus.Processing;
                                }
                                //設定關卡至下一關
                                if (finished)
                                {
                                    form.FormStatus = approveDto.FlowStatus;
                                    cardBo.Finish(card, (FormStatus)form.FormStatus, updateDto);
                                }
                                else
                                {
                                    cardBo.Update(card, updateDto);
                                }
                                // 先計算全部步驟，設定關卡數量
                                notifications = new List<Notification>();
                                flows = new List<FormFlow>();
                                flows.Add(flow);
                                errorMessage = UpdateForm(form, flows, cardBo, card, notifications);

                                if (errorMessage == string.Empty) //無錯誤訊息，簽核成功
                                {
                                    // 下一關為本人或本人之預設角色，非代理人時自動簽核
                                    Role? role = _sinoSignBo.UserInRole(empNo, nextStep.RecipientEmpNo);
                                    if (role != null && role.RoleType != RoleType.Agent)
                                    {
                                        approveDto.FlowUID = nextStep.FlowUID;
                                        errorMessage = Approve(approveDto, cardBo, empNo);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return errorMessage;
        }

        /// <summary>
        /// 是否能看特定流程的待審表單
        /// </summary>
        /// <param name="formUID">表單編號</param>
        /// <param name="empNo">觀看者員工編號</param>
        /// <returns></returns>
        public bool CanSeeApprovalFormCard(Guid formUID, string empNo)
        {
            bool canSee = false;
            if (string.IsNullOrWhiteSpace(empNo))
            {
                return canSee;
            }
            if (formUID == Guid.Empty)
            {
                return canSee;
            }
            Form form = GetForm(formUID);
            if (form != null)
            {
                if (form.FormStatus != (int)FormStatus.Processing)
                {
                    return false;
                }

                FormFlow? flow = _formFlowBo.GetFormFlow(formUID, form.CurrentStep);
                if (flow != null && flow.FormUID != Guid.Empty)
                {
                    if (flow.RecipientEmpNo == empNo) // 本人
                    {
                        canSee = true;
                    }
                    else if (_attendanceBo.IsAdmin(empNo)) // 為管理員
                    {
                        canSee = true;
                    }
                    else //是否有此角色
                    {
                        List<Role> roles = _sinoSignBo.GetUserRoles(empNo);
                        foreach (Role role in roles)
                        {
                            if (role.RoleId == flow.RecipientEmpNo)
                            {
                                canSee = true;
                                break;
                            }
                        }
                    }
                }
            }
            return canSee;
        }

        /// <summary>
        /// 能看部門資料
        /// </summary>
        /// <param name="deptNo"></param>
        /// <param name="empNo"></param>
        /// <returns></returns>
        public bool CanSeeDepartment(int deptNo, string empNo)
        {
            bool canSee = false;
            Employee? employee = _employeeBo.GetEmployeeDetail(empNo);
            if (employee != null)
            {
                if (employee.DeptNo == 1) // 治理
                {
                    canSee = true;
                }
                else
                {
                    // 是否為登記桌
                    List<Role> roles = _sinoSignBo.GetUserRoles(empNo);
                    string role = deptNo.ToString("00");
                    IEnumerable<Role> query = roles.Where(x => x.RoleId == role);

                    if (query.Count() == 1) // 角色為登記桌
                    {
                        canSee = true;
                    }
                    else
                    {
                        if (_employeeBo.IsAboveDeputyManager(empNo, deptNo))
                        {
                            canSee = true;
                        }
                    }
                }
            }
            return canSee;
        }

        /// <summary>
        /// 能看哪些部門資料
        /// </summary>
        /// <param name="deptNo"></param>
        /// <param name="empNo"></param>
        /// <returns></returns>
        public (List<int>, bool) CanSeeDepartments(string empNo)
        {
            bool canSeeAll = false; // 是否能看所有部門
            List<int> depts = new List<int>();
            List<int> allDepts = new List<int>();
            DataTable dtDepts = _departmentBo.GetDepartments();
            foreach (DataRow dr in dtDepts.Rows)
            {
                int deptNo = (int)dr["DeptNo"];
                allDepts.Add(deptNo);
            }
            Employee? employee = _employeeBo.GetEmployeeDetail(empNo);
            if (employee != null)
            {
                if (employee.DeptNo == 1) // 治理
                {
                    depts = allDepts; // 治理部門能看所有部門
                    canSeeAll = true; // 治理部門能看所有部門
                }
                else
                {
                    foreach (int deptNo in allDepts)
                    {

                        // 是否為登記桌
                        List<Role> roles = _sinoSignBo.GetUserRoles(empNo);
                        string role = deptNo.ToString("00");
                        IEnumerable<Role> query = roles.Where(x => x.RoleId == role);

                        if (query.Count() == 1) // 角色為登記桌
                        {
                            depts.Add(deptNo);
                        }
                        else
                        {
                            if (_employeeBo.IsAboveDeputyManager(empNo, deptNo))
                            {
                                depts.Add(deptNo);
                            }
                        }
                    }
                }
            }
            return (depts, canSeeAll);
        }

        /// <summary>
        /// 該 empNo 是否有權限看 empNo 的收件匣
        /// </summary>
        /// <param name="empNo">收件匣擁有者員工編號</param>
        /// <param name="userId">查看者員工編號</param>
        /// <returns>
        ///   若有權限查看則傳回 <c>true</c>，否則傳回 <c>false</c>.
        /// </returns>
        public bool CanSeeInbox(string empNo, string userId)
        {
            bool canSeeInbox = false;

            if (string.IsNullOrWhiteSpace(empNo) || string.IsNullOrWhiteSpace(userId))
            {
                return false;
            }

            // 收件匣限本人或主管或管理員可看
            bool isAdmin = _attendanceBo.IsAdmin(userId);
            if (isAdmin)
            {
                return true;
            }

            Employee? employeeWatcher = _employeeBo.GetEmployeeDetail(userId);
            if (employeeWatcher == null)
            {
                return false;
            }

            // 治理部門
            if (employeeWatcher.DeptNo == 1)
            {
                return true;
            }

            // 本人
            if (userId == empNo)
            {
                return true;
            }


            Employee? employee = _employeeBo.GetEmployeeDetail(empNo);
            if (employee == null)
            {
                return false;
            }

            // 是否為主管
            if (employee != null && employeeWatcher.IsAboveDeputyManager && employeeWatcher.DeptNo == employee.DeptNo)
            {
                return true;
            }

            // 是否為組長
            canSeeInbox = _employeeBo.IsTeamLeaderOf(userId, empNo);

            return canSeeInbox;
        }

        /// <summary>
        /// 是否能看特定通知表單
        /// </summary>
        /// <param name="formUID">表單編號</param>
        /// <param name="notifyId">The notify id.</param>
        /// <param name="logonUserId">登入者帳號</param>
        /// <param name="userId">查看員工編號</param>
        /// <returns></returns>
        public bool CanSeeNotifyFormCard(Guid formUID, int notifyId, string logonUserId, string userId)
        {
            List<Role> roles = _sinoSignBo.GetUserFormalRoles(userId);
            bool canSee = _attendanceBo.IsNotificationExist(roles, formUID, notifyId);

            if (canSee == false && _attendanceBo.IsAdmin(logonUserId))
            {
                canSee = _attendanceBo.IsNotificationExist(formUID, notifyId);
            }
            return canSee;
        }

        /// <summary>
        /// 是否能看特定已填表單
        /// </summary>
        /// <param name="formUID">表單編號</param>
        /// <param name="empNo">觀看者員工編號</param>
        /// <returns></returns>
        public bool CanSeeSentFormCard(Guid formUID, string empNo)
        {
            bool canSee = false;
            Form form = GetForm(formUID);
            if (form != null)
            {
                Employee? employeeWatcher = _employeeBo.GetEmployeeDetail(empNo);
                if (employeeWatcher == null)
                {
                    return false; // 未取得員工資料，不允許查看
                }

                if (_attendanceBo.IsAdmin(empNo) || (employeeWatcher.DeptNo == 1) || // 填表人 或 申請人 、管理員與治理都能看
                   form.CreatedEmpNo == empNo || form.EmpNo == empNo)
                {
                    canSee = true;
                }
                else
                {
                    // 是否為登記桌
                    List<Role> roles = _sinoSignBo.GetUserRoles(empNo);
                    string role = form.DeptNo.ToString("00");
                    IEnumerable<Role> query = roles.Where(x => x.RoleId == role);

                    if (query.Count() > 0)
                    {
                        canSee = true;
                    }
                    else
                    {
                        // 是否為正副主管
                        if (_employeeBo.IsAboveDeputyManager(empNo, form.DeptNo))
                        {
                            canSee = true;
                        }
                        // 是否為該員組長
                        if (employeeWatcher.IsTeamLeader)
                        {
                            Employee? employee = _employeeBo.GetEmployeeDetail(form.EmpNo);
                            if (employee.TeamID != null)
                            {
                                List<int> teamNos = _employeeBo.GetTeamLeaderTeamNos(empNo);
                                if(teamNos.Contains((int)employee.TeamID))
                                {
                                    canSee = true;
                                }
                            }
                        }
                    }
                }
            }
            return canSee;
        }

        /// <summary>
        /// 是否能看特定已簽核表單
        /// </summary>
        /// <param name="formUID">表單編號</param>
        /// <param name="empNo">觀看者員工編號</param>
        /// <returns></returns>
        public bool CanSeeSignedFormCard(Guid formUID, string empNo)
        {
            bool canSee = false;
            Form form = GetForm(formUID);
            if (form == null)
            {
                return false;
            }

            if (_attendanceBo.IsAdmin(empNo))
            {
                return true;
            }

            foreach (FormFlow flow in form.Flows)
            {
                // 簽核人與實際簽核代理人均可看
                if (flow.Step <= form.CurrentStep &&
                    (flow.ApproverEmpNo == empNo) || (flow.RecipientEmpNo == empNo))
                {
                    canSee = true;
                    break;
                }

                //檢查員工是否有已簽核的角色
                List<Role> roles = _sinoSignBo.GetUserRoles(empNo);
                foreach (Role role in roles)
                {
                    if (flow.Step <= form.CurrentStep && role.RoleId == flow.RecipientEmpNo)
                    {
                        canSee = true;
                        break;
                    }
                }


            }
            return canSee;
        }

        /// <summary>
        /// 是否可抽單
        /// </summary>
        /// <param name="form">The form.</param>
        /// <param name="cardBo">The card 商業物件</param>
        /// <param name="card">The card.</param>
        /// <param name="withdraw">抽單</param>
        /// <returns>
        ///   <c>true</c> if this instance can withdraw the specified form; otherwise, <c>false</c>.
        /// </returns>
        public bool CanWithdraw(Form form, ICardBaseBo cardBo, CardBase card, Withdraw withdraw)
        {
            bool ret = false;
            // 只允許申請人與填表人抽單/撤簽， 管理員也可以抽單/撤簽
            if (withdraw.WithdrawEmpNo == form.EmpNo || withdraw.WithdrawEmpNo == form.CreatedEmpNo
              || _attendanceBo.IsAdmin(withdraw.WithdrawEmpNo)
             )
            {
                ret = true;
            }
            return ret;
        }

        /// <summary>結案抽單</summary>
        /// <param name="form">表單</param>
        /// <param name="cardBo">三卡商業物件</param>
        /// <param name="card">三卡</param>
        /// <param name="withdraw">抽單物件</param>
        /// <returns>抽單成功傳回空值，失敗傳回錯誤訊息</returns>
        public string ClosedWithdraw(Form form, ICardBaseBo cardBo, CardBase card, Withdraw withdraw)
        {
            string errorMessage = string.Empty;

            DateTime now = DateTime.Now;
            double timeDiff = (now - _lastClosedWithdrawTime).TotalSeconds;
            //建立 withdraw 的 hash code, 用來檢查 withdraw 是否重覆發送
            string jsonString = JsonConvert.SerializeObject(withdraw);
            string withdrawHashCode = CardUtility.ComputeMD5Hash(jsonString);

            // 檢查 withdrawHashCode 是否與前一次相同
            if (_lastClosedWithdrawHashCode == withdrawHashCode &&
                timeDiff <= HASH_CODE_EXPIRE_SECONDS)
            {
                _logger.LogWarning("偵測到重複送出的抽單: {WithdrawHashCode}", withdrawHashCode);
                return "重複送出抽單";
            }

            // 更新最後一次的 hash code 與時間
            _lastClosedWithdrawHashCode = withdrawHashCode;
            _lastClosedWithdrawTime = now;

            if (form.FormStatus != (int)FormStatus.Agree) // 已同意結案方可抽單
            {
                switch ((FormStatus)form.FormStatus)
                {
                    case FormStatus.Withdraw:
                        errorMessage = "表單已抽單，無法抽單";
                        break;
                    case FormStatus.Processing:
                        errorMessage = "表單進行中，尚未結案";
                        break;
                    case FormStatus.Deny:
                        errorMessage = "表單已駁回，無法抽單";
                        break;
                    default:
                        errorMessage = "表單狀態不明，請聯絡系統管理者";
                        break;
                }

                _logger.LogError("Action: {Action} 發生錯誤，單號{FormNo}，抽單人{EmpNo}，錯誤訊息:{ErrorMessage}，原始資料:{Withdraw}",
                    nameof(ClosedWithdraw), form.FormNo, withdraw.WithdrawEmpNo, errorMessage, JsonConvert.SerializeObject(withdraw));
                return errorMessage;
            }

            if (_attendanceBo.IsAdmin(withdraw.WithdrawEmpNo)) // 只有管理員能 結案抽單
            {
                withdraw.WithdrawName = "管理者"; // 寫入 DB的 UpdatedName 要記成管理者
                string realWithdrawName = _employeeBo.GetEmployeeName(withdraw.WithdrawEmpNo);
                errorMessage = cardBo.CanClosedWithdraw(card);
                if (errorMessage == string.Empty)
                {
                    try
                    {
                        //呼叫FormDao寫入DB
                        errorMessage = _attendanceBo.ClosedWithdraw(withdraw);
                        _logger.LogInformation("Action: {Action}, 單號 {FormNo} {FormID} {FormUID} 抽單人 {WithdrawEmpNo} {WithdrawName} {WithdrawIP} {WithdrawHost}",
                            nameof(ClosedWithdraw), form.FormNo, withdraw.FormID, withdraw.FormUID, withdraw.WithdrawEmpNo, realWithdrawName, withdraw.WithdrawIP, withdraw.WithdrawHost);
                    }
                    catch (Exception ex)
                    {
                        string strForm = JsonConvert.SerializeObject(form);
                        _logger.LogError(ex, "Action: {Action} 發生錯誤，, 單號 {FormNo} {FormID} {FormUID} 抽單人 {WithdrawEmpNo} {WithdrawName} {WithdrawIP} {WithdrawHost}",
                            nameof(ClosedWithdraw), form.FormNo, withdraw.FormID, withdraw.FormUID, withdraw.WithdrawEmpNo, realWithdrawName, withdraw.WithdrawIP, withdraw.WithdrawHost);
                        Console.WriteLine(ex.Message);
                        errorMessage = ex.Message;
                        string errorStart = "錯誤訊息：";
                        if (errorMessage.Contains(errorStart))
                        {
                            int iStart = errorMessage.LastIndexOf(errorStart) + errorStart.Length;
                            errorMessage = errorMessage.Substring(iStart);
                        }
                    }
                }
            }
            else //無抽單權限
            {
                errorMessage = AttendanceParameters.WithdrawNoRightError;
            }
            return errorMessage;
        }

        /// <summary>
        /// 紀錄員工本人或本人角色第一次讀取通知的時間
        /// </summary>
        /// <param name="userId">查看員工編號</param>
        /// <param name="id">通知編號</param>
        /// <returns>紀錄第一次讀取通知的結果是成功或失敗</returns>
        public bool DeliveredNotifications(string userId, int id)
        {
            bool ret = false;
            List<Role> roles = _sinoSignBo.GetUserFormalRoles(userId);
            bool checkRole = _attendanceBo.GetNotifications()
                .Where(notification => notification.ID == id)
                .Where(notification => roles.Any(role => role.RoleId == notification.NotifyEmpNo.Trim()))
                .Any();
            if (checkRole)
            {
                ret = _attendanceBo.DeliveredNotifications(id);
            }
            return ret;
        }

        /// <summary>
        /// Encodes the name of the file for attachment
        /// </summary>
        /// <param name="formNo">The form number</param>
        /// <param name="serial">The attachment serial</param>
        /// <param name="fileName">Name of the file.</param>
        /// <returns></returns>
        public string EncodeAttachmentName(string formNo, int serial, string fileName)
        {
            string ext = Path.GetExtension(fileName).ToLower();
            string encodedFileName = $"{formNo}-{serial}{ext}";
            return encodedFileName;
        }

        /// <summary>
        /// 取得特定時間區間內已同意已填表單 JSON，包括他人代填與本人填表，以填表時間為準
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="userId">登入者</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <returns></returns>
        public string GetAgreedSentBoxJson(string empNo, string userId, DateTime startDate, DateTime endDate)
        {
            string ret = "[]";
            if (CanSeeInbox(empNo, userId))
            {
                ret = JsonConvert.SerializeObject(GetAgreedSentBox(empNo, startDate, endDate));
            }
            return ret;
        }

        /// <summary>
        /// 取得特定月份已同意已填表單，包括他人代填與本人填表，依表單年月為準，非填表時間
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="userId">登入者</param>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <returns></returns>
        public string GetAgreedSentBoxYearMonthJson(string empNo, string userId, int year, int month)
        {
            string ret = "[]";
            if (CanSeeInbox(empNo, userId))
            {
                ret = JsonConvert.SerializeObject(GetAgreedSentBoxYearMonth(empNo, year, month));
            }
            return ret;
        }

        /// <summary>
        /// 取得日期區間內全社的已填表單，以填表日期為準
        /// </summary>
        /// <param name="startDate">The start date.</param>
        /// <param name="endDate">The end date.</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns></returns>
        public List<SentBox> GetAllSentBox(DateTime startDate, DateTime endDate, string? projNo)
        {
            return GetAllSentBox(startDate, endDate, _attendanceBo.GetAllSentBox, _attendanceBo.GetAllSentBox, projNo);
        }

        /// <summary>
        /// 取得日期區間內全社的已填表單，以內容日期為準
        /// </summary>
        /// <param name="startDate">The start date.</param>
        /// <param name="endDate">The end date.</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns></returns>
        public List<SentBox> GetAllSentBoxByContentDate(DateTime startDate, DateTime endDate, string? projNo = null)
        {
            return GetAllSentBox(startDate, endDate, _attendanceBo.GetAllSentBoxByContentDate, _attendanceBo.GetAllSentBoxByContentDate, projNo);
        }

        /// <summary>
        /// 取得特定待簽核表單及卡
        /// </summary>
        /// <param name="formUID">Form Unique ID</param>
        /// <param name="cardBo">三卡商業元件</param>
        /// <param name="empNo">員工編號</param>
        /// <param name="userId">登入者帳號</param>
        /// <returns></returns>
        public string GetApprovalFormCard(Guid formUID, ICardBaseBo cardBo, string empNo, string userId)
        {
            string ret = "[]";
            if (CanSeeApprovalFormCard(formUID, empNo))
            {
                Form form = GetForm(formUID);
                FormCard formCard = cardBo.GetFormCard(form, userId);
                if (formCard.Card != null)
                {
                    CardBase card = formCard.Card;
                    (card.RemindMessageType, card.RemindMessage) = cardBo.GetListRemindMessage(card);
                    if (card.RemindMessageType > 0)
                    {
                        card.RemindSigner = true;
                    }
                    ret = JsonConvert.SerializeObject(formCard);
                }
            }
            return ret;
        }

        /// <summary>
        /// 取得表單附件
        /// </summary>
        /// <param name="formUID">表單 Guid</param>
        /// <param name="id">附件 id</param>
        /// <returns>FormAttachment 物件 或 null</returns>
        public FormAttachment? GetAttachment(Guid formUID, int id)
        {
            FormAttachment? attachment = null;
            DataTable dt = _attendanceBo.GetAttachments(formUID);
            if (dt != null && dt.Rows.Count > 0)
            {
                List<FormAttachment>? attachments = SqlHelper.ConvertDataTable<FormAttachment>(dt);
                if (attachments != null && attachments.Count > 0)
                {
                    foreach (var attach in attachments)
                    {
                        if (attach.ID == id)
                        {
                            attach.URL = $"/api/Form/DownloadAttachment/{formUID}/{attach.ID}";
                            attachment = attach;
                            break;
                        }
                    }
                }
            }
            return attachment;
        }

        /// <summary>
        /// 取得表單附件
        /// </summary>
        /// <param name="formUID">表單Guid</param>
        /// <returns></returns>
        public List<FormAttachment>? GetAttachments(Guid formUID)
        {
            List<FormAttachment>? attachments = null;
            DataTable dt = _attendanceBo.GetAttachments(formUID);
            if (dt != null && dt.Rows.Count > 0)
            {
                attachments = SqlHelper.ConvertDataTable<FormAttachment>(dt);
                foreach (FormAttachment attachment in attachments)
                {
                    attachment.URL = $"/api/Form/DownloadAttachment/{formUID}/{attachment.ID}";
                }
            }
            return attachments;
        }

        /// <summary>
        /// 取得特定月份某員工所有卡，依表單年月為準，非填表時間
        /// </summary>
        /// <param name="cardBoFactory"></param>
        /// <param name="empNo">員工編號</param>
        /// <param name="date">日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns></returns>
        public List<CardsList> GetCardsMonth(ICardBoFactory cardBoFactory, string empNo, DateTime date, int? status = null)
        {
            List<CardsList> cards = new List<CardsList>();
            List<FormType> formTypes = _formTypeBo.GetFormTypes();
            foreach (FormType formType in formTypes)
            {
                ICardBaseBo? cardBo = cardBoFactory.GetCardBo(formType.FormID);
                if (cardBo != null)
                {
                    List<CardBase> list = cardBo.GetUserCardsMonth(empNo, date, status);
                    if (list.Any())
                    {
                        CardsList card = new CardsList();
                        card.Cards = list;
                        card.FormName = formType.FormName;
                        card.FormID = formType.FormID;
                        cards.Add(card);
                    }
                }
            }
            return cards;
        }

        /// <summary>
        /// 取得部門已填表單，限部門登記桌或管理員
        /// </summary>
        /// <param name="deptNo">部門編號</param>
        /// <returns>已填表單</returns>
        public List<SentBox> GetDepartmentSentBox(int deptNo)
        {
            DataTable dt = _attendanceBo.GetDepartmentSentBox(deptNo);
            List<SentBox> origSentBoxes = SqlHelper.ConvertDataTable<SentBox>(dt);
            List<SentBox> sentBoxes = DedupFormViews<SentBox>(origSentBoxes);
            Dictionary<int, string> formStatusNames = _attendanceBo.GetFormStatusNames();
            foreach (SentBox sentBox in sentBoxes)
            {
                if (sentBox.EmpNo != sentBox.CreatedEmpNo)
                {
                    sentBox.IsAgentFilled = true; // 別人代填
                }
                sentBox.FormStatusName = formStatusNames[sentBox.FormStatus];
                sentBox.AddedSigner = AddSignersAddName(sentBox.AddedSigner);
            }
            return sentBoxes;
        }

        /// <summary>
        /// 取得部門已填表單，限部門登記桌或管理員
        /// </summary>
        /// <param name="request"></param>
        /// <returns>已填表單</returns>
        public List<SentBox> GetDepartmentSentBox(GetFormCardsRequest request)
        {
            DataTable dt;
            if (string.IsNullOrWhiteSpace(request.ProjNo))
            {
                dt = _attendanceBo.GetDepartmentSentBox(request.DeptNo, request.StartDate, request.EndDate);
            }
            else
            {
                dt = _attendanceBo.GetDepartmentSentBox(request.DeptNo, request.StartDate, request.EndDate, request.ProjNo);
            }
            List<SentBox> origSentBoxes = SqlHelper.ConvertDataTable<SentBox>(dt);
            List<SentBox> sentBoxes = DedupFormViews<SentBox>(origSentBoxes);
            Dictionary<int, string> formStatusNames = _attendanceBo.GetFormStatusNames();
            foreach (SentBox sentBox in sentBoxes)
            {
                if (sentBox.EmpNo != sentBox.CreatedEmpNo)
                {
                    sentBox.IsAgentFilled = true; // 別人代填
                }
                sentBox.FormStatusName = formStatusNames[sentBox.FormStatus];
                sentBox.AddedSigner = AddSignersAddName(sentBox.AddedSigner);
            }
            return sentBoxes;
        }

        /// <summary>
        /// 取得部門已填表單，限部門登記桌或管理員
        /// </summary>
        /// <param name="deptNo">部門編號</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>已填表單</returns>
        public List<SentBox> GetDepartmentSentBoxByContentDate(int deptNo, DateTime startDate, DateTime endDate, string? projNo = null)
        {
            DataTable dt;
            if (string.IsNullOrWhiteSpace(projNo))
            {
                dt = _attendanceBo.GetDepartmentSentBoxByContentDate(deptNo, startDate, endDate);
            }
            else
            {
                dt = _attendanceBo.GetDepartmentSentBoxByContentDate(deptNo, startDate, endDate, projNo);
            }
            List<SentBox> origSentBoxes = SqlHelper.ConvertDataTable<SentBox>(dt);
            List<SentBox> sentBoxes = DedupFormViews<SentBox>(origSentBoxes);
            Dictionary<int, string> formStatusNames = _attendanceBo.GetFormStatusNames();
            foreach (SentBox sentBox in sentBoxes)
            {
                if (sentBox.EmpNo != sentBox.CreatedEmpNo)
                {
                    sentBox.IsAgentFilled = true; // 別人代填
                }
                sentBox.FormStatusName = formStatusNames[sentBox.FormStatus];
                sentBox.AddedSigner = AddSignersAddName(sentBox.AddedSigner);
            }
            return sentBoxes;
        }

        /// <summary>
        /// 取得部門已填表單，限部門登記桌或管理員，以內容日期為準
        /// </summary>
        /// <param name="request"></param>
        /// <returns>已填表單 JSON</returns>
        public string GetDepartmentSentBoxByContentDateJson(GetFormCardsRequest request)
        {
            string ret = "[]";
            List<SentBox> sentBoxes = new List<SentBox>();
            // 1. 若 DeptNo 不為 0，則檢查是否有權限查看該部門資料
            if (request.DeptNo != 0)
            {
                bool canSee = CanSeeDepartment(request.DeptNo, request.EmpNo);
                if (canSee)
                {
                    sentBoxes = GetDepartmentSentBoxByContentDate(request.DeptNo, request.StartDate, request.EndDate, request.ProjNo);
                }
            }
            else // 2. 若 DeptNo 為 0，則取得全社的已填表單 或是使用者有權限查看的所有部門已填表單
            {
                (List<int> depts, bool canSeeAll) = CanSeeDepartments(request.EmpNo);
                if (canSeeAll)
                {
                    sentBoxes = GetAllSentBoxByContentDate(request.StartDate, request.EndDate, request.ProjNo);
                }
                else
                {
                    foreach (int deptNo in depts)
                    {
                        List<SentBox> deptSentBoxes = GetDepartmentSentBoxByContentDate(deptNo, request.StartDate, request.EndDate, request.ProjNo);
                        sentBoxes.AddRange(deptSentBoxes);
                    }
                }
            }

            // 3. 若有已填表單，則將表單狀態名稱加入
            if (sentBoxes.Count > 0)
            {
                Dictionary<int, string> formStatusNames = _attendanceBo.GetFormStatusNames();
                foreach (SentBox box in sentBoxes)
                {
                    box.FormStatusName = formStatusNames[box.FormStatus];
                }
                List<SentBox> dedupedSentBoxes = DedupFormViews<SentBox>(sentBoxes);
                ret = JsonConvert.SerializeObject(dedupedSentBoxes);
            }

            return ret;
        }

        /// <summary>
        /// 取得部門已填表單，限部門登記桌或管理員
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>已填表單 JSON</returns>
        public string GetDepartmentSentBoxJson(string empNo)
        {
            string ret = "[]";
            DateTime endDate = DateTime.Now;
            DateTime startDate = endDate.Date.AddDays(-31);
            if (empNo != null)
            {
                int deptNo = _employeeBo.GetDepartmentNumber(empNo);
                if (deptNo != 0)
                {
                    GetFormCardsRequest request = new GetFormCardsRequest();
                    request.EmpNo = empNo;
                    request.DeptNo = deptNo;
                    request.StartDate = startDate;
                    request.EndDate = endDate;
                    ret = GetDepartmentSentBoxJson(request);
                }
            }
            return ret;
        }

        /// <summary>
        /// 取得部門已填表單，限部門登記桌或管理員，以填表日期為準
        /// </summary>
        /// <param name="request"></param>
        /// <returns>已填表單 JSON</returns>
        public string GetDepartmentSentBoxJson(GetFormCardsRequest request)
        {
            string ret = "[]";
            List<SentBox> sentBoxes = new List<SentBox>();
            // 1. 若 DeptNo 不為 0，則檢查是否有權限查看該部門資料
            if (request.DeptNo != 0)
            {
                bool canSee = CanSeeDepartment(request.DeptNo, request.EmpNo);

                if (canSee)
                {
                    sentBoxes = GetDepartmentSentBox(request);
                }
            }
            else // 2. 若 DeptNo 為 0，則取得全社的已填表單 或是使用者有權限查看的所有部門已填表單
            {
                (List<int> depts, bool canSeeAll) = CanSeeDepartments(request.EmpNo);
                if (canSeeAll)
                {
                    sentBoxes = GetAllSentBox(request.StartDate, request.EndDate, request.ProjNo);
                }
                else
                {
                    foreach (int deptNo in depts)
                    {
                        request.DeptNo = deptNo;
                        List<SentBox> deptSentBoxes = GetDepartmentSentBox(request);
                        sentBoxes.AddRange(deptSentBoxes);
                    }
                }
            }

            // 3. 若有已填表單，則將表單狀態名稱加入
            if (sentBoxes.Count > 0)
            {
                Dictionary<int, string> formStatusNames = _attendanceBo.GetFormStatusNames();
                foreach (SentBox box in sentBoxes)
                {
                    box.FormStatusName = formStatusNames[box.FormStatus];
                }
                List<SentBox> dedupedSentBoxes = DedupFormViews<SentBox>(sentBoxes);
                ret = JsonConvert.SerializeObject(dedupedSentBoxes);
            }

            return ret;
        }

        /// <summary>
        /// 取得 部門  某月份已填表單 ，限部門登記桌或管理員，依表單年月為準，非填表時間
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="deptNo">部門編號</param>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <returns>已填表單</returns>
        public string GetDepartmentSentBoxYearMonthJson(string empNo, int deptNo, int year, int month)
        {
            string ret = "[]";
            if (empNo != null && CanSeeDepartment(deptNo, empNo))
            {
                List<SentBox> sentBoxes = GetDepartmentSentBoxYearMonth(deptNo, year, month);
                Dictionary<int, string> formStatusNames = _attendanceBo.GetFormStatusNames();
                foreach (SentBox box in sentBoxes)
                {
                    box.FormStatusName = formStatusNames[box.FormStatus];
                }
                ret = JsonConvert.SerializeObject(sentBoxes);
            }
            return ret;
        }

        /// <summary>取得 Form</summary>
        /// <param name="formUID">The form form.</param>
        /// <returns>DataTable Form</returns>
        public Form GetForm(Guid formUID)
        {
            Form form = new Form();
            DataTable dt = _attendanceBo.GetForm(formUID);
            if (dt != null && dt.Rows.Count > 0)
            {
                List<Form> forms = SqlHelper.ConvertDataTable<Form>(dt);
                form = forms[0];
                form.Flows = _formFlowBo.GetFormFlows(formUID);
                form.FormStatusName = GetFormStatusName(form.FormStatus);
                form.Attachments = GetAttachments(formUID);
            }
            return form;
        }

        /// <summary>
        /// 取得特定時間區間內所有表單，限管理者使用
        /// </summary>
        /// <param name="cardBoFactory"></param>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <returns>表單</returns>
        public List<FormCard> GetFormCards(ICardBoFactory cardBoFactory, DateTime startDate, DateTime endDate)
        {
            startDate = new DateTime(startDate.Year, startDate.Month, startDate.Day);
            endDate = new DateTime(endDate.Year, endDate.Month, endDate.Day);

            List<FormCard> formCards = new List<FormCard>();
            List<FormType> formTypes = _formTypeBo.GetFormTypes();
            foreach (FormType formType in formTypes)
            {
                ICardBaseBo? cardBo = cardBoFactory.GetCardBo(formType.FormID);
                if (cardBo != null)
                {
                    List<FormCard> formCardDtos = cardBo.GetFormCards(startDate, endDate);
                    foreach (FormCard formCard in formCardDtos)
                    {
                        formCards.Add(formCard);
                    }
                }
            }
            formCards.Sort((x, y) => x.FormInfo.CompareTo(y.FormInfo));
            return formCards;
        }


        /// <summary>
        /// 取得特定時間區間內所有表單，限管理者使用
        /// </summary>
        /// <param name="cardBoFactory"></param>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單</returns>
        public List<FormCard> GetFormCards(ICardBoFactory cardBoFactory, DateTime startDate, DateTime endDate, string projNo)
        {
            startDate = startDate.Date;
            endDate = endDate.Date;

            List<FormCard> formCards = new List<FormCard>();
            List<FormType> formTypes = _formTypeBo.GetFormTypes();
            foreach (FormType formType in formTypes)
            {
                ICardBaseBo? cardBo = cardBoFactory.GetCardBo(formType.FormID);
                if (cardBo != null)
                {
                    List<FormCard> formCardDtos = cardBo.GetFormCards(startDate, endDate, projNo);
                    foreach (FormCard formCard in formCardDtos)
                    {
                        formCards.Add(formCard);
                    }
                }
            }
            formCards.Sort((x, y) => x.FormInfo.CompareTo(y.FormInfo));
            return formCards;
        }

        /// <summary>
        /// 取得特定時間區間內所有表單及卡，限管理者使用
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="cardBoFactory"></param>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單</returns>
        public List<FormCard>? GetFormCards(string empNo, ICardBoFactory cardBoFactory, DateTime startDate, DateTime endDate, string projNo)
        {
            List<FormCard>? ret = null;
            // 所有表單限管理員可看
            if (_attendanceBo.IsAdmin(empNo))
            {
                if (string.IsNullOrWhiteSpace(projNo))
                {
                    ret = GetFormCards(cardBoFactory, startDate, endDate);
                }
                else
                {
                    projNo = projNo.ToUpper();
                    ret = GetFormCards(cardBoFactory, startDate, endDate, projNo);
                }
            }
            return ret;
        }

        /// <summary>
        /// 取得特定時間區間內所有表單及卡，限管理者使用
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="cardBoFactory"></param>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單</returns>
        public string GetFormCardsJson(string empNo, ICardBoFactory cardBoFactory, DateTime startDate, DateTime endDate, string projNo)
        {
            string ret = "[]";
            List<FormCard>? formCards = GetFormCards(empNo, cardBoFactory, startDate, endDate, projNo);
            if (formCards != null)
            {
                ret = JsonConvert.SerializeObject(formCards);
            }
            return ret;
        }


        /// <summary>只取得 Form</summary>
        /// <param name="formUID">The form form.</param>
        /// <returns>DataTable Form</returns>
        public Form GetFormOnly(Guid formUID)
        {
            Form form = new Form();
            DataTable dt = _attendanceBo.GetForm(formUID);
            if (dt != null && dt.Rows.Count > 0)
            {
                List<Form> forms = SqlHelper.ConvertDataTable<Form>(dt);
                form = forms[0];
            }
            return form;
        }

        /// <summary>
        /// 取得特定時間區間內所有表單 by 填報日期，限管理者使用
        /// </summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <returns>表單</returns>
        public List<FormView> GetForms(DateTime startDate, DateTime endDate)
        {
            startDate = new DateTime(startDate.Year, startDate.Month, startDate.Day, 0, 0, 0, DateTimeKind.Local);
            endDate = new DateTime(endDate.Year, endDate.Month, endDate.Day, 0, 0, 0, DateTimeKind.Local);
            DataTable dt = _attendanceBo.GetForms(startDate, endDate);
            List<FormView> forms = SqlHelper.ConvertDataTable<FormView>(dt);
            forms.Sort((x, y) => x.FormInfo.CompareTo(y.FormInfo));
            List<FormView> dedupedforms = DedupFormViews<FormView>(forms);
            return dedupedforms;
        }

        /// <summary>
        /// 取得特定時間區間內所有表單 by 填報日期，限管理者使用
        /// </summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單</returns>
        public List<FormView> GetForms(DateTime startDate, DateTime endDate, string projNo)
        {
            startDate = startDate.Date;
            endDate = endDate.Date;
            DataTable dt = _attendanceBo.GetForms(startDate, endDate, projNo);
            List<FormView> forms = SqlHelper.ConvertDataTable<FormView>(dt);
            forms.Sort((x, y) => x.FormInfo.CompareTo(y.FormInfo));
            List<FormView> dedupedforms = DedupFormViews<FormView>(forms);
            return dedupedforms;
        }

        /// <summary>
        /// 取得特定時間區間內所有表單 by 填報日期，限管理者使用
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單</returns>
        public List<FormView> GetForms(string empNo, DateTime startDate, DateTime endDate, string projNo)
        {
            List<FormView> forms = GetForms(empNo, startDate, endDate, projNo, GetForms, GetForms);
            return forms;
        }


        /*
        /// <summary>
        /// 取得特定時間區間內所有表單，限管理者使用
        /// </summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="getForms"></param>
        /// <returns>表單</returns>
        private List<FormView> GetFormsByContentDate(DateTime startDate, DateTime endDate, DelegateGetForms getForms)
        {
            startDate = startDate.Date;
            endDate = endDate.Date;
            DataTable dt = getForms(startDate, endDate);
            List<FormView> form = SqlHelper.ConvertDataTable<FormView>(dt);
            form.Sort((subjectx, y) => subjectx.FormInfo.CompareTo(y.FormInfo));
            List<FormView> dedupedforms = DedupFormViews<FormView>(form);
            return dedupedforms;
        }
        */

        /// <summary>
        /// 取得特定時間區間內所有表單 by 內容日期，限管理者使用
        /// </summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <returns>表單</returns>
        public List<FormView> GetFormsByContentDate(DateTime startDate, DateTime endDate)
        {
            DataTable dt = _attendanceBo.GetFormsByContentDate(startDate.Date, endDate.Date);
            List<FormView> forms = SqlHelper.ConvertDataTable<FormView>(dt);
            forms.Sort((x, y) => x.FormInfo.CompareTo(y.FormInfo));
            List<FormView> dedupedforms = DedupFormViews<FormView>(forms);
            return dedupedforms;
        }

        /// <summary>
        /// 取得特定時間區間內所有表單 by 內容日期，限管理者使用
        /// </summary>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單</returns>
        public List<FormView> GetFormsByContentDate(DateTime startDate, DateTime endDate, string projNo)
        {
            DataTable dt = _attendanceBo.GetFormsByContentDate(startDate.Date, endDate.Date, projNo);
            List<FormView> forms = SqlHelper.ConvertDataTable<FormView>(dt);
            forms.Sort((x, y) => x.FormInfo.CompareTo(y.FormInfo));
            List<FormView> dedupedforms = DedupFormViews<FormView>(forms);
            return dedupedforms;
        }

        /// <summary>
        /// 取得特定時間區間內所有表單 by 內容日期，限管理者使用
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單</returns>
        public List<FormView> GetFormsByContentDate(string empNo, DateTime startDate, DateTime endDate, string projNo)
        {
            List<FormView> forms = GetForms(empNo, startDate, endDate, projNo, GetFormsByContentDate, GetFormsByContentDate);
            return forms;
        }

        /// <summary>
        /// 取得特定時間區間內所有表單 by 內容日期，限管理者使用
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單</returns>
        public string GetFormsByContentDateJson(string empNo, DateTime startDate, DateTime endDate, string projNo)
        {
            List<FormView> forms = GetFormsByContentDate(empNo, startDate, endDate, projNo);
            string ret = JsonConvert.SerializeObject(forms);
            return ret;
        }

        /// <summary>
        /// 取得特定時間區間內所有表單 by 填報日期，限管理者使用
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns>表單</returns>
        public string GetFormsJson(string empNo, DateTime startDate, DateTime endDate, string projNo)
        {
            List<FormView> forms = GetForms(empNo, startDate, endDate, projNo);
            string ret = JsonConvert.SerializeObject(forms);
            return ret;
        }

        /// <summary>Gets the name of the Form Status.</summary>
        /// <param name="formStatus">The Form Status.</param>
        /// <returns>Form Status FormName</returns>
        public string GetFormStatusName(int formStatus)
        {
            string flowStatusName = string.Empty;
            Dictionary<int, string> formStatusNames = _attendanceBo.GetFormStatusNames();
            if (formStatusNames.Count > 0)
            {
                flowStatusName = formStatusNames[formStatus];
            }
            return flowStatusName;
        }

        /// <summary>取得收件匣</summary>
        /// <param name="empNo">簽核人員工編號</param>
        /// <param name="cardBoFactory">The card bo factory.</param>
        /// <returns>收件匣JSON</returns>
        public List<RoleInbox> GetInbox(string empNo, ICardBoFactory cardBoFactory)
        {
            List<RoleInbox> list = new List<RoleInbox>();
            if (!string.IsNullOrWhiteSpace(empNo))
            {
                List<Role> roles = _sinoSignBo.GetUserRoles(empNo);
                DataTable dt = _attendanceBo.GetInboxes(roles);
                Dictionary<int, string> formFlowStatusNames = _attendanceBo.GetFormFlowStatusNames();
                dt.Columns.Add("FormStatusName", typeof(string));

                foreach (DataRow dr in dt.Rows)
                {
                    int i = (byte)dr["FormStatus"];
                    dr["FormStatusName"] = formFlowStatusNames[i];
                }

                foreach (Role role in roles)
                {
                    RoleInbox roleInbox = new RoleInbox();
                    roleInbox.Role = role.RoleId;
                    DataRow[] drs = dt.Select($"RecipientEmpNo='{roleInbox.Role}'");
                    if (role.RoleType == RoleType.Agent)
                    {
                        roleInbox.IsAgency = true;
                    }
                    List<Inbox> inboxes = SqlHelper.ConvertDataTable<Inbox>(drs);
                    roleInbox.Inboxes = new List<Inbox>();
                    foreach (Inbox inbox in inboxes)
                    {
                        if (_IsMultipleVicePresidents) // S03 副執行長 有兩個人
                        {
                            if (inbox.RecipientEmpNo == "S03")
                            {
                                // 判斷出勤表單的部門
                                List<Department> administrativeDepartment = _departmentBo.GetAdministrativeDepartments();

                                // 若表單的部門為行政部門，則由 _AdministrativeS03 簽核
                                var dept = administrativeDepartment.FirstOrDefault(department => department.DeptNo == inbox.DeptNo);

                                if (empNo == _AdministrativeVicePresident) // 行政部門副執行長
                                {
                                    if (dept == null) // 非行政部門表單
                                    {
                                        continue; // 跳到下一筆
                                    }
                                }
                                else if (dept != null) //另一位副執行長，且表單為行政部門
                                {
                                    continue; // 跳到下一筆
                                }
                            }
                        }

                        if (inbox.EmpNo != inbox.CreatedEmpNo)
                        {
                            inbox.IsAgentFilled = true; // 別人代填
                        }
                        if (!string.IsNullOrWhiteSpace(inbox.AddedSigner))
                        {
                            inbox.AddedSigner = AddSignersAddName(inbox.AddedSigner);
                        }

                        // 檢查每張表單的警示訊息
                        ICardBaseBo? cardBo = cardBoFactory.GetCardBo(inbox.FormID);
                        if (cardBo != null)
                        {
                            CardBase? card = cardBo.GetCard(inbox.FormUID);
                            inbox.Card = card;
                            if (card != null)
                            {
                                Form form = new Form();
                                form.FormUID = inbox.FormUID;
                                form.FormID = inbox.FormID;
                                form.FormSubject = inbox.FormSubject;
                                form.FormInfo = inbox.FormInfo;
                                form.FormNo = inbox.FormNo;
                                cardBo.AmendCard(form, card);
                                card.SetApplicationType();
                                List<FormAttachment>? formAttachments = GetAttachments(form.FormUID);
                                if (formAttachments != null)
                                {
                                    inbox.Attachments = formAttachments;
                                    card.UploadedFiles = FormAttachment2UploadedFile(formAttachments);
                                }

                                (inbox.RemindMessageType, inbox.RemindMessage) = cardBo.GetListRemindMessage(card);
                                card.RemindMessage = inbox.RemindMessage;
                                if (inbox.RemindMessageType > 0)
                                {
                                    inbox.RemindSigner = true;
                                    card.RemindSigner = true;
                                }

                                // 本張卡總時數
                                inbox.Hours = cardBo.GetHours(card);
                            }
                        }
                        roleInbox.Inboxes.Add(inbox);
                    }
                    list.Add(roleInbox);
                }
            }
            return list;
        }

        /// <summary>
        /// 取得收件匣數量
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>收件匣數量</returns>
        public int GetInboxCount(string empNo)
        {
            List<Role> roles = _sinoSignBo.GetUserRoles(empNo);
            DataTable dt = _attendanceBo.GetInboxes(roles);
            int count = 0;

            if (_IsMultipleVicePresidents) // S03 副執行長 有兩個人
            {
                if (empNo == _AdministrativeVicePresident || empNo == _ResearchVicePresident) //S03 副執行長 兩個人,換人就要改設定檔
                {
                    // 判斷出勤表單的部門
                    List<Department> administrativeDepartment = _departmentBo.GetAdministrativeDepartments();

                    List<Inbox> inboxes = SqlHelper.ConvertDataTable<Inbox>(dt);
                    foreach (Inbox inbox in inboxes)
                    {
                        // 若表單的部門為行政部門，則由 2273 簽核
                        var dept = administrativeDepartment.FirstOrDefault(department => department.DeptNo == inbox.DeptNo);
                        if (inbox.RecipientEmpNo == "S03") // S03 副執行長關卡
                        {
                            if (empNo == _AdministrativeVicePresident) // 行政部門副執行長
                            {
                                if (dept == null) // 非行政部門表單
                                {
                                    continue; // 跳到下一筆
                                }
                            }
                            else if (dept != null) //另一位S03，且是行政部門表單
                            {
                                continue; // 跳到下一筆
                            }
                        }
                        count++;
                    }
                }
                else // 其他人
                {
                    count = dt.Rows.Count;
                }
            }
            else // S03 副執行長只有一個人
            {
                count = dt.Rows.Count;
            }
            return count;
        }

        /// <summary>取得特定員工 empNo 的待審表單供社內首頁提醒使用</summary>
        /// <param name="empNo">待審員工編號</param>
        /// <param name="userId">查看員工</param>
        /// <param name="cardBoFactory"></param>
        /// <returns>收件匣JSON</returns>
        public string GetInboxForRemind(string empNo, string userId, ICardBoFactory cardBoFactory)
        {
            string hostUrl = "https://attendance.sinotech.org.tw/AttendanceCard/Inbox/";
            // https://attendance.sinotech.org.tw/AttendanceCard/Inbox/B1CardApp/84865a69-2c9f-44fb-90ab-594408ca4d5e
            StringBuilder builder = new StringBuilder();
            List<RoleInbox> list = GetInbox(empNo, cardBoFactory);
            if (list.Count > 0)
            {
                int count = 0;
                foreach (RoleInbox roleInbox in list)
                {
                    count += roleInbox.Inboxes.Count;
                }
                if (count > 0)
                {
                    foreach (RoleInbox roleInbox in list)
                    {
                        foreach (Inbox inbox in roleInbox.Inboxes)
                        {
                            string url = $"{hostUrl}{inbox.FormID}/{inbox.FormUID}";

                            builder.Append($"<tr><td><a href=\"{url}\" target=\"_blank\">{inbox.FormNo}</a></td><td><a href=\"{url}\" target=\"_blank\">{inbox.FormSubject}</a></td><td>{inbox.FlowName}</td><td>{inbox.FilledTime.ToString("yyyy/MM/dd HH:mm:ss")}</td></tr>");
                        }
                    }
                }
            }
            string ret = builder.ToString();
            return ret;
        }

        /// <summary>
        ///   <para>userId取得員工待審表單(收件匣)</para>
        /// </summary>
        /// <param name="empNo">待審員工編號</param>
        /// <param name="userId">查看員工</param>
        /// <param name="cardBoFactory"></param>
        /// <returns>收件匣JSON</returns>
        public string GetInboxJson(string empNo, string userId, ICardBoFactory cardBoFactory)
        {
            string ret = "[]";
            // 收件匣限本人或管理員可看
            if (CanSeeInbox(empNo, userId))
            {
                List<RoleInbox> list = GetInbox(empNo, cardBoFactory);
                ret = JsonConvert.SerializeObject(list);
            }
            return ret;
        }

        /// <summary>userId取得員工收件匣(待核表單)</summary>
        /// <param name="empNo">收件匣(待核表單) 員工編號</param>
        /// <param name="userId">查看員工編號</param>
        /// <param name="logonUserId">登入員工</param>
        /// <param name="cardBoFactory"></param>
        /// <returns>收件匣JSON</returns>
        public string GetInboxJson(string empNo, string userId, string logonUserId, ICardBoFactory cardBoFactory)
        {
            string ret = "[]";
            if (logonUserId != userId)
            {
                bool isAdmin = _attendanceBo.IsAdmin(userId);
                if (!isAdmin) //非管理員禁止切換身份查
                {
                    return ret;
                }
            }
            // 收件匣限本人或主管或管理員可看
            if (CanSeeInbox(empNo, userId))
            {
                List<RoleInbox> list = GetInbox(empNo, cardBoFactory);
                ret = JsonConvert.SerializeObject(list);
            }
            return ret;
        }

        /// <summary>
        /// 取得特定月份某員工所有卡的JSON string，依表單年月為準，非填表時間
        /// </summary>
        /// <param name="cardBoFactory"></param>
        /// <param name="empNo">員工編號</param>
        /// <param name="date">日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns>JSON string</returns>
        public string GetMonthSentCardsJson(ICardBoFactory cardBoFactory, string empNo, DateTime date, int? status = null)
        {
            List<CardsList> cards = GetCardsMonth(cardBoFactory, empNo, date, status);
            string ret = JsonConvert.SerializeObject(cards);
            return ret;
        }

        /// <summary>
        /// 取得所有的未讀通知數量
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>所有的未讀通知數量</returns>
        public int GetNotificationCount(string empNo)
        {
            List<Role> roles = _sinoSignBo.GetUserFormalRoles(empNo);
            int count = _attendanceBo.GetNotifications()
                .Where(notification => roles.Any(role => role.RoleId == notification.NotifyEmpNo.Trim()))
                .Where(notification => (notification.ViewTime is null)).Count();
            return count;
        }

        /// <summary>
        /// 取得特定通知表單及卡
        /// </summary>
        /// <param name="formUID">Form Unique ID</param>
        /// <param name="cardBo">三卡商業元件</param>
        /// <param name="notifyId">The notify id.</param>
        /// <param name="logonUserId">登入者帳號</param>
        /// <param name="userId">查看員工編號</param>
        /// <returns></returns>
        public string GetNotifyFormCard(Guid formUID, ICardBaseBo cardBo, int notifyId, string logonUserId, string userId)
        {
            string ret = "[]";

            if (!string.IsNullOrWhiteSpace(userId) && CanSeeNotifyFormCard(formUID, notifyId, logonUserId, userId))
            {
                Form form = GetForm(formUID);
                FormCard formCard = cardBo.GetFormCard(form, logonUserId);
                ret = JsonConvert.SerializeObject(formCard);
            }
            return ret;
        }

        /// 取得在特定時間區間的通知表單卡別資料
        /// <param name="empNo">申請人員工編號</param>
        /// <param name="userId">被通知者員工編號</param>
        /// <param name="logonUserId">實際查詢通知的員工編號</param>
        /// <param name="deptNo">申請人部門編號</param>
        /// <param name="isRead">通知狀態</param>
        /// <param name="formStatus">表單狀態</param>
        /// <param name="startDate">起始時間</param>
        /// <param name="endDate">結束時間</param>
        /// <returns>通知JSON</returns>
        public string GetNotifyFormCards(string empNo, string userId, string logonUserId, int deptNo, List<int> isRead, List<int> formStatus, DateTime startDate, DateTime endDate)
        {
            string ret = "[]";

            // 通知限被通知者本人或管理員可看
            if (logonUserId != userId)
            {
                bool isAdmin = _attendanceBo.IsAdmin(logonUserId);
                if (!isAdmin) // 非管理員禁止切換身份查
                {
                    return ret;
                }
            }

            List<Role> roles = _sinoSignBo.GetUserFormalRoles(userId);
            List<NotifyFormCards> notifications = _attendanceBo.GetNotifyFormCards(empNo, deptNo, roles, isRead, formStatus, startDate, endDate);

            ret = JsonConvert.SerializeObject(notifications);

            return ret;
        }

        /// <summary>
        /// 取得未結案表單，管理員傳回所有人的表單，其他人傳回自己的表單或自己填的表單
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <returns>未結案表單</returns>
        public string GetOpenFormsJson(string empNo)
        {
            string ret = "[]";
            Dictionary<int, string> formStatusNames = _attendanceBo.GetFormStatusNames();
            bool isAdmin = _attendanceBo.IsAdmin(empNo);
            if (isAdmin)
            {
                DataTable dt = _attendanceBo.GetFormsByStatus(FormStatus.Processing);
                List<Form> forms = SqlHelper.ConvertDataTable<Form>(dt);
                foreach (Form form in forms)
                {
                    form.AddedSigner = AddSignersAddName(form.AddedSigner);
                    form.FormStatusName = formStatusNames[form.FormStatus];
                }
                ret = JsonConvert.SerializeObject(forms);
            }
            else
            {
                DataTable dt = _attendanceBo.GetFormsByStatus(FormStatus.Processing);
                if (dt != null && dt.Rows.Count > 0)
                {
                    DataRow[] drs = dt.Select($"CreatedEmpNo = {empNo} OR EmpNo = {empNo}");
                    List<Form> forms = SqlHelper.ConvertDataTable<Form>(drs);
                    foreach (Form form in forms)
                    {
                        form.AddedSigner = AddSignersAddName(form.AddedSigner);
                        form.FormStatusName = formStatusNames[form.FormStatus];
                    }
                    ret = JsonConvert.SerializeObject(forms);
                }
            }
            return ret;
        }

        /// <summary>
        /// 以某人身份取得未結案表單
        /// </summary>
        /// <param name="userId">登入者</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>未結案表單</returns>
        public string GetOpenFormsJson(string userId, string empNo)
        {
            string ret = "[]";
            if (!string.IsNullOrWhiteSpace(userId) && !string.IsNullOrWhiteSpace(empNo))
            {
                bool isAdmin = _attendanceBo.IsAdmin(userId);
                if (isAdmin) //管理員才能模擬
                {
                    ret = GetOpenFormsJson(empNo);
                }
            }
            return ret;
        }

        /// <summary>
        /// 取得未結案表單，管理員傳回所有人的表單，其他人傳回自己的表單或自己填的表單
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <returns>未結案表單</returns>
        public string GetOpenFormsJson(string empNo, DateTime startDate, DateTime endDate)
        {
            string ret = "[]";
            Dictionary<int, string> formStatusNames = _attendanceBo.GetFormStatusNames();
            bool isAdmin = _attendanceBo.IsAdmin(empNo);
            if (isAdmin)
            {
                DataTable dt = _attendanceBo.GetFormsByStatus(FormStatus.Processing, startDate, endDate);
                List<Form> forms = SqlHelper.ConvertDataTable<Form>(dt);
                foreach (Form form in forms)
                {
                    form.AddedSigner = AddSignersAddName(form.AddedSigner);
                    form.FormStatusName = formStatusNames[form.FormStatus];
                }
                ret = JsonConvert.SerializeObject(forms);
            }
            else
            {
                DataTable dt = _attendanceBo.GetFormsByStatus(FormStatus.Processing);
                if (dt != null && dt.Rows.Count > 0)
                {
                    DataRow[] drs = dt.Select($"CreatedEmpNo = {empNo} OR EmpNo = {empNo}");
                    List<Form> forms = SqlHelper.ConvertDataTable<Form>(drs);
                    foreach (Form form in forms)
                    {
                        form.AddedSigner = AddSignersAddName(form.AddedSigner);
                        form.FormStatusName = formStatusNames[form.FormStatus];
                    }
                    ret = JsonConvert.SerializeObject(forms);
                }
            }
            return ret;
        }

        /// <summary>
        /// 取得未結案表單，管理員傳回所有人的表單，其他人傳回自己的表單或自己填的表單
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="cardBoFactory"></param>
        /// <param name="startDate">啟始填表日期</param>
        /// <param name="endDate">結束填表日期</param>
        /// <param name="projNo">計畫編號，若不填則傳回所有</param>
        /// <returns>未結案表單</returns>
        public string GetOpenFormsJson(string empNo, ICardBoFactory cardBoFactory, DateTime startDate, DateTime endDate, string projNo)
        {
            if (string.IsNullOrWhiteSpace(projNo))
            {
                return GetOpenFormsJson(empNo, startDate, endDate);
            }

            bool isAdmin = _attendanceBo.IsAdmin(empNo);
            List<FormCard> allFormCards = GetFormCards(cardBoFactory, startDate, endDate, projNo);
            List<FormCard> formCards = new List<FormCard>();
            if (isAdmin)
            {
                foreach (FormCard formCard in allFormCards)
                {
                    if (formCard.FormStatus == (int)FormStatus.Processing)
                    {
                        formCards.Add(formCard);
                    }
                }
            }
            else
            {
                foreach (FormCard formCard in allFormCards)
                {
                    if ((formCard.CreatedEmpNo == empNo || formCard.EmpNo == empNo)
                        && (formCard.FormStatus == (int)FormStatus.Processing))
                    {
                        formCards.Add(formCard);
                    }
                }
            }
            string ret = JsonConvert.SerializeObject(formCards);
            return ret;
        }

        /// <summary>Get Remote File</summary>
        /// <param name="attachment">Form Attachment</param>
        /// <returns>Attachment file</returns>
        public byte[] GetRemoteFile(FormAttachment? attachment)
        {
            byte[] ret = new byte[0];
            if (attachment == null)
            {
                return ret;
            }
            string username = _configuration.GetSecuredConfigurationString("Attachments:UserName");
            string password = _configuration.GetSecuredConfigurationString("Attachments:Password");
            string domain = _configuration.GetSecuredConfigurationString("Attachments:Domain");
            NetworkCredential credential = new NetworkCredential(username, password, domain);
            string networkPath = _configuration.GetSecuredConfigurationString("Attachments:ShareFolder");
            NetworkConnection networkConnection = new NetworkConnection(networkPath, credential);
            string target = Path.Combine(networkPath, attachment.FileDirectory, attachment.EncodedFileName);
            using (networkConnection)
            {
                if (File.Exists(target))
                {
                    FileInfo fi = new FileInfo(target);
                    if (fi.Exists)
                    {
                        ret = File.ReadAllBytes(fi.FullName);
                    }
                    else
                    {
                        throw new IOException($"Target file:{attachment.EncodedFileName} ({attachment.OriginalFileName}) not exist!");
                    }
                }
                else
                {
                    _logger.LogInformation("Action: {Action}失敗, 找不到 {attachment}",
                        nameof(GetRemoteFile), attachment.EncodedFileName);
                }
            }
            return ret;
        }

        /// <summary>Get Remote File</summary>
        /// <param name="formUID">表單 Guid</param>
        /// <param name="id">附件 id</param>
        /// <returns>遠端檔案</returns>
        public byte[] GetRemoteFile(Guid formUID, int id)
        {
            FormAttachment? attachment = GetAttachment(formUID, id);
            return GetRemoteFile(attachment);
        }

        /// <summary>
        /// Gets the sent form json.
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="userId">登入者</param>
        /// <returns></returns>
        public string GetSentBoxJson(string empNo, string userId)
        {
            string ret = "[]";
            if (CanSeeInbox(empNo, userId))
            {
                ret = JsonConvert.SerializeObject(GetSentBox(empNo));
            }
            return ret;
        }

        /// <summary>
        /// 取得特定已填表單及卡
        /// </summary>
        /// <param name="formUID">Form Unique ID</param>
        /// <param name="cardBo">三卡商業元件</param>
        /// <param name="empNo">員工編號</param>
        /// <param name="userId">登入者帳號</param>
        /// <returns></returns>
        public string GetSentFormCard(Guid formUID, ICardBaseBo cardBo, string empNo, string userId)
        {
            string ret = "[]";
            if (!string.IsNullOrWhiteSpace(userId) && (empNo == userId || CanSeeSentFormCard(formUID, empNo)))
            {
                Form form = GetForm(formUID);
                FormCard formCard = cardBo.GetFormCard(form, userId);
                ret = JsonConvert.SerializeObject(formCard);
            }
            return ret;
        }

        /// <summary>
        /// 取得特定已簽核表單及卡
        /// </summary>
        /// <param name="formUID">Form Unique ID</param>
        /// <param name="cardBo">三卡商業元件</param>
        /// <param name="empNo">員工編號</param>
        /// <param name="userId">登入者帳號</param>
        /// <returns></returns>
        public string GetSignedFormCard(Guid formUID, ICardBaseBo cardBo, string empNo, string userId)
        {
            string ret = "[]";
            if (!string.IsNullOrWhiteSpace(userId) && (empNo == userId || _attendanceBo.IsAdmin(userId)) && CanSeeSignedFormCard(formUID, empNo))
            {
                Form form = GetForm(formUID);
                FormCard formCard = cardBo.GetFormCard(form, userId);
                ret = JsonConvert.SerializeObject(formCard);
            }
            return ret;
        }

        /// <summary>
        /// 取得 已簽核表單 json.
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="userId">登入者</param>
        /// <param name="days">日數</param>
        /// <returns></returns>
        public string GetSignedFormsJson(string empNo, string userId, int days)
        {
            string ret = "[]";
            if (CanSeeInbox(empNo, userId))
            {
                ret = JsonConvert.SerializeObject(GetSignedForms(empNo, days));
            }
            return ret;
        }

        /// <summary>
        /// 取得 已簽核表單 json.
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="userId">登入者</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <returns></returns>
        public string GetSignedFormsJson(string empNo, string userId, DateTime startDate, DateTime endDate)
        {
            string ret = "[]";
            if (CanSeeInbox(empNo, userId))
            {
                ret = JsonConvert.SerializeObject(GetSignedForms(empNo, startDate, endDate));
            }
            return ret;
        }

        /// <summary>
        /// 取得 已簽核表單 json.，以申請內容日期為準
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="userId">登入者</param>
        /// <param name="startDate">申請內容啟始日期</param>
        /// <param name="endDate">申請內容結束日期</param>
        /// <returns></returns>
        public string GetSignedFormsJsonByContentDate(string empNo, string userId, DateTime startDate, DateTime endDate, DateTime contentStartDate, DateTime contentEndDate)
        {
            string ret = "[]";
            if (CanSeeInbox(empNo, userId))
            {
                ret = JsonConvert.SerializeObject(GetSignedFormsByContentDate(empNo, startDate, endDate, contentStartDate, contentEndDate));
            }
            return ret;
        }

        /// <summary>
        /// 取得使用者前N筆簽核意見
        /// </summary>
        /// <param name="userId">登入使用者</param>
        /// <param name="empNo">員工編號</param>
        /// <param name="count">數量</param>
        /// <returns>簽核意見</returns>
        public List<string> GetTopApproveComments(string userId, string empNo, int count)
        {
            List<string> ret = new List<string>();

            if (userId == empNo || _attendanceBo.IsAdmin(userId))
            {
                ret = _attendanceBo.GetTopApproveComments(empNo, count);
            }
            return ret;
        }

        /// <summary>
        /// 取得特定時間區間內已填表單 JSON，包括他人代填與本人填表，以填表時間為準
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="userId">登入者</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns></returns>
        public string GetUserSentBoxJson(string empNo, string userId, DateTime startDate, DateTime endDate, string? projNo = null, int? status = null)
        {
            string ret = "[]";
            if (CanSeeInbox(empNo, userId))
            {
                ret = JsonConvert.SerializeObject(GetUserSentBox(empNo, startDate, endDate, projNo, status));
            }
            return ret;
        }

        /// <summary>
        /// 取得特定時間區間內已填表單 JSON，包括他人代填與本人填表，以內容時間為準
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="userId">登入者</param>
        /// <param name="startDate">啟始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns></returns>
        public string GetUserSentBoxJsonByContentDate(string empNo, string userId, DateTime startDate, DateTime endDate, string? projNo = null, int? status = null)
        {
            string ret = "[]";
            if (CanSeeInbox(empNo, userId))
            {
                ret = JsonConvert.SerializeObject(GetUserSentBoxByContentDate(empNo, startDate, endDate, projNo, status));
            }
            return ret;
        }

        /// <summary>
        /// 取得特定月份已填表單，包括他人代填與本人填表，依表單年月為準，非填表時間
        /// </summary>
        /// <param name="cardBoFactory">CardBo Factory</param>
        /// <param name="empNo">員工編號</param>
        /// <param name="userId">登入者</param>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <returns></returns>
        public string GetUserSentBoxYearMonthJson(ICardBoFactory cardBoFactory, string empNo, string userId, int year, int month)
        {
            string ret = "[]";
            if (CanSeeInbox(empNo, userId))
            {
                ret = JsonConvert.SerializeObject(GetUserSentFormCardsYearMonth(cardBoFactory, empNo, year, month));
            }
            return ret;
        }

        /// <summary>
        /// 取得特定月份已填表單及卡，包括他人代填與本人填表，依表單年月為準，非填表時間
        /// </summary>
        /// <param name="cardBoFactory">三卡商業元件</param>
        /// <param name="empNo">填表員工編號</param>
        /// <param name="year">表單年</param>
        /// <param name="month">表單月</param>
        /// <returns></returns>
        public List<FormCard> GetUserSentFormCardsYearMonth(ICardBoFactory cardBoFactory, string empNo, int year, int month)
        {
            List<FormCard> formCards = new List<FormCard>();
            List<FormType> formTypes = _formTypeBo.GetFormTypes();
            foreach (FormType formType in formTypes)
            {
                ICardBaseBo? cardBo = cardBoFactory.GetCardBo(formType.FormID);
                if (cardBo != null)
                {
                    List<FormCard> formCardDtos = cardBo.GetUserSentFormCardsYearMonth(empNo, year, month);
                    foreach (FormCard formCard in formCardDtos)
                    {
                        formCards.Add(formCard);
                    }
                }
            }
            formCards.Sort((x, y) => x.FormInfo.CompareTo(y.FormInfo));
            return formCards;
        }

        /// <summary>
        /// 標註某員工及其對應之角色的所有未讀通知為已讀
        /// </summary>
        /// <param name="logonUserId">登入員工</param>
        /// <param name="userId">被通知的員工編號</param>
        /// <returns>有一筆以上的通知被標註已讀則回傳true，否則回傳false</returns>
        public bool MarkDeliveredNotifications(string logonUserId, string userId)
        {
            bool ret = false;
            if (logonUserId == userId)
            {
                List<Role> roles = _sinoSignBo.GetUserFormalRoles(userId);
                ret = _attendanceBo.MarkDeliveredNotifications(roles);
            }
            return ret;
        }

        /// <summary>群簽</summary>
        /// <param name="approveList">簽核資料物件 List</param>
        /// <param name="cardBoFactory">三卡商業物件工廠</param>
        /// <param name="userId">簽核員工編號</param>
        /// <returns>簽核成功與否</returns>
        public List<string> MultipleApprove(List<Approve> approveList, ICardBoFactory cardBoFactory, string userId)
        {
            List<string> ret = new List<string>();
            // 計算 approveList 的 hash code, 用來檢查是否重覆送出
            string jsonString = JsonConvert.SerializeObject(approveList);
            string approveHashCode = CardUtility.ComputeMD5Hash(jsonString);
            DateTime now = DateTime.Now;
            double timeDiff = (now - _lastMultiApproveTime).TotalSeconds;
            //檢查是否重覆送出
            if (approveHashCode == _lastMultiApproveHashCode && timeDiff <= HASH_CODE_EXPIRE_SECONDS)
            {
                ret.Add("重覆送出群簽");
                return ret;
            }
            _lastMultiApproveHashCode = approveHashCode;
            _lastMultiApproveTime = now;

            foreach (Approve approveDto in approveList)
            {
                ICardBaseBo? cardBo = cardBoFactory.GetCardBo(approveDto.FormID);
                if (cardBo != null)
                {
                    string errorMessage = Approve(approveDto, cardBo, userId);
                    if (errorMessage != string.Empty)
                    {
                        Form form = GetForm(approveDto.FormUID);
                        ret.Add($"{form.FormSubject} 簽核失敗:{errorMessage}");
                    }
                }
            }
            return ret;
        }

        /// <summary>抽單</summary>
        /// <param name="form">表單</param>
        /// <param name="cardBo">三卡商業物件</param>
        /// <param name="card">三卡</param>
        /// <param name="withdraw">抽單物件</param>
        /// <returns>抽單成功傳回空值，失敗傳回錯誤訊息</returns>
        public string Withdraw(Form form, ICardBaseBo cardBo, CardBase card, Withdraw withdraw)
        {
            string errorMessage = string.Empty;

            //建立 withdraw 的 hash code, 用來檢查 withdraw 是否重覆發送
            string jsonString = JsonConvert.SerializeObject(withdraw);
            string withdrawHashCode = CardUtility.ComputeMD5Hash(jsonString);

            DateTime now = DateTime.Now;
            double timeDiff = (now - _lastWithdrawTime).TotalSeconds;
            // 檢查 withdrawHashCode 是否與前一次相同
            if (_lastWithdrawHashCode == withdrawHashCode && timeDiff <= HASH_CODE_EXPIRE_SECONDS)
            {
                _logger.LogWarning("偵測到重複送出的抽單: {WithdrawHashCode}", withdrawHashCode);
                return "重複送出抽單";
            }

            // 更新最後一次的 hash code
            _lastWithdrawHashCode = withdrawHashCode;
            _lastWithdrawTime = now;


            if (form.FormStatus != (int)FormStatus.Processing)
            {
                if (form.FormStatus == (int)FormStatus.Withdraw)
                {
                    errorMessage = "表單已抽單，無法抽單";
                }
                else // FormStatus 是 Agree 或 Deny
                {
                    errorMessage = "表單已簽核，無法抽單";
                }
                _logger.LogError("Action: {Action} 發生錯誤，單號{FormNo}，抽單人{EmpNo}，錯誤訊息:{ErrorMessage}，原始資料:{Withdraw}", nameof(Withdraw),
                form.FormNo, withdraw.WithdrawEmpNo, errorMessage, JsonConvert.SerializeObject(withdraw));
            }
            else if (CanWithdraw(form, cardBo, card, withdraw))
            {
                errorMessage = cardBo.CanWithdraw(card);
                if (errorMessage == string.Empty)
                {
                    withdraw.WithdrawName = _employeeBo.GetEmployeeName(withdraw.WithdrawEmpNo);
                    form.UpdatedEmpNo = withdraw.WithdrawEmpNo;
                    form.UpdatedName = withdraw.WithdrawName;
                    form.UpdatedTime = withdraw.WithdrawTime;
                    form.UpdatedIP = withdraw.WithdrawIP;
                    form.UpdatedHost = withdraw.WithdrawHost;

                    form.FormStatus = (int)FormStatus.Withdraw;
                    form.EndTime = withdraw.WithdrawTime;

                    // Card
                    if (card != null)
                    {
                        card.UpdatedEmpNo = withdraw.WithdrawEmpNo;
                        card.UpdatedName = withdraw.WithdrawName;
                        card.UpdatedTime = withdraw.WithdrawTime;
                        card.UpdatedIP = withdraw.WithdrawIP;
                        card.UpdatedHost = withdraw.WithdrawHost;

                        cardBo.Withdraw(card, withdraw);

                        try
                        {
                            List<FormFlow> flows = new List<FormFlow>();
                            DataSet dataSet = PrepareDataSet(form, flows, cardBo, card, false, null);
                            //呼叫FormDao寫入DB
                            errorMessage = _attendanceBo.UpdateForm(form.FormID, dataSet);
                            _logger.LogInformation("Action: {Action}, 單號 {FormNo} {FormUID}",
                                nameof(Withdraw), form.FormNo, form.FormUID);
                        }
                        catch (Exception ex)
                        {
                            string strForm = JsonConvert.SerializeObject(form);
                            _logger.LogError(ex, "Action: {Action} 發生錯誤，內容{StrForm}，錯誤訊息:{Message}", nameof(Withdraw), strForm, ex.Message);
                            Console.WriteLine(ex.Message);
                            //隱藏真實錯誤訊息 原應為 ex.Message;
                            errorMessage = AttendanceParameters.WithdrawErrorMessage;
                        }
                    }
                }
            }
            else //無抽單權限
            {
                errorMessage = AttendanceParameters.WithdrawNoRightError;
            }
            return errorMessage;
        }

    }
}
