﻿using System;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 簽核DTO
    /// </summary>
    public class Approve
    {

        /// <summary>
        /// 卡名
        /// </summary>
        /// <value>
        /// A1Card、B1Card、B1CardApp、C1Card
        /// </value>
        public string FormID { get; set; } = string.Empty;

        /// <summary>
        /// 流程UUID
        /// </summary>
        public Guid FlowUID { get; set; }

        /// <summary>
        /// 表單UUID
        /// </summary>
        public Guid FormUID { get; set; }

        /// <summary>
        /// 簽核人員工編號
        /// </summary>
        public string ApproverEmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 簽核人姓名
        /// </summary>
        public string ApproverName { get; set; } = string.Empty;

        /// <summary>
        /// 簽核人部門編號
        /// </summary>
        public int ApproverDeptNo { get; set; }

        /// <summary>
        /// 簽核人部門簡稱
        /// </summary>
        public string ApproverDeptSName { get; set; } = string.Empty;

        /// <summary>
        /// 簽核人組別編號
        /// </summary>
        public int? ApproverTeamID { get; set; }

        /// <summary>
        /// 簽核人組別中文名
        /// </summary>
        public string? ApproverTeamCName { get; set; }

        /// <summary>
        /// 核可日期時間
        /// </summary>
        public DateTime ApproveTime { get; set; }

        /// <summary>
        /// 核可IP
        /// </summary>
        public string? ApproveIP { get; set; }

        /// <summary>
        /// 核可主機名
        /// </summary>
        public string? ApproveHost { get; set; }

        /// <summary>
        /// 是否為代理人簽核
        /// </summary>
        public bool IsAgentApprove { get; set; }

        /// <summary>
        /// 流程狀態
        /// </summary>
        public int FlowStatus { get; set; }

        /// <summary>
        ///   流程是否為通知
        /// </summary>
        public bool IsNotification { get; set; }


        /// <summary>
        /// 簽核意見
        /// </summary>
        public string Comment { get; set; } = string.Empty;
    }
}
