﻿using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves;
using Sinotech.Mis.HR.Attendance.C1CardObjs;
using Sinotech.Mis.HR.Attendance.Common;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves.Tests
{
    public class OfficialLeaveTests : TestC1CardBase
    {
        public OfficialLeaveTests()
        {
            #region Prepare Data

            _c1Card.LeaveNumber = LeaveKindEnum.OfficialLeave;

            #endregion
        }

        [Fact]
        public void TestCanTakeThisLeave()
        {
            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CanTakeThisLeave();

            Assert.Equal(OfficialLeave.CodeOk, result.Code);
        }

        [Fact]
        public void TestExceedQuota()
        {
            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CheckOverPermittedLeaveHours();

            Assert.Equal(OfficialLeave.CodeOk, result.Code);
        }

        [Theory]
        [InlineData(null, OfficialLeave.CodeReasonFieldRequired)]
        [InlineData("",   OfficialLeave.CodeReasonFieldRequired)]
        [InlineData(" ",   OfficialLeave.CodeReasonFieldRequired)]
        [InlineData(" \t",   OfficialLeave.CodeReasonFieldRequired)]
        [InlineData("Reason", OfficialLeave.CodeOk)]
        public void TestCheckRequiredFields(string reason, int expectedCode)
        {
            _c1Card.Reason = reason;
            var card = C1CardFactory.CreateLeave(_c1Card, _c1CardBo);
            var result = card.CheckRequiredFields();

            Assert.Equal(expectedCode, result.Code);
        }

        [Theory]
        [InlineData(Gender.Female, true)]
        [InlineData(Gender.Male, true)]
        public void TestGender(Gender gender, bool expected)
        {
            var result = OfficialLeave.IsAllowForThisGender(gender);
            Assert.Equal(expected, result);
        }
    }
}