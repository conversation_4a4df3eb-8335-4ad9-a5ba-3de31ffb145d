﻿using System;
using System.Data;

namespace Sinotech.Mis.HR.Attendance.DataAccess.Interfaces
{
    public interface IB1CardDao
    {

        /// <summary>
        /// 取得加班卡
        /// </summary>
        /// <param name="date">加班日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>指定日期之加班卡DataTable</returns>
        public DataTable GetB1Card(DateTime date, string empNo);

        /// <summary>
        /// 取得 表單關係人 某月份加班單
        /// </summary>
        /// <param name="empNo">表單關係人 員工編號</param>
        /// <param name="date">日期</param>
        /// <param name="status">表單狀態</param>
        /// <returns>已填表單</returns>
        public DataTable GetB1CardMonth(string empNo, DateTime date, int? status = null);

        /// <summary>
        /// 取得B1Cards
        /// </summary>
        /// <param name="formUID">表單識別碼</param>
        public DataTable GetB1Cards(Guid formUID);

        /// <summary>
        /// 依日期區間取得所有加班卡
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public DataTable GetB1Cards(DateTime startDate, DateTime endDate);

        /// <summary>
        /// 依日期區間取得某計畫所有加班卡
        /// </summary>
        /// <param name="startDate">最早填卡日期</param>
        /// <param name="endDate">最晚填卡日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns></returns>
        public DataTable GetB1Cards(DateTime startDate, DateTime endDate, string projNo);

        /// <summary>
        /// 依日期區間取得所有表單及加班卡
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public DataTable GetB1CardsForms(DateTime startDate, DateTime endDate);

        /// <summary>
        /// 依日期區間取得某計畫所有表單及加班卡
        /// </summary>
        /// <param name="startDate">最早填卡日期</param>
        /// <param name="endDate">最晚填卡日期</param>
        /// <param name="projNo">計畫編號</param>
        /// <returns></returns>
        public DataTable GetB1CardsForms(DateTime startDate, DateTime endDate, string projNo);

        /// <summary>
        /// 取得加班類型列表
        /// </summary>
        /// <returns></returns>
        public DataTable GetB1CardTypes();

        /// <summary>
        /// 取得 填表人/表單關係人 某月份已填加班單
        /// </summary>
        /// <param name="empNo">填表人/表單關係人 員工編號</param>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <param name="status">表單狀態</param>
        /// <returns>已填表單</returns>
        public DataTable GetSentB1CardYearMonth(string empNo, int year, int month, int? status = null);

        /// <summary>
        /// 是否已填該日的加班卡，也就是 B1_Status 為1或2，會Join Form
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns>表單及加班卡DataTable</returns>
        public DataTable IsFilledB1Card(DateTime date, string empNo);

        /// <summary>
        /// 是否已填該日的加班卡，也就是 B1_Status 為1或2
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="empNo">員工編號</param>
        /// <returns></returns>
        public bool IsFilled(DateTime date, string empNo);

    }
}
