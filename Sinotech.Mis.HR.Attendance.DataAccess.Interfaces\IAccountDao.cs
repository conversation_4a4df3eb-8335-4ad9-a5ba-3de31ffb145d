﻿using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Data;

namespace Sinotech.Mis.HR.Attendance.DataAccess.Interfaces
{
    public interface IAccountDao
    {

        /// <summary>
        /// 取得登入失敗次數
        /// </summary>
        /// <param name="accountId"></param>
        /// <returns>15分鐘內 登入失敗次數</returns>
        public int GetLoginFailCount(string accountId);

        /// <summary>
        /// 取得某帳號登入失敗資料
        /// </summary>
        /// <param name="accountId">帳號</param>
        /// <returns>登入失敗 DataRow</returns>
        public DataRow? GetLoginFailRecord(string accountId);


        /// <summary>
        /// 增加登入記錄
        /// </summary>
        /// <param name="logInOutDto"></param>
        public bool InsertLoginRecord(AccountLogInOutDto logInOutDto);

        /// <summary>
        /// 記錄帳號登入成功
        /// </summary>
        /// <param name="logInOutDto"></param>
        /// <returns></returns>
        public bool LogAccountLogin(AccountLogInOutDto logInOutDto);

        /// <summary>
        /// 記錄帳號登入失敗
        /// </summary>
        /// <param name="logInOutDto"></param>
        /// <param name="firstLoginTime"></param>
        /// <param name="firstLockTime"></param>
        /// <param name="failCount"></param>
        /// <returns></returns>
        public bool LogAccountLoginFail(AccountLogInOutDto logInOutDto, DateTime firstLoginTime, DateTime? firstLockTime, int failCount);

        /// <summary>
        /// 記錄帳號登出
        /// </summary>
        /// <param name="logInOutDto"></param>
        /// <returns></returns>
        public bool LogAccountLogout(AccountLogInOutDto logInOutDto);

        /// <summary>
        /// 記錄帳號解鎖
        /// </summary>
        /// <param name="logInOutDto"></param>
        /// <returns></returns>
        public bool LogAccountUnlock(AccountLogInOutDto logInOutDto);

    }
}
