﻿using Sinotech.Mis.Common;
using Sinotech.Mis.HR.Attendance.BusinessLogic.B1CardObjs.PositionType;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.Tests
{
    [ExcludeFromCodeCoverage]
    public class SpecialStaffTests
    {
        private readonly IB1CardDataProvider _provider;

        public SpecialStaffTests()
        {
            _provider = new MockB1CardDataProvider();
        }

        [Fact]
        public void IsOvertimeAllowed_ShouldReturnTrue()
        {
            var specialStaff = new SpecialStaff("12345", DateTime.Now, _provider);
            Assert.True(specialStaff.IsOvertimeAllowed);
        }

        //[Fact]
        //public void HasAppliedOvertimeWork_ShouldReturnDefault()
        //{
        //    var specialStaff = new SpecialStaff("12345", DateTime.Now, _provider);
        //    Assert.True(specialStaff.HasAppliedOvertimeWork());
        //}

        //[Fact]
        //public void CheckAppliedHours_ShouldReturnDefault()
        //{
        //    var specialStaff = new SpecialStaff("12345", DateTime.Now, _provider);
        //    var result = specialStaff.CheckAppliedHours();
        //    Assert.Equal(B1CardStatusEnum.Success, result.Status);
        //}

        //[Fact]
        //public void CheckDailyOvertimeHours_ShouldReturnDefault()
        //{
        //    var specialStaff = new SpecialStaff("12345", DateTime.Now, _provider);
        //    var result = specialStaff.CheckDailyOvertimeHours();
        //    Assert.Equal(B1CardStatusEnum.Success, result.Status);
        //}

        //[Fact]
        //public void CheckMonthlyOvertimeHours_ShouldReturnErrorWhenExceedsLimit()
        //{
        //    var specialStaff = new SpecialStaff("12345", DateTime.Now, _provider);
        //    var result = specialStaff.CheckMonthlyOvertimeHours();
        //    Assert.Equal(B1CardStatusEnum.Error, result.Status);
        //    Assert.Equal(538, result.Code);
        //    Assert.Equal("【加班】時數已達上限", result.Message);
        //}

        //[Fact]
        //public void CheckQuarterlyOvertimeHours_ShouldReturnDefault()
        //{
        //    var specialStaff = new SpecialStaff("12345", DateTime.Now, _provider);
        //    var result = specialStaff.CheckQuarterlyOvertimeHours();
        //    Assert.Equal(B1CardStatusEnum.Success, result.Status);
        //}

        //[Fact]
        //public void GetWarningResult_ShouldReturnDefault()
        //{
        //    var specialStaff = new SpecialStaff("12345", DateTime.Now, _provider);
        //    var result = specialStaff.GetWarningResult();
        //    Assert.Equal(B1CardStatusEnum.Warning, result.Status);
        //}

        public string EmployeeNumber => "12345";
        public DateTime OvertimeDate => DateTime.Now;

        public int GetAppliedOvertimeHourInQuota() => 0;
        public B1CardApp? GetB1CardApp() => null;
        public B1Card GetB1Card() => new B1Card();
        public double GetCurrentMonthlyWeightedOvertimeHours() => 100;
        public bool GetHasAppliedOvertimeWork() => true;
        public bool GetHasB1CardFilled() => true;
        public int GetMonthOvertimeHourInQuota() => 0;
        public Workday GetOverTimeDateInfo() => new Workday();
        public B1CardPositionEnum GetPositionType() => B1CardPositionEnum.Driver;
        public List<Project> GetProjectList() => new List<Project>();
        public Project? GetProjectInfo(string projectNumber) => null;
        public int GetQuarterOvertimeHourInQuota() => 0;
        public double GetSpecialStaffAllowedMonthlyWeightedOvertimeHours() => 120;
        public double GetWeightedOvertimeHours() => 30;
        public bool IsSpecialStaff() => true;
    }
}
