﻿using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Sinotech.Mis.Extensions.Configuration;
using Sinotech.Mis.Helpers;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.Tests;
using Sinotech.Mis.HR.Attendance.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using System.Threading;
using System.Threading.Tasks;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.Tests
{
    [ExcludeFromCodeCoverage]
    public class A1CardBoTests
    {

        private readonly A1CardBo _a1CardBo;
        private readonly ICardChecker _cardChecker;
        private readonly FormBo _formBo;
        private readonly FormFlowBo _formFlowBo;
        private readonly string ConnectionStringAttendance;

        public A1CardBoTests(A1CardBo a1CardBo, FormBo formBo, FormFlowBo formFlowBo, ICardChecker cardChecker)
        {
            _a1CardBo = a1CardBo;
            IConfiguration Configuration = new ConfigurationBuilder().
            AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).Build();
            ConnectionStringAttendance = Configuration.GetSecuredConnectionString("Attendance");
            _formBo = formBo;
            _formFlowBo = formFlowBo;
            _cardChecker = cardChecker;
            TestHelper.ClearData(ConnectionStringAttendance);
        }

        private static async void AddA1Card(object obj)
        {
            A1CardBo a1CardBo = (A1CardBo)obj;
            A1Card a1Card = new A1Card();
            a1Card.FormUID = Guid.Empty;
            a1Card.A1_EMPNO = "0349";
            a1Card.A1_YYMM = "11103";
            a1Card.A1_NN = '2';
            a1Card.A1_SOURCE = "Attendance";
            a1Card.A1_WYYMMDD = "1110727";
            a1Card.A1_STATUS = 0;
            a1Card.AddSigners = "0274";
            a1Card.A1_WDate = DateTime.Parse("2022-07-26T23:53:33.913");
            a1Card.FilledTime = DateTime.Parse("2022-07-26T23:54:48.440");
            a1Card.CreatedTime = DateTime.Parse("2022-07-26T23:55:48.440");
            a1Card.UpdatedTime = null;
            A1CardDetail detail = new A1CardDetail();
            detail.A1_PROJNO = "TI21508";
            detail.A1_DDHH = "8007081100";
            detail.A1_SERIALNO = "1";
            detail.A1_HOUR = 25;
            a1Card.Details.Add(detail);
            detail = new A1CardDetail();
            detail.A1_PROJNO = "RP19553";
            detail.A1_DDHH = "0001007700";
            detail.A1_HOUR = 15;
            detail.A1_SERIALNO = "2";
            a1Card.Details.Add(detail);

            string creatorId = "0395";
            string ipAddress = "***********";
            string hostname = "03-769.secinc";

            Guid? guid;
            string errorMessage;
            (guid, errorMessage) = await a1CardBo.AddA1Card(creatorId, a1Card, ipAddress, hostname);
        }

        [Fact]
        public async Task AddA1CardTest()
        {
            A1Card a1Card = new A1Card();
            a1Card.FormUID = Guid.Empty;
            a1Card.A1_EMPNO = "2268";
            a1Card.A1_YYMM = "11103";
            a1Card.A1_NN = '2';
            a1Card.A1_SOURCE = "Attendance";
            a1Card.A1_WYYMMDD = "1110727";
            a1Card.A1_STATUS = 0;
            a1Card.AddSigners = "0274";
            a1Card.A1_WDate = new DateTime(2022,7, 26, 23, 53, 33, 913, DateTimeKind.Local);
            a1Card.FilledTime = new DateTime(2022, 7, 26, 23, 55, 48, 440, DateTimeKind.Local);
            a1Card.CreatedTime = new DateTime(2022, 7, 26, 23, 55, 48, 440, DateTimeKind.Local);
            a1Card.UpdatedTime = null;
            A1CardDetail detail = new A1CardDetail();
            detail.A1_PROJNO = "TI21508";
            detail.A1_DDHH = "8007081100";
            detail.A1_SERIALNO = "1";
            detail.A1_HOUR = 25;
            a1Card.Details.Add(detail);
            detail = new A1CardDetail();
            detail.A1_PROJNO = "RP19553";
            detail.A1_DDHH = "0001007700";
            detail.A1_HOUR = 15;
            detail.A1_SERIALNO = "2";
            a1Card.Details.Add(detail);

            string creatorId = "2268";
            string ipAddress = "***********";
            string hostname = "03-769.secinc";

            Guid? guid;
            string errorMessage;
            (guid, errorMessage) = await _a1CardBo.AddA1Card(creatorId, a1Card, ipAddress, hostname);
            Assert.NotNull(guid);
            Assert.Empty(errorMessage);
            Assert.Equal("80070811000", a1Card.Details[0].A1_DDHH);
            Assert.Equal("00010077000", a1Card.Details[1].A1_DDHH);
            string sql = "SELECT TOP 1 FormUID FROM Form;";
            SqlHelper.GetFieldValue(ConnectionStringAttendance, sql);
            Guid formUID = (Guid)SqlHelper.GetFieldValue(ConnectionStringAttendance, sql);
            Form form = _formBo.GetForm(formUID);
            Assert.NotNull(form);
            string sRocYear = CardUtility.RocChineseYearString(DateTime.Now);
            Assert.Equal($"旬111000001", form.FormNo);
            Assert.Equal("正常工作卡-孫睿宏-111年03月中旬", form.FormSubject);
            Assert.Equal("2268", form.EmpNo);
            Assert.Equal("孫睿宏", form.EmpName);
            Assert.Equal(4, form.DeptNo);
            Assert.Equal("企劃處", form.DeptSName);
            Assert.Null(form.TeamID);
            Assert.Null(form.TeamCName);
            DateTime createdTime = (DateTime)a1Card.CreatedTime;
            DateTime filledTime = a1Card.A1_WDate;
            createdTime = createdTime.ToLocalTime();
            filledTime = filledTime.ToLocalTime();
            Assert.Equal(guid, form.FormUID);
            Assert.Equal(createdTime, form.CreatedTime);
            Assert.Equal(filledTime, form.FilledTime);
            Assert.Equal(creatorId, form.CreatedEmpNo);
            Assert.Equal(ipAddress, form.CreatedIP);
            Assert.Equal(a1Card.AddSigners, form.AddedSigner);
            Assert.Null(form.EndTime);
            Assert.Equal(2, form.TotalSteps);
            Assert.Equal(1, form.CurrentStep);

            Assert.Equal(a1Card.UpdatedEmpNo, form.UpdatedEmpNo);
            Assert.Equal(a1Card.UpdatedIP, form.UpdatedIP);
            Assert.Equal(a1Card.UpdatedTime, form.UpdatedTime);

            List<FormFlow> flows = _formFlowBo.GetFormFlows(form.FormUID);
            Assert.Equal(2, flows.Count);
            Assert.Equal(a1Card.AddSigners, flows[0].RecipientEmpNo);
            Assert.Equal("0274", flows[0].RecipientEmpNo);
            Assert.Equal("薛強", flows[0].RecipientName);
            Assert.Equal("加會人員", flows[0].FlowName);
            Assert.Equal((int)FlowStatus.Processing, flows[0].FlowStatus);
            Assert.False(flows[0].IsNotification);
            Assert.Equal(1, flows[0].Step);

            Assert.Equal("L10", flows[1].RecipientEmpNo);
            Assert.Equal("主辦部門主管", flows[1].FlowName);
            Assert.Equal((int)FlowStatus.NotSend, flows[1].FlowStatus);
            Assert.False(flows[1].IsNotification);
            Assert.Equal(2, flows[1].Step);

            a1Card = (A1Card)_a1CardBo.GetCard(form.FormUID);
            Assert.Equal(2, a1Card.Details.Count);

            //第二輪測試
            string jsonString = "[{\"Name\":\"A1Card\",\"FormUID\":\"00000000-0000-0000-0000-000000000000\",\"A1_EMPNO\":\"0395\",\"A1_YYMM\":\"11103\",\"A1_NN\":\"3\",\"A1_PROJNO\":\"RP19553\",\"A1_EXPNO\":\"\",\"A1_ITEMNO\":\"01\",\"A1_DDHH\":\"06570800606\",\"A1_HOUR\":38,\"A1_WYYMMDD\":\"1110728\",\"A1_AYYMMDD\":null,\"A1_STATUS\":0,\"A1_SHEETNO\":null,\"A1_SERIALNO\":null,\"A1_SOURCE\":\"Attendance\",\"AddSigners\":\"\",\"A1_WDate\":\"2022-07-29T03:29:10.00Z\",\"CreatedTime\":\"2022-07-29T03:33:33.666Z\"},{\"Name\":\"A1Card\",\"FormUID\":\"00000000-0000-0000-0000-000000000000\",\"A1_EMPNO\":\"0395\",\"A1_YYMM\":\"11103\",\"A1_NN\":\"3\",\"A1_PROJNO\":\"RP19555\",\"A1_EXPNO\":\"\",\"A1_ITEMNO\":\"01\",\"A1_DDHH\":\"02318000282\",\"A1_HOUR\":26,\"A1_WYYMMDD\":\"1110728\",\"A1_AYYMMDD\":null,\"A1_STATUS\":0,\"A1_SHEETNO\":null,\"A1_SERIALNO\":null,\"A1_SOURCE\":\"Attendance\",\"AddSigners\":\"\",\"A1_WDate\":\"2022-07-29T03:29:10.00Z\",\"CreatedTime\":\"2022-07-29T03:33:33.666Z\"}]";
            List<A1Card> cardsInput = JsonConvert.DeserializeObject<List<A1Card>>(jsonString);
            detail = new A1CardDetail();
            detail.A1_PROJNO = "RP19553";
            detail.A1_DDHH = "06570800606";
            detail.A1_SERIALNO = "1";
            detail.A1_HOUR = 38;
            cardsInput[0].Details.Add(detail);
            detail = new A1CardDetail();
            detail.A1_PROJNO = "RP19555";
            detail.A1_DDHH = "02318000282";
            detail.A1_HOUR = 26;
            detail.A1_SERIALNO = "2";
            cardsInput[0].Details.Add(detail);

            Assert.Equal(2, cardsInput.Count);
            Assert.Equal('3', cardsInput[0].A1_NN);
            Assert.Equal('3', cardsInput[1].A1_NN);
            Assert.Equal("0395", cardsInput[0].A1_EMPNO);
            Assert.Equal(0, cardsInput[0].A1_STATUS);
            Assert.Equal(0, cardsInput[1].A1_STATUS);
            Assert.Equal("RP19553", cardsInput[0].Details[0].A1_PROJNO);
            Assert.Equal("RP19555", cardsInput[0].Details[1].A1_PROJNO);
            Assert.Equal("1", cardsInput[0].Details[0].A1_SERIALNO);
            Assert.Equal("2", cardsInput[0].Details[1].A1_SERIALNO);
            Assert.Equal("06570800606", cardsInput[0].Details[0].A1_DDHH);
            Assert.Equal("02318000282", cardsInput[0].Details[1].A1_DDHH);
            Assert.Equal(38, cardsInput[0].Details[0].A1_HOUR);
            Assert.Equal(26, cardsInput[0].Details[1].A1_HOUR);

            creatorId = "0178";
            ipAddress = "***********";
            hostname = "VS2018.secinc";
            a1Card = cardsInput[0];
            string result;
            (guid, result) = await _a1CardBo.AddA1Card(creatorId, a1Card, ipAddress, hostname);
            Assert.Null(guid);

            a1Card.Details[0].A1_DDHH = "06570000606";
            (guid, result) = await _a1CardBo.AddA1Card(creatorId, a1Card, ipAddress, hostname);
            // Guid
            Assert.NotNull(guid);
            Assert.NotEqual(guid, Guid.Empty);

            //處理後Status即更新
            Assert.Equal((int)FormStatus.Agree, cardsInput[0].A1_STATUS);

            jsonString = @"{""Name"":""A1Card"",""A1_EMPNO"":""0395"",""A1_YYMM"":""11202"",""A1_NN"":""3"",
""A1_WYYMMDD"":""1120315"",""A1_AYYMMDD"":null,""A1_STATUS"":0,
""A1_SHEETNO"":null,""A1_SOURCE"":""Attendance"",
""A1_WDate"":""2023-03-15T14:57:59.863+08:00"",""A1_ADate"":null,
""Details"":[{""ID"":null,""A1_PROJNO"":""RP19553"",
""A1_DDHH"":""88880000"",""A1_HOUR"":32,""A1_SERIALNO"":null}],
""Details"":[{""ID"":null,""A1_PROJNO"":""RP19555"",
""A1_DDHH"":""00000000"",""A1_HOUR"":0,""A1_SERIALNO"":null}],
""FormUID"":""00000000-0000-0000-0000-000000000000"",
""UpdatedEmpNo"":null,""UpdatedName"":null,
""UpdatedTime"":null,""UpdatedIP"":null,
""UpdatedHost"":null,""AddSigners"":"""",
""CreatedTime"":""2023-03-15T14:57:59.863+08:00"",
""FilledTime"":""2023-03-15T14:57:26.08+08:00"",
""UploadedFiles"":null,""RemindSigner"":false,""RemindMessageType"":0,
""RemindMessage"":null}";
            a1Card = JsonConvert.DeserializeObject<A1Card>(jsonString);

            (guid, result) = await _a1CardBo.AddA1Card(creatorId, a1Card, ipAddress, hostname);

            // Guid
            Assert.Null(guid);
            Assert.Equal("計畫編號：RP19555 工時有誤，每旬總工時須大於 0 小時", result);
        }

        [Fact]
        public void AddCardMultiThread()
        {
            // 創建3個執行緒物件
            Thread thread1 = new Thread(new ParameterizedThreadStart(AddA1Card));
            Thread thread2 = new Thread(new ParameterizedThreadStart(AddA1Card));
            Thread thread3 = new Thread(new ParameterizedThreadStart(AddA1Card));
            // 啟動執行緒
            thread1.Start(_a1CardBo);
            thread2.Start(_a1CardBo);
            thread3.Start(_a1CardBo);
            // 等待所有執行緒結束
            thread1.Join();
            thread2.Join();
            thread3.Join();
            string strSql = "SELECT ID FROM A1Card;";
            DataTable dt = SqlHelper.GetDataTable(ConnectionStringAttendance, strSql);
            Assert.Equal(2, dt.Rows.Count);
        }

        [Fact]
        public void AddCardTest()
        {
            Form form = new Form();
            A1Card card = new A1Card();
            bool bl = _a1CardBo.AmendCard(form, card);
            Assert.True(bl);
        }

        [Fact]
        public void CheckDataTest()
        {
            A1Card a1Card = new A1Card();
            a1Card.FormUID = Guid.Empty;
            a1Card.A1_EMPNO = "0349";
            a1Card.A1_YYMM = "11103";
            a1Card.A1_NN = '2';
            a1Card.A1_SOURCE = "Attendance";
            a1Card.A1_WYYMMDD = "1110727";
            a1Card.A1_STATUS = 0;
            a1Card.AddSigners = "";
            a1Card.A1_WDate = DateTime.Parse("2022-07-26T23:53:33.913");
            a1Card.CreatedTime = DateTime.Parse("2022-07-26T23:55:32.440");
            a1Card.FilledTime = DateTime.Parse("2022-07-26T23:54:48.440");
            CardCheckResult result = _cardChecker.CheckA1CardAdd(a1Card);
            Assert.Equal(100, result.Code);

            A1CardDetail detail = new A1CardDetail();
            detail.A1_PROJNO = "TI21508";
            detail.A1_DDHH = "0000000000";
            detail.A1_HOUR = 0;
            a1Card.Details.Add(detail);
            result = _cardChecker.CheckA1CardAdd(a1Card);
            Assert.Equal(120, result.Code);
            detail = new A1CardDetail();
            a1Card.Details[0].A1_DDHH = "8107081100";
            a1Card.Details[0].A1_HOUR = 26;
            result = _cardChecker.CheckA1CardAdd(a1Card);
            Assert.Equal(140, result.Code);
            a1Card.Details[0].A1_DDHH = "8007081100";
            a1Card.Details[0].A1_HOUR = 25;
            result = _cardChecker.CheckA1CardAdd(a1Card);
            Assert.Equal(0, result.Code);

            detail.A1_PROJNO = "RP19553";
            detail.A1_DDHH = "0001007700";
            detail.A1_HOUR = 15;
            a1Card.Details.Add(detail);
            result = _cardChecker.CheckA1CardAdd(a1Card);
            Assert.Equal(0, result.Code);

        }

        [Fact]
        public async Task GetFormCardsTest()
        {
            DateTime startDate = new DateTime(2022, 7, 1, 0, 0, 0, DateTimeKind.Local);
            DateTime endDate = new DateTime(2022, 8, 1, 0, 0, 0, DateTimeKind.Local);
            await AddA1CardTest();
            List<FormCard> formCards = _a1CardBo.GetFormCards(startDate, endDate);
            Assert.NotEmpty(formCards);
        }

        [Fact]
        public async Task GetFormTest()
        {
            A1Card a1Card = new A1Card();
            a1Card.FormUID = Guid.Empty;
            a1Card.A1_EMPNO = "0349";
            a1Card.A1_YYMM = "11103";
            a1Card.A1_NN = '2';
            a1Card.A1_SOURCE = "Attendance";
            a1Card.A1_WYYMMDD = "1110727";
            a1Card.A1_STATUS = 0;
            a1Card.AddSigners = "";
            a1Card.A1_WDate = DateTime.Parse("2022-07-26T23:53:33.913");
            a1Card.CreatedTime = DateTime.Parse("2022-07-26T23:55:32.440");
            a1Card.FilledTime = DateTime.Parse("2022-07-26T23:54:48.440");
            A1CardDetail detail = new A1CardDetail();
            detail.A1_PROJNO = "TI21508";
            detail.A1_DDHH = "8007081100";
            detail.A1_HOUR = 25;
            a1Card.Details.Add(detail);
            detail = new A1CardDetail();
            detail.A1_PROJNO = "RP19553";
            detail.A1_DDHH = "0001007700";
            detail.A1_HOUR = 15;
            a1Card.Details.Add(detail);
            string creatorId = "0395";
            string ipAddress = "***********";
            string hostname = "03-769.secinc";

            Guid? guid;
            string errorMessage;
            (guid, errorMessage) = await _a1CardBo.AddA1Card(creatorId, a1Card, ipAddress, hostname);
            Assert.NotNull(guid);
            Assert.Empty(errorMessage);
            Form form = _formBo.GetForm((Guid)guid);
            FormCard formCard = _a1CardBo.GetFormCard(form, "0395");
            Assert.NotNull(formCard.Card);

            //第二輪測試
            a1Card.A1_EMPNO = "0395";
            a1Card.A1_YYMM = "11103";
            a1Card.A1_NN = '3';
            a1Card.A1_STATUS = 0;
            a1Card.A1_ADate = null;
            a1Card.A1_WYYMMDD = "1110728";
            a1Card.AddSigners = "";
            a1Card.A1_WDate = DateTime.Parse("2022-07-29T03:29:10.00Z");
            a1Card.CreatedTime = DateTime.Parse("2022-07-29T03:33:33.666Z");
            a1Card.Details = new List<A1CardDetail>();
            detail = new A1CardDetail();
            detail.A1_PROJNO = "RP19553";
            detail.A1_SERIALNO = "1";
            detail.A1_DDHH = "06570800606";
            detail.A1_HOUR = 38;
            a1Card.Details.Add(detail);
            detail = new A1CardDetail();
            detail.A1_PROJNO = "RP19555";
            detail.A1_SERIALNO = "2";
            detail.A1_DDHH = "02318000282";
            detail.A1_HOUR = 26;
            a1Card.Details.Add(detail);

            creatorId = "0178";
            ipAddress = "***********";
            hostname = null;
            (guid, errorMessage) = await _a1CardBo.AddA1Card(creatorId, a1Card, ipAddress, hostname);
            Assert.NotEmpty(errorMessage);
            errorMessage = string.Empty;
            a1Card.A1_EMPNO = "2024";
            a1Card.Details[0].A1_DDHH = "06570000606";
            (guid, errorMessage) = await _a1CardBo.AddA1Card(creatorId, a1Card, ipAddress, hostname);
            Assert.Equal("2024", a1Card.A1_EMPNO);
            Assert.Empty(errorMessage);

            form = _formBo.GetForm((Guid)guid);
            formCard = _a1CardBo.GetFormCard(form, "2024");
            Assert.NotNull(formCard);
        }

    }
}