﻿using FakeItEasy;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sinotech.Mis.Extensions.Configuration;
using Sinotech.Mis.HR.Attendance.BusinessLogic;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.Tests;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

#nullable enable
namespace Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers.Tests
{

    /// <summary>
    /// A1CardControllerTests 類別的單元測試。
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class A1CardControllerTests
    {

        private readonly IA1CardBo _a1CardBo;
        AttendanceBo _attendanceBo;
        private readonly A1CardController _a1CardController;
        private readonly string ConnectionStringAttendance;
        private readonly IConfiguration _configuration;
        private readonly ICardChecker _cardChecker;
        private readonly ILogger<A1CardController> _logger;

        /// <summary>
        /// 初始化 A1CardControllerTests 類別的新執行個體。
        /// </summary>
        /// <param name="a1CardBo">A1CardBo 物件。</param>
        /// <param name="attendanceBo">AttendanceBo 物件。</param>
        /// <param name="cardChecker">ICardChecker 物件。</param>
        /// <param name="logger">ILogger 物件。</param>
        public A1CardControllerTests(IA1CardBo a1CardBo, AttendanceBo attendanceBo, ICardChecker cardChecker, ILogger<A1CardController> logger)
        {
            _a1CardBo = a1CardBo;
            _attendanceBo = attendanceBo;
            _cardChecker = cardChecker;

            _logger = logger;
            IConfiguration configuration = new ConfigurationBuilder().
                AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).Build();
            _configuration = configuration;
            _a1CardController = new A1CardController(a1CardBo, attendanceBo, cardChecker, configuration, logger);
            ConnectionStringAttendance = configuration.GetSecuredConnectionString("Attendance");
            TestHelper.ClearData(ConnectionStringAttendance);
        }

        [Fact]
        public void A1CardControllerTest()
        {
            IConfiguration configuration = new ConfigurationBuilder().
                AddJsonFile("appsettings.internet.json", optional: true, reloadOnChange: true).Build();
            A1CardController controller = new A1CardController(_a1CardBo, _attendanceBo, _cardChecker, configuration, _logger);
            Assert.NotNull(controller);
        }

        /// <summary>
        /// 設定表單資料。
        /// </summary>
        /// <param name="empNo">員工編號。</param>
        /// <returns>包含 A1CardForm 物件的列表。</returns>
        private static List<A1CardForm> SetFormData(string empNo)
        {
            A1CardForm a1CardForm = new A1CardForm();
            a1CardForm.A1_HOUR = 2;
            a1CardForm.A1_DDHH = "10000000000";
            a1CardForm.A1_EMPNO = empNo;
            a1CardForm.A1_NN = '2';
            a1CardForm.A1_PROJNO = "RP19553";
            a1CardForm.A1_SERIALNO = "1";
            a1CardForm.A1_YYMM = "11102";
            a1CardForm.A1_SOURCE = "Attendance";
            a1CardForm.FilledTime = DateTime.Now;
            a1CardForm.CreatedTime = ((DateTime)a1CardForm.FilledTime).AddMinutes(1);
            a1CardForm.A1_WDate = (DateTime)a1CardForm.CreatedTime;
            a1CardForm.A1_WYYMMDD = "1110228";
            List<A1CardForm> list = new List<A1CardForm>();
            list.Add(a1CardForm);
            return list;
        }

        /// <summary>
        /// 提交表單資料。
        /// </summary>
        /// <param name="empNo">員工編號。</param>
        private async Task Submit(string empNo)
        {
            _a1CardController.ControllerContext.HttpContext = new DefaultHttpContext();
            TestHelper.ClearData(ConnectionStringAttendance);
            List<A1CardForm> list = SetFormData(empNo);
            string result = await _a1CardController.Submit(list);
            Assert.NotEqual(string.Empty, result);
            CardCheckResult expected = AttendanceParameters.ResultBadEmployee;
            CardCheckResult? actual = JsonConvert.DeserializeObject<CardCheckResult>(result);
            Assert.NotNull(actual);
            Assert.Equal(expected.Message, actual.Message);
            _a1CardController.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo(empNo);

            result = await _a1CardController.Submit(list);
            actual = JsonConvert.DeserializeObject<CardCheckResult>(result);
            Assert.NotNull(actual);
            Assert.Equal(string.Empty, actual.Message);
            expected = AttendanceParameters.ResultOk;
            Assert.Equal(expected.Status, actual.Status);

            result = await _a1CardController.Submit(list);
            actual = JsonConvert.DeserializeObject<CardCheckResult>(result);
            Assert.NotNull(actual);
            Assert.False(actual.Status == CardStatusEnum.Error);
        }



        /// <summary>
        /// 測試 IsFilled 方法。
        /// </summary>
        [Fact]
        public async Task IsFilledTest()
        {
            string result = _a1CardController.IsFilled("0395", "11202", '2');
            string ret = "{\"FormUID\":null,\"FormNo\":null,\"IsApproved\":false}";
            Assert.Equal(ret, result);
            TempResult f = JsonConvert.DeserializeObject<TempResult>(result);
            Assert.False(f.IsApproved);
            Assert.True(string.IsNullOrEmpty(f.FormUID));
            Assert.True(string.IsNullOrEmpty(f.FormNo));
            List<A1CardForm> list = SetFormData("2024");
            _a1CardController.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo("2024");
            await _a1CardController.Submit(list);
            result = _a1CardController.IsFilled("2024", "11102", '2');
            TempResult x = JsonConvert.DeserializeObject<TempResult>(result);
            Assert.True(x.IsApproved);
            Assert.NotEmpty(x.FormUID);
            Assert.NotEmpty(x.FormNo);
            // 模擬 A1CardBo.IsFilled 方法，送出 Exception
            var fakeA1CardBo = A.Fake<IA1CardBo>();
            A.CallTo(() => fakeA1CardBo.IsFilled("2024", "11102", '2')).Throws<Exception>();
            A1CardController controller = new(fakeA1CardBo, _attendanceBo, _cardChecker, _configuration, _logger);
            controller.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo("2024");
            result = controller.IsFilled("2024", "11102", '2');
            x = JsonConvert.DeserializeObject<TempResult>(result);
            Assert.False(x.IsApproved);
            Assert.Null(x.FormUID);
        }

        /// <summary>
        /// 測試 Submit 方法。
        /// </summary>
        /// <param name="empNo">員工編號。</param>
        [Theory]
        [InlineData("2268")]
        public void SubmitTest(string empNo)
        {
            _ = Submit(empNo);
        }

        [Fact]
        public void UpdateA1Card_Fail_Test()
        {
            List<A1CardForm> cards = new();
            string retStr = _a1CardController.Update(cards);
            Assert.NotNull(retStr);
            CardCheckResult? result = JsonConvert.DeserializeObject<CardCheckResult>(retStr);
            Assert.NotNull(result);
            Assert.Equal(AttendanceParameters.ResultBadEmployee, result);
        }

        /// <summary>
        /// 測試 UpdateA1Card 方法。
        /// </summary>
        [Fact]
        public void UpdateA1CardTest()
        {
            _a1CardController.ControllerContext.HttpContext = new DefaultHttpContext();
            TestHelper.ClearData(ConnectionStringAttendance);
            List<A1CardForm> cards = SetFormData("2259");
            Assert.True(cards.Any());
            _ = Submit("2259");
            DateTime start = DateTime.Now.AddDays(-1);
            DateTime end = start.AddDays(2);
            List<CardBase> cardBaseList = _a1CardBo.GetCards(start, end);
            Assert.NotNull(cardBaseList);
            Assert.Single(cardBaseList);
            A1Card a1Card = (A1Card)cardBaseList[0];
            Assert.Single(a1Card.Details);
            Assert.Equal(1, a1Card.Details[0].A1_HOUR);
            cards[0].ID = a1Card.Details[0].ID;
            cards[0].FormUID = a1Card.FormUID;
            Assert.Equal(cards[0].A1_PROJNO, a1Card.Details[0].A1_PROJNO);
            Assert.Equal(cards[0].A1_DDHH, a1Card.Details[0].A1_DDHH);

            _a1CardController.ControllerContext.HttpContext = TestHelper.FakeHttpContextEmpNo("2268");
            A1CardForm a1CardForm = cards[0];
            a1CardForm.A1_DDHH = "10007778000";
            Assert.Equal(a1CardForm.A1_DDHH, cards[0].A1_DDHH);

            string retStr = _a1CardController.Update(cards);
            Assert.NotNull(retStr);
            CardCheckResult? result = JsonConvert.DeserializeObject<CardCheckResult>(retStr);
            Assert.NotNull(result);
            Assert.Equal(AttendanceParameters.ResultOk, result);

            cardBaseList = _a1CardBo.GetCards(start, end);
            Assert.NotNull(cardBaseList);
            Assert.Single(cardBaseList);
            a1Card = (A1Card)cardBaseList.First();
            Assert.Equal(30, a1Card.Details[0].A1_HOUR);
        }

        /// <summary>
        /// 臨時結果結構。
        /// </summary>
        public struct TempResult
        {

            public string FormUID;
            public string FormNo;
            public bool IsApproved;

        }

    }
}