﻿using System;
using Xunit;
using FakeItEasy;
using Sinotech.Mis.HR.Attendance.Common;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves.Tests
{
    public class FuneralLeaveTests
    {
        private C1CardBo _c1CardBo;
        private readonly C1Card c1Card = new C1Card();

        public FuneralLeaveTests(C1CardBo c1CardBo)
        {
            c1Card.LeaveNumber = Common.LeaveKindEnum.FuneralLeave;
            _c1CardBo = c1CardBo;
        }

        [Theory]
        [InlineData(0, 0)]
        [InlineData(1, 8)]
        [InlineData(2, 8)]
        [InlineData(3, 8)]
        [InlineData(4, 8)]
        [InlineData(5, 8)]
        [InlineData(6, 8)]
        [InlineData(7, 8)]
        [InlineData(21, 6)]
        [InlineData(22, 6)]
        [InlineData(23, 6)]
        [InlineData(24, 6)]
        [InlineData(25, 6)]
        [InlineData(26, 6)]
        [InlineData(27, 3)]
        [InlineData(28, 3)]
        [InlineData(29, 6)]
        [InlineData(30, 6)]
        [InlineData(31, 6)]
        [InlineData(32, 6)]
        [InlineData(33, 6)]
        [InlineData(34, 6)]
        [InlineData(35, 3)]
        [InlineData(36, 3)]
        [InlineData(51, 3)]
        [InlineData(52, 3)]
        [InlineData(53, 3)]
        [InlineData(54, 3)]
        [InlineData(55, 3)]
        [InlineData(200, 0)]
        public void GetPermittedDaysTest(int leaveSubNumber, int expectedDays)
        {
            c1Card.LeaveSubNumber = leaveSubNumber;
            FuneralLeave leave = new FuneralLeave(c1Card, _c1CardBo);
            int days = leave.GetPermittedDays(leaveSubNumber);
            Assert.Equal(expectedDays, days);
        }

        [Theory]
        [InlineData(6, "2022-01-02", "0000", "2022-01-02", "2022-04-30")]
        [InlineData(6, "2022-02-14", "0000", "2022-02-14", "2022-05-31")]
        [InlineData(6, "2022-03-03", "0000", "2022-03-03", "2022-06-30")]
        [InlineData(6, "2022-04-02", "0000", "2022-04-02", "2022-07-31")]
        [InlineData(6, "2022-05-14", "0000", "2022-05-14", "2022-08-31")]
        [InlineData(6, "2022-06-03", "0000", "2022-06-03", "2022-09-30")]
        [InlineData(6, "2022-07-02", "0000", "2022-07-02", "2022-10-31")]
        [InlineData(6, "2022-08-14", "0000", "2022-08-14", "2022-11-30")]
        [InlineData(6, "2022-09-03", "0000", "2022-09-03", "2022-12-31")]
        [InlineData(6, "2022-10-02", "0000", "2022-10-02", "2023-01-31")]
        [InlineData(6, "2022-11-14", "0000", "2022-11-14", "2023-02-28")]
        [InlineData(6, "2022-12-12", "0000", "2022-12-12", "2023-03-31")]

        public void CalculateLeavePermittedPeriodTest(int leaveSubNumber, DateTime eventDate, string empNo, DateTime expectedStartDate, DateTime expectedEndDate)
        {
            var fakeEmployeeBo = A.Fake<IEmployeeBo>();
            A.CallTo(() => fakeEmployeeBo.IsEmployee(empNo)).Returns(true);
            c1Card.LeaveSubNumber = leaveSubNumber;
            FuneralLeave leave = new FuneralLeave(c1Card, _c1CardBo);
            (DateTime startDate, DateTime endDate) = leave.CalculateLeavePermittedPeriod(eventDate, empNo);
            Assert.Equal(expectedStartDate, startDate);
            Assert.Equal(expectedEndDate, endDate.Date);

            c1Card.RelatedFormNumber = "1234567890";
            leave = new FuneralLeave(c1Card, _c1CardBo);
            (startDate, endDate) = leave.CalculateLeavePermittedPeriod(eventDate, empNo);
            Assert.Equal(expectedStartDate, startDate);
            Assert.Equal(expectedEndDate, endDate.Date);
        }

    }
}