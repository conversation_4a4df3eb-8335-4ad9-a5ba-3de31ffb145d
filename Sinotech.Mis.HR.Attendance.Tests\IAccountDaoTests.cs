﻿using FakeItEasy;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.DataAccess.Interfaces;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.DataAccess.Ado.Tests
{
    [ExcludeFromCodeCoverage]
    public class IAccountDaoTests
    {
        private readonly IAccountDao _accountDao;

        /// <summary>
        /// 建構元
        /// </summary>
        public IAccountDaoTests()
        {
            _accountDao = A.Fake<IAccountDao>();
            A.<PERSON>To(() => _accountDao.LogAccountLogin(A<AccountLogInOutDto>.Ignored)).Returns(true);
            A.CallTo(() => _accountDao.LogAccountLogout(A<AccountLogInOutDto>.Ignored)).Returns(true);
            A.CallTo(() => _accountDao.LogAccountUnlock(A<AccountLogInOutDto>.Ignored)).Returns(true);
            A.CallTo(() => _accountDao.GetLoginFailCount(A<string>.Ignored)).Returns(0);
        }

        /// <summary>
        /// 測試建構子
        /// </summary>
        [Fact]
        public void AccountDaoTest()
        {
            // Arrange
            string fakeConnectionString = "test";
            // Act
            IAccountDao accountDao = new AccountDao(fakeConnectionString);
            // Assert
            Assert.NotNull(accountDao);
        }

        /// <summary>
        /// 測試登入失敗次數
        /// </summary>
        [Fact]
        public void LoginFailCountTest()
        {
            // Arrange
            string accountId = "test";
            // Act
            int result = _accountDao.GetLoginFailCount(accountId); 
            // Assert
            Assert.Equal(0, result);
        }

        /// <summary>
        /// 測試記錄登入
        /// </summary>
        [Fact]
        public void LogAccountLoginTest()
        {
            // Arrange
            AccountLogInOutDto accountLogInOutDto = new AccountLogInOutDto();
            // Act
            bool result =_accountDao.LogAccountLogin(accountLogInOutDto);
            // Assert
            Assert.True(result);
        }

        /// <summary>
        /// 測試記錄登出
        /// </summary>
        [Fact]
        public void LogAccountLogoutTest()
        {
            // Arrange
            AccountLogInOutDto accountLogInOutDto = new AccountLogInOutDto();
            // Act
            bool result = _accountDao.LogAccountLogout(accountLogInOutDto);
            // Assert
            Assert.True(result);
        }

        /// <summary>
        /// 測試記錄帳號解鎖
        /// </summary>
        [Fact]
        public void LogAccountUnlockTest()
        {
            // Arrange
            AccountLogInOutDto accountLogInOutDto = new AccountLogInOutDto();
            // Act
            bool result = _accountDao.LogAccountUnlock(accountLogInOutDto);
            // Assert
            Assert.True(result);
        }
    }
}