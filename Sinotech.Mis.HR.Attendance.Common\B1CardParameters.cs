﻿using Sinotech.Mis.Common;
using Sinotech.Mis.HR.Attendance.BusinessLogic;
using System;
using System.Collections.Generic;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 加班卡參數
    /// </summary>
    public class B1CardParameters
    {
        /// <summary>
        /// 加班日期
        /// </summary>
        public DateTime OvertimeDate { get; set; }

        /// <summary>
        /// 刷卡時間
        /// </summary>
        public DayInTime InTime { get; set; } = new DayInTime();
        /// <summary>
        /// 當日詳細資料
        /// </summary>
        public Workday DayDetail { get; set; } = new Workday();

        /// <summary>
        /// 本張 B1Card 所用到的所有計畫的詳細資料
        /// </summary>
        public List<Project> Projects { get; set; } = new List<Project>();

        /// <summary>
        /// 員工詳細資料
        /// </summary>
        public Employee EmployeeDetail { get; set; } = new Employee();

        /// <summary>
        /// 員工職等
        /// </summary>
        public B1CardPositionEnum EmployeePosition { get; set; }

        /// <summary>
        /// 是否為副理以上 (不可加班人員)
        /// </summary>
        public bool IsAboveDeputyManager { get; set; }

        /// <summary>
        /// 是否為可加班特殊員工 (組長等，可加班時數與一般員工不同)
        /// </summary>
        public bool IsSpecialStaff { get; set; } = false;
        /// <summary>
        /// 是否為司機
        /// </summary>
        public bool IsDriver { get; set; } = false;
        /// <summary>
        /// 特殊員工每月加權後可加班時數
        /// </summary>
        public double SpecialStaffAllowedMonthWeightedOvertimeHours { get; set; } = 0.0;
        /// <summary>
        /// 特殊員工每月加權後已加班時數
        /// </summary>
        public double CurrentMonthWeightedOvertimeHours { get; set; } = 0.0;

        /// <summary>
        /// 加班卡
        /// </summary>
        public B1Card B1Card { get; set; } = new B1Card();

        /// <summary>
        /// 加班卡細項
        /// </summary>
        public List<B1CardDetailProject> B1CardDetails { get; set; } = new List<B1CardDetailProject>();

        /// <summary>
        /// 單日加班詳細資料
        /// </summary>
        public DayOvertime DayOvertime { get; set; } = new DayOvertime();

        /// <summary>
        /// 本日是否能報加班
        /// </summary>
        public bool CanOvertime { get; set; } = false;

        /// <summary>
        /// 是否加班申請卡已核可
        /// </summary>
        public bool IsFilledB1CardApp { get; set; } = false;

        /// <summary>
        /// 是否填過加班卡
        /// </summary>
        public bool IsFilledB1Card { get; set; } = false;

        /// <summary>
        /// 加班申請卡(若有)
        /// </summary>
        public B1CardApp? B1CardApp { get; set; } = null;

        /// <summary>
        /// 若已填報，該表單單號及名稱
        /// </summary>
        public FormFilled? FilledForm { get; set; }


        /// <summary>
        /// 每月加班統計
        /// </summary>
        public MonthOvertimeStatics MonthOvertimeStatics { get; set; } = new MonthOvertimeStatics();

        /// <summary>
        /// 每季加班統計
        /// </summary>
        public QuarterlyOvertimeStatics QuarterlyOvertimeStatics { get; set; } = new QuarterlyOvertimeStatics();

        /// <summary>
        /// 是否資料全部正確
        /// </summary>
        public bool IsValid { get; set; } = false;

        /// <summary>
        /// 警告訊息
        /// </summary>
        public string WarningMessage { get; set; } = string.Empty;

        /// <summary>
        /// 錯誤訊息
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

    }
}
