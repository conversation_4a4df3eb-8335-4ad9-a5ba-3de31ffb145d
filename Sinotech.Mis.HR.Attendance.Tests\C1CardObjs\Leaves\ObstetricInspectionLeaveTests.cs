﻿using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves.Tests
{
    [ExcludeFromCodeCoverage]
    public class ObstetricInspectionLeaveTests
    {
        private readonly ObstetricInspectionLeave obstetricInspectionLeave;

        public ObstetricInspectionLeaveTests(C1CardBo c1CardBo)
        {
            C1Card c1Card = new C1Card();
            obstetricInspectionLeave = new ObstetricInspectionLeave(c1Card, c1CardBo);
        }

        [Theory]
        [InlineData("2022-10-11", "0349", "2022-01-11", "2022-11-11")]
        [InlineData("2022-10-31", "0349", "2022-01-31", "2022-11-30")]
        [InlineData("2022-12-10", "0349", "2022-03-10", "2023-01-10")]
        [InlineData("2022-11-30", "0349", "2022-02-28", "2022-12-30")]
        [InlineData("2023-01-28", "0349", "2022-04-28", "2023-02-28")]
        [InlineData("2023-01-29", "0349", "2022-04-29", "2023-02-28")]
        [InlineData("2023-01-30", "0349", "2022-04-30", "2023-02-28")]
        [InlineData("2023-01-31", "0349", "2022-04-30", "2023-02-28")]
        [InlineData("2023-02-14", "0349", "2022-05-14", "2023-03-14")]
        [InlineData("2023-04-10", "0349", "2022-07-10", "2023-05-10")]
        public void CalculateLeavePermittedPeriodTest(DateTime eventDate, string empNo, DateTime expectedStartDate, DateTime expectedEndDate)
        {
            DateTime startDate;
            DateTime endDate;
            (startDate, endDate) = obstetricInspectionLeave.CalculateLeavePermittedPeriod(eventDate, empNo);
            Assert.Equal(expectedStartDate, startDate);
            Assert.Equal(expectedEndDate, endDate);
        }

        [Theory]
        [InlineData(Gender.Female, true)]
        [InlineData(Gender.Male, false)]
        [InlineData((Gender)2, false)]
        public void IsAllowForThisGenderTest(Gender gender, bool expected)
        {
            bool result = ObstetricInspectionLeave.IsAllowForThisGender(gender);
            Assert.Equal(expected, result);
        }
    }
}