﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.Utilities;
using System.Collections.Generic;

namespace Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers
{
    /// <summary>
    /// 部門 Web API
    /// </summary>
    [Authorize]
    [ApiController]
    [Route("api/[controller]/[action]")]
    public class DepartmentController : ControllerBase
    {
        private readonly IAttendanceBo _attendanceBo;
        private readonly IDepartmentBo _departmentBo;
        private readonly bool _useNegotiate;

        
        /// <summary>
        ///   <see cref="DepartmentController" /> 的建構函式</summary>
        ///   <param name="attendanceBo">出勤商業元件</param>
        /// <param name="departmentBo">部門 商業元件</param>
        /// <param name="configuration">設定檔</param>
        public DepartmentController(IAttendanceBo attendanceBo, IDepartmentBo departmentBo, IConfiguration configuration)
        {
            _attendanceBo = attendanceBo;
            _departmentBo = departmentBo;
            _useNegotiate = configuration.GetValue<bool>("UseNegotiate");
        }

        /// <summary>
        /// 取得所有部門
        /// </summary>
        /// <returns>所有部門 JSON String</returns>
        [Authorize]
        [HttpGet]
        [Route("/api/[controller]/[action]")]
        public string GetDepartments()
        {
            string ret = "[]";
            string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            if (!string.IsNullOrWhiteSpace(userId))
            {
                ret = _departmentBo.GetDepartmentsJson();
            }
            return ret;
        }

        /// <summary>
        /// 依指定員工身分，取得該員在【三卡首頁】可查詢的資料
        /// </summary>
        /// <param name="empNo"></param>
        /// <returns>部門編號、部門簡稱、組別編號、組別名稱、員工編號、員工姓名</returns>
        [Authorize]
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public List<DeptTeam> GetAllowedQueryEmployees(string empNo)
        {
            List<DeptTeam> ret = new List<DeptTeam>();
            string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            if (!string.IsNullOrWhiteSpace(userId))
            {
                ret = _attendanceBo.GetAllowedQueryEmployees(empNo);
            }
            return ret;
        }

        /// <summary>
        /// 依指定員工身分，取得該員在【部門填報紀錄】可查詢的資料
        /// </summary>
        /// <param name="empNo"></param>
        /// <returns>部門編號、部門簡稱、組別編號、組別名稱、員工編號、員工姓名</returns>
        [Authorize]
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public List<DeptTeam> GetAllowedQueryDepartmentSentBoxEmployees(string empNo)
        {
            List<DeptTeam> ret = new List<DeptTeam>();
            string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            if (!string.IsNullOrWhiteSpace(userId))
            {
                ret = _attendanceBo.GetAllowedQueryDepartmentSentBoxEmployees(empNo);
            }
            return ret;
        }

        /// <summary>
        /// 依指定員工身分，取得該員在【部門填報紀錄】能否查詢全社資料
        /// </summary>
        /// <param name="empNo"></param>
        /// <returns>部門編號、部門簡稱、組別編號、組別名稱、員工編號、員工姓名</returns>
        [Authorize]
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public bool IsAllowedQueryAllDepartmentSentBox(string empNo)
        {
            bool ret = false;
            string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
            if (!string.IsNullOrWhiteSpace(userId))
            {
                ret = _attendanceBo.IsAllowedQueryAllDepartmentSentBox(empNo);
            }
            return ret;
        }
    }
}
