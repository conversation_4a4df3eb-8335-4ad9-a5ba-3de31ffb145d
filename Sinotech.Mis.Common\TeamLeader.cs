﻿using System;

namespace Sinotech.Mis.Common
{
    /// <summary>
    /// 組長基本資料
    /// </summary>
    public class TeamLeader
    {
        /// <summary>
        /// 部門編號
        /// </summary>
        public int DeptNo { get; set; }

        /// <summary>
        /// 部門簡稱
        /// </summary>
        public string DeptSName { get; set; } = string.Empty;

        /// <summary>
        /// 組別編號
        /// </summary>
        public int TeamID { get; set; }

        /// <summary>
        /// 組別中文名稱
        /// </summary>
        public string TeamCName { get; set; } = string.Empty;

        /// <summary>
        /// 組長職務編號
        /// </summary>
        public string JobNo { get; set; } = string.Empty;

        /// <summary>
        /// 組長職務名稱
        /// </summary>
        public string JobName { get; set; } = string.Empty;

        /// <summary>
        /// 組長員工編號
        /// </summary>
        public string EmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 組長中文名
        /// </summary>
        public string CName { get; set; } = string.Empty;

        /// <summary>
        /// 組啟用日
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 組停用日
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 組別是否啟用
        /// </summary>
        public bool IsActive { get; set; } = true;
    }
}
