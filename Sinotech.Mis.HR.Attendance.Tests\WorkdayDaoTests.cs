﻿using Microsoft.Extensions.Configuration;
using Sinotech.Mis.Extensions.Configuration;
using System;
using System.Data;
using Xunit;

namespace Sinotech.Mis.Utilities.DataAccess.Ado.Tests
{
    public class WorkdayDaoTests
    {
        private readonly WorkdayDao _workdayDao;
        public WorkdayDaoTests()
        {
            IConfiguration Configuration = new ConfigurationBuilder().
                AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).
                Build();
            string connectionStringWorkday = Configuration.GetSecuredConnectionString("Workday");
            _workdayDao = new WorkdayDao(connectionStringWorkday);
        }

        [Theory]
        [InlineData(2021, 1, 1, "2021-01-08")]
        [InlineData(2021, 1, 11, "2021-01-20")]
        [InlineData(2021, 1, 21, "2021-01-29")]
        [InlineData(2022, 2, 8, "2022-02-10")]
        [InlineData(2022, 2, 17, "2022-02-18")]
        [InlineData(2022, 2, 24, "2022-02-25")]
        [InlineData(2022, 3, 1, "2022-03-10")]
        [InlineData(2022, 3, 11, "2022-03-18")]
        [InlineData(2022, 3, 21, "2022-03-31")]
        public void GetLastWorkDayInTenDaysTest(int year, int month, int day, DateTime expected)
        {
            DateTime actual = _workdayDao.GetLastWorkDayInTenDays(year, month, day);
            Assert.Equal(expected, actual);
        }

        [Theory]
        [InlineData("2021-01-01", "2021-01-31", "0349", true)]
        [InlineData("2021-02-01", "2021-02-28", "0349", true)]
        [InlineData("2021-01-01", "2021-01-31", "0395", true)]
        [InlineData("2021-02-01", "2021-02-28", "0395", true)]
        public void GetShiftsDateRangeTest(DateTime startDate, DateTime endDate, string empNo, bool expect)
        {
            DataTable dt = _workdayDao.GetShiftsDateRange(startDate, endDate, empNo);
            bool actual = dt.Rows.Count == 0;
            Assert.Equal(expect, actual);
        }
    }
}