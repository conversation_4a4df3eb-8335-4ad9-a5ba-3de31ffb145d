﻿using System;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Net;
using System.Text.RegularExpressions;

namespace Sinotech.Mis.Helpers
{
    /// <summary>
    /// 網路檔案複製<br />
    /// 此檔案由網路搜尋來，非原創
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class NetworkCopier
    {
        static string DefaultDomain { get; set; } = string.Empty;
        static string DefaultPassword { get; set; } = string.Empty;
        static string DefaultUserId { get; set; } = string.Empty;

        /// <summary>
        /// 建構元
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="password"></param>
        /// <param name="domain"></param>
        public NetworkCopier(string userName, string password, string domain)
        {
            DefaultDomain = NotNull(domain);
            DefaultUserId = NotNull(userName);
            DefaultPassword = NotNull(password);
        }


        /// <summary>
        /// 傳回指定網路UNC路徑目錄裡符合指定搜尋模式的檔案名稱(包括檔案的路徑)
        /// </summary>
        /// <param name="path">網路UNC路徑</param>
        /// <param name="pattern">搜尋模式</param>
        /// <param name="domain"></param>
        /// <param name="userName"></param>
        /// <param name="passwd"></param>
        /// <returns></returns>
        public static string[] DirFiles(string path, string pattern, string? domain = null, string? userName = null, string? passwd = null)
        {
            using (new NetworkConnection(GetSharePath(path),
                new NetworkCredential(userName ?? DefaultUserId, passwd ?? DefaultPassword, domain ?? DefaultDomain)))
            {
                return Directory.GetFiles(path, pattern);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="path"></param>
        /// <param name="domain"></param>
        /// <param name="userName"></param>
        /// <param name="passwd"></param>
        /// <returns></returns>
        public static NetworkConnection GetConnectionContext(string path, string? domain = null, string? userName = null, string? passwd = null)
        {
            return new NetworkConnection(GetSharePath(path),
                new NetworkCredential(userName ?? DefaultUserId, passwd ?? DefaultPassword, domain ?? DefaultDomain));
        }

        /// <summary>
        /// 複製
        /// </summary>
        /// <param name="srcPath">來源檔案pathname</param>
        /// <param name="dstPath">目的地pathname</param>
        /// <param name="domain">網域</param>
        /// <param name="userName">帳號</param>
        /// <param name="password">密碼</param>
        public void Copy(string srcPath, string dstPath, string? domain = null, string? userName = null, string? password = null)
        {
            using (new NetworkConnection(GetSharePath(srcPath),
                new NetworkCredential(userName ?? DefaultUserId, password ?? DefaultPassword, domain ?? DefaultDomain)))
            {
                File.Copy(srcPath, dstPath);
            }
        }

        /// <summary>
        /// 存檔
        /// </summary>
        /// <param name="dest">目的地pathname</param>
        /// <param name="bytes">檔案內容</param>
        /// <param name="domain">網域</param>
        /// <param name="userName">帳號</param>
        /// <param name="password">密碼</param>
        public void SaveTo(string dest, byte[] bytes, string? domain = null, string? userName = null, string? password = null)
        {
            using (new NetworkConnection(GetSharePath(dest),
                new NetworkCredential(userName ?? DefaultUserId, password ?? DefaultPassword, domain ?? DefaultDomain)))
            {
                File.WriteAllBytes(dest, bytes);
            }
        }

        /// <summary>
        /// 取得分享目錄名
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        static string GetSharePath(string path)
        {
            var m = Regex.Match(path, @"^\\\\[^\\]+\\[^\\]+",
                RegexOptions.NonBacktracking, TimeSpan.FromSeconds(0.5));
            if (m.Success) return m.Value;
            return path;
        }

        /// <summary>
        /// 判斷參數是否為 Null，若為Null則產生 ArgumentNullException 中斷程式
        /// </summary>
        /// <param name="parameter"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        static string NotNull(string? parameter)
        {
            if (string.IsNullOrEmpty(parameter))
            {
                string errorMessage = "未設定預設登入身分";
                throw new ArgumentNullException(errorMessage);
            }
            return parameter;
        }
    }
}
