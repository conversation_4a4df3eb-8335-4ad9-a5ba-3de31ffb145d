{"name": "attendancecard", "version": "1.3.0", "engines": {"node": ">=22.0.0"}, "type": "module", "scripts": {"build": "vue-tsc --noEmit && vite build", "dev.debug": "vite --host --debug", "dev": "echo Starting the development server && vite --host", "serve": "vite --host", "preview": "vite preview", "test": "vitest", "coverage": "vitest run --coverage"}, "dependencies": {"@fontsource/noto-sans-tc": "^5.2.6", "@fullcalendar/bootstrap5": "^6.1.19", "@fullcalendar/core": "^6.1.19", "@fullcalendar/daygrid": "^6.1.19", "@fullcalendar/interaction": "^6.1.19", "@fullcalendar/vue3": "^6.1.19", "@popperjs/core": "^2.11.8", "@primevue/themes": "^4.1.1", "bootstrap": "^5.3.7", "bootstrap-icons": "^1.13.1", "crypto-js": "^4.2.0", "lodash.isequal": "^4.5.0", "pinia": "^3.0.3", "primelocale": "^2.1.6", "primevue": "^4.1.1", "vue": "^3.5.20", "vue-router": "^4.5.1", "vue-select": "^4.0.0-beta.6"}, "devDependencies": {"@babel/types": "^7.25.2", "@pinia/testing": "^0.1.5", "@types/bootstrap": "^5.2.10", "@types/crypto-js": "^4.2.2", "@types/lodash.isequal": "^4.5.8", "@types/vue-select": "^3.16.2", "@vitejs/plugin-vue": "^6.0.1", "@vitest/coverage-istanbul": "^3.2.4", "@vitest/eslint-plugin": "^1.3.4", "@vue/eslint-config-typescript": "^14.1.3", "@vue/test-utils": "^2.4.6", "eslint": "^9.14.0", "eslint-plugin-vue": "^9.30.0", "jsdom": "^24.1.1", "sass": "^1.77.8", "typescript": "^5.9.2", "vite": "^7.1.3", "vitest": "^3.2.4", "vue-tsc": "^3.0.6"}, "license": "BSD-3-<PERSON><PERSON>"}