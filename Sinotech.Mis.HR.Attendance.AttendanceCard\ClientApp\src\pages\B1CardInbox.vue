<template>
  <CardInbox
    :modelValue="data"
    :approveComment="approveComment"
  >
    <template #header>
      <i class="bi bi-vector-pen me-1" />
      <span>待核卡 / </span>
      <span>{{ FORM_ID.B1Card }}</span>
    </template>
  </CardInbox>
  <div class="container border border-2 px-0">
    <B1Card :modelValue="data" />
  </div>
  <div class="container px-0">
    <OvertimeInfo
      :modelValue="{
        card: FORM_ID.B1Card,
        overtimeDateStatic: overtimeDateStatic,
        overtimeData: overtimeData,
        currentInfoNote: false
      }"
    />
  </div>
  <div class="container px-0">
    <FormFlow
      :modelValue="data"
      :editComment="true"
      @update:approveComment="onUpdateComment"
    />
  </div>

  <div class="container px-0">
    <div class="text-black-50 text-end">
      <span>
        查詢時間：
      </span>
      <span class="fst-italic">
        {{ dateToRocString(cardStore.queryDataTime) }}
      </span>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useToast } from 'primevue/usetoast'
import { useCardStore } from '../store/card'
import { onBeforeRouteLeave } from 'vue-router'
import { FORM_ID } from '../api/appConst'
import { dateToRocString } from '../api/appFunction'
import CardInbox from '../components/CardInbox.vue'
import B1Card from '../components/B1Card.vue'
import FormFlow from '../components/FormFlow.vue'
import OvertimeInfo from '../components/OvertimeInfo.vue'
import { useOvertimeData } from '../composable/overtimeData'
import { useAbortController } from '../composable/abortController'

const approveComment = ref<string>('')
const { overtimeDateStatic, overtimeData, onSetOvertimeDate, onGetOvertimeData } = useOvertimeData()
const cardStore = useCardStore()
const { data } = storeToRefs(cardStore)
const toast = useToast()
const { abortController, abortListener } = useAbortController()

const onUpdateComment = (event: string): void => {
  approveComment.value = event
}

onBeforeRouteLeave(() => {
  cardStore.$reset()
  toast.removeGroup('app')
})

watch(data, async () => {
  if (data.value !== undefined) {
    if (data.value.formInfo !== null && data.value.empNo !== null) {
      const date = data.value.formInfo.split('/')
      onSetOvertimeDate(new Date(parseInt(date[0], 10) + 1911, parseInt(date[1], 10) - 1, parseInt(date[2], 10)))
      await onGetOvertimeData(data.value.empNo, abortController.signal)
    }
  }
})

onMounted(async (): Promise<void> => {
  abortListener()
  if (cardStore.data !== undefined) {
    if (cardStore.data?.formInfo !== null && cardStore.data?.empNo !== null) {
      const date = cardStore.data?.formInfo.split('/')
      onSetOvertimeDate(new Date(parseInt(date[0], 10) + 1911, parseInt(date[1], 10) - 1, parseInt(date[2], 10)))
      await onGetOvertimeData(cardStore.data.empNo, abortController.signal)
    }
  }
})
</script>