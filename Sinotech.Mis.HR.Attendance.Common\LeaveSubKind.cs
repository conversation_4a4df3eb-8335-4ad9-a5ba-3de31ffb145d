﻿namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 假別細項
    /// </summary>
    public class LeaveSubKind
    {
        /// <summary>假別細項編號</summary>
        /// <value>假別細項編號</value>
        public int LeaveSubNumber { get; set; }

        /// <summary>
        /// 假別細項名稱
        /// </summary>
        /// <value>
        /// 假別細項名稱
        /// </value>
        public string LeaveSubName { get; set; } = string.Empty;

        /// <summary>
        /// 假別上限
        /// </summary>
        public int UpperLimit { get; set; } = 0;


        /// <summary>
        /// 允許超假
        /// </summary>
        /// <value>
        ///  0:不檢查
        ///  1:超假時,須提醒並記錄超假
        ///  2:強制不允許超假
        /// </value>
        public int PermitExtraLeave { get; set; } = 0;

        /// <summary>
        /// 允許申請次數
        /// </summary>
        /// <value>
        /// 0:不檢查 
        /// 1:以一年為計算單位
        /// 2:需一次請完的假別,檢查該事件請假是否超假
        /// 3:保留代休假
        /// 4:可分次請,該事件,一生只允許請一次. EX.(喪假)
        /// 5:可分次請,檢查該事件是否超假. EX.(喪假)
        /// 6.以一個月為計算單位
        /// </value>
        public int PermittedApplyNumber { get; set; } = 0;

        /// <summary>
        /// 假別細項類別名稱
        /// </summary>
        /// <value>
        /// 假別細項類別名稱
        /// </value>
        public string LeaveSubType { get; set; } = string.Empty;
        /// <summary>
        /// 顯示順序
        /// </summary>
        public int DisplayOrder { get; set; }
    }
}