﻿using Sinotech.Mis.HR.Attendance.BusinessLogic;
using FakeItEasy;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sinotech.Mis.Extensions.Configuration;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.DataAccess.Interfaces;
using Sinotech.Mis.HR.Attendance.Tests;
using Sinotech.Mis.Utilities.DataAccess.Interfaces;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.BusinessLogic.Tests
{
    [ExcludeFromCodeCoverage]
    public class B1CardBoTests
    {
#nullable enable
        private readonly IAttendanceBo _attendanceBo;
        private readonly IB1CardAppBo _b1CardAppBo;
        private readonly IB1CardBo _b1CardBo;
        private readonly EmployeeBo _employeeBo;
        private readonly FormBo _formBo;
        private readonly FormFlowBo _formFlowBo;
        private readonly OvertimeBo _overtimeBo;
        private readonly ProjectBo _projectBo;
        private readonly IWorkdayBo _workdayBo;

        public B1CardBoTests(IAttendanceBo attendanceBo, IB1CardBo b1CardBo, IB1CardAppBo b1CardAppBo,
            EmployeeBo employeeBo, OvertimeBo overtimeBo,
            FormTypeBo formTypeBo, FormFlowBo formFlowBo, NotificationMail notificationMail,
            IWorkdayBo workdayBo, FormBo formBo)
        {
            _attendanceBo = attendanceBo;
            _employeeBo = employeeBo;

            _overtimeBo = overtimeBo;

            _projectBo = new ProjectBo(A.Fake<IProjectDao>());
            _workdayBo = workdayBo;
            _b1CardAppBo = b1CardAppBo;
            _b1CardBo = b1CardBo;
            _formBo = formBo;
            _formFlowBo = formFlowBo;
            IConfiguration configuration = new ConfigurationBuilder().
            AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).Build();
            string ConnectionStringAttendance = configuration.GetSecuredConnectionString("Attendance");
            TestHelper.ClearData(ConnectionStringAttendance);
        }

        [Fact]
        public async Task AddB1CardTest()
        {
            // Assert
            DateTime date = new DateTime(2023, 1, 2, 0, 0, 0, DateTimeKind.Local);
            B1Card b1Card = new B1Card();
            string creatorId = "0395";
            // Act
            B1CardCheckResult checkResult = await _b1CardBo.AddB1Card(creatorId, b1Card, "127.0.0.1", "localhost");

            // Assert
            Assert.False(checkResult.IsValid);

            // Assert
            b1Card.EmpNo = "0395";
            b1Card.TotalHours = 7;
            b1Card.B1_WDate = date;
            b1Card.CreatedTime = date;
            b1Card.Details.Add(new B1CardDetail { Project = "RP19553", Hour = 7, B1_CODE = 1, StartTime = date.AddHours(8), EndTime = date.AddHours(8) });

            // Act
            checkResult = await _b1CardBo.AddB1Card(creatorId, b1Card, "127.0.0.1", "localhost");

            // Assert
            Assert.False(checkResult.IsValid);
            // Assert
            b1Card.EmpNo = "2023";

            // Act
            checkResult = await _b1CardBo.AddB1Card(creatorId, b1Card, "127.0.0.1", "localhost");

            // Assert
            Assert.False(checkResult.IsValid);
        }

        [Fact]
        public void CanClosedWithdrawTest()
        {
            // Arrange
            var card = new B1Card { Status = (int)FormStatus.Withdraw };

            // Act
            var result = _b1CardBo.CanClosedWithdraw(card);

            // Assert
            Assert.Equal("表單已抽單", result);
        }

        [Fact]
        public void CanClosedWithdraw_ReturnsCorrectMessage_WhenStatusIsProcessing()
        {
            // Arrange
            var card = new B1Card { Status = (int)FormStatus.Processing };

            // Act
            var result = _b1CardBo.CanClosedWithdraw(card);

            // Assert
            Assert.Equal("表單進行中", result);
        }

        [Fact]
        public void CanClosedWithdraw_ReturnsCorrectMessage_WhenStatusIsWithdraw()
        {
            // Arrange
            var card = new B1Card { Status = (int)FormStatus.Withdraw };

            // Act
            var result = _b1CardBo.CanClosedWithdraw(card);

            // Assert
            Assert.Equal("表單已抽單", result);
        }

        [Fact]
        public void CanClosedWithdraw_ReturnsEmptyString_WhenStatusIsOther()
        {
            // Arrange
            var card = new B1Card { Status = 0 }; // Assuming 0 is a status other than Withdraw or Processing

            // Act
            var result = _b1CardBo.CanClosedWithdraw(card);

            // Assert
            Assert.Equal(string.Empty, result);
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("   ")]
        public void CannotCallDateCanFillB1CardWithInvalidEmpNo(string value)
        {
            // Assert
            DateTime date = new DateTime(2023, 1, 2, 0, 0, 0, DateTimeKind.Local);

            // Act
            B1CardCheckResult checkResult = _b1CardBo.DateCanFillB1Card(date, value);

            // Assert
            Assert.False(checkResult.CanOvertime);
            Assert.Equal(checkResult.UserErrorMessage, checkResult.ErrorMessage);
        }

        [Fact]
        public void CanWithdrawTest()
        {
            // Arrange
            var card = new B1Card { Status = 0 }; // Assuming 0 is a status other than Withdraw or Processing

            // Act
            var result = _b1CardBo.CanWithdraw(card);

            // Assert
            Assert.Equal(string.Empty, result);
        }

        [Fact]
        public void GetB1CardTypesTest()
        {
            var list = _b1CardBo.GetB1CardTypes();
            Assert.NotEmpty(list);
            list = _b1CardBo.GetB1CardTypes();
            Assert.NotEmpty(list);
        }

        [Fact]
        public void GetFormCards_Empty_Test()
        {
            DateTime startDate = new DateTime(2022, 1, 4, 19, 0, 0, DateTimeKind.Local);
            DateTime endDate = new DateTime(2022, 1, 4, 23, 0, 0, DateTimeKind.Local);
            List<FormCard> formCards = _b1CardBo.GetFormCards(startDate, endDate);
            Assert.Empty(formCards);
        }

        [Fact]
        public async Task GetFormCardsTest()
        {
            B1Card b1Card = new B1Card();
            var result = _b1CardBo.IsValidB1Card(b1Card);
            Assert.NotNull(result);
            Assert.False(result.IsValid);

            string b1CardAppStr = "{\"B1_ADate\":null,\"B1_Code\":\"1\",\"B1_Date\":\"2022-01-04T00:00:00+08:00\",\"B1_DateTypeId\":1,\"B1_DeptNo\":4,\"B1_EmpNo\":\"0395\",\"B1_FormID\":null,\"B1_Hour\":2,\"B1_IsOverdue\":false,\"B1_OverdueReason\":\"\",\"B1_PaidHour\":0,\"B1_PrjNo\":\"RP19553\",\"B1_Reason\":\"test\",\"B1_ShouldSignEmpNo\":null,\"B1_SOURCE\":\"Attendance\",\"B1_Status\":1,\"B1_UDate\":null,\"B1_UpdatedEmpNo\":null,\"B1_WDate\":\"2024-10-25T10:22:59.621+08:00\",\"B1_WritedEmpNo\":\"0395\",\"FormUID\":\"00000000-0000-0000-0000-000000000000\",\"ID\":null,\"Name\":\"B1CardApp\",\"RequisitionID\":null,\"UpdatedEmpNo\":null,\"UpdatedName\":null,\"UpdatedTime\":null,\"UpdatedIP\":null,\"UpdatedHost\":null,\"AddSigners\":\"\",\"CreatedTime\":\"2024-10-25T10:22:59.621+08:00\",\"FilledTime\":\"2024-10-25T10:22:27.585+08:00\",\"ApplicationType\":\"\",\"UploadedFiles\":[],\"RemindSigner\":false,\"RemindMessageType\":0,\"RemindMessage\":null}";

            string creatorId = "0395";

            B1CardApp b1CardApp = JsonConvert.DeserializeObject<B1CardApp>(b1CardAppStr);
            var b1CardAppCheckResult = await _b1CardAppBo.AddB1CardApp(creatorId, b1CardApp, "127.0.0.1", "localhost");
            Assert.True(b1CardAppCheckResult.IsValid);

            Form form = _formBo.GetForm((Guid)b1CardAppCheckResult.FormUID);
            FormCard formCard = _b1CardAppBo.GetFormCard(form, "0395");
            Guid flowUID = formCard.Flows[0].FlowUID;

            Approve approve = new Approve();
            approve.ApproverDeptSName = "企劃處";
            approve.ApproveIP = "127.0.0.1";
            approve.ApproverName = "test";
            approve.ApproverEmpNo = "0391";
            approve.FlowUID = flowUID;
            approve.FormID = form.FormID;
            approve.FormUID = form.FormUID;
            approve.ApproveTime = DateTime.Now;
            approve.FlowStatus = (int)FlowStatus.Agree;

            string approveResult = _formBo.Approve(approve, _b1CardAppBo, "0391");
            Assert.Empty(approveResult);

            string strB1Card = "{\"B1_ADate\":null,\"B1_WDate\":\"2022-01-04T09:36:04.991+08:00\",\"DateTypeId\":1,\"Details\":[{\"ID\":null,\"Project\":\"RP19553\",\"StartTime\":\"2022-01-04T19:00:00+08:00\",\"EndTime\":\"2022-01-04T23:00:00+08:00\",\"Hour\":0,\"HourLeft\":0,\"B1_CODE\":2,\"ApplicationType\":\"\",\"SerialNo\":0}],\"EmpNo\":\"0395\",\"InOvertimeHours\":0,\"Name\":\"B1Card\",\"Reason\":\"test\",\"SheetNo\":\"\",\"Source\":\"Attendance\",\"Status\":1,\"TotalHours\":0,\"FormUID\":\"00000000-0000-0000-0000-000000000000\",\"UpdatedEmpNo\":null,\"UpdatedName\":null,\"UpdatedTime\":null,\"UpdatedIP\":null,\"UpdatedHost\":null,\"AddSigners\":\"\",\"CreatedTime\":\"2022-01-04T09:36:04.991+08:00\",\"FilledTime\":\"2022-01-04T09:35:44.381+08:00\",\"ApplicationType\":\"\",\"UploadedFiles\":[],\"RemindSigner\":false,\"RemindMessageType\":0,\"RemindMessage\":null}";
            b1Card = JsonConvert.DeserializeObject<B1Card>(strB1Card);
            B1CardDetail detail = new B1CardDetail();
            detail.Project = "RP19553";
            detail.Hour = 4;
            detail.B1_CODE = 2;
            detail.ApplicationType = "社外加班";
            detail.StartTime = new DateTime(2022, 1, 4, 19, 0, 0, DateTimeKind.Local);
            detail.EndTime = new DateTime(2022, 1, 4, 23, 0, 0, DateTimeKind.Local);
            b1Card.Details = new List<B1CardDetail>();
            b1Card.Details.Add(detail);
            result = _b1CardBo.IsValidB1Card(b1Card);
            Assert.NotNull(result);
            var result2 = await _b1CardBo.AddB1Card("0395", b1Card, "127.0.0.1", "localhost");
            Assert.Empty(result2.ErrorMessage);
            DateTime startDate = new DateTime(2022, 1, 1, 0, 0, 0, DateTimeKind.Local);
            DateTime endDate = new DateTime(2022, 1, 5, 0, 0, 0, DateTimeKind.Local);
            List<FormCard> formCards = _b1CardBo.GetFormCards(startDate, endDate);
            Assert.NotEmpty(formCards);
            Assert.Single(formCards);
        }

        [Fact]
        public void GetFormCardTest()
        {
            // Arrange
            var form = new Form
            {
                ID = 1,
                FormID = "FormID",
                FormUID = Guid.NewGuid(),
                FormNo = "FormNo",
                FormSubject = "FormSubject",
                FormInfo = "FormInfo",
                EmpNo = "EmpNo",
                EmpName = "EmpName",
                DeptNo = 3,
                DeptSName = "DeptSName",
                TeamID = null,
                TeamCName = "TeamCName",
                CreatedEmpNo = "CreatedEmpNo",
                CreatedName = "CreatedName",
                FilledTime = DateTime.Now,
                CreatedTime = DateTime.Now,
                CreatedIP = "CreatedIP",
                CreatedHost = "CreatedHost",
                StartTime = DateTime.Now,
                EndTime = DateTime.Now,
                FormStatus = 1,
                TotalSteps = 5,
                CurrentStep = 2,
                UpdatedEmpNo = "UpdatedEmpNo",
                UpdatedName = "UpdatedName",
                UpdatedTime = DateTime.Now,
                UpdatedIP = "UpdatedIP",
                UpdatedHost = "UpdatedHost",
                AddedSigner = "AddedSigner",
                Flows = new List<FormFlow>(),
                Attachments = new List<FormAttachment>()
            };

            var userId = "UserId";

            // Act
            var result = _b1CardBo.GetFormCard(form, userId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(form.ID, result.ID);
            Assert.Equal(form.FormID, result.FormID);
            Assert.Equal(form.FormUID, result.FormUID);
            Assert.Equal(form.FormNo, result.FormNo);
            Assert.Equal(form.FormSubject, result.FormSubject);
            Assert.Equal(form.FormInfo, result.FormInfo);
            Assert.Equal(form.EmpNo, result.EmpNo);
            Assert.Equal(form.EmpName, result.EmpName);
            Assert.Equal(form.DeptNo, result.DeptNo);
            Assert.Equal(form.DeptSName, result.DeptSName);
            Assert.Equal(form.TeamID, result.TeamID);
            Assert.Equal(form.TeamCName, result.TeamCName);
            Assert.Equal(form.CreatedEmpNo, result.CreatedEmpNo);
            Assert.Equal(form.CreatedName, result.CreatedName);
            Assert.Equal(form.FilledTime, result.FilledTime);
            Assert.Equal(form.CreatedTime, result.CreatedTime);
            Assert.Equal(form.CreatedIP, result.CreatedIP);
            Assert.Equal(form.CreatedHost, result.CreatedHost);
            Assert.Equal(form.StartTime, result.StartTime);
            Assert.Equal(form.EndTime, result.EndTime);
            Assert.Equal(form.FormStatus, result.FormStatus);
            Assert.Equal("進行中", result.FormStatusName);
            Assert.Equal(form.TotalSteps, result.TotalSteps);
            Assert.Equal(form.CurrentStep, result.CurrentStep);
            Assert.Equal(form.UpdatedEmpNo, result.UpdatedEmpNo);
            Assert.Equal(form.UpdatedName, result.UpdatedName);
            Assert.Equal(form.UpdatedTime, result.UpdatedTime);
            Assert.Equal(form.UpdatedIP, result.UpdatedIP);
            Assert.Equal(form.UpdatedHost, result.UpdatedHost);
            Assert.Equal(form.Flows, result.Flows);
            Assert.Equal(form.Attachments, result.Attachments);
        }

        [Fact]
        public async Task IsValidB1CardTest()
        {
            B1Card b1Card = new B1Card();
            var result = _b1CardBo.IsValidB1Card(b1Card);
            Assert.NotNull(result);
            Assert.False(result.IsValid);

            string b1CardAppStr = "{\"B1_ADate\":null,\"B1_Code\":\"1\",\"B1_Date\":\"2022-01-04T00:00:00+08:00\",\"B1_DateTypeId\":1,\"B1_DeptNo\":4,\"B1_EmpNo\":\"0395\",\"B1_FormID\":null,\"B1_Hour\":2,\"B1_IsOverdue\":false,\"B1_OverdueReason\":\"\",\"B1_PaidHour\":0,\"B1_PrjNo\":\"RP19553\",\"B1_Reason\":\"test\",\"B1_ShouldSignEmpNo\":null,\"B1_SOURCE\":\"Attendance\",\"B1_Status\":1,\"B1_UDate\":null,\"B1_UpdatedEmpNo\":null,\"B1_WDate\":\"2024-10-25T10:22:59.621+08:00\",\"B1_WritedEmpNo\":\"0395\",\"FormUID\":\"00000000-0000-0000-0000-000000000000\",\"ID\":null,\"Name\":\"B1CardApp\",\"RequisitionID\":null,\"UpdatedEmpNo\":null,\"UpdatedName\":null,\"UpdatedTime\":null,\"UpdatedIP\":null,\"UpdatedHost\":null,\"AddSigners\":\"\",\"CreatedTime\":\"2024-10-25T10:22:59.621+08:00\",\"FilledTime\":\"2024-10-25T10:22:27.585+08:00\",\"ApplicationType\":\"\",\"UploadedFiles\":[],\"RemindSigner\":false,\"RemindMessageType\":0,\"RemindMessage\":null}";

            string creatorId = "0395";

            B1CardApp b1CardApp = JsonConvert.DeserializeObject<B1CardApp>(b1CardAppStr);
            var b1CardAppCheckResult = await _b1CardAppBo.AddB1CardApp(creatorId, b1CardApp, "127.0.0.1", "localhost");
            Assert.True(b1CardAppCheckResult.IsValid);

            Form form = _formBo.GetForm((Guid)b1CardAppCheckResult.FormUID);
            FormCard formCard = _b1CardAppBo.GetFormCard(form, "0395");
            Guid flowUID = formCard.Flows[0].FlowUID;

            Approve approve = new Approve();
            approve.ApproverDeptSName = "企劃處";
            approve.ApproveIP = "127.0.0.1";
            approve.ApproverName = "test";
            approve.ApproverEmpNo = "0391";
            approve.FlowUID = flowUID;
            approve.FormID = form.FormID;
            approve.FormUID = form.FormUID;
            approve.ApproveTime = DateTime.Now;
            approve.FlowStatus = (int)FlowStatus.Agree;

            string approveResult = _formBo.Approve(approve, _b1CardAppBo, "0391");
            Assert.Empty(approveResult);

            string strB1Card = "{\"B1_ADate\":null,\"B1_WDate\":\"2022-01-04T09:36:04.991+08:00\",\"DateTypeId\":1,\"Details\":[{\"ID\":null,\"Project\":\"RP19553\",\"StartTime\":\"2022-01-04T19:00:00+08:00\",\"EndTime\":\"2022-01-04T23:00:00+08:00\",\"Hour\":0,\"HourLeft\":0,\"B1_CODE\":2,\"ApplicationType\":\"\",\"SerialNo\":0}],\"EmpNo\":\"0395\",\"InOvertimeHours\":0,\"Name\":\"B1Card\",\"Reason\":\"test\",\"SheetNo\":\"\",\"Source\":\"Attendance\",\"Status\":1,\"TotalHours\":0,\"FormUID\":\"00000000-0000-0000-0000-000000000000\",\"UpdatedEmpNo\":null,\"UpdatedName\":null,\"UpdatedTime\":null,\"UpdatedIP\":null,\"UpdatedHost\":null,\"AddSigners\":\"\",\"CreatedTime\":\"2022-01-04T09:36:04.991+08:00\",\"FilledTime\":\"2022-01-04T09:35:44.381+08:00\",\"ApplicationType\":\"\",\"UploadedFiles\":[],\"RemindSigner\":false,\"RemindMessageType\":0,\"RemindMessage\":null}";
            b1Card = JsonConvert.DeserializeObject<B1Card>(strB1Card);
            B1CardDetail detail = new B1CardDetail();
            detail.Project = "RP19553";
            detail.Hour = 4;
            detail.B1_CODE = 2;
            detail.ApplicationType = "社外加班";
            detail.StartTime = new DateTime(2022, 1, 4, 19, 0, 0, DateTimeKind.Local);
            detail.EndTime = new DateTime(2022, 1, 4, 23, 0, 0, DateTimeKind.Local);
            b1Card.Details = new List<B1CardDetail>();
            b1Card.Details.Add(detail);
            result = _b1CardBo.IsValidB1Card(b1Card);
            Assert.NotNull(result);
            Assert.True(result.IsValid);
        }


        [Fact]
        public void Update_ShouldUpdateCardDetails()
        {
            // Arrange
            var card = new B1Card();
            var updateDto = new Updater
            {
                UpdatedEmpNo = "E123",
                UpdatedName = "John Doe",
                UpdatedTime = DateTime.Now,
                UpdatedIP = "***********",
                UpdatedHost = "localhost"
            };

            // Act
            _b1CardBo.Update(card, updateDto);

            // Assert
            Assert.Equal(updateDto.UpdatedEmpNo, card.UpdatedEmpNo);
            Assert.Equal(updateDto.UpdatedName, card.UpdatedName);
            Assert.Equal(updateDto.UpdatedTime, card.UpdatedTime);
            Assert.Equal(updateDto.UpdatedIP, card.UpdatedIP);
            Assert.Equal(updateDto.UpdatedHost, card.UpdatedHost);
        }

        [Fact]
        public void TestFinish()
        {
            // Arrange
            var card = new B1Card();
            var formStatus = FormStatus.Agree;
            var updateDto = new Updater
            {
                UpdatedTime = DateTime.Now
            };

            // Act
            _b1CardBo.Finish(card, formStatus, updateDto);

            // Assert
            Assert.Equal(formStatus, (FormStatus)card.Status);
            Assert.Equal(updateDto.UpdatedTime, card.B1_ADate);
        }

        [Fact]
        public void GetCardTest()
        {
            IB1CardDao b1CardDao = A.Fake<IB1CardDao>();
            List<B1CardDto> b1Cards = new List<B1CardDto>();
            string strB1Card = "{\"B1_ADate\":null,\"B1_WDate\":\"2022-01-04T09:36:04.991+08:00\",\"DateTypeId\":1,\"Details\":[{\"ID\":null,\"Project\":\"RP19553\",\"StartTime\":\"2022-01-04T19:00:00+08:00\",\"EndTime\":\"2022-01-04T23:00:00+08:00\",\"Hour\":0,\"HourLeft\":0,\"B1_CODE\":2,\"ApplicationType\":\"\",\"SerialNo\":0}],\"EmpNo\":\"0395\",\"InOvertimeHours\":0,\"Name\":\"B1Card\",\"Reason\":\"test\",\"SheetNo\":\"\",\"Source\":\"Attendance\",\"Status\":1,\"TotalHours\":0,\"FormUID\":\"00000000-0000-0000-0000-000000000000\",\"UpdatedEmpNo\":null,\"UpdatedName\":null,\"UpdatedTime\":null,\"UpdatedIP\":null,\"UpdatedHost\":null,\"AddSigners\":\"\",\"CreatedTime\":\"2022-01-04T09:36:04.991+08:00\",\"FilledTime\":\"2022-01-04T09:35:44.381+08:00\",\"ApplicationType\":\"\",\"UploadedFiles\":[],\"RemindSigner\":false,\"RemindMessageType\":0,\"RemindMessage\":null}";
            B1Card? b1Card = JsonConvert.DeserializeObject<B1Card>(strB1Card);
            Assert.NotNull(b1Card);

            List<DataTable> dtList = _b1CardBo.ToUserDefinedDataTables(b1Card);
            DataTable table = dtList[0];
            foreach (DataTable dt in dtList)
            {
                if (dt.TableName == "B1Card")
                {
                    table = dt;
                }
            }

            A.CallTo(() => b1CardDao.GetB1Cards(A<Guid>.Ignored)).Returns(table);

            ILogger<B1CardBo> logger = A.Fake<ILogger<B1CardBo>>();
            IB1CardBo b1CardBo = new B1CardBo(_attendanceBo, _b1CardAppBo, _employeeBo,
                _formBo, _formFlowBo, b1CardDao, _overtimeBo, _projectBo,
                logger, _workdayBo);
            CardBase? card = b1CardBo.GetCard(Guid.NewGuid());
            Assert.NotNull(card);
            Assert.IsType<B1Card>(card);
        }

        [Fact]
        public void GetCardsTest()
        {
            DateTime start = new DateTime(2023, 10, 1, 0, 0, 0, DateTimeKind.Local);
            DateTime end = new DateTime(2023, 10, 2, 0, 0, 0, DateTimeKind.Local);
            List<CardBase> cards = _b1CardBo.GetCards(start, end);
            Assert.NotNull(cards);
            Assert.Empty(cards);
        }
    }
}
