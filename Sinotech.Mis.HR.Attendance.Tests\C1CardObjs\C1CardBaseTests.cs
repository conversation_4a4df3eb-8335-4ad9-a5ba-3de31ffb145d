﻿using FakeItEasy;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs;
using Sinotech.Mis.HR.Attendance.BusinessLogic.C1CardObjs.Leaves;
using Sinotech.Mis.HR.Attendance.Common;
using System;
using System.Diagnostics.CodeAnalysis;
using Xunit;

namespace Sinotech.Mis.HR.Attendance.C1CardObjs.Tests
{
    [ExcludeFromCodeCoverage]
    public class C1CardBaseTests
    {

        private readonly IC1CardBo _c1CardBo;

#nullable enable
        public C1CardBaseTests()
        {
            _c1CardBo = A.Fake<IC1CardBo>();
        }

        private CardCheckResult CheckData(C1Card c1Card)
        {
            IC1CardBo c1CardBo = _c1CardBo;
            c1Card.LeaveUnit = c1CardBo.GetLeaveUnit(c1Card); // 由資料庫得到請假單位
            c1Card.LeaveMinimumUnit = c1CardBo.GetLeaveMinimumUnit(c1Card); // 由資料庫得到請假單位
            c1Card.LeaveMaximum = c1CardBo.GetLeaveMaximum(c1Card); // 由資料庫得到最大請假數量，依請假單位
            c1Card.Hours = c1CardBo.CalculateTakeLeaveWorkingHours(c1Card.StartDate, c1Card.EndDate);
            c1Card.Days = c1CardBo.CalculateTakeLeaveWorkingDays(c1Card.Hours);
            c1Card.CertificateRequired = c1CardBo.GetCertificateRequired(c1Card);

            C1CardBase? c1CardBase = C1CardFactory.CreateLeave(c1Card, _c1CardBo);
            if (c1CardBase == null)
            {
                Assert.Fail("C1CardFactory.CreateLeave 發生錯誤");
            }
            CardCheckResult result = c1CardBase.CheckData();
            return result;
        }

        [Fact]
        public void CheckDataTest()
        {
            C1Card c1Card = new C1Card();
            c1Card.LeaveNumber = LeaveKindEnum.PersonalLeave;
            c1Card.StartDate = new DateTime(2023, 11, 1, 9, 0, 0, DateTimeKind.Local);
            c1Card.EndDate = new DateTime(2023, 11, 1, 8, 0, 0, DateTimeKind.Local);
            CardCheckResult result = CheckData(c1Card);
            Assert.NotNull(result);
            Assert.Equal(3000311, result.Code);

            c1Card.Deputy = "0276";
            result = CheckData(c1Card);
            Assert.Equal(3000312, result.Code);

            c1Card.EmpNo = "0395";
            c1Card.Deputy = "0349";
            result = CheckData(c1Card);
            Assert.Equal(3000312, result.Code);
            Assert.Equal(AttendanceParameters.Bad_Deputy_Error, result.Message);

            //c1Card.EndDate = new DateTime(2023, 10, 10, 14, 0, 0, DateTimeKind.Local);
            //result = CheckData(c1Card);
            //Assert.Equal(3000302, result.Code);
            //Assert.Equal(AttendanceParameters.Leave_Date_Only_Allow_WorkDays, result.Message);

            //c1Card.EndDate = new DateTime(2023, 12, 29, 17, 0, 0, DateTimeKind.Local);
            //result = CheckData(c1Card);
            //Assert.Equal(3004901, result.Code);

            //c1Card.LeaveNumber = LeaveKindEnum.BusinessOutLeave;
            //result = CheckData(c1Card);
            //Assert.Equal(3005301, result.Code);

            //c1Card.Reason = "公出";
            //result = CheckData(c1Card);
            //Assert.Equal(3000100, result.Code);

            //c1Card.LeaveNumber = LeaveKindEnum.MaternityLeave;
            //result = CheckData(c1Card);
            //Assert.Equal(3000314, result.Code);

            //c1Card.EventDate = c1Card.StartDate;
            //result = CheckData(c1Card);
            //Assert.Equal(3007302, result.Code);

            //c1Card.LeaveSubNumber = 1;
            //result = CheckData(c1Card);
            //Assert.Equal(3007301, result.Code);

            //c1Card.LeaveSubNumber = 0;
            //c1Card.EmpNo = "0349";
            //result = CheckData(c1Card);
            //Assert.Equal(3007302, result.Code);

            //c1Card.LeaveSubNumber = 1;
            //result = CheckData(c1Card);
            //Assert.Equal(3000303, result.Code);

            //c1Card.StartDate = new DateTime(2023, 11, 1, 8, 0, 0, DateTimeKind.Local);
            //c1Card.EndDate = new DateTime(2023, 11, 11, 17, 0, 0, DateTimeKind.Local);
            //result = CheckData(c1Card);
            //Assert.Equal(3000100, result.Code);

            //c1Card.StartDate = new DateTime(2023, 11, 1, 8, 0, 0, DateTimeKind.Local);
            //c1Card.EndDate = new DateTime(2023, 11, 11, 17, 0, 0, DateTimeKind.Local);
            //result = CheckData(c1Card);
            //Assert.Equal(3000100, result.Code);

            //c1Card.StartDate = new DateTime(2023, 11, 1, 9, 0, 0, DateTimeKind.Local);
            //c1Card.EndDate = new DateTime(2023, 11, 1, 14, 0, 0, DateTimeKind.Local);
            //c1Card.LeaveNumber = LeaveKindEnum.ObstetricInspectionLeave;
            //c1Card.EmpNo = "0395";
            //result = CheckData(c1Card);
            //Assert.Equal(3015301, result.Code);

            //c1Card.EmpNo = "0349";
            //c1Card.EventDate = null;
            //result = CheckData(c1Card);
            //Assert.Equal(3000314, result.Code);

            //c1Card.Deputy = "0276";
            //result = CheckData(c1Card);
            //Assert.Equal(3000314, result.Code);

            //c1Card.EventDate = new DateTime(2023, 11, 1, 9, 0, 0, DateTimeKind.Local);
            //result = CheckData(c1Card);
            //Assert.Equal(3000100, result.Code);

            //c1Card.LeaveNumber = LeaveKindEnum.MaternityLeave;
            //c1Card.EventDate = null;
            //result = CheckData(c1Card);
            //Assert.Equal(3000314, result.Code);

            //c1Card.EventDate = new DateTime(2023, 11, 1, 9, 0, 0, DateTimeKind.Local);
            //result = CheckData(c1Card);
            //Assert.Equal(3000303, result.Code);

            //c1Card.StartDate = new DateTime(2023, 11, 1, 9, 0, 0, DateTimeKind.Local);
            //c1Card.EndDate = new DateTime(2023, 11, 1, 14, 0, 0, DateTimeKind.Local);
            //result = CheckData(c1Card);
            //Assert.Equal(3000303, result.Code);

            //c1Card.LeaveSubNumber = 1;
            //result = CheckData(c1Card);
            //Assert.Equal(3000303, result.Code);

            //c1Card.StartDate = new DateTime(2023, 11, 1, 8, 0, 0, DateTimeKind.Local);
            //c1Card.EndDate = new DateTime(2023, 11, 1, 17, 0, 0, DateTimeKind.Local);
            //result = CheckData(c1Card);
            //Assert.Equal(3000100, result.Code);
        }

        [Theory]
        [InlineData((LeaveKindEnum)30, Gender.Female)]
        [InlineData((LeaveKindEnum)30, Gender.Male)]
        public void IsAllowForThisGender_ThrowException(LeaveKindEnum leaveKind, Gender gender)
        {
            Assert.Throws <LeaveNotFoundException>(() => C1CardBase.IsAllowForThisGender(leaveKind, gender));
        }

        [Theory]
        [InlineData(LeaveKindEnum.AnnualLeave, Gender.Female, true)] // 年假
        [InlineData(LeaveKindEnum.AnnualLeave, Gender.Male, true)] // 年假
        [InlineData(LeaveKindEnum.BirthdayLeave, Gender.Female, true)] // 生日假
        [InlineData(LeaveKindEnum.BirthdayLeave, Gender.Male, true)] // 生日假
        [InlineData(LeaveKindEnum.FamilyCareLeave, Gender.Female, true)] // 生日假
        [InlineData(LeaveKindEnum.FamilyCareLeave, Gender.Male, true)] // 生日假
        [InlineData(LeaveKindEnum.ForeignLeave, Gender.Female, true)] // 國外假
        [InlineData(LeaveKindEnum.ForeignLeave, Gender.Male, true)] // 國外假
        [InlineData(LeaveKindEnum.FuneralLeave, Gender.Female, true)] // 喪假
        [InlineData(LeaveKindEnum.FuneralLeave, Gender.Male, true)] // 喪假
        [InlineData(LeaveKindEnum.MarriageLeave, Gender.Female, true)] // 婚假
        [InlineData(LeaveKindEnum.MarriageLeave, Gender.Male, true)] // 婚假
        [InlineData(LeaveKindEnum.MaternityLeave, Gender.Female, true)] // 產假
        [InlineData(LeaveKindEnum.MaternityLeave, Gender.Male, false)] // 產假
        [InlineData(LeaveKindEnum.MenstrualLeave, Gender.Female, true)] // 生理假
        [InlineData(LeaveKindEnum.MenstrualLeave, Gender.Male, false)] // 生理假
        [InlineData(LeaveKindEnum.ObstetricInspectionLeave, Gender.Female, true)] // 產檢假
        [InlineData(LeaveKindEnum.ObstetricInspectionLeave, Gender.Male, false)] // 產檢假
        [InlineData(LeaveKindEnum.OfficialLeave, Gender.Female, true)] //公假
        [InlineData(LeaveKindEnum.OfficialLeave, Gender.Male, true)] // 公假
        [InlineData(LeaveKindEnum.PaternityLeave, Gender.Female, true)] // 陪產假
        [InlineData(LeaveKindEnum.PaternityLeave, Gender.Male, true)] // 陪產假
        [InlineData(LeaveKindEnum.PersonalLeave, Gender.Female, true)] //事假
        [InlineData(LeaveKindEnum.PersonalLeave, Gender.Male, true)] // 事假
        [InlineData(LeaveKindEnum.PostponedLeave, Gender.Female, true)] // 延休假
        [InlineData(LeaveKindEnum.PostponedLeave, Gender.Male, true)] // 延休假
        [InlineData(LeaveKindEnum.QuarantineCareLeave, Gender.Female, true)] // 防疫照顧假
        [InlineData(LeaveKindEnum.QuarantineCareLeave, Gender.Male, true)] // 防疫照顧假
        [InlineData(LeaveKindEnum.QuarantineIsolationLeave, Gender.Female, true)] // 防疫隔離假
        [InlineData(LeaveKindEnum.QuarantineIsolationLeave, Gender.Male, true)] // 防疫隔離假
        [InlineData(LeaveKindEnum.SickLeave, Gender.Female, true)] // 病假
        [InlineData(LeaveKindEnum.SickLeave, Gender.Male, true)] // 病假
        [InlineData(LeaveKindEnum.VaccinationLeave, Gender.Female, true)] // 疫苗接種假
        [InlineData(LeaveKindEnum.VaccinationLeave, Gender.Male, true)] // 疫苗接種假
        public void IsAllowForThisGenderTest(LeaveKindEnum leaveKind, Gender gender, bool expected)
        {
            bool result = C1CardBase.IsAllowForThisGender(leaveKind, gender);
            Assert.Equal(expected, result);
        }

        /// <summary>
        /// 測試一般規則檢查
        /// </summary>
        [Fact]
        public void CheckGeneralRules_WhenEmployeeNotExists_ShouldReturnBadEmployee()
        {
            // Arrange
            var c1Card = new C1Card { EmpNo = "NotExist" };
            A.CallTo(() => _c1CardBo.IsEmployee(c1Card.EmpNo)).Returns(false);
            var sut = new PersonalLeave(c1Card, _c1CardBo);

            // Act
            var result = sut.CheckGeneralRules();

            // Assert
            Assert.Equal(AttendanceParameters.ResultBadEmployee.Code, result.Code);
        }

        [Fact]
        public void CheckGeneralRules_WhenAddSignerNotExists_ShouldReturnBadEmployee()
        {
            // Arrange
            var c1Card = new C1Card 
            { 
                EmpNo = "0001",
                AddSigners = "0002,NotExist"
            };
            A.CallTo(() => _c1CardBo.IsEmployee(c1Card.EmpNo)).Returns(true);
            A.CallTo(() => _c1CardBo.IsEmployee("0002")).Returns(true);
            A.CallTo(() => _c1CardBo.IsEmployee("NotExist")).Returns(false);
            var sut = new PersonalLeave(c1Card, _c1CardBo);

            // Act
            var result = sut.CheckGeneralRules();

            // Assert
            Assert.Equal(AttendanceParameters.ResultBadEmployee.Code, result.Code);
        }

        [Fact]
        public void CheckGeneralRules_WhenAllValid_ShouldReturnSuccess()
        {
            // Arrange
            var c1Card = new C1Card 
            { 
                EmpNo = "0001",
                AddSigners = "0002,0003"
            };
            A.CallTo(() => _c1CardBo.IsEmployee(A<string>._)).Returns(true);
            var sut = new PersonalLeave(c1Card, _c1CardBo);

            // Act
            var result = sut.CheckGeneralRules();

            // Assert
            Assert.Equal(C1CardBase.CodeOk, result.Code);
        }
    }
}
