﻿using System;
using System.Collections.Generic;

namespace Sinotech.Mis.HR.Attendance.Common
{
    /// <summary>
    /// 收件匣/待審表單
    /// </summary>
    public class Inbox
    {
        /// <summary>
        /// 表單識別碼
        /// </summary>
        public Guid FormUID { get; set; } = Guid.Empty;
        /// <summary>
        /// 表單編號
        /// </summary>
        public string FormID { get; set; } = string.Empty;
        /// <summary>
        /// 表單單號
        /// </summary>
        public string FormNo { get; set; } = string.Empty;
        /// <summary>
        /// 表單主旨
        /// </summary>
        public string FormSubject { get; set; } = string.Empty;
        /// <summary>
        /// 表單資訊
        /// </summary>
        public string FormInfo { get; set; } = string.Empty;

        /// <summary>
        /// 表單附件
        /// </summary>
        public List<FormAttachment>? Attachments { get; set; } = null;

        /// <summary>
        /// 卡
        /// </summary>
        public CardBase? Card { get; set; } = null;

        /// <summary>
        /// 申請人員工編號
        /// </summary>
        public string EmpNo { get; set; } = string.Empty;
        /// <summary>
        /// 申請人姓名
        /// </summary>
        public string EmpName { get; set; } = string.Empty;
        /// <summary>
        /// 申請人部門編號
        /// </summary>
        public int DeptNo { get; set; }
        /// <summary>
        /// 申請人部門簡稱
        /// </summary>
        public string DeptSName { get; set; } = string.Empty;
        /// <summary>
        /// 申請人組別編號
        /// </summary>
        public int TeamID { get; set; }
        /// <summary>
        /// 申請人組別名稱
        /// </summary>
        public string TeamCName { get; set; } = string.Empty;
        /// <summary>
        /// 填表人員工編號
        /// </summary>
        public string CreatedEmpNo { get; set; } = string.Empty;
        /// <summary>
        /// 填表人姓名
        /// </summary>
        /// <summary>
        /// 開始填表時間
        /// </summary>
        public DateTime FilledTime { get; set; }

        /// <summary>
        /// 資料建立時間
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 資料建立來源IP 位址
        /// </summary>
        public string CreatedIP { get; set; } = string.Empty;

        /// <summary>
        /// 本張卡總時數
        /// </summary>
        public int Hours { get; set; } = 0;
        /// <summary>
        /// 資料建立來源電腦名稱
        /// </summary>
        public string? CreatedHost { get; set; }
        /// <summary>
        /// 加會人員
        /// </summary>
        public string? AddedSigner { get; set; }
        /// <summary>
        /// 表單流程開始日期時間
        /// </summary>
        public DateTime StartTime { get; set; }
        /// <summary>
        /// 表單流程結案日期時間
        /// </summary>
        public DateTime? EndTime { get; set; }
        /// <summary>
        /// 表單簽核狀態
        /// </summary>
        public int FormStatus { get; set; }
        /// <summary>
        /// 表單簽核狀態名稱
        /// </summary>
        public string FormStatusName { get; set; } = string.Empty;
        /// <summary>
        /// 總關卡數(共幾關)
        /// </summary>
        public int TotalSteps { get; set; }
        /// <summary>
        /// 目前關卡
        /// </summary>
        public int CurrentStep { get; set; }
        /// <summary>
        /// 最後修改者員工編號
        /// </summary>
        public string? UpdatedEmpNo { get; set; }
        /// <summary>
        /// 最後修改來源IP 位址
        /// </summary>
        public string? UpdatedIP { get; set; }

        /// <summary>
        /// 最後修改來源電腦名稱
        /// </summary>
        public string? UpdatedHost { get; set; }
        /// <summary>
        /// 流程識別碼
        /// </summary>
        public Guid FlowUID { get; set; }
        /// <summary>
        /// 收件人員工編號或角色
        /// </summary>
        public string RecipientEmpNo { get; set; } = string.Empty;
        /// <summary>
        /// 預設收件人或角色名稱
        /// </summary>
        public string RecipientName { get; set; } = string.Empty;
        /// <summary>
        /// 流程關卡名稱
        /// </summary>
        public string FlowName { get; set; } = string.Empty;
        /// <summary>
        /// 目前關卡數/第幾關
        /// </summary>
        public int Step { get; set; }
        /// <summary>
        /// 流水號
        /// </summary>
        public int ID { get; set; }
        /// <summary>
        /// 預設收件人部門編號
        /// </summary>
        public int RecipientDeptNo { get; set; }
        /// <summary>
        /// 預設收件人部門簡稱
        /// </summary>

        public string RecipientDeptSName { get; set; } = string.Empty;
        /// <summary>
        /// 別人代填
        /// </summary>
        public bool IsAgentFilled { get; set; } = false;

        /// <summary>
        /// 替別人填
        /// </summary>
        public bool IsAgency { get; set; } = false;

        /// <summary>
        /// 是否提醒簽核人
        /// </summary>
        public bool RemindSigner { get; set; } = false;

        /// <summary>
        /// 提醒訊息類型 , 0: 無訊息、 1以上各卡不同
        /// </summary>
        public int RemindMessageType { get; set; } = 0;

        /// <summary>
        /// 提醒訊息
        /// </summary>
        public string? RemindMessage { get; set; } = null;
    }
}
