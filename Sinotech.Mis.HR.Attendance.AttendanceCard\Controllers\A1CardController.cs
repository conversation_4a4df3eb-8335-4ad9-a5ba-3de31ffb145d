﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sinotech.Mis.HR.Attendance.BusinessLogic;
using Sinotech.Mis.HR.Attendance.Common;
using Sinotech.Mis.HR.Attendance.Utilities;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Sinotech.Mis.HR.Attendance.AttendanceCard.Controllers
{
    /// <summary>
    /// 正常工作卡Web API
    /// </summary>
    [Authorize]
    [ApiController]
    [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
    [Route("api/[controller]/[action]")]
    public class A1CardController : ControllerBase
    {

        // 避免重覆送出鎖定物件
        private static readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);
        private readonly IA1CardBo _a1CardBo;
        private readonly IAttendanceBo _attendanceBo;
        private readonly ICardChecker _cardChecker;
        private readonly ILogger<A1CardController> _logger;
        private readonly bool _useNegotiate;

        /// <summary>  
        ///   <see cref="A1CardController" /> 的建構函式  
        /// </summary>  
        /// <param name="a1CardBo">A1Card 商業物件</param>  
        /// <param name="attendanceBo">Attendance 商業物件</param>  
        /// <param name="cardChecker">卡片檢查器</param>  
        /// <param name="configuration">應用程式設定</param>  
        /// <param name="logger">日誌記錄器</param>
        public A1CardController(IA1CardBo a1CardBo, IAttendanceBo attendanceBo, ICardChecker cardChecker,
            IConfiguration configuration, ILogger<A1CardController> logger)
        {
            _a1CardBo = a1CardBo;
            _attendanceBo = attendanceBo;
            _cardChecker = cardChecker;
            _useNegotiate = configuration.GetValue<bool>("UseNegotiate");
            _logger = logger;
        }

        private static A1Card _LastCard { get; set; } = new A1Card();

        /// <summary>
        /// 檢查某員工是否已填某旬之有效（簽核中及同意）正常工作卡
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="chineseYearMonth">民國年月(yyymm)</param>
        /// <param name="tenDays">旬別(1:上旬、2:中旬、3:下旬)</param>
        /// <returns>若未填則傳回 {FormUID: null, FormNo: null}</returns>
        [Authorize]
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public string IsFilled(string empNo, string chineseYearMonth, char tenDays)
        {
            FormFilled formFilled = new FormFilled();
            try
            {
                string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
                if (!string.IsNullOrWhiteSpace(userId))
                {
                    formFilled = _a1CardBo.IsFilled(empNo, chineseYearMonth, tenDays);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("/api/A1Card/IsFilled({EmpNo},{ChineseYearMonth},{TenDays}) 發生錯誤：{Message} {StackTrace}", empNo, chineseYearMonth, tenDays, ex.Message, ex.StackTrace);
            }
            string ret = JsonConvert.SerializeObject(formFilled);
            return ret;
        }

        /// <summary>送出正常工作卡</summary>
        /// <param name="cards">List of A1CardForm</param>
        /// <returns>CardCheckResult JSON ,如 { Code: 訊息代碼, Message: "...", Status: CardStatusEnum   },其中CardStatusEnum   { Success = 0, Warning = 1,  Error = -1,  Conformation = 9  }</returns>
        [Authorize]
        [HttpPost]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/")]
        public async Task<string> Submit(List<A1CardForm> cards)
        {
            CardCheckResult result = AttendanceParameters.ResultGeneralError;
            string errorMessage;
            await _semaphore.WaitAsync();
            try
            {
                A1Card a1Card = A1CardBo.A1CardForm2A1Card(cards);
                if (!a1Card.EasyEquals(_LastCard))
                {
                    string creatorId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
                    if (string.IsNullOrWhiteSpace(creatorId))
                    {
                        result = AttendanceParameters.ResultBadEmployee;
                    }
                    else
                    {
                        result = _cardChecker.CheckA1CardAdd(a1Card); // 先檢查是否有錯誤
                        if (result.Code == 0)
                        {
                            var (ip, hostname) = CardUtility.GetIP_Hostname(Request.HttpContext);
                            Guid? guid;
                            (guid, errorMessage) = await _a1CardBo.AddA1Card(creatorId, a1Card, ip.ToString(), hostname);
                            bool isValid = (guid != null && guid != Guid.Empty && guid.HasValue);
                            if (isValid)
                            {
                                result = AttendanceParameters.ResultOk;
                                _LastCard = a1Card.EasyClone();
                            }
                            else
                            {
                                result = new CardCheckResult(3000399, CardStatusEnum.Error, errorMessage);
                            }
                        }
                    }
                }
                else
                {
                    result = new CardCheckResult(1000311, CardStatusEnum.Error, AttendanceParameters.RepeatSubmitForm);
                }

            }
            catch (Exception ex)
            {
                _logger.LogError("/api/A1Card/Submit({Cards}) 發生錯誤：{Message} {StackTrace}", JsonConvert.SerializeObject(cards), ex.Message, ex.StackTrace);
            }
            finally
            {
                _semaphore.Release();
            }
            string ret = JsonConvert.SerializeObject(result);
            return ret;
        }

        /// <summary>更新正常工作卡</summary>
        /// <param name="cards">List of A1CardForm</param>
        /// <returns>CardCheckResult JSON ,如 { Code: 訊息代碼, Message: "...", Status: CardStatusEnum   },其中CardStatusEnum   { Success = 0, Warning = 1,  Error = -1,  Conformation = 9  }</returns>
        [Authorize]
        [HttpPut]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/")]
        public string Update(List<A1CardForm> cards)
        {
            CardCheckResult result = AttendanceParameters.ResultGeneralError;
            try
            {
                string userId = CardUtility.GetValidEmpNo(HttpContext, _useNegotiate);
                bool isAdmin = _attendanceBo.IsAdmin(userId);
                if (string.IsNullOrWhiteSpace(userId) || !isAdmin)
                {
                    result = AttendanceParameters.ResultBadEmployee;
                }
                else
                {
                    A1Card a1Card = A1CardBo.A1CardForm2A1Card(cards);
                    result = _cardChecker.CheckA1CardUpdate(a1Card); // 先檢查是否有錯誤
                    if (result.Code == 0)
                    {
                        var (ip, hostname) = CardUtility.GetIP_Hostname(Request.HttpContext);
                        result = _a1CardBo.UpdateA1CardProjectNo(userId, cards, ip.ToString(), hostname);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("/api/A1Card/Submit({Cards}) 發生錯誤：{Message} {StackTrace}", JsonConvert.SerializeObject(cards), ex.Message, ex.StackTrace);
            }
            string ret = JsonConvert.SerializeObject(result);
            return ret;
        }

    }
}
